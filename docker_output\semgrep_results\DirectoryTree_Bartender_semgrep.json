{"version": "1.130.0", "results": [], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/DirectoryTree_Bartender/.github/workflows/run-tests.yml", "start": {"line": 56, "col": 53, "offset": 1365}, "end": {"line": 56, "col": 69, "offset": 1381}}, {"path": "downloaded_repos/DirectoryTree_Bartender/.github/workflows/run-tests.yml", "start": {"line": 56, "col": 97, "offset": 1365}, "end": {"line": 56, "col": 115, "offset": 1383}}, {"path": "downloaded_repos/DirectoryTree_Bartender/.github/workflows/run-tests.yml", "start": {"line": 57, "col": 19, "offset": 1365}, "end": {"line": 57, "col": 22, "offset": 1368}}]], "message": "Syntax error at line downloaded_repos/DirectoryTree_Bartender/.github/workflows/run-tests.yml:56:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ matrix.laravel` was unexpected", "path": "downloaded_repos/DirectoryTree_Bartender/.github/workflows/run-tests.yml", "spans": [{"file": "downloaded_repos/DirectoryTree_Bartender/.github/workflows/run-tests.yml", "start": {"line": 56, "col": 53, "offset": 1365}, "end": {"line": 56, "col": 69, "offset": 1381}}, {"file": "downloaded_repos/DirectoryTree_Bartender/.github/workflows/run-tests.yml", "start": {"line": 56, "col": 97, "offset": 1365}, "end": {"line": 56, "col": 115, "offset": 1383}}, {"file": "downloaded_repos/DirectoryTree_Bartender/.github/workflows/run-tests.yml", "start": {"line": 57, "col": 19, "offset": 1365}, "end": {"line": 57, "col": 22, "offset": 1368}}]}], "paths": {"scanned": ["downloaded_repos/DirectoryTree_Bartender/.gitattributes", "downloaded_repos/DirectoryTree_Bartender/.github/workflows/run-cs-fix.yml", "downloaded_repos/DirectoryTree_Bartender/.github/workflows/run-tests.yml", "downloaded_repos/DirectoryTree_Bartender/.gitignore", "downloaded_repos/DirectoryTree_Bartender/README.md", "downloaded_repos/DirectoryTree_Bartender/art/logo.svg", "downloaded_repos/DirectoryTree_Bartender/composer.json", "downloaded_repos/DirectoryTree_Bartender/database/migrations/2024_03_31_000001_add_provider_columns_to_users_table.php", "downloaded_repos/DirectoryTree_Bartender/database/migrations/2024_10_27_131354_add_provider_token_columns_to_users_table.php", "downloaded_repos/DirectoryTree_Bartender/license.md", "downloaded_repos/DirectoryTree_Bartender/phpunit.xml", "downloaded_repos/DirectoryTree_Bartender/src/BartenderManager.php", "downloaded_repos/DirectoryTree_Bartender/src/BartenderServiceProvider.php", "downloaded_repos/DirectoryTree_Bartender/src/Controllers/AuthController.php", "downloaded_repos/DirectoryTree_Bartender/src/Events/UserAuthenticated.php", "downloaded_repos/DirectoryTree_Bartender/src/Facades/Bartender.php", "downloaded_repos/DirectoryTree_Bartender/src/ProviderHandler.php", "downloaded_repos/DirectoryTree_Bartender/src/ProviderRedirector.php", "downloaded_repos/DirectoryTree_Bartender/src/ProviderRepository.php", "downloaded_repos/DirectoryTree_Bartender/src/StoresProviderTokens.php", "downloaded_repos/DirectoryTree_Bartender/src/UserProviderHandler.php", "downloaded_repos/DirectoryTree_Bartender/src/UserProviderRedirector.php", "downloaded_repos/DirectoryTree_Bartender/src/UserProviderRepository.php"], "skipped": [{"path": "downloaded_repos/DirectoryTree_Bartender/.github/workflows/run-tests.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/DirectoryTree_Bartender/tests/BartenderManagerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_Bartender/tests/Pest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_Bartender/tests/TestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_Bartender/tests/User.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_Bartender/tests/UserProviderHandlerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/DirectoryTree_Bartender/tests/UserProviderRepositoryTest.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6162447929382324, "profiling_times": {"config_time": 6.66485333442688, "core_time": 2.2475345134735107, "ignores_time": 0.002651214599609375, "total_time": 8.916681051254272}, "parsing_time": {"total_time": 0.33600807189941406, "per_file_time": {"mean": 0.01976518069996553, "std_dev": 7.918048603638944e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.714747428894043, "per_file_time": {"mean": 0.011345197284032426, "std_dev": 0.0002812204586879942}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.15076684951782227, "per_file_and_rule_time": {"mean": 0.0017948434466407413, "std_dev": 1.0888931112846088e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.0023365020751953125, "per_def_and_rule_time": {"mean": 0.00010620463978160511, "std_dev": 2.974809752434467e-08}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}