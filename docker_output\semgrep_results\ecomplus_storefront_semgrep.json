{"version": "1.130.0", "results": [{"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/ecomplus_storefront/.vuepress/config.js", "start": {"line": 18, "col": 46, "offset": 457}, "end": {"line": 18, "col": 49, "offset": 460}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/ecomplus_storefront/.vuepress/config.js", "start": {"line": 38, "col": 53, "offset": 1233}, "end": {"line": 38, "col": 93, "offset": 1273}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/components/js/PaymentMethods.js", "start": {"line": 123, "col": 13, "offset": 2922}, "end": {"line": 123, "col": 81, "offset": 2990}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/lib/load-payment-client.js", "start": {"line": 39, "col": 13, "offset": 1005}, "end": {"line": 39, "col": 29, "offset": 1021}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/plugins/toast.js", "start": {"line": 46, "col": 3, "offset": 1035}, "end": {"line": 63, "col": 9, "offset": 1520}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/SearchEngine.js", "start": {"line": 370, "col": 47, "offset": 10294}, "end": {"line": 370, "col": 68, "offset": 10315}, "extra": {"message": "RegExp() called with a `{ options, text }` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/TheProduct.js", "start": {"line": 601, "col": 11, "offset": 17518}, "end": {"line": 601, "col": 58, "offset": 17565}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/src/webpack.config.js", "start": {"line": 109, "col": 59, "offset": 3288}, "end": {"line": 109, "col": 73, "offset": 3302}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/template/pages/index.ejs", "start": {"line": 11, "col": 5, "offset": 192}, "end": {"line": 11, "col": 37, "offset": 224}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/template/public/admin/index.html", "start": {"line": 7, "col": 3, "offset": 167}, "end": {"line": 7, "col": 85, "offset": 249}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/template/public/admin/index.html", "start": {"line": 11, "col": 3, "offset": 341}, "end": {"line": 11, "col": 83, "offset": 421}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-renderer/functions/index.js", "start": {"line": 91, "col": 25, "offset": 2823}, "end": {"line": 91, "col": 38, "offset": 2836}, "extra": {"message": "Detected directly writing to a Response object from user-defined input. This bypasses any HTML escaping and may expose your application to a Cross-Site-scripting (XSS) vulnerability. Instead, use 'resp.render()' to render safely escaped HTML.", "metadata": {"interfile": true, "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Cross_Site_Scripting_Prevention_Cheat_Sheet.html"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.direct-response-write.direct-response-write", "shortlink": "https://sg.run/vzGl"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-renderer/src/lib/entry.js", "start": {"line": 28, "col": 43, "offset": 825}, "end": {"line": 28, "col": 50, "offset": 832}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-renderer/src/lib/entry.js", "start": {"line": 28, "col": 52, "offset": 834}, "end": {"line": 28, "col": 56, "offset": 838}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-renderer/src/lib/get-store-data.js", "start": {"line": 33, "col": 38, "offset": 970}, "end": {"line": 33, "col": 44, "offset": 976}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-renderer/src/lib/minify-html.js", "start": {"line": 20, "col": 33, "offset": 614}, "end": {"line": 20, "col": 83, "offset": 664}, "extra": {"message": "RegExp() called with a `entryAssetsReference` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-renderer/src/renderer.js", "start": {"line": 102, "col": 46, "offset": 2794}, "end": {"line": 102, "col": 63, "offset": 2811}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-renderer/src/renderer.js", "start": {"line": 108, "col": 46, "offset": 2986}, "end": {"line": 108, "col": 69, "offset": 3009}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-renderer/src/renderer.js", "start": {"line": 147, "col": 46, "offset": 3914}, "end": {"line": 147, "col": 50, "offset": 3918}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/scripts/copy.js", "start": {"line": 21, "col": 44, "offset": 537}, "end": {"line": 21, "col": 50, "offset": 543}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/scripts/copy.js", "start": {"line": 21, "col": 72, "offset": 565}, "end": {"line": 21, "col": 78, "offset": 571}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/scripts/install.js", "start": {"line": 28, "col": 9, "offset": 656}, "end": {"line": 28, "col": 22, "offset": 669}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/scripts/install.js", "start": {"line": 30, "col": 9, "offset": 709}, "end": {"line": 30, "col": 38, "offset": 738}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/scripts/install.js", "start": {"line": 34, "col": 38, "offset": 794}, "end": {"line": 34, "col": 46, "offset": 802}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/scripts/install.js", "start": {"line": 50, "col": 27, "offset": 1243}, "end": {"line": 50, "col": 32, "offset": 1248}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/scripts/install.js", "start": {"line": 50, "col": 34, "offset": 1250}, "end": {"line": 50, "col": 40, "offset": 1256}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/scripts/install.js", "start": {"line": 55, "col": 33, "offset": 1376}, "end": {"line": 55, "col": 44, "offset": 1387}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/scripts/install.js", "start": {"line": 55, "col": 46, "offset": 1389}, "end": {"line": 55, "col": 54, "offset": 1397}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/scripts/install.js", "start": {"line": 93, "col": 28, "offset": 2431}, "end": {"line": 93, "col": 64, "offset": 2467}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/scripts/lib/recursive-copy.js", "start": {"line": 24, "col": 31, "offset": 733}, "end": {"line": 24, "col": 35, "offset": 737}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/scripts/lib/recursive-copy.js", "start": {"line": 24, "col": 37, "offset": 739}, "end": {"line": 24, "col": 41, "offset": 743}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/scripts/lib/recursive-copy.js", "start": {"line": 24, "col": 54, "offset": 756}, "end": {"line": 24, "col": 56, "offset": 758}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/scripts/lib/recursive-copy.js", "start": {"line": 24, "col": 58, "offset": 760}, "end": {"line": 24, "col": 62, "offset": 764}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/lib/theme.js.ejs", "start": {"line": 7, "col": 17, "offset": 153}, "end": {"line": 7, "col": 43, "offset": 179}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/preview/base-preview.js", "start": {"line": 293, "col": 39, "offset": 10733}, "end": {"line": 293, "col": 50, "offset": 10744}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/preview/base-preview.js", "start": {"line": 300, "col": 5, "offset": 10873}, "end": {"line": 300, "col": 33, "offset": 10901}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/preview/code-preview.js", "start": {"line": 75, "col": 9, "offset": 1863}, "end": {"line": 75, "col": 61, "offset": 1915}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/preview/code-preview.js", "start": {"line": 90, "col": 9, "offset": 2343}, "end": {"line": 90, "col": 48, "offset": 2382}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/preview/settings/general.js", "start": {"line": 128, "col": 7, "offset": 3248}, "end": {"line": 136, "col": 15, "offset": 3539}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/preview/settings/general.js", "start": {"line": 139, "col": 9, "offset": 3604}, "end": {"line": 139, "col": 89, "offset": 3684}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/preview/settings/general.js", "start": {"line": 162, "col": 13, "offset": 4438}, "end": {"line": 162, "col": 88, "offset": 4513}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/preview/virtual-doc.js", "start": {"line": 19, "col": 11, "offset": 723}, "end": {"line": 19, "col": 33, "offset": 745}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/head.ejs", "start": {"line": 41, "col": 5, "offset": 1052}, "end": {"line": 41, "col": 26, "offset": 1073}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/head.ejs", "start": {"line": 46, "col": 1, "offset": 1095}, "end": {"line": 46, "col": 28, "offset": 1122}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.var-in-script-tag.var-in-script-tag", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/json.ejs", "start": {"line": 61, "col": 17, "offset": 1503}, "end": {"line": 67, "col": 3, "offset": 1770}, "extra": {"message": "Detected a template variable used in a script tag. Although template variables are HTML escaped, HTML escaping does not always prevent cross-site scripting (XSS) attacks when used directly in JavaScript. If you need this data on the rendered page, consider placing it in the HTML portion (outside of a script tag). Alternatively, use a JavaScript-specific encoder, such as the one available in OWASP ESAPI.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://www.veracode.com/blog/secure-development/nodejs-template-engines-why-default-encoders-are-not-enough", "https://github.com/ESAPI/owasp-esapi-js"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.var-in-script-tag.var-in-script-tag", "shortlink": "https://sg.run/Ek9L"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.var-in-script-tag.var-in-script-tag", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/json.ejs", "start": {"line": 69, "col": 36, "offset": 1823}, "end": {"line": 77, "col": 3, "offset": 2141}, "extra": {"message": "Detected a template variable used in a script tag. Although template variables are HTML escaped, HTML escaping does not always prevent cross-site scripting (XSS) attacks when used directly in JavaScript. If you need this data on the rendered page, consider placing it in the HTML portion (outside of a script tag). Alternatively, use a JavaScript-specific encoder, such as the one available in OWASP ESAPI.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://www.veracode.com/blog/secure-development/nodejs-template-engines-why-default-encoders-are-not-enough", "https://github.com/ESAPI/owasp-esapi-js"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.var-in-script-tag.var-in-script-tag", "shortlink": "https://sg.run/Ek9L"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/layout/footer.ejs", "start": {"line": 163, "col": 12, "offset": 5600}, "end": {"line": 163, "col": 68, "offset": 5656}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/layout/header.ejs", "start": {"line": 288, "col": 51, "offset": 10029}, "end": {"line": 288, "col": 89, "offset": 10067}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/layout/header.ejs", "start": {"line": 329, "col": 17, "offset": 11900}, "end": {"line": 329, "col": 37, "offset": 11920}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/layout/header.ejs", "start": {"line": 377, "col": 19, "offset": 14292}, "end": {"line": 377, "col": 38, "offset": 14311}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/meta.ejs", "start": {"line": 121, "col": 9, "offset": 3772}, "end": {"line": 121, "col": 38, "offset": 3801}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.var-in-script-tag.var-in-script-tag", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/meta.ejs", "start": {"line": 121, "col": 9, "offset": 3772}, "end": {"line": 121, "col": 38, "offset": 3801}, "extra": {"message": "Detected a template variable used in a script tag. Although template variables are HTML escaped, HTML escaping does not always prevent cross-site scripting (XSS) attacks when used directly in JavaScript. If you need this data on the rendered page, consider placing it in the HTML portion (outside of a script tag). Alternatively, use a JavaScript-specific encoder, such as the one available in OWASP ESAPI.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://www.veracode.com/blog/secure-development/nodejs-template-engines-why-default-encoders-are-not-enough", "https://github.com/ESAPI/owasp-esapi-js"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.var-in-script-tag.var-in-script-tag", "shortlink": "https://sg.run/Ek9L"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.var-in-script-src.var-in-script-src", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/scripts.ejs", "start": {"line": 33, "col": 14, "offset": 1101}, "end": {"line": 33, "col": 41, "offset": 1128}, "extra": {"message": "Detected a template variable used as the 'src' in a script tag. Although template variables are HTML escaped, HTML escaping does not always prevent malicious URLs from being injected and could results in a cross-site scripting (XSS) vulnerability. Prefer not to dynamically generate the 'src' attribute and use static URLs instead. If you must do this, carefully check URLs against an allowlist and be sure to URL-encode the result.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://www.veracode.com/blog/secure-development/nodejs-template-engines-why-default-encoders-are-not-enough", "https://github.com/ESAPI/owasp-esapi-js"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.var-in-script-src.var-in-script-src", "shortlink": "https://sg.run/ndxZ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/scripts.ejs", "start": {"line": 48, "col": 6, "offset": 1494}, "end": {"line": 48, "col": 33, "offset": 1521}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/banner-slider.ejs", "start": {"line": 38, "col": 15, "offset": 888}, "end": {"line": 38, "col": 31, "offset": 904}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/banner-slider.ejs", "start": {"line": 38, "col": 40, "offset": 913}, "end": {"line": 38, "col": 52, "offset": 925}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/blog.ejs", "start": {"line": 48, "col": 23, "offset": 1737}, "end": {"line": 48, "col": 53, "offset": 1767}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/custom-html.ejs", "start": {"line": 5, "col": 9, "offset": 157}, "end": {"line": 5, "col": 24, "offset": 172}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/document-description.ejs", "start": {"line": 9, "col": 9, "offset": 268}, "end": {"line": 9, "col": 33, "offset": 292}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/inc/banner.ejs", "start": {"line": 48, "col": 20, "offset": 1327}, "end": {"line": 48, "col": 32, "offset": 1339}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/inc/banner.ejs", "start": {"line": 50, "col": 21, "offset": 1391}, "end": {"line": 50, "col": 32, "offset": 1402}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/inc/md-content.ejs", "start": {"line": 7, "col": 7, "offset": 190}, "end": {"line": 7, "col": 35, "offset": 218}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.var-in-script-tag.var-in-script-tag", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/product-block.ejs", "start": {"line": 241, "col": 62, "offset": 9342}, "end": {"line": 243, "col": 3, "offset": 9380}, "extra": {"message": "Detected a template variable used in a script tag. Although template variables are HTML escaped, HTML escaping does not always prevent cross-site scripting (XSS) attacks when used directly in JavaScript. If you need this data on the rendered page, consider placing it in the HTML portion (outside of a script tag). Alternatively, use a JavaScript-specific encoder, such as the one available in OWASP ESAPI.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://www.veracode.com/blog/secure-development/nodejs-template-engines-why-default-encoders-are-not-enough", "https://github.com/ESAPI/owasp-esapi-js"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.var-in-script-tag.var-in-script-tag", "shortlink": "https://sg.run/Ek9L"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/stamps.ejs", "start": {"line": 12, "col": 13, "offset": 498}, "end": {"line": 12, "col": 27, "offset": 512}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/stamps.ejs", "start": {"line": 17, "col": 11, "offset": 586}, "end": {"line": 17, "col": 25, "offset": 600}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/maintenance/index.ejs", "start": {"line": 35, "col": 15, "offset": 925}, "end": {"line": 35, "col": 51, "offset": 961}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/styles.scss.ejs", "start": {"line": 20, "col": 1, "offset": 411}, "end": {"line": 20, "col": 30, "offset": 440}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/styles.scss.ejs", "start": {"line": 24, "col": 1, "offset": 492}, "end": {"line": 24, "col": 26, "offset": 517}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/bootstrap-icons/demo.html", "start": {"line": 4, "col": 20, "offset": 51}, "end": {"line": 4, "col": 134, "offset": 165}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/feather-icons/demo.html", "start": {"line": 4, "col": 20, "offset": 51}, "end": {"line": 4, "col": 134, "offset": 165}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/font-awesome/demo.html", "start": {"line": 4, "col": 20, "offset": 51}, "end": {"line": 4, "col": 134, "offset": 165}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/line-awesome/demo.html", "start": {"line": 4, "col": 20, "offset": 51}, "end": {"line": 4, "col": 134, "offset": 165}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/tabler-icons/demo.html", "start": {"line": 4, "col": 20, "offset": 51}, "end": {"line": 4, "col": 134, "offset": 165}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-analytics/src/append/head.ejs", "start": {"line": 2, "col": 64, "offset": 117}, "end": {"line": 2, "col": 91, "offset": 144}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.var-in-script-src.var-in-script-src", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-analytics/src/append/head.ejs", "start": {"line": 2, "col": 64, "offset": 117}, "end": {"line": 2, "col": 91, "offset": 144}, "extra": {"message": "Detected a template variable used as the 'src' in a script tag. Although template variables are HTML escaped, HTML escaping does not always prevent malicious URLs from being injected and could results in a cross-site scripting (XSS) vulnerability. Prefer not to dynamically generate the 'src' attribute and use static URLs instead. If you must do this, carefully check URLs against an allowlist and be sure to URL-encode the result.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://www.veracode.com/blog/secure-development/nodejs-template-engines-why-default-encoders-are-not-enough", "https://github.com/ESAPI/owasp-esapi-js"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.var-in-script-src.var-in-script-src", "shortlink": "https://sg.run/ndxZ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-analytics/src/append/head.ejs", "start": {"line": 10, "col": 21, "offset": 351}, "end": {"line": 10, "col": 48, "offset": 378}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-analytics/src/append/head.ejs", "start": {"line": 17, "col": 21, "offset": 612}, "end": {"line": 17, "col": 48, "offset": 639}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-compre-confie/src/append/stamps.ejs", "start": {"line": 5, "col": 63, "offset": 139}, "end": {"line": 5, "col": 97, "offset": 173}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-compre-confie/src/append/stamps.ejs", "start": {"line": 12, "col": 65, "offset": 367}, "end": {"line": 12, "col": 99, "offset": 401}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.var-in-script-src.var-in-script-src", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-compre-confie/src/append/stamps.ejs", "start": {"line": 12, "col": 65, "offset": 367}, "end": {"line": 12, "col": 99, "offset": 401}, "extra": {"message": "Detected a template variable used as the 'src' in a script tag. Although template variables are HTML escaped, HTML escaping does not always prevent malicious URLs from being injected and could results in a cross-site scripting (XSS) vulnerability. Prefer not to dynamically generate the 'src' attribute and use static URLs instead. If you must do this, carefully check URLs against an allowlist and be sure to URL-encode the result.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://www.veracode.com/blog/secure-development/nodejs-template-engines-why-default-encoders-are-not-enough", "https://github.com/ESAPI/owasp-esapi-js"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.var-in-script-src.var-in-script-src", "shortlink": "https://sg.run/ndxZ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-compre-confie/src/index.js", "start": {"line": 191, "col": 19, "offset": 7520}, "end": {"line": 191, "col": 87, "offset": 7588}, "extra": {"message": "Detection of insertAdjacentHTML from non-constant definition. This can inadvertently expose users to cross-site scripting (XSS) attacks if this comes from user-provided input. If you have to use insertAdjacentHTML, consider using a sanitization library such as DOMPurify to sanitize your HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://developer.mozilla.org/en-US/docs/Web/API/Document/writeln", "https://developer.mozilla.org/en-US/docs/Web/API/Document/write", "https://developer.mozilla.org/en-US/docs/Web/API/Element/insertAdjacentHTML"], "category": "security", "confidence": "MEDIUM", "technology": ["react"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "shortlink": "https://sg.run/E5x8"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-ebit/src/append/stamps.ejs", "start": {"line": 5, "col": 36, "offset": 102}, "end": {"line": 5, "col": 62, "offset": 128}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-ebit/src/append/stamps.ejs", "start": {"line": 12, "col": 68, "offset": 326}, "end": {"line": 12, "col": 94, "offset": 352}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.var-in-script-src.var-in-script-src", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-ebit/src/append/stamps.ejs", "start": {"line": 12, "col": 68, "offset": 326}, "end": {"line": 12, "col": 94, "offset": 352}, "extra": {"message": "Detected a template variable used as the 'src' in a script tag. Although template variables are HTML escaped, HTML escaping does not always prevent malicious URLs from being injected and could results in a cross-site scripting (XSS) vulnerability. Prefer not to dynamically generate the 'src' attribute and use static URLs instead. If you must do this, carefully check URLs against an allowlist and be sure to URL-encode the result.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://www.veracode.com/blog/secure-development/nodejs-template-engines-why-default-encoders-are-not-enough", "https://github.com/ESAPI/owasp-esapi-js"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.var-in-script-src.var-in-script-src", "shortlink": "https://sg.run/ndxZ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-ebit/src/index.js", "start": {"line": 113, "col": 21, "offset": 4620}, "end": {"line": 113, "col": 102, "offset": 4701}, "extra": {"message": "Detection of insertAdjacentHTML from non-constant definition. This can inadvertently expose users to cross-site scripting (XSS) attacks if this comes from user-provided input. If you have to use insertAdjacentHTML, consider using a sanitization library such as DOMPurify to sanitize your HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://developer.mozilla.org/en-US/docs/Web/API/Document/writeln", "https://developer.mozilla.org/en-US/docs/Web/API/Document/write", "https://developer.mozilla.org/en-US/docs/Web/API/Element/insertAdjacentHTML"], "category": "security", "confidence": "MEDIUM", "technology": ["react"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "shortlink": "https://sg.run/E5x8"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-fb-pixel/src/append/body.ejs", "start": {"line": 2, "col": 44, "offset": 54}, "end": {"line": 2, "col": 73, "offset": 83}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-fb-pixel/src/append/head.ejs", "start": {"line": 6, "col": 9, "offset": 104}, "end": {"line": 6, "col": 66, "offset": 161}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-fb-pixel/src/append/head.ejs", "start": {"line": 15, "col": 16, "offset": 580}, "end": {"line": 15, "col": 45, "offset": 609}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-gmc-ratings/src/append/stamps.ejs", "start": {"line": 16, "col": 26, "offset": 541}, "end": {"line": 16, "col": 54, "offset": 569}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-gmc-ratings/src/append/stamps.ejs", "start": {"line": 17, "col": 24, "offset": 594}, "end": {"line": 17, "col": 71, "offset": 641}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-gmc-ratings/src/append/stamps.ejs", "start": {"line": 22, "col": 14, "offset": 709}, "end": {"line": 22, "col": 25, "offset": 720}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/append/body.ejs", "start": {"line": 4, "col": 8, "offset": 105}, "end": {"line": 4, "col": 106, "offset": 203}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/append/body.ejs", "start": {"line": 8, "col": 10, "offset": 267}, "end": {"line": 8, "col": 84, "offset": 341}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/append/body.ejs", "start": {"line": 10, "col": 10, "offset": 375}, "end": {"line": 10, "col": 79, "offset": 444}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/append/body.ejs", "start": {"line": 12, "col": 24, "offset": 470}, "end": {"line": 12, "col": 47, "offset": 493}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/append/body.ejs", "start": {"line": 13, "col": 26, "offset": 520}, "end": {"line": 13, "col": 51, "offset": 545}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/append/body.ejs", "start": {"line": 14, "col": 22, "offset": 568}, "end": {"line": 14, "col": 43, "offset": 589}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/append/body.ejs", "start": {"line": 38, "col": 16, "offset": 1187}, "end": {"line": 38, "col": 81, "offset": 1252}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/append/body.ejs", "start": {"line": 68, "col": 8, "offset": 2181}, "end": {"line": 68, "col": 72, "offset": 2245}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/append/product-block.ejs", "start": {"line": 2, "col": 30, "offset": 52}, "end": {"line": 2, "col": 84, "offset": 106}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/append/product-block.ejs", "start": {"line": 2, "col": 100, "offset": 122}, "end": {"line": 2, "col": 118, "offset": 140}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/append/product-card-slots.ejs", "start": {"line": 2, "col": 44, "offset": 68}, "end": {"line": 2, "col": 116, "offset": 140}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/append/product-card-slots.ejs", "start": {"line": 2, "col": 142, "offset": 166}, "end": {"line": 2, "col": 173, "offset": 197}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/append/product-slots.ejs", "start": {"line": 4, "col": 42, "offset": 119}, "end": {"line": 4, "col": 138, "offset": 215}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/append/product-slots.ejs", "start": {"line": 13, "col": 33, "offset": 433}, "end": {"line": 13, "col": 51, "offset": 451}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/append/product-slots.ejs", "start": {"line": 22, "col": 15, "offset": 599}, "end": {"line": 22, "col": 33, "offset": 617}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-offers-notification/src/append/product-slots.ejs", "start": {"line": 52, "col": 23, "offset": 1839}, "end": {"line": 52, "col": 33, "offset": 1849}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-offers-notification/src/append/product-slots.ejs", "start": {"line": 92, "col": 23, "offset": 2981}, "end": {"line": 92, "col": 33, "offset": 2991}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-opinioes-verificadas/src/append/body.ejs", "start": {"line": 4, "col": 7, "offset": 71}, "end": {"line": 4, "col": 77, "offset": 141}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-opinioes-verificadas/src/append/body.ejs", "start": {"line": 19, "col": 31, "offset": 612}, "end": {"line": 19, "col": 41, "offset": 622}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-opinioes-verificadas/src/append/product-block.ejs", "start": {"line": 5, "col": 24, "offset": 166}, "end": {"line": 5, "col": 42, "offset": 184}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-opinioes-verificadas/src/append/product-block.ejs", "start": {"line": 7, "col": 19, "offset": 230}, "end": {"line": 7, "col": 52, "offset": 263}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-opinioes-verificadas/src/append/product-block.ejs", "start": {"line": 8, "col": 22, "offset": 286}, "end": {"line": 8, "col": 77, "offset": 341}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-opinioes-verificadas/src/append/product-block.ejs", "start": {"line": 9, "col": 18, "offset": 360}, "end": {"line": 9, "col": 50, "offset": 392}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-opinioes-verificadas/src/append/product-block.ejs", "start": {"line": 10, "col": 17, "offset": 410}, "end": {"line": 10, "col": 70, "offset": 463}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-opinioes-verificadas/src/append/product-block.ejs", "start": {"line": 11, "col": 23, "offset": 487}, "end": {"line": 11, "col": 71, "offset": 535}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-opinioes-verificadas/src/append/product-block.ejs", "start": {"line": 17, "col": 22, "offset": 640}, "end": {"line": 17, "col": 40, "offset": 658}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-opinioes-verificadas/src/append/product-block.ejs", "start": {"line": 18, "col": 25, "offset": 684}, "end": {"line": 18, "col": 58, "offset": 717}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-opinioes-verificadas/src/append/product-block.ejs", "start": {"line": 19, "col": 24, "offset": 742}, "end": {"line": 19, "col": 56, "offset": 774}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-opinioes-verificadas/src/append/product-block.ejs", "start": {"line": 20, "col": 23, "offset": 798}, "end": {"line": 20, "col": 76, "offset": 851}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-opinioes-verificadas/src/append/product-block.ejs", "start": {"line": 21, "col": 27, "offset": 879}, "end": {"line": 21, "col": 75, "offset": 927}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-opinioes-verificadas/src/append/product-block.ejs", "start": {"line": 22, "col": 25, "offset": 953}, "end": {"line": 22, "col": 43, "offset": 971}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-opinioes-verificadas/src/append/product-slots.ejs", "start": {"line": 4, "col": 66, "offset": 215}, "end": {"line": 4, "col": 84, "offset": 233}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-opinioes-verificadas/src/append/product-slots.ejs", "start": {"line": 7, "col": 60, "offset": 327}, "end": {"line": 7, "col": 78, "offset": 345}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-opinioes-verificadas/src/append/stamps.ejs", "start": {"line": 3, "col": 5, "offset": 68}, "end": {"line": 3, "col": 69, "offset": 132}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tag-manager/src/append/body.ejs", "start": {"line": 1, "col": 68, "offset": 67}, "end": {"line": 1, "col": 97, "offset": 96}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tag-manager/src/append/head.ejs", "start": {"line": 2, "col": 35, "offset": 65}, "end": {"line": 2, "col": 62, "offset": 92}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.var-in-script-tag.var-in-script-tag", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tag-manager/src/append/head.ejs", "start": {"line": 2, "col": 35, "offset": 65}, "end": {"line": 2, "col": 62, "offset": 92}, "extra": {"message": "Detected a template variable used in a script tag. Although template variables are HTML escaped, HTML escaping does not always prevent cross-site scripting (XSS) attacks when used directly in JavaScript. If you need this data on the rendered page, consider placing it in the HTML portion (outside of a script tag). Alternatively, use a JavaScript-specific encoder, such as the one available in OWASP ESAPI.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://www.veracode.com/blog/secure-development/nodejs-template-engines-why-default-encoders-are-not-enough", "https://github.com/ESAPI/owasp-esapi-js"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.var-in-script-tag.var-in-script-tag", "shortlink": "https://sg.run/Ek9L"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tag-manager/src/append/head.ejs", "start": {"line": 4, "col": 9, "offset": 126}, "end": {"line": 4, "col": 66, "offset": 183}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tag-manager/src/append/head.ejs", "start": {"line": 7, "col": 62, "offset": 416}, "end": {"line": 7, "col": 89, "offset": 443}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tag-manager/src/append/head.ejs", "start": {"line": 12, "col": 30, "offset": 762}, "end": {"line": 12, "col": 57, "offset": 789}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tag-manager/src/append/head.ejs", "start": {"line": 12, "col": 60, "offset": 792}, "end": {"line": 12, "col": 89, "offset": 821}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.var-in-script-tag.var-in-script-tag", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tag-manager/src/append/head.ejs", "start": {"line": 12, "col": 60, "offset": 792}, "end": {"line": 12, "col": 89, "offset": 821}, "extra": {"message": "Detected a template variable used in a script tag. Although template variables are HTML escaped, HTML escaping does not always prevent cross-site scripting (XSS) attacks when used directly in JavaScript. If you need this data on the rendered page, consider placing it in the HTML portion (outside of a script tag). Alternatively, use a JavaScript-specific encoder, such as the one available in OWASP ESAPI.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://www.veracode.com/blog/secure-development/nodejs-template-engines-why-default-encoders-are-not-enough", "https://github.com/ESAPI/owasp-esapi-js"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.var-in-script-tag.var-in-script-tag", "shortlink": "https://sg.run/Ek9L"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.insecure-object-assign.insecure-object-assign", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tag-manager/src/lib/watch-app-routes.js", "start": {"line": 122, "col": 17, "offset": 3909}, "end": {"line": 122, "col": 65, "offset": 3957}, "extra": {"message": "Depending on the context, user control data in `Object.assign` can cause web response to include data that it should not have or can lead to a mass assignment vulnerability.", "metadata": {"cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "owasp": ["A01:2021 - Broken Access Control"], "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Mass_Assignment_Cheat_Sheet.html", "https://en.wikipedia.org/wiki/Mass_assignment_vulnerability"], "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/javascript.lang.security.insecure-object-assign.insecure-object-assign", "shortlink": "https://sg.run/2R0D"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tawkto/src/append/body.ejs", "start": {"line": 7, "col": 33, "offset": 247}, "end": {"line": 7, "col": 64, "offset": 278}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tawkto/src/append/body.ejs", "start": {"line": 7, "col": 65, "offset": 279}, "end": {"line": 7, "col": 109, "offset": 323}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tiktok-pixel/src/append/head.ejs", "start": {"line": 9, "col": 20, "offset": 904}, "end": {"line": 9, "col": 75, "offset": 959}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tiktok-pixel/src/append/head.ejs", "start": {"line": 10, "col": 15, "offset": 1094}, "end": {"line": 10, "col": 43, "offset": 1122}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.var-in-script-tag.var-in-script-tag", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tiktok-pixel/src/append/head.ejs", "start": {"line": 10, "col": 15, "offset": 1094}, "end": {"line": 10, "col": 43, "offset": 1122}, "extra": {"message": "Detected a template variable used in a script tag. Although template variables are HTML escaped, HTML escaping does not always prevent cross-site scripting (XSS) attacks when used directly in JavaScript. If you need this data on the rendered page, consider placing it in the HTML portion (outside of a script tag). Alternatively, use a JavaScript-specific encoder, such as the one available in OWASP ESAPI.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://www.veracode.com/blog/secure-development/nodejs-template-engines-why-default-encoders-are-not-enough", "https://github.com/ESAPI/owasp-esapi-js"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.var-in-script-tag.var-in-script-tag", "shortlink": "https://sg.run/Ek9L"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-trustvox/src/append/body.ejs", "start": {"line": 3, "col": 43, "offset": 118}, "end": {"line": 3, "col": 73, "offset": 148}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-trustvox/src/append/body.ejs", "start": {"line": 14, "col": 36, "offset": 525}, "end": {"line": 14, "col": 66, "offset": 555}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-trustvox/src/append/body.ejs", "start": {"line": 37, "col": 10, "offset": 1352}, "end": {"line": 37, "col": 87, "offset": 1429}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.var-in-script-src.var-in-script-src", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-trustvox/src/append/body.ejs", "start": {"line": 37, "col": 10, "offset": 1352}, "end": {"line": 37, "col": 87, "offset": 1429}, "extra": {"message": "Detected a template variable used as the 'src' in a script tag. Although template variables are HTML escaped, HTML escaping does not always prevent malicious URLs from being injected and could results in a cross-site scripting (XSS) vulnerability. Prefer not to dynamically generate the 'src' attribute and use static URLs instead. If you must do this, carefully check URLs against an allowlist and be sure to URL-encode the result.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://www.veracode.com/blog/secure-development/nodejs-template-engines-why-default-encoders-are-not-enough", "https://github.com/ESAPI/owasp-esapi-js"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.var-in-script-src.var-in-script-src", "shortlink": "https://sg.run/ndxZ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-trustvox/src/append/product-slots.ejs", "start": {"line": 15, "col": 32, "offset": 329}, "end": {"line": 15, "col": 57, "offset": 354}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-yourviews/src/append/body.ejs", "start": {"line": 10, "col": 55, "offset": 327}, "end": {"line": 10, "col": 86, "offset": 358}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-yourviews/src/append/product-block.ejs", "start": {"line": 6, "col": 47, "offset": 201}, "end": {"line": 6, "col": 65, "offset": 219}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-yourviews/src/append/product-block.ejs", "start": {"line": 7, "col": 49, "offset": 270}, "end": {"line": 7, "col": 68, "offset": 289}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-yourviews/src/append/product-block.ejs", "start": {"line": 8, "col": 50, "offset": 341}, "end": {"line": 8, "col": 65, "offset": 356}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-yourviews/src/append/product-block.ejs", "start": {"line": 9, "col": 50, "offset": 408}, "end": {"line": 9, "col": 70, "offset": 428}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-yourviews/src/append/stamps.ejs", "start": {"line": 5, "col": 44, "offset": 120}, "end": {"line": 5, "col": 57, "offset": 133}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-yourviews/src/append/stamps.ejs", "start": {"line": 5, "col": 95, "offset": 171}, "end": {"line": 5, "col": 108, "offset": 184}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "path": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-yourviews/src/append/stamps.ejs", "start": {"line": 5, "col": 169, "offset": 245}, "end": {"line": 5, "col": 200, "offset": 276}, "extra": {"message": "Detected an explicit unescape in an EJS template, using '<%- ... %>' If external data can reach these locations, your application is exposed to a cross-site scripting (XSS) vulnerability. Use '<%= ... %>' to escape this data. If you need escaping, ensure no external data can reach this location.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["http://www.managerjs.com/blog/2015/05/will-ejs-escape-save-me-from-xss-sorta/"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.ejs.explicit-unescape.template-explicit-unescape", "shortlink": "https://sg.run/dKXQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/ecomplus_storefront/scripts/build-themes.js", "start": {"line": 62, "col": 53, "offset": 1485}, "end": {"line": 62, "col": 61, "offset": 1493}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/ecomplus_storefront/scripts/build-themes.js", "start": {"line": 72, "col": 56, "offset": 1965}, "end": {"line": 72, "col": 61, "offset": 1970}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/ecomplus_storefront/scripts/build-themes.js", "start": {"line": 106, "col": 55, "offset": 2845}, "end": {"line": 106, "col": 60, "offset": 2850}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/ecomplus_storefront/scripts/setup.js", "start": {"line": 15, "col": 32, "offset": 331}, "end": {"line": 15, "col": 35, "offset": 334}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/ecomplus_storefront/scripts/setup.js", "start": {"line": 15, "col": 37, "offset": 336}, "end": {"line": 15, "col": 45, "offset": 344}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/ecomplus_storefront/scripts/setup.js", "start": {"line": 27, "col": 40, "offset": 720}, "end": {"line": 27, "col": 43, "offset": 723}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/ecomplus_storefront/scripts/setup.js", "start": {"line": 27, "col": 45, "offset": 725}, "end": {"line": 27, "col": 80, "offset": 760}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/ecomplus_storefront/.editorconfig", "downloaded_repos/ecomplus_storefront/.github/ISSUE_TEMPLATE/bug_report.md", "downloaded_repos/ecomplus_storefront/.github/ISSUE_TEMPLATE/config.yml", "downloaded_repos/ecomplus_storefront/.github/ISSUE_TEMPLATE/feature_request.md", "downloaded_repos/ecomplus_storefront/.github/build-docs", "downloaded_repos/ecomplus_storefront/.github/build-themes", "downloaded_repos/ecomplus_storefront/.github/pull_request_template.md", "downloaded_repos/ecomplus_storefront/.github/renovate.json", "downloaded_repos/ecomplus_storefront/.github/workflows/build-docs.yml", "downloaded_repos/ecomplus_storefront/.github/workflows/build-themes.yml", "downloaded_repos/ecomplus_storefront/.github/workflows/compress-images.yml", "downloaded_repos/ecomplus_storefront/.github/workflows/lighthouse.yml", "downloaded_repos/ecomplus_storefront/.github/workflows/publish.yml", "downloaded_repos/ecomplus_storefront/.gitignore", "downloaded_repos/ecomplus_storefront/.gitmodules", "downloaded_repos/ecomplus_storefront/.gitpod.yml", "downloaded_repos/ecomplus_storefront/.husky/.gitignore", "downloaded_repos/ecomplus_storefront/.husky/commit-msg", "downloaded_repos/ecomplus_storefront/.lintstagedrc", "downloaded_repos/ecomplus_storefront/.nvmrc", "downloaded_repos/ecomplus_storefront/.vscode/extensions.json", "downloaded_repos/ecomplus_storefront/.vscode/settings.json", "downloaded_repos/ecomplus_storefront/.vuepress/components/DemoAnimateCss.vue", "downloaded_repos/ecomplus_storefront/.vuepress/components/DemoInstantSearch.vue", "downloaded_repos/ecomplus_storefront/.vuepress/components/DemoProductCard.vue", "downloaded_repos/ecomplus_storefront/.vuepress/config.js", "downloaded_repos/ecomplus_storefront/.vuepress/enhanceApp.js", "downloaded_repos/ecomplus_storefront/.vuepress/public/assets/img/banner.png", "downloaded_repos/ecomplus_storefront/.vuepress/public/assets/img/cms-colors.png", "downloaded_repos/ecomplus_storefront/.vuepress/public/assets/img/cms-config.png", "downloaded_repos/ecomplus_storefront/.vuepress/public/assets/img/cms-dd.gif", "downloaded_repos/ecomplus_storefront/.vuepress/public/assets/img/cms-html-section.png", "downloaded_repos/ecomplus_storefront/.vuepress/public/assets/img/cms-info.png", "downloaded_repos/ecomplus_storefront/.vuepress/public/assets/img/cms-insert-code.png", "downloaded_repos/ecomplus_storefront/.vuepress/public/assets/img/css-vars.png", "downloaded_repos/ecomplus_storefront/.vuepress/public/assets/img/ejs-info-bar.png", "downloaded_repos/ecomplus_storefront/.vuepress/public/assets/img/ejs-meta.png", "downloaded_repos/ecomplus_storefront/.vuepress/public/assets/img/logo.png", "downloaded_repos/ecomplus_storefront/.vuepress/styles/components/DemoInstantSearch.scss", "downloaded_repos/ecomplus_storefront/.vuepress/styles/components/DemoProductCard.scss", "downloaded_repos/ecomplus_storefront/.vuepress/styles/demo.scss", "downloaded_repos/ecomplus_storefront/.vuepress/styles/index.styl", "downloaded_repos/ecomplus_storefront/.vuepress/styles/palette.styl", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/.npmignore", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/CHANGELOG.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/README.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/__tests__/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/netlify.toml", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/package.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/App.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/components/CreditCardForm.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/components/EcCheckout.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/components/EcOrder.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/components/EcOrderInfo.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/components/EcOrdersList.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/components/EcSummary.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/components/PaymentMethods.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/components/html/CreditCardForm.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/components/html/EcCheckout.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/components/html/EcOrder.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/components/html/EcOrderInfo.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/components/html/EcOrdersList.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/components/html/EcSummary.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/components/html/PaymentMethods.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/components/js/CreditCardForm.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/components/js/EcCheckout.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/components/js/EcOrder.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/components/js/EcOrderInfo.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/components/js/EcOrdersList.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/components/js/EcSummary.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/components/js/PaymentMethods.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/components/scss/CreditCardForm.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/components/scss/EcCheckout.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/components/scss/EcOrder.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/components/scss/EcOrderInfo.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/components/scss/EcOrdersList.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/components/scss/EcSummary.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/components/scss/PaymentMethods.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/lib/base-modules-request-data.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/lib/credit-card.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/lib/i18n.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/lib/load-payment-client.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/lib/sync-cart-to-api.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/lib/utils.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/main.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/plugins/clipboard.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/plugins/toast.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/router/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/store/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/store/modules/account.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/store/modules/checkout.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/views/Account.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/views/Cart.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/views/Checkout.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/views/Confirmation.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/views/Order.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/views/js/Account.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/views/js/Cart.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/views/js/Checkout.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/views/js/Confirmation.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/views/js/Order.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/vue.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/webpack.public-path.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/.npmignore", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/CHANGELOG.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/README.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/__tests__/__fixtures__/cart.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/__tests__/__fixtures__/product.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/__tests__/components/TsABackdrop.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/__tests__/components/TsAPicture.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/__tests__/components/TsAPrices.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/__tests__/components/TsBootstrap.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/__tests__/components/TsCartItem.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/__tests__/components/TsCartQuickview.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/__tests__/components/TsInstantSearch.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/__tests__/components/TsLoginModal.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/__tests__/components/TsProductCard.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/__tests__/components/TsProductGallery.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/__tests__/components/TsQuantitySelector.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/__tests__/components/TsSearchEngine.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/__tests__/components/TsTheProduct.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/__tests__/server/App.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/__tests__/server/main.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/__tests__/server/public/index.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/all.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/docs/ABackdrop.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/docs/APicture.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/docs/APrices.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/docs/CartItem.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/docs/CartQuickview.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/docs/InstantSearch.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/docs/LoginModal.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/docs/ProductCard.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/docs/ProductGallery.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/docs/ProductVariations.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/docs/SearchEngine.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/docs/ShippingCalculator.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/docs/ShippingLine.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/docs/TheProduct.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/package.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/AAlert.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/ABackdrop.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/ALink.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/APagination.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/APicture.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/APrices.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/AShare.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/AccountAddresses.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/AccountForm.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/AccountPoints.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/AddressForm.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/BuyTogether.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/CartItem.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/CartQuickview.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/DiscountApplier.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/EarnPointsProgress.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/InputDate.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/InputDocNumber.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/InputPhone.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/InputZipCode.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/InstantSearch.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/ItemCustomizations.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/KitProductVariations.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/LoginBlock.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/LoginModal.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/PaymentOption.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/PointsApplier.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/ProductCard.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/ProductGallery.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/ProductQuickview.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/ProductVariations.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/QuantitySelector.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/RecommendedItems.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/SearchEngine.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/ShippingCalculator.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/ShippingLine.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/TheAccount.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/TheCart.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/TheProduct.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/AAlert.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/ABackdrop.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/ALink.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/APagination.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/APicture.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/APrices.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/AShare.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/AccountAddresses.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/AccountForm.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/AccountPoints.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/AddressForm.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/BuyTogether.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/CartItem.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/CartQuickview.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/DiscountApplier.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/EarnPointsProgress.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/InputDate.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/InputDocNumber.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/InputPhone.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/InputZipCode.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/InstantSearch.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/ItemCustomizations.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/KitProductVariations.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/LoginBlock.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/LoginModal.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/PaymentOption.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/PointsApplier.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/ProductCard.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/ProductGallery.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/ProductQuickview.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/ProductVariations.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/QuantitySelector.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/RecommendedItems.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/SearchEngine.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/ShippingCalculator.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/ShippingLine.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/TheAccount.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/TheCart.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/html/TheProduct.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/AAlert.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/ABackdrop.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/ALink.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/APagination.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/APicture.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/APrices.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/AShare.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/AccountAddresses.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/AccountForm.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/AccountPoints.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/AddressForm.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/BuyTogether.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/CartItem.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/CartQuickview.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/DiscountApplier.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/EarnPointsProgress.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/InputDate.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/InputDocNumber.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/InputPhone.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/InputZipCode.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/InstantSearch.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/ItemCustomizations.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/KitProductVariations.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/LoginBlock.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/LoginModal.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/PaymentOption.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/PointsApplier.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/ProductCard.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/ProductGallery.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/ProductQuickview.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/ProductVariations.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/QuantitySelector.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/RecommendedItems.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/SearchEngine.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/ShippingCalculator.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/ShippingLine.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/TheAccount.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/TheCart.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/TheProduct.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/helpers/add-idle-callback.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/helpers/check-form-validity.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/helpers/favorite-products.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/helpers/scroll-to-element.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/helpers/sort-apps.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/helpers/wait-storefront-info.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/scss/ABackdrop.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/scss/APicture.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/scss/APrices.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/scss/AccountAddresses.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/scss/AccountForm.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/scss/AccountPoints.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/scss/AddressForm.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/scss/BuyTogether.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/scss/CartItem.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/scss/CartQuickview.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/scss/DiscountApplier.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/scss/EarnPointsProgress.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/scss/InstantSearch.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/scss/KitProductVariations.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/scss/LoginBlock.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/scss/LoginModal.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/scss/PaymentOption.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/scss/ProductCard.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/scss/ProductGallery.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/scss/ProductQuickview.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/scss/ProductVariations.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/scss/QuantitySelector.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/scss/RecommendedItems.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/scss/SearchEngine.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/scss/ShippingCalculator.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/scss/ShippingLine.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/scss/TheAccount.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/scss/TheCart.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/scss/TheProduct.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/webpack.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/.npmignore", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/CHANGELOG.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/README.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/bin.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/content/README.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/content/blog/my-blog-post.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/content/dictionary/en_us.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/content/dictionary/pt_br.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/content/pages/about-us.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/content/pages/home.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/content/settings.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/firebase.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/functions/.gitignore", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/functions/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/functions/package-lock.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/functions/package.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/netlify.toml", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/package.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/src/assets/sitemap.xml.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/src/assets/sw.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/src/bin/bundler.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/src/bin/scripts/build.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/src/bin/scripts/serve.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/src/bin/server.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/src/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/src/lib/cms-collections.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/src/lib/config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/src/lib/entry.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/src/lib/get-assets-references.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/src/lib/get-store-data.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/src/lib/minify-html.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/src/lib/paths.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/src/renderer.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/src/webpack.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/storefront.webpack.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/template/README.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/template/assets/sample.txt", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/template/js/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/template/pages/#brands.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/template/pages/#categories.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/template/pages/#cms/blog.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/template/pages/#cms/pages.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/template/pages/#collections.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/template/pages/#products.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/template/pages/@/head.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/template/pages/@/scripts.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/template/pages/app/index.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/template/pages/index.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/template/public/admin/config.yml", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/template/public/admin/index.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/template/public/img/README.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/template/public/img/icon.png", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/template/public/img/large-icon.png", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/template/public/img/uploads/favicon.png", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/template/public/img/uploads/icon.png", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/template/public/img/uploads/large-icon.png", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/template/public/img/uploads/logo.png", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/template/public/img/uploads/og-image.png", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/template/scss/styles.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-renderer/CHANGELOG.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-renderer/README.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-renderer/functions/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-renderer/package.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-renderer/src/assets/ecom-manifest.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-renderer/src/assets/starter.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-renderer/src/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-renderer/src/lib/cms-collections.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-renderer/src/lib/config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-renderer/src/lib/entry.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-renderer/src/lib/get-assets-references.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-renderer/src/lib/get-store-data.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-renderer/src/lib/minify-html.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-renderer/src/lib/paths.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-renderer/src/renderer.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-starter/CHANGELOG.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-starter/package.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/.node-version", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/.npmignore", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/CHANGELOG.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/README.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/__fixtures__/empty.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/__fixtures__/placeholder.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/blog.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/brands.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/categories.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/code.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/collections.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/contacts.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/footer.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/header.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/home.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/info.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/maintenance.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/menu.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/pages/contato.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/pages/entrega.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/pages/faq.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/pages/pagamentos.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/pages/privacidade.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/pages/sobre-nos.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/pages/termos.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/pages/trocas.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/posts/esta-loja-e-um-pwa.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/products.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/search.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/settings.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/social.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/stamps.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/widgets/addi.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/widgets/analytics.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/widgets/compre-confie.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/widgets/ebit.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/widgets/fb-pixel.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/widgets/gmc-ratings.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/widgets/martan.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/widgets/minicart.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/widgets/offers-notification.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/widgets/opinioes-verificadas.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/widgets/product-card.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/widgets/product.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/widgets/search-engine.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/widgets/search.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/widgets/tag-manager.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/widgets/tawkto.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/widgets/tiktok-pixel.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/widgets/trustvox.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/widgets/user.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/content/widgets/yourviews.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/dist.package.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/docs/01-elements.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/docs/02-pages.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/docs/03-checkout.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/docs/04-cms.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/docs/05-javascript.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/docs/06-widgets.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/package.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/scripts/copy.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/scripts/install.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/scripts/lib/recursive-copy.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/scripts/pack.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/scripts/theme.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/storefront.webpack.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/admin.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/checkout.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/lib/config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/lib/ecom.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/lib/emitter.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/lib/fallback-404.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/lib/fetch-info.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/lib/get-scoped-slots.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/lib/glide-slides.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/lib/lazy-load.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/lib/load-widgets.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/lib/menu.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/lib/overlay.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/lib/persist-utm.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/lib/search.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/lib/shopping-cart.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/lib/theme.js.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/lib/utils.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/base-config/collections/blog-posts.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/base-config/collections/extra-pages.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/base-config/collections/layout/code.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/base-config/collections/layout/footer.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/base-config/collections/layout/header.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/base-config/collections/layout/menu.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/base-config/collections/layout.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/base-config/collections/pages/blog.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/base-config/collections/pages/brands.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/base-config/collections/pages/categories.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/base-config/collections/pages/collections.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/base-config/collections/pages/home.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/base-config/collections/pages/products.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/base-config/collections/pages/search.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/base-config/collections/pages/types/retail.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/base-config/collections/pages.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/base-config/collections/settings/contacts.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/base-config/collections/settings/general.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/base-config/collections/settings/info.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/base-config/collections/settings/maintenance.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/base-config/collections/settings/social.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/base-config/collections/settings/stamps.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/base-config/collections/settings.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/base-config/collections/widgets.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/base-config/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/base-config/sections.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/ecomplus-identity.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/init.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/pages-preview.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/preview/base-preview.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/preview/blog-preview.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/preview/brands-preview.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/preview/categories-preview.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/preview/code-preview.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/preview/collections-preview.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/preview/fetch-page.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/preview/home-preview.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/preview/product-preview.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/preview/search-preview.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/preview/settings/general.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/preview/settings/lib/color-functions.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/preview/settings/lib/set-custom-font.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/js/netlify-cms/preview/virtual-doc.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/#brands.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/#categories.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/#cms/pages.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/#cms/posts.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/#collections.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/#products.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/404.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/head.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/helpers/widgets-append.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/json.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/layout/footer.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/layout/header.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/layout/inc/ecom-credits.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/layout/inc/logo.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/layout/inc/networks.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/layout/menu.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/layout/sections.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/meta.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/scripts.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/banner-slider.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/banners-grid.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/blog-post.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/blog.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/brand-retail.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/brands-carousel.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/breadcrumbs.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/buy-together.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/category-retail.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/collection-retail.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/collection-shelf.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/custom-content.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/custom-html.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/document-banner.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/document-description.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/dynamic-showcase.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/extra-page.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/inc/banner.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/inc/loading.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/inc/md-content.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/inc/product-item.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/inc/products-carousel.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/inc/recommendations-shelf.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/inc/retail-grid.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/inc/timer.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/info-bar.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/offers-timer.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/page-title.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/product-block.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/product-description.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/product-specifications.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/recommended-products.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/related-products.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/responsive-banner.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/search-engine.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/shelfs-nav.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/sections/stamps.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/@/view.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/admin/index.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/app/index.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/blog.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/index.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/maintenance/index.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/pages/search.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/admin/config.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/assets/cms-preview.css", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/assets/cms.css", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/assets/cvv.png", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/assets/icons/bootstrap-icons/font/storefront-icons.woff2", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/assets/icons/feather-icons/font/storefront-icons.woff2", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/assets/icons/font-awesome/font/storefront-icons.woff2", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/assets/icons/line-awesome/font/storefront-icons.woff2", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/assets/icons/tabler-icons/font/storefront-icons.woff2", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/assets/img-placeholder.png", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/assets/payments.png", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/assets/ssl-safe.png", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/img/icon.png", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/img/large-icon.png", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/img/uploads/banner1.png", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/img/uploads/banner2.png", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/img/uploads/banner2.webp", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/img/uploads/favicon.png", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/img/uploads/headless.png", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/img/uploads/headphone.png", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/img/uploads/headphone.webp", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/img/uploads/icon.png", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/img/uploads/large-icon.png", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/img/uploads/logo.png", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/img/uploads/logo.webp", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/img/uploads/og-image.png", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/img/uploads/passion.png", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/img/uploads/passion.webp", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/img/uploads/pwa-reliable.png", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/img/uploads/rect8589.png", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/img/uploads/rect859.png", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/img/uploads/rect89.png", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/img/uploads/rect89.webp", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/img/uploads/ssl-safe.png", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/public/robots.txt", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/_main.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/_variables.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/components/_app.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/components/_banner-slider.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/components/_banner.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/components/_brands-carousel.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/components/_footer.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/components/_header.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/components/_hero-banner.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/components/_html-clearfix.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/components/_info-bar.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/components/_loading.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/components/_maintenance.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/components/_md-content.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/components/_menu.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/components/_offers-timer.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/components/_page-title.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/components/_pay-icon.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/components/_product-card.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/components/_product.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/components/_products-carousel.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/components/_retail-grid.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/components/_rounded-icon.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/components/_shelfs-nav.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/components/_stamps.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/components/_timer.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/components/_top-bar.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/libs/_glide.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/styles.scss.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/cerulean/_bootswatch.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/cerulean/_custom.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/cerulean/_variables.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/clean-dark/_custom.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/clean-dark/_variables.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/clean-gray/_custom.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/clean-gray/_variables.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/clean-white/_custom.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/clean-white/_variables.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/cosmo/_bootswatch.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/cosmo/_custom.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/cosmo/_variables.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/cyborg/_bootswatch.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/cyborg/_custom.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/cyborg/_variables.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/darkly/_bootswatch.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/darkly/_custom.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/darkly/_variables.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/ecom-beauty/_custom.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/ecom-beauty/_footer.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/ecom-beauty/_header.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/ecom-beauty/_product-card.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/ecom-beauty/_product.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/ecom-beauty/_variables.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/ecom-beauty/config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/flatly/_bootswatch.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/flatly/_custom.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/flatly/_variables.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/journal/_bootswatch.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/journal/_custom.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/journal/_variables.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/litera/_bootswatch.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/litera/_custom.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/litera/_variables.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/lumen/_bootswatch.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/lumen/_custom.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/lumen/_variables.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/lux/_bootswatch.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/lux/_custom.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/lux/_variables.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/materia/_bootswatch.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/materia/_custom.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/materia/_variables.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/minty/_bootswatch.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/minty/_custom.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/minty/_variables.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/niche-baby/_custom.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/niche-baby/_variables.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/niche-flowers/_custom.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/niche-flowers/_variables.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/niche-game/_custom.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/niche-game/_variables.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/pulse/_bootswatch.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/pulse/_custom.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/pulse/_variables.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/sandstone/_bootswatch.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/sandstone/_custom.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/sandstone/_variables.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/simplex/_bootswatch.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/simplex/_custom.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/simplex/_variables.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/sketchy/_bootswatch.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/sketchy/_custom.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/sketchy/_variables.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/slate/_bootswatch.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/slate/_custom.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/slate/_variables.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/solar/_bootswatch.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/solar/_custom.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/solar/_variables.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/spacelab/_bootswatch.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/spacelab/_custom.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/spacelab/_variables.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/united/_bootswatch.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/united/_custom.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/united/_variables.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/yeti/_bootswatch.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/yeti/_custom.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/template/scss/themes/yeti/_variables.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-template/webpack.externals.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/.npmignore", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/CHANGELOG.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/README.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/__tests__/index.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/README", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/bootstrap-icons/LICENSE.txt", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/bootstrap-icons/README.txt", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/bootstrap-icons/config.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/bootstrap-icons/css/animation.css", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/bootstrap-icons/css/storefront-icons-codes.css", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/bootstrap-icons/css/storefront-icons-embedded.css", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/bootstrap-icons/css/storefront-icons-ie7-codes.css", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/bootstrap-icons/css/storefront-icons-ie7.css", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/bootstrap-icons/css/storefront-icons.css", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/bootstrap-icons/demo.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/bootstrap-icons/font/storefront-icons.eot", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/bootstrap-icons/font/storefront-icons.svg", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/bootstrap-icons/font/storefront-icons.ttf", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/bootstrap-icons/font/storefront-icons.woff", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/bootstrap-icons/font/storefront-icons.woff2", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/feather-icons/LICENSE.txt", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/feather-icons/README.txt", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/feather-icons/config.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/feather-icons/css/animation.css", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/feather-icons/css/storefront-icons-codes.css", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/feather-icons/css/storefront-icons-embedded.css", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/feather-icons/css/storefront-icons-ie7-codes.css", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/feather-icons/css/storefront-icons-ie7.css", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/feather-icons/css/storefront-icons.css", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/feather-icons/demo.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/feather-icons/font/storefront-icons.eot", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/feather-icons/font/storefront-icons.svg", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/feather-icons/font/storefront-icons.ttf", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/feather-icons/font/storefront-icons.woff", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/feather-icons/font/storefront-icons.woff2", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/font-awesome/LICENSE.txt", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/font-awesome/README.txt", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/font-awesome/config.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/font-awesome/css/animation.css", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/font-awesome/css/storefront-icons-codes.css", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/font-awesome/css/storefront-icons-embedded.css", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/font-awesome/css/storefront-icons-ie7-codes.css", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/font-awesome/css/storefront-icons-ie7.css", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/font-awesome/css/storefront-icons.css", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/font-awesome/demo.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/font-awesome/font/storefront-icons.eot", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/font-awesome/font/storefront-icons.svg", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/font-awesome/font/storefront-icons.ttf", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/font-awesome/font/storefront-icons.woff", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/font-awesome/font/storefront-icons.woff2", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/line-awesome/LICENSE.txt", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/line-awesome/README.txt", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/line-awesome/config.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/line-awesome/css/animation.css", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/line-awesome/css/storefront-icons-codes.css", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/line-awesome/css/storefront-icons-embedded.css", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/line-awesome/css/storefront-icons-ie7-codes.css", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/line-awesome/css/storefront-icons-ie7.css", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/line-awesome/css/storefront-icons.css", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/line-awesome/demo.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/line-awesome/font/storefront-icons.eot", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/line-awesome/font/storefront-icons.svg", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/line-awesome/font/storefront-icons.ttf", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/line-awesome/font/storefront-icons.woff", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/line-awesome/font/storefront-icons.woff2", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/tabler-icons/LICENSE.txt", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/tabler-icons/README.txt", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/tabler-icons/config.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/tabler-icons/css/animation.css", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/tabler-icons/css/storefront-icons-codes.css", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/tabler-icons/css/storefront-icons-embedded.css", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/tabler-icons/css/storefront-icons-ie7-codes.css", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/tabler-icons/css/storefront-icons-ie7.css", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/tabler-icons/css/storefront-icons.css", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/tabler-icons/demo.html", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/tabler-icons/font/storefront-icons.eot", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/tabler-icons/font/storefront-icons.svg", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/tabler-icons/font/storefront-icons.ttf", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/tabler-icons/font/storefront-icons.woff", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/assets/icons/tabler-icons/font/storefront-icons.woff2", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/docs/01-components.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/docs/02-colors.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/docs/03-icons.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/docs/04-animations.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/docs/05-vars.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/docs/06-javascript.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/docs/07-theming.md", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/package.json", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/_animate.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/_custom.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/_functions.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/_icons.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/_imports.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/_mixins.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/_reboot.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/_variables.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/_vars.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/components/_alert.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/components/_badge.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/components/_buttons.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/components/_dropdown.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/components/_forms.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/components/_list-group.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/components/_modal.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/components/_nav.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/components/_pagination.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/components/_progress.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/components/_tables.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/content/_code.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/content/_images.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/mixins/_alert.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/mixins/_badge.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/mixins/_buttons.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/mixins/_forms.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/mixins/_gradients.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/mixins/_list-group.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/mixins/_table-row.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/presets/_colors.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/presets/_defaults.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/presets/vars/_colors.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/styles.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/utilities/_spacing.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/scss/utilities/_text.scss", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/src/$.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/src/_env.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/src/animate-css.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/src/bootstrap.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/src/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-twbs/webpack.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-addi/.npmignore", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-addi/CHANGELOG.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-addi/README.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-addi/__tests__/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-addi/cms.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-addi/package.json", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-addi/src/append/body.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-addi/src/append/product-slots.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-addi/src/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-addi/webpack.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-analytics/.npmignore", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-analytics/CHANGELOG.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-analytics/README.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-analytics/__tests__/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-analytics/cms.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-analytics/package.json", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-analytics/src/append/head.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-analytics/src/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-analytics/src/lib/common.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-analytics/src/lib/watch-app-routes.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-analytics/src/lib/watch-shopping-cart.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-analytics/webpack.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-compre-confie/.npmignore", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-compre-confie/CHANGELOG.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-compre-confie/README.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-compre-confie/__tests__/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-compre-confie/cms.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-compre-confie/package.json", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-compre-confie/src/append/stamps.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-compre-confie/src/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-compre-confie/webpack.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-ebit/.npmignore", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-ebit/CHANGELOG.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-ebit/README.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-ebit/__tests__/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-ebit/cms.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-ebit/package.json", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-ebit/src/append/stamps.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-ebit/src/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-ebit/webpack.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-fb-pixel/.npmignore", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-fb-pixel/CHANGELOG.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-fb-pixel/README.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-fb-pixel/__tests__/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-fb-pixel/cms.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-fb-pixel/package.json", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-fb-pixel/src/append/body.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-fb-pixel/src/append/head.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-fb-pixel/src/append/test.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-fb-pixel/src/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-fb-pixel/src/lib/common.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-fb-pixel/src/lib/parse-context.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-fb-pixel/src/lib/watch-app-routes.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-fb-pixel/src/lib/watch-shopping-cart.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-fb-pixel/webpack.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-gmc-ratings/.npmignore", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-gmc-ratings/CHANGELOG.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-gmc-ratings/README.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-gmc-ratings/__tests__/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-gmc-ratings/cms.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-gmc-ratings/package.json", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-gmc-ratings/src/append/stamps.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-gmc-ratings/src/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-gmc-ratings/webpack.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/.npmignore", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/CHANGELOG.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/README.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/__tests__/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/cms.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/package.json", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/append/body.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/append/head.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/append/product-block.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/append/product-card-slots.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/append/product-slots.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/append/stamps.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/utils/get-width.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/utils/textToNumber.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/utils/time-ago.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/widgets/reviews/AuthorAndRating.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/widgets/reviews/AverageScore.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/widgets/reviews/AverageTotal.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/widgets/reviews/CardReview.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/widgets/reviews/GridView.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/widgets/reviews/HeaderExpanded.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/widgets/reviews/HeaderMinimal.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/widgets/reviews/ListView.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/widgets/reviews/Quickview.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/widgets/reviews/ReviewBody.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/widgets/reviews/ReviewReply.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/widgets/reviews/Reviews.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/widgets/reviews/Score.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/widgets/reviews/Sort.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/widgets/reviews/ThumbsPictures.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/widgets/reviews/Total.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/widgets/reviews/VerifiedPurchase.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/widgets/reviews/VideoPlayer.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/widgets/reviews/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/widgets/reviews/isRecommended.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/widgets/snippets/Rating.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/widgets/snippets/Snippets.vue", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/src/widgets/snippets/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-martan/webpack.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-minicart/.npmignore", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-minicart/CHANGELOG.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-minicart/README.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-minicart/__tests__/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-minicart/cms.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-minicart/package.json", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-minicart/src/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-minicart/webpack.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-offers-notification/.npmignore", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-offers-notification/CHANGELOG.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-offers-notification/README.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-offers-notification/__tests__/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-offers-notification/cms.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-offers-notification/package.json", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-offers-notification/src/append/product-slots.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-offers-notification/src/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-offers-notification/webpack.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-opinioes-verificadas/.npmignore", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-opinioes-verificadas/CHANGELOG.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-opinioes-verificadas/README.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-opinioes-verificadas/__tests__/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-opinioes-verificadas/cms.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-opinioes-verificadas/package.json", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-opinioes-verificadas/src/append/body.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-opinioes-verificadas/src/append/head.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-opinioes-verificadas/src/append/product-block.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-opinioes-verificadas/src/append/product-card-slots.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-opinioes-verificadas/src/append/product-slots.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-opinioes-verificadas/src/append/stamps.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-opinioes-verificadas/src/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-opinioes-verificadas/webpack.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-product/.npmignore", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-product/CHANGELOG.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-product/README.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-product/__tests__/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-product/cms.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-product/package.json", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-product/src/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-product/webpack.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-product-card/.npmignore", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-product-card/CHANGELOG.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-product-card/README.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-product-card/__tests__/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-product-card/cms.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-product-card/package.json", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-product-card/src/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-product-card/webpack.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-search/.npmignore", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-search/CHANGELOG.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-search/README.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-search/__tests__/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-search/cms.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-search/package.json", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-search/src/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-search/webpack.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-search-engine/.npmignore", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-search-engine/CHANGELOG.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-search-engine/README.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-search-engine/__tests__/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-search-engine/cms.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-search-engine/package.json", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-search-engine/src/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-search-engine/webpack.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tag-manager/.npmignore", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tag-manager/CHANGELOG.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tag-manager/README.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tag-manager/__tests__/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tag-manager/cms.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tag-manager/package.json", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tag-manager/src/append/body.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tag-manager/src/append/head.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tag-manager/src/append/test.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tag-manager/src/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tag-manager/src/lib/common.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tag-manager/src/lib/parse-context.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tag-manager/src/lib/parse-dom.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tag-manager/src/lib/watch-app-routes.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tag-manager/src/lib/watch-shopping-cart.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tag-manager/webpack.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tawkto/.npmignore", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tawkto/CHANGELOG.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tawkto/README.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tawkto/__tests__/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tawkto/cms.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tawkto/package.json", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tawkto/src/append/body.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tawkto/src/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tawkto/webpack.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tiktok-pixel/.npmignore", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tiktok-pixel/CHANGELOG.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tiktok-pixel/README.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tiktok-pixel/__tests__/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tiktok-pixel/cms.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tiktok-pixel/package.json", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tiktok-pixel/src/append/head.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tiktok-pixel/src/append/test.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tiktok-pixel/src/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tiktok-pixel/src/lib/common.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tiktok-pixel/src/lib/parse-context.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tiktok-pixel/src/lib/watch-app-routes.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tiktok-pixel/src/lib/watch-search.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tiktok-pixel/src/lib/watch-shopping-cart.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-tiktok-pixel/webpack.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-trustvox/.npmignore", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-trustvox/CHANGELOG.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-trustvox/README.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-trustvox/__tests__/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-trustvox/cms.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-trustvox/package.json", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-trustvox/src/append/body.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-trustvox/src/append/product-block.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-trustvox/src/append/product-card-slots.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-trustvox/src/append/product-slots.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-trustvox/src/append/stamps.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-trustvox/src/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-trustvox/webpack.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-user/.npmignore", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-user/CHANGELOG.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-user/README.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-user/__tests__/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-user/cms.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-user/package.json", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-user/src/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-user/webpack.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-yourviews/.npmignore", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-yourviews/CHANGELOG.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-yourviews/README.md", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-yourviews/__tests__/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-yourviews/cms.config.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-yourviews/package.json", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-yourviews/src/append/body.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-yourviews/src/append/product-block.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-yourviews/src/append/product-card-slots.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-yourviews/src/append/product-slots.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-yourviews/src/append/stamps.ejs", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-yourviews/src/index.js", "downloaded_repos/ecomplus_storefront/@ecomplus/widget-yourviews/webpack.config.js", "downloaded_repos/ecomplus_storefront/CODE_OF_CONDUCT.md", "downloaded_repos/ecomplus_storefront/CONTRIBUTING.md", "downloaded_repos/ecomplus_storefront/LICENSE", "downloaded_repos/ecomplus_storefront/README.md", "downloaded_repos/ecomplus_storefront/babel.config.json", "downloaded_repos/ecomplus_storefront/commitlint.config.js", "downloaded_repos/ecomplus_storefront/docs/customizando.md", "downloaded_repos/ecomplus_storefront/docs/customization.md", "downloaded_repos/ecomplus_storefront/example.env", "downloaded_repos/ecomplus_storefront/jsconfig.json", "downloaded_repos/ecomplus_storefront/lerna.json", "downloaded_repos/ecomplus_storefront/netlify.toml", "downloaded_repos/ecomplus_storefront/package.json", "downloaded_repos/ecomplus_storefront/postcss.config.js", "downloaded_repos/ecomplus_storefront/scripts/build-themes.js", "downloaded_repos/ecomplus_storefront/scripts/release-starter.js", "downloaded_repos/ecomplus_storefront/scripts/setup.js", "downloaded_repos/ecomplus_storefront/webpack.config.js"], "skipped": [{"path": "downloaded_repos/ecomplus_storefront/package-lock.json", "reason": "exceeded_size_limit"}]}, "time": {"rules": [], "rules_parse_time": 2.072659969329834, "profiling_times": {"config_time": 5.746313571929932, "core_time": 11.90693187713623, "ignores_time": 0.001688241958618164, "total_time": 17.655790328979492}, "parsing_time": {"total_time": 6.73323392868042, "per_file_time": {"mean": 0.01565868355507075, "std_dev": 0.0008213680413855332}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 56.403130531311035, "per_file_time": {"mean": 0.021356732499549776, "std_dev": 0.026316449349607193}, "very_slow_stats": {"time_ratio": 0.2931418737195963, "count_ratio": 0.002271866717152594}, "very_slow_files": [{"fpath": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-ebit/src/index.js", "ftime": 1.5444881916046143}, {"fpath": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-compre-confie/src/index.js", "ftime": 1.8569581508636475}, {"fpath": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-search-engine/src/index.js", "ftime": 1.9211061000823975}, {"fpath": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/TheProduct.js", "ftime": 3.1566059589385986}, {"fpath": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/components/js/EcOrderInfo.js", "ftime": 3.18865704536438}, {"fpath": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/store/modules/checkout.js", "ftime": 4.86630392074585}]}, "matching_time": {"total_time": 19.636489629745483, "per_file_and_rule_time": {"mean": 0.010039105127681735, "std_dev": 0.0007513597887356812}, "very_slow_stats": {"time_ratio": 0.304503625101985, "count_ratio": 0.01789366053169734}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-search-engine/src/index.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.18160486221313477}, {"fpath": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-fb-pixel/src/lib/watch-app-routes.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.18279004096984863}, {"fpath": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/components/js/EcOrderInfo.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.18732810020446777}, {"fpath": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/store/modules/checkout.js", "rule_id": "javascript.express.security.audit.express-open-redirect.express-open-redirect", "time": 0.22534990310668945}, {"fpath": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/components/js/EcCheckout.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.23659181594848633}, {"fpath": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/TheProduct.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.2407829761505127}, {"fpath": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/components/js/EcOrderInfo.js", "rule_id": "javascript.express.security.audit.express-open-redirect.express-open-redirect", "time": 0.24435091018676758}, {"fpath": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/TheProduct.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.27233195304870605}, {"fpath": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/store/modules/checkout.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.39743781089782715}, {"fpath": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/components/js/EcOrderInfo.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.44533801078796387}]}, "tainting_time": {"total_time": 10.330336332321167, "per_def_and_rule_time": {"mean": 0.0029969063917380813, "std_dev": 0.0002784820961139018}, "very_slow_stats": {"time_ratio": 0.26442349091088185, "count_ratio": 0.006672468813460981}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/TheProduct.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.06530904769897461}, {"fpath": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/store/modules/checkout.js", "fline": 1, "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.06532597541809082}, {"fpath": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/TheProduct.js", "fline": 1, "rule_id": "javascript.browser.security.open-redirect.js-open-redirect", "time": 0.07124614715576172}, {"fpath": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/TheProduct.js", "fline": 1, "rule_id": "javascript.express.security.require-request.require-request", "time": 0.07227396965026855}, {"fpath": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-framework/src/bin/scripts/build.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.08887290954589844}, {"fpath": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-components/src/js/TheProduct.js", "fline": 1, "rule_id": "javascript.express.security.cors-misconfiguration.cors-misconfiguration", "time": 0.09512996673583984}, {"fpath": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-compre-confie/src/index.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.19343996047973633}, {"fpath": "downloaded_repos/ecomplus_storefront/@ecomplus/widget-ebit/src/index.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.2534599304199219}, {"fpath": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/store/modules/checkout.js", "fline": 288, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.3427729606628418}, {"fpath": "downloaded_repos/ecomplus_storefront/@ecomplus/storefront-app/src/store/modules/checkout.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.751399040222168}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1120551872}, "engine_requested": "OSS", "skipped_rules": []}