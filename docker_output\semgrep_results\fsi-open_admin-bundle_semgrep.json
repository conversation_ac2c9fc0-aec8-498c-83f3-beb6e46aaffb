{"version": "1.130.0", "results": [{"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/fsi-open_admin-bundle/Behat/Context/DataContext.php", "start": {"line": 82, "col": 13, "offset": 2126}, "end": {"line": 82, "col": 32, "offset": 2145}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/fsi-open_admin-bundle/Resources/public/js/collection.js", "start": {"line": 15, "col": 26, "offset": 554}, "end": {"line": 15, "col": 69, "offset": 597}, "extra": {"message": "RegExp() called with a `prototypeName` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/fsi-open_admin-bundle/Resources/public/js/collection.js", "start": {"line": 16, "col": 26, "offset": 631}, "end": {"line": 16, "col": 57, "offset": 662}, "extra": {"message": "RegExp() called with a `prototypeName` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/fsi-open_admin-bundle/Resources/public/js/require.js", "start": {"line": 36, "col": 167, "offset": 15195}, "end": {"line": 36, "col": 174, "offset": 15202}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/fsi-open_admin-bundle/docker-compose.yml", "start": {"line": 5, "col": 5, "offset": 32}, "end": {"line": 5, "col": 8, "offset": 35}, "extra": {"message": "Service 'web' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/fsi-open_admin-bundle/docker-compose.yml", "start": {"line": 5, "col": 5, "offset": 32}, "end": {"line": 5, "col": 8, "offset": 35}, "extra": {"message": "Service 'web' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/fsi-open_admin-bundle/docker-compose.yml", "start": {"line": 23, "col": 5, "offset": 697}, "end": {"line": 23, "col": 13, "offset": 705}, "extra": {"message": "Service 'selenium' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/fsi-open_admin-bundle/docker-compose.yml", "start": {"line": 23, "col": 5, "offset": 697}, "end": {"line": 23, "col": 13, "offset": 705}, "extra": {"message": "Service 'selenium' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/fsi-open_admin-bundle/Resources/translations/FSiAdminBundle.pl.yml:11:\n (approximate error location; error nearby after) error calling parser: did not find expected key character 0 position 0 returned: 0", "path": "downloaded_repos/fsi-open_admin-bundle/Resources/translations/FSiAdminBundle.pl.yml"}], "paths": {"scanned": ["downloaded_repos/fsi-open_admin-bundle/.github/workflows/ci.yml", "downloaded_repos/fsi-open_admin-bundle/.gitignore", "downloaded_repos/fsi-open_admin-bundle/Admin/AbstractElement.php", "downloaded_repos/fsi-open_admin-bundle/Admin/CRUD/BatchElement.php", "downloaded_repos/fsi-open_admin-bundle/Admin/CRUD/CRUDElement.php", "downloaded_repos/fsi-open_admin-bundle/Admin/CRUD/Context/BatchElementContext.php", "downloaded_repos/fsi-open_admin-bundle/Admin/CRUD/Context/CRUDFormElementContext.php", "downloaded_repos/fsi-open_admin-bundle/Admin/CRUD/Context/CRUDListElementContext.php", "downloaded_repos/fsi-open_admin-bundle/Admin/CRUD/Context/DeleteElementContext.php", "downloaded_repos/fsi-open_admin-bundle/Admin/CRUD/Context/FormElementContext.php", "downloaded_repos/fsi-open_admin-bundle/Admin/CRUD/Context/ListElementContext.php", "downloaded_repos/fsi-open_admin-bundle/Admin/CRUD/Context/Request/BatchFormSubmitHandler.php", "downloaded_repos/fsi-open_admin-bundle/Admin/CRUD/Context/Request/BatchFormValidRequestHandler.php", "downloaded_repos/fsi-open_admin-bundle/Admin/CRUD/Context/Request/DataGridBindDataHandler.php", "downloaded_repos/fsi-open_admin-bundle/Admin/CRUD/Context/Request/DataGridSetDataHandler.php", "downloaded_repos/fsi-open_admin-bundle/Admin/CRUD/Context/Request/DataSourceBindParametersHandler.php", "downloaded_repos/fsi-open_admin-bundle/Admin/CRUD/Context/Request/DeleteRequestHandler.php", "downloaded_repos/fsi-open_admin-bundle/Admin/CRUD/Context/Request/FormSubmitHandler.php", "downloaded_repos/fsi-open_admin-bundle/Admin/CRUD/Context/Request/FormValidRequestHandler.php", "downloaded_repos/fsi-open_admin-bundle/Admin/CRUD/DataIndexerElement.php", "downloaded_repos/fsi-open_admin-bundle/Admin/CRUD/DeleteElement.php", "downloaded_repos/fsi-open_admin-bundle/Admin/CRUD/DependentBatchElement.php", "downloaded_repos/fsi-open_admin-bundle/Admin/CRUD/DependentCRUDElement.php", "downloaded_repos/fsi-open_admin-bundle/Admin/CRUD/DependentDeleteElement.php", "downloaded_repos/fsi-open_admin-bundle/Admin/CRUD/DependentFormElement.php", "downloaded_repos/fsi-open_admin-bundle/Admin/CRUD/DependentListElement.php", "downloaded_repos/fsi-open_admin-bundle/Admin/CRUD/FormElement.php", "downloaded_repos/fsi-open_admin-bundle/Admin/CRUD/GenericBatchElement.php", "downloaded_repos/fsi-open_admin-bundle/Admin/CRUD/GenericCRUDElement.php", "downloaded_repos/fsi-open_admin-bundle/Admin/CRUD/GenericDeleteElement.php", "downloaded_repos/fsi-open_admin-bundle/Admin/CRUD/GenericFormElement.php", "downloaded_repos/fsi-open_admin-bundle/Admin/CRUD/GenericListElement.php", "downloaded_repos/fsi-open_admin-bundle/Admin/CRUD/ListElement.php", "downloaded_repos/fsi-open_admin-bundle/Admin/Context/ContextAbstract.php", "downloaded_repos/fsi-open_admin-bundle/Admin/Context/ContextInterface.php", "downloaded_repos/fsi-open_admin-bundle/Admin/Context/ContextManager.php", "downloaded_repos/fsi-open_admin-bundle/Admin/Context/Request/AbstractFormSubmitHandler.php", "downloaded_repos/fsi-open_admin-bundle/Admin/Context/Request/AbstractFormValidRequestHandler.php", "downloaded_repos/fsi-open_admin-bundle/Admin/Context/Request/AbstractHandler.php", "downloaded_repos/fsi-open_admin-bundle/Admin/Context/Request/HandlerInterface.php", "downloaded_repos/fsi-open_admin-bundle/Admin/DependentElement.php", "downloaded_repos/fsi-open_admin-bundle/Admin/DependentElementImpl.php", "downloaded_repos/fsi-open_admin-bundle/Admin/Display/Context/DisplayContext.php", "downloaded_repos/fsi-open_admin-bundle/Admin/Display/Context/Request/Handler.php", "downloaded_repos/fsi-open_admin-bundle/Admin/Display/DependentDisplayElement.php", "downloaded_repos/fsi-open_admin-bundle/Admin/Display/Element.php", "downloaded_repos/fsi-open_admin-bundle/Admin/Display/GenericDisplayElement.php", "downloaded_repos/fsi-open_admin-bundle/Admin/Element.php", "downloaded_repos/fsi-open_admin-bundle/Admin/LocaleProviderAware.php", "downloaded_repos/fsi-open_admin-bundle/Admin/Manager/DependentElementsVisitor.php", "downloaded_repos/fsi-open_admin-bundle/Admin/Manager/ElementCollectionVisitor.php", "downloaded_repos/fsi-open_admin-bundle/Admin/Manager/Visitor.php", "downloaded_repos/fsi-open_admin-bundle/Admin/Manager.php", "downloaded_repos/fsi-open_admin-bundle/Admin/ManagerInterface.php", "downloaded_repos/fsi-open_admin-bundle/Admin/RedirectableElement.php", "downloaded_repos/fsi-open_admin-bundle/Admin/RequestStackAware.php", "downloaded_repos/fsi-open_admin-bundle/Admin/ResourceRepository/Context/Request/FormSubmitHandler.php", "downloaded_repos/fsi-open_admin-bundle/Admin/ResourceRepository/Context/Request/FormValidRequestHandler.php", "downloaded_repos/fsi-open_admin-bundle/Admin/ResourceRepository/Context/ResourceRepositoryContext.php", "downloaded_repos/fsi-open_admin-bundle/Admin/ResourceRepository/Element.php", "downloaded_repos/fsi-open_admin-bundle/Admin/ResourceRepository/GenericResourceElement.php", "downloaded_repos/fsi-open_admin-bundle/Admin/ResourceRepository/ResourceFormBuilder.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Context/AbstractContext.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Context/AdminContext.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Context/DataContext.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Context/DisplayContext.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Context/FiltersContext.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Context/FormContext.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Context/ListContext.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Context/MessageContext.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Context/NavigationContext.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Context/ResourceContext.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Element/Display.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Element/Filters.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Element/ListElement.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Element/ListResultsElement.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Element/Messages.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Element/Pagination.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Page/AdminPanel.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Page/CategoryList.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Page/CategoryNewsCreate.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Page/CategoryNewsEdit.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Page/CategoryNewsList.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Page/CustomNewsCreate.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Page/CustomNewsEdit.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Page/CustomNewsList.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Page/CustomSubscribersList.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Page/DTOForm.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Page/DefaultPage.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Page/HomePageEdit.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Page/NewsCreate.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Page/NewsDisplay.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Page/NewsEdit.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Page/NewsList.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Page/Page.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Page/PersonAddForm.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Page/PersonEditForm.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Page/PersonList.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Page/SubscriberEdit.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Page/SubscriberForm.php", "downloaded_repos/fsi-open_admin-bundle/Behat/Page/SubscribersList.php", "downloaded_repos/fsi-open_admin-bundle/CHANGELOG-1.1.md", "downloaded_repos/fsi-open_admin-bundle/CHANGELOG-2.1.md", "downloaded_repos/fsi-open_admin-bundle/CHANGELOG-3.0.md", "downloaded_repos/fsi-open_admin-bundle/CHANGELOG-3.1.md", "downloaded_repos/fsi-open_admin-bundle/Controller/AdminController.php", "downloaded_repos/fsi-open_admin-bundle/Controller/BatchController.php", "downloaded_repos/fsi-open_admin-bundle/Controller/ControllerAbstract.php", "downloaded_repos/fsi-open_admin-bundle/Controller/DataIndexerElementFinder.php", "downloaded_repos/fsi-open_admin-bundle/Controller/DisplayController.php", "downloaded_repos/fsi-open_admin-bundle/Controller/FormController.php", "downloaded_repos/fsi-open_admin-bundle/Controller/ListController.php", "downloaded_repos/fsi-open_admin-bundle/Controller/PositionableController.php", "downloaded_repos/fsi-open_admin-bundle/Controller/ReorderTreeController.php", "downloaded_repos/fsi-open_admin-bundle/Controller/ResourceController.php", "downloaded_repos/fsi-open_admin-bundle/DataGrid/Extension/Admin/ColumnTypeExtension/BatchActionExtension.php", "downloaded_repos/fsi-open_admin-bundle/DataGrid/Extension/Admin/ColumnTypeExtension/ElementActionExtension.php", "downloaded_repos/fsi-open_admin-bundle/DependencyInjection/Compiler/FlashMessagesPass.php", "downloaded_repos/fsi-open_admin-bundle/DependencyInjection/Compiler/ResourceRepositoryPass.php", "downloaded_repos/fsi-open_admin-bundle/DependencyInjection/Compiler/TwigGlobalsPass.php", "downloaded_repos/fsi-open_admin-bundle/DependencyInjection/Configuration.php", "downloaded_repos/fsi-open_admin-bundle/DependencyInjection/FSIAdminExtension.php", "downloaded_repos/fsi-open_admin-bundle/Display/Display.php", "downloaded_repos/fsi-open_admin-bundle/Display/Property/Formatter/Boolean.php", "downloaded_repos/fsi-open_admin-bundle/Display/Property/Formatter/Callback.php", "downloaded_repos/fsi-open_admin-bundle/Display/Property/Formatter/Collection.php", "downloaded_repos/fsi-open_admin-bundle/Display/Property/Formatter/DateTime.php", "downloaded_repos/fsi-open_admin-bundle/Display/Property/Formatter/EmptyValue.php", "downloaded_repos/fsi-open_admin-bundle/Display/Property/ValueFormatter.php", "downloaded_repos/fsi-open_admin-bundle/Display/Property.php", "downloaded_repos/fsi-open_admin-bundle/Display/PropertyAccessDisplay.php", "downloaded_repos/fsi-open_admin-bundle/Display/SimpleDisplay.php", "downloaded_repos/fsi-open_admin-bundle/Doctrine/Admin/BatchElement.php", "downloaded_repos/fsi-open_admin-bundle/Doctrine/Admin/CRUDElement.php", "downloaded_repos/fsi-open_admin-bundle/Doctrine/Admin/DataIndexerElementImpl.php", "downloaded_repos/fsi-open_admin-bundle/Doctrine/Admin/DeleteElement.php", "downloaded_repos/fsi-open_admin-bundle/Doctrine/Admin/DependentBatchElement.php", "downloaded_repos/fsi-open_admin-bundle/Doctrine/Admin/DependentCRUDElement.php", "downloaded_repos/fsi-open_admin-bundle/Doctrine/Admin/DependentDeleteElement.php", "downloaded_repos/fsi-open_admin-bundle/Doctrine/Admin/DependentDisplayElement.php", "downloaded_repos/fsi-open_admin-bundle/Doctrine/Admin/DependentFormElement.php", "downloaded_repos/fsi-open_admin-bundle/Doctrine/Admin/DependentListElement.php", "downloaded_repos/fsi-open_admin-bundle/Doctrine/Admin/DisplayElement.php", "downloaded_repos/fsi-open_admin-bundle/Doctrine/Admin/Element.php", "downloaded_repos/fsi-open_admin-bundle/Doctrine/Admin/ElementImpl.php", "downloaded_repos/fsi-open_admin-bundle/Doctrine/Admin/FormElement.php", "downloaded_repos/fsi-open_admin-bundle/Doctrine/Admin/ListElement.php", "downloaded_repos/fsi-open_admin-bundle/Doctrine/Admin/ResourceElement.php", "downloaded_repos/fsi-open_admin-bundle/Event/AdminContextPreCreateEvent.php", "downloaded_repos/fsi-open_admin-bundle/Event/AdminEvent.php", "downloaded_repos/fsi-open_admin-bundle/Event/BatchEvent.php", "downloaded_repos/fsi-open_admin-bundle/Event/BatchObjectPostApplyEvent.php", "downloaded_repos/fsi-open_admin-bundle/Event/BatchObjectPreApplyEvent.php", "downloaded_repos/fsi-open_admin-bundle/Event/BatchObjectsPostApplyEvent.php", "downloaded_repos/fsi-open_admin-bundle/Event/BatchObjectsPreApplyEvent.php", "downloaded_repos/fsi-open_admin-bundle/Event/BatchRequestPostSubmitEvent.php", "downloaded_repos/fsi-open_admin-bundle/Event/BatchRequestPreSubmitEvent.php", "downloaded_repos/fsi-open_admin-bundle/Event/DisplayEvent.php", "downloaded_repos/fsi-open_admin-bundle/Event/DisplayResponsePreRenderEvent.php", "downloaded_repos/fsi-open_admin-bundle/Event/FormDataPostSaveEvent.php", "downloaded_repos/fsi-open_admin-bundle/Event/FormDataPreSaveEvent.php", "downloaded_repos/fsi-open_admin-bundle/Event/FormEvent.php", "downloaded_repos/fsi-open_admin-bundle/Event/FormRequestPostSubmitEvent.php", "downloaded_repos/fsi-open_admin-bundle/Event/FormRequestPreSubmitEvent.php", "downloaded_repos/fsi-open_admin-bundle/Event/FormResponsePreRenderEvent.php", "downloaded_repos/fsi-open_admin-bundle/Event/ListDataGridPostSetDataEvent.php", "downloaded_repos/fsi-open_admin-bundle/Event/ListDataGridPostSubmitRequestEvent.php", "downloaded_repos/fsi-open_admin-bundle/Event/ListDataGridPreSetDataEvent.php", "downloaded_repos/fsi-open_admin-bundle/Event/ListDataGridPreSubmitRequestEvent.php", "downloaded_repos/fsi-open_admin-bundle/Event/ListDataSourcePostBindEvent.php", "downloaded_repos/fsi-open_admin-bundle/Event/ListDataSourcePreBindEvent.php", "downloaded_repos/fsi-open_admin-bundle/Event/ListEvent.php", "downloaded_repos/fsi-open_admin-bundle/Event/ListResponsePreRenderEvent.php", "downloaded_repos/fsi-open_admin-bundle/Event/MenuEvent.php", "downloaded_repos/fsi-open_admin-bundle/Event/MenuMainEvent.php", "downloaded_repos/fsi-open_admin-bundle/Event/MenuToolsEvent.php", "downloaded_repos/fsi-open_admin-bundle/Event/MovedDownTreeEvent.php", "downloaded_repos/fsi-open_admin-bundle/Event/MovedUpTreeEvent.php", "downloaded_repos/fsi-open_admin-bundle/Event/PositionableEvent.php", "downloaded_repos/fsi-open_admin-bundle/Event/PositionablePostMoveEvent.php", "downloaded_repos/fsi-open_admin-bundle/Event/PositionablePreMoveEvent.php", "downloaded_repos/fsi-open_admin-bundle/EventSubscriber/CleanUpMenuSubscriber.php", "downloaded_repos/fsi-open_admin-bundle/EventSubscriber/LocaleMenuSubscriber.php", "downloaded_repos/fsi-open_admin-bundle/EventSubscriber/LocaleSubscriber.php", "downloaded_repos/fsi-open_admin-bundle/EventSubscriber/MainMenuSubscriber.php", "downloaded_repos/fsi-open_admin-bundle/EventSubscriber/MessagesSubscriber.php", "downloaded_repos/fsi-open_admin-bundle/EventSubscriber/TranslationLocaleMenuSubscriber.php", "downloaded_repos/fsi-open_admin-bundle/EventSubscriber/TranslationLocaleSubscriber.php", "downloaded_repos/fsi-open_admin-bundle/Exception/ContextBuilderException.php", "downloaded_repos/fsi-open_admin-bundle/Exception/ContextException.php", "downloaded_repos/fsi-open_admin-bundle/Exception/ContextExceptionInterface.php", "downloaded_repos/fsi-open_admin-bundle/Exception/ExceptionInterface.php", "downloaded_repos/fsi-open_admin-bundle/Exception/InvalidArgumentException.php", "downloaded_repos/fsi-open_admin-bundle/Exception/MissingOptionException.php", "downloaded_repos/fsi-open_admin-bundle/Exception/RequestHandlerException.php", "downloaded_repos/fsi-open_admin-bundle/Exception/RuntimeException.php", "downloaded_repos/fsi-open_admin-bundle/FSiAdminBundle.php", "downloaded_repos/fsi-open_admin-bundle/Factory/ProductionLine.php", "downloaded_repos/fsi-open_admin-bundle/Factory/Worker/DoctrineWorker.php", "downloaded_repos/fsi-open_admin-bundle/Factory/Worker/FormWorker.php", "downloaded_repos/fsi-open_admin-bundle/Factory/Worker/ListWorker.php", "downloaded_repos/fsi-open_admin-bundle/Factory/Worker/LocaleProviderWorker.php", "downloaded_repos/fsi-open_admin-bundle/Factory/Worker/RequestStackWorker.php", "downloaded_repos/fsi-open_admin-bundle/Factory/Worker.php", "downloaded_repos/fsi-open_admin-bundle/Form/CollectionTypeExtension.php", "downloaded_repos/fsi-open_admin-bundle/LICENSE.md", "downloaded_repos/fsi-open_admin-bundle/Menu/Builder/Builder.php", "downloaded_repos/fsi-open_admin-bundle/Menu/Builder/Exception/InvalidYamlStructureException.php", "downloaded_repos/fsi-open_admin-bundle/Menu/Builder/MenuBuilder.php", "downloaded_repos/fsi-open_admin-bundle/Menu/Item/ElementItem.php", "downloaded_repos/fsi-open_admin-bundle/Menu/Item/Item.php", "downloaded_repos/fsi-open_admin-bundle/Menu/Item/RoutableItem.php", "downloaded_repos/fsi-open_admin-bundle/Menu/KnpMenu/ElementVoter.php", "downloaded_repos/fsi-open_admin-bundle/Menu/KnpMenu/ItemAttributesDecorator.php", "downloaded_repos/fsi-open_admin-bundle/Menu/KnpMenu/ItemDecorator.php", "downloaded_repos/fsi-open_admin-bundle/Menu/KnpMenu/ItemElementsDecorator.php", "downloaded_repos/fsi-open_admin-bundle/Menu/KnpMenu/ItemLabelDecorator.php", "downloaded_repos/fsi-open_admin-bundle/Menu/KnpMenu/ItemRouteDecorator.php", "downloaded_repos/fsi-open_admin-bundle/Menu/KnpMenu/MenuBuilder.php", "downloaded_repos/fsi-open_admin-bundle/Message/FlashMessages.php", "downloaded_repos/fsi-open_admin-bundle/Message/RequestStackFlashMessages.php", "downloaded_repos/fsi-open_admin-bundle/Message/SessionFlashMessages.php", "downloaded_repos/fsi-open_admin-bundle/Model/PositionableInterface.php", "downloaded_repos/fsi-open_admin-bundle/README.md", "downloaded_repos/fsi-open_admin-bundle/Request/Parameters.php", "downloaded_repos/fsi-open_admin-bundle/ResourceRepository/Form/ResourceTypeExtension.php", "downloaded_repos/fsi-open_admin-bundle/ResourceRepository/TranslatableMapBuilder.php", "downloaded_repos/fsi-open_admin-bundle/Resources/config/resource_repository_translatable.xml", "downloaded_repos/fsi-open_admin-bundle/Resources/config/routing/admin.yaml", "downloaded_repos/fsi-open_admin-bundle/Resources/config/services.xml", "downloaded_repos/fsi-open_admin-bundle/Resources/doc/admin_dependent_elements.md", "downloaded_repos/fsi-open_admin-bundle/Resources/doc/admin_element.md", "downloaded_repos/fsi-open_admin-bundle/Resources/doc/admin_element_batch.md", "downloaded_repos/fsi-open_admin-bundle/Resources/doc/admin_element_crud.md", "downloaded_repos/fsi-open_admin-bundle/Resources/doc/admin_element_display.md", "downloaded_repos/fsi-open_admin-bundle/Resources/doc/admin_element_form.md", "downloaded_repos/fsi-open_admin-bundle/Resources/doc/admin_element_list.md", "downloaded_repos/fsi-open_admin-bundle/Resources/doc/admin_element_resource.md", "downloaded_repos/fsi-open_admin-bundle/Resources/doc/admin_panel_translation.md", "downloaded_repos/fsi-open_admin-bundle/Resources/doc/admin_positionable.md", "downloaded_repos/fsi-open_admin-bundle/Resources/doc/admin_tree.md", "downloaded_repos/fsi-open_admin-bundle/Resources/doc/configuration.md", "downloaded_repos/fsi-open_admin-bundle/Resources/doc/contexts.md", "downloaded_repos/fsi-open_admin-bundle/Resources/doc/embedding_element.md", "downloaded_repos/fsi-open_admin-bundle/Resources/doc/events.md", "downloaded_repos/fsi-open_admin-bundle/Resources/doc/home_page.md", "downloaded_repos/fsi-open_admin-bundle/Resources/doc/how_to_create_edit_link_at_list.md", "downloaded_repos/fsi-open_admin-bundle/Resources/doc/index.md", "downloaded_repos/fsi-open_admin-bundle/Resources/doc/installation.md", "downloaded_repos/fsi-open_admin-bundle/Resources/doc/menu.md", "downloaded_repos/fsi-open_admin-bundle/Resources/public/css/bootstrap.min.css", "downloaded_repos/fsi-open_admin-bundle/Resources/public/fonts/glyphicons-halflings-regular.eot", "downloaded_repos/fsi-open_admin-bundle/Resources/public/fonts/glyphicons-halflings-regular.svg", "downloaded_repos/fsi-open_admin-bundle/Resources/public/fonts/glyphicons-halflings-regular.ttf", "downloaded_repos/fsi-open_admin-bundle/Resources/public/fonts/glyphicons-halflings-regular.woff", "downloaded_repos/fsi-open_admin-bundle/Resources/public/fonts/glyphicons-halflings-regular.woff2", "downloaded_repos/fsi-open_admin-bundle/Resources/public/js/collection.js", "downloaded_repos/fsi-open_admin-bundle/Resources/public/js/require.js", "downloaded_repos/fsi-open_admin-bundle/Resources/translations/FSiAdminBundle.en.yml", "downloaded_repos/fsi-open_admin-bundle/Resources/translations/FSiAdminBundle.pl.yml", "downloaded_repos/fsi-open_admin-bundle/Resources/views/Admin/index.html.twig", "downloaded_repos/fsi-open_admin-bundle/Resources/views/Admin/main_menu_theme.html.twig", "downloaded_repos/fsi-open_admin-bundle/Resources/views/Admin/tools_menu_theme.html.twig", "downloaded_repos/fsi-open_admin-bundle/Resources/views/CRUD/datagrid.html.twig", "downloaded_repos/fsi-open_admin-bundle/Resources/views/CRUD/datagrid_edit_form.html.twig", "downloaded_repos/fsi-open_admin-bundle/Resources/views/CRUD/datasource.html.twig", "downloaded_repos/fsi-open_admin-bundle/Resources/views/CRUD/list.html.twig", "downloaded_repos/fsi-open_admin-bundle/Resources/views/Display/display.html.twig", "downloaded_repos/fsi-open_admin-bundle/Resources/views/Form/form.html.twig", "downloaded_repos/fsi-open_admin-bundle/Resources/views/Form/form_theme.html.twig", "downloaded_repos/fsi-open_admin-bundle/Resources/views/List/list.html.twig", "downloaded_repos/fsi-open_admin-bundle/Resources/views/Resource/resource.html.twig", "downloaded_repos/fsi-open_admin-bundle/Resources/views/base.html.twig", "downloaded_repos/fsi-open_admin-bundle/Resources/views/flash_messages.html.twig", "downloaded_repos/fsi-open_admin-bundle/Translatable/DataGrid/Extension/DefaultLocaleExtension.php", "downloaded_repos/fsi-open_admin-bundle/Translatable/EventSubscriber/RequestContextLocaleListener.php", "downloaded_repos/fsi-open_admin-bundle/Translatable/Form/DefaultTranslation.php", "downloaded_repos/fsi-open_admin-bundle/Translatable/Form/Extension/TranslatableExtension.php", "downloaded_repos/fsi-open_admin-bundle/Translatable/Form/Extractor/FormDataClassExtractor.php", "downloaded_repos/fsi-open_admin-bundle/Translatable/Form/Extractor/FormDataExtractor.php", "downloaded_repos/fsi-open_admin-bundle/Translatable/Form/FormTranslatableData.php", "downloaded_repos/fsi-open_admin-bundle/Translatable/Form/PropertyTranslatableData.php", "downloaded_repos/fsi-open_admin-bundle/Translatable/Form/TranslatableFormDataExtractor.php", "downloaded_repos/fsi-open_admin-bundle/Translatable/Form/TranslatablePropertyBuilder.php", "downloaded_repos/fsi-open_admin-bundle/Translatable/Menu/Voter/TranslatableElementVoter.php", "downloaded_repos/fsi-open_admin-bundle/Twig/MessageTwigExtension.php", "downloaded_repos/fsi-open_admin-bundle/Twig/TranslatableExtension.php", "downloaded_repos/fsi-open_admin-bundle/UPGRADE-1.1.md", "downloaded_repos/fsi-open_admin-bundle/UPGRADE-2.1.md", "downloaded_repos/fsi-open_admin-bundle/UPGRADE-3.0.md", "downloaded_repos/fsi-open_admin-bundle/behat.yml", "downloaded_repos/fsi-open_admin-bundle/composer.json", "downloaded_repos/fsi-open_admin-bundle/docker-compose.yml", "downloaded_repos/fsi-open_admin-bundle/features/admin_panel_main_page.feature", "downloaded_repos/fsi-open_admin-bundle/features/change_admin_panel_language.feature", "downloaded_repos/fsi-open_admin-bundle/features/crud/crud_list.feature", "downloaded_repos/fsi-open_admin-bundle/features/crud/crud_list_create.feature", "downloaded_repos/fsi-open_admin-bundle/features/crud/crud_list_custom_templates.feature", "downloaded_repos/fsi-open_admin-bundle/features/crud/crud_list_delete.feature", "downloaded_repos/fsi-open_admin-bundle/features/crud/crud_list_edit.feature", "downloaded_repos/fsi-open_admin-bundle/features/crud/crud_list_edit_at_list.feature", "downloaded_repos/fsi-open_admin-bundle/features/crud/crud_list_filtering.feature", "downloaded_repos/fsi-open_admin-bundle/features/crud/crud_list_pagination.feature", "downloaded_repos/fsi-open_admin-bundle/features/crud/crud_list_sorting.feature", "downloaded_repos/fsi-open_admin-bundle/features/crud/crud_results_per_page.feature", "downloaded_repos/fsi-open_admin-bundle/features/delete/list_delete.feature", "downloaded_repos/fsi-open_admin-bundle/features/dependent_crud/dependent_crud_list.feature", "downloaded_repos/fsi-open_admin-bundle/features/dependent_crud/dependent_crud_list_create.feature", "downloaded_repos/fsi-open_admin-bundle/features/dependent_crud/dependent_crud_list_delete.feature", "downloaded_repos/fsi-open_admin-bundle/features/dependent_crud/dependent_crud_list_edit.feature", "downloaded_repos/fsi-open_admin-bundle/features/dependent_crud/dependent_crud_list_edit_at_list.feature", "downloaded_repos/fsi-open_admin-bundle/features/dependent_crud/dependent_crud_list_filtering.feature", "downloaded_repos/fsi-open_admin-bundle/features/dependent_crud/dependent_crud_list_pagination.feature", "downloaded_repos/fsi-open_admin-bundle/features/dependent_crud/dependent_crud_list_sorting.feature", "downloaded_repos/fsi-open_admin-bundle/features/dependent_crud/dependent_crud_results_per_page.feature", "downloaded_repos/fsi-open_admin-bundle/features/display.feature", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/.gitignore", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/bin/console", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/config/admin_menu.yaml", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/config/config.yaml", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/config/doctrine/Category.orm.xml", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/config/doctrine/News.orm.xml", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/config/doctrine/Node.orm.xml", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/config/doctrine/Person.orm.xml", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/config/doctrine/Resource.orm.xml", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/config/doctrine/Subscriber.orm.xml", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/config/doctrine/Tag.orm.xml", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/config/doctrine/TagElement.orm.xml", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/config/framework_44.yaml", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/config/framework_54.yaml", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/config/resource_map.yaml", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/config/routing.yaml", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/AppKernel.php", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/Admin/AboutUsPage.php", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/Admin/Category.php", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/Admin/CategoryNews.php", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/Admin/CategoryNewsDisplay.php", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/Admin/CustomNews.php", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/Admin/CustomSubscriber.php", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/Admin/DTOFormElement.php", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/Admin/DisplayNews.php", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/Admin/News.php", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/Admin/Node.php", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/Admin/Person.php", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/Admin/Structure/HomePage.php", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/Admin/Subscriber.php", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/Admin/SubscriberDelete.php", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/Admin/SubscriberForm.php", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/CustomAdmin/Contact.php", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/DTO/Model.php", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/DataGrid/NewsDataGridBuilder.php", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/DataSource/NewsDataSourceBuilder.php", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/DependencyInjection/FSiFixturesExtension.php", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/Entity/Category.php", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/Entity/News.php", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/Entity/Node.php", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/Entity/Person.php", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/Entity/Resource.php", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/Entity/Subscriber.php", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/Entity/Tag.php", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/Entity/TagElement.php", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/FSiFixturesBundle.php", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/Form/NewsType.php", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/Form/TagElementType.php", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/Form/TagType.php", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/Resources/config/services.xml", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/Resources/views/Admin/custom_form.html.twig", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/Resources/views/Admin/custom_list.html.twig", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/Resources/views/Admin/subscriber_custom_list.html.twig", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/src/FixturesBundle/Resources/views/Admin/subscriber_list.html.twig", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/translations/messages.en.yml", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/web/.htaccess", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/web/app_test.php", "downloaded_repos/fsi-open_admin-bundle/features/fixtures/project/web/files/.gitkeep", "downloaded_repos/fsi-open_admin-bundle/features/form/collections.feature", "downloaded_repos/fsi-open_admin-bundle/features/form/form.feature", "downloaded_repos/fsi-open_admin-bundle/features/form/nested_collections.feature", "downloaded_repos/fsi-open_admin-bundle/features/list/list.feature", "downloaded_repos/fsi-open_admin-bundle/features/list/list_custom_templates.feature", "downloaded_repos/fsi-open_admin-bundle/features/list/list_filtering.feature", "downloaded_repos/fsi-open_admin-bundle/features/list/list_pagination.feature", "downloaded_repos/fsi-open_admin-bundle/features/list/list_sorting.feature", "downloaded_repos/fsi-open_admin-bundle/features/list/results_per_page.feature", "downloaded_repos/fsi-open_admin-bundle/features/resource.feature", "downloaded_repos/fsi-open_admin-bundle/phpcs.xml", "downloaded_repos/fsi-open_admin-bundle/phpstan.neon", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Admin/CRUD/Context/BatchElementContextSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Admin/CRUD/Context/DeleteElementContextSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Admin/CRUD/Context/FormElementContextSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Admin/CRUD/Context/ListElementContextSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Admin/CRUD/Context/Request/BatchFormSubmitHandlerSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Admin/CRUD/Context/Request/BatchFormValidRequestHandlerSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Admin/CRUD/Context/Request/DataGridBindDataHandlerSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Admin/CRUD/Context/Request/DataGridSetDataHandlerSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Admin/CRUD/Context/Request/DataSourceBindParametersHandlerSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Admin/CRUD/Context/Request/DeleteRequestHandlerSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Admin/CRUD/Context/Request/FormSubmitHandlerSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Admin/CRUD/Context/Request/FormValidRequestHandlerSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Admin/CRUD/DependentBatchElementSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Admin/CRUD/DependentCRUDElementSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Admin/CRUD/DependentFormElementSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Admin/CRUD/DependentListElementSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Admin/CRUD/GenericBatchElementSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Admin/CRUD/GenericCRUDElementSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Admin/CRUD/GenericFormElementSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Admin/CRUD/GenericListElementSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Admin/Context/ContextManagerSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Admin/Display/Context/DisplayContextSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Admin/Manager/ElementCollectionVisitorSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Admin/ManagerSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Admin/ResourceRepository/Context/Request/FormSubmitHandlerSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Admin/ResourceRepository/Context/Request/FormValidRequestHandlerSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Admin/ResourceRepository/Context/ResourceRepositoryContextSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Admin/ResourceRepository/ResourceFormBuilderSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Controller/AdminControllerSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Controller/BatchControllerSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Controller/DisplayControllerSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Controller/FormControllerSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Controller/ListControllerSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Controller/PositionableControllerSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Controller/ReorderTreeControllerSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Controller/ResourceControllerSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/DataGrid/Extension/Admin/ColumnTypeExtension/BatchActionExtensionSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/DataGrid/Extension/Admin/ColumnTypeExtension/ElementActionExtensionSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/DependencyInjection/Compiler/ResourceRepositoryPassSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/DependencyInjection/Compiler/TwigGlobalsPassSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Display/Property/Formatter/BooleanSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Display/Property/Formatter/CallbackSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Display/Property/Formatter/CollectionSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Display/Property/Formatter/DateTimeSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Display/Property/Formatter/EmptyValueSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Display/PropertyAccessDisplaySpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Display/SimpleDisplaySpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Doctrine/Admin/BatchElementSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Doctrine/Admin/CRUDElementSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Doctrine/Admin/DeleteElementSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Doctrine/Admin/DependentBatchElementSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Doctrine/Admin/DependentCRUDElementSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Doctrine/Admin/DependentDeleteElementSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Doctrine/Admin/DependentFormElementSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Doctrine/Admin/DependentListElementSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Doctrine/Admin/FormElementSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Doctrine/Admin/ListElementSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Doctrine/Admin/ResourceElementSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/EventSubscriber/CleanUpMenuSubscriberSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/EventSubscriber/LocaleMenuSubscriberSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/EventSubscriber/LocaleSubscriberSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/EventSubscriber/MainMenuSubscriberSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/EventSubscriber/MessagesSubscriberSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/EventSubscriber/admin_menu.yml", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/EventSubscriber/invalid_admin_menu.yml", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/FSiAdminBundleSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Factory/ProductionLineSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Factory/Worker/DoctrineWorkerSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Factory/Worker/FormWorkerSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Factory/Worker/ListWorkerSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Factory/Worker/LocaleProviderWorkerSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Factory/Worker/RequestStackWorkerSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Menu/Builder/MenuBuilderSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Menu/Item/ElementItemSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Menu/Item/ItemSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Menu/KnpMenu/ElementVoterSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/FSi/Bundle/AdminBundle/Menu/KnpMenu/MenuBuilderSpec.php", "downloaded_repos/fsi-open_admin-bundle/spec/fixtures/Admin/CRUDElement.php", "downloaded_repos/fsi-open_admin-bundle/spec/fixtures/Admin/CustomClass.php", "downloaded_repos/fsi-open_admin-bundle/spec/fixtures/Admin/DoctrineElement.php", "downloaded_repos/fsi-open_admin-bundle/spec/fixtures/Admin/RequestStackAwareElement.php", "downloaded_repos/fsi-open_admin-bundle/spec/fixtures/Admin/SimpleAdminElement.php", "downloaded_repos/fsi-open_admin-bundle/spec/fixtures/CustomAdmin/SimpleAdminElement.php", "downloaded_repos/fsi-open_admin-bundle/spec/fixtures/Doctrine/MyBatchElement.php", "downloaded_repos/fsi-open_admin-bundle/spec/fixtures/Doctrine/MyCrudElement.php", "downloaded_repos/fsi-open_admin-bundle/spec/fixtures/Doctrine/MyDeleteElement.php", "downloaded_repos/fsi-open_admin-bundle/spec/fixtures/Doctrine/MyDependentBatchElement.php", "downloaded_repos/fsi-open_admin-bundle/spec/fixtures/Doctrine/MyDependentCrudElement.php", "downloaded_repos/fsi-open_admin-bundle/spec/fixtures/Doctrine/MyDependentDeleteElement.php", "downloaded_repos/fsi-open_admin-bundle/spec/fixtures/Doctrine/MyDependentFormElement.php", "downloaded_repos/fsi-open_admin-bundle/spec/fixtures/Doctrine/MyDependentListElement.php", "downloaded_repos/fsi-open_admin-bundle/spec/fixtures/Doctrine/MyFormElement.php", "downloaded_repos/fsi-open_admin-bundle/spec/fixtures/Doctrine/MyListElement.php", "downloaded_repos/fsi-open_admin-bundle/spec/fixtures/Entity/Resource.php", "downloaded_repos/fsi-open_admin-bundle/spec/fixtures/MyBatch.php", "downloaded_repos/fsi-open_admin-bundle/spec/fixtures/MyBundle.php", "downloaded_repos/fsi-open_admin-bundle/spec/fixtures/MyCRUD.php", "downloaded_repos/fsi-open_admin-bundle/spec/fixtures/MyDependentBatch.php", "downloaded_repos/fsi-open_admin-bundle/spec/fixtures/MyDependentCRUD.php", "downloaded_repos/fsi-open_admin-bundle/spec/fixtures/MyDependentForm.php", "downloaded_repos/fsi-open_admin-bundle/spec/fixtures/MyDependentList.php", "downloaded_repos/fsi-open_admin-bundle/spec/fixtures/MyForm.php", "downloaded_repos/fsi-open_admin-bundle/spec/fixtures/MyList.php", "downloaded_repos/fsi-open_admin-bundle/spec/fixtures/MyResource.php", "downloaded_repos/fsi-open_admin-bundle/spec/fixtures/MyResourceElement.php"], "skipped": [{"path": "downloaded_repos/fsi-open_admin-bundle/Resources/public/js/bootstrap.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fsi-open_admin-bundle/Resources/public/js/jquery-1.11.2.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fsi-open_admin-bundle/Resources/translations/FSiAdminBundle.pl.yml", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.5983250141143799, "profiling_times": {"config_time": 5.923964977264404, "core_time": 3.9825706481933594, "ignores_time": 0.0017728805541992188, "total_time": 9.909108638763428}, "parsing_time": {"total_time": 1.2618038654327393, "per_file_time": {"mean": 0.003260475104477362, "std_dev": 0.0001445935352546363}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 4.0853540897369385, "per_file_time": {"mean": 0.0029820102844795183, "std_dev": 0.00028156315582305067}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.7061049938201904, "per_file_and_rule_time": {"mean": 0.0007750878088037218, "std_dev": 1.179537987172905e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.14447855949401855, "per_def_and_rule_time": {"mean": 0.0010545880255037848, "std_dev": 1.1187961859246483e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1086467328}, "engine_requested": "OSS", "skipped_rules": []}