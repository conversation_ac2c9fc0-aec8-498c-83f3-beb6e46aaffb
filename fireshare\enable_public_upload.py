#!/usr/bin/env python3
"""
🔧 Enable Public Upload for Command Injection Testing
===================================================

This script enables public uploads in Fireshare so you can test the command injection
vulnerability without needing authentication. It demonstrates multiple attack vectors:

1. Enable public uploads via admin API
2. Test authenticated upload endpoint  
3. Test both vulnerable endpoints

⚠️  ETHICAL USE ONLY: Use this script only on systems you own or have explicit permission to test!
"""

import requests
import json
import sys
import time

class PublicUploadEnabler:
    def __init__(self, target_url, username="admin", password="admin"):
        self.target_url = target_url.rstrip('/')
        self.session = requests.Session()
        self.username = username
        self.password = password
        
        print("🔧 Public Upload Enabler for Command Injection Testing")
        print("=" * 55)
        print(f"Target: {self.target_url}")
        print(f"Credentials: {self.username} / {self.password}")
        print()

    def authenticate(self):
        """Authenticate with admin credentials"""
        print("🔐 Authenticating with admin credentials...")
        
        try:
            response = self.session.post(
                f"{self.target_url}/api/login",
                json={"username": self.username, "password": self.password},
                timeout=10
            )
            
            if response.status_code == 200:
                print("✅ Authentication successful!")
                return True
            else:
                print(f"❌ Authentication failed: {response.status_code}")
                print(f"Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Authentication error: {e}")
            return False

    def get_current_config(self):
        """Get the current configuration"""
        print("📋 Getting current configuration...")
        
        try:
            response = self.session.get(
                f"{self.target_url}/api/admin/config",
                timeout=10
            )
            
            if response.status_code == 200:
                config = response.json()
                print("✅ Configuration retrieved successfully!")
                
                # Show current upload settings
                allow_public = config.get('app_config', {}).get('allow_public_upload', False)
                show_public = config.get('ui_config', {}).get('show_public_upload', False)
                
                print(f"   📤 Public upload allowed: {allow_public}")
                print(f"   👁️  Show public upload UI: {show_public}")
                
                return config
            else:
                print(f"❌ Failed to get config: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Error getting config: {e}")
            return None

    def enable_public_upload(self):
        """Enable public uploads via admin API"""
        print("\n🚀 Enabling public uploads...")
        
        # Get current config first
        config = self.get_current_config()
        if not config:
            return False
        
        # Modify config to enable public uploads
        config['app_config']['allow_public_upload'] = True
        config['ui_config']['show_public_upload'] = True
        
        try:
            response = self.session.put(
                f"{self.target_url}/api/admin/config",
                json={"config": config},
                timeout=10
            )
            
            if response.status_code == 200:
                print("✅ Public uploads enabled successfully!")
                print("   📤 Public upload endpoint is now active")
                print("   🌐 URL: /api/upload/public")
                return True
            else:
                print(f"❌ Failed to enable public uploads: {response.status_code}")
                print(f"Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Error enabling public uploads: {e}")
            return False

    def test_upload_endpoints(self):
        """Test both upload endpoints to confirm they work"""
        print("\n🧪 Testing upload endpoints...")
        
        # Create a test file
        test_content = b"fake video content for testing"
        test_filename = "test.mp4"
        
        # Test 1: Public upload (should work now)
        print("\n1️⃣ Testing public upload endpoint...")
        files = {'file': (test_filename, test_content, 'video/mp4')}
        
        try:
            response = self.session.post(
                f"{self.target_url}/api/upload/public",
                files=files,
                timeout=30
            )
            
            if response.status_code == 201:
                print("✅ Public upload endpoint working!")
            elif response.status_code == 401:
                print("❌ Public upload still disabled")
            else:
                print(f"❓ Unexpected response: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error testing public upload: {e}")
        
        # Test 2: Authenticated upload
        print("\n2️⃣ Testing authenticated upload endpoint...")
        files = {'file': (test_filename, test_content, 'video/mp4')}
        
        try:
            response = self.session.post(
                f"{self.target_url}/api/upload",
                files=files,
                timeout=30
            )
            
            if response.status_code == 201:
                print("✅ Authenticated upload endpoint working!")
            else:
                print(f"❓ Unexpected response: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error testing authenticated upload: {e}")

    def show_attack_options(self):
        """Show available attack options"""
        print("\n🎯 AVAILABLE ATTACK VECTORS")
        print("-" * 30)
        print()
        print("Now you can test command injection using:")
        print()
        print("1️⃣ PUBLIC UPLOAD (No authentication required)")
        print("   Endpoint: /api/upload/public")
        print("   Vulnerable code: Line 269 in api.py")
        print("   Command: python3 command_injection_demo.py http://localhost:8080")
        print()
        print("2️⃣ AUTHENTICATED UPLOAD (Requires login)")
        print("   Endpoint: /api/upload")
        print("   Vulnerable code: Line 303 in api.py")
        print("   Command: python3 command_injection_demo.py http://localhost:8080 -u admin -p admin")
        print()
        print("🚨 BOTH ENDPOINTS ARE VULNERABLE!")
        print("   The same Popen() vulnerability exists in both upload functions")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(
        description="Enable public uploads for command injection testing",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 enable_public_upload.py http://localhost:8080
  python3 enable_public_upload.py http://localhost:8080 -u admin -p password123
        """
    )
    
    parser.add_argument("url", help="Target Fireshare URL (e.g., http://localhost:8080)")
    parser.add_argument("-u", "--username", default="admin", help="Admin username (default: admin)")
    parser.add_argument("-p", "--password", default="admin", help="Admin password (default: admin)")
    parser.add_argument("--test-only", action="store_true", help="Only test endpoints, don't modify config")
    
    args = parser.parse_args()
    
    # Validate URL
    if not args.url.startswith(('http://', 'https://')):
        print("❌ Error: URL must start with http:// or https://")
        sys.exit(1)
    
    enabler = PublicUploadEnabler(args.url, args.username, args.password)
    
    # Authenticate
    if not enabler.authenticate():
        print("\n❌ Cannot proceed without authentication")
        print("💡 Make sure Fireshare is running and credentials are correct")
        print("🔗 Default credentials are usually: admin / admin")
        sys.exit(1)
    
    if args.test_only:
        enabler.test_upload_endpoints()
        return
    
    # Get current config
    current_config = enabler.get_current_config()
    if not current_config:
        print("❌ Cannot get current configuration")
        sys.exit(1)
    
    # Check if public upload is already enabled
    if current_config.get('app_config', {}).get('allow_public_upload', False):
        print("\n✅ Public uploads are already enabled!")
    else:
        # Enable public uploads
        if not enabler.enable_public_upload():
            print("❌ Failed to enable public uploads")
            sys.exit(1)
    
    # Test endpoints
    enabler.test_upload_endpoints()
    
    # Show attack options
    enabler.show_attack_options()
    
    print("\n🎉 SETUP COMPLETE!")
    print("=" * 20)
    print("✅ Both upload endpoints are now available for testing")
    print("🚀 Ready to test command injection!")
    print()
    print("Next steps:")
    print("1. Run: python3 command_injection_demo.py http://localhost:8080")
    print("2. Or: python3 command_injection_demo.py http://localhost:8080 -u admin -p admin")
    print()
    print("⚠️  Remember: This is for educational purposes only!")

if __name__ == "__main__":
    main()
