{"version": "1.130.0", "results": [{"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/footer.html", "start": {"line": 48, "col": 25, "offset": 1773}, "end": {"line": 48, "col": 68, "offset": 1816}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/versions.html", "start": {"line": 32, "col": 52, "offset": 1195}, "end": {"line": 32, "col": 106, "offset": 1249}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/blocks.js", "start": {"line": 132, "col": 21, "offset": 4131}, "end": {"line": 132, "col": 52, "offset": 4162}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/garbage.js", "start": {"line": 541, "col": 7, "offset": 18508}, "end": {"line": 541, "col": 58, "offset": 18559}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/garbage.js", "start": {"line": 691, "col": 23, "offset": 23273}, "end": {"line": 691, "col": 48, "offset": 23298}, "extra": {"message": "RegExp() called with a `element2` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/graph.js", "start": {"line": 505, "col": 3, "offset": 12441}, "end": {"line": 505, "col": 24, "offset": 12462}, "extra": {"message": "Bracket object notation with user input is present, this might allow an attacker to access all properties of the object and even it's prototype. Use literal values for object properties.", "metadata": {"confidence": "LOW", "owasp": ["A02:2017 - Broken Authentication", "A04:2021 - Insecure Design"], "cwe": ["CWE-522: Insufficiently Protected Credentials"], "category": "security", "technology": ["express"], "references": ["https://github.com/nodesecurity/eslint-plugin-security/blob/3c7522ca1be800353513282867a1034c795d9eb4/docs/the-dangers-of-square-bracket-notation.md"], "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/javascript.express.security.audit.remote-property-injection.remote-property-injection", "shortlink": "https://sg.run/Z4gn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/graph.js", "start": {"line": 515, "col": 3, "offset": 12828}, "end": {"line": 515, "col": 24, "offset": 12849}, "extra": {"message": "Bracket object notation with user input is present, this might allow an attacker to access all properties of the object and even it's prototype. Use literal values for object properties.", "metadata": {"confidence": "LOW", "owasp": ["A02:2017 - Broken Authentication", "A04:2021 - Insecure Design"], "cwe": ["CWE-522: Insufficiently Protected Credentials"], "category": "security", "technology": ["express"], "references": ["https://github.com/nodesecurity/eslint-plugin-security/blob/3c7522ca1be800353513282867a1034c795d9eb4/docs/the-dangers-of-square-bracket-notation.md"], "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/javascript.express.security.audit.remote-property-injection.remote-property-injection", "shortlink": "https://sg.run/Z4gn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/graph.js", "start": {"line": 1050, "col": 19, "offset": 29468}, "end": {"line": 1050, "col": 36, "offset": 29485}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/graph.js", "start": {"line": 1417, "col": 19, "offset": 40048}, "end": {"line": 1417, "col": 75, "offset": 40104}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/graph.js", "start": {"line": 1428, "col": 34, "offset": 40541}, "end": {"line": 1428, "col": 51, "offset": 40558}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/graph.js", "start": {"line": 1440, "col": 30, "offset": 41012}, "end": {"line": 1440, "col": 57, "offset": 41039}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/nzbget.js", "start": {"line": 112, "col": 13, "offset": 2896}, "end": {"line": 112, "col": 47, "offset": 2930}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-generic-api-key.detected-generic-api-key", "path": "downloaded_repos/Dash<PERSON>z_dashticz/js/components/trafficinfo.js", "start": {"line": 31, "col": 41, "offset": 927}, "end": {"line": 31, "col": 81, "offset": 967}, "extra": {"message": "Generic API Key detected", "metadata": {"source-rule-url": "https://github.com/dxa4481/truffleHogRegexes/blob/master/truffleHogRegexes/regexes.json", "category": "security", "technology": ["secrets"], "confidence": "LOW", "references": ["https://github.com/dxa4481/truffleHogRegexes/blob/master/truffleHogRegexes/regexes.json"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "cwe": ["CWE-798: Use of Hard-coded Credentials"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-generic-api-key.detected-generic-api-key", "shortlink": "https://sg.run/qxj8"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "path": "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/js/domoticz-api.js", "start": {"line": 444, "col": 51, "offset": 15884}, "end": {"line": 444, "col": 56, "offset": 15889}, "extra": {"message": "Insecure WebSocket Detected. WebSocket Secure (wss) should be used for all WebSocket connections.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "13.5.1 Insecure WebSocket", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x21-V13-API.md#v135-websocket-security-requirements", "section": "V13: API and Web Service Verification Requirements", "version": "4"}, "category": "security", "technology": ["regex"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "shortlink": "https://sg.run/GWyz"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "path": "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/js/domoticz-api.js", "start": {"line": 447, "col": 18, "offset": 16052}, "end": {"line": 447, "col": 23, "offset": 16057}, "extra": {"message": "Insecure WebSocket Detected. WebSocket Secure (wss) should be used for all WebSocket connections.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "13.5.1 Insecure WebSocket", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x21-V13-API.md#v135-websocket-security-requirements", "section": "V13: API and Web Service Verification Requirements", "version": "4"}, "category": "security", "technology": ["regex"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "shortlink": "https://sg.run/GWyz"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/functions.js", "start": {"line": 272, "col": 22, "offset": 8211}, "end": {"line": 272, "col": 43, "offset": 8232}, "extra": {"message": "RegExp() called with a `find` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.incomplete-sanitization.incomplete-sanitization", "path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/settings.js", "start": {"line": 1166, "col": 48, "offset": 42102}, "end": {"line": 1166, "col": 72, "offset": 42126}, "extra": {"message": "`val.replace` method will only replace the first occurrence when used with a string argument (\"'\"). If this method is used for escaping of dangerous data then there is a possibility for a bypass. Try to use sanitization library instead or use a Regex with a global flag.", "metadata": {"cwe": ["CWE-116: Improper Encoding or Escaping of Output"], "category": "security", "technology": ["javascript"], "owasp": ["A03:2021 - Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Encoding"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.incomplete-sanitization.incomplete-sanitization", "shortlink": "https://sg.run/1GbQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/log.html", "start": {"line": 54, "col": 3, "offset": 2360}, "end": {"line": 54, "col": 68, "offset": 2425}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/breadcrumbs.html:1:\n Failure: not a program", "path": "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/breadcrumbs.html"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/layout.html:1:\n Failure: not a program", "path": "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/layout.html"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/search.html:1:\n Failure: not a program", "path": "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/search.html"}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/versions.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 62, "offset": 82}}, {"path": "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/versions.html", "start": {"line": 36, "col": 1, "offset": 0}, "end": {"line": 36, "col": 12, "offset": 11}}]], "message": "Syntax error at line downloaded_repos/Dash<PERSON>z_dashticz/docs/_themes/sphinx_rtd_theme/versions.html:1:\n `{% if READTHEDOCS %}\n{# Add rst-badge after rst-versions for small badge style. #}` was unexpected", "path": "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/versions.html", "spans": [{"file": "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/versions.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 62, "offset": 82}}, {"file": "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/versions.html", "start": {"line": 36, "col": 1, "offset": 0}, "end": {"line": 36, "col": 12, "offset": 11}}]}], "paths": {"scanned": ["downloaded_repos/Dash<PERSON><PERSON>_dashticz/.babelrc", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/.browserslistrc", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/.dockerignore", "downloaded_repos/Dashtic<PERSON>_dashticz/.eslintrc.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/.gitignore", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/.jsbeautifyrc", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/.prettierignore", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/.prettierrc", "downloaded_repos/Dashtic<PERSON>_dashticz/.readthedocs.yaml", "downloaded_repos/Dashticz_dashticz/.vscode/settings.json", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/Dockerfile", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/Makefile", "downloaded_repos/Dashticz_dashticz/README.md", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/babel.config.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/bin/glob-ls.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/css/creative.css", "downloaded_repos/Dash<PERSON>z_dashticz/css/helpers.css", "downloaded_repos/Dashticz_dashticz/css/plugins.css", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/css/sortable.css", "downloaded_repos/Dashticz_dashticz/custom/CONFIG_DEFAULT.js", "downloaded_repos/Dashticz_dashticz/custom/custom.DEFAULT.js", "downloaded_repos/Dashticz_dashticz/custom_2/CONFIG_DEFAULT.js", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/Makefile", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/_static/.gitignore", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/_static/css/custom.css", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/__init__.py", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/breadcrumbs.html", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/footer.html", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/layout.html", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/search.html", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/searchbox.html", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/static/css/badge_only.css", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/static/css/theme.css", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/static/fonts/FontAwesome.otf", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/static/fonts/fontawesome-webfont.eot", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/static/fonts/fontawesome-webfont.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/static/fonts/fontawesome-webfont.ttf", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/static/fonts/fontawesome-webfont.woff", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/static/fonts/fontawesome-webfont.woff2", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/static/js/theme.js", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/theme.conf", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/versions.html", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/blocks.rst", "downloaded_repos/Dashticz_dashticz/docs/blocks/buttons.rst", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/domoticzblocks.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/frames.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/framesexample.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/graphs.rst", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/img/659_1_2.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/batterylevel.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/block659.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/block659_4_custom.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/block659_w3.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/block_another.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/blockbackground.jpg", "downloaded_repos/Dashticz_dashticz/docs/blocks/img/climate.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/climate_te_hu.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/colorpicker1.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/cprgb.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/cprgbw.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/cprgbwwz.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/customlabels.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/dial.png", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/img/evohome.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/framescale.jpg", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/img/graph_bar.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/graph_buttons1.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/graph_buttons2.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/graph_buttons3.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/graph_buttons4.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/graph_customheader_after.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/graph_customheader_before.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/graph_debug.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/graph_groupByDevice_tooltip1.png", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/img/graph_groupby_day.png", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/img/graph_stacked.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/graph_zoom_x.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/graph_zoom_x2.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/graphswitch.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/group.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/group_by_device_1.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/group_by_device_2.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/group_by_device_3.png", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/img/muligraph_patch4_1.png", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/img/muligraph_patch4_2.png", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/img/muligraph_patch4_3.png", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/img/muligraph_patch4_4.png", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/img/muligraph_patch4_5.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/multigraph3.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/multigraph4.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/multigraph5.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/multigraph6.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/multigraph_button_styling.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/multigraph_custom.png", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/img/multigraph_point_styling.jpg", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/img/multigraph_setpoints.png", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/img/p1.jpg", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/img/p1_custom.jpg", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/img/p1_legend.jpg", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/img/p1_legend_2.jpg", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/img/p1multi.png", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/img/solar_1_default.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/solar_custom_legend.jpg", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/img/solar_default.jpg", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/img/solar_legend.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/solar_yellow_bar.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/tooltiptotal_array.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/tooltiptotal_false.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/tooltiptotal_true.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/img/water.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/Stationclock.jpg", "downloaded_repos/Dashtic<PERSON>_dashticz/docs/blocks/specials/alarmmeldingen.JPG", "downloaded_repos/Dashtic<PERSON>_dashticz/docs/blocks/specials/alarmmeldingen.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/blocktitle.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/calendar.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/camera_block.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/cameras.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/clock.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/clock.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/clocks.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/custom9292.png", "downloaded_repos/Dashticz_dashticz/docs/blocks/specials/customstreamplayer.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/dial.rst", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/specials/domoticzlog.png", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/specials/domoticzlog.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/garbage.rst", "downloaded_repos/Dashticz_dashticz/docs/blocks/specials/gmChooseMaps.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/googlemaps.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/googlemaps.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/group.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/haymanclock.jpg", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/specials/html.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/blocktitle.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/calendar0.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/calendar1.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/calendar2.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/calendar2_modal.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/calendar_birtdays.png", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/specials/img/caleventclasses.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/dial_combi.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/dial_combi2.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/dial_dialswitch.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/dial_dimmer.jpg", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/specials/img/dial_on-of_switch.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/dial_p1-meter-cons.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/dial_p1-meter-prod.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/dial_p1values.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/dial_temp-hum-baro.jpg", "downloaded_repos/Dash<PERSON>z_dashticz/docs/blocks/specials/img/dial_temp-humidity.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/dial_textcombi.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/dial_wind.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/dialbg.jpg", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/specials/img/dialblinds.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/dialmenu.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/dials.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/energydials.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/flipclock.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/googlemapsroute.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/group.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/haymanclock.jpg", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/specials/img/hb-dial.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/humbar-dial.jpg", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/specials/img/irailbe.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/irailbebrussel.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/longfonds.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/miniclock.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/multicolor_selector_switch.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/orangeclouds.jpg", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/specials/img/owm3_limits.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/owmwidgets_layout.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/p1dials.jpg", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/specials/img/stoputrlun.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/th_dial.jpg", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/specials/img/thermtempupdown.jpg", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/specials/img/thermupdown.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/timechart.gif", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/timechart2.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/timechart3.jpg", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/specials/img/toon_dial.jpg", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/specials/img/tpcutrlun.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/trafficinfo.jpg", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/specials/img/treinen.jpg", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/specials/img/treinstations.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/updown.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/waqi-citycode.jpg", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/specials/img/waqi-large.jpg", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/specials/img/waqi-wordpress.jpg", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/blocks/specials/img/waqi.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/weather-rows.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/weather_current.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/weather_default.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/weather_detailed.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/weather_hourly.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/weather_icons.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/weatherwidget.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/winddial.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/img/windknopen.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/longfonds.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/moon.17.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/moon.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/news.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/news.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/nzbget.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/owmwidget.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/publictransport.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/secpanel.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/security_panel.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/security_panel_block.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/security_panel_frame.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/sonarr.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/spotify.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/streamplayer.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/sunrise.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/sunrise.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/timegraph.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/topbar.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/trafficinfo.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/traffictrain.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/tv-guide.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/tvguide.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/waqi.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/weather.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/weather_owm.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/ziggohorizon.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/columns.rst", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/conf.py", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/configuration.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/customcss/Bulb_rgba.jpg", "downloaded_repos/Dashticz_dashticz/docs/customcss/Customcode_bulb.jpg", "downloaded_repos/Dashticz_dashticz/docs/customcss/Lmslargebuttons.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/customcss/block_120_css.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/customcss/block_120_lastupdate.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/customcss/cust_drop_down_block.jpg", "downloaded_repos/Dashticz_dashticz/docs/customcss/customcss.rst", "downloaded_repos/Dashticz_dashticz/docs/customcss/specialclasses.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/customization.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/customjs/customjs.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/customjs/zware_windstoten.png", "downloaded_repos/Dashticz_dashticz/docs/dashticzconfiguration.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/develop/code.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/develop/design.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/develop/develop.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/develop/documentation.rst", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/develop/translations.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/gettingstarted/apiprotection2.jpg", "downloaded_repos/Dashticz_dashticz/docs/gettingstarted/automaticinstall.rst", "downloaded_repos/Dashticz_dashticz/docs/gettingstarted/basicdashboard.rst", "downloaded_repos/Dashtic<PERSON>_dashticz/docs/gettingstarted/guide_step_1.png", "downloaded_repos/Dashtic<PERSON>_dashticz/docs/gettingstarted/guide_step_2.png", "downloaded_repos/Dashticz_dashticz/docs/gettingstarted/index.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/gettingstarted/installation.rst", "downloaded_repos/Dashtic<PERSON>_dashticz/docs/gettingstarted/manualinstall.rst", "downloaded_repos/Dashticz_dashticz/docs/img/dashticz-websocket.gif", "downloaded_repos/Dashtic<PERSON>_dashticz/docs/img/dashticz.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/index.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/introduction.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/make.bat", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/releasenotes/index.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/releasenotes/releasenotes.rst", "downloaded_repos/Dashticz_dashticz/docs/releasenotes/upgrading/upgrading.rst", "downloaded_repos/Dashticz_dashticz/docs/releasenotes/upgrading/v3.10.1.rst", "downloaded_repos/Dash<PERSON>z_dashticz/docs/releasenotes/upgrading/v320.rst", "downloaded_repos/Dash<PERSON>z_dashticz/docs/releasenotes/upgrading/v341.rst", "downloaded_repos/Dash<PERSON>z_dashticz/docs/releasenotes/upgrading/v349.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/releasenotes/upgrading/v373.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/releasenotes/upgrading/v376.rst", "downloaded_repos/Dash<PERSON>z_dashticz/docs/releasenotes/upgrading/v389.rst", "downloaded_repos/Dashtic<PERSON>_dashticz/docs/requirements.txt", "downloaded_repos/Dashticz_dashticz/docs/screens.rst", "downloaded_repos/Dashticz_dashticz/docs/tipsandtricks/buttonimage.jpg", "downloaded_repos/Dashticz_dashticz/docs/tipsandtricks.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/troubleshooting/connection.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/troubleshooting/docker.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/troubleshooting/index.rst", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/troubleshooting/issues-domoticzblocks.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/troubleshooting/other.rst", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/dz_logo.png", "downloaded_repos/Dashticz_dashticz/font/Audiowide.eot", "downloaded_repos/Dashticz_dashticz/font/Audiowide.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/font/Audiowide.ttf", "downloaded_repos/Dashticz_dashticz/font/Audiowide.woff", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/font/Digital.eot", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/font/Digital.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/font/Digital.ttf", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/font/Digital.woff", "downloaded_repos/Dash<PERSON>z_dashticz/img/Coltemp48.png", "downloaded_repos/Dashticz_dashticz/img/Customw48.png", "downloaded_repos/Dashticz_dashticz/img/Customww48.png", "downloaded_repos/Dash<PERSON>z_dashticz/img/RGB48.png", "downloaded_repos/Dashticz_dashticz/img/White48.png", "downloaded_repos/Dashticz_dashticz/img/air.png", "downloaded_repos/Dashticz_dashticz/img/alarm.png", "downloaded_repos/Dashticz_dashticz/img/alarmeringen.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/bg0.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/bg1.jpg", "downloaded_repos/Dash<PERSON>z_dashticz/img/bg10.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/bg11.jpg", "downloaded_repos/Dashticz_dashticz/img/bg12.jpg", "downloaded_repos/Dashticz_dashticz/img/bg13.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/bg2.jpg", "downloaded_repos/Dash<PERSON>z_dashticz/img/bg3.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/bg4.jpg", "downloaded_repos/Dash<PERSON>z_dashticz/img/bg5.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/bg7.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/bg8.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/bg9.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/bg_afternoon.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/bg_night.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/blinds_closed.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/blinds_open.png", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/img/buien_radar.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/bulb.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/bulb_off.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/bulb_on.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/bulbyellow.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/bulbyellow_off.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/bulbyellow_on.png", "downloaded_repos/Dash<PERSON>z_dashticz/img/coldwarm.jpg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/curtainclosed.png", "downloaded_repos/Dashticz_dashticz/img/curtainopen.png", "downloaded_repos/Dashticz_dashticz/img/dashticz.png", "downloaded_repos/Dashticz_dashticz/img/dashticz.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/dishwasher_240px.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/dishwasher_off.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/dishwasher_on.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/door_closed.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/door_open.png", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/img/evohome.png", "downloaded_repos/Dashticz_dashticz/img/fan.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/fan_off.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/fan_on.png", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/img/favicon/android-chrome-192x192.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/favicon/apple-touch-icon-114x114.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/favicon/apple-touch-icon-120x120.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/favicon/apple-touch-icon-144x144.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/favicon/apple-touch-icon-152x152.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/favicon/apple-touch-icon-180x180.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/favicon/apple-touch-icon-57x57.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/favicon/apple-touch-icon-60x60.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/favicon/apple-touch-icon-72x72.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/favicon/apple-touch-icon-76x76.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/favicon/favicon-16x16.png", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/img/favicon/favicon-32x32.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/favicon/favicon-96x96.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/favicon/favicon.ico", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/favicon/largetile.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/favicon/mediumtile.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/favicon/smalltile.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/favicon/widetile.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/fr24.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/garbage/kliko.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/garbage/kliko_black.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/garbage/kliko_blue.png", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/img/garbage/kliko_brown.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/garbage/kliko_green.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/garbage/kliko_grey.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/garbage/kliko_orange.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/garbage/kliko_red.png", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/img/garbage/kliko_white.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/garbage/kliko_yellow.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/garbage/tree.png", "downloaded_repos/Dashticz_dashticz/img/heating.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/heating_off.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/heating_on.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/hot_water_on.png", "downloaded_repos/Dashticz_dashticz/img/kodi.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/longfonds.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/mi_robot.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/microphone_off.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/microphone_on.png", "downloaded_repos/Dashticz_dashticz/img/moon/moon.0.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.00.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.01.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.02.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/moon/moon.03.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.04.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.05.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.06.png", "downloaded_repos/Dashticz_dashticz/img/moon/moon.07.png", "downloaded_repos/Dashticz_dashticz/img/moon/moon.08.png", "downloaded_repos/Dashticz_dashticz/img/moon/moon.09.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.10.png", "downloaded_repos/Dashticz_dashticz/img/moon/moon.11.png", "downloaded_repos/Dashticz_dashticz/img/moon/moon.12.png", "downloaded_repos/Dashticz_dashticz/img/moon/moon.13.png", "downloaded_repos/Dashticz_dashticz/img/moon/moon.14.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.15.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.16.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.17.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.18.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.19.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/moon/moon.20.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.21.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.22.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.23.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/moon/moon.24.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/moon/moon.25.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/moon/moon.26.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/moon/moon.27.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/moon/moon.28.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.29.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/moon/moon.30.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/moon/moon.31.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/moon/moon.32.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.33.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/moon/moon.34.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/moon/moon.35.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/moon/moon.36.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/moon/moon.37.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/moon/moon.38.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/moon/moon.39.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.40.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/moon/moon.41.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/moon/moon.42.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/moon/moon.43.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/moon/moon.44.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/moon/moon.45.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.46.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.47.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.48.png", "downloaded_repos/Dashticz_dashticz/img/moon/moon.49.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.50.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.51.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/moon/moon.52.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.53.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/moon/moon.54.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/moon/moon.55.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.56.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.57.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/moon/moon.58.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.59.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/moon/moon.60.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.61.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.62.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/moon/moon.63.png", "downloaded_repos/Dashticz_dashticz/img/moon/moon.64.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.65.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.66.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.67.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/moon/moon.68.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.69.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/moon/moon.70.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.71.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.72.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.73.png", "downloaded_repos/Dashticz_dashticz/img/moon/moon.74.png", "downloaded_repos/Dashticz_dashticz/img/moon/moon.75.png", "downloaded_repos/Dashticz_dashticz/img/moon/moon.76.png", "downloaded_repos/Dashticz_dashticz/img/moon/moon.77.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.78.png", "downloaded_repos/Dashticz_dashticz/img/moon/moon.79.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.80.png", "downloaded_repos/Dashticz_dashticz/img/moon/moon.81.png", "downloaded_repos/Dashticz_dashticz/img/moon/moon.82.png", "downloaded_repos/Dashticz_dashticz/img/moon/moon.83.png", "downloaded_repos/Dashticz_dashticz/img/moon/moon.84.png", "downloaded_repos/Dashticz_dashticz/img/moon/moon.85.png", "downloaded_repos/Dashticz_dashticz/img/moon/moon.86.png", "downloaded_repos/Dashticz_dashticz/img/moon/moon.87.png", "downloaded_repos/Dashticz_dashticz/img/moon/moon.88.png", "downloaded_repos/Dashticz_dashticz/img/moon/moon.89.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/moon/moon.90.png", "downloaded_repos/Dashticz_dashticz/img/moon/moon.91.png", "downloaded_repos/Dashticz_dashticz/img/moon/moon.92.png", "downloaded_repos/Dashticz_dashticz/img/moon/moon.93.png", "downloaded_repos/Dashticz_dashticz/img/moon/moon.94.png", "downloaded_repos/Dashticz_dashticz/img/moon/moon.95.png", "downloaded_repos/Dashticz_dashticz/img/moon/moon.96.png", "downloaded_repos/Dashticz_dashticz/img/moon/moon.97.png", "downloaded_repos/Dashticz_dashticz/img/moon/moon.98.png", "downloaded_repos/Dashticz_dashticz/img/moon/moon.99.png", "downloaded_repos/Dashticz_dashticz/img/motion_off.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/motion_on.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/ns.png", "downloaded_repos/Dashticz_dashticz/img/radiator.svg", "downloaded_repos/Dashticz_dashticz/img/radio-streaming.png", "downloaded_repos/Dashticz_dashticz/img/radio.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/radio_on.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/roofwindow_closed.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/roofwindow_open.svg", "downloaded_repos/Dash<PERSON>z_dashticz/img/rpi.svg", "downloaded_repos/Dashticz_dashticz/img/sleep.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/sleep_off.png", "downloaded_repos/Dashtic<PERSON>_dashticz/img/sleep_on.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/speaker_off.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/speaker_on.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/toon.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/tv.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/tv_off.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/uptime.png", "downloaded_repos/Dashticz_dashticz/img/volumio.png", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/fill/01d.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/fill/01n.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/fill/02d.svg", "downloaded_repos/Dash<PERSON>z_dashticz/img/weathericons/fill/02n.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/fill/03d.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/fill/03n.svg", "downloaded_repos/Dash<PERSON>z_dashticz/img/weathericons/fill/04d.svg", "downloaded_repos/Dash<PERSON>z_dashticz/img/weathericons/fill/04n.svg", "downloaded_repos/Dash<PERSON>z_dashticz/img/weathericons/fill/09d.svg", "downloaded_repos/Dash<PERSON>z_dashticz/img/weathericons/fill/09n.svg", "downloaded_repos/Dash<PERSON>z_dashticz/img/weathericons/fill/10d.svg", "downloaded_repos/Dash<PERSON>z_dashticz/img/weathericons/fill/10n.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/fill/11d.svg", "downloaded_repos/Dash<PERSON>z_dashticz/img/weathericons/fill/11n.svg", "downloaded_repos/Dash<PERSON>z_dashticz/img/weathericons/fill/13d.svg", "downloaded_repos/Dash<PERSON>z_dashticz/img/weathericons/fill/13n.svg", "downloaded_repos/Dash<PERSON>z_dashticz/img/weathericons/fill/50d.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/fill/50n.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/line/01d.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/line/01n.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/line/02d.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/line/02n.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/line/03d.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/line/03n.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/line/04d.svg", "downloaded_repos/Dash<PERSON>z_dashticz/img/weathericons/line/04n.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/line/09d.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/line/09n.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/line/10d.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/line/10n.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/line/11d.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/line/11n.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/line/13d.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/line/13n.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/line/50d.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/line/50n.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/linestatic/01d.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/linestatic/01n.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/linestatic/02d.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/linestatic/02n.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/linestatic/03d.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/linestatic/03n.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/linestatic/04d.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/linestatic/04n.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/linestatic/09d.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/linestatic/09n.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/linestatic/10d.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/linestatic/10n.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/linestatic/11d.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/linestatic/11n.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/linestatic/13d.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/linestatic/13n.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/linestatic/50d.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/weathericons/linestatic/50n.svg", "downloaded_repos/Dashticz_dashticz/img/weathericons/meteo/01d.svg", "downloaded_repos/Dashticz_dashticz/img/weathericons/meteo/01n.svg", "downloaded_repos/Dashticz_dashticz/img/weathericons/meteo/02d.svg", "downloaded_repos/Dashticz_dashticz/img/weathericons/meteo/02n.svg", "downloaded_repos/Dashticz_dashticz/img/weathericons/meteo/03d.svg", "downloaded_repos/Dashticz_dashticz/img/weathericons/meteo/03n.svg", "downloaded_repos/Dashticz_dashticz/img/weathericons/meteo/04d.svg", "downloaded_repos/Dashticz_dashticz/img/weathericons/meteo/04n.svg", "downloaded_repos/Dashticz_dashticz/img/weathericons/meteo/09d.svg", "downloaded_repos/Dashticz_dashticz/img/weathericons/meteo/09n.svg", "downloaded_repos/Dashticz_dashticz/img/weathericons/meteo/10d.svg", "downloaded_repos/Dashticz_dashticz/img/weathericons/meteo/10n.svg", "downloaded_repos/Dashticz_dashticz/img/weathericons/meteo/11d.svg", "downloaded_repos/Dashticz_dashticz/img/weathericons/meteo/11n.svg", "downloaded_repos/Dashticz_dashticz/img/weathericons/meteo/13d.svg", "downloaded_repos/Dashticz_dashticz/img/weathericons/meteo/13n.svg", "downloaded_repos/Dashticz_dashticz/img/weathericons/meteo/50d.svg", "downloaded_repos/Dashticz_dashticz/img/weathericons/meteo/50n.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/wii.svg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/index.html", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/index2.html", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/blocks.js", "downloaded_repos/Dash<PERSON>z_dashticz/js/blocktypes.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/calendar.js", "downloaded_repos/Dash<PERSON>z_dashticz/js/chromecast.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/coins.js", "downloaded_repos/Dash<PERSON>z_dashticz/js/colorpicker.js", "downloaded_repos/Dashticz_dashticz/js/components/alarmmeldingen.js", "downloaded_repos/Dash<PERSON>z_dashticz/js/components/basicclock.css", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/basicclock.js", "downloaded_repos/Dashticz_dashticz/js/components/blocktitle.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/button.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/calendar.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/camera.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/coronavirus.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/dial.js", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/js/components/domoticzblock.js", "downloaded_repos/Dashticz_dashticz/js/components/flipclock.css", "downloaded_repos/Dash<PERSON>z_dashticz/js/components/flipclock.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/frame.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/garbage.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/graph.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/group.js", "downloaded_repos/Dashticz_dashticz/js/components/haymanclock.css", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/haymanclock.js", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/js/components/html.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/log.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/longfonds.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/map.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/news.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/nzbget.js", "downloaded_repos/Dashticz_dashticz/js/components/owmwidget.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/publictransport.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/secpanel.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/simpleblock.js", "downloaded_repos/Dashticz_dashticz/js/components/stationclock.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/streamplayer.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/template.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/timegraph.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/traffic.js", "downloaded_repos/Dash<PERSON>z_dashticz/js/components/trafficinfo.js", "downloaded_repos/Dashticz_dashticz/js/components/train.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/tvguide.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/waqi.js", "downloaded_repos/Dashticz_dashticz/js/components/weather.css", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/weather.js", "downloaded_repos/Dashticz_dashticz/js/dashticz.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/debug.js", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/js/domoticz-api.js", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/js/dt_function.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/fullscreen.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/functions.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/loader.js", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/js/login.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/main.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/moon.js", "downloaded_repos/Dash<PERSON>z_dashticz/js/polyfills.js", "downloaded_repos/Dashticz_dashticz/js/savesettings.php", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/settings.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/sonarr.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/sortable.js", "downloaded_repos/Dash<PERSON>z_dashticz/js/spotify.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/switches.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/thermostat.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/version.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/weather.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/weather_owm.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/jsconfig.json", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/lang/bs_BA.json", "downloaded_repos/Dashticz_dashticz/lang/ca_ES.json", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/lang/cs_CZ.json", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/lang/da_DK.json", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/lang/de_DE.json", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/lang/en_US.json", "downloaded_repos/Dashticz_dashticz/lang/es_ES.json", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/lang/fi_FI.json", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/lang/fr_FR.json", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/lang/hu_HU.json", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/lang/it_IT.json", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/lang/ja_JP.json", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/lang/lt_LT.json", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/lang/nb_NO.json", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/lang/nl_NL.json", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/lang/nn_NO.json", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/lang/pl_PL.json", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/lang/pt_PT.json", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/lang/ro_RO.json", "downloaded_repos/Dashtic<PERSON>_dashticz/lang/ru_RU.json", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/lang/sk_SK.json", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/lang/sl_SI.json", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/lang/sl_SL.json", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/lang/sr_RS.json", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/lang/sv_SE.json", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/lang/tr_TR.json", "downloaded_repos/Dashticz_dashticz/lang/uk_UA.json", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/lang/zh_CN.json", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/log.html", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/nginx.conf", "downloaded_repos/Dashtic<PERSON>_dashticz/package-lock.json", "downloaded_repos/Dashticz_dashticz/package.json", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/scripts/bu.sh", "downloaded_repos/Dashticz_dashticz/scripts/dashticz_install.sh", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/airplane.aac", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/airplane.mp3", "downloaded_repos/Dashticz_dashticz/sounds/airplane.ogg", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/arm.aac", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/sounds/arm.mp3", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/arm.ogg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/sounds/beer_can_opening.aac", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/sounds/beer_can_opening.mp3", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/beer_can_opening.ogg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/sounds/bell_ring.aac", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/sounds/bell_ring.mp3", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/bell_ring.ogg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/sounds/branch_break.aac", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/sounds/branch_break.mp3", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/branch_break.ogg", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/button_click.aac", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/button_click.mp3", "downloaded_repos/Dashticz_dashticz/sounds/button_click.ogg", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/button_click_on.aac", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/sounds/button_click_on.mp3", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/button_click_on.ogg", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/button_push.aac", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/button_push.mp3", "downloaded_repos/Dashticz_dashticz/sounds/button_push.ogg", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/camera_flashing.aac", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/camera_flashing.mp3", "downloaded_repos/Dashticz_dashticz/sounds/camera_flashing.ogg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/sounds/computer_error.aac", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/sounds/computer_error.mp3", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/sounds/computer_error.ogg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/sounds/disarm.aac", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/sounds/disarm.mp3", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/sounds/disarm.ogg", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/door_bell.aac", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/door_bell.mp3", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/door_bell.ogg", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/door_bump.aac", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/door_bump.mp3", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/door_bump.ogg", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/doorbell.aac", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/doorbell.mp3", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/doorbell.ogg", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/glass.aac", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/glass.mp3", "downloaded_repos/Dashticz_dashticz/sounds/glass.ogg", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/key.aac", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/key.mp3", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/key.ogg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/sounds/light_bulb_breaking.aac", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/sounds/light_bulb_breaking.mp3", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/light_bulb_breaking.ogg", "downloaded_repos/Dashticz_dashticz/sounds/ping.aac", "downloaded_repos/Dashticz_dashticz/sounds/ping.mp3", "downloaded_repos/Dashticz_dashticz/sounds/ping.ogg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/sounds/pop_cork.aac", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/sounds/pop_cork.mp3", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/sounds/pop_cork.ogg", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/snap.aac", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/snap.mp3", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/snap.ogg", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/sonar.aac", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/sonar.mp3", "downloaded_repos/Dashticz_dashticz/sounds/sonar.ogg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/sounds/tap.aac", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/sounds/tap.mp3", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/tap.ogg", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/water_droplet.aac", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/water_droplet.mp3", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/water_droplet.ogg", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/wrongcode.aac", "downloaded_repos/Dashtic<PERSON>_dashticz/sounds/wrongcode.mp3", "downloaded_repos/Dashticz_dashticz/sounds/wrongcode.ogg", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/src/handlebars-helpers.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/src/index.js", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/src/loader.scss", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/src/templateengine.js", "downloaded_repos/Dashticz_dashticz/switch_horizon.php", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/themes/white/white.css", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/tpl/calendar_0.tpl", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/tpl/calendar_1.tpl", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/tpl/calendar_2_event.tpl", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/tpl/calendar_2_modal.tpl", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/tpl/calendar_2_template.tpl", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/tpl/calendar_3.tpl", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/tpl/calendar_4.tpl", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/tpl/calendar_modal_iframe.tpl", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/tpl/camera_image.tpl", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/tpl/camera_video.tpl", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/tpl/clock_hayman.tpl", "downloaded_repos/Dash<PERSON>z_dashticz/tpl/colorpicker.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/tpl/corona_graph_header.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/tpl/corona_report.tpl", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/tpl/debug.tpl", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/tpl/debugstatus.tpl", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/tpl/dial.tpl", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/tpl/dialblinds.tpl", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/tpl/garbage_0.tpl", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/tpl/garbage_1.tpl", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/tpl/garbage_2.tpl", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/tpl/graph_header.tpl", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/tpl/graph_tooltip.tpl", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/tpl/graph_tooltip_table.tpl", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/tpl/news_modal.tpl", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/tpl/news_row.tpl", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/tpl/pubtrans_ov.tpl", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/tpl/pubtrans_treinen.tpl", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/tpl/secpanel.tpl", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/tpl/secpanel_modal.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/tpl/thermostat_block.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/tpl/thermostat_evo_cont.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/tpl/thermostat_evo_hw.tpl", "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/tpl/thermostat_evo_zone.tpl", "downloaded_repos/Dash<PERSON>z_dashticz/tpl/traffic.tpl", "downloaded_repos/Dash<PERSON>z_dashticz/tpl/tvguide_0.tpl", "downloaded_repos/Dash<PERSON>z_dashticz/tpl/tvguide_1.tpl", "downloaded_repos/Dash<PERSON>z_dashticz/tpl/weather_0.tpl", "downloaded_repos/Dash<PERSON>z_dashticz/tpl/weather_1.tpl", "downloaded_repos/Dash<PERSON>z_dashticz/tpl/weather_2.tpl", "downloaded_repos/Dash<PERSON>z_dashticz/tpl/weather_3.tpl", "downloaded_repos/Dash<PERSON>z_dashticz/tpl/weather_4.tpl", "downloaded_repos/Dash<PERSON>z_dashticz/tpl/weatherknmi_0.tpl", "downloaded_repos/Dash<PERSON>z_dashticz/tpl/weatherknmi_1.tpl", "downloaded_repos/Dash<PERSON>z_dashticz/tpl/weatherknmi_2.tpl", "downloaded_repos/Dash<PERSON>z_dashticz/tpl/weatherknmi_3.tpl", "downloaded_repos/Dash<PERSON>z_dashticz/tpl/weatherknmi_4.tpl", "downloaded_repos/Dashticz_dashticz/uploads/.gitignore", "downloaded_repos/Dash<PERSON><PERSON>_dashticz/version.txt", "downloaded_repos/Dashtic<PERSON>_dashticz/webpack.config.js"], "skipped": [{"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/dist/475.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/dist/assets/fonts/fa-brands-400.eot", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/dist/assets/fonts/fa-brands-400.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/dist/assets/fonts/fa-brands-400.ttf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/dist/assets/fonts/fa-brands-400.woff", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/dist/assets/fonts/fa-brands-400.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/dist/assets/fonts/fa-regular-400.eot", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/dist/assets/fonts/fa-regular-400.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/dist/assets/fonts/fa-regular-400.ttf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/dist/assets/fonts/fa-regular-400.woff", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/dist/assets/fonts/fa-regular-400.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/dist/assets/fonts/fa-solid-900.eot", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/dist/assets/fonts/fa-solid-900.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/dist/assets/fonts/fa-solid-900.ttf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/dist/assets/fonts/fa-solid-900.woff", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/dist/assets/fonts/fa-solid-900.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/dist/assets/fonts/fa-v4compatibility.ttf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/dist/assets/fonts/fa-v4compatibility.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/dist/assets/fonts/glyphicons-halflings-regular.eot", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/dist/assets/fonts/glyphicons-halflings-regular.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/dist/assets/fonts/glyphicons-halflings-regular.ttf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/dist/assets/fonts/glyphicons-halflings-regular.woff", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/dist/assets/fonts/glyphicons-halflings-regular.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/dist/assets/images/ui-icons_444444_256x240.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/dist/assets/images/ui-icons_555555_256x240.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/dist/assets/images/ui-icons_777620_256x240.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/dist/assets/images/ui-icons_777777_256x240.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/dist/assets/images/ui-icons_cc0000_256x240.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/dist/assets/images/ui-icons_ffffff_256x240.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/dist/bundle.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/dist/bundle.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/dist/bundle.js.LICENSE.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/breadcrumbs.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/layout.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/search.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/static/js/modernizr.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/docs/_themes/sphinx_rtd_theme/versions.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/docs/blocks/specials/camera_block.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/Dash<PERSON>z_dashticz/img/bg14.jpg", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/bg6.jpg", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/bg_morning.jpg", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/img/bg_noon.jpg", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/cors.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/garbage/index.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/composer.lock", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/ical5/SG_iCal.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/ical5/blocks/SG_iCal_VCalendar.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/ical5/blocks/SG_iCal_VEvent.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/ical5/blocks/SG_iCal_VTimeZone.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/ical5/helpers/SG_iCal_Duration.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/ical5/helpers/SG_iCal_Factory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/ical5/helpers/SG_iCal_Freq.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/ical5/helpers/SG_iCal_Line.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/ical5/helpers/SG_iCal_Parser.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/ical5/helpers/SG_iCal_Query.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/ical5/helpers/SG_iCal_Recurrence.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/ical5/index.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/index.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/autoload.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/composer/ClassLoader.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/composer/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/composer/autoload_classmap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/composer/autoload_namespaces.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/composer/autoload_psr4.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/composer/autoload_real.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/composer/autoload_static.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/composer/installed.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/johngrogg/ics-parser/.editorconfig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/johngrogg/ics-parser/.github/ISSUE_TEMPLATE/bug_report.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/johngrogg/ics-parser/.github/PULL_REQUEST_TEMPLATE/pull_request_template.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/johngrogg/ics-parser/.github/dependabot.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/johngrogg/ics-parser/.github/release_template.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/johngrogg/ics-parser/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/johngrogg/ics-parser/CONTRIBUTING.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/johngrogg/ics-parser/FUNDING.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/johngrogg/ics-parser/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/johngrogg/ics-parser/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/johngrogg/ics-parser/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/johngrogg/ics-parser/composer.lock", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/johngrogg/ics-parser/ecs.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/johngrogg/ics-parser/examples/ICal.ics", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/johngrogg/ics-parser/examples/index.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/johngrogg/ics-parser/phpunit.xml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/johngrogg/ics-parser/rector.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/johngrogg/ics-parser/src/ICal/Event.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/johngrogg/ics-parser/src/ICal/ICal.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/johngrogg/ics-parser/tests/KeyValueTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/johngrogg/ics-parser/tests/RecurrencesTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/johngrogg/ics-parser/tests/SingleEventsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/johngrogg/ics-parser/tests/ical/ical-monthly.ics", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/johngrogg/ics-parser/tests/ical/issue-196.ics", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/johngrogg/ics-parser/tests/rfc5545RecurrenceExamplesTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/.github/workflows/php.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/example/index.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/readme.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/src/Freq.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/src/IcalParser.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/src/Recurrence.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/src/WindowsTimezones.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/blank_description.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/cal/38_weekly_recurring_event_missing_day.ics", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/cal/FrenchHolidays.ics", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/cal/basic.ics", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/cal/blank_description.ics", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/cal/blank_line_end.ics", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/cal/blank_line_mid.ics", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/cal/daily_recur.ics", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/cal/daily_recur2.ics", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/cal/day_long_recur_yearly.ics", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/cal/forced_types.ics", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/cal/google_birthday.ics", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/cal/minimal.ics", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/cal/missing_RRULE_notice.ics", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/cal/multiline_description.ics", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/cal/multiple_attachments.ics", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/cal/multiple_categories.ics", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/cal/multiple_rrules.ics", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/cal/parserv2.ics", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/cal/readme.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/cal/recur_instances.ics", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/cal/recur_instances_finite.ics", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/cal/recur_instances_with_modifications.ics", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/cal/recur_instances_with_modifications_and_interval.ics", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/cal/recur_instances_with_modifications_to_first_day.ics", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/cal/url.ics", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/cal/utc_negative_zero.ics", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/cal/weird_windows_timezones.ics", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/cal/wrong_dates.ics", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/multiline_description.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/multiple_attachments.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/multiple_categories.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/recurring_events.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/sort_events.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/timezone.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/url.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tests/wrong_dates.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/ical/vendor/om/icalparser/tools/windowstimezones.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/info.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/nocache.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/dashticz/upload.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dash<PERSON>z_dashticz/vendor/jquery.newsTicker.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dashticz_dashticz/vendor/stationclock.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/vendor/weather/css/weather-icons-wind.min.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/vendor/weather/css/weather-icons.min.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/vendor/weather/font/weathericons-regular-webfont.eot", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/vendor/weather/font/weathericons-regular-webfont.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/vendor/weather/font/weathericons-regular-webfont.ttf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/vendor/weather/font/weathericons-regular-webfont.woff", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/vendor/weather/font/weathericons-regular-webfont.woff2", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.815134048461914, "profiling_times": {"config_time": 6.2737438678741455, "core_time": 8.98298954963684, "ignores_time": 0.002144336700439453, "total_time": 15.259715795516968}, "parsing_time": {"total_time": 7.069988965988159, "per_file_time": {"mean": 0.04310968881700097, "std_dev": 0.006290957669505498}, "very_slow_stats": {"time_ratio": 0.2248122096671769, "count_ratio": 0.024390243902439025}, "very_slow_files": [{"fpath": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/blocks.js", "ftime": 0.34797000885009766}, {"fpath": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/graph.js", "ftime": 0.36489295959472656}, {"fpath": "downloaded_repos/Dash<PERSON>z_dashticz/js/components/flipclock.js", "ftime": 0.4368879795074463}, {"fpath": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/dial.js", "ftime": 0.4396688938140869}]}, "scanning_time": {"total_time": 65.4078197479248, "per_file_time": {"mean": 0.03712135059473601, "std_dev": 0.08061986154804329}, "very_slow_stats": {"time_ratio": 0.6360771557133352, "count_ratio": 0.00851305334846765}, "very_slow_files": [{"fpath": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/calendar.js", "ftime": 2.017083168029785}, {"fpath": "downloaded_repos/Dashticz_dashticz/js/dashticz.js", "ftime": 2.027189016342163}, {"fpath": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/garbage.js", "ftime": 2.3624608516693115}, {"fpath": "downloaded_repos/Dash<PERSON>z_dashticz/js/spotify.js", "ftime": 2.510577917098999}, {"fpath": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/main.js", "ftime": 2.875657081604004}, {"fpath": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/settings.js", "ftime": 3.758455991744995}, {"fpath": "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/js/domoticz-api.js", "ftime": 4.088031053543091}, {"fpath": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/blocks.js", "ftime": 4.088796854019165}, {"fpath": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/dial.js", "ftime": 4.381657123565674}, {"fpath": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/graph.js", "ftime": 4.640467166900635}]}, "matching_time": {"total_time": 34.29908776283264, "per_file_and_rule_time": {"mean": 0.04757155029519089, "std_dev": 0.014561466511740557}, "very_slow_stats": {"time_ratio": 0.6917235154279449, "count_ratio": 0.11650485436893204}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/graph.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.46651697158813477}, {"fpath": "downloaded_repos/<PERSON><PERSON><PERSON>_dashticz/js/domoticz-api.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.4946901798248291}, {"fpath": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/dial.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.5135860443115234}, {"fpath": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/sortable.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.568587064743042}, {"fpath": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/main.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.6082990169525146}, {"fpath": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/blocks.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.7576699256896973}, {"fpath": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/settings.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.7882459163665771}, {"fpath": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/graph.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.8504440784454346}, {"fpath": "downloaded_repos/Dash<PERSON>z_dashticz/js/spotify.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 1.1047179698944092}, {"fpath": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/dial.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 1.748898983001709}]}, "tainting_time": {"total_time": 12.634372472763062, "per_def_and_rule_time": {"mean": 0.0029314089263951424, "std_dev": 8.309305836857558e-05}, "very_slow_stats": {"time_ratio": 0.13605304036150542, "count_ratio": 0.0037122969837587007}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/components/graph.js", "fline": 1497, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.07001495361328125}, {"fpath": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/settings.js", "fline": 1, "rule_id": "javascript.express.security.x-frame-options-misconfiguration.x-frame-options-misconfiguration", "time": 0.07067704200744629}, {"fpath": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/settings.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.07289481163024902}, {"fpath": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/blocks.js", "fline": 833, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.08462190628051758}, {"fpath": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/settings.js", "fline": 982, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.10099101066589355}, {"fpath": "downloaded_repos/Dash<PERSON>z_dashticz/js/spotify.js", "fline": 129, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.10496211051940918}, {"fpath": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/settings.js", "fline": 1, "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.13155102729797363}, {"fpath": "downloaded_repos/Dash<PERSON>z_dashticz/js/components/flipclock.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.20582890510559082}, {"fpath": "downloaded_repos/Dash<PERSON>z_dashticz/js/components/flipclock.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.23259902000427246}, {"fpath": "downloaded_repos/Dash<PERSON><PERSON>_dashticz/js/moon.js", "fline": 165, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.27126312255859375}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1093234432}, "engine_requested": "OSS", "skipped_rules": []}