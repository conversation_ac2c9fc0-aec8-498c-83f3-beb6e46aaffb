{"version": "1.130.0", "results": [{"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/docx-preview/docx-preview.js", "start": {"line": 3537, "col": 13, "offset": 145747}, "end": {"line": 3537, "col": 49, "offset": 145783}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/docx-preview/docx-preview.min.mjs", "start": {"line": 1, "col": 63837, "offset": 63836}, "end": {"line": 1, "col": 63899, "offset": 63898}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/docx-preview/docx-preview.mjs", "start": {"line": 3533, "col": 9, "offset": 131486}, "end": {"line": 3533, "col": 45, "offset": 131522}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/PPTXjs/js/filereader.js", "start": {"line": 273, "col": 28, "offset": 8820}, "end": {"line": 273, "col": 41, "offset": 8833}, "extra": {"message": "RegExp() called with a `readAsMap` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/PPTXjs/js/filereader.js", "start": {"line": 338, "col": 49, "offset": 10794}, "end": {"line": 338, "col": 72, "offset": 10817}, "extra": {"message": "RegExp() called with a `opts` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/PPTXjs/js/filereader.js", "start": {"line": 413, "col": 16, "offset": 13171}, "end": {"line": 413, "col": 62, "offset": 13217}, "extra": {"message": "RegExp() called with a `name` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/PPTXjs/js/filereader.js", "start": {"line": 427, "col": 36, "offset": 13659}, "end": {"line": 427, "col": 87, "offset": 13710}, "extra": {"message": "RegExp() called with a `name` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/officeToHtml/officeToHtml.js", "start": {"line": 4440, "col": 55, "offset": 238427}, "end": {"line": 4440, "col": 104, "offset": 238476}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/officeToHtml/officeToHtml.js", "start": {"line": 9515, "col": 59, "offset": 510566}, "end": {"line": 9515, "col": 117, "offset": 510624}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "message": "Timeout when running javascript.express.security.audit.remote-property-injection.remote-property-injection on downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/PPTXjs/js/pptxjs.js:\n ", "path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/PPTXjs/js/pptxjs.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "message": "Timeout when running javascript.express.security.injection.raw-html-format.raw-html-format on downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/PPTXjs/js/pptxjs.js:\n ", "path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/PPTXjs/js/pptxjs.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "message": "Timeout when running javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring on downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/PPTXjs/js/pptxjs.js:\n ", "path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/PPTXjs/js/pptxjs.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/jquery-ui.js:\n ", "path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/jquery-ui.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/jquery-ui.js:\n ", "path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/jquery-ui.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "message": "Timeout when running javascript.express.security.injection.raw-html-format.raw-html-format on downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/jquery-ui.js:\n ", "path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/jquery-ui.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/officeToHtml/officeToHtml.js:\n ", "path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/officeToHtml/officeToHtml.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/pdf.js:\n ", "path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/pdf.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/pdf.js:\n ", "path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/pdf.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/pdf.js:\n ", "path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/pdf.js"}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/docx-preview/docx-preview.min.mjs", "start": {"line": 1, "col": 45859, "offset": 0}, "end": {"line": 1, "col": 45863, "offset": 4}}]], "message": "Syntax error at line downloaded_repos/vish4395_laravel-file-viewer/resources/assets/docx-preview/docx-preview.min.mjs:1:\n `5:1,` was unexpected", "path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/docx-preview/docx-preview.min.mjs", "spans": [{"file": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/docx-preview/docx-preview.min.mjs", "start": {"line": 1, "col": 45859, "offset": 0}, "end": {"line": 1, "col": 45863, "offset": 4}}]}], "paths": {"scanned": ["downloaded_repos/vish4395_laravel-file-viewer/.styleci.yml", "downloaded_repos/vish4395_laravel-file-viewer/CHANGELOG.md", "downloaded_repos/vish4395_laravel-file-viewer/CONTRIBUTING.md", "downloaded_repos/vish4395_laravel-file-viewer/LICENSE.md", "downloaded_repos/vish4395_laravel-file-viewer/README.md", "downloaded_repos/vish4395_laravel-file-viewer/composer.json", "downloaded_repos/vish4395_laravel-file-viewer/config/config.php", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/docx-preview/docx-preview.d.ts", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/docx-preview/docx-preview.js", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/docx-preview/docx-preview.js.map", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/docx-preview/docx-preview.min.js.map", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/docx-preview/docx-preview.min.mjs", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/docx-preview/docx-preview.min.mjs.map", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/docx-preview/docx-preview.mjs", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/docx-preview/docx-preview.mjs.map", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/loading.gif", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/office.css", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/PPTXjs/css/nv.d3.min.css", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/PPTXjs/css/pptxjs.css", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/PPTXjs/js/divs2slides.css", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/PPTXjs/js/divs2slides.js", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/PPTXjs/js/filereader.js", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/PPTXjs/js/pptxjs.js", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/PPTXjs/~$Sample_11.pptx", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/PPTXjs/~$Sample_12.pptx", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/PPTXjs/~$Sample_video.pptx", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/css/sheetjs.css", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/languages/all.js", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/languages/de-CH.js", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/languages/de-DE.js", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/languages/en-US.js", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/languages/index.js", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/languages/ja-JP.js", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/languages/pl-PL.js", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/languages/pt-BR.js", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/languages/ru-RU.js", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/js/dropsheet.js", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/js/main.js", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/js/sheetjsw.js", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/js/shim.js", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/js/xhr-hack.js", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/handsontable.full.min.css", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/index.html", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/docx/jszip-utils.js", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/AUTHORS.txt", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/images/ui-icons_444444_256x240.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/images/ui-icons_555555_256x240.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/images/ui-icons_777620_256x240.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/images/ui-icons_777777_256x240.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/images/ui-icons_cc0000_256x240.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/images/ui-icons_ffffff_256x240.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/jquery-ui.css", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/jquery-ui.js", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/jquery-ui.min.css", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/themes/start/images/ui-bg_glass_45_0078ae_1x400.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/themes/start/images/ui-bg_glass_55_f8da4e_1x400.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/themes/start/images/ui-bg_glass_75_79c9ec_1x400.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/themes/start/images/ui-bg_gloss-wave_45_e14f1c_500x100.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/themes/start/images/ui-bg_gloss-wave_50_6eac2c_500x100.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/themes/start/images/ui-bg_gloss-wave_75_2191c0_500x100.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/themes/start/images/ui-bg_inset-hard_100_fcfdfd_1x100.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/themes/start/images/ui-icons_0078ae_256x240.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/themes/start/images/ui-icons_056b93_256x240.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/themes/start/images/ui-icons_d8e7f3_256x240.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/themes/start/images/ui-icons_e0fdff_256x240.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/themes/start/images/ui-icons_f5e175_256x240.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/themes/start/images/ui-icons_f7a50d_256x240.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/themes/start/images/ui-icons_fcd113_256x240.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/themes/start/jquery-ui.css", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/themes/start/jquery-ui.min.css", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/themes/start/theme.css", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/officeToHtml/officeToHtml.css", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/officeToHtml/officeToHtml.js", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/debugger.js", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/annotation-check.svg", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/annotation-comment.svg", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/annotation-help.svg", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/annotation-insert.svg", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/annotation-key.svg", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/annotation-newparagraph.svg", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/annotation-noicon.svg", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/annotation-note.svg", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/annotation-paragraph.svg", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/findbarButton-next-rtl.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/findbarButton-next.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/findbarButton-previous-rtl.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/findbarButton-previous.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/grab.cur", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/grabbing.cur", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/loading-icon.gif", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/loading-small.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/secondaryToolbarButton-documentProperties.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/secondaryToolbarButton-firstPage.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/secondaryToolbarButton-handTool.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/secondaryToolbarButton-lastPage.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/secondaryToolbarButton-rotateCcw.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/secondaryToolbarButton-rotateCw.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/secondaryToolbarButton-selectTool.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/shadow.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/texture.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/toolbarButton-bookmark.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/toolbarButton-download.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/toolbarButton-menuArrows.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/toolbarButton-openFile.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/toolbarButton-pageDown-rtl.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/toolbarButton-pageDown.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/toolbarButton-pageUp-rtl.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/toolbarButton-pageUp.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/toolbarButton-presentationMode.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/toolbarButton-print.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/toolbarButton-search.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/toolbarButton-secondaryToolbarToggle-rtl.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/toolbarButton-secondaryToolbarToggle.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/toolbarButton-sidebarToggle-rtl.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/toolbarButton-sidebarToggle.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/toolbarButton-viewAttachments.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/toolbarButton-viewOutline-rtl.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/toolbarButton-viewOutline.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/toolbarButton-viewThumbnail.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/toolbarButton-zoomIn.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/toolbarButton-zoomOut.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/treeitem-collapsed-rtl.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/treeitem-collapsed.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/treeitem-expanded.png", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/images/<EMAIL>", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/ach/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/af/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/ak/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/an/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/ar/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/as/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/ast/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/az/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/be/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/bg/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/bn-BD/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/bn-IN/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/br/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/bs/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/ca/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/cs/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/csb/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/cy/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/da/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/de/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/el/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/en-GB/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/en-US/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/en-ZA/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/eo/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/es-AR/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/es-CL/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/es-ES/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/es-MX/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/et/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/eu/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/fa/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/ff/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/fi/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/fr/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/fy-NL/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/ga-IE/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/gd/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/gl/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/gu-IN/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/he/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/hi-IN/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/hr/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/hu/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/hy-AM/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/id/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/is/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/it/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/ja/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/ka/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/kk/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/km/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/kn/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/ko/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/ku/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/lg/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/lij/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/locale.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/lt/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/lv/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/mai/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/mk/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/ml/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/mn/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/mr/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/ms/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/my/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/nb-NO/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/nl/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/nn-NO/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/nso/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/oc/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/or/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/pa-IN/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/pl/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/pt-BR/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/pt-PT/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/rm/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/ro/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/ru/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/rw/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/sah/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/si/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/sk/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/sl/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/son/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/sq/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/sr/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/sv-SE/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/sw/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/ta/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/ta-LK/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/te/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/th/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/tl/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/tn/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/tr/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/uk/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/ur/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/vi/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/wo/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/xh/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/zh-CN/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/zh-TW/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/locale/zu/viewer.properties", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/pdf.js", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/pdf.js.map", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/pdf.viewer.css", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/verySimpleImageViewer/css/jquery.verySimpleImageViewer.css", "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/verySimpleImageViewer/js/jquery.verySimpleImageViewer.js", "downloaded_repos/vish4395_laravel-file-viewer/resources/views/docstyledef.blade.php", "downloaded_repos/vish4395_laravel-file-viewer/resources/views/layouts/blank_app_no_logo.blade.php", "downloaded_repos/vish4395_laravel-file-viewer/resources/views/previewFileAudio.blade.php", "downloaded_repos/vish4395_laravel-file-viewer/resources/views/previewFileDetails.blade.php", "downloaded_repos/vish4395_laravel-file-viewer/resources/views/previewFileDocxjs.blade.php", "downloaded_repos/vish4395_laravel-file-viewer/resources/views/previewFileGoogle.blade.php", "downloaded_repos/vish4395_laravel-file-viewer/resources/views/previewFileImage.blade.php", "downloaded_repos/vish4395_laravel-file-viewer/resources/views/previewFileOffice.blade.php", "downloaded_repos/vish4395_laravel-file-viewer/resources/views/previewFileVideo.blade.php", "downloaded_repos/vish4395_laravel-file-viewer/src/LaravelFileViewer.php", "downloaded_repos/vish4395_laravel-file-viewer/src/LaravelFileViewerFacade.php", "downloaded_repos/vish4395_laravel-file-viewer/src/LaravelFileViewerServiceProvider.php"], "skipped": [{"path": "downloaded_repos/vish4395_laravel-file-viewer/laravel-file-viewer.png", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/docx-preview/docx-preview.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/docx-preview/docx-preview.min.mjs", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/PPTXjs/js/d3.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/PPTXjs/js/divs2slides.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/PPTXjs/js/jszip.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/PPTXjs/js/nv.d3.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/PPTXjs/js/pptxjs.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/PPTXjs/js/pptxjs.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/dist/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/dist/handsontable.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/dist/handsontable.css.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/dist/handsontable.full.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/dist/handsontable.full.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/dist/handsontable.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/dist/handsontable.js.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/dist/handsontable.min.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/dist/handsontable.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/dist/languages/all.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/dist/languages/all.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/dist/languages/de-CH.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/dist/languages/de-CH.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/dist/languages/de-DE.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/dist/languages/de-DE.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/dist/languages/en-US.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/dist/languages/en-US.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/dist/languages/ja-JP.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/dist/languages/ja-JP.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/dist/languages/pl-PL.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/dist/languages/pl-PL.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/dist/languages/pt-BR.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/dist/languages/pt-BR.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/dist/languages/ru-RU.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/dist/languages/ru-RU.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/dist/pikaday/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/dist/pikaday/pikaday.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/dist/pikaday/pikaday.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/js/xlsx.full.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/handsontable.full.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/xlsx.full.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/docx/mammoth.browser.js", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/docx/mammoth.browser.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery/jquery-1.12.4.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery/jquery-3.3.1.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery/jquery-migrate-1.4.1.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery/jquery.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/jquery-ui.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/jquery-ui.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/officeToHtml/officeToHtml.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/pdf.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/pdf.worker.js", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/pdf.worker.js.map", "reason": "exceeded_size_limit"}]}, "time": {"rules": [], "rules_parse_time": 1.8330209255218506, "profiling_times": {"config_time": 5.787886142730713, "core_time": 48.34971046447754, "ignores_time": 0.0019404888153076172, "total_time": 54.14097023010254}, "parsing_time": {"total_time": 6.967000722885132, "per_file_time": {"mean": 0.17864104417654184, "std_dev": 0.12061771540614122}, "very_slow_stats": {"time_ratio": 0.7364901086072315, "count_ratio": 0.15384615384615385}, "very_slow_files": [{"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/languages/all.js", "ftime": 0.3173208236694336}, {"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/verySimpleImageViewer/js/jquery.verySimpleImageViewer.js", "ftime": 0.38137388229370117}, {"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/SheetJS/del/handsontable/languages/index.js", "ftime": 0.5049140453338623}, {"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/docx-preview/docx-preview.min.mjs", "ftime": 1.1742820739746094}, {"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/docx-preview/docx-preview.js", "ftime": 1.3040721416473389}, {"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/docx-preview/docx-preview.mjs", "ftime": 1.4491641521453857}]}, "scanning_time": {"total_time": 118.38277769088745, "per_file_time": {"mean": 0.19502928779388393, "std_dev": 4.289697896108213}, "very_slow_stats": {"time_ratio": 0.9143641583895357, "count_ratio": 0.018121911037891267}, "very_slow_files": [{"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/PPTXjs/js/divs2slides.js", "ftime": 1.9611058235168457}, {"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/debugger.js", "ftime": 1.9824199676513672}, {"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/verySimpleImageViewer/js/jquery.verySimpleImageViewer.js", "ftime": 2.4626028537750244}, {"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/pdf/pdf.js", "ftime": 5.414335012435913}, {"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/jquery_ui/jquery-ui.js", "ftime": 5.450253009796143}, {"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/docx-preview/docx-preview.min.mjs", "ftime": 7.935503005981445}, {"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/docx-preview/docx-preview.mjs", "ftime": 7.936305046081543}, {"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/docx-preview/docx-preview.js", "ftime": 11.81997299194336}, {"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/PPTXjs/js/pptxjs.js", "ftime": 16.89557909965515}, {"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/officeToHtml/officeToHtml.js", "ftime": 44.68241500854492}]}, "matching_time": {"total_time": 35.43305850028992, "per_file_and_rule_time": {"mean": 0.13074929335900343, "std_dev": 0.12280313212244805}, "very_slow_stats": {"time_ratio": 0.8862572933184725, "count_ratio": 0.22878228782287824}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/officeToHtml/officeToHtml.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 1.2058460712432861}, {"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/docx-preview/docx-preview.min.mjs", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 1.216567039489746}, {"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/docx-preview/docx-preview.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 1.3156800270080566}, {"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/officeToHtml/officeToHtml.js", "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 1.3654398918151855}, {"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/officeToHtml/officeToHtml.js", "rule_id": "javascript.aws-lambda.security.tainted-eval.tainted-eval", "time": 1.3940200805664062}, {"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/officeToHtml/officeToHtml.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 1.6344890594482422}, {"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/officeToHtml/officeToHtml.js", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 1.7571299076080322}, {"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/docx-preview/docx-preview.js", "rule_id": "javascript.aws-lambda.security.tainted-eval.tainted-eval", "time": 2.0131959915161133}, {"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/docx-preview/docx-preview.js", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "time": 2.4091720581054688}, {"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/officeToHtml/officeToHtml.js", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "time": 2.621767044067383}]}, "tainting_time": {"total_time": 32.71277070045471, "per_def_and_rule_time": {"mean": 0.0017238114928837386, "std_dev": 0.0015931603764799158}, "very_slow_stats": {"time_ratio": 0.6091802473636516, "count_ratio": 0.0018443378827001106}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/officeToHtml/officeToHtml.js", "fline": 204, "rule_id": "javascript.lang.security.detect-eval-with-expression.detect-eval-with-expression", "time": 0.47088003158569336}, {"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/officeToHtml/officeToHtml.js", "fline": 204, "rule_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "time": 0.5250849723815918}, {"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/officeToHtml/officeToHtml.js", "fline": 204, "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "time": 0.556251049041748}, {"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/officeToHtml/officeToHtml.js", "fline": 204, "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 0.8654310703277588}, {"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/officeToHtml/officeToHtml.js", "fline": 204, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 1.2543110847473145}, {"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/officeToHtml/officeToHtml.js", "fline": 204, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 1.8648860454559326}, {"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/officeToHtml/officeToHtml.js", "fline": 204, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 2.1318910121917725}, {"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/officeToHtml/officeToHtml.js", "fline": 204, "rule_id": "javascript.lang.security.audit.sqli.node-postgres-sqli.node-postgres-sqli", "time": 2.209902048110962}, {"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/officeToHtml/officeToHtml.js", "fline": 204, "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 2.3353309631347656}, {"fpath": "downloaded_repos/vish4395_laravel-file-viewer/resources/assets/officetohtml/officeToHtml/officeToHtml.js", "fline": 204, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 2.698901891708374}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}