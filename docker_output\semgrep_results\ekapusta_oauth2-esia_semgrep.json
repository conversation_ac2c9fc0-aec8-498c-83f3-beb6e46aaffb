{"version": "1.130.0", "results": [{"check_id": "generic.secrets.security.detected-private-key.detected-private-key", "path": "downloaded_repos/ekapusta_oauth2-esia/resources/another.gost.test.key", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 65, "offset": 92}, "extra": {"message": "Private Key detected. This is a sensitive credential and should not be hardcoded here. Instead, store this in a separate, private file.", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "source-rule-url": "https://github.com/grab/secret-scanner/blob/master/scanner/signatures/pattern.go", "category": "security", "technology": ["secrets"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-private-key.detected-private-key", "shortlink": "https://sg.run/b7dr"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-private-key.detected-private-key", "path": "downloaded_repos/ekapusta_oauth2-esia/resources/another.rsa.test.key", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 65, "offset": 92}, "extra": {"message": "Private Key detected. This is a sensitive credential and should not be hardcoded here. Instead, store this in a separate, private file.", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "source-rule-url": "https://github.com/grab/secret-scanner/blob/master/scanner/signatures/pattern.go", "category": "security", "technology": ["secrets"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-private-key.detected-private-key", "shortlink": "https://sg.run/b7dr"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-private-key.detected-private-key", "path": "downloaded_repos/ekapusta_oauth2-esia/resources/ekapusta.gost.test.key", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 65, "offset": 92}, "extra": {"message": "Private Key detected. This is a sensitive credential and should not be hardcoded here. Instead, store this in a separate, private file.", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "source-rule-url": "https://github.com/grab/secret-scanner/blob/master/scanner/signatures/pattern.go", "category": "security", "technology": ["secrets"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-private-key.detected-private-key", "shortlink": "https://sg.run/b7dr"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-private-key.detected-private-key", "path": "downloaded_repos/ekapusta_oauth2-esia/resources/ekapusta.gost2012.test.key", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 65, "offset": 92}, "extra": {"message": "Private Key detected. This is a sensitive credential and should not be hardcoded here. Instead, store this in a separate, private file.", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "source-rule-url": "https://github.com/grab/secret-scanner/blob/master/scanner/signatures/pattern.go", "category": "security", "technology": ["secrets"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-private-key.detected-private-key", "shortlink": "https://sg.run/b7dr"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-private-key.detected-private-key", "path": "downloaded_repos/ekapusta_oauth2-esia/resources/ekapusta.rsa.test.key", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 60, "offset": 87}, "extra": {"message": "Private Key detected. This is a sensitive credential and should not be hardcoded here. Instead, store this in a separate, private file.", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "source-rule-url": "https://github.com/grab/secret-scanner/blob/master/scanner/signatures/pattern.go", "category": "security", "technology": ["secrets"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-private-key.detected-private-key", "shortlink": "https://sg.run/b7dr"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/ekapusta_oauth2-esia/src/Security/Signer/OpensslPkcs7.php", "start": {"line": 53, "col": 9, "offset": 1648}, "end": {"line": 53, "col": 26, "offset": 1665}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/ekapusta_oauth2-esia/src/Security/Signer/OpensslPkcs7.php", "start": {"line": 54, "col": 9, "offset": 1675}, "end": {"line": 54, "col": 29, "offset": 1695}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/ekapusta_oauth2-esia/src/Transport/Process.php", "start": {"line": 20, "col": 9, "offset": 311}, "end": {"line": 24, "col": 20, "offset": 473}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/ekapusta_oauth2-esia/.gitattributes", "downloaded_repos/ekapusta_oauth2-esia/.gitignore", "downloaded_repos/ekapusta_oauth2-esia/.php_cs.dist", "downloaded_repos/ekapusta_oauth2-esia/.travis.yml", "downloaded_repos/ekapusta_oauth2-esia/LICENSE.md", "downloaded_repos/ekapusta_oauth2-esia/README.md", "downloaded_repos/ekapusta_oauth2-esia/composer.json", "downloaded_repos/ekapusta_oauth2-esia/phpunit.xml.dist", "downloaded_repos/ekapusta_oauth2-esia/resources/README.md", "downloaded_repos/ekapusta_oauth2-esia/resources/another.gost.test.cer", "downloaded_repos/ekapusta_oauth2-esia/resources/another.gost.test.key", "downloaded_repos/ekapusta_oauth2-esia/resources/another.gost.test.public.key", "downloaded_repos/ekapusta_oauth2-esia/resources/another.rsa.test.cer", "downloaded_repos/ekapusta_oauth2-esia/resources/another.rsa.test.key", "downloaded_repos/ekapusta_oauth2-esia/resources/another.rsa.test.public.key", "downloaded_repos/ekapusta_oauth2-esia/resources/ekapusta.gost.test.cer", "downloaded_repos/ekapusta_oauth2-esia/resources/ekapusta.gost.test.key", "downloaded_repos/ekapusta_oauth2-esia/resources/ekapusta.gost.test.public.key", "downloaded_repos/ekapusta_oauth2-esia/resources/ekapusta.gost2012.test.cer", "downloaded_repos/ekapusta_oauth2-esia/resources/ekapusta.gost2012.test.key", "downloaded_repos/ekapusta_oauth2-esia/resources/ekapusta.gost2012.test.public.key", "downloaded_repos/ekapusta_oauth2-esia/resources/ekapusta.rsa.test.cer", "downloaded_repos/ekapusta_oauth2-esia/resources/ekapusta.rsa.test.key", "downloaded_repos/ekapusta_oauth2-esia/resources/ekapusta.rsa.test.public.key", "downloaded_repos/ekapusta_oauth2-esia/resources/esia.gost.prod.cer", "downloaded_repos/ekapusta_oauth2-esia/resources/esia.gost.prod.public.key", "downloaded_repos/ekapusta_oauth2-esia/resources/esia.gost.test.cer", "downloaded_repos/ekapusta_oauth2-esia/resources/esia.gost.test.public.key", "downloaded_repos/ekapusta_oauth2-esia/resources/esia.prod.cer", "downloaded_repos/ekapusta_oauth2-esia/resources/esia.prod.public.key", "downloaded_repos/ekapusta_oauth2-esia/resources/esia.test.cer", "downloaded_repos/ekapusta_oauth2-esia/resources/esia.test.public.key", "downloaded_repos/ekapusta_oauth2-esia/src/EsiaService.php", "downloaded_repos/ekapusta_oauth2-esia/src/Interfaces/EsiaServiceInterface.php", "downloaded_repos/ekapusta_oauth2-esia/src/Interfaces/Provider/ProviderInterface.php", "downloaded_repos/ekapusta_oauth2-esia/src/Interfaces/Security/SignerInterface.php", "downloaded_repos/ekapusta_oauth2-esia/src/Interfaces/Token/ScopedTokenInterface.php", "downloaded_repos/ekapusta_oauth2-esia/src/Provider/EsiaProvider.php", "downloaded_repos/ekapusta_oauth2-esia/src/Security/JWTSigner/OpenSslCliJwtSigner.php", "downloaded_repos/ekapusta_oauth2-esia/src/Security/JWTSigner/TmpFile.php", "downloaded_repos/ekapusta_oauth2-esia/src/Security/Signer/Exception/SignException.php", "downloaded_repos/ekapusta_oauth2-esia/src/Security/Signer/OpensslCli.php", "downloaded_repos/ekapusta_oauth2-esia/src/Security/Signer/OpensslPkcs7.php", "downloaded_repos/ekapusta_oauth2-esia/src/Security/Signer.php", "downloaded_repos/ekapusta_oauth2-esia/src/Token/EsiaAccessToken.php", "downloaded_repos/ekapusta_oauth2-esia/src/Transport/Process.php"], "skipped": [{"path": "downloaded_repos/ekapusta_oauth2-esia/tests/AuthenticationBot/authentication-bot.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ekapusta_oauth2-esia/tests/AuthenticationBot/package-lock.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ekapusta_oauth2-esia/tests/AuthenticationBot/package.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ekapusta_oauth2-esia/tests/AuthenticationBot.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ekapusta_oauth2-esia/tests/EsiaServiceTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ekapusta_oauth2-esia/tests/Factory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ekapusta_oauth2-esia/tests/Fixtures/expired.token.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ekapusta_oauth2-esia/tests/Provider/EsiaProviderTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ekapusta_oauth2-esia/tests/Security/Signer/OpensslCliTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ekapusta_oauth2-esia/tests/Security/Signer/OpensslPkcs7Test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ekapusta_oauth2-esia/tests/Security/SignerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ekapusta_oauth2-esia/tests/Token/EsiaAccessTokenTest.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.9638230800628662, "profiling_times": {"config_time": 5.888826131820679, "core_time": 2.7037062644958496, "ignores_time": 0.001984834671020508, "total_time": 8.595275640487671}, "parsing_time": {"total_time": 0.2185347080230713, "per_file_time": {"mean": 0.013658419251441954, "std_dev": 2.5412182281803197e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.5728569030761719, "per_file_time": {"mean": 0.005304230584038627, "std_dev": 9.463216432332774e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.09774661064147949, "per_file_and_rule_time": {"mean": 0.0019166002086564607, "std_dev": 7.011777102825735e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.004588127136230469, "per_def_and_rule_time": {"mean": 0.0004588127136230469, "std_dev": 4.594104689203959e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}