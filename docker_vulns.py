#!/usr/bin/env python3

import subprocess
import csv
import datetime

# CONFIG
SEARCH_TERMS = ["Web server"]
MIN_STARS = 1000
MAX_STARS = 10000
INCLUDE_OFFICIAL = False
REQUIRE_AUTOMATED = False
KEYWORDS_IN_DESCRIPTION = []
SORT_BY = "pull_count"  # "stars" or "name"
SORT_ORDER = "desc"  # "asc" or "desc"
OUTPUT_CSV = "docker_search_final_results.csv"

def run_docker_search(term):
    cmd = ["docker", "search", term, "--no-trunc"]
    try:
        output = subprocess.check_output(cmd, text=True)
        return output.strip().split("\n")[1:]  # skip header
    except subprocess.CalledProcessError as e:
        print(f"[!] Error running search for '{term}': {e}")
        return []

def parse_table(lines):
    results = []
    for line in lines:
        parts = [p.strip() for p in line.split("  ") if p.strip()]
        if len(parts) < 5:
            continue
        name, desc, stars, official, automated = parts[:5]
        try:
            stars = int(stars)
        except ValueError:
            continue

        # Filters
        if stars < MIN_STARS or stars > MAX_STARS:
            continue
        if INCLUDE_OFFICIAL and official != "[OK]":
            continue
        if REQUIRE_AUTOMATED and automated != "[OK]":
            continue
        if KEYWORDS_IN_DESCRIPTION and not any(kw.lower() in desc.lower() for kw in KEYWORDS_IN_DESCRIPTION):
            continue

        results.append({
            "Name": name,
            "Description": desc,
            "Stars": stars,
            "Official": official,
            "Automated": automated,
            "LastChecked": datetime.datetime.now().isoformat()
        })
    return results

def save_to_csv(data):
    with open(OUTPUT_CSV, mode="w", newline="", encoding="utf-8") as f:
        writer = csv.DictWriter(f, fieldnames=["Name", "Description", "Stars", "Official", "Automated", "LastChecked"])
        writer.writeheader()
        for row in data:
            writer.writerow(row)
    print(f"[+] Saved {len(data)} images to {OUTPUT_CSV}")

def sort_results(results):
    reverse = SORT_ORDER.lower() == "desc"
    if SORT_BY == "stars":
        return sorted(results, key=lambda x: x["Stars"], reverse=reverse)
    elif SORT_BY == "name":
        return sorted(results, key=lambda x: x["Name"].lower(), reverse=reverse)
    else:
        return results

def main():
    all_results = []
    for term in SEARCH_TERMS:
        print(f"[+] Searching Docker for: {term}")
        lines = run_docker_search(term)
        parsed = parse_table(lines)
        all_results.extend(parsed)

    sorted_results = sort_results(all_results)
    save_to_csv(sorted_results)

if __name__ == "__main__":
    main()
