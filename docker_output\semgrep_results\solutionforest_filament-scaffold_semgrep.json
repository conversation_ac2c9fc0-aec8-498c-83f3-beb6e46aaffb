{"version": "1.130.0", "results": [{"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/solutionforest_filament-scaffold/bin/build.js", "start": {"line": 37, "col": 33, "offset": 1003}, "end": {"line": 37, "col": 126, "offset": 1096}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/solutionforest_filament-scaffold/.editorconfig", "downloaded_repos/solutionforest_filament-scaffold/.gitattributes", "downloaded_repos/solutionforest_filament-scaffold/.github/CONTRIBUTING.md", "downloaded_repos/solutionforest_filament-scaffold/.github/FUNDING.yml", "downloaded_repos/solutionforest_filament-scaffold/.github/ISSUE_TEMPLATE/bug.yml", "downloaded_repos/solutionforest_filament-scaffold/.github/ISSUE_TEMPLATE/config.yml", "downloaded_repos/solutionforest_filament-scaffold/.github/SECURITY.md", "downloaded_repos/solutionforest_filament-scaffold/.github/dependabot.yml", "downloaded_repos/solutionforest_filament-scaffold/.github/workflows/dependabot-auto-merge.yml", "downloaded_repos/solutionforest_filament-scaffold/.github/workflows/fix-php-code-style-issues.yml", "downloaded_repos/solutionforest_filament-scaffold/.github/workflows/phpstan.yml", "downloaded_repos/solutionforest_filament-scaffold/.github/workflows/update-changelog.yml", "downloaded_repos/solutionforest_filament-scaffold/.gitignore", "downloaded_repos/solutionforest_filament-scaffold/.prettierrc", "downloaded_repos/solutionforest_filament-scaffold/CHANGELOG.md", "downloaded_repos/solutionforest_filament-scaffold/LICENSE.md", "downloaded_repos/solutionforest_filament-scaffold/README.md", "downloaded_repos/solutionforest_filament-scaffold/bin/build.js", "downloaded_repos/solutionforest_filament-scaffold/composer.json", "downloaded_repos/solutionforest_filament-scaffold/config/filament-scaffold.php", "downloaded_repos/solutionforest_filament-scaffold/package.json", "downloaded_repos/solutionforest_filament-scaffold/phpstan-baseline.neon", "downloaded_repos/solutionforest_filament-scaffold/phpstan.neon.dist", "downloaded_repos/solutionforest_filament-scaffold/phpunit.xml.dist", "downloaded_repos/solutionforest_filament-scaffold/pint.json", "downloaded_repos/solutionforest_filament-scaffold/postcss.config.cjs", "downloaded_repos/solutionforest_filament-scaffold/src/FilamentScaffold.php", "downloaded_repos/solutionforest_filament-scaffold/src/FilamentScaffoldPlugin.php", "downloaded_repos/solutionforest_filament-scaffold/src/FilamentScaffoldServiceProvider.php", "downloaded_repos/solutionforest_filament-scaffold/src/Models/Scaffold.php", "downloaded_repos/solutionforest_filament-scaffold/src/Resources/ScaffoldResource/Pages/CreateScaffold.php", "downloaded_repos/solutionforest_filament-scaffold/src/Resources/ScaffoldResource.php", "downloaded_repos/solutionforest_filament-scaffold/src/Testing/TestsFilamentScaffold.php", "downloaded_repos/solutionforest_filament-scaffold/stubs/.gitkeep", "downloaded_repos/solutionforest_filament-scaffold/tailwind.config.js"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.6303820610046387, "profiling_times": {"config_time": 5.968705654144287, "core_time": 2.3892006874084473, "ignores_time": 0.002031087875366211, "total_time": 8.36074423789978}, "parsing_time": {"total_time": 0.2736232280731201, "per_file_time": {"mean": 0.012437419457869098, "std_dev": 0.00015658516820987045}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.7638180255889893, "per_file_time": {"mean": 0.00830236984335858, "std_dev": 0.0004935903253380751}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.22662019729614258, "per_file_and_rule_time": {"mean": 0.0008716161434467024, "std_dev": 1.881531940517823e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.017294645309448242, "per_def_and_rule_time": {"mean": 0.00043236613273620614, "std_dev": 6.811559188335536e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1085719040}, "engine_requested": "OSS", "skipped_rules": []}