# Unified Vulnerability Scanner Docker Image
FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    wget \
    docker.io \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt /tmp/
RUN pip install --no-cache-dir -r /tmp/requirements.txt

# Install Semgrep
RUN pip install semgrep

# Create working directory
WORKDIR /app

# Copy scanner files
COPY unified_vulnerability_scanner.py /app/
COPY advanced_scanner.py /app/
COPY cve_trend_scanner.py /app/
COPY vulnerability_pattern_scanner.py /app/

# Create output directories
RUN mkdir -p /app/output /app/semgrep_results /app/downloaded_repos

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV GITHUB_TOKEN=""

# Create entrypoint script
RUN echo '#!/bin/bash\n\
if [ -z "$GITHUB_TOKEN" ]; then\n\
    echo "❌ GITHUB_TOKEN environment variable is required"\n\
    exit 1\n\
fi\n\
\n\
echo "🎯 Starting Unified Vulnerability Scanner"\n\
echo "GitHub Token: ${GITHUB_TOKEN:0:8}..."\n\
echo "Arguments: $@"\n\
echo ""\n\
\n\
# Run the scanner with provided arguments\n\
python unified_vulnerability_scanner.py "$@"\n\
\n\
# Copy results to output volume\n\
cp *.csv /app/output/ 2>/dev/null || true\n\
cp *.json /app/output/ 2>/dev/null || true\n\
cp *.txt /app/output/ 2>/dev/null || true\n\
cp -r semgrep_results /app/output/ 2>/dev/null || true\n\
\n\
echo ""\n\
echo "✅ Scan complete. Results saved to /app/output/"\n\
ls -la /app/output/\n\
' > /app/entrypoint.sh && chmod +x /app/entrypoint.sh

# Expose volume for results
VOLUME ["/app/output"]

# Set entrypoint
ENTRYPOINT ["/app/entrypoint.sh"]

# Default command
CMD ["--max-repos", "25", "--pages", "2"]
