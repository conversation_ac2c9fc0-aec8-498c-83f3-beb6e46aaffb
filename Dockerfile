# Unified Vulnerability Scanner Docker Image - All-in-One Container
FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    wget \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt /tmp/
RUN pip install --no-cache-dir -r /tmp/requirements.txt

# Install Semgrep
RUN pip install semgrep

# Create working directory and subdirectories
WORKDIR /app
RUN mkdir -p /app/output /app/semgrep_results /app/downloaded_repos /app/logs

# Copy all scanner files
COPY unified_vulnerability_scanner.py /app/
COPY advanced_scanner.py /app/
COPY cve_trend_scanner.py /app/
COPY vulnerability_pattern_scanner.py /app/
COPY test_improved_scanner.py /app/
COPY run_improved_scanner.py /app/

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV GITHUB_TOKEN=""
ENV SEMGREP_IN_DOCKER=1

# Create comprehensive entrypoint script
RUN echo '#!/bin/bash\n\
set -e\n\
\n\
# Color codes for output\n\
RED="\\033[0;31m"\n\
GREEN="\\033[0;32m"\n\
YELLOW="\\033[1;33m"\n\
BLUE="\\033[0;34m"\n\
NC="\\033[0m" # No Color\n\
\n\
echo -e "${BLUE}🎯 UNIFIED VULNERABILITY SCANNER - DOCKER CONTAINER${NC}"\n\
echo "================================================================"\n\
echo "Container-based vulnerability scanning with Semgrep integration"\n\
echo ""\n\
\n\
# Check GitHub token\n\
if [ -z "$GITHUB_TOKEN" ]; then\n\
    echo -e "${RED}❌ GITHUB_TOKEN environment variable is required${NC}"\n\
    echo "Set it with: docker run -e GITHUB_TOKEN=your_token ..."\n\
    exit 1\n\
fi\n\
\n\
echo -e "${GREEN}✅ GitHub Token: ${GITHUB_TOKEN:0:8}...${NC}"\n\
\n\
# Check Semgrep installation\n\
if command -v semgrep >/dev/null 2>&1; then\n\
    SEMGREP_VERSION=$(semgrep --version 2>/dev/null | head -n1 || echo "unknown")\n\
    echo -e "${GREEN}✅ Semgrep installed: $SEMGREP_VERSION${NC}"\n\
else\n\
    echo -e "${RED}❌ Semgrep not found${NC}"\n\
    exit 1\n\
fi\n\
\n\
# Show container info\n\
echo -e "${BLUE}📋 Container Information:${NC}"\n\
echo "  Working Directory: $(pwd)"\n\
echo "  Python Version: $(python --version)"\n\
echo "  Arguments: $@"\n\
echo ""\n\
\n\
# Create log file\n\
LOG_FILE="/app/logs/scanner_$(date +%Y%m%d_%H%M%S).log"\n\
echo -e "${BLUE}📝 Logging to: $LOG_FILE${NC}"\n\
echo ""\n\
\n\
# Run the scanner with logging\n\
echo -e "${GREEN}🚀 Starting vulnerability scan...${NC}"\n\
python unified_vulnerability_scanner.py --no-docker "$@" 2>&1 | tee "$LOG_FILE"\n\
SCANNER_EXIT_CODE=${PIPESTATUS[0]}\n\
\n\
# Copy results to output volume\n\
echo ""\n\
echo -e "${BLUE}📁 Copying results to output directory...${NC}"\n\
cp *.csv /app/output/ 2>/dev/null || echo "No CSV files to copy"\n\
cp *.json /app/output/ 2>/dev/null || echo "No JSON files to copy"\n\
cp *.txt /app/output/ 2>/dev/null || echo "No TXT files to copy"\n\
cp -r semgrep_results /app/output/ 2>/dev/null || echo "No Semgrep results to copy"\n\
cp -r logs /app/output/ 2>/dev/null || echo "No logs to copy"\n\
\n\
# Show results summary\n\
echo ""\n\
echo -e "${GREEN}✅ Scan completed with exit code: $SCANNER_EXIT_CODE${NC}"\n\
echo -e "${BLUE}📊 Results Summary:${NC}"\n\
\n\
if [ -f "/app/output/unified_vulnerability_results.csv" ]; then\n\
    RESULT_COUNT=$(tail -n +2 "/app/output/unified_vulnerability_results.csv" | wc -l)\n\
    echo -e "${GREEN}  📄 Top repositories found: $RESULT_COUNT${NC}"\n\
else\n\
    echo -e "${YELLOW}  ⚠️ No CSV results file found${NC}"\n\
fi\n\
\n\
if [ -d "/app/output/semgrep_results" ]; then\n\
    SEMGREP_COUNT=$(find /app/output/semgrep_results -name "*.json" | wc -l)\n\
    echo -e "${GREEN}  🔍 Semgrep analysis files: $SEMGREP_COUNT${NC}"\n\
fi\n\
\n\
echo ""\n\
echo -e "${BLUE}📁 Output files:${NC}"\n\
ls -la /app/output/ 2>/dev/null || echo "No output files"\n\
\n\
echo ""\n\
echo -e "${GREEN}🎯 Scan complete! Check /app/output/ for results.${NC}"\n\
\n\
exit $SCANNER_EXIT_CODE\n\
' > /app/entrypoint.sh && chmod +x /app/entrypoint.sh

# Expose volume for results
VOLUME ["/app/output"]

# Set entrypoint
ENTRYPOINT ["/app/entrypoint.sh"]

# Default command - scan 50 repos, keep top 10
CMD ["--max-repos", "50", "--pages", "3", "--concurrent", "1"]
