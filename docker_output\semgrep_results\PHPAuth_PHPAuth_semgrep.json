{"version": "1.130.0", "results": [{"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/PHPAuth_PHPAuth/files/import.php", "start": {"line": 78, "col": 50, "offset": 1843}, "end": {"line": 78, "col": 80, "offset": 1873}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "path": "downloaded_repos/PHPAuth_PHPAuth/sources/Auth.php", "start": {"line": 612, "col": 13, "offset": 16934}, "end": {"line": 612, "col": 64, "offset": 16985}, "extra": {"message": "Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues", "metadata": {"cwe": ["CWE-697: Incorrect Comparison"], "references": ["https://www.php.net/manual/en/types.comparisons.php", "https://web.archive.org/web/20210430183236/https://www.whitehatsec.com/blog/magic-hashes/"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.md5-loose-equality.md5-loose-equality", "shortlink": "https://sg.run/Do4G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/PHPAuth_PHPAuth/.all-contributorsrc", "downloaded_repos/PHPAuth_PHPAuth/.editorconfig", "downloaded_repos/PHPAuth_PHPAuth/.gitattributes", "downloaded_repos/PHPAuth_PHPAuth/.github/ISSUE_TEMPLATE/bug_report.md", "downloaded_repos/PHPAuth_PHPAuth/.github/ISSUE_TEMPLATE/custom.md", "downloaded_repos/PHPAuth_PHPAuth/.github/ISSUE_TEMPLATE/feature_request.md", "downloaded_repos/PHPAuth_PHPAuth/.gitignore", "downloaded_repos/PHPAuth_PHPAuth/.travis.yml", "downloaded_repos/PHPAuth_PHPAuth/CHANGELOG.md", "downloaded_repos/PHPAuth_PHPAuth/CODE_OF_CONDUCT.md", "downloaded_repos/PHPAuth_PHPAuth/CONFIG_HINTS.md", "downloaded_repos/PHPAuth_PHPAuth/CUSTOM_VALIDATORS.md", "downloaded_repos/PHPAuth_PHPAuth/DEPRECATION_WARNING.md", "downloaded_repos/PHPAuth_PHPAuth/LICENSE", "downloaded_repos/PHPAuth_PHPAuth/README.md", "downloaded_repos/PHPAuth_PHPAuth/banner_small.png", "downloaded_repos/PHPAuth_PHPAuth/composer.json", "downloaded_repos/PHPAuth_PHPAuth/database_defs/database_mssql.sql", "downloaded_repos/PHPAuth_PHPAuth/database_defs/database_mysql.sql", "downloaded_repos/PHPAuth_PHPAuth/database_defs/database_pgsql.sql", "downloaded_repos/PHPAuth_PHPAuth/database_defs/database_translation_dictionary.sql", "downloaded_repos/PHPAuth_PHPAuth/examples/html-frontend-password-strength-gui-feedback/index.php", "downloaded_repos/PHPAuth_PHPAuth/examples/html-frontend-password-strength-gui-feedback/zxcvbn.js", "downloaded_repos/PHPAuth_PHPAuth/files/import.php", "downloaded_repos/PHPAuth_PHPAuth/files/import_langs_to_sql.php", "downloaded_repos/PHPAuth_PHPAuth/files/make_forgotten_dictionary_by_sql.php", "downloaded_repos/PHPAuth_PHPAuth/languages/ar_TN.php", "downloaded_repos/PHPAuth_PHPAuth/languages/bs_BA.php", "downloaded_repos/PHPAuth_PHPAuth/languages/cs_CZ.php", "downloaded_repos/PHPAuth_PHPAuth/languages/da_DK.php", "downloaded_repos/PHPAuth_PHPAuth/languages/de_DE.php", "downloaded_repos/PHPAuth_PHPAuth/languages/en_GB.php", "downloaded_repos/PHPAuth_PHPAuth/languages/es_MX.php", "downloaded_repos/PHPAuth_PHPAuth/languages/fa_IR.php", "downloaded_repos/PHPAuth_PHPAuth/languages/fr_FR.php", "downloaded_repos/PHPAuth_PHPAuth/languages/gr_GR.php", "downloaded_repos/PHPAuth_PHPAuth/languages/hu_HU.php", "downloaded_repos/PHPAuth_PHPAuth/languages/id_ID.php", "downloaded_repos/PHPAuth_PHPAuth/languages/it_IT.php", "downloaded_repos/PHPAuth_PHPAuth/languages/ja_JP.php", "downloaded_repos/PHPAuth_PHPAuth/languages/nl_BE.php", "downloaded_repos/PHPAuth_PHPAuth/languages/nl_NL.php", "downloaded_repos/PHPAuth_PHPAuth/languages/no_NB.php", "downloaded_repos/PHPAuth_PHPAuth/languages/pl_PL.php", "downloaded_repos/PHPAuth_PHPAuth/languages/ps_AF.php", "downloaded_repos/PHPAuth_PHPAuth/languages/pt_BR.php", "downloaded_repos/PHPAuth_PHPAuth/languages/ro_RO.php", "downloaded_repos/PHPAuth_PHPAuth/languages/ru_RU.php", "downloaded_repos/PHPAuth_PHPAuth/languages/se_SE.php", "downloaded_repos/PHPAuth_PHPAuth/languages/sk_SK.php", "downloaded_repos/PHPAuth_PHPAuth/languages/sl_SI.php", "downloaded_repos/PHPAuth_PHPAuth/languages/sr_RS.php", "downloaded_repos/PHPAuth_PHPAuth/languages/th_TH.php", "downloaded_repos/PHPAuth_PHPAuth/languages/tr_TR.php", "downloaded_repos/PHPAuth_PHPAuth/languages/uk_UA.php", "downloaded_repos/PHPAuth_PHPAuth/languages/vi_VN.php", "downloaded_repos/PHPAuth_PHPAuth/languages/zh_CN.php", "downloaded_repos/PHPAuth_PHPAuth/languages/zh_TW.php", "downloaded_repos/PHPAuth_PHPAuth/phpunit.xml", "downloaded_repos/PHPAuth_PHPAuth/sources/Auth.php", "downloaded_repos/PHPAuth_PHPAuth/sources/AuthInterface.php", "downloaded_repos/PHPAuth_PHPAuth/sources/Config.php", "downloaded_repos/PHPAuth_PHPAuth/sources/ConfigInterface.php", "downloaded_repos/PHPAuth_PHPAuth/sources/Core/Result.php", "downloaded_repos/PHPAuth_PHPAuth/sources/Helpers.php"], "skipped": [{"path": "downloaded_repos/PHPAuth_PHPAuth/files/domains.json", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/PHPAuth_PHPAuth/tests/AuthTest.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.659925937652588, "profiling_times": {"config_time": 6.056915283203125, "core_time": 9.843104600906372, "ignores_time": 0.0016138553619384766, "total_time": 15.902345418930054}, "parsing_time": {"total_time": 1.2820322513580322, "per_file_time": {"mean": 0.028489605585734056, "std_dev": 0.010365507903632836}, "very_slow_stats": {"time_ratio": 0.5403061388636126, "count_ratio": 0.022222222222222223}, "very_slow_files": [{"fpath": "downloaded_repos/PHPAuth_PHPAuth/examples/html-frontend-password-strength-gui-feedback/zxcvbn.js", "ftime": 0.6926898956298828}]}, "scanning_time": {"total_time": 11.077800512313843, "per_file_time": {"mean": 0.06330171721322193, "std_dev": 0.32805837225134654}, "very_slow_stats": {"time_ratio": 0.6793695322729937, "count_ratio": 0.005714285714285714}, "very_slow_files": [{"fpath": "downloaded_repos/PHPAuth_PHPAuth/examples/html-frontend-password-strength-gui-feedback/zxcvbn.js", "ftime": 7.525920152664185}]}, "matching_time": {"total_time": 2.7575764656066895, "per_file_and_rule_time": {"mean": 0.013997850079221767, "std_dev": 0.0010368093242453587}, "very_slow_stats": {"time_ratio": 0.4182854327737043, "count_ratio": 0.04060913705583756}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/PHPAuth_PHPAuth/examples/html-frontend-password-strength-gui-feedback/zxcvbn.js", "rule_id": "javascript.express.security.audit.express-open-redirect.express-open-redirect", "time": 0.10602593421936035}, {"fpath": "downloaded_repos/PHPAuth_PHPAuth/examples/html-frontend-password-strength-gui-feedback/zxcvbn.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.11860990524291992}, {"fpath": "downloaded_repos/PHPAuth_PHPAuth/sources/Auth.php", "rule_id": "php.lang.security.injection.tainted-callable.tainted-callable", "time": 0.12212204933166504}, {"fpath": "downloaded_repos/PHPAuth_PHPAuth/examples/html-frontend-password-strength-gui-feedback/zxcvbn.js", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "time": 0.14056706428527832}, {"fpath": "downloaded_repos/PHPAuth_PHPAuth/sources/Auth.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.1451411247253418}, {"fpath": "downloaded_repos/PHPAuth_PHPAuth/sources/Auth.php", "rule_id": "php.laravel.security.laravel-sql-injection.laravel-sql-injection", "time": 0.1577908992767334}, {"fpath": "downloaded_repos/PHPAuth_PHPAuth/examples/html-frontend-password-strength-gui-feedback/zxcvbn.js", "rule_id": "javascript.lang.security.audit.sqli.node-mysql-sqli.node-mysql-sqli", "time": 0.15988707542419434}, {"fpath": "downloaded_repos/PHPAuth_PHPAuth/examples/html-frontend-password-strength-gui-feedback/zxcvbn.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.2033100128173828}]}, "tainting_time": {"total_time": 4.304109334945679, "per_def_and_rule_time": {"mean": 0.006385918894578162, "std_dev": 0.0011463519471385378}, "very_slow_stats": {"time_ratio": 0.9356551522391916, "count_ratio": 0.04302670623145401}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/PHPAuth_PHPAuth/examples/html-frontend-password-strength-gui-feedback/zxcvbn.js", "fline": 1, "rule_id": "javascript.aws-lambda.security.pg-sqli.pg-sqli", "time": 0.10298609733581543}, {"fpath": "downloaded_repos/PHPAuth_PHPAuth/examples/html-frontend-password-strength-gui-feedback/zxcvbn.js", "fline": 1, "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.10829591751098633}, {"fpath": "downloaded_repos/PHPAuth_PHPAuth/examples/html-frontend-password-strength-gui-feedback/zxcvbn.js", "fline": 1, "rule_id": "javascript.express.security.cors-misconfiguration.cors-misconfiguration", "time": 0.10962986946105957}, {"fpath": "downloaded_repos/PHPAuth_PHPAuth/examples/html-frontend-password-strength-gui-feedback/zxcvbn.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.18577098846435547}, {"fpath": "downloaded_repos/PHPAuth_PHPAuth/examples/html-frontend-password-strength-gui-feedback/zxcvbn.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.2436671257019043}, {"fpath": "downloaded_repos/PHPAuth_PHPAuth/examples/html-frontend-password-strength-gui-feedback/zxcvbn.js", "fline": 1, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.2768220901489258}, {"fpath": "downloaded_repos/PHPAuth_PHPAuth/examples/html-frontend-password-strength-gui-feedback/zxcvbn.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.2771790027618408}, {"fpath": "downloaded_repos/PHPAuth_PHPAuth/examples/html-frontend-password-strength-gui-feedback/zxcvbn.js", "fline": 1, "rule_id": "javascript.lang.security.audit.sqli.node-postgres-sqli.node-postgres-sqli", "time": 0.2844569683074951}, {"fpath": "downloaded_repos/PHPAuth_PHPAuth/examples/html-frontend-password-strength-gui-feedback/zxcvbn.js", "fline": 1, "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 0.2879500389099121}, {"fpath": "downloaded_repos/PHPAuth_PHPAuth/examples/html-frontend-password-strength-gui-feedback/zxcvbn.js", "fline": 1, "rule_id": "javascript.lang.security.audit.sqli.node-mysql-sqli.node-mysql-sqli", "time": 0.4486231803894043}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090729792}, "engine_requested": "OSS", "skipped_rules": []}