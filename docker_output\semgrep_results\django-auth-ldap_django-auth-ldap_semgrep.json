{"version": "1.130.0", "results": [{"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/django-auth-ldap_django-auth-ldap/django_auth_ldap/backend.py", "start": {"line": 175, "col": 13, "offset": 5772}, "end": {"line": 175, "col": 70, "offset": 5829}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"Rejecting empty password for %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/django-auth-ldap_django-auth-ldap/.editorconfig", "downloaded_repos/django-auth-ldap_django-auth-ldap/.github/dependabot.yml", "downloaded_repos/django-auth-ldap_django-auth-ldap/.github/workflows/check.yml", "downloaded_repos/django-auth-ldap_django-auth-ldap/.github/workflows/docs.yml", "downloaded_repos/django-auth-ldap_django-auth-ldap/.github/workflows/release.yml", "downloaded_repos/django-auth-ldap_django-auth-ldap/.github/workflows/test.yml", "downloaded_repos/django-auth-ldap_django-auth-ldap/.gitignore", "downloaded_repos/django-auth-ldap_django-auth-ldap/.readthedocs.yml", "downloaded_repos/django-auth-ldap_django-auth-ldap/LICENSE", "downloaded_repos/django-auth-ldap_django-auth-ldap/MANIFEST.in", "downloaded_repos/django-auth-ldap_django-auth-ldap/README.rst", "downloaded_repos/django-auth-ldap_django-auth-ldap/django_auth_ldap/__init__.py", "downloaded_repos/django-auth-ldap_django-auth-ldap/django_auth_ldap/backend.py", "downloaded_repos/django-auth-ldap_django-auth-ldap/django_auth_ldap/config.py", "downloaded_repos/django-auth-ldap_django-auth-ldap/docs/Makefile", "downloaded_repos/django-auth-ldap_django-auth-ldap/docs/authentication.rst", "downloaded_repos/django-auth-ldap_django-auth-ldap/docs/changes.rst", "downloaded_repos/django-auth-ldap_django-auth-ldap/docs/conf.py", "downloaded_repos/django-auth-ldap_django-auth-ldap/docs/contributing.rst", "downloaded_repos/django-auth-ldap_django-auth-ldap/docs/custombehavior.rst", "downloaded_repos/django-auth-ldap_django-auth-ldap/docs/example.rst", "downloaded_repos/django-auth-ldap_django-auth-ldap/docs/ext/daldocs.py", "downloaded_repos/django-auth-ldap_django-auth-ldap/docs/groups.rst", "downloaded_repos/django-auth-ldap_django-auth-ldap/docs/index.rst", "downloaded_repos/django-auth-ldap_django-auth-ldap/docs/install.rst", "downloaded_repos/django-auth-ldap_django-auth-ldap/docs/logging.rst", "downloaded_repos/django-auth-ldap_django-auth-ldap/docs/multiconfig.rst", "downloaded_repos/django-auth-ldap_django-auth-ldap/docs/performance.rst", "downloaded_repos/django-auth-ldap_django-auth-ldap/docs/permissions.rst", "downloaded_repos/django-auth-ldap_django-auth-ldap/docs/reference.rst", "downloaded_repos/django-auth-ldap_django-auth-ldap/docs/requirements.txt", "downloaded_repos/django-auth-ldap_django-auth-ldap/docs/users.rst", "downloaded_repos/django-auth-ldap_django-auth-ldap/pyproject.toml", "downloaded_repos/django-auth-ldap_django-auth-ldap/tox.ini"], "skipped": [{"path": "downloaded_repos/django-auth-ldap_django-auth-ldap/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/django-auth-ldap_django-auth-ldap/tests/import_test_without_django.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/django-auth-ldap_django-auth-ldap/tests/models.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/django-auth-ldap_django-auth-ldap/tests/settings.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/django-auth-ldap_django-auth-ldap/tests/tests.ldif", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/django-auth-ldap_django-auth-ldap/tests/tests.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 2.1856539249420166, "profiling_times": {"config_time": 6.479539632797241, "core_time": 3.886716365814209, "ignores_time": 0.002254962921142578, "total_time": 10.370497941970825}, "parsing_time": {"total_time": 0.20726847648620605, "per_file_time": {"mean": 0.018842588771473278, "std_dev": 7.775288506312909e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.7544829845428467, "per_file_time": {"mean": 0.022208645373960077, "std_dev": 0.00893464572398949}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.6842706203460693, "per_file_and_rule_time": {"mean": 0.002683414197435566, "std_dev": 7.058622036784415e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.16540074348449707, "per_def_and_rule_time": {"mean": 0.00015677795590947588, "std_dev": 3.56137053767859e-08}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}