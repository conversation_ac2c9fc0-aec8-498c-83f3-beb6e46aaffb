{"version": "1.130.0", "results": [], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/Ecodev_graphql-upload/.github/workflows/main.yml", "start": {"line": 35, "col": 37, "offset": 833}, "end": {"line": 35, "col": 56, "offset": 852}}]], "message": "Syntax error at line downloaded_repos/Ecodev_graphql-upload/.github/workflows/main.yml:35:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ runner.tool_cache` was unexpected", "path": "downloaded_repos/Ecodev_graphql-upload/.github/workflows/main.yml", "spans": [{"file": "downloaded_repos/Ecodev_graphql-upload/.github/workflows/main.yml", "start": {"line": 35, "col": 37, "offset": 833}, "end": {"line": 35, "col": 56, "offset": 852}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/Ecodev_graphql-upload/.github/workflows/main.yml", "start": {"line": 38, "col": 37, "offset": 952}, "end": {"line": 38, "col": 56, "offset": 971}}]], "message": "Syntax error at line downloaded_repos/Ecodev_graphql-upload/.github/workflows/main.yml:38:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ runner.tool_cache` was unexpected", "path": "downloaded_repos/Ecodev_graphql-upload/.github/workflows/main.yml", "spans": [{"file": "downloaded_repos/Ecodev_graphql-upload/.github/workflows/main.yml", "start": {"line": 38, "col": 37, "offset": 952}, "end": {"line": 38, "col": 56, "offset": 971}}]}], "paths": {"scanned": ["downloaded_repos/Ecodev_graphql-upload/.gitattributes", "downloaded_repos/Ecodev_graphql-upload/.github/dependabot.yml", "downloaded_repos/Ecodev_graphql-upload/.github/workflows/main.yml", "downloaded_repos/Ecodev_graphql-upload/.gitignore", "downloaded_repos/Ecodev_graphql-upload/.php-cs-fixer.dist.php", "downloaded_repos/Ecodev_graphql-upload/.scrutinizer.yml", "downloaded_repos/Ecodev_graphql-upload/LICENSE.md", "downloaded_repos/Ecodev_graphql-upload/README.md", "downloaded_repos/Ecodev_graphql-upload/bin/pre-commit.sh", "downloaded_repos/Ecodev_graphql-upload/composer.json", "downloaded_repos/Ecodev_graphql-upload/composer.lock", "downloaded_repos/Ecodev_graphql-upload/phpstan.neon.dist", "downloaded_repos/Ecodev_graphql-upload/phpunit.xml.dist", "downloaded_repos/Ecodev_graphql-upload/src/UploadMiddleware.php", "downloaded_repos/Ecodev_graphql-upload/src/UploadType.php"], "skipped": [{"path": "downloaded_repos/Ecodev_graphql-upload/.github/workflows/main.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/Ecodev_graphql-upload/tests/Psr7/PsrUploadedFileStub.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Ecodev_graphql-upload/tests/UploadMiddlewareTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Ecodev_graphql-upload/tests/UploadTypeTest.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.5835061073303223, "profiling_times": {"config_time": 5.492985963821411, "core_time": 2.159499406814575, "ignores_time": 0.001573801040649414, "total_time": 7.654865026473999}, "parsing_time": {"total_time": 0.09456849098205566, "per_file_time": {"mean": 0.011821061372756958, "std_dev": 4.778554418827241e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.29074740409851074, "per_file_time": {"mean": 0.0076512474762766, "std_dev": 0.00014986353291891215}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.055295705795288086, "per_file_and_rule_time": {"mean": 0.0005700588226318359, "std_dev": 1.2029094046532187e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.0009889602661132812, "per_def_and_rule_time": {"mean": 0.0009889602661132812, "std_dev": 0.0}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1091254080}, "engine_requested": "OSS", "skipped_rules": []}