{"version": "1.130.0", "results": [], "errors": [], "paths": {"scanned": ["downloaded_repos/simplygoodwork_craft-donkeytail/.craftplugin", "downloaded_repos/simplygoodwork_craft-donkeytail/.gitignore", "downloaded_repos/simplygoodwork_craft-donkeytail/CHANGELOG.md", "downloaded_repos/simplygoodwork_craft-donkeytail/LICENSE.md", "downloaded_repos/simplygoodwork_craft-donkeytail/README.md", "downloaded_repos/simplygoodwork_craft-donkeytail/babel.config.js", "downloaded_repos/simplygoodwork_craft-donkeytail/composer.json", "downloaded_repos/simplygoodwork_craft-donkeytail/composer.lock", "downloaded_repos/simplygoodwork_craft-donkeytail/package.json", "downloaded_repos/simplygoodwork_craft-donkeytail/postcss.config.js", "downloaded_repos/simplygoodwork_craft-donkeytail/resources/img/plugin-logo.png", "downloaded_repos/simplygoodwork_craft-donkeytail/src/Donkeytail.php", "downloaded_repos/simplygoodwork_craft-donkeytail/src/assetbundles/donkeytail/DonkeytailAsset.php", "downloaded_repos/simplygoodwork_craft-donkeytail/src/assetbundles/donkeytail/src/app.css", "downloaded_repos/simplygoodwork_craft-donkeytail/src/assetbundles/donkeytail/src/components/DonkeytailCanvas.vue", "downloaded_repos/simplygoodwork_craft-donkeytail/src/assetbundles/donkeytail/src/components/DonkeytailPin.vue", "downloaded_repos/simplygoodwork_craft-donkeytail/src/assetbundles/donkeytail/src/main.js", "downloaded_repos/simplygoodwork_craft-donkeytail/src/controllers/DefaultController.php", "downloaded_repos/simplygoodwork_craft-donkeytail/src/fields/Donkeytail.php", "downloaded_repos/simplygoodwork_craft-donkeytail/src/gql/DonkeytailType.php", "downloaded_repos/simplygoodwork_craft-donkeytail/src/gql/PinType.php", "downloaded_repos/simplygoodwork_craft-donkeytail/src/icon-mask.svg", "downloaded_repos/simplygoodwork_craft-donkeytail/src/icon.svg", "downloaded_repos/simplygoodwork_craft-donkeytail/src/models/DonkeytailModel.php", "downloaded_repos/simplygoodwork_craft-donkeytail/src/models/PinModel.php", "downloaded_repos/simplygoodwork_craft-donkeytail/src/templates/_components/fields/Donkeytail_input.twig", "downloaded_repos/simplygoodwork_craft-donkeytail/src/templates/_components/fields/Donkeytail_settings.twig", "downloaded_repos/simplygoodwork_craft-donkeytail/src/translations/en/donkeytail.php", "downloaded_repos/simplygoodwork_craft-donkeytail/src/variables/DonkeytailVariable.php", "downloaded_repos/simplygoodwork_craft-donkeytail/tailwind.config.js", "downloaded_repos/simplygoodwork_craft-donkeytail/vue.config.js", "downloaded_repos/simplygoodwork_craft-donkeytail/yarn.lock"], "skipped": [{"path": "downloaded_repos/simplygoodwork_craft-donkeytail/resources/img/screenshot.png", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/simplygoodwork_craft-donkeytail/src/assetbundles/donkeytail/dist/app.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplygoodwork_craft-donkeytail/src/assetbundles/donkeytail/dist/css/app.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplygoodwork_craft-donkeytail/src/assetbundles/donkeytail/dist/css/chunk-vendors.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplygoodwork_craft-donkeytail/src/assetbundles/donkeytail/dist/js/app.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/simplygoodwork_craft-donkeytail/src/assetbundles/donkeytail/dist/js/chunk-vendors.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.80906081199646, "profiling_times": {"config_time": 6.73990535736084, "core_time": 2.6576528549194336, "ignores_time": 0.001714944839477539, "total_time": 9.400418281555176}, "parsing_time": {"total_time": 0.3038492202758789, "per_file_time": {"mean": 0.01787348354563994, "std_dev": 0.00016887019944022286}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.3027424812316895, "per_file_time": {"mean": 0.01608324050903321, "std_dev": 0.0010657739114804702}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.13818359375, "per_file_and_rule_time": {"mean": 0.0032135719476744187, "std_dev": 1.8834455610269594e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.0084228515625, "per_def_and_rule_time": {"mean": 0.00046793619791666663, "std_dev": 3.255605697126561e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}