#!/usr/bin/env python3

"""
Docker Unified Vulnerability Scanner Runner
===========================================

Builds and runs the unified vulnerability scanner in Docker with Semgrep integration.
"""

import os
import sys
import subprocess
import argparse
import time
from pathlib import Path

def check_docker():
    """Check if Docker is available and running"""
    try:
        result = subprocess.run(["docker", "--version"], capture_output=True, text=True)
        if result.returncode != 0:
            return False, "Docker not installed"
        
        result = subprocess.run(["docker", "info"], capture_output=True, text=True)
        if result.returncode != 0:
            return False, "Docker daemon not running"
        
        return True, "Docker available"
    except FileNotFoundError:
        return False, "Docker not found in PATH"

def check_github_token():
    """Check if GitHub token is set"""
    token = os.getenv("GITHUB_TOKEN")
    if not token or token == "your_github_token_here":
        return False, "GITHUB_TOKEN not set"
    return True, f"Token set: {token[:8]}..."

def build_docker_image():
    """Build the Docker image"""
    print("🔨 Building Docker image...")
    
    try:
        cmd = ["docker", "build", "-t", "unified-vuln-scanner", "."]
        result = subprocess.run(cmd, check=True)
        print("✅ Docker image built successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to build Docker image: {e}")
        return False

def pull_semgrep_image():
    """Pull the latest Semgrep image"""
    print("📥 Pulling Semgrep Docker image...")
    
    try:
        cmd = ["docker", "pull", "returntocorp/semgrep:latest"]
        result = subprocess.run(cmd, check=True)
        print("✅ Semgrep image pulled successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to pull Semgrep image: {e}")
        return False

def create_output_directory():
    """Create output directory for results"""
    output_dir = Path("./output")
    output_dir.mkdir(exist_ok=True)
    
    # Create subdirectories
    (output_dir / "semgrep_results").mkdir(exist_ok=True)
    (output_dir / "logs").mkdir(exist_ok=True)
    
    print(f"📁 Output directory created: {output_dir.absolute()}")
    return output_dir

def run_docker_scanner(args):
    """Run the unified scanner in Docker"""
    print("🚀 Running Unified Vulnerability Scanner in Docker...")
    
    # Prepare Docker command
    docker_cmd = [
        "docker", "run", "--rm",
        "-e", f"GITHUB_TOKEN={os.getenv('GITHUB_TOKEN')}",
        "-v", f"{os.getcwd()}/output:/app/output",
        "-v", "/var/run/docker.sock:/var/run/docker.sock",  # For Docker-in-Docker
        "--name", "vuln-scanner-run",
        "unified-vuln-scanner"
    ]
    
    # Add scanner arguments
    scanner_args = []
    if args.max_repos:
        scanner_args.extend(["--max-repos", str(args.max_repos)])
    if args.pages:
        scanner_args.extend(["--pages", str(args.pages)])
    if args.min_stars:
        scanner_args.extend(["--min-stars", str(args.min_stars)])
    if args.max_stars:
        scanner_args.extend(["--max-stars", str(args.max_stars)])
    if args.concurrent:
        scanner_args.extend(["--concurrent", str(args.concurrent)])
    if args.timeout:
        scanner_args.extend(["--timeout", str(args.timeout)])
    if args.no_docker:
        scanner_args.append("--no-docker")
    
    docker_cmd.extend(scanner_args)
    
    print(f"Command: {' '.join(docker_cmd)}")
    print()
    
    try:
        # Run the container
        start_time = time.time()
        result = subprocess.run(docker_cmd, check=True)
        end_time = time.time()
        
        duration = end_time - start_time
        print(f"\n✅ Scan completed successfully in {duration:.1f} seconds")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Scanner failed with return code {e.returncode}")
        return False
    except KeyboardInterrupt:
        print(f"\n⚠️ Scan interrupted by user")
        # Try to stop the container
        try:
            subprocess.run(["docker", "stop", "vuln-scanner-run"], check=False)
        except:
            pass
        return False

def run_with_compose(args):
    """Run using docker-compose"""
    print("🚀 Running with Docker Compose...")
    
    # Create .env file for docker-compose
    with open(".env", "w") as f:
        f.write(f"GITHUB_TOKEN={os.getenv('GITHUB_TOKEN')}\n")
    
    try:
        cmd = ["docker-compose", "up", "--build"]
        result = subprocess.run(cmd, check=True)
        print("✅ Docker Compose run completed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Docker Compose failed: {e}")
        return False

def show_results():
    """Show scan results"""
    output_dir = Path("./output")
    
    if not output_dir.exists():
        print("❌ No output directory found")
        return
    
    print("\n📊 SCAN RESULTS")
    print("=" * 40)
    
    # List output files
    csv_files = list(output_dir.glob("*.csv"))
    json_files = list(output_dir.glob("*.json"))
    
    if csv_files:
        print("📄 CSV Results:")
        for file in csv_files:
            size = file.stat().st_size
            print(f"  {file.name} ({size:,} bytes)")
    
    if json_files:
        print("📋 JSON Results:")
        for file in json_files:
            size = file.stat().st_size
            print(f"  {file.name} ({size:,} bytes)")
    
    # Check for Semgrep results
    semgrep_dir = output_dir / "semgrep_results"
    if semgrep_dir.exists():
        semgrep_files = list(semgrep_dir.glob("*.json"))
        if semgrep_files:
            print(f"🔍 Semgrep Results: {len(semgrep_files)} files")
    
    # Show high-priority results if available
    high_priority_file = output_dir / "high_priority_unified_results.csv"
    if high_priority_file.exists():
        try:
            with open(high_priority_file, 'r') as f:
                lines = sum(1 for line in f) - 1  # Subtract header
            print(f"\n🎯 High-priority targets found: {lines}")
        except:
            pass

def main():
    parser = argparse.ArgumentParser(description="Docker Unified Vulnerability Scanner")
    parser.add_argument("--max-repos", type=int, default=50, help="Maximum repositories to analyze")
    parser.add_argument("--pages", type=int, default=3, help="Search pages per query")
    parser.add_argument("--min-stars", type=int, default=50, help="Minimum star count")
    parser.add_argument("--max-stars", type=int, default=2000, help="Maximum star count")
    parser.add_argument("--concurrent", type=int, default=2, help="Max concurrent scans")
    parser.add_argument("--timeout", type=int, default=300, help="Semgrep timeout in seconds")
    parser.add_argument("--no-docker", action="store_true", help="Don't use Docker for Semgrep")
    parser.add_argument("--compose", action="store_true", help="Use docker-compose instead")
    parser.add_argument("--build-only", action="store_true", help="Only build the image")
    parser.add_argument("--show-results", action="store_true", help="Show existing results")
    
    args = parser.parse_args()
    
    print("🎯 DOCKER UNIFIED VULNERABILITY SCANNER")
    print("=" * 50)
    
    # Show results only
    if args.show_results:
        show_results()
        return
    
    # Check prerequisites
    print("🔍 Checking prerequisites...")
    
    docker_ok, docker_msg = check_docker()
    print(f"  Docker: {docker_msg}")
    if not docker_ok:
        print("❌ Docker is required. Please install and start Docker.")
        sys.exit(1)
    
    token_ok, token_msg = check_github_token()
    print(f"  GitHub Token: {token_msg}")
    if not token_ok:
        print("❌ Please set GITHUB_TOKEN environment variable")
        print("   export GITHUB_TOKEN='your_token_here'")
        sys.exit(1)
    
    print("✅ Prerequisites satisfied\n")
    
    # Create output directory
    output_dir = create_output_directory()
    
    # Pull Semgrep image
    if not pull_semgrep_image():
        print("⚠️ Failed to pull Semgrep image, continuing anyway...")
    
    # Build Docker image
    if not build_docker_image():
        print("❌ Failed to build Docker image")
        sys.exit(1)
    
    if args.build_only:
        print("✅ Build complete")
        return
    
    # Run scanner
    if args.compose:
        success = run_with_compose(args)
    else:
        success = run_docker_scanner(args)
    
    if success:
        show_results()
        print(f"\n🎯 Results saved to: {output_dir.absolute()}")
    else:
        print("❌ Scan failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
