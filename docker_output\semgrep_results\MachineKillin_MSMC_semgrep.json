{"version": "1.130.0", "results": [{"check_id": "python.requests.security.disabled-cert-validation.disabled-cert-validation", "path": "downloaded_repos/MachineKillin_MSMC/MSMC.py", "start": {"line": 115, "col": 22, "offset": 5859}, "end": {"line": 115, "col": 274, "offset": 6111}, "extra": {"message": "Certificate verification has been explicitly disabled. This permits insecure connections to insecure servers. Re-enable certification validation.", "fix": "requests.get('https://plancke.io/hypixel/player/stats/'+self.name, proxies=getproxy(), headers={'User-Agent':'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0'}, verify=True)", "metadata": {"cwe": ["CWE-295: Improper Certificate Validation"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A07:2021 - Identification and Authentication Failures"], "references": ["https://stackoverflow.com/questions/41740361/is-it-safe-to-disable-ssl-certificate-verification-in-pythonss-requests-lib"], "category": "security", "technology": ["requests"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/python.requests.security.disabled-cert-validation.disabled-cert-validation", "shortlink": "https://sg.run/AlYp"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.requests.security.disabled-cert-validation.disabled-cert-validation", "path": "downloaded_repos/MachineKillin_MSMC/MSMC.py", "start": {"line": 133, "col": 27, "offset": 7273}, "end": {"line": 133, "col": 116, "offset": 7362}, "extra": {"message": "Certificate verification has been explicitly disabled. This permits insecure connections to insecure servers. Re-enable certification validation.", "fix": "requests.get(\"https://sky.shiiyu.moe/stats/\"+self.name, proxies=getproxy(), verify=True)", "metadata": {"cwe": ["CWE-295: Improper Certificate Validation"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A07:2021 - Identification and Authentication Failures"], "references": ["https://stackoverflow.com/questions/41740361/is-it-safe-to-disable-ssl-certificate-verification-in-pythonss-requests-lib"], "category": "security", "technology": ["requests"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/python.requests.security.disabled-cert-validation.disabled-cert-validation", "shortlink": "https://sg.run/AlYp"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.requests.security.disabled-cert-validation.disabled-cert-validation", "path": "downloaded_repos/MachineKillin_MSMC/MSMC.py", "start": {"line": 141, "col": 23, "offset": 7690}, "end": {"line": 141, "col": 117, "offset": 7784}, "extra": {"message": "Certificate verification has been explicitly disabled. This permits insecure connections to insecure servers. Re-enable certification validation.", "fix": "requests.get(f'http://s.optifine.net/capes/{self.name}.png', proxies=getproxy(), verify=True)", "metadata": {"cwe": ["CWE-295: Improper Certificate Validation"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A07:2021 - Identification and Authentication Failures"], "references": ["https://stackoverflow.com/questions/41740361/is-it-safe-to-disable-ssl-certificate-verification-in-pythonss-requests-lib"], "category": "security", "technology": ["requests"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/python.requests.security.disabled-cert-validation.disabled-cert-validation", "shortlink": "https://sg.run/AlYp"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/MachineKillin_MSMC/MSMC.py", "start": {"line": 141, "col": 36, "offset": 7703}, "end": {"line": 141, "col": 82, "offset": 7749}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.requests.security.disabled-cert-validation.disabled-cert-validation", "path": "downloaded_repos/MachineKillin_MSMC/MSMC.py", "start": {"line": 150, "col": 34, "offset": 8080}, "end": {"line": 150, "col": 140, "offset": 8186}, "extra": {"message": "Certificate verification has been explicitly disabled. This permits insecure connections to insecure servers. Re-enable certification validation.", "fix": "requests.get(f\"https://email.avine.tools/check?email={self.email}&password={self.password}\", verify=True)", "metadata": {"cwe": ["CWE-295: Improper Certificate Validation"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A07:2021 - Identification and Authentication Failures"], "references": ["https://stackoverflow.com/questions/41740361/is-it-safe-to-disable-ssl-certificate-verification-in-pythonss-requests-lib"], "category": "security", "technology": ["requests"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/python.requests.security.disabled-cert-validation.disabled-cert-validation", "shortlink": "https://sg.run/AlYp"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.requests.security.disabled-cert-validation.disabled-cert-validation", "path": "downloaded_repos/MachineKillin_MSMC/MSMC.py", "start": {"line": 166, "col": 29, "offset": 8957}, "end": {"line": 166, "col": 192, "offset": 9120}, "extra": {"message": "Certificate verification has been explicitly disabled. This permits insecure connections to insecure servers. Re-enable certification validation.", "fix": "requests.get('https://api.minecraftservices.com/minecraft/profile/namechange', headers={'Authorization': f'Bearer {self.token}'}, proxies=getproxy(), verify=True)", "metadata": {"cwe": ["CWE-295: Improper Certificate Validation"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A07:2021 - Identification and Authentication Failures"], "references": ["https://stackoverflow.com/questions/41740361/is-it-safe-to-disable-ssl-certificate-verification-in-pythonss-requests-lib"], "category": "security", "technology": ["requests"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/python.requests.security.disabled-cert-validation.disabled-cert-validation", "shortlink": "https://sg.run/AlYp"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/MachineKillin_MSMC/MSMC.py", "downloaded_repos/MachineKillin_MSMC/README.md", "downloaded_repos/MachineKillin_MSMC/requirements.txt"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 2.0532751083374023, "profiling_times": {"config_time": 7.198914051055908, "core_time": 4.783282995223999, "ignores_time": 0.0018572807312011719, "total_time": 11.985077619552612}, "parsing_time": {"total_time": 0.04314088821411133, "per_file_time": {"mean": 0.04314088821411133, "std_dev": 0.0}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 2.050144672393799, "per_file_time": {"mean": 0.29287781034197125, "std_dev": 0.4849887626067367}, "very_slow_stats": {"time_ratio": 0.9748511733436602, "count_ratio": 0.14285714285714285}, "very_slow_files": [{"fpath": "downloaded_repos/MachineKillin_MSMC/MSMC.py", "ftime": 1.9985859394073486}]}, "matching_time": {"total_time": 1.3171274662017822, "per_file_and_rule_time": {"mean": 0.03658687406116061, "std_dev": 0.0045625169414762025}, "very_slow_stats": {"time_ratio": 0.4864797889665781, "count_ratio": 0.08333333333333333}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/MachineKillin_MSMC/MSMC.py", "rule_id": "python.django.security.injection.command.command-injection-os-system.command-injection-os-system", "time": 0.10231590270996094}, {"fpath": "downloaded_repos/MachineKillin_MSMC/MSMC.py", "rule_id": "python.django.security.injection.path-traversal.path-traversal-open.path-traversal-open", "time": 0.16653990745544434}, {"fpath": "downloaded_repos/MachineKillin_MSMC/MSMC.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.3719000816345215}]}, "tainting_time": {"total_time": 0.5419869422912598, "per_def_and_rule_time": {"mean": 0.0014262814270822627, "std_dev": 0.00011518505472388108}, "very_slow_stats": {"time_ratio": 0.385074197473406, "count_ratio": 0.002631578947368421}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/MachineKillin_MSMC/MSMC.py", "fline": 300, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.20870518684387207}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089102016}, "engine_requested": "OSS", "skipped_rules": []}