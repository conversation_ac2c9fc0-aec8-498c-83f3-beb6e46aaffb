{"version": "1.130.0", "results": [{"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Hapus_Akun.php", "start": {"line": 5, "col": 24, "offset": 79}, "end": {"line": 5, "col": 65, "offset": 120}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Hapus_Guru.php", "start": {"line": 5, "col": 24, "offset": 79}, "end": {"line": 5, "col": 60, "offset": 115}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Hapus_Mata_Pelajaran.php", "start": {"line": 5, "col": 24, "offset": 79}, "end": {"line": 5, "col": 86, "offset": 141}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Hapus_Murid.php", "start": {"line": 5, "col": 24, "offset": 79}, "end": {"line": 5, "col": 62, "offset": 117}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Hapus_Pesan.php", "start": {"line": 5, "col": 24, "offset": 79}, "end": {"line": 5, "col": 61, "offset": 116}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Input_Nilai.php", "start": {"line": 46, "col": 11, "offset": 1754}, "end": {"line": 46, "col": 96, "offset": 1839}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Input_Nilai.php", "start": {"line": 67, "col": 23, "offset": 2731}, "end": {"line": 67, "col": 116, "offset": 2824}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Input_Nilai.php", "start": {"line": 75, "col": 24, "offset": 3024}, "end": {"line": 75, "col": 212, "offset": 3212}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Proses_Edit_Akun.php", "start": {"line": 11, "col": 10, "offset": 255}, "end": {"line": 11, "col": 85, "offset": 330}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Proses_Edit_Akun.php", "start": {"line": 16, "col": 10, "offset": 452}, "end": {"line": 16, "col": 112, "offset": 554}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Proses_Edit_Akun.php", "start": {"line": 20, "col": 10, "offset": 637}, "end": {"line": 20, "col": 111, "offset": 738}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Proses_Edit_Akun.php", "start": {"line": 24, "col": 10, "offset": 758}, "end": {"line": 24, "col": 142, "offset": 890}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Proses_Edit_Guru.php", "start": {"line": 14, "col": 10, "offset": 290}, "end": {"line": 14, "col": 187, "offset": 467}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Proses_Edit_Guru.php", "start": {"line": 18, "col": 10, "offset": 487}, "end": {"line": 18, "col": 201, "offset": 678}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Proses_Edit_Mata_Pelajaran.php", "start": {"line": 13, "col": 10, "offset": 278}, "end": {"line": 13, "col": 190, "offset": 458}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Proses_Edit_Mata_Pelajaran.php", "start": {"line": 17, "col": 10, "offset": 478}, "end": {"line": 17, "col": 226, "offset": 694}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Proses_Edit_Murid.php", "start": {"line": 16, "col": 10, "offset": 333}, "end": {"line": 16, "col": 226, "offset": 549}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Proses_Edit_Murid.php", "start": {"line": 20, "col": 10, "offset": 569}, "end": {"line": 20, "col": 243, "offset": 802}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Proses_Input_Akun.php", "start": {"line": 8, "col": 31, "offset": 159}, "end": {"line": 8, "col": 83, "offset": 211}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Proses_Input_Akun.php", "start": {"line": 10, "col": 16, "offset": 294}, "end": {"line": 10, "col": 115, "offset": 393}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Proses_Input_Mata_Pelajaran.php", "start": {"line": 10, "col": 31, "offset": 220}, "end": {"line": 10, "col": 100, "offset": 289}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Proses_Input_Mata_Pelajaran.php", "start": {"line": 12, "col": 11, "offset": 367}, "end": {"line": 12, "col": 186, "offset": 542}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Proses_Tambah_Guru.php", "start": {"line": 12, "col": 9, "offset": 231}, "end": {"line": 12, "col": 177, "offset": 399}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Proses_Tambah_Murid.php", "start": {"line": 14, "col": 9, "offset": 271}, "end": {"line": 14, "col": 209, "offset": 471}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Admin/akun_edit.php", "start": {"line": 41, "col": 31, "offset": 1681}, "end": {"line": 41, "col": 74, "offset": 1724}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Admin/guru_edit.php", "start": {"line": 41, "col": 31, "offset": 1672}, "end": {"line": 41, "col": 69, "offset": 1710}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Admin/mata_pelajaran_edit.php", "start": {"line": 51, "col": 37, "offset": 1814}, "end": {"line": 51, "col": 101, "offset": 1878}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Admin/mata_pelajaran_edit.php", "start": {"line": 54, "col": 20, "offset": 1941}, "end": {"line": 54, "col": 67, "offset": 1988}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Admin/murid_edit.php", "start": {"line": 39, "col": 31, "offset": 1651}, "end": {"line": 39, "col": 71, "offset": 1691}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Admin/process_guru.php", "start": {"line": 7, "col": 17, "offset": 149}, "end": {"line": 7, "col": 100, "offset": 232}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Admin/process_guru.php", "start": {"line": 18, "col": 41, "offset": 498}, "end": {"line": 18, "col": 180, "offset": 637}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Admin/process_guru.php", "start": {"line": 21, "col": 14, "offset": 698}, "end": {"line": 21, "col": 205, "offset": 889}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Admin/process_guru.php", "start": {"line": 24, "col": 14, "offset": 952}, "end": {"line": 24, "col": 199, "offset": 1137}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Guru/Nilai.php", "start": {"line": 46, "col": 11, "offset": 1894}, "end": {"line": 46, "col": 124, "offset": 2007}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Guru/Nilai.php", "start": {"line": 68, "col": 23, "offset": 2898}, "end": {"line": 68, "col": 116, "offset": 2991}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Guru/Nilai.php", "start": {"line": 76, "col": 24, "offset": 3191}, "end": {"line": 76, "col": 212, "offset": 3379}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Se<PERSON>lah/Guru/process_guru.php", "start": {"line": 7, "col": 17, "offset": 149}, "end": {"line": 7, "col": 100, "offset": 232}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Se<PERSON>lah/Guru/process_guru.php", "start": {"line": 18, "col": 41, "offset": 498}, "end": {"line": 18, "col": 180, "offset": 637}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Se<PERSON>lah/Guru/process_guru.php", "start": {"line": 21, "col": 14, "offset": 698}, "end": {"line": 21, "col": 205, "offset": 889}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Se<PERSON>lah/Guru/process_guru.php", "start": {"line": 24, "col": 14, "offset": 952}, "end": {"line": 24, "col": 199, "offset": 1137}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/Proses_Kirim.php", "start": {"line": 5, "col": 8, "offset": 80}, "end": {"line": 5, "col": 204, "offset": 276}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/romadebrian_WEB-Sekolah/js/numscroller-1.0.js", "start": {"line": 83, "col": 46, "offset": 3519}, "end": {"line": 83, "col": 56, "offset": 3529}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/romadebrian_WEB-Sekolah/js/numscroller-1.0.js", "start": {"line": 83, "col": 57, "offset": 3530}, "end": {"line": 83, "col": 66, "offset": 3539}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/romadebrian_WEB-Sekolah/js/numscroller-1.0.js", "start": {"line": 83, "col": 67, "offset": 3540}, "end": {"line": 83, "col": 76, "offset": 3549}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/romadebrian_WEB-Sekolah/js/numscroller-1.0.js", "start": {"line": 83, "col": 77, "offset": 3550}, "end": {"line": 83, "col": 92, "offset": 3565}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/romadebrian_WEB-Sekolah/js/numscroller-1.0.js", "start": {"line": 83, "col": 93, "offset": 3566}, "end": {"line": 83, "col": 106, "offset": 3579}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/proses_daftar.php", "start": {"line": 6, "col": 20, "offset": 87}, "end": {"line": 6, "col": 79, "offset": 146}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/proses_daftar.php", "start": {"line": 9, "col": 14, "offset": 216}, "end": {"line": 9, "col": 115, "offset": 317}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/proses_daftar.php", "start": {"line": 11, "col": 15, "offset": 334}, "end": {"line": 11, "col": 247, "offset": 566}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/romadebrian_WEB-Sekolah/proses_login.php", "start": {"line": 6, "col": 31, "offset": 122}, "end": {"line": 6, "col": 95, "offset": 186}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "path": "downloaded_repos/romadebrian_WEB-Sekolah/proses_login.php", "start": {"line": 8, "col": 4, "offset": 216}, "end": {"line": 8, "col": 30, "offset": 242}, "extra": {"message": "Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues", "metadata": {"cwe": ["CWE-697: Incorrect Comparison"], "references": ["https://www.php.net/manual/en/types.comparisons.php", "https://web.archive.org/web/20210430183236/https://www.whitehatsec.com/blog/magic-hashes/"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.md5-loose-equality.md5-loose-equality", "shortlink": "https://sg.run/Do4G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "path": "downloaded_repos/romadebrian_WEB-Sekolah/proses_login.php", "start": {"line": 17, "col": 5, "offset": 450}, "end": {"line": 17, "col": 23, "offset": 468}, "extra": {"message": "Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues", "metadata": {"cwe": ["CWE-697: Incorrect Comparison"], "references": ["https://www.php.net/manual/en/types.comparisons.php", "https://web.archive.org/web/20210430183236/https://www.whitehatsec.com/blog/magic-hashes/"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.md5-loose-equality.md5-loose-equality", "shortlink": "https://sg.run/Do4G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "path": "downloaded_repos/romadebrian_WEB-Sekolah/proses_login.php", "start": {"line": 23, "col": 10, "offset": 579}, "end": {"line": 23, "col": 28, "offset": 597}, "extra": {"message": "Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues", "metadata": {"cwe": ["CWE-697: Incorrect Comparison"], "references": ["https://www.php.net/manual/en/types.comparisons.php", "https://web.archive.org/web/20210430183236/https://www.whitehatsec.com/blog/magic-hashes/"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.md5-loose-equality.md5-loose-equality", "shortlink": "https://sg.run/Do4G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "path": "downloaded_repos/romadebrian_WEB-Sekolah/proses_login.php", "start": {"line": 29, "col": 10, "offset": 707}, "end": {"line": 29, "col": 28, "offset": 725}, "extra": {"message": "Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues", "metadata": {"cwe": ["CWE-697: Incorrect Comparison"], "references": ["https://www.php.net/manual/en/types.comparisons.php", "https://web.archive.org/web/20210430183236/https://www.whitehatsec.com/blog/magic-hashes/"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.md5-loose-equality.md5-loose-equality", "shortlink": "https://sg.run/Do4G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/romadebrian_WEB-Sekolah/js/jquery-ui.js:\n ", "path": "downloaded_repos/romadebrian_WEB-Sekolah/js/jquery-ui.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "message": "Timeout when running javascript.express.security.audit.remote-property-injection.remote-property-injection on downloaded_repos/romadebrian_WEB-Sekolah/js/jquery-ui.js:\n ", "path": "downloaded_repos/romadebrian_WEB-Sekolah/js/jquery-ui.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "message": "Timeout when running typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method on downloaded_repos/romadebrian_WEB-Sekolah/js/jquery-ui.js:\n ", "path": "downloaded_repos/romadebrian_WEB-Sekolah/js/jquery-ui.js"}], "paths": {"scanned": ["downloaded_repos/romadebrian_WEB-Sekolah/Admin/Hapus_Akun.php", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Hapus_Guru.php", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Hapus_Mata_Pelajaran.php", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Hapus_Murid.php", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Hapus_Pesan.php", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Input_Nilai.php", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Nilai.php", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Note untuk logika <PERSON>.txt", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Proses_Edit_Akun.php", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Proses_Edit_Guru.php", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Proses_Edit_Mata_Pelajaran.php", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Proses_Edit_Murid.php", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Proses_Input_Akun.php", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Proses_Input_Mata_Pelajaran.php", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Proses_Tambah_Guru.php", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/Proses_Tambah_Murid.php", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/akun.php", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/akun2.php", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/akun_edit.php", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/guru.php", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/guru_edit.php", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/icon.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/image/a.png", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/image/e.png", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/image/g.png", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/image/m.png", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/image/mpl.png", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/image/n.png", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/index.php", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/mata_pelajaran.php", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/mata_pelajaran_edit.php", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/murid.php", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/murid_edit.php", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/pesan.php", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/process_guru.php", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/tambah_akun.php", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/tambah_guru.php", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/tambah_mata_pelajaran.php", "downloaded_repos/romadebrian_WEB-Sekolah/Admin/tambah_murid.php", "downloaded_repos/romadebrian_WEB-Sekolah/Data Base school.sql", "downloaded_repos/romadebrian_WEB-Sekolah/Guru/Nilai.php", "downloaded_repos/romadebrian_WEB-Sekolah/Guru/index.php", "downloaded_repos/romadebrian_WEB-Se<PERSON>lah/Guru/process_guru.php", "downloaded_repos/romadebrian_WEB-Sekolah/<PERSON>min.txt", "downloaded_repos/romadebrian_WEB-Sekolah/LICENSE", "downloaded_repos/romadebrian_WEB-Sekolah/Level.txt", "downloaded_repos/romadebrian_WEB-Sekolah/Logika.txt", "downloaded_repos/romadebrian_WEB-Sekolah/Murid/index.php", "downloaded_repos/romadebrian_WEB-Sekolah/Note Untuk Database.txt", "downloaded_repos/romadebrian_WEB-Sekolah/Proses_Kirim.php", "downloaded_repos/romadebrian_WEB-Sekolah/README.md", "downloaded_repos/romadebrian_WEB-Sekolah/akutansi.php", "downloaded_repos/romadebrian_WEB-Sekolah/css/bootstrap.css", "downloaded_repos/romadebrian_WEB-Sekolah/css/font-awesome.css", "downloaded_repos/romadebrian_WEB-Sekolah/css/jquery-ui.css", "downloaded_repos/romadebrian_WEB-Sekolah/css/roma.css", "downloaded_repos/romadebrian_WEB-Sekolah/css/style.css", "downloaded_repos/romadebrian_WEB-Sekolah/css/swipebox.css", "downloaded_repos/romadebrian_WEB-Sekolah/css/utama.css", "downloaded_repos/romadebrian_WEB-Sekolah/fasilitas.php", "downloaded_repos/romadebrian_WEB-Sekolah/fonts/FontAwesome.otf", "downloaded_repos/romadebrian_WEB-Sekolah/fonts/fontawesome-webfont.eot", "downloaded_repos/romadebrian_WEB-Sekolah/fonts/fontawesome-webfont.svg", "downloaded_repos/romadebrian_WEB-Sekolah/fonts/fontawesome-webfont.ttf", "downloaded_repos/romadebrian_WEB-Sekolah/fonts/fontawesome-webfont.woff", "downloaded_repos/romadebrian_WEB-Sekolah/fonts/fontawesome-webfont.woff2", "downloaded_repos/romadebrian_WEB-Sekolah/fonts/glyphicons-halflings-regular.woff", "downloaded_repos/romadebrian_WEB-Sekolah/fonts/glyphicons-halflings-regular.woff2", "downloaded_repos/romadebrian_WEB-Sekolah/images/1.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/2.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/Backup/ab1.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/Backup/ab2.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/Backup/avb1.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/Backup/banner2.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/Backup/bg1.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/Backup/g1.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/Backup/g10.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/Backup/g11.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/Backup/g12.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/Backup/g2.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/Backup/g3.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/Backup/g4.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/Backup/g5.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/Backup/g6.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/Backup/g7.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/Backup/g8.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/Backup/g9.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/Backup/t1.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/Backup/t2.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/Backup/t3.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/Backup/t4.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/Backup/t5.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/Backup/t6.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/FUTSALL.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/Lap1.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/MURID1-b.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/MURID1.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/MURID2.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/MURID3.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/SMKBARUNAWATI.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/Thumbs.db", "downloaded_repos/romadebrian_WEB-Sekolah/images/akun/akun.png", "downloaded_repos/romadebrian_WEB-Sekolah/images/akun/e akun.png", "downloaded_repos/romadebrian_WEB-Sekolah/images/akun/kumpul.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/baruna2.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/basket.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/basket1.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/bengkel.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/c-arrows.png", "downloaded_repos/romadebrian_WEB-Sekolah/images/carousel.png", "downloaded_repos/romadebrian_WEB-Sekolah/images/icon_gambar.ico", "downloaded_repos/romadebrian_WEB-Sekolah/images/icons.svg", "downloaded_repos/romadebrian_WEB-Sekolah/images/kantin.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/komputer.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/logo.ico", "downloaded_repos/romadebrian_WEB-Sekolah/images/lpg.png", "downloaded_repos/romadebrian_WEB-Sekolah/images/otomotif/oto.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/otomotif/oto2.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/perpustakaan.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/perpustkaan.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/play-button.png", "downloaded_repos/romadebrian_WEB-Sekolah/images/rpl/rpl.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/rpl/rpl2.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/rpl/rpl3.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/search.png", "downloaded_repos/romadebrian_WEB-Sekolah/images/sekolah.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/tanaman.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/images/up2.png", "downloaded_repos/romadebrian_WEB-Sekolah/images/voly.jpg", "downloaded_repos/romadebrian_WEB-Sekolah/index.php", "downloaded_repos/romadebrian_WEB-Sekolah/js/bootstrap.js", "downloaded_repos/romadebrian_WEB-Sekolah/js/easing.js", "downloaded_repos/romadebrian_WEB-Sekolah/js/jquery-ui.js", "downloaded_repos/romadebrian_WEB-Sekolah/js/move-top.js", "downloaded_repos/romadebrian_WEB-Sekolah/js/numscroller-1.0.js", "downloaded_repos/romadebrian_WEB-Sekolah/js/simplePlayer.js", "downloaded_repos/romadebrian_WEB-Sekolah/koneksi.php", "downloaded_repos/romadebrian_WEB-Sekolah/logout.php", "downloaded_repos/romadebrian_WEB-Sekolah/mail.php", "downloaded_repos/romadebrian_WEB-Sekolah/napigasi.php", "downloaded_repos/romadebrian_WEB-Sekolah/napigasi2.php", "downloaded_repos/romadebrian_WEB-Sekolah/otomotif.php", "downloaded_repos/romadebrian_WEB-Sekolah/profile_sekolah.php", "downloaded_repos/romadebrian_WEB-Sekolah/proses_daftar.php", "downloaded_repos/romadebrian_WEB-Sekolah/proses_login.php", "downloaded_repos/romadebrian_WEB-Sekolah/rpl.php", "downloaded_repos/romadebrian_WEB-Sekolah/screenshot/admin.png", "downloaded_repos/romadebrian_WEB-Sekolah/screenshot/akun.png", "downloaded_repos/romadebrian_WEB-Sekolah/screenshot/contact.png", "downloaded_repos/romadebrian_WEB-Sekolah/screenshot/guru.png", "downloaded_repos/romadebrian_WEB-Sekolah/screenshot/matapelajaran.png", "downloaded_repos/romadebrian_WEB-Sekolah/screenshot/murid.png", "downloaded_repos/romadebrian_WEB-Sekolah/screenshot/nilai.png", "downloaded_repos/romadebrian_WEB-Sekolah/urutan.txt", "downloaded_repos/romadebrian_WEB-Sekolah/validasi_daftar.js", "downloaded_repos/romadebrian_WEB-Sekolah/w3layouts-License.txt"], "skipped": [{"path": "downloaded_repos/romadebrian_WEB-Sekolah/images/otomotif/oto3.png", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/romadebrian_WEB-Sekolah/js/SmoothScroll.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/romadebrian_WEB-Sekolah/js/jquery-2.1.4.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/romadebrian_WEB-Sekolah/js/jquery-ui.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/romadebrian_WEB-Sekolah/js/jquery.swipebox.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/romadebrian_WEB-Sekolah/screenshot/beranda.png", "reason": "exceeded_size_limit"}]}, "time": {"rules": [], "rules_parse_time": 1.9549438953399658, "profiling_times": {"config_time": 5.796963691711426, "core_time": 32.15226984024048, "ignores_time": 0.0017406940460205078, "total_time": 37.95193958282471}, "parsing_time": {"total_time": 1.0695068836212158, "per_file_time": {"mean": 0.01944557970220392, "std_dev": 0.004177278431517595}, "very_slow_stats": {"time_ratio": 0.45489281041642843, "count_ratio": 0.01818181818181818}, "very_slow_files": [{"fpath": "downloaded_repos/romadebrian_WEB-Sekolah/js/bootstrap.js", "ftime": 0.4865109920501709}]}, "scanning_time": {"total_time": 36.498305559158325, "per_file_time": {"mean": 0.09918017814988683, "std_dev": 2.297842687748726}, "very_slow_stats": {"time_ratio": 0.8401860224192197, "count_ratio": 0.005434782608695652}, "very_slow_files": [{"fpath": "downloaded_repos/romadebrian_WEB-Sekolah/js/bootstrap.js", "ftime": 1.5887091159820557}, {"fpath": "downloaded_repos/romadebrian_WEB-Sekolah/js/jquery-ui.js", "ftime": 29.07665705680847}]}, "matching_time": {"total_time": 2.24080228805542, "per_file_and_rule_time": {"mean": 0.007807673477545014, "std_dev": 0.0002880788679492706}, "very_slow_stats": {"time_ratio": 0.08597136639199472, "count_ratio": 0.003484320557491289}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/romadebrian_WEB-Sekolah/js/bootstrap.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.19264483451843262}]}, "tainting_time": {"total_time": 0.8105640411376953, "per_def_and_rule_time": {"mean": 0.002936826236006144, "std_dev": 9.231819680973427e-05}, "very_slow_stats": {"time_ratio": 0.15032978876106257, "count_ratio": 0.0036231884057971015}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/romadebrian_WEB-Sekolah/js/bootstrap.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.12185192108154297}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090729792}, "engine_requested": "OSS", "skipped_rules": []}