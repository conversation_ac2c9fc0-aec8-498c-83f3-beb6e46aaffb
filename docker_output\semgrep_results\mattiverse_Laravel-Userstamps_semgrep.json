{"version": "1.130.0", "results": [], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mattiverse_Laravel-Userstamps/.github/workflows/tests.yml", "start": {"line": 45, "col": 56, "offset": 916}, "end": {"line": 45, "col": 72, "offset": 932}}, {"path": "downloaded_repos/mattiverse_Laravel-Userstamps/.github/workflows/tests.yml", "start": {"line": 45, "col": 96, "offset": 916}, "end": {"line": 45, "col": 112, "offset": 932}}, {"path": "downloaded_repos/mattiverse_Laravel-Userstamps/.github/workflows/tests.yml", "start": {"line": 45, "col": 140, "offset": 916}, "end": {"line": 45, "col": 158, "offset": 934}}]], "message": "Syntax error at line downloaded_repos/mattiverse_Laravel-Userstamps/.github/workflows/tests.yml:45:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ matrix.laravel` was unexpected", "path": "downloaded_repos/mattiverse_Laravel-Userstamps/.github/workflows/tests.yml", "spans": [{"file": "downloaded_repos/mattiverse_Laravel-Userstamps/.github/workflows/tests.yml", "start": {"line": 45, "col": 56, "offset": 916}, "end": {"line": 45, "col": 72, "offset": 932}}, {"file": "downloaded_repos/mattiverse_Laravel-Userstamps/.github/workflows/tests.yml", "start": {"line": 45, "col": 96, "offset": 916}, "end": {"line": 45, "col": 112, "offset": 932}}, {"file": "downloaded_repos/mattiverse_Laravel-Userstamps/.github/workflows/tests.yml", "start": {"line": 45, "col": 140, "offset": 916}, "end": {"line": 45, "col": 158, "offset": 934}}]}], "paths": {"scanned": ["downloaded_repos/mattiverse_<PERSON>vel-Userstamps/.editorconfig", "downloaded_repos/mattiverse_Laravel-Userstamps/.gitattributes", "downloaded_repos/mattiverse_Laravel-Userstamps/.github/workflows/tests.yml", "downloaded_repos/mattiverse_Laravel-Userstamps/.gitignore", "downloaded_repos/mattiverse_Laravel-Userstamps/.styleci.yml", "downloaded_repos/mattiverse_Laravel-Userstamps/LICENSE.md", "downloaded_repos/mattiverse_Laravel-Userstamps/README.md", "downloaded_repos/mattive<PERSON>_<PERSON><PERSON>-Userstamps/composer.json", "downloaded_repos/mattiverse_Laravel-Userstamps/logo.svg", "downloaded_repos/mattiverse_Laravel-Userstamps/phpunit.xml.dist", "downloaded_repos/mattiverse_<PERSON>vel-Userstamps/src/Listeners/Creating.php", "downloaded_repos/mattiverse_<PERSON>vel-Userstamps/src/Listeners/Deleting.php", "downloaded_repos/mattiverse_<PERSON>vel-Userstamps/src/Listeners/Restoring.php", "downloaded_repos/mattiverse_<PERSON>vel-Userstamps/src/Listeners/Updating.php", "downloaded_repos/mattiverse_<PERSON>vel-Userstamps/src/Traits/Userstamps.php", "downloaded_repos/mattiverse_Laravel-Userstamps/src/Userstamps.php", "downloaded_repos/mattiverse_<PERSON>vel-Userstamps/src/UserstampsScope.php", "downloaded_repos/mattiverse_<PERSON>vel-Userstamps/src/UserstampsServiceProvider.php"], "skipped": [{"path": "downloaded_repos/mattiverse_Laravel-Userstamps/.github/workflows/tests.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mattiverse_Laravel-Userstamps/tests/UserstampsTest.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.691612958908081, "profiling_times": {"config_time": 6.7785913944244385, "core_time": 2.242690324783325, "ignores_time": 0.0017960071563720703, "total_time": 9.023857116699219}, "parsing_time": {"total_time": 0.1631309986114502, "per_file_time": {"mean": 0.01483009078285911, "std_dev": 6.506102113798838e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.32706499099731445, "per_file_time": {"mean": 0.006958829595687544, "std_dev": 0.00011477109380850623}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.06184077262878418, "per_file_and_rule_time": {"mean": 0.0008962430815765824, "std_dev": 2.784343562187356e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.0, "per_def_and_rule_time": {"mean": 0.0, "std_dev": 0.0}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1091254080}, "engine_requested": "OSS", "skipped_rules": []}