{"version": "1.130.0", "results": [{"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/config/database.ts", "start": {"line": 7, "col": 50, "offset": 167}, "end": {"line": 7, "col": 90, "offset": 207}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/pluginpal_strapi-plugin-sitemap/server/services/__tests__/pattern.test.js", "start": {"line": 13, "col": 5, "offset": 265}, "end": {"line": 13, "col": 28, "offset": 288}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/pluginpal_strapi-plugin-sitemap/xsl/sitemap.xsl.js", "start": {"line": 37, "col": 5, "offset": 750}, "end": {"line": 37, "col": 53, "offset": 798}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/pluginpal_strapi-plugin-sitemap/.editorconfig", "downloaded_repos/pluginpal_strapi-plugin-sitemap/.eslintignore", "downloaded_repos/pluginpal_strapi-plugin-sitemap/.eslintrc", "downloaded_repos/pluginpal_strapi-plugin-sitemap/.gitattributes", "downloaded_repos/pluginpal_strapi-plugin-sitemap/.github/ISSUE_TEMPLATE/bug_report.md", "downloaded_repos/pluginpal_strapi-plugin-sitemap/.github/ISSUE_TEMPLATE/feature_request.md", "downloaded_repos/pluginpal_strapi-plugin-sitemap/.github/PULL_REQUEST_TEMPLATE.md", "downloaded_repos/pluginpal_strapi-plugin-sitemap/.github/workflows/publish.yml", "downloaded_repos/pluginpal_strapi-plugin-sitemap/.github/workflows/tests.yml", "downloaded_repos/pluginpal_strapi-plugin-sitemap/.gitignore", "downloaded_repos/pluginpal_strapi-plugin-sitemap/.yamllint.yml", "downloaded_repos/pluginpal_strapi-plugin-sitemap/CODE_OF_CONDUCT.md", "downloaded_repos/pluginpal_strapi-plugin-sitemap/CONTRIBUTING.md", "downloaded_repos/pluginpal_strapi-plugin-sitemap/LICENSE.md", "downloaded_repos/pluginpal_strapi-plugin-sitemap/MIGRATION.md", "downloaded_repos/pluginpal_strapi-plugin-sitemap/README.md", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/assets/images/logo.svg", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/components/CMEditViewExclude/index.jsx", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/components/Header/index.jsx", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/components/HostnameModal/index.jsx", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/components/Info/index.jsx", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/components/List/Collection/Row.jsx", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/components/List/Collection/index.jsx", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/components/List/Custom/Row.jsx", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/components/List/Custom/index.jsx", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/components/Loader/index.jsx", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/components/ModalForm/Collection/index.jsx", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/components/ModalForm/Custom/index.jsx", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/components/ModalForm/index.jsx", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/components/ModalForm/mapper.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/components/PluginIcon/index.jsx", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/components/SelectContentTypes/index.jsx", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/components/SelectLanguage/index.jsx", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/components/Tabs/index.jsx", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/config/constants.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/containers/App/index.jsx", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/containers/Main/index.jsx", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/helpers/configureStore.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/helpers/getTrad.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/helpers/pluginId.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/helpers/timeFormat.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/helpers/useActiveElement.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/index.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/permissions.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/state/actions/Sitemap.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/state/reducers/Sitemap/index.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/state/reducers/index.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/tabs/CollectionURLs/index.jsx", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/tabs/CustomURLs/index.jsx", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/tabs/Settings/index.jsx", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/translations/ar.json", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/translations/de.json", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/translations/en.json", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/translations/es.json", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/translations/fr.json", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/translations/index.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/translations/it.json", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/translations/ja.json", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/translations/ko.json", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/translations/nl.json", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/translations/pl.json", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/translations/pt-BR.json", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/translations/pt.json", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/translations/ru.json", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/translations/tr.json", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/translations/uk.json", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/translations/vi.json", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/translations/zh-Hans.json", "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/translations/zh.json", "downloaded_repos/pluginpal_strapi-plugin-sitemap/bin/strapi-sitemap", "downloaded_repos/pluginpal_strapi-plugin-sitemap/codecov.yml", "downloaded_repos/pluginpal_strapi-plugin-sitemap/jest.config.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/package.json", "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/.editorconfig", "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/.env", "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/.env.example", "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/.eslintignore", "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/.eslintrc", "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/.gitignore", "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/.strapi/client/app.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/.strapi/client/index.html", "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/README.md", "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/config/admin.ts", "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/config/api.ts", "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/config/database.ts", "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/config/env/ci/plugins.ts", "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/config/middlewares.ts", "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/config/plugins.ts", "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/config/server.ts", "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/database/migrations/.gitkeep", "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/favicon.png", "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/package.json", "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/public/robots.txt", "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/public/uploads/.gitkeep", "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/src/admin/app.example.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/src/admin/webpack.config.example.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/src/api/.gitkeep", "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/src/index.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/tsconfig.json", "downloaded_repos/pluginpal_strapi-plugin-sitemap/server/bootstrap.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/server/cli.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/server/config.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/server/content-types/index.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/server/content-types/sitemap/schema.json", "downloaded_repos/pluginpal_strapi-plugin-sitemap/server/content-types/sitemap_cache/schema.json", "downloaded_repos/pluginpal_strapi-plugin-sitemap/server/controllers/core.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/server/controllers/index.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/server/controllers/pattern.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/server/controllers/settings.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/server/register.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/server/routes/admin.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/server/routes/content-api.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/server/routes/index.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/server/services/__tests__/pattern.test.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/server/services/core.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/server/services/index.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/server/services/lifecycle.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/server/services/pattern.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/server/services/query.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/server/services/settings.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/server/utils/__tests__/index.test.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/server/utils/index.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/strapi-admin.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/strapi-server.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/tsconfig.json", "downloaded_repos/pluginpal_strapi-plugin-sitemap/xsl/sitemap.xsl", "downloaded_repos/pluginpal_strapi-plugin-sitemap/xsl/sitemap.xsl.css", "downloaded_repos/pluginpal_strapi-plugin-sitemap/xsl/sitemap.xsl.js", "downloaded_repos/pluginpal_strapi-plugin-sitemap/yarn.lock"], "skipped": [{"path": "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/config/env/test/database.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/config/env/test/plugins.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/config/env/test/server.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/src/api/test/content-types/test/schema.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/src/api/test/controllers/test.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/src/api/test/routes/test.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/src/api/test/services/test.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/tests/healthcheck.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluginpal_strapi-plugin-sitemap/playground/tests/helpers.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluginpal_strapi-plugin-sitemap/xsl/sortable.min.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.7323098182678223, "profiling_times": {"config_time": 7.57806658744812, "core_time": 3.629452705383301, "ignores_time": 0.002193450927734375, "total_time": 11.211021900177002}, "parsing_time": {"total_time": 1.4545161724090576, "per_file_time": {"mean": 0.014692082549586441, "std_dev": 0.0005617205763013007}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 8.164360284805298, "per_file_time": {"mean": 0.022869356540070863, "std_dev": 0.00689911166749223}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 3.0422654151916504, "per_file_and_rule_time": {"mean": 0.0067010251435939425, "std_dev": 0.00027244836974549833}, "very_slow_stats": {"time_ratio": 0.1270494019444544, "count_ratio": 0.006607929515418502}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/components/ModalForm/Collection/index.jsx", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.10086989402770996}, {"fpath": "downloaded_repos/pluginpal_strapi-plugin-sitemap/server/services/core.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.142441987991333}, {"fpath": "downloaded_repos/pluginpal_strapi-plugin-sitemap/admin/src/tabs/Settings/index.jsx", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.14320611953735352}]}, "tainting_time": {"total_time": 0.9150495529174805, "per_def_and_rule_time": {"mean": 0.002841768797880374, "std_dev": 6.927067027562918e-05}, "very_slow_stats": {"time_ratio": 0.11779689191963723, "count_ratio": 0.003105590062111801}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/pluginpal_strapi-plugin-sitemap/server/utils/index.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.10778999328613281}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}