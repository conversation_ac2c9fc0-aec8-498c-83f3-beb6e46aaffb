{"version": "1.130.0", "results": [{"check_id": "generic.secrets.security.detected-snyk-api-key.detected-snyk-api-key", "path": "downloaded_repos/webreinvent_vaahcms/CHANGELOG.md", "start": {"line": 3388, "col": 13, "offset": 392242}, "end": {"line": 3388, "col": 81, "offset": 392310}, "extra": {"message": "Snyk API Key detected", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "category": "security", "technology": ["secrets", "snyk"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-snyk-api-key.detected-snyk-api-key", "shortlink": "https://sg.run/lxO9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/webreinvent_vaahcms/Helpers/files.php", "start": {"line": 38, "col": 23, "offset": 1379}, "end": {"line": 38, "col": 36, "offset": 1392}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/webreinvent_vaahcms/Helpers/files.php", "start": {"line": 49, "col": 16, "offset": 1704}, "end": {"line": 49, "col": 29, "offset": 1717}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/webreinvent_vaahcms/Models/LanguageString.php", "start": {"line": 425, "col": 29, "offset": 12596}, "end": {"line": 425, "col": 69, "offset": 12636}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/layouts/App.vue", "start": {"line": 28, "col": 21, "offset": 836}, "end": {"line": 29, "col": 34, "offset": 932}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/layouts/App.vue", "start": {"line": 30, "col": 21, "offset": 988}, "end": {"line": 31, "col": 33, "offset": 1080}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/layouts/App.vue", "start": {"line": 43, "col": 17, "offset": 1533}, "end": {"line": 45, "col": 30, "offset": 1677}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/layouts/AppExtended.vue", "start": {"line": 48, "col": 21, "offset": 1263}, "end": {"line": 49, "col": 34, "offset": 1359}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/layouts/AppExtended.vue", "start": {"line": 50, "col": 21, "offset": 1415}, "end": {"line": 51, "col": 33, "offset": 1507}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/layouts/AppExtended.vue", "start": {"line": 63, "col": 17, "offset": 1960}, "end": {"line": 65, "col": 30, "offset": 2104}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/advanced/batches/components/Table.vue", "start": {"line": 169, "col": 25, "offset": 7780}, "end": {"line": 169, "col": 38, "offset": 7793}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/advanced/batches/components/Table.vue", "start": {"line": 182, "col": 25, "offset": 8272}, "end": {"line": 182, "col": 38, "offset": 8285}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/advanced/failedjobs/components/Table.vue", "start": {"line": 114, "col": 17, "offset": 4523}, "end": {"line": 114, "col": 30, "offset": 4536}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/advanced/jobs/components/Table.vue", "start": {"line": 127, "col": 17, "offset": 4671}, "end": {"line": 127, "col": 30, "offset": 4684}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/advanced/logs/Item.vue", "start": {"line": 139, "col": 25, "offset": 5403}, "end": {"line": 141, "col": 36, "offset": 5542}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/advanced/logs/components/Table.vue", "start": {"line": 99, "col": 17, "offset": 3741}, "end": {"line": 99, "col": 30, "offset": 3754}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/dashboard/Dashboard.vue", "start": {"line": 24, "col": 21, "offset": 618}, "end": {"line": 24, "col": 67, "offset": 664}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/dashboard/Dashboard.vue", "start": {"line": 161, "col": 41, "offset": 8571}, "end": {"line": 161, "col": 51, "offset": 8581}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/media/Item.vue", "start": {"line": 268, "col": 41, "offset": 12168}, "end": {"line": 268, "col": 63, "offset": 12190}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/registrations/Item.vue", "start": {"line": 236, "col": 37, "offset": 10452}, "end": {"line": 236, "col": 59, "offset": 10474}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/registrations/Item.vue", "start": {"line": 335, "col": 13, "offset": 15574}, "end": {"line": 335, "col": 35, "offset": 15596}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/settings/general/components/Securities.vue", "start": {"line": 22, "col": 25, "offset": 968}, "end": {"line": 22, "col": 35, "offset": 978}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/settings/localization/Index.vue", "start": {"line": 82, "col": 13, "offset": 2759}, "end": {"line": 82, "col": 23, "offset": 2769}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/settings/update/Index.vue", "start": {"line": 49, "col": 17, "offset": 1724}, "end": {"line": 49, "col": 27, "offset": 1734}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/settings/update/Index.vue", "start": {"line": 160, "col": 21, "offset": 8389}, "end": {"line": 160, "col": 32, "offset": 8400}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/settings/update/Index.vue", "start": {"line": 161, "col": 21, "offset": 8496}, "end": {"line": 161, "col": 32, "offset": 8507}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/taxonomies/Item.vue", "start": {"line": 214, "col": 33, "offset": 8050}, "end": {"line": 214, "col": 55, "offset": 8072}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/users/Item.vue", "start": {"line": 211, "col": 37, "offset": 9392}, "end": {"line": 211, "col": 59, "offset": 9414}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/users/Item.vue", "start": {"line": 233, "col": 13, "offset": 10239}, "end": {"line": 233, "col": 35, "offset": 10261}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/webreinvent_vaahcms/.github/FUNDING.yml", "downloaded_repos/webreinvent_vaahcms/.github/PULL_REQUEST_TEMPLATE/feature-template.md", "downloaded_repos/webreinvent_vaahcms/.github/PULL_REQUEST_TEMPLATE/release-template.md", "downloaded_repos/webreinvent_vaahcms/.gitignore", "downloaded_repos/webreinvent_vaahcms/.gitkeep", "downloaded_repos/webreinvent_vaahcms/CHANGELOG.md", "downloaded_repos/webreinvent_vaahcms/Config/.gitkeep", "downloaded_repos/webreinvent_vaahcms/Config/vaahcms.php", "downloaded_repos/webreinvent_vaahcms/Console/.gitkeep", "downloaded_repos/webreinvent_vaahcms/Database/Dbml/README.md", "downloaded_repos/webreinvent_vaahcms/Database/Dbml/vaahcms.dbml", "downloaded_repos/webreinvent_vaahcms/Database/Dbml/vaahcms.dbml.sql", "downloaded_repos/webreinvent_vaahcms/Database/Migrations/.gitkeep", "downloaded_repos/webreinvent_vaahcms/Database/Migrations/2019_05_13_202430_create_vh_users_table.php", "downloaded_repos/webreinvent_vaahcms/Database/Migrations/2019_05_14_201806_create_vh_registrations_table.php", "downloaded_repos/webreinvent_vaahcms/Database/Migrations/2019_05_14_202442_create_vh_roles_table.php", "downloaded_repos/webreinvent_vaahcms/Database/Migrations/2019_05_14_202449_create_vh_permissions_table.php", "downloaded_repos/webreinvent_vaahcms/Database/Migrations/2019_05_14_220712_create_vh_user_authorizations_table.php", "downloaded_repos/webreinvent_vaahcms/Database/Migrations/2019_05_14_220721_create_vh_role_permissions_table.php", "downloaded_repos/webreinvent_vaahcms/Database/Migrations/2019_05_14_221339_create_vh_user_roles_table.php", "downloaded_repos/webreinvent_vaahcms/Database/Migrations/2019_05_20_192222_create_vh_modules_table.php", "downloaded_repos/webreinvent_vaahcms/Database/Migrations/2019_05_24_001930_create_vh_settings_table.php", "downloaded_repos/webreinvent_vaahcms/Database/Migrations/2019_05_24_002715_create_vh_themes_table.php", "downloaded_repos/webreinvent_vaahcms/Database/Migrations/2019_05_24_004312_create_vh_migrations_table.php", "downloaded_repos/webreinvent_vaahcms/Database/Migrations/2019_05_26_144118_create_vh_theme_locations_table.php", "downloaded_repos/webreinvent_vaahcms/Database/Migrations/2019_05_26_175455_create_vh_theme_blocks_table.php", "downloaded_repos/webreinvent_vaahcms/Database/Migrations/2019_05_27_034209_create_vh_theme_templates_table.php", "downloaded_repos/webreinvent_vaahcms/Database/Migrations/2020_02_12_223736_create_vh_lang_languages_table.php", "downloaded_repos/webreinvent_vaahcms/Database/Migrations/2020_02_12_223811_create_vh_lang_categories_table.php", "downloaded_repos/webreinvent_vaahcms/Database/Migrations/2020_02_12_223820_create_vh_lang_strings_table.php", "downloaded_repos/webreinvent_vaahcms/Database/Migrations/2020_04_25_223820_create_vh_medias_table.php", "downloaded_repos/webreinvent_vaahcms/Database/Migrations/2020_04_25_223830_create_vh_mediable_table.php", "downloaded_repos/webreinvent_vaahcms/Database/Migrations/2020_04_26_223830_create_vh_notifications_table.php", "downloaded_repos/webreinvent_vaahcms/Database/Migrations/2020_04_26_223930_create_vh_notification_contents_table.php", "downloaded_repos/webreinvent_vaahcms/Database/Migrations/2020_04_26_223940_create_vh_notified_table.php", "downloaded_repos/webreinvent_vaahcms/Database/Migrations/2020_05_13_223140_create_vh_theme_template_fields_table.php", "downloaded_repos/webreinvent_vaahcms/Database/Migrations/2021_01_02_185505_create_jobs_table.php", "downloaded_repos/webreinvent_vaahcms/Database/Migrations/2021_01_02_185532_create_job_batches_table.php", "downloaded_repos/webreinvent_vaahcms/Database/Migrations/2021_01_02_185552_create_failed_jobs_table.php", "downloaded_repos/webreinvent_vaahcms/Database/Migrations/2021_08_27_165030_vh_taxonomy_types.php", "downloaded_repos/webreinvent_vaahcms/Database/Migrations/2021_08_27_165044_vh_taxonomies.php", "downloaded_repos/webreinvent_vaahcms/Database/Seeders/.gitkeep", "downloaded_repos/webreinvent_vaahcms/Database/Seeders/VaahCmsTableSeeder.php", "downloaded_repos/webreinvent_vaahcms/Database/Seeders/json/language_strings.json", "downloaded_repos/webreinvent_vaahcms/Database/Seeders/json/languages.json", "downloaded_repos/webreinvent_vaahcms/Database/Seeders/json/notification_contents.json", "downloaded_repos/webreinvent_vaahcms/Database/Seeders/json/notifications.json", "downloaded_repos/webreinvent_vaahcms/Database/Seeders/json/permissions.json", "downloaded_repos/webreinvent_vaahcms/Database/Seeders/json/roles.json", "downloaded_repos/webreinvent_vaahcms/Database/Seeders/json/settings.json", "downloaded_repos/webreinvent_vaahcms/Database/Seeders/json/taxonomies.json", "downloaded_repos/webreinvent_vaahcms/Database/Seeders/json/taxonomy_types.json", "downloaded_repos/webreinvent_vaahcms/Entities/Registration.php", "downloaded_repos/webreinvent_vaahcms/Entities/Taxonomy.php", "downloaded_repos/webreinvent_vaahcms/Entities/TaxonomyType.php", "downloaded_repos/webreinvent_vaahcms/Entities/Theme.php", "downloaded_repos/webreinvent_vaahcms/Entities/ThemeTemplate.php", "downloaded_repos/webreinvent_vaahcms/Entities/User.php", "downloaded_repos/webreinvent_vaahcms/Facades/VaahExcelFacade.php", "downloaded_repos/webreinvent_vaahcms/Facades/VaahFileFacade.php", "downloaded_repos/webreinvent_vaahcms/Helpers/actions.php", "downloaded_repos/webreinvent_vaahcms/Helpers/api.php", "downloaded_repos/webreinvent_vaahcms/Helpers/assets.php", "downloaded_repos/webreinvent_vaahcms/Helpers/backend.php", "downloaded_repos/webreinvent_vaahcms/Helpers/common.php", "downloaded_repos/webreinvent_vaahcms/Helpers/country.php", "downloaded_repos/webreinvent_vaahcms/Helpers/currency.php", "downloaded_repos/webreinvent_vaahcms/Helpers/env.php", "downloaded_repos/webreinvent_vaahcms/Helpers/filepond.php", "downloaded_repos/webreinvent_vaahcms/Helpers/files.php", "downloaded_repos/webreinvent_vaahcms/Helpers/html.php", "downloaded_repos/webreinvent_vaahcms/Helpers/json.php", "downloaded_repos/webreinvent_vaahcms/Helpers/modules.php", "downloaded_repos/webreinvent_vaahcms/Helpers/settings.php", "downloaded_repos/webreinvent_vaahcms/Helpers/themes.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/.gitkeep", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/Api/AuthController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/Api/PermissionsController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/Api/PublicController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/Api/RegistrationsController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/Api/RolesController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/Api/TaxonomiesController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/Api/TaxonomyTypesController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/Api/UsersController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/Backend/Advanced/BatchesController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/Backend/Advanced/FailedJobsController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/Backend/Advanced/JobsController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/Backend/Advanced/LogsController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/Backend/MediaController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/Backend/ModulesController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/Backend/PermissionsController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/Backend/ProfileController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/Backend/RegistrationsController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/Backend/RolesController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/Backend/Settings/EnvController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/Backend/Settings/GeneralController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/Backend/Settings/LocalizationController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/Backend/Settings/NotificationsController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/Backend/Settings/UpdateController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/Backend/Settings/UserSettingController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/Backend/TaxonomiesController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/Backend/ThemesController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/Backend/UsersController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/ComposerController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/DashboardController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/ExtendController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/Frontend/WelcomeController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/JsonController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/PublicController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/SampleController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/Settings/BackupsController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/Settings/SettingsController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/SetupController.php", "downloaded_repos/webreinvent_vaahcms/Http/Controllers/UiController.php", "downloaded_repos/webreinvent_vaahcms/Http/Middleware/.gitkeep", "downloaded_repos/webreinvent_vaahcms/Http/Middleware/HasBackendAccess.php", "downloaded_repos/webreinvent_vaahcms/Http/Middleware/IsHttps.php", "downloaded_repos/webreinvent_vaahcms/Http/Middleware/IsInstalled.php", "downloaded_repos/webreinvent_vaahcms/Http/Middleware/IsNotInstalled.php", "downloaded_repos/webreinvent_vaahcms/Http/Middleware/SetLocale.php", "downloaded_repos/webreinvent_vaahcms/Http/Middleware/SetThemeDetails.php", "downloaded_repos/webreinvent_vaahcms/Http/Requests/.gitkeep", "downloaded_repos/webreinvent_vaahcms/INSTRUCTIONS.md", "downloaded_repos/webreinvent_vaahcms/Jobs/ProcessMails.php", "downloaded_repos/webreinvent_vaahcms/Jobs/ProcessNotifications.php", "downloaded_repos/webreinvent_vaahcms/LICENSE", "downloaded_repos/webreinvent_vaahcms/Libraries/VaahBackup.php", "downloaded_repos/webreinvent_vaahcms/Libraries/VaahDB.php", "downloaded_repos/webreinvent_vaahcms/Libraries/VaahExcel.php", "downloaded_repos/webreinvent_vaahcms/Libraries/VaahFile.php", "downloaded_repos/webreinvent_vaahcms/Libraries/VaahHelper.php", "downloaded_repos/webreinvent_vaahcms/Libraries/VaahMail.php", "downloaded_repos/webreinvent_vaahcms/Libraries/VaahSeeder.php", "downloaded_repos/webreinvent_vaahcms/Libraries/VaahSetup.php", "downloaded_repos/webreinvent_vaahcms/Libraries/VaahStr.php", "downloaded_repos/webreinvent_vaahcms/Loaders/ModulesLoader.php", "downloaded_repos/webreinvent_vaahcms/Loaders/ThemesLoader.php", "downloaded_repos/webreinvent_vaahcms/Mail/GenericMail.php", "downloaded_repos/webreinvent_vaahcms/Mail/SecurityOtpMail.php", "downloaded_repos/webreinvent_vaahcms/Mail/TestMail.php", "downloaded_repos/webreinvent_vaahcms/Models/.gitkeep", "downloaded_repos/webreinvent_vaahcms/Models/Batch.php", "downloaded_repos/webreinvent_vaahcms/Models/BatchBase.php", "downloaded_repos/webreinvent_vaahcms/Models/FailedJob.php", "downloaded_repos/webreinvent_vaahcms/Models/FailedJobBase.php", "downloaded_repos/webreinvent_vaahcms/Models/Job.php", "downloaded_repos/webreinvent_vaahcms/Models/JobBase.php", "downloaded_repos/webreinvent_vaahcms/Models/Language.php", "downloaded_repos/webreinvent_vaahcms/Models/LanguageCategory.php", "downloaded_repos/webreinvent_vaahcms/Models/LanguageString.php", "downloaded_repos/webreinvent_vaahcms/Models/Media.php", "downloaded_repos/webreinvent_vaahcms/Models/MediaBase.php", "downloaded_repos/webreinvent_vaahcms/Models/Mediable.php", "downloaded_repos/webreinvent_vaahcms/Models/Migration.php", "downloaded_repos/webreinvent_vaahcms/Models/Module.php", "downloaded_repos/webreinvent_vaahcms/Models/ModuleBase.php", "downloaded_repos/webreinvent_vaahcms/Models/Notification.php", "downloaded_repos/webreinvent_vaahcms/Models/NotificationContent.php", "downloaded_repos/webreinvent_vaahcms/Models/Notified.php", "downloaded_repos/webreinvent_vaahcms/Models/Permission.php", "downloaded_repos/webreinvent_vaahcms/Models/PermissionBase.php", "downloaded_repos/webreinvent_vaahcms/Models/Registration.php", "downloaded_repos/webreinvent_vaahcms/Models/RegistrationBase.php", "downloaded_repos/webreinvent_vaahcms/Models/Role.php", "downloaded_repos/webreinvent_vaahcms/Models/RoleBase.php", "downloaded_repos/webreinvent_vaahcms/Models/Setting.php", "downloaded_repos/webreinvent_vaahcms/Models/Taxonomy.php", "downloaded_repos/webreinvent_vaahcms/Models/TaxonomyBase.php", "downloaded_repos/webreinvent_vaahcms/Models/TaxonomyType.php", "downloaded_repos/webreinvent_vaahcms/Models/Theme.php", "downloaded_repos/webreinvent_vaahcms/Models/ThemeBase.php", "downloaded_repos/webreinvent_vaahcms/Models/ThemeBlock.php", "downloaded_repos/webreinvent_vaahcms/Models/ThemeLocation.php", "downloaded_repos/webreinvent_vaahcms/Models/ThemeTemplate.php", "downloaded_repos/webreinvent_vaahcms/Models/ThemeTemplateField.php", "downloaded_repos/webreinvent_vaahcms/Models/User.php", "downloaded_repos/webreinvent_vaahcms/Models/UserBase.php", "downloaded_repos/webreinvent_vaahcms/Models/VaahModel.php", "downloaded_repos/webreinvent_vaahcms/Notifications/Notice.php", "downloaded_repos/webreinvent_vaahcms/Notifications/TestSmtp.php", "downloaded_repos/webreinvent_vaahcms/Observers/.gitkeep", "downloaded_repos/webreinvent_vaahcms/Observers/CrudObserver.php", "downloaded_repos/webreinvent_vaahcms/Observers/CrudWithUidObserver.php", "downloaded_repos/webreinvent_vaahcms/Observers/CrudWithUuidObserver.php", "downloaded_repos/webreinvent_vaahcms/Observers/UidObserver.php", "downloaded_repos/webreinvent_vaahcms/Providers/FacadesServiceProvider.php", "downloaded_repos/webreinvent_vaahcms/Providers/ModulesServiceProvider.php", "downloaded_repos/webreinvent_vaahcms/Providers/ThemesServiceProvider.php", "downloaded_repos/webreinvent_vaahcms/README.md", "downloaded_repos/webreinvent_vaahcms/Resources/assets/.gitkeep", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/common/fontawesome-6.2.0/css/all.css", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/common/fontawesome-6.2.0/css/all.min.css", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/common/fontawesome-6.2.0/css/brands.css", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/common/fontawesome-6.2.0/css/brands.min.css", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/common/fontawesome-6.2.0/css/fontawesome.css", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/common/fontawesome-6.2.0/css/fontawesome.min.css", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/common/fontawesome-6.2.0/css/regular.css", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/common/fontawesome-6.2.0/css/regular.min.css", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/common/fontawesome-6.2.0/css/solid.css", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/common/fontawesome-6.2.0/css/solid.min.css", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/common/fontawesome-6.2.0/css/svg-with-js.css", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/common/fontawesome-6.2.0/css/svg-with-js.min.css", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/common/fontawesome-6.2.0/css/v4-font-face.css", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/common/fontawesome-6.2.0/css/v4-font-face.min.css", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/common/fontawesome-6.2.0/css/v4-shims.css", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/common/fontawesome-6.2.0/css/v4-shims.min.css", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/common/fontawesome-6.2.0/css/v5-font-face.css", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/common/fontawesome-6.2.0/css/v5-font-face.min.css", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/common/fontawesome-6.2.0/webfonts/fa-brands-400.ttf", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/common/fontawesome-6.2.0/webfonts/fa-brands-400.woff2", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/common/fontawesome-6.2.0/webfonts/fa-regular-400.ttf", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/common/fontawesome-6.2.0/webfonts/fa-regular-400.woff2", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/common/fontawesome-6.2.0/webfonts/fa-solid-900.ttf", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/common/fontawesome-6.2.0/webfonts/fa-solid-900.woff2", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/common/fontawesome-6.2.0/webfonts/fa-v4compatibility.ttf", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/common/fontawesome-6.2.0/webfonts/fa-v4compatibility.woff2", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/mix-manifest.json", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/builds/app-extended.js.LICENSE.txt", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/builds/app.js.LICENSE.txt", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/css/build.css", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/css/style.css", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/images/log-device.png", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/images/vaahcms-logo-device.png", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/images/vaahcms-logo.svg", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/.gitkeep", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/buefy-build.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/buefy.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_autocomplete.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_carousel.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_checkbox.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_clockpicker.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_collapse.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_colorpicker.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_datepicker.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_dialog.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_dropdown.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_form.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_icon.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_image.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_loading.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_menu.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_message.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_modal.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_navbar.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_notices.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_notification.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_numberinput.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_pagination.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_progress.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_radio.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_rate.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_select.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_sidebar.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_skeleton.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_slider.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_steps.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_switch.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_table.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_tabs.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_tag.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_taginput.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_timepicker.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_tooltip.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/components/_upload.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/utils/_all.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/utils/_animations.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/utils/_functions.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/utils/_helpers.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/utils/_variables-ext.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/buefy-0.9.19/utils/_variables.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/build.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/LICENSE", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/README.md", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/base/_all.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/base/_animations.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/base/_generic.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/base/_helpers.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/base/_minireset.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/bulma.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/components/_all.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/components/_breadcrumb.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/components/_card.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/components/_dropdown.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/components/_level.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/components/_media.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/components/_menu.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/components/_message.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/components/_modal.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/components/_navbar.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/components/_pagination.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/components/_panel.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/components/_tabs.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/elements/_all.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/elements/_box.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/elements/_button.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/elements/_container.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/elements/_content.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/elements/_form.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/elements/_icon.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/elements/_image.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/elements/_notification.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/elements/_other.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/elements/_progress.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/elements/_table.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/elements/_tag.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/elements/_title.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/form/_all.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/form/_checkbox-radio.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/form/_file.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/form/_input-textarea.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/form/_select.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/form/_shared.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/form/_tools.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/grid/_all.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/grid/_columns.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/grid/_tiles.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/helpers/_all.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/helpers/_color.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/helpers/_flexbox.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/helpers/_float.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/helpers/_other.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/helpers/_overflow.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/helpers/_position.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/helpers/_spacing.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/helpers/_typography.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/helpers/_visibility.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/layout/_all.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/layout/_footer.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/layout/_hero.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/layout/_section.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/package.json", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/utilities/_all.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/utilities/_animations.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/utilities/_controls.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/utilities/_derived-variables.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/utilities/_extends.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/utilities/_functions.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/utilities/_initial-variables.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-0.9.3/utilities/_mixins.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/bulma-helpers.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/helpers/_all.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/helpers/border/_all.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/helpers/border/borders.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/helpers/cursor/_all.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/helpers/cursor/cursors.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/helpers/flex/_all.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/helpers/flex/align-justify.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/helpers/flex/align-self-items.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/helpers/flex/core.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/helpers/media-queries/_all.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/helpers/media-queries/flex/_all.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/helpers/media-queries/flex/align-justify.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/helpers/media-queries/flex/align-self-items.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/helpers/media-queries/flex/core.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/helpers/media-queries/sizing/_all.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/helpers/media-queries/spacing/_all.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/helpers/misc/_all.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/helpers/misc/misc.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/helpers/sizing/_all.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/helpers/sizing/max-min-width-height.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/helpers/sizing/misc.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/helpers/sizing/width-height.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/helpers/spacing/_all.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/helpers/spacing/margin-padding.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/mixins/_all.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/mixins/general.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/mixins/media-queries.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/mixins/responsiveness.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/variables/_all.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/variables/customization.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/variables/media-queries.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/variables/responsiveness.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/variables/sizing.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/variables/spacing.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/bulma-helpers/sass/variables/units.sass", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/customize/buefy.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/customize/bulma.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/customize/element-plus.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/customize/variables.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/oruga-bulma-0.2.6/bulma-build.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/oruga-bulma-0.2.6/bulma.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/oruga-bulma-0.2.6/components/_autocomplete.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/oruga-bulma-0.2.6/components/_button.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/oruga-bulma-0.2.6/components/_carousel.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/oruga-bulma-0.2.6/components/_checkbox.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/oruga-bulma-0.2.6/components/_datepicker.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/oruga-bulma-0.2.6/components/_dropdown.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/oruga-bulma-0.2.6/components/_form.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/oruga-bulma-0.2.6/components/_icon.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/oruga-bulma-0.2.6/components/_loading.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/oruga-bulma-0.2.6/components/_modal.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/oruga-bulma-0.2.6/components/_notification.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/oruga-bulma-0.2.6/components/_pagination.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/oruga-bulma-0.2.6/components/_radio.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/oruga-bulma-0.2.6/components/_select.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/oruga-bulma-0.2.6/components/_sidebar.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/oruga-bulma-0.2.6/components/_skeleton.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/oruga-bulma-0.2.6/components/_slider.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/oruga-bulma-0.2.6/components/_steps.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/oruga-bulma-0.2.6/components/_switch.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/oruga-bulma-0.2.6/components/_table.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/oruga-bulma-0.2.6/components/_tabs.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/oruga-bulma-0.2.6/components/_taginput.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/oruga-bulma-0.2.6/components/_timepicker.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/oruga-bulma-0.2.6/components/_tooltip.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/oruga-bulma-0.2.6/components/_upload.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/oruga-bulma-0.2.6/components/utils/_all.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/oruga-bulma-0.2.6/components/utils/_animations.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/oruga-bulma-0.2.6/components/utils/_functions.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/oruga-bulma-0.2.6/components/utils/_helpers.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/oruga-bulma-0.2.6/components/utils/_variables-ext.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/oruga-bulma-0.2.6/components/utils/_variables.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/scss/style.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/mix-manifest.json", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/_draggable.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/_fonts.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/_primeicons.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/build.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/fonts/Inter-Bold.woff", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/fonts/Inter-Bold.woff2", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/fonts/Inter-Light.woff", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/fonts/Inter-Light.woff2", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/fonts/Inter-Medium.woff", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/fonts/Inter-Medium.woff2", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/fonts/Inter-Regular.woff", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/fonts/Inter-Regular.woff2", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/fonts/Inter-SemiBold.woff", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/fonts/Inter-SemiBold.woff2", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/fonts/primeicons.eot", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/fonts/primeicons.svg", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/fonts/primeicons.ttf", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/fonts/primeicons.woff", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/global/_functions.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/global/_math.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/global/_vars.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/global/_vars_tailwind_light.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/global/all.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/mix-manifest.json", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primeflex-3.3.0/primeflex.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primeflex-3.3.0/src/_animation.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primeflex-3.3.0/src/_border.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primeflex-3.3.0/src/_borderradius.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primeflex-3.3.0/src/_colors.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primeflex-3.3.0/src/_display.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primeflex-3.3.0/src/_elevation.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primeflex-3.3.0/src/_flexbox.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primeflex-3.3.0/src/_formlayout.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primeflex-3.3.0/src/_gap.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primeflex-3.3.0/src/_grid.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primeflex-3.3.0/src/_image.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primeflex-3.3.0/src/_liststyle.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primeflex-3.3.0/src/_misc.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primeflex-3.3.0/src/_mixins.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primeflex-3.3.0/src/_overflow.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primeflex-3.3.0/src/_padding.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primeflex-3.3.0/src/_position.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primeflex-3.3.0/src/_size.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primeflex-3.3.0/src/_spacing.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primeflex-3.3.0/src/_transform.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primeflex-3.3.0/src/_transition.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primeflex-3.3.0/src/_typography.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primeflex-3.3.0/src/_userselect.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primeflex-3.3.0/src/_utils.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primeflex-3.3.0/src/_variables.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primeflex-3.3.0/src/_zindex.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-3.29.2/images/color.png", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-3.29.2/images/hue.png", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-3.29.2/primevue.css", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/_extensions.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/_variables.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/_colors.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/_common.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/_components.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/_mixins.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/button/_button.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/button/_speeddial.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/button/_splitbutton.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/data/_carousel.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/data/_datatable.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/data/_dataview.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/data/_filter.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/data/_fullcalendar.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/data/_orderlist.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/data/_organizationchart.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/data/_paginator.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/data/_picklist.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/data/_timeline.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/data/_tree.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/data/_treetable.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/file/_fileupload.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/input/_autocomplete.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/input/_calendar.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/input/_cascadeselect.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/input/_checkbox.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/input/_chips.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/input/_colorpicker.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/input/_dropdown.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/input/_editor.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/input/_inputgroup.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/input/_inputnumber.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/input/_inputswitch.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/input/_inputtext.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/input/_listbox.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/input/_multiselect.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/input/_password.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/input/_radiobutton.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/input/_rating.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/input/_selectbutton.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/input/_slider.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/input/_togglebutton.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/input/_treeselect.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/menu/_breadcrumb.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/menu/_contextmenu.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/menu/_dock.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/menu/_megamenu.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/menu/_menu.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/menu/_menubar.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/menu/_panelmenu.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/menu/_steps.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/menu/_tabmenu.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/menu/_tieredmenu.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/messages/_inlinemessage.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/messages/_message.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/messages/_toast.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/misc/_avatar.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/misc/_badge.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/misc/_blockui.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/misc/_chip.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/misc/_inplace.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/misc/_progressbar.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/misc/_progressspinner.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/misc/_scrolltop.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/misc/_skeleton.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/misc/_tag.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/misc/_terminal.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/multimedia/_galleria.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/multimedia/_image.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/overlay/_confirmpopup.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/overlay/_dialog.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/overlay/_overlaypanel.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/overlay/_sidebar.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/overlay/_tooltip.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/panel/_accordion.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/panel/_card.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/panel/_divider.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/panel/_fieldset.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/panel/_panel.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/panel/_scrollpanel.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/panel/_splitter.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/panel/_tabview.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/designer/components/panel/_toolbar.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/primevue-tailwind-light/theme.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/style/style.scss", "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/scss/style-just-for-backup.scss", "downloaded_repos/webreinvent_vaahcms/Resources/lang/.gitkeep", "downloaded_repos/webreinvent_vaahcms/Resources/lang/en/messages.php", "downloaded_repos/webreinvent_vaahcms/Resources/lang/en/validation.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/.gitkeep", "downloaded_repos/webreinvent_vaahcms/Resources/views/backend/vaahone/components/aside-logo.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/backend/vaahone/components/aside-menu.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/backend/vaahone/components/errors.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/backend/vaahone/components/flash.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/backend/vaahone/components/top-left-menu.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/backend/vaahone/components/top-right-menu.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/backend/vaahone/components/top-right-user-menu.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/backend/vaahone/components/top-search-bar.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/backend/vaahone/extend/aside-menu.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/backend/vaahone/extend/dashboard.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/backend/vaahone/extend/extendable.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/backend/vaahone/extend/settings-menu.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/backend/vaahone/extend/top-left-menu.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/backend/vaahone/extend/top-right-menu.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/backend/vaahone/extend/user-menu.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/backend/vaahone/layouts/backend.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/backend/vaahone/layouts/default.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/backend/vaahone/layouts/ui.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/backend/vaahone/pages/index.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/backend/vaahone/pages/ui/index.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/backend/vaahtwo/components/errors.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/backend/vaahtwo/components/flash.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/backend/vaahtwo/components/head.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/backend/vaahtwo/layouts/backend.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/backend/vaahtwo/layouts/default.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/backend/vaahtwo/pages/index.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/backend/vaahtwo/pages/vaahextend.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/frontend/default.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/frontend/layouts/default.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/frontend/layouts/simple.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/frontend/partials/errors.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/frontend/partials/flash.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/frontend/partials/head.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/frontend/partials/scripts.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/frontend/theme-welcome.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/frontend/welcome.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/mails/default.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/mails/layout.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/mails/security-otp.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/templates/env.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/templates/env_v2.blade.php", "downloaded_repos/webreinvent_vaahcms/Resources/views/templates/lang.blade.php", "downloaded_repos/webreinvent_vaahcms/Routes/api/api-routes-batches.php", "downloaded_repos/webreinvent_vaahcms/Routes/api/api-routes-failedjobs.php", "downloaded_repos/webreinvent_vaahcms/Routes/api/api-routes-jobs.php", "downloaded_repos/webreinvent_vaahcms/Routes/api/api-routes-media.php", "downloaded_repos/webreinvent_vaahcms/Routes/api/api-routes-medias.php", "downloaded_repos/webreinvent_vaahcms/Routes/api/api-routes-modules.php", "downloaded_repos/webreinvent_vaahcms/Routes/api/api-routes-permissions.php", "downloaded_repos/webreinvent_vaahcms/Routes/api/api-routes-registrations.php", "downloaded_repos/webreinvent_vaahcms/Routes/api/api-routes-roles.php", "downloaded_repos/webreinvent_vaahcms/Routes/api/api-routes-taxonomies.php", "downloaded_repos/webreinvent_vaahcms/Routes/api/api-routes-themes.php", "downloaded_repos/webreinvent_vaahcms/Routes/api/api-routes-users.php", "downloaded_repos/webreinvent_vaahcms/Routes/api.php", "downloaded_repos/webreinvent_vaahcms/Routes/backend/advanced.php", "downloaded_repos/webreinvent_vaahcms/Routes/backend/general.php", "downloaded_repos/webreinvent_vaahcms/Routes/backend/profile.php", "downloaded_repos/webreinvent_vaahcms/Routes/backend/routes-media.php", "downloaded_repos/webreinvent_vaahcms/Routes/backend/routes-modules.php", "downloaded_repos/webreinvent_vaahcms/Routes/backend/routes-permissions.php", "downloaded_repos/webreinvent_vaahcms/Routes/backend/routes-registrations.php", "downloaded_repos/webreinvent_vaahcms/Routes/backend/routes-roles.php", "downloaded_repos/webreinvent_vaahcms/Routes/backend/routes-taxonomies.php", "downloaded_repos/webreinvent_vaahcms/Routes/backend/routes-themes.php", "downloaded_repos/webreinvent_vaahcms/Routes/backend/routes-users.php", "downloaded_repos/webreinvent_vaahcms/Routes/backend/settings.php", "downloaded_repos/webreinvent_vaahcms/Routes/backend/setup.php", "downloaded_repos/webreinvent_vaahcms/Routes/backend/ui.php", "downloaded_repos/webreinvent_vaahcms/Routes/backend.php", "downloaded_repos/webreinvent_vaahcms/Routes/frontend.php", "downloaded_repos/webreinvent_vaahcms/Tests/.gitkeep", "downloaded_repos/webreinvent_vaahcms/Tests/Browser/Data/login.json", "downloaded_repos/webreinvent_vaahcms/Tests/Browser/LoginTest.php", "downloaded_repos/webreinvent_vaahcms/Tests/Browser/Pages/LoginPage.php", "downloaded_repos/webreinvent_vaahcms/Tests/Browser/Pages/Page.php", "downloaded_repos/webreinvent_vaahcms/Traits/.gitkeep", "downloaded_repos/webreinvent_vaahcms/Traits/CrudObservantTrait.php", "downloaded_repos/webreinvent_vaahcms/Traits/CrudWithUidObservantTrait.php", "downloaded_repos/webreinvent_vaahcms/Traits/CrudWithUuidObservantTrait.php", "downloaded_repos/webreinvent_vaahcms/Traits/UidObservantTrait.php", "downloaded_repos/webreinvent_vaahcms/VaahCms.php", "downloaded_repos/webreinvent_vaahcms/VaahCmsFacade.php", "downloaded_repos/webreinvent_vaahcms/VaahCmsServiceProvider.php", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/assets/data/country.json", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/assets/data/customers-large.json", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/assets/data/products-orders-small.json", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/assets/data/products-orders.json", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/assets/data/products-small.json", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/assets/data/products.json", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/molecules/Aside.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/molecules/Copyright.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/molecules/Logo.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/molecules/Notices.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/molecules/SettingsRadios.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/molecules/Sidebar.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/molecules/Topnav.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/molecules/TopnavExtended.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/Account.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/Configuration.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/Dependencies.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/EnvVariableSettings.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/Footer.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/GeneralSettings.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/HelloWorld.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/LocalizationSettings.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/Migrate.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/NestedDraggable.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/NotificationSettings.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/ProductListDemo.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/Sidebar.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/TaxonomiesModal.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/TheWelcome.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/Topnav.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/AccordionComponent.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/ButtonsComponent.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/CheckboxComponent.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/CodePreview.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/Colors.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/ConfirmDialogComponent.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/ConfirmPopupComponent.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/ContextMenuComponent.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/DataTableComponent.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/DialogComponent.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/DropdownComponent.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/FieldSetComponent.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/HelperTextComponent.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/InlineMessageComponent.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/InputComponent.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/Level.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/LightButtons.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/ListboxComponent.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/Media.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/MenubarComponent.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/MessagesComponent.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/MulitSelectComponent.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/MultilipleSelectButtonComponent.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/OverlayMenuComponent.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/OverlayPanelComponent.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/PanelComponent.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/PlainMenuComponent.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/Radio.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/SelectButtonComponent.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/Sidebar.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/SwitchComponent.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/TabViewComponent.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/TieredMenuComponent.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/TimelineComponent.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/ToastsComponent.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UI-elements/ToggleButtonComponent.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UpdateSettings.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UserDetails.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UserSettings.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/UsersForm.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/components/organisms/WelcomeItem.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/index-extended.html", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/index.html", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/jsconfig.json", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/layouts/App.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/layouts/AppExtended.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/layouts/Auth.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/layouts/Backend.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/layouts/Default.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/layouts/Public.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/main-extended.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/main.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/mix-manifest.json", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/package.json", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/NotFound.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/advanced/AdvancedLayout.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/advanced/batches/List.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/advanced/batches/components/Actions.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/advanced/batches/components/Filters.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/advanced/batches/components/Table.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/advanced/failedjobs/List.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/advanced/failedjobs/components/Actions.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/advanced/failedjobs/components/Filters.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/advanced/failedjobs/components/Table.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/advanced/jobs/List.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/advanced/jobs/components/Actions.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/advanced/jobs/components/Filters.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/advanced/jobs/components/Table.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/advanced/logs/Item.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/advanced/logs/List.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/advanced/logs/components/Actions.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/advanced/logs/components/Filters.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/advanced/logs/components/Table.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/dashboard/Dashboard.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/media/Form.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/media/Item.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/media/List.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/media/components/Actions.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/media/components/FileUploader.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/media/components/Filters.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/media/components/Table.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/modules/Form.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/modules/Install.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/modules/Item.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/modules/List.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/modules/components/Actions.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/modules/components/Table.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/permissions/Form.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/permissions/Item.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/permissions/List.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/permissions/ViewRole.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/permissions/components/Actions.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/permissions/components/Filters.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/permissions/components/RoleDetasilsView.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/permissions/components/Table.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/profile/components/FileUploader.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/profile/index.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/public/404.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/public/ForgotPassword.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/public/ResetPassword.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/public/Signin.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/public/Signup.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/public/setup/Index.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/public/setup/intall/Account.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/public/setup/intall/Configuration.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/public/setup/intall/Dependencies.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/public/setup/intall/Index.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/public/setup/intall/Migrate.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/registrations/Form.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/registrations/Item.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/registrations/List.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/registrations/components/Actions.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/registrations/components/Filters.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/registrations/components/Table.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/roles/Form.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/roles/Item.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/roles/List.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/roles/ViewPermission.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/roles/ViewUser.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/roles/components/Actions.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/roles/components/Filters.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/roles/components/PermissionDetailsView.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/roles/components/RoleUserDetailsView.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/roles/components/Table.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/settings/SettingsLayout.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/settings/backups/Index.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/settings/backups/IndexJs.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/settings/env/Index.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/settings/general/Index.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/settings/general/components/DateTime.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/settings/general/components/MetaTags.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/settings/general/components/Scripts.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/settings/general/components/Securities.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/settings/general/components/SiteSettings.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/settings/general/components/SocialMediaLink.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/settings/localization/Index.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/settings/notifications/List.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/settings/notifications/components/Actions.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/settings/notifications/components/Filters.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/settings/notifications/components/Index.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/settings/partials/AsideMenu.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/settings/template/Index.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/settings/template/IndexJs.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/settings/update/Index.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/settings/user-settings/Index.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/taxonomies/Form.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/taxonomies/Item.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/taxonomies/List.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/taxonomies/components/Actions.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/taxonomies/components/Filters.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/taxonomies/components/Table.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/taxonomies/components/TaxonomyTypeModal.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/themes/Form.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/themes/Install.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/themes/Item.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/themes/List.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/themes/components/Actions.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/themes/components/Filters.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/themes/components/Table.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/ui/ContentTypes.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/ui/Dashboard.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/ui/Extend.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/ui/ForgotPassword.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/ui/Index.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/ui/Install.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/ui/Media.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/ui/MenuContent.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/ui/MultiFactorAuth.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/ui/Pages.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/ui/PrivatePages.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/ui/Profile.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/ui/PublicPages.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/ui/Settings.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/ui/Setup.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/ui/SignUp.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/ui/Signin.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/ui/Taxonomies.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/ui/UiElements.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/ui/Users.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/users/Form.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/users/Item.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/users/List.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/users/ViewRole.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/users/components/Actions.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/users/components/FileUploader.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/users/components/Filters.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/pages/users/components/Table.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/routes/router.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/routes/routes.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/routes/vue-routes-advanced.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/routes/vue-routes-dashboard.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/routes/vue-routes-install.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/routes/vue-routes-media.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/routes/vue-routes-modules.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/routes/vue-routes-permissions.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/routes/vue-routes-profile.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/routes/vue-routes-public.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/routes/vue-routes-registrations.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/routes/vue-routes-roles.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/routes/vue-routes-settings.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/routes/vue-routes-taxonomies.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/routes/vue-routes-themes.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/routes/vue-routes-ui.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/routes/vue-routes-users.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/service/CountryService.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/service/CustomerService.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/service/EditorService.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/service/ProductService.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/advanced/store-batches.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/advanced/store-failedjobs.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/advanced/store-jobs.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/advanced/store-logs.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/auth.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/dashboard.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/root.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/settings/store-env.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/settings/store-general_setting.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/settings/store-localization.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/settings/store-notification.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/settings/store-update.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/settings/store-user_setting.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/setup.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-media.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-modules.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-permissions.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-profile.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-registrations.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-roles.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-settings.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-taxonomies.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-themes.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-users.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/vaahvue/pinia/vaah.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/vaahvue/vue-three/primeflex/Loader.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/vaahvue/vue-three/primeflex/VhField.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/vaahvue/vue-three/primeflex/VhFieldVertical.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/vaahvue/vue-three/primeflex/VhViewRow.vue", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/vite.config.js", "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/webpack.mix.js", "downloaded_repos/webreinvent_vaahcms/changelog-template.hbs", "downloaded_repos/webreinvent_vaahcms/composer.json", "downloaded_repos/webreinvent_vaahcms/mix-manifest.json", "downloaded_repos/webreinvent_vaahcms/vaah-config.json"], "skipped": [{"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/build/fonts/Inter-Bold.woff", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/build/fonts/Inter-Bold.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/build/fonts/Inter-Light.woff", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/build/fonts/Inter-Light.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/build/fonts/Inter-Medium.woff", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/build/fonts/Inter-Medium.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/build/fonts/Inter-Regular.woff", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/build/fonts/Inter-Regular.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/build/fonts/Inter-SemiBold.woff", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/build/fonts/Inter-SemiBold.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/build/fonts/primeicons.eot", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/build/fonts/primeicons.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/build/fonts/primeicons.ttf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/build/fonts/primeicons.woff", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/build/mix-manifest.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/build/vaahone.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/build/vaahtwo.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/builds/app-extended.js", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahone/builds/app.js", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/build/Sidebar.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/build/Sidebar.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/build/build.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/build/fonts/Inter-Bold.woff", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/build/fonts/Inter-Bold.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/build/fonts/Inter-Light.woff", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/build/fonts/Inter-Light.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/build/fonts/Inter-Medium.woff", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/build/fonts/Inter-Medium.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/build/fonts/Inter-Regular.woff", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/build/fonts/Inter-Regular.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/build/fonts/Inter-SemiBold.woff", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/build/fonts/Inter-SemiBold.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/build/index-extended.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/build/index-extended.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/build/index.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/build/index.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/build/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/build/main.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/build/mainExtended.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/build/mix-manifest.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/webreinvent_vaahcms/Resources/assets/backend/vaahtwo/build/quill.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.5933620929718018, "profiling_times": {"config_time": 6.262336492538452, "core_time": 8.984100818634033, "ignores_time": 0.0017392635345458984, "total_time": 15.249271392822266}, "parsing_time": {"total_time": 7.141461610794067, "per_file_time": {"mean": 0.022109788268712283, "std_dev": 0.002007502476316661}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 63.161325216293335, "per_file_time": {"mean": 0.02933642601778607, "std_dev": 0.05634961270660697}, "very_slow_stats": {"time_ratio": 0.6558713557785523, "count_ratio": 0.0074314909428704135}, "very_slow_files": [{"fpath": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/advanced/store-logs.js", "ftime": 2.5042638778686523}, {"fpath": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-taxonomies.js", "ftime": 2.8237500190734863}, {"fpath": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-media.js", "ftime": 2.828155040740967}, {"fpath": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-settings.js", "ftime": 2.8603219985961914}, {"fpath": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-permissions.js", "ftime": 2.8814830780029297}, {"fpath": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-registrations.js", "ftime": 2.90972900390625}, {"fpath": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-themes.js", "ftime": 3.020066022872925}, {"fpath": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-modules.js", "ftime": 3.088557004928589}, {"fpath": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-roles.js", "ftime": 3.2113828659057617}, {"fpath": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-users.js", "ftime": 4.137042999267578}]}, "matching_time": {"total_time": 27.78081202507019, "per_file_and_rule_time": {"mean": 0.03268330826478846, "std_dev": 0.006868154752458292}, "very_slow_stats": {"time_ratio": 0.6923888878528349, "count_ratio": 0.09411764705882353}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-themes.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.48749589920043945}, {"fpath": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-settings.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.4888899326324463}, {"fpath": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-themes.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.4989969730377197}, {"fpath": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-settings.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.5226790904998779}, {"fpath": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-permissions.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.5277290344238281}, {"fpath": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-registrations.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.5348210334777832}, {"fpath": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-modules.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.5512909889221191}, {"fpath": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-users.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.5631840229034424}, {"fpath": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-roles.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.5678040981292725}, {"fpath": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-media.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.5751481056213379}]}, "tainting_time": {"total_time": 8.887601137161255, "per_def_and_rule_time": {"mean": 0.0009908139506311319, "std_dev": 1.9186582318535933e-05}, "very_slow_stats": {"time_ratio": 0.08568903633876283, "count_ratio": 0.0014492753623188406}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-themes.js", "fline": 1, "rule_id": "javascript.express.security.require-request.require-request", "time": 0.055155038833618164}, {"fpath": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-roles.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.05699586868286133}, {"fpath": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-modules.js", "fline": 1, "rule_id": "javascript.express.security.require-request.require-request", "time": 0.0577390193939209}, {"fpath": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-users.js", "fline": 1, "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.05823516845703125}, {"fpath": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-modules.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.05971384048461914}, {"fpath": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-settings.js", "fline": 1, "rule_id": "javascript.express.security.cors-misconfiguration.cors-misconfiguration", "time": 0.06000494956970215}, {"fpath": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-users.js", "fline": 1, "rule_id": "javascript.express.security.audit.express-open-redirect.express-open-redirect", "time": 0.0631098747253418}, {"fpath": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-modules.js", "fline": 1, "rule_id": "javascript.express.security.cors-misconfiguration.cors-misconfiguration", "time": 0.06467819213867188}, {"fpath": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-taxonomies.js", "fline": 1, "rule_id": "javascript.express.security.x-frame-options-misconfiguration.x-frame-options-misconfiguration", "time": 0.06493210792541504}, {"fpath": "downloaded_repos/webreinvent_vaahcms/Vue/vaahtwo/stores/store-permissions.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.06616997718811035}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1101055104}, "engine_requested": "OSS", "skipped_rules": []}