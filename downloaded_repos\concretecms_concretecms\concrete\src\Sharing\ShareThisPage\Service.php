<?php
namespace Concrete\Core\Sharing\ShareThisPage;

use Concrete\Core\Page\Page;
use Concrete\Core\Sharing\SocialNetwork\Service as SocialNetworkService;
use Request;
use URL;

class Service extends SocialNetworkService
{
    public static function getByHandle($ssHandle)
    {
        $services = ServiceList::get();
        foreach ($services as $s) {
            if ($s->getHandle() == $ssHandle) {
                return $s;
            }
        }
    }

    public function getServiceLinkTarget(): string
    {
        // @TODO - perhaps make this more modular and defined within each service's config.
        if (in_array($this->getHandle(), ['email', 'print'])) {
            return '';
        } else {
            return '_blank';
        }
    }

    public function getServiceLink(?Page $c = null)
    {
        if (!is_object($c)) {
            $req = Request::getInstance();
            $c = $req->getCurrentPage();
            if($c) {
            	$url = urlencode(URL::to($c));
            } else {
            	$url = urlencode($req->getUri());
            }
        } elseif (!$c->isError()) {
            $url = urlencode(URL::to($c));
        }

        if (is_object($c) && !$c->isError()) {
            $title = $c->getCollectionName();
        } else {
            $title = \Core::make('site')->getSite()->getSiteName();
        }

        if (!empty($url)) {
            switch ($this->getHandle()) {
                case 'facebook':
                    return "https://www.facebook.com/sharer/sharer.php?u=$url";
                case 'twitter':
                    return "https://twitter.com/intent/tweet?url=$url";
                case 'linkedin':
                    return "https://www.linkedin.com/shareArticle?mini-true&amp;url={$url}&amp;title=".urlencode($title);
                case 'pinterest':
                    return "https://www.pinterest.com/pin/create/button?url=$url";
                case 'google_plus':
                    return "https://plus.google.com/share?url=$url";
                case 'reddit':
                    return "https://www.reddit.com/submit?url={$url}";
                case 'bluesky':
                    return 'https://bsky.app/intent/compose?text=' . rawurlencode($title . "\n") . $url;
                case 'print':
                    return "javascript:window.print();";
                case 'email':
                    $body = rawurlencode(t("Check out this article on %s:\n\n%s\n%s", tc('SiteName', \Core::make('site')->getSite()->getSiteName()), $title, urldecode($url)));
                    $subject = rawurlencode(t('Thought you\'d enjoy this article.'));

                    return "mailto:?body={$body}&amp;subject={$subject}";
            }
        }
    }
}
