{"version": "1.130.0", "results": [], "errors": [], "paths": {"scanned": ["downloaded_repos/HynekPetrak_sshame/.gitignore", "downloaded_repos/HynekPetrak_sshame/LICENSE", "downloaded_repos/HynekPetrak_sshame/README.md", "downloaded_repos/HynekPetrak_sshame/pyproject.toml", "downloaded_repos/HynekPetrak_sshame/sshame/__init__.py", "downloaded_repos/HynekPetrak_sshame/sshame/__main__.py", "downloaded_repos/HynekPetrak_sshame/sshame/db.py", "downloaded_repos/HynekPetrak_sshame/sshame/main.py", "downloaded_repos/HynekPetrak_sshame/sshame.png"], "skipped": [{"path": "downloaded_repos/HynekPetrak_sshame/test/keys/dsa_key", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/HynekPetrak_sshame/test/keys/dsa_key.pub", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/HynekPetrak_sshame/test/keys/ecdsa_key", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/HynekPetrak_sshame/test/keys/ecdsa_key.pub", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/HynekPetrak_sshame/test/keys/ed25519_key", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/HynekPetrak_sshame/test/keys/ed25519_key.pub", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/HynekPetrak_sshame/test/keys/rsa_key", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/HynekPetrak_sshame/test/keys/rsa_key.pub", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.9934189319610596, "profiling_times": {"config_time": 6.703620433807373, "core_time": 4.117689847946167, "ignores_time": 0.0031180381774902344, "total_time": 10.825303792953491}, "parsing_time": {"total_time": 0.08526182174682617, "per_file_time": {"mean": 0.021315455436706543, "std_dev": 0.0003309844432095588}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.7115178108215332, "per_file_time": {"mean": 0.0777962641282515, "std_dev": 0.09585584760613074}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 1.0601627826690674, "per_file_and_rule_time": {"mean": 0.01859934706436961, "std_dev": 0.0008887814834602746}, "very_slow_stats": {"time_ratio": 0.34585715747490525, "count_ratio": 0.05263157894736842}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/HynekPetrak_sshame/sshame/main.py", "rule_id": "python.django.security.injection.sql.sql-injection-using-db-cursor-execute.sql-injection-db-cursor-execute", "time": 0.10715198516845703}, {"fpath": "downloaded_repos/HynekPetrak_sshame/sshame/main.py", "rule_id": "python.lang.security.dangerous-subprocess-use.dangerous-subprocess-use", "time": 0.11707687377929688}, {"fpath": "downloaded_repos/HynekPetrak_sshame/sshame/main.py", "rule_id": "python.django.security.injection.path-traversal.path-traversal-open.path-traversal-open", "time": 0.14243602752685547}]}, "tainting_time": {"total_time": 0.31784892082214355, "per_def_and_rule_time": {"mean": 0.0006909759148307468, "std_dev": 1.1068491865677997e-05}, "very_slow_stats": {"time_ratio": 0.18012384156380917, "count_ratio": 0.002173913043478261}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/HynekPetrak_sshame/sshame/main.py", "fline": 611, "rule_id": "python.sqlalchemy.security.audit.avoid-sqlalchemy-text.avoid-sqlalchemy-text", "time": 0.05725216865539551}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089364160}, "engine_requested": "OSS", "skipped_rules": []}