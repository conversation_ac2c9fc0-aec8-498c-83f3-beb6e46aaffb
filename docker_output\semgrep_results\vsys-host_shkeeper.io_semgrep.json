{"version": "1.130.0", "results": [{"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/vsys-host_shkeeper.io/Dockerfile", "start": {"line": 11, "col": 1, "offset": 168}, "end": {"line": 19, "col": 28, "offset": 357}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\n<PERSON><PERSON> gunicorn \\\n    --access-logfile - \\\n    --reload \\\n    --workers 1 \\\n    --threads 32 \\\n    --worker-class gthread \\\n    --timeout 30 \\\n    -b 0.0.0.0:5000 \\\n    \"shkeeper:create_app()\"", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/avalanche.py", "start": {"line": 34, "col": 13, "offset": 1243}, "end": {"line": 34, "col": 83, "offset": 1313}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/avalanche.py", "start": {"line": 42, "col": 17, "offset": 1508}, "end": {"line": 42, "col": 64, "offset": 1555}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/bitcoin_like_crypto.py", "start": {"line": 15, "col": 17, "offset": 322}, "end": {"line": 15, "col": 43, "offset": 348}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/bitcoin_like_crypto.py", "start": {"line": 28, "col": 17, "offset": 745}, "end": {"line": 28, "col": 43, "offset": 771}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/bitcoin_like_crypto.py", "start": {"line": 48, "col": 13, "offset": 1470}, "end": {"line": 48, "col": 39, "offset": 1496}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/bitcoin_like_crypto.py", "start": {"line": 56, "col": 13, "offset": 1749}, "end": {"line": 56, "col": 39, "offset": 1775}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/bitcoin_like_crypto.py", "start": {"line": 72, "col": 13, "offset": 2203}, "end": {"line": 72, "col": 39, "offset": 2229}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/bitcoin_like_crypto.py", "start": {"line": 81, "col": 13, "offset": 2504}, "end": {"line": 81, "col": 39, "offset": 2530}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/bitcoin_like_crypto.py", "start": {"line": 114, "col": 13, "offset": 3549}, "end": {"line": 114, "col": 39, "offset": 3575}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/bitcoin_like_crypto.py", "start": {"line": 125, "col": 13, "offset": 3935}, "end": {"line": 125, "col": 39, "offset": 3961}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/bitcoin_like_crypto.py", "start": {"line": 136, "col": 13, "offset": 4370}, "end": {"line": 136, "col": 39, "offset": 4396}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/bnb.py", "start": {"line": 34, "col": 13, "offset": 1191}, "end": {"line": 34, "col": 83, "offset": 1261}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/bnb.py", "start": {"line": 42, "col": 17, "offset": 1456}, "end": {"line": 42, "col": 64, "offset": 1503}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/ethereum.py", "start": {"line": 28, "col": 13, "offset": 819}, "end": {"line": 28, "col": 74, "offset": 880}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/ethereum.py", "start": {"line": 36, "col": 13, "offset": 1078}, "end": {"line": 36, "col": 73, "offset": 1138}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/ethereum.py", "start": {"line": 46, "col": 17, "offset": 1468}, "end": {"line": 46, "col": 65, "offset": 1516}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/ethereum.py", "start": {"line": 63, "col": 13, "offset": 2029}, "end": {"line": 63, "col": 63, "offset": 2079}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/ethereum.py", "start": {"line": 71, "col": 17, "offset": 2274}, "end": {"line": 71, "col": 64, "offset": 2321}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/ethereum.py", "start": {"line": 90, "col": 13, "offset": 2925}, "end": {"line": 90, "col": 70, "offset": 2982}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/ethereum.py", "start": {"line": 98, "col": 13, "offset": 3193}, "end": {"line": 98, "col": 70, "offset": 3250}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/ethereum.py", "start": {"line": 108, "col": 13, "offset": 3589}, "end": {"line": 108, "col": 58, "offset": 3634}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/ethereum.py", "start": {"line": 128, "col": 13, "offset": 4512}, "end": {"line": 128, "col": 83, "offset": 4582}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/ethereum.py", "start": {"line": 135, "col": 13, "offset": 4771}, "end": {"line": 135, "col": 65, "offset": 4823}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/ethereum.py", "start": {"line": 148, "col": 21, "offset": 5278}, "end": {"line": 148, "col": 55, "offset": 5312}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/ethereum.py", "start": {"line": 158, "col": 13, "offset": 5676}, "end": {"line": 158, "col": 71, "offset": 5734}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/polygon.py", "start": {"line": 34, "col": 13, "offset": 1219}, "end": {"line": 34, "col": 83, "offset": 1289}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/polygon.py", "start": {"line": 42, "col": 17, "offset": 1484}, "end": {"line": 42, "col": 64, "offset": 1531}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/solana.py", "start": {"line": 34, "col": 13, "offset": 1209}, "end": {"line": 34, "col": 83, "offset": 1279}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/solana.py", "start": {"line": 42, "col": 17, "offset": 1474}, "end": {"line": 42, "col": 64, "offset": 1521}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/tron_token.py", "start": {"line": 34, "col": 17, "offset": 941}, "end": {"line": 34, "col": 65, "offset": 989}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/tron_token.py", "start": {"line": 47, "col": 17, "offset": 1352}, "end": {"line": 47, "col": 64, "offset": 1399}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/tron_token.py", "start": {"line": 66, "col": 13, "offset": 2002}, "end": {"line": 66, "col": 70, "offset": 2059}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/tron_token.py", "start": {"line": 74, "col": 13, "offset": 2279}, "end": {"line": 74, "col": 72, "offset": 2338}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/tron_token.py", "start": {"line": 94, "col": 13, "offset": 2882}, "end": {"line": 94, "col": 58, "offset": 2927}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/tron_token.py", "start": {"line": 109, "col": 13, "offset": 3393}, "end": {"line": 109, "col": 73, "offset": 3453}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/tron_token.py", "start": {"line": 118, "col": 13, "offset": 3788}, "end": {"line": 118, "col": 74, "offset": 3849}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/tron_token.py", "start": {"line": 131, "col": 13, "offset": 4409}, "end": {"line": 131, "col": 83, "offset": 4479}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/tron_token.py", "start": {"line": 138, "col": 13, "offset": 4656}, "end": {"line": 138, "col": 63, "offset": 4706}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/tron_token.py", "start": {"line": 145, "col": 13, "offset": 4895}, "end": {"line": 145, "col": 65, "offset": 4947}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/tron_token.py", "start": {"line": 153, "col": 13, "offset": 5155}, "end": {"line": 153, "col": 72, "offset": 5214}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/tron_token.py", "start": {"line": 160, "col": 13, "offset": 5424}, "end": {"line": 160, "col": 84, "offset": 5495}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/tron_token.py", "start": {"line": 167, "col": 13, "offset": 5662}, "end": {"line": 167, "col": 47, "offset": 5696}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/tron_token.py", "start": {"line": 172, "col": 13, "offset": 5819}, "end": {"line": 172, "col": 63, "offset": 5869}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/xrp.py", "start": {"line": 36, "col": 13, "offset": 1297}, "end": {"line": 36, "col": 83, "offset": 1367}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/xrp.py", "start": {"line": 44, "col": 17, "offset": 1562}, "end": {"line": 44, "col": 64, "offset": 1609}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/js/custom-manage.js", "start": {"line": 527, "col": 7, "offset": 14749}, "end": {"line": 527, "col": 34, "offset": 14776}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/js/custom-manage.js", "start": {"line": 664, "col": 13, "offset": 19391}, "end": {"line": 664, "col": 41, "offset": 19419}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/js/custom-payout.js", "start": {"line": 132, "col": 7, "offset": 3848}, "end": {"line": 132, "col": 34, "offset": 3875}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/js/custom-payouts.js", "start": {"line": 298, "col": 13, "offset": 7287}, "end": {"line": 298, "col": 82, "offset": 7356}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/js/custom-payouts.js", "start": {"line": 411, "col": 9, "offset": 10270}, "end": {"line": 411, "col": 62, "offset": 10323}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/js/custom-payouts.js", "start": {"line": 523, "col": 9, "offset": 13868}, "end": {"line": 523, "col": 80, "offset": 13939}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/js/custom-transactions.js", "start": {"line": 298, "col": 13, "offset": 7287}, "end": {"line": 298, "col": 82, "offset": 7356}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/js/custom-transactions.js", "start": {"line": 411, "col": 9, "offset": 10270}, "end": {"line": 411, "col": 62, "offset": 10323}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/js/custom-transactions.js", "start": {"line": 522, "col": 9, "offset": 13882}, "end": {"line": 522, "col": 80, "offset": 13953}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/js/custom-wallets.js", "start": {"line": 32, "col": 17, "offset": 987}, "end": {"line": 32, "col": 82, "offset": 1052}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/js/custom-wallets.js", "start": {"line": 44, "col": 9, "offset": 1317}, "end": {"line": 44, "col": 110, "offset": 1418}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/js/custom-wallets.js", "start": {"line": 100, "col": 17, "offset": 2898}, "end": {"line": 100, "col": 55, "offset": 2936}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/js/custom-wallets.js", "start": {"line": 113, "col": 13, "offset": 3277}, "end": {"line": 113, "col": 52, "offset": 3316}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/js/custom-wallets.js", "start": {"line": 120, "col": 13, "offset": 3560}, "end": {"line": 120, "col": 52, "offset": 3599}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/js/custom-wallets.js", "start": {"line": 127, "col": 13, "offset": 3819}, "end": {"line": 127, "col": 52, "offset": 3858}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/js/tron_multiserver.js", "start": {"line": 17, "col": 9, "offset": 827}, "end": {"line": 17, "col": 74, "offset": 892}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/js/tron_multiserver.js", "start": {"line": 27, "col": 9, "offset": 1119}, "end": {"line": 27, "col": 74, "offset": 1184}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.unescaped-template-extension.unescaped-template-extension", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/wallet.py", "start": {"line": 71, "col": 12, "offset": 1748}, "end": {"line": 71, "col": 65, "offset": 1801}, "extra": {"message": "Flask does not automatically escape Jinja templates unless they have .html, .htm, .xml, or .xhtml extensions. This could lead to XSS attacks. Use .html, .htm, .xml, or .xhtml for your template extensions. See https://flask.palletsprojects.com/en/1.1.x/templating/#jinja-setup for more information.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "source-rule-url": "https://pypi.org/project/flake8-flask/", "references": ["https://flask.palletsprojects.com/en/1.1.x/templating/#jinja-setup", "https://semgrep.dev/blog/2020/bento-check-unescaped-template-extensions-in-flask/", "https://bento.dev/checks/flask/unescaped-file-extension/"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.unescaped-template-extension.unescaped-template-extension", "shortlink": "https://sg.run/x1Rg"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.unescaped-template-extension.unescaped-template-extension", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/wallet.py", "start": {"line": 107, "col": 12, "offset": 2785}, "end": {"line": 107, "col": 100, "offset": 2873}, "extra": {"message": "Flask does not automatically escape Jinja templates unless they have .html, .htm, .xml, or .xhtml extensions. This could lead to XSS attacks. Use .html, .htm, .xml, or .xhtml for your template extensions. See https://flask.palletsprojects.com/en/1.1.x/templating/#jinja-setup for more information.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "source-rule-url": "https://pypi.org/project/flake8-flask/", "references": ["https://flask.palletsprojects.com/en/1.1.x/templating/#jinja-setup", "https://semgrep.dev/blog/2020/bento-check-unescaped-template-extensions-in-flask/", "https://bento.dev/checks/flask/unescaped-file-extension/"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.unescaped-template-extension.unescaped-template-extension", "shortlink": "https://sg.run/x1Rg"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.unescaped-template-extension.unescaped-template-extension", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/wallet.py", "start": {"line": 141, "col": 12, "offset": 3848}, "end": {"line": 148, "col": 6, "offset": 4057}, "extra": {"message": "Flask does not automatically escape Jinja templates unless they have .html, .htm, .xml, or .xhtml extensions. This could lead to XSS attacks. Use .html, .htm, .xml, or .xhtml for your template extensions. See https://flask.palletsprojects.com/en/1.1.x/templating/#jinja-setup for more information.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "source-rule-url": "https://pypi.org/project/flake8-flask/", "references": ["https://flask.palletsprojects.com/en/1.1.x/templating/#jinja-setup", "https://semgrep.dev/blog/2020/bento-check-unescaped-template-extensions-in-flask/", "https://bento.dev/checks/flask/unescaped-file-extension/"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.unescaped-template-extension.unescaped-template-extension", "shortlink": "https://sg.run/x1Rg"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.unescaped-template-extension.unescaped-template-extension", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/wallet.py", "start": {"line": 163, "col": 12, "offset": 4472}, "end": {"line": 170, "col": 6, "offset": 4738}, "extra": {"message": "Flask does not automatically escape Jinja templates unless they have .html, .htm, .xml, or .xhtml extensions. This could lead to XSS attacks. Use .html, .htm, .xml, or .xhtml for your template extensions. See https://flask.palletsprojects.com/en/1.1.x/templating/#jinja-setup for more information.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "source-rule-url": "https://pypi.org/project/flake8-flask/", "references": ["https://flask.palletsprojects.com/en/1.1.x/templating/#jinja-setup", "https://semgrep.dev/blog/2020/bento-check-unescaped-template-extensions-in-flask/", "https://bento.dev/checks/flask/unescaped-file-extension/"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.unescaped-template-extension.unescaped-template-extension", "shortlink": "https://sg.run/x1Rg"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.unescaped-template-extension.unescaped-template-extension", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/wallet.py", "start": {"line": 203, "col": 12, "offset": 5697}, "end": {"line": 207, "col": 6, "offset": 5862}, "extra": {"message": "Flask does not automatically escape Jinja templates unless they have .html, .htm, .xml, or .xhtml extensions. This could lead to XSS attacks. Use .html, .htm, .xml, or .xhtml for your template extensions. See https://flask.palletsprojects.com/en/1.1.x/templating/#jinja-setup for more information.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "source-rule-url": "https://pypi.org/project/flake8-flask/", "references": ["https://flask.palletsprojects.com/en/1.1.x/templating/#jinja-setup", "https://semgrep.dev/blog/2020/bento-check-unescaped-template-extensions-in-flask/", "https://bento.dev/checks/flask/unescaped-file-extension/"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.unescaped-template-extension.unescaped-template-extension", "shortlink": "https://sg.run/x1Rg"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.unescaped-template-extension.unescaped-template-extension", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/wallet.py", "start": {"line": 323, "col": 12, "offset": 9857}, "end": {"line": 329, "col": 6, "offset": 10089}, "extra": {"message": "Flask does not automatically escape Jinja templates unless they have .html, .htm, .xml, or .xhtml extensions. This could lead to XSS attacks. Use .html, .htm, .xml, or .xhtml for your template extensions. See https://flask.palletsprojects.com/en/1.1.x/templating/#jinja-setup for more information.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "source-rule-url": "https://pypi.org/project/flake8-flask/", "references": ["https://flask.palletsprojects.com/en/1.1.x/templating/#jinja-setup", "https://semgrep.dev/blog/2020/bento-check-unescaped-template-extensions-in-flask/", "https://bento.dev/checks/flask/unescaped-file-extension/"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.unescaped-template-extension.unescaped-template-extension", "shortlink": "https://sg.run/x1Rg"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.unescaped-template-extension.unescaped-template-extension", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/wallet.py", "start": {"line": 335, "col": 12, "offset": 10156}, "end": {"line": 340, "col": 6, "offset": 10385}, "extra": {"message": "Flask does not automatically escape Jinja templates unless they have .html, .htm, .xml, or .xhtml extensions. This could lead to XSS attacks. Use .html, .htm, .xml, or .xhtml for your template extensions. See https://flask.palletsprojects.com/en/1.1.x/templating/#jinja-setup for more information.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "source-rule-url": "https://pypi.org/project/flake8-flask/", "references": ["https://flask.palletsprojects.com/en/1.1.x/templating/#jinja-setup", "https://semgrep.dev/blog/2020/bento-check-unescaped-template-extensions-in-flask/", "https://bento.dev/checks/flask/unescaped-file-extension/"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.unescaped-template-extension.unescaped-template-extension", "shortlink": "https://sg.run/x1Rg"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.unescaped-template-extension.unescaped-template-extension", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/wallet.py", "start": {"line": 394, "col": 12, "offset": 12127}, "end": {"line": 398, "col": 6, "offset": 12249}, "extra": {"message": "Flask does not automatically escape Jinja templates unless they have .html, .htm, .xml, or .xhtml extensions. This could lead to XSS attacks. Use .html, .htm, .xml, or .xhtml for your template extensions. See https://flask.palletsprojects.com/en/1.1.x/templating/#jinja-setup for more information.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "source-rule-url": "https://pypi.org/project/flake8-flask/", "references": ["https://flask.palletsprojects.com/en/1.1.x/templating/#jinja-setup", "https://semgrep.dev/blog/2020/bento-check-unescaped-template-extensions-in-flask/", "https://bento.dev/checks/flask/unescaped-file-extension/"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.unescaped-template-extension.unescaped-template-extension", "shortlink": "https://sg.run/x1Rg"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.unescaped-template-extension.unescaped-template-extension", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/wallet.py", "start": {"line": 413, "col": 12, "offset": 12712}, "end": {"line": 415, "col": 6, "offset": 12816}, "extra": {"message": "Flask does not automatically escape Jinja templates unless they have .html, .htm, .xml, or .xhtml extensions. This could lead to XSS attacks. Use .html, .htm, .xml, or .xhtml for your template extensions. See https://flask.palletsprojects.com/en/1.1.x/templating/#jinja-setup for more information.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "source-rule-url": "https://pypi.org/project/flake8-flask/", "references": ["https://flask.palletsprojects.com/en/1.1.x/templating/#jinja-setup", "https://semgrep.dev/blog/2020/bento-check-unescaped-template-extensions-in-flask/", "https://bento.dev/checks/flask/unescaped-file-extension/"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.unescaped-template-extension.unescaped-template-extension", "shortlink": "https://sg.run/x1Rg"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.unescaped-template-extension.unescaped-template-extension", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/wallet.py", "start": {"line": 446, "col": 16, "offset": 13487}, "end": {"line": 448, "col": 10, "offset": 13585}, "extra": {"message": "Flask does not automatically escape Jinja templates unless they have .html, .htm, .xml, or .xhtml extensions. This could lead to XSS attacks. Use .html, .htm, .xml, or .xhtml for your template extensions. See https://flask.palletsprojects.com/en/1.1.x/templating/#jinja-setup for more information.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "source-rule-url": "https://pypi.org/project/flake8-flask/", "references": ["https://flask.palletsprojects.com/en/1.1.x/templating/#jinja-setup", "https://semgrep.dev/blog/2020/bento-check-unescaped-template-extensions-in-flask/", "https://bento.dev/checks/flask/unescaped-file-extension/"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.unescaped-template-extension.unescaped-template-extension", "shortlink": "https://sg.run/x1Rg"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.unescaped-template-extension.unescaped-template-extension", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/wallet.py", "start": {"line": 460, "col": 20, "offset": 14009}, "end": {"line": 462, "col": 14, "offset": 14119}, "extra": {"message": "Flask does not automatically escape Jinja templates unless they have .html, .htm, .xml, or .xhtml extensions. This could lead to XSS attacks. Use .html, .htm, .xml, or .xhtml for your template extensions. See https://flask.palletsprojects.com/en/1.1.x/templating/#jinja-setup for more information.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "source-rule-url": "https://pypi.org/project/flake8-flask/", "references": ["https://flask.palletsprojects.com/en/1.1.x/templating/#jinja-setup", "https://semgrep.dev/blog/2020/bento-check-unescaped-template-extensions-in-flask/", "https://bento.dev/checks/flask/unescaped-file-extension/"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.unescaped-template-extension.unescaped-template-extension", "shortlink": "https://sg.run/x1Rg"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.unescaped-template-extension.unescaped-template-extension", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/wallet.py", "start": {"line": 466, "col": 20, "offset": 14371}, "end": {"line": 468, "col": 14, "offset": 14481}, "extra": {"message": "Flask does not automatically escape Jinja templates unless they have .html, .htm, .xml, or .xhtml extensions. This could lead to XSS attacks. Use .html, .htm, .xml, or .xhtml for your template extensions. See https://flask.palletsprojects.com/en/1.1.x/templating/#jinja-setup for more information.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "source-rule-url": "https://pypi.org/project/flake8-flask/", "references": ["https://flask.palletsprojects.com/en/1.1.x/templating/#jinja-setup", "https://semgrep.dev/blog/2020/bento-check-unescaped-template-extensions-in-flask/", "https://bento.dev/checks/flask/unescaped-file-extension/"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.unescaped-template-extension.unescaped-template-extension", "shortlink": "https://sg.run/x1Rg"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.unescaped-template-extension.unescaped-template-extension", "path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/wallet.py", "start": {"line": 471, "col": 20, "offset": 14661}, "end": {"line": 473, "col": 14, "offset": 14770}, "extra": {"message": "Flask does not automatically escape Jinja templates unless they have .html, .htm, .xml, or .xhtml extensions. This could lead to XSS attacks. Use .html, .htm, .xml, or .xhtml for your template extensions. See https://flask.palletsprojects.com/en/1.1.x/templating/#jinja-setup for more information.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "source-rule-url": "https://pypi.org/project/flake8-flask/", "references": ["https://flask.palletsprojects.com/en/1.1.x/templating/#jinja-setup", "https://semgrep.dev/blog/2020/bento-check-unescaped-template-extensions-in-flask/", "https://bento.dev/checks/flask/unescaped-file-extension/"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.unescaped-template-extension.unescaped-template-extension", "shortlink": "https://sg.run/x1Rg"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/vsys-host_shkeeper.io/.dockerignore", "downloaded_repos/vsys-host_shkeeper.io/.github/issue_create_auto_reply.md", "downloaded_repos/vsys-host_shkeeper.io/.github/workflows/ci-dev.yml", "downloaded_repos/vsys-host_shkeeper.io/.github/workflows/ci.yml", "downloaded_repos/vsys-host_shkeeper.io/.github/workflows/issue_create_auto_reply.yml", "downloaded_repos/vsys-host_shkeeper.io/.gitignore", "downloaded_repos/vsys-host_shkeeper.io/Dockerfile", "downloaded_repos/vsys-host_shkeeper.io/LICENSE", "downloaded_repos/vsys-host_shkeeper.io/README.md", "downloaded_repos/vsys-host_shkeeper.io/contrib/shkeeper-change-password.py", "downloaded_repos/vsys-host_shkeeper.io/docs/multipayout.md", "downloaded_repos/vsys-host_shkeeper.io/migrations/README", "downloaded_repos/vsys-host_shkeeper.io/migrations/alembic.ini", "downloaded_repos/vsys-host_shkeeper.io/migrations/env.py", "downloaded_repos/vsys-host_shkeeper.io/migrations/script.py.mako", "downloaded_repos/vsys-host_shkeeper.io/migrations/versions/0319bf7b9426_change_uniq_constraints_for_transaction_.py", "downloaded_repos/vsys-host_shkeeper.io/migrations/versions/cd6076e578ca_add_fee_policies.py", "downloaded_repos/vsys-host_shkeeper.io/requirements.txt", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/__init__.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/api_v1.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/auth.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/callback.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/events.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/exceptions.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/models.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/avalanche.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/bitcoin_like_crypto.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/bnb.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/crypto.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/ethereum.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/polygon.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/rate_source.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/solana.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/tron_token.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/xrp.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/cryptos/__init__.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/cryptos/avalanche-usdc.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/cryptos/avalanche-usdt.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/cryptos/avax.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/cryptos/bitcoin_lightning.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/cryptos/bnb-usdc.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/cryptos/bnb-usdt.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/cryptos/bnb.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/cryptos/btc.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/cryptos/doge.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/cryptos/eth-pyusd.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/cryptos/eth-usdc.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/cryptos/eth-usdt.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/cryptos/eth.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/cryptos/ltc.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/cryptos/matic.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/cryptos/monero.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/cryptos/polygon-usdc.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/cryptos/polygon-usdt.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/cryptos/sol.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/cryptos/solana-pyusd.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/cryptos/solana-usdc.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/cryptos/solana-usdt.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/cryptos/trx.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/cryptos/usdc.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/cryptos/usdt.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/cryptos/xrp.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/rates/__init__.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/rates/binance.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/rates/coinbase.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/rates/kraken.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/rates/kucoin.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/rates/manual.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/css/custom-main.dark.css", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/css/custom-main.light.css", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/css/daterangepicker.dark.css", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/css/daterangepicker.light.css", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/css/main.min.dark.css", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/css/main.min.light.css", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/images/icons.dark.svg", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/images/icons.light.svg", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/images/vsys-logo-small.dark.png", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/images/vsys-logo-small.light.png", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/images/vsys-logo.dark.png", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/images/vsys-logo.light.png", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/js/custom-manage.js", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/js/custom-payout.js", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/js/custom-payouts.js", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/js/custom-rates.js", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/js/custom-server.js", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/js/custom-transactions.js", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/js/custom-wallets.js", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/js/mobile-menu.js", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/js/tron_multiserver.js", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/tasks.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/templates/auth/login.j2", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/templates/auth/set-password.j2", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/templates/base.j2", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/templates/wallet/manage.j2", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/templates/wallet/manage_server_bitcoinlightning.j2", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/templates/wallet/manage_server_crypto.j2", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/templates/wallet/manage_server_trontoken.j2", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/templates/wallet/manage_server_trontoken_status.j2", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/templates/wallet/page.j2", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/templates/wallet/payout.j2", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/templates/wallet/payout_btc_lightning.j2", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/templates/wallet/payout_eth.j2", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/templates/wallet/payout_eth_coin.j2", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/templates/wallet/payout_tron.j2", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/templates/wallet/payouts.j2", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/templates/wallet/payouts_table.j2", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/templates/wallet/rates.j2", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/templates/wallet/transactions.j2", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/templates/wallet/transactions_table.j2", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/templates/wallet/unlock_key_input.j2", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/templates/wallet/unlock_setup.j2", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/templates/wallet/unlock_unlocked.j2", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/templates/wallet/wallets.j2", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/utils.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/wallet.py", "downloaded_repos/vsys-host_shkeeper.io/shkeeper/wallet_encryption.py"], "skipped": [{"path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/js/daterangepicker.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/js/jquery.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/js/moment.min.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.8072168827056885, "profiling_times": {"config_time": 6.318922996520996, "core_time": 4.823907136917114, "ignores_time": 0.0022008419036865234, "total_time": 11.146517276763916}, "parsing_time": {"total_time": 1.1867034435272217, "per_file_time": {"mean": 0.01671413300742566, "std_dev": 0.0008393961436254291}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 18.295183420181274, "per_file_time": {"mean": 0.0603801433009283, "std_dev": 0.05052808882020761}, "very_slow_stats": {"time_ratio": 0.17804904039210712, "count_ratio": 0.006600660066006601}, "very_slow_files": [{"fpath": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/cryptos/bitcoin_lightning.py", "ftime": 1.5548079013824463}, {"fpath": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/api_v1.py", "ftime": 1.702631950378418}]}, "matching_time": {"total_time": 10.122925758361816, "per_file_and_rule_time": {"mean": 0.012166978074954109, "std_dev": 0.0011993550326218317}, "very_slow_stats": {"time_ratio": 0.34748211835766335, "count_ratio": 0.021634615384615384}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/wallet.py", "rule_id": "python.lang.security.dangerous-system-call.dangerous-system-call", "time": 0.1764819622039795}, {"fpath": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/js/custom-payout.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.17655301094055176}, {"fpath": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/static/js/custom-manage.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.1845088005065918}, {"fpath": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/__init__.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.21404099464416504}, {"fpath": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/bitcoin_like_crypto.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.22743797302246094}, {"fpath": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/tron_token.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.2436199188232422}, {"fpath": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/classes/ethereum.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.26045989990234375}, {"fpath": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/cryptos/bitcoin_lightning.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.32893919944763184}, {"fpath": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/api_v1.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.35129690170288086}, {"fpath": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/callback.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.42220091819763184}]}, "tainting_time": {"total_time": 3.4301557540893555, "per_def_and_rule_time": {"mean": 0.000789085749733001, "std_dev": 2.5178628661181673e-06}, "very_slow_stats": {"time_ratio": 0.014943648191896138, "count_ratio": 0.00023004370830457787}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/vsys-host_shkeeper.io/shkeeper/modules/cryptos/bitcoin_lightning.py", "fline": 215, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.05125904083251953}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}