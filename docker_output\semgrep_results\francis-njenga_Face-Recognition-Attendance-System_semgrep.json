{"version": "1.130.0", "results": [{"check_id": "generic.secrets.security.detected-bcrypt-hash.detected-bcrypt-hash", "path": "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/database/attendance-db.sql", "start": {"line": 43, "col": 38, "offset": 1094}, "end": {"line": 43, "col": 98, "offset": 1154}, "extra": {"message": "bcrypt hash detected", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "category": "security", "technology": ["secrets", "bcrypt"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-bcrypt-hash.detected-bcrypt-hash", "shortlink": "https://sg.run/3A8G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-bcrypt-hash.detected-bcrypt-hash", "path": "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/database/attendance-db.sql", "start": {"line": 123, "col": 41, "offset": 3393}, "end": {"line": 123, "col": 101, "offset": 3453}, "extra": {"message": "bcrypt hash detected", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "category": "security", "technology": ["secrets", "bcrypt"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-bcrypt-hash.detected-bcrypt-hash", "shortlink": "https://sg.run/3A8G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/index.php", "start": {"line": 34, "col": 17, "offset": 814}, "end": {"line": 34, "col": 27, "offset": 824}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/assets/javascript/confirmation.js", "start": {"line": 4, "col": 5, "offset": 134}, "end": {"line": 4, "col": 36, "offset": 165}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/assets/javascript/face_logics/script.js", "start": {"line": 22, "col": 9, "offset": 826}, "end": {"line": 23, "col": 25, "offset": 911}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/assets/javascript/face_logics/script.js", "start": {"line": 111, "col": 25, "offset": 3347}, "end": {"line": 111, "col": 61, "offset": 3383}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/assets/javascript/face_logics/script.js", "start": {"line": 216, "col": 3, "offset": 6511}, "end": {"line": 216, "col": 34, "offset": 6542}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.backticks-use.backticks-use", "path": "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/lib/php_functions.php", "start": {"line": 89, "col": 9, "offset": 1821}, "end": {"line": 96, "col": 20, "offset": 2041}, "extra": {"message": "Backticks use may lead to command injection vulnerabilities.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.php.net/manual/en/language.operators.execution.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/BackticksSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.backticks-use.backticks-use", "shortlink": "https://sg.run/4xj9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-callable.tainted-callable", "path": "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/pages/administrator/handle_delete.php", "start": {"line": 11, "col": 35, "offset": 267}, "end": {"line": 11, "col": 69, "offset": 301}, "extra": {"message": "Callable based on user input risks remote code execution.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "owasp": ["A03:2021 - Injection"], "references": ["https://www.php.net/manual/en/language.types.callable.php"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-callable.tainted-callable", "shortlink": "https://sg.run/YGb33"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/pages/administrator/manage-students.php", "start": {"line": 17, "col": 22, "offset": 485}, "end": {"line": 17, "col": 33, "offset": 496}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/pages/administrator/manage-students.php", "start": {"line": 27, "col": 31, "offset": 883}, "end": {"line": 27, "col": 58, "offset": 910}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/pages/lecture/download-record.php", "start": {"line": 13, "col": 25, "offset": 269}, "end": {"line": 13, "col": 86, "offset": 330}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/pages/lecture/download-record.php", "start": {"line": 25, "col": 23, "offset": 550}, "end": {"line": 25, "col": 78, "offset": 605}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/pages/lecture/manageFolder.php", "start": {"line": 11, "col": 12, "offset": 332}, "end": {"line": 11, "col": 87, "offset": 407}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/pages/lecture/studentTable.php", "start": {"line": 24, "col": 24, "offset": 660}, "end": {"line": 24, "col": 82, "offset": 718}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/pages/lecture/view-attendance.php", "start": {"line": 10, "col": 25, "offset": 266}, "end": {"line": 10, "col": 86, "offset": 327}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/pages/lecture/view-attendance.php", "start": {"line": 19, "col": 23, "offset": 509}, "end": {"line": 19, "col": 78, "offset": 564}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/pages/lecture/view-students.php", "start": {"line": 10, "col": 25, "offset": 266}, "end": {"line": 10, "col": 86, "offset": 327}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/pages/lecture/view-students.php", "start": {"line": 19, "col": 23, "offset": 509}, "end": {"line": 19, "col": 78, "offset": 564}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/pages/lecture/view-students.php", "start": {"line": 91, "col": 44, "offset": 3047}, "end": {"line": 91, "col": 104, "offset": 3107}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-open-redirect.express-open-redirect", "message": "Timeout when running javascript.express.security.audit.express-open-redirect.express-open-redirect on downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/assets/javascript/min/js/xlsx.js:\n ", "path": "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/assets/javascript/min/js/xlsx.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/assets/javascript/min/js/xlsx.js:\n ", "path": "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/assets/javascript/min/js/xlsx.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.lang.security.audit.md5-used-as-password.md5-used-as-password", "message": "Timeout when running javascript.lang.security.audit.md5-used-as-password.md5-used-as-password on downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/assets/javascript/min/js/xlsx.js:\n ", "path": "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/assets/javascript/min/js/xlsx.js"}], "paths": {"scanned": ["downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/.htaccess", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/README.md", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/database/attendance-db.sql", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/database/database_connection.php", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/index.php", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/models/age_gender_model-shard1", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/models/age_gender_model-weights_manifest.json", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/models/face_expression_model-shard1", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/models/face_expression_model-weights_manifest.json", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/models/face_landmark_68_model-shard1", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/models/face_landmark_68_model-weights_manifest.json", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/models/face_landmark_68_tiny_model-shard1", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/models/face_landmark_68_tiny_model-weights_manifest.json", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/models/face_recognition_model-weights_manifest.json", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/models/mtcnn_model-weights_manifest.json", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/models/ssd_mobilenetv1_model-weights_manifest.json", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/models/tiny_face_detector_model-shard1", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/models/tiny_face_detector_model-weights_manifest.json", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/passwords.txt", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/assets/css/admin_styles.css", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/assets/css/login_styles.css", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/assets/css/styles.css", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/assets/javascript/active_link.js", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/assets/javascript/addCourse.js", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/assets/javascript/addLecture.js", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/assets/javascript/admin_functions.js", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/assets/javascript/confirmation.js", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/assets/javascript/delete_request.js", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/assets/javascript/face_logics/script.js", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/assets/javascript/min/js/filesaver.js", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/assets/javascript/min/js/xlsx.js", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/assets/javascript/script.js", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/assets/javascript/select.js", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/images/admin.png", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/images/class.jpeg", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/images/computer lab.jpeg", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/images/default.png", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/images/laboratory.jpeg", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/images/lecture hall.jpeg", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/images/logo/attnlg.jpeg", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/images/logo/attnlg.png", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/images/office image.jpeg", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/images/user.png", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/lib/php_functions.php", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/pages/404.php", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/pages/administrator/create-venue.php", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/pages/administrator/handle_delete.php", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/pages/administrator/home.php", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/pages/administrator/includes/sidebar.php", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/pages/administrator/includes/topbar.php", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/pages/administrator/manage-course.php", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/pages/administrator/manage-lecture.php", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/pages/administrator/manage-students.php", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/pages/lecture/attendanceUpload.php", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/pages/lecture/download-record.php", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/pages/lecture/handle_attendance.php", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/pages/lecture/home.php", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/pages/lecture/includes/sidebar.php", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/pages/lecture/includes/topbar.php", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/pages/lecture/manageFolder.php", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/pages/lecture/studentTable.php", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/pages/lecture/view-attendance.php", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/pages/lecture/view-students.php", "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/pages/login.php"], "skipped": [{"path": "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/models/face_recognition_model-shard1", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/models/face_recognition_model-shard2", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/models/mtcnn_model-shard1", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/models/ssd_mobilenetv1_model-shard1", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/models/ssd_mobilenetv1_model-shard2", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/assets/javascript/face_logics/face-api.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/assets/javascript/min/js/xlsx.js", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.6221699714660645, "profiling_times": {"config_time": 5.832804441452026, "core_time": 7.965435028076172, "ignores_time": 0.001950979232788086, "total_time": 13.801239490509033}, "parsing_time": {"total_time": 0.7363603115081787, "per_file_time": {"mean": 0.017960007597760463, "std_dev": 0.0002901378678616544}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 10.269413232803345, "per_file_time": {"mean": 0.06040831313413731, "std_dev": 0.1812384620094317}, "very_slow_stats": {"time_ratio": 0.5377596394923683, "count_ratio": 0.0058823529411764705}, "very_slow_files": [{"fpath": "downloaded_repos/francis-nje<PERSON>_Face-Recognition-Attendance-System/resources/assets/javascript/min/js/xlsx.js", "ftime": 5.522475957870483}]}, "matching_time": {"total_time": 1.835695743560791, "per_file_and_rule_time": {"mean": 0.008268899745769326, "std_dev": 0.00011202543302747512}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.6374485492706299, "per_def_and_rule_time": {"mean": 0.0026450147272640244, "std_dev": 8.193824778637954e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}