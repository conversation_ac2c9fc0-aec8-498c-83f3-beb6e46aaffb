{"version": "1.130.0", "results": [{"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_files/admin.php", "start": {"line": 183, "col": 22, "offset": 6788}, "end": {"line": 183, "col": 67, "offset": 6833}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_files/admin.php", "start": {"line": 212, "col": 17, "offset": 8099}, "end": {"line": 212, "col": 31, "offset": 8113}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_files/admin.php", "start": {"line": 213, "col": 17, "offset": 8132}, "end": {"line": 213, "col": 31, "offset": 8146}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_files/admin.php", "start": {"line": 214, "col": 17, "offset": 8165}, "end": {"line": 214, "col": 31, "offset": 8179}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_files/admin.php", "start": {"line": 328, "col": 23, "offset": 13345}, "end": {"line": 328, "col": 66, "offset": 13388}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_page/all-files-update-ajax.php", "start": {"line": 26, "col": 10, "offset": 741}, "end": {"line": 26, "col": 23, "offset": 754}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_page/all-files-update-ajax.php", "start": {"line": 31, "col": 21, "offset": 927}, "end": {"line": 31, "col": 34, "offset": 940}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_page/all-files-update-ajax.php", "start": {"line": 32, "col": 25, "offset": 967}, "end": {"line": 32, "col": 42, "offset": 984}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_page/filedrag.js", "start": {"line": 15, "col": 3, "offset": 341}, "end": {"line": 15, "col": 35, "offset": 373}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_page/filedrag.js", "start": {"line": 99, "col": 5, "offset": 2692}, "end": {"line": 99, "col": 103, "offset": 2790}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_page/filedrag.js", "start": {"line": 130, "col": 6, "offset": 3664}, "end": {"line": 130, "col": 44, "offset": 3702}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_page/uploads-require-maxsite.php", "start": {"line": 236, "col": 20, "offset": 8775}, "end": {"line": 236, "col": 34, "offset": 8789}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_users/group.php", "start": {"line": 171, "col": 23, "offset": 5949}, "end": {"line": 171, "col": 56, "offset": 5982}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/auto_post/filedrag.js", "start": {"line": 16, "col": 3, "offset": 326}, "end": {"line": 16, "col": 35, "offset": 358}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/auto_post/uploads-require-maxsite.php", "start": {"line": 33, "col": 13, "offset": 916}, "end": {"line": 33, "col": 17, "offset": 920}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/auto_post/uploads-require-maxsite.php", "start": {"line": 44, "col": 20, "offset": 1165}, "end": {"line": 44, "col": 33, "offset": 1178}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/auto_post/uploads-require-maxsite.php", "start": {"line": 46, "col": 17, "offset": 1233}, "end": {"line": 46, "col": 30, "offset": 1246}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/jquery.markitup.js", "start": {"line": 224, "col": 9, "offset": 7820}, "end": {"line": 224, "col": 26, "offset": 7837}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/js/jquery.tablesorter.js", "start": {"line": 3, "col": 3143, "offset": 8537}, "end": {"line": 3, "col": 3159, "offset": 8553}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/less/unicss/mixins/colors-gmd.html", "start": {"line": 22, "col": 32, "offset": 504}, "end": {"line": 22, "col": 146, "offset": 618}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/common/comments.php", "start": {"line": 321, "col": 5, "offset": 14456}, "end": {"line": 321, "col": 21, "offset": 14472}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/common/comments.php", "start": {"line": 382, "col": 5, "offset": 17294}, "end": {"line": 382, "col": 21, "offset": 17310}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/common/core/block-pages.php", "start": {"line": 203, "col": 9, "offset": 9822}, "end": {"line": 203, "col": 58, "offset": 9871}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/common/core/block-pages.php", "start": {"line": 220, "col": 17, "offset": 10753}, "end": {"line": 220, "col": 81, "offset": 10817}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/common/core/block-pages.php", "start": {"line": 308, "col": 13, "offset": 15617}, "end": {"line": 308, "col": 60, "offset": 15664}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/common/core/block-pages.php", "start": {"line": 312, "col": 17, "offset": 15778}, "end": {"line": 312, "col": 71, "offset": 15832}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/common/core/block-pages.php", "start": {"line": 314, "col": 17, "offset": 15910}, "end": {"line": 314, "col": 69, "offset": 15962}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/common/core/block-pages.php", "start": {"line": 319, "col": 13, "offset": 16052}, "end": {"line": 319, "col": 60, "offset": 16099}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/common/core/cache.php", "start": {"line": 80, "col": 5, "offset": 2432}, "end": {"line": 80, "col": 32, "offset": 2459}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/common/core/cache.php", "start": {"line": 120, "col": 43, "offset": 4026}, "end": {"line": 120, "col": 70, "offset": 4053}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/common/core/cache.php", "start": {"line": 126, "col": 45, "offset": 4287}, "end": {"line": 126, "col": 76, "offset": 4318}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/common/core/cache.php", "start": {"line": 195, "col": 4, "offset": 6430}, "end": {"line": 195, "col": 21, "offset": 6447}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/common/core/cache.php", "start": {"line": 200, "col": 10, "offset": 6529}, "end": {"line": 200, "col": 27, "offset": 6546}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/common/core/format.php", "start": {"line": 43, "col": 3, "offset": 1922}, "end": {"line": 43, "col": 33, "offset": 1952}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/common/core/format.php", "start": {"line": 46, "col": 3, "offset": 1982}, "end": {"line": 46, "col": 33, "offset": 2012}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/common/core/format.php", "start": {"line": 904, "col": 4, "offset": 35235}, "end": {"line": 904, "col": 47, "offset": 35278}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/common/core/hooks.php", "start": {"line": 69, "col": 57, "offset": 2244}, "end": {"line": 69, "col": 75, "offset": 2262}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/common/core/init.php", "start": {"line": 116, "col": 15, "offset": 4735}, "end": {"line": 116, "col": 36, "offset": 4756}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/common/core/options.php", "start": {"line": 130, "col": 14, "offset": 3912}, "end": {"line": 130, "col": 34, "offset": 3932}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/common/core/options.php", "start": {"line": 200, "col": 13, "offset": 6015}, "end": {"line": 200, "col": 53, "offset": 6055}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/common/core/options.php", "start": {"line": 226, "col": 4, "offset": 6663}, "end": {"line": 226, "col": 17, "offset": 6676}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/common/core/security.php", "start": {"line": 273, "col": 18, "offset": 10287}, "end": {"line": 273, "col": 37, "offset": 10306}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.audit.openssl-decrypt-validate.openssl-decrypt-validate", "path": "downloaded_repos/maxsite_cms/application/maxsite/common/core/security.php", "start": {"line": 472, "col": 13, "offset": 18194}, "end": {"line": 472, "col": 97, "offset": 18278}, "extra": {"message": "The function `openssl_decrypt` returns either a string of the decrypted data on success or `false` on failure. If the failure case is not handled, this could lead to undefined behavior in your application. Please handle the case where `openssl_decrypt` returns `false`.", "metadata": {"references": ["https://www.php.net/manual/en/function.openssl-decrypt.php"], "cwe": ["CWE-252: Unchecked Return Value"], "owasp": ["A02:2021 - Cryptographic Failures"], "technology": ["php", "openssl"], "category": "security", "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.audit.openssl-decrypt-validate.openssl-decrypt-validate", "shortlink": "https://sg.run/kzn7"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/common/core/sidebar.php", "start": {"line": 60, "col": 6, "offset": 2438}, "end": {"line": 60, "col": 28, "offset": 2460}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/common/core/template.php", "start": {"line": 245, "col": 3, "offset": 9665}, "end": {"line": 245, "col": 37, "offset": 9699}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/common/core/tmpl.php", "start": {"line": 71, "col": 5, "offset": 2548}, "end": {"line": 71, "col": 40, "offset": 2583}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/common/core/tmpl.php", "start": {"line": 78, "col": 5, "offset": 2658}, "end": {"line": 78, "col": 40, "offset": 2693}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/common/core/units.php", "start": {"line": 46, "col": 9, "offset": 2610}, "end": {"line": 46, "col": 27, "offset": 2628}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/common/core/units.php", "start": {"line": 114, "col": 17, "offset": 6368}, "end": {"line": 114, "col": 46, "offset": 6397}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/common/core/units.php", "start": {"line": 122, "col": 17, "offset": 6887}, "end": {"line": 122, "col": 46, "offset": 6916}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/common/core/units.php", "start": {"line": 141, "col": 17, "offset": 7958}, "end": {"line": 141, "col": 68, "offset": 8009}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/common/inifile.php", "start": {"line": 22, "col": 3, "offset": 736}, "end": {"line": 22, "col": 32, "offset": 765}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/common/inifile.php", "start": {"line": 300, "col": 28, "offset": 11028}, "end": {"line": 300, "col": 44, "offset": 11044}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/common/uploads.php", "start": {"line": 123, "col": 24, "offset": 5942}, "end": {"line": 123, "col": 72, "offset": 5990}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/down_count/index.php", "start": {"line": 116, "col": 12, "offset": 4153}, "end": {"line": 116, "col": 39, "offset": 4180}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.curl-ssl-verifypeer-off.curl-ssl-verifypeer-off", "path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/fbauth/index.php", "start": {"line": 120, "col": 2, "offset": 4869}, "end": {"line": 120, "col": 44, "offset": 4911}, "extra": {"message": "SSL verification is disabled but should not be (currently CURLOPT_SSL_VERIFYPEER= 0)", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.saotn.org/dont-turn-off-curlopt_ssl_verifypeer-fix-php-configuration/"], "category": "security", "technology": ["php"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.curl-ssl-verifypeer-off.curl-ssl-verifypeer-off", "shortlink": "https://sg.run/PJqv"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.curl-ssl-verifypeer-off.curl-ssl-verifypeer-off", "path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/loginza_auth/index.php", "start": {"line": 235, "col": 2, "offset": 11572}, "end": {"line": 235, "col": 44, "offset": 11614}, "extra": {"message": "SSL verification is disabled but should not be (currently CURLOPT_SSL_VERIFYPEER= 0)", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.saotn.org/dont-turn-off-curlopt_ssl_verifypeer-fix-php-configuration/"], "category": "security", "technology": ["php"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.curl-ssl-verifypeer-off.curl-ssl-verifypeer-off", "shortlink": "https://sg.run/PJqv"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/random_gal/index.php", "start": {"line": 272, "col": 19, "offset": 11336}, "end": {"line": 272, "col": 64, "offset": 11381}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/rss_get/lastrss.php", "start": {"line": 70, "col": 15, "offset": 2703}, "end": {"line": 70, "col": 55, "offset": 2743}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/run_php/index.php", "start": {"line": 22, "col": 2, "offset": 799}, "end": {"line": 22, "col": 28, "offset": 825}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/sape/admin.php", "start": {"line": 24, "col": 20, "offset": 707}, "end": {"line": 24, "col": 23, "offset": 710}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/sape/admin.php", "start": {"line": 30, "col": 21, "offset": 1118}, "end": {"line": 30, "col": 70, "offset": 1167}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/sape/index.php", "start": {"line": 100, "col": 20, "offset": 3619}, "end": {"line": 100, "col": 78, "offset": 3677}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/text_block/index.php", "start": {"line": 95, "col": 3, "offset": 3666}, "end": {"line": 95, "col": 50, "offset": 3713}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/theme_switch/index.php", "start": {"line": 66, "col": 20, "offset": 2426}, "end": {"line": 66, "col": 66, "offset": 2472}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/theme_switch/index.php", "start": {"line": 74, "col": 21, "offset": 2739}, "end": {"line": 74, "col": 36, "offset": 2754}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/ushki/index.php", "start": {"line": 207, "col": 5, "offset": 7427}, "end": {"line": 207, "col": 55, "offset": 7477}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/ushki_to_hook/index.php", "start": {"line": 144, "col": 6, "offset": 6122}, "end": {"line": 144, "col": 16, "offset": 6132}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/wpconvert/admin.php", "start": {"line": 43, "col": 20, "offset": 1136}, "end": {"line": 43, "col": 59, "offset": 1175}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/wpconvert/admin.php", "start": {"line": 98, "col": 19, "offset": 3056}, "end": {"line": 98, "col": 49, "offset": 3086}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/wpconvert/admin.php", "start": {"line": 148, "col": 51, "offset": 5601}, "end": {"line": 148, "col": 81, "offset": 5631}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/wpconvert/admin.php", "start": {"line": 234, "col": 20, "offset": 8599}, "end": {"line": 234, "col": 59, "offset": 8638}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/wpconvert/admin.php", "start": {"line": 310, "col": 19, "offset": 11428}, "end": {"line": 310, "col": 49, "offset": 11458}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/wpconvert/admin.php", "start": {"line": 391, "col": 51, "offset": 14359}, "end": {"line": 391, "col": 81, "offset": 14389}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/wpconvert/export-max.php", "start": {"line": 93, "col": 28, "offset": 3669}, "end": {"line": 93, "col": 99, "offset": 3740}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/wpconvert/export-max.php", "start": {"line": 220, "col": 32, "offset": 7667}, "end": {"line": 220, "col": 94, "offset": 7729}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/wpconvert/export-max.php", "start": {"line": 241, "col": 33, "offset": 8657}, "end": {"line": 241, "col": 98, "offset": 8722}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/shared/type/loginform/loginform.php", "start": {"line": 14, "col": 2, "offset": 409}, "end": {"line": 14, "col": 68, "offset": 475}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/shared/type/loginform/loginform.php", "start": {"line": 23, "col": 2, "offset": 729}, "end": {"line": 23, "col": 71, "offset": 798}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/shared/type/loginform/loginform.php", "start": {"line": 28, "col": 3, "offset": 970}, "end": {"line": 28, "col": 65, "offset": 1032}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/shared/type/loginform/loginform.php", "start": {"line": 48, "col": 2, "offset": 1787}, "end": {"line": 48, "col": 63, "offset": 1848}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/shared/type/page/units/page-comments.php", "start": {"line": 55, "col": 2, "offset": 2377}, "end": {"line": 55, "col": 68, "offset": 2443}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/shared/type/page/units/page-comments.php", "start": {"line": 93, "col": 3, "offset": 3515}, "end": {"line": 93, "col": 15, "offset": 3527}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/shared/type/page/units/page-comments.php", "start": {"line": 119, "col": 5, "offset": 5188}, "end": {"line": 119, "col": 73, "offset": 5256}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/shared/type/page/units/page-comments.php", "start": {"line": 126, "col": 4, "offset": 5511}, "end": {"line": 126, "col": 68, "offset": 5575}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/shared/type/password-recovery/password-recovery.php", "start": {"line": 28, "col": 3, "offset": 1000}, "end": {"line": 28, "col": 69, "offset": 1066}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/shared/type/password-recovery/password-recovery.php", "start": {"line": 37, "col": 3, "offset": 1327}, "end": {"line": 37, "col": 72, "offset": 1396}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/shared/type/password-recovery/password-recovery.php", "start": {"line": 39, "col": 3, "offset": 1411}, "end": {"line": 39, "col": 80, "offset": 1488}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/shared/type/registration/registration.php", "start": {"line": 21, "col": 2, "offset": 802}, "end": {"line": 21, "col": 68, "offset": 868}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/shared/type/registration/registration.php", "start": {"line": 30, "col": 2, "offset": 1122}, "end": {"line": 30, "col": 71, "offset": 1191}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/shared/type/registration/registration.php", "start": {"line": 135, "col": 2, "offset": 5087}, "end": {"line": 135, "col": 69, "offset": 5154}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/shared/type/search/search.php", "start": {"line": 200, "col": 3, "offset": 7531}, "end": {"line": 200, "col": 56, "offset": 7584}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/shared/type/users/units/users-form-lost.php", "start": {"line": 47, "col": 4, "offset": 1766}, "end": {"line": 47, "col": 74, "offset": 1836}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/shared/type/users/units/users-form-lost.php", "start": {"line": 50, "col": 4, "offset": 1984}, "end": {"line": 50, "col": 74, "offset": 2054}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/shared/type/users/units/users-form.php", "start": {"line": 65, "col": 4, "offset": 2329}, "end": {"line": 65, "col": 74, "offset": 2399}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/shared/type/users/units/users-form.php", "start": {"line": 73, "col": 5, "offset": 2764}, "end": {"line": 73, "col": 68, "offset": 2827}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/shared/type/users/units/users-form.php", "start": {"line": 91, "col": 5, "offset": 3991}, "end": {"line": 91, "col": 68, "offset": 4054}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/shared/type/users/units/users.php", "start": {"line": 61, "col": 3, "offset": 2229}, "end": {"line": 61, "col": 56, "offset": 2282}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/shared/type/users/units/users.php", "start": {"line": 70, "col": 4, "offset": 2558}, "end": {"line": 70, "col": 66, "offset": 2620}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/any1/index.php", "start": {"line": 13, "col": 2, "offset": 453}, "end": {"line": 13, "col": 61, "offset": 512}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/any1/index.php", "start": {"line": 20, "col": 1, "offset": 661}, "end": {"line": 20, "col": 30, "offset": 690}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/any2/index.php", "start": {"line": 13, "col": 2, "offset": 453}, "end": {"line": 13, "col": 61, "offset": 512}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/any2/index.php", "start": {"line": 20, "col": 1, "offset": 661}, "end": {"line": 20, "col": 30, "offset": 690}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/file1/index.php", "start": {"line": 13, "col": 2, "offset": 461}, "end": {"line": 13, "col": 61, "offset": 520}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/file1/index.php", "start": {"line": 21, "col": 3, "offset": 708}, "end": {"line": 21, "col": 23, "offset": 728}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/file2/index.php", "start": {"line": 13, "col": 2, "offset": 461}, "end": {"line": 13, "col": 61, "offset": 520}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/file2/index.php", "start": {"line": 21, "col": 3, "offset": 707}, "end": {"line": 21, "col": 23, "offset": 727}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/footer-copy-stat/index.php", "start": {"line": 13, "col": 2, "offset": 434}, "end": {"line": 13, "col": 61, "offset": 493}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/header11/index.php", "start": {"line": 10, "col": 2, "offset": 387}, "end": {"line": 10, "col": 61, "offset": 446}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/header6/index.php", "start": {"line": 10, "col": 5, "offset": 389}, "end": {"line": 10, "col": 64, "offset": 448}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/header7/index.php", "start": {"line": 14, "col": 2, "offset": 505}, "end": {"line": 14, "col": 61, "offset": 564}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/lightslider/index.php", "start": {"line": 14, "col": 2, "offset": 476}, "end": {"line": 14, "col": 61, "offset": 535}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/module1/index.php", "start": {"line": 13, "col": 5, "offset": 453}, "end": {"line": 13, "col": 64, "offset": 512}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/module2/index.php", "start": {"line": 13, "col": 5, "offset": 438}, "end": {"line": 13, "col": 64, "offset": 497}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/distr/tiny-slider/assets/tiny-slider.js", "start": {"line": 144, "col": 3, "offset": 3187}, "end": {"line": 144, "col": 25, "offset": 3209}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/distr/tiny-slider/assets/tiny-slider.js", "start": {"line": 1648, "col": 9, "offset": 52213}, "end": {"line": 1648, "col": 36, "offset": 52240}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/distr/tiny-slider/assets/tiny-slider.js", "start": {"line": 1870, "col": 9, "offset": 62600}, "end": {"line": 1870, "col": 48, "offset": 62639}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/distr/tiny-slider/assets/tiny-slider.js", "start": {"line": 1871, "col": 9, "offset": 62648}, "end": {"line": 1871, "col": 48, "offset": 62687}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/distr/tiny-slider/assets/tiny-slider.js", "start": {"line": 1879, "col": 11, "offset": 62967}, "end": {"line": 1879, "col": 79, "offset": 63035}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/distr/tiny-slider/assets/tiny-slider.js", "start": {"line": 2149, "col": 48, "offset": 70372}, "end": {"line": 2149, "col": 82, "offset": 70406}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/distr/tiny-slider/assets/tiny-slider.js", "start": {"line": 2845, "col": 5, "offset": 90900}, "end": {"line": 2845, "col": 95, "offset": 90990}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/type/mfstore/admin-ajax.php", "start": {"line": 19, "col": 21, "offset": 426}, "end": {"line": 19, "col": 45, "offset": 450}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/type/mfstore/admin-ajax.php", "start": {"line": 23, "col": 25, "offset": 553}, "end": {"line": 23, "col": 51, "offset": 579}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/type/mfstore/admin-ajax.php", "start": {"line": 30, "col": 25, "offset": 812}, "end": {"line": 30, "col": 36, "offset": 823}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/system/core/Common.php", "start": {"line": 91, "col": 5, "offset": 2372}, "end": {"line": 91, "col": 18, "offset": 2385}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/maxsite_cms/system/core/Loader.php", "start": {"line": 831, "col": 4, "offset": 19666}, "end": {"line": 831, "col": 120, "offset": 19782}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/system/core/Output.php", "start": {"line": 559, "col": 6, "offset": 12688}, "end": {"line": 559, "col": 23, "offset": 12705}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/maxsite_cms/system/database/DB_cache.php", "start": {"line": 110, "col": 10, "offset": 2674}, "end": {"line": 110, "col": 33, "offset": 2697}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/system/helpers/captcha_helper.php", "start": {"line": 99, "col": 7, "offset": 2353}, "end": {"line": 99, "col": 34, "offset": 2380}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.mcrypt-use.mcrypt-use", "path": "downloaded_repos/maxsite_cms/system/helpers/captcha_helper.php", "start": {"line": 322, "col": 4, "offset": 7853}, "end": {"line": 322, "col": 56, "offset": 7905}, "extra": {"message": "Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL.", "metadata": {"cwe": ["CWE-676: Use of Potentially Dangerous Function"], "references": ["https://www.php.net/manual/en/intro.mcrypt.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/CryptoFunctionsSniff.php"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Dangerous Method or Function"], "source": "https://semgrep.dev/r/php.lang.security.mcrypt-use.mcrypt-use", "shortlink": "https://sg.run/BkZR"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/system/helpers/file_helper.php", "start": {"line": 146, "col": 7, "offset": 3189}, "end": {"line": 146, "col": 50, "offset": 3232}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/maxsite_cms/system/libraries/Cache/drivers/Cache_file.php", "start": {"line": 62, "col": 11, "offset": 1434}, "end": {"line": 62, "col": 29, "offset": 1452}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/system/libraries/Cache/drivers/Cache_file.php", "start": {"line": 66, "col": 4, "offset": 1508}, "end": {"line": 66, "col": 34, "offset": 1538}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/system/libraries/Cache/drivers/Cache_file.php", "start": {"line": 111, "col": 10, "offset": 2471}, "end": {"line": 111, "col": 40, "offset": 2501}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/maxsite_cms/system/libraries/Cache/drivers/Cache_file.php", "start": {"line": 157, "col": 11, "offset": 3440}, "end": {"line": 157, "col": 29, "offset": 3458}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/maxsite_cms/system/libraries/Email.php", "start": {"line": 1583, "col": 3, "offset": 36530}, "end": {"line": 1583, "col": 101, "offset": 36628}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.mcrypt-use.mcrypt-use", "path": "downloaded_repos/maxsite_cms/system/libraries/Encrypt.php", "start": {"line": 274, "col": 3, "offset": 6239}, "end": {"line": 274, "col": 77, "offset": 6313}, "extra": {"message": "Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL.", "metadata": {"cwe": ["CWE-676: Use of Potentially Dangerous Function"], "references": ["https://www.php.net/manual/en/intro.mcrypt.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/CryptoFunctionsSniff.php"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Dangerous Method or Function"], "source": "https://semgrep.dev/r/php.lang.security.mcrypt-use.mcrypt-use", "shortlink": "https://sg.run/BkZR"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.mcrypt-use.mcrypt-use", "path": "downloaded_repos/maxsite_cms/system/libraries/Encrypt.php", "start": {"line": 275, "col": 3, "offset": 6316}, "end": {"line": 275, "col": 58, "offset": 6371}, "extra": {"message": "Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL.", "metadata": {"cwe": ["CWE-676: Use of Potentially Dangerous Function"], "references": ["https://www.php.net/manual/en/intro.mcrypt.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/CryptoFunctionsSniff.php"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Dangerous Method or Function"], "source": "https://semgrep.dev/r/php.lang.security.mcrypt-use.mcrypt-use", "shortlink": "https://sg.run/BkZR"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.mcrypt-use.mcrypt-use", "path": "downloaded_repos/maxsite_cms/system/libraries/Encrypt.php", "start": {"line": 276, "col": 3, "offset": 6374}, "end": {"line": 276, "col": 134, "offset": 6505}, "extra": {"message": "Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL.", "metadata": {"cwe": ["CWE-676: Use of Potentially Dangerous Function"], "references": ["https://www.php.net/manual/en/intro.mcrypt.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/CryptoFunctionsSniff.php"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Dangerous Method or Function"], "source": "https://semgrep.dev/r/php.lang.security.mcrypt-use.mcrypt-use", "shortlink": "https://sg.run/BkZR"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.mcrypt-use.mcrypt-use", "path": "downloaded_repos/maxsite_cms/system/libraries/Encrypt.php", "start": {"line": 292, "col": 3, "offset": 6792}, "end": {"line": 292, "col": 77, "offset": 6866}, "extra": {"message": "Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL.", "metadata": {"cwe": ["CWE-676: Use of Potentially Dangerous Function"], "references": ["https://www.php.net/manual/en/intro.mcrypt.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/CryptoFunctionsSniff.php"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Dangerous Method or Function"], "source": "https://semgrep.dev/r/php.lang.security.mcrypt-use.mcrypt-use", "shortlink": "https://sg.run/BkZR"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.mcrypt-use.mcrypt-use", "path": "downloaded_repos/maxsite_cms/system/libraries/Encrypt.php", "start": {"line": 301, "col": 3, "offset": 7012}, "end": {"line": 301, "col": 104, "offset": 7113}, "extra": {"message": "Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL.", "metadata": {"cwe": ["CWE-676: Use of Potentially Dangerous Function"], "references": ["https://www.php.net/manual/en/intro.mcrypt.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/CryptoFunctionsSniff.php"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Dangerous Method or Function"], "source": "https://semgrep.dev/r/php.lang.security.mcrypt-use.mcrypt-use", "shortlink": "https://sg.run/BkZR"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.ftp-use.ftp-use", "path": "downloaded_repos/maxsite_cms/system/libraries/Ftp.php", "start": {"line": 115, "col": 4, "offset": 2351}, "end": {"line": 115, "col": 35, "offset": 2382}, "extra": {"message": "FTP allows for unencrypted file transfers. Consider using an encrypted alternative.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.php.net/manual/en/intro.ftp.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/FringeFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.ftp-use.ftp-use", "shortlink": "https://sg.run/RoYN"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.ftp-use.ftp-use", "path": "downloaded_repos/maxsite_cms/system/libraries/Ftp.php", "start": {"line": 131, "col": 3, "offset": 2570}, "end": {"line": 131, "col": 70, "offset": 2637}, "extra": {"message": "FTP allows for unencrypted file transfers. Consider using an encrypted alternative.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.php.net/manual/en/intro.ftp.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/FringeFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.ftp-use.ftp-use", "shortlink": "https://sg.run/RoYN"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.ftp-use.ftp-use", "path": "downloaded_repos/maxsite_cms/system/libraries/Ftp.php", "start": {"line": 179, "col": 3, "offset": 3647}, "end": {"line": 179, "col": 47, "offset": 3691}, "extra": {"message": "FTP allows for unencrypted file transfers. Consider using an encrypted alternative.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.php.net/manual/en/intro.ftp.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/FringeFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.ftp-use.ftp-use", "shortlink": "https://sg.run/RoYN"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.ftp-use.ftp-use", "path": "downloaded_repos/maxsite_cms/system/libraries/Ftp.php", "start": {"line": 209, "col": 3, "offset": 4163}, "end": {"line": 209, "col": 47, "offset": 4207}, "extra": {"message": "FTP allows for unencrypted file transfers. Consider using an encrypted alternative.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.php.net/manual/en/intro.ftp.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/FringeFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.ftp-use.ftp-use", "shortlink": "https://sg.run/RoYN"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.ftp-use.ftp-use", "path": "downloaded_repos/maxsite_cms/system/libraries/Ftp.php", "start": {"line": 263, "col": 3, "offset": 5174}, "end": {"line": 263, "col": 65, "offset": 5236}, "extra": {"message": "FTP allows for unencrypted file transfers. Consider using an encrypted alternative.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.php.net/manual/en/intro.ftp.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/FringeFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.ftp-use.ftp-use", "shortlink": "https://sg.run/RoYN"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.ftp-use.ftp-use", "path": "downloaded_repos/maxsite_cms/system/libraries/Ftp.php", "start": {"line": 311, "col": 3, "offset": 6118}, "end": {"line": 311, "col": 65, "offset": 6180}, "extra": {"message": "FTP allows for unencrypted file transfers. Consider using an encrypted alternative.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.php.net/manual/en/intro.ftp.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/FringeFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.ftp-use.ftp-use", "shortlink": "https://sg.run/RoYN"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.ftp-use.ftp-use", "path": "downloaded_repos/maxsite_cms/system/libraries/Ftp.php", "start": {"line": 343, "col": 3, "offset": 6653}, "end": {"line": 343, "col": 63, "offset": 6713}, "extra": {"message": "FTP allows for unencrypted file transfers. Consider using an encrypted alternative.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.php.net/manual/en/intro.ftp.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/FringeFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.ftp-use.ftp-use", "shortlink": "https://sg.run/RoYN"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.ftp-use.ftp-use", "path": "downloaded_repos/maxsite_cms/system/libraries/Ftp.php", "start": {"line": 390, "col": 3, "offset": 7461}, "end": {"line": 390, "col": 52, "offset": 7510}, "extra": {"message": "FTP allows for unencrypted file transfers. Consider using an encrypted alternative.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.php.net/manual/en/intro.ftp.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/FringeFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.ftp-use.ftp-use", "shortlink": "https://sg.run/RoYN"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.ftp-use.ftp-use", "path": "downloaded_repos/maxsite_cms/system/libraries/Ftp.php", "start": {"line": 439, "col": 3, "offset": 8443}, "end": {"line": 439, "col": 51, "offset": 8491}, "extra": {"message": "FTP allows for unencrypted file transfers. Consider using an encrypted alternative.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.php.net/manual/en/intro.ftp.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/FringeFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.ftp-use.ftp-use", "shortlink": "https://sg.run/RoYN"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.ftp-use.ftp-use", "path": "downloaded_repos/maxsite_cms/system/libraries/Ftp.php", "start": {"line": 479, "col": 3, "offset": 9095}, "end": {"line": 479, "col": 54, "offset": 9146}, "extra": {"message": "FTP allows for unencrypted file transfers. Consider using an encrypted alternative.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.php.net/manual/en/intro.ftp.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/FringeFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.ftp-use.ftp-use", "shortlink": "https://sg.run/RoYN"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.ftp-use.ftp-use", "path": "downloaded_repos/maxsite_cms/system/libraries/Ftp.php", "start": {"line": 508, "col": 3, "offset": 9563}, "end": {"line": 508, "col": 42, "offset": 9602}, "extra": {"message": "FTP allows for unencrypted file transfers. Consider using an encrypted alternative.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.php.net/manual/en/intro.ftp.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/FringeFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.ftp-use.ftp-use", "shortlink": "https://sg.run/RoYN"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.ftp-use.ftp-use", "path": "downloaded_repos/maxsite_cms/system/libraries/Ftp.php", "start": {"line": 637, "col": 3, "offset": 12217}, "end": {"line": 637, "col": 30, "offset": 12244}, "extra": {"message": "FTP allows for unencrypted file transfers. Consider using an encrypted alternative.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.php.net/manual/en/intro.ftp.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/FringeFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.ftp-use.ftp-use", "shortlink": "https://sg.run/RoYN"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/maxsite_cms/system/libraries/Image_lib.php", "start": {"line": 612, "col": 3, "offset": 15746}, "end": {"line": 612, "col": 33, "offset": 15776}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/maxsite_cms/system/libraries/Image_lib.php", "start": {"line": 694, "col": 3, "offset": 17428}, "end": {"line": 694, "col": 33, "offset": 17458}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/system/libraries/Image_lib.php", "start": {"line": 707, "col": 3, "offset": 17788}, "end": {"line": 707, "col": 43, "offset": 17828}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/maxsite_cms/system/libraries/Session.php", "start": {"line": 742, "col": 12, "offset": 18432}, "end": {"line": 742, "col": 45, "offset": 18465}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "path": "downloaded_repos/maxsite_cms/system/libraries/Upload.php", "start": {"line": 420, "col": 7, "offset": 10941}, "end": {"line": 420, "col": 26, "offset": 10960}, "extra": {"message": "Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues", "metadata": {"cwe": ["CWE-697: Incorrect Comparison"], "references": ["https://www.php.net/manual/en/types.comparisons.php", "https://web.archive.org/web/20210430183236/https://www.whitehatsec.com/blog/magic-hashes/"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.md5-loose-equality.md5-loose-equality", "shortlink": "https://sg.run/Do4G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/maxsite_cms/system/libraries/Upload.php", "start": {"line": 1066, "col": 5, "offset": 26177}, "end": {"line": 1066, "col": 48, "offset": 26220}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/maxsite_cms/system/libraries/Upload.php", "start": {"line": 1076, "col": 5, "offset": 26469}, "end": {"line": 1076, "col": 31, "offset": 26495}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/maxsite_cms/system/libraries/Upload.php", "start": {"line": 1090, "col": 5, "offset": 26757}, "end": {"line": 1090, "col": 31, "offset": 26783}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/update-maxsite/index.php", "start": {"line": 64, "col": 48, "offset": 2000}, "end": {"line": 64, "col": 79, "offset": 2031}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/update-maxsite/index.php", "start": {"line": 86, "col": 9, "offset": 2706}, "end": {"line": 86, "col": 79, "offset": 2776}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/update-maxsite/index.php", "start": {"line": 89, "col": 9, "offset": 2860}, "end": {"line": 89, "col": 81, "offset": 2932}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/update-maxsite/index.php", "start": {"line": 92, "col": 9, "offset": 3018}, "end": {"line": 92, "col": 80, "offset": 3089}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.curl-ssl-verifypeer-off.curl-ssl-verifypeer-off", "path": "downloaded_repos/maxsite_cms/update-maxsite/index.php", "start": {"line": 144, "col": 9, "offset": 5052}, "end": {"line": 144, "col": 56, "offset": 5099}, "extra": {"message": "SSL verification is disabled but should not be (currently CURLOPT_SSL_VERIFYPEER= false)", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.saotn.org/dont-turn-off-curlopt_ssl_verifypeer-fix-php-configuration/"], "category": "security", "technology": ["php"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.curl-ssl-verifypeer-off.curl-ssl-verifypeer-off", "shortlink": "https://sg.run/PJqv"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/update-maxsite/index.php", "start": {"line": 257, "col": 22, "offset": 9329}, "end": {"line": 257, "col": 69, "offset": 9376}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/update-maxsite/pclzip.lib.php", "start": {"line": 2238, "col": 8, "offset": 85898}, "end": {"line": 2238, "col": 32, "offset": 85922}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/update-maxsite/pclzip.lib.php", "start": {"line": 2266, "col": 12, "offset": 86878}, "end": {"line": 2266, "col": 36, "offset": 86902}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/update-maxsite/pclzip.lib.php", "start": {"line": 2321, "col": 6, "offset": 88518}, "end": {"line": 2321, "col": 28, "offset": 88540}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/update-maxsite/pclzip.lib.php", "start": {"line": 2895, "col": 6, "offset": 108979}, "end": {"line": 2895, "col": 31, "offset": 109004}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/update-maxsite/pclzip.lib.php", "start": {"line": 4029, "col": 6, "offset": 148606}, "end": {"line": 4029, "col": 31, "offset": 148631}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/update-maxsite/pclzip.lib.php", "start": {"line": 4852, "col": 18, "offset": 175858}, "end": {"line": 4852, "col": 42, "offset": 175882}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/update-maxsite/pclzip.lib.php", "start": {"line": 4867, "col": 18, "offset": 176426}, "end": {"line": 4867, "col": 42, "offset": 176450}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/update-maxsite/pclzip.lib.php", "start": {"line": 4885, "col": 18, "offset": 177120}, "end": {"line": 4885, "col": 42, "offset": 177144}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/update-maxsite/pclzip.lib.php", "start": {"line": 4896, "col": 18, "offset": 177562}, "end": {"line": 4896, "col": 42, "offset": 177586}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/update-maxsite/pclzip.lib.php", "start": {"line": 4912, "col": 18, "offset": 178150}, "end": {"line": 4912, "col": 42, "offset": 178174}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/update-maxsite/pclzip.lib.php", "start": {"line": 4938, "col": 14, "offset": 179080}, "end": {"line": 4938, "col": 38, "offset": 179104}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/update-maxsite/pclzip.lib.php", "start": {"line": 4950, "col": 10, "offset": 179370}, "end": {"line": 4950, "col": 32, "offset": 179392}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/update-maxsite/pclzip.lib.php", "start": {"line": 5214, "col": 6, "offset": 187435}, "end": {"line": 5214, "col": 28, "offset": 187457}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/maxsite_cms/update-maxsite/pclzip.lib.php", "start": {"line": 5627, "col": 18, "offset": 200538}, "end": {"line": 5627, "col": 32, "offset": 200552}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": "Syntax error", "message": "Syntax error at line downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_home/admin.php:266:\n `�` was unexpected", "path": "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_home/admin.php"}], "paths": {"scanned": ["downloaded_repos/maxsite_cms/.github/FUNDING.yml", "downloaded_repos/maxsite_cms/License", "downloaded_repos/maxsite_cms/LicenseCodeIgniter", "downloaded_repos/maxsite_cms/README.md", "downloaded_repos/maxsite_cms/application/cache/db/.gitkeep/.gitkeep", "downloaded_repos/maxsite_cms/application/cache/html/.gitkeep/.gitkeep", "downloaded_repos/maxsite_cms/application/cache/rss/.gitkeep/.gitkeep", "downloaded_repos/maxsite_cms/application/config/autoload.php", "downloaded_repos/maxsite_cms/application/config/config.php", "downloaded_repos/maxsite_cms/application/config/constants.php", "downloaded_repos/maxsite_cms/application/config/database.php-distr", "downloaded_repos/maxsite_cms/application/config/doctypes.php", "downloaded_repos/maxsite_cms/application/config/foreign_chars.php", "downloaded_repos/maxsite_cms/application/config/hooks.php", "downloaded_repos/maxsite_cms/application/config/migration.php", "downloaded_repos/maxsite_cms/application/config/mimes.php", "downloaded_repos/maxsite_cms/application/config/mso_config.php-distr", "downloaded_repos/maxsite_cms/application/config/profiler.php", "downloaded_repos/maxsite_cms/application/config/routes.php", "downloaded_repos/maxsite_cms/application/config/smileys.php", "downloaded_repos/maxsite_cms/application/config/user_agents.php", "downloaded_repos/maxsite_cms/application/controllers/codeigniter_welcome.php", "downloaded_repos/maxsite_cms/application/controllers/maxsite.php", "downloaded_repos/maxsite_cms/application/errors/error_404.php", "downloaded_repos/maxsite_cms/application/errors/error_db.php", "downloaded_repos/maxsite_cms/application/errors/error_general.php", "downloaded_repos/maxsite_cms/application/errors/error_php.php", "downloaded_repos/maxsite_cms/application/language/de/calendar_lang.php", "downloaded_repos/maxsite_cms/application/language/de/date_lang.php", "downloaded_repos/maxsite_cms/application/language/de/db_lang.php", "downloaded_repos/maxsite_cms/application/language/de/email_lang.php", "downloaded_repos/maxsite_cms/application/language/de/form_validation_lang.php", "downloaded_repos/maxsite_cms/application/language/de/ftp_lang.php", "downloaded_repos/maxsite_cms/application/language/de/imglib_lang.php", "downloaded_repos/maxsite_cms/application/language/de/number_lang.php", "downloaded_repos/maxsite_cms/application/language/de/profiler_lang.php", "downloaded_repos/maxsite_cms/application/language/de/unit_test_lang.php", "downloaded_repos/maxsite_cms/application/language/de/upload_lang.php", "downloaded_repos/maxsite_cms/application/language/de/validation_lang.php", "downloaded_repos/maxsite_cms/application/language/english/index.html", "downloaded_repos/maxsite_cms/application/language/russian/calendar_lang.php", "downloaded_repos/maxsite_cms/application/language/russian/calendar_lang.php-rus", "downloaded_repos/maxsite_cms/application/language/russian/date_lang.php", "downloaded_repos/maxsite_cms/application/language/russian/db_lang.php", "downloaded_repos/maxsite_cms/application/language/russian/email_lang.php", "downloaded_repos/maxsite_cms/application/language/russian/ftp_lang.php", "downloaded_repos/maxsite_cms/application/language/russian/imglib_lang.php", "downloaded_repos/maxsite_cms/application/language/russian/info.txt", "downloaded_repos/maxsite_cms/application/language/russian/number_lang.php", "downloaded_repos/maxsite_cms/application/language/russian/profiler_lang.php", "downloaded_repos/maxsite_cms/application/language/russian/scaffolding_lang.php", "downloaded_repos/maxsite_cms/application/language/russian/unit_test_lang.php", "downloaded_repos/maxsite_cms/application/language/russian/upload_lang.php", "downloaded_repos/maxsite_cms/application/language/russian/validation_lang.php", "downloaded_repos/maxsite_cms/application/libraries/maxsite_lib.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/admin.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/common.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/default.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/index.html", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_cat/admin.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_cat/do-ajax.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_cat/index.html", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_cat/index.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_cat/info.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_cat/script.js", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_cat/style.css", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_comments/admin.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_comments/edit.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_comments/index.html", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_comments/index.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_comments/info.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_comusers/comusers.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_comusers/edit.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_comusers/index.html", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_comusers/index.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_comusers/info.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_files/admin.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_files/document_plain.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_files/index.html", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_files/index.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_files/info.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_files/mp3.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_home/admin.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_home/index.html", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_home/index.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_home/info.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_menu/index.html", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_menu/index.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_menu/info.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_options/admin.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_options/editor.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_options/general.ini", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_options/general.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_options/index.html", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_options/index.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_options/info.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_options/other.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_options/page-type.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_options/templates.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_page/admin.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_page/all-files-update-ajax.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_page/all-files.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_page/all_meta.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_page/bsave-post-ajax.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_page/edit.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_page/filedrag.js", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_page/form-do_script.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_page/form.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_page/index.html", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_page/index.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_page/info.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_page/meta.ini", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_page/new.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_page/post-edit-functions.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_page/post-edit.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_page/uploads-require-maxsite.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_plugin_options/example.txt", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_plugin_options/index.html", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_plugin_options/index.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_plugin_options/info.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_plugins/admin.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_plugins/index.html", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_plugins/index.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_plugins/info.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_sidebars/admin.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_sidebars/index.html", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_sidebars/index.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_sidebars/info.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_sidebars/sidebars.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_sidebars/widgets.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_users/admin.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_users/edit.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_users/group.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_users/index.html", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_users/index.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_users/info.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_users/my_profile.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_users/users.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/auto_post/admin.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/auto_post/filedrag.js", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/auto_post/index.html", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/auto_post/index.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/auto_post/info.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/auto_post/lib/add-new-page.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/auto_post/lib/export-page.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/auto_post/lib/format.txt", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/auto_post/lib/test-file.txt", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/auto_post/style.css", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/auto_post/uploads-require-maxsite.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_files/admin.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_files/index.html", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_files/index.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_files/info.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_files/load-file-ajax.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_files/save-file-ajax.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/autosave-post-ajax.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/bb.js.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/bb.style.css", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/color-table.html", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/editor-bb.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/abbr.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/add.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/address.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/audio.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/bold.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/center.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/cite.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/clean.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/code.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/colors.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/csharp.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/css.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/cut.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/delphi.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/dfn.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/dl.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/document_break.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/document_quote.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/flash.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/fonts.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/fullscreen.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/grin.gif", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/h1.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/h2.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/h3.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/h4.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/h5.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/h6.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/handle.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/help.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/highlight.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/hr-line.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/html-pre.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/html.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/image.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/image_add.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/italic.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/js.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/justify.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/left.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/link.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/list-bullet.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/list-item.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/list-numeric.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/menu.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/movies.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/page_red.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/paragraph.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/php.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/picture.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/preview.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/qsave.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/quotes.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/replace.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/right.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/script_edit.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/select_colors.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/separator.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/small.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/sql.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/stroke.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/sub.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/submenu.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/sup.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/table.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/table_add.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/table_go.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/table_row_insert.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/table_select.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/text.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/text_padding_center.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/text_padding_justify.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/text_padding_left.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/text_padding_right.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/text_smallcaps.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/underline.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/images/xml.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/index.html", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/index.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/info.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/jquery.markitup.js", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/preview-ajax.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/style.css", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/index.html", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/template_options/index.html", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/template_options/index.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/template_options/info.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/template_options/options.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/template_options/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/css/-lazy.css", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/css/style.css", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/fonts/fontawesome/fontawesome-webfont.woff", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/fonts/fontawesome/fontawesome-webfont.woff2", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/fonts/opensans/open-sans-v15-latin_cyrillic-700.woff", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/fonts/opensans/open-sans-v15-latin_cyrillic-700.woff2", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/fonts/opensans/open-sans-v15-latin_cyrillic-700italic.woff", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/fonts/opensans/open-sans-v15-latin_cyrillic-700italic.woff2", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/fonts/opensans/open-sans-v15-latin_cyrillic-italic.woff", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/fonts/opensans/open-sans-v15-latin_cyrillic-italic.woff2", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/fonts/opensans/open-sans-v15-latin_cyrillic-regular.woff", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/fonts/opensans/open-sans-v15-latin_cyrillic-regular.woff2", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/images/avatar_user.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/images/favicons/favicon1.fw.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/images/favicons/favicon1.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/images/logo01.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/images/logo02.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/images/maxsitelogo.fw.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/images/maxsitelogo.png", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/js/jquery.fullscreen.js", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/js/jquery.tablesorter.js", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/js/jquery.tabs.js", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/js/my.js", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/less/main/base.less", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/less/main/extension-unicss.less", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/less/main/fontawesome.less", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/less/main/forms.less", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/less/main/jquery.datetimepicker.less", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/less/main/jquery.datetimepicker.min.less", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/less/main/loginform.less", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/less/main/my-template.less", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/less/main/variables.less", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/less/style.less", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/less/unicss/mixins/colors-gmd.html", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/less/unicss/mixins/colors-gmd.less", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/less/unicss/mixins/less-framework-lite.less", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/less/unicss/unicss.less", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/index.html", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/loginform.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/profiles/font-arial.css", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/profiles/font-georgia.css", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/profiles/font-opensans.css", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/profiles/font-segoe.css", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/profiles/font-verdana.css", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/profiles/theme-red.css", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/template.php", "downloaded_repos/maxsite_cms/application/maxsite/admin/template/index.html", "downloaded_repos/maxsite_cms/application/maxsite/common/category.php", "downloaded_repos/maxsite_cms/application/maxsite/common/comments.php", "downloaded_repos/maxsite_cms/application/maxsite/common/common.php", "downloaded_repos/maxsite_cms/application/maxsite/common/core/allows.php", "downloaded_repos/maxsite_cms/application/maxsite/common/core/block-pages.php", "downloaded_repos/maxsite_cms/application/maxsite/common/core/cache.php", "downloaded_repos/maxsite_cms/application/maxsite/common/core/cookie.php", "downloaded_repos/maxsite_cms/application/maxsite/common/core/debug.php", "downloaded_repos/maxsite_cms/application/maxsite/common/core/email.php", "downloaded_repos/maxsite_cms/application/maxsite/common/core/format.php", "downloaded_repos/maxsite_cms/application/maxsite/common/core/helpers.php", "downloaded_repos/maxsite_cms/application/maxsite/common/core/hooks.php", "downloaded_repos/maxsite_cms/application/maxsite/common/core/images.php", "downloaded_repos/maxsite_cms/application/maxsite/common/core/info.php", "downloaded_repos/maxsite_cms/application/maxsite/common/core/init.php", "downloaded_repos/maxsite_cms/application/maxsite/common/core/language.php", "downloaded_repos/maxsite_cms/application/maxsite/common/core/log.php", "downloaded_repos/maxsite_cms/application/maxsite/common/core/login.php", "downloaded_repos/maxsite_cms/application/maxsite/common/core/meta.php", "downloaded_repos/maxsite_cms/application/maxsite/common/core/options.php", "downloaded_repos/maxsite_cms/application/maxsite/common/core/page-out.php", "downloaded_repos/maxsite_cms/application/maxsite/common/core/plugins.php", "downloaded_repos/maxsite_cms/application/maxsite/common/core/security.php", "downloaded_repos/maxsite_cms/application/maxsite/common/core/shortcode.php", "downloaded_repos/maxsite_cms/application/maxsite/common/core/sidebar.php", "downloaded_repos/maxsite_cms/application/maxsite/common/core/template.php", "downloaded_repos/maxsite_cms/application/maxsite/common/core/thumb.php", "downloaded_repos/maxsite_cms/application/maxsite/common/core/tmpl.php", "downloaded_repos/maxsite_cms/application/maxsite/common/core/units.php", "downloaded_repos/maxsite_cms/application/maxsite/common/core/url.php", "downloaded_repos/maxsite_cms/application/maxsite/common/functions-edit.php", "downloaded_repos/maxsite_cms/application/maxsite/common/idna.php", "downloaded_repos/maxsite_cms/application/maxsite/common/inifile.php", "downloaded_repos/maxsite_cms/application/maxsite/common/jquery/alerts/images/help.gif", "downloaded_repos/maxsite_cms/application/maxsite/common/jquery/alerts/images/important.gif", "downloaded_repos/maxsite_cms/application/maxsite/common/jquery/alerts/images/info.gif", "downloaded_repos/maxsite_cms/application/maxsite/common/jquery/alerts/images/title.gif", "downloaded_repos/maxsite_cms/application/maxsite/common/jquery/alerts/jquery.alerts.css", "downloaded_repos/maxsite_cms/application/maxsite/common/jquery/alerts/jquery.alerts.js", "downloaded_repos/maxsite_cms/application/maxsite/common/jquery/jquery.cookie.js", "downloaded_repos/maxsite_cms/application/maxsite/common/jquery/jquery.scrollto.js", "downloaded_repos/maxsite_cms/application/maxsite/common/jquery/jquery.showhide.js", "downloaded_repos/maxsite_cms/application/maxsite/common/jquery/jscolor/arrow.gif", "downloaded_repos/maxsite_cms/application/maxsite/common/jquery/jscolor/cross.gif", "downloaded_repos/maxsite_cms/application/maxsite/common/jquery/jscolor/hs.png", "downloaded_repos/maxsite_cms/application/maxsite/common/jquery/jscolor/hv.png", "downloaded_repos/maxsite_cms/application/maxsite/common/jquery/jscolor/jscolor.js", "downloaded_repos/maxsite_cms/application/maxsite/common/language/convert-po-to-lang.zip", "downloaded_repos/maxsite_cms/application/maxsite/common/language/de.php", "downloaded_repos/maxsite_cms/application/maxsite/common/language/en-f.php", "downloaded_repos/maxsite_cms/application/maxsite/common/language/en.php", "downloaded_repos/maxsite_cms/application/maxsite/common/language/lang.zip", "downloaded_repos/maxsite_cms/application/maxsite/common/language/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/common/language/ro.php", "downloaded_repos/maxsite_cms/application/maxsite/common/language/ua.php", "downloaded_repos/maxsite_cms/application/maxsite/common/page/format.php", "downloaded_repos/maxsite_cms/application/maxsite/common/page/get-pages.php", "downloaded_repos/maxsite_cms/application/maxsite/common/page/other-pages.php", "downloaded_repos/maxsite_cms/application/maxsite/common/page/page-nav.php", "downloaded_repos/maxsite_cms/application/maxsite/common/page/view-count.php", "downloaded_repos/maxsite_cms/application/maxsite/common/page.php", "downloaded_repos/maxsite_cms/application/maxsite/common/psr4/Pattern/Singleton.php", "downloaded_repos/maxsite_cms/application/maxsite/common/psr4/Pdo/PdoConnect.php", "downloaded_repos/maxsite_cms/application/maxsite/common/psr4/Pdo/PdoQuery.php", "downloaded_repos/maxsite_cms/application/maxsite/common/psr4/autoload.php", "downloaded_repos/maxsite_cms/application/maxsite/common/uploads.php", "downloaded_repos/maxsite_cms/application/maxsite/mso_config.php-distr", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images/blogger.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images/bobrdobr.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images/delicious.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images/digg.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images/facebook.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images/friendfeed.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images/google-bookmarks.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images/gplusone.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images/liveinternet.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images/livejournal.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images/mail-ru.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images/memori.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images/mister-wong.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images/myspace.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images/odnoklassniki.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images/pikabu.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images/rutvit.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images/technorati.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images/twitter.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images/vkontakte.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images/yahoo-bookmarks.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images/yandex.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images/yaru.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images24/blogger.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images24/bobrdobr.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images24/delicious.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images24/digg.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images24/facebook.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images24/friendfeed.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images24/google-bookmarks.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images24/gplusone.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images24/liveinternet.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images24/livejournal.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images24/mail-ru.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images24/memori.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images24/mister-wong.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images24/myspace.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images24/odnoklassniki.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images24/pikabu.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images24/rutvit.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images24/technorati.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images24/twitter.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images24/vkontakte.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images24/yahoo-bookmarks.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images24/yandex.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images24/yaru.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images32/blogger.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images32/bobrdobr.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images32/delicious.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images32/digg.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images32/facebook.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images32/friendfeed.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images32/google-bookmarks.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images32/gplusone.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images32/liveinternet.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images32/livejournal.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images32/mail-ru.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images32/memori.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images32/mister-wong.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images32/myspace.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images32/odnoklassniki.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images32/pikabu.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images32/rutvit.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images32/technorati.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images32/twitter.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images32/vkontakte.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images32/yahoo-bookmarks.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images32/yandex.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/images32/yaru.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/addzakl/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/admin_ip/admin.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/admin_ip/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/admin_ip/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/admin_ip/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/antispam/admin.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/antispam/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/antispam/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/antispam/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/any_file/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/any_file/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/auth_content/_custom.css", "downloaded_repos/maxsite_cms/application/maxsite/plugins/auth_content/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/auth_content/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/authors/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/authors/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/authors/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/bbcode/bbcode-help.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/bbcode/bg58.gif", "downloaded_repos/maxsite_cms/application/maxsite/plugins/bbcode/create_list_help.txt", "downloaded_repos/maxsite_cms/application/maxsite/plugins/bbcode/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/bbcode/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/bbcode/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/bbcode/language/en.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/bbcode/language/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/bbcode/show_help.txt", "downloaded_repos/maxsite_cms/application/maxsite/plugins/bbcode/text-demo.txt", "downloaded_repos/maxsite_cms/application/maxsite/plugins/bbcode/text-normalize.txt", "downloaded_repos/maxsite_cms/application/maxsite/plugins/calendar/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/calendar/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/calendar/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/captcha/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/captcha/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/captcha/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/catclouds/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/catclouds/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/catclouds/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/category/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/category/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/category/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/comment_button/comment_button.js", "downloaded_repos/maxsite_cms/application/maxsite/plugins/comment_button/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/comment_button/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/comment_button/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/comment_smiles/bg.gif", "downloaded_repos/maxsite_cms/application/maxsite/plugins/comment_smiles/comment_smiles.js", "downloaded_repos/maxsite_cms/application/maxsite/plugins/comment_smiles/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/comment_smiles/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/comment_smiles/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/comment_smiles/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/plugins/cron/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/cron/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/cron/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/down_count/admin.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/down_count/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/down_count/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/down_count/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/editor.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/events/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/events/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/events/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/faq/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/faq/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/faq/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/favorites/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/favorites/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/favorites/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/fbauth/images/step1.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/fbauth/images/step2.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/fbauth/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/fbauth/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/fbauth/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/feedburner/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/feedburner/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/feedburner/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/forms/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/forms/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/forms/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/forms/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/plugins/global_cache/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/global_cache/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/global_cache/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/guestbook/admin.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/guestbook/edit.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/guestbook/editone.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/guestbook/guestbook.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/guestbook/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/guestbook/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/guestbook/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/internal_links/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/internal_links/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/internal_links/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/last_comments/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/last_comments/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/last_comments/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/last_pages/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/last_pages/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/last_pages/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/last_pages_unit/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/last_pages_unit/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/last_pages_unit/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/last_pages_unit/last-pages.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/lightbox/css/-jquery.lightbox-0.5.css", "downloaded_repos/maxsite_cms/application/maxsite/plugins/lightbox/css/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/lightbox/css/jquery.lightbox-0.5.css", "downloaded_repos/maxsite_cms/application/maxsite/plugins/lightbox/help.txt", "downloaded_repos/maxsite_cms/application/maxsite/plugins/lightbox/images/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/lightbox/images/lightbox-blank.gif", "downloaded_repos/maxsite_cms/application/maxsite/plugins/lightbox/images/lightbox-btn-close.gif", "downloaded_repos/maxsite_cms/application/maxsite/plugins/lightbox/images/lightbox-btn-next.gif", "downloaded_repos/maxsite_cms/application/maxsite/plugins/lightbox/images/lightbox-btn-prev.gif", "downloaded_repos/maxsite_cms/application/maxsite/plugins/lightbox/images/lightbox-ico-loading.gif", "downloaded_repos/maxsite_cms/application/maxsite/plugins/lightbox/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/lightbox/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/lightbox/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/lightbox/js/-jquery.lightbox.js", "downloaded_repos/maxsite_cms/application/maxsite/plugins/lightbox/js/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/lightbox/js/jquery.lightbox.js", "downloaded_repos/maxsite_cms/application/maxsite/plugins/links/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/links/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/links/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/login_form/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/login_form/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/login_form/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/loginza_auth/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/loginza_auth/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/loginza_auth/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/loginza_auth/sign_in_big_buttons.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/loginza_auth/sign_in_button_gray.gif", "downloaded_repos/maxsite_cms/application/maxsite/plugins/mail_send/admin.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/mail_send/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/mail_send/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/mail_send/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/meta_robots/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/meta_robots/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/mkj_sc/changelog.txt", "downloaded_repos/maxsite_cms/application/maxsite/plugins/mkj_sc/images/auto/1.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/mkj_sc/images/auto/2.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/mkj_sc/images/auto/3.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/mkj_sc/images/auto/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/mkj_sc/images/blackphoto/1.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/mkj_sc/images/blackphoto/2.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/mkj_sc/images/blackphoto/3.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/mkj_sc/images/blackphoto/4.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/mkj_sc/images/blackphoto/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/mkj_sc/images/computers/1.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/mkj_sc/images/computers/2.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/mkj_sc/images/computers/3.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/mkj_sc/images/computers/4.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/mkj_sc/images/computers/5.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/mkj_sc/images/computers/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/mkj_sc/images/default/1.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/mkj_sc/images/default/2.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/mkj_sc/images/default/3.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/mkj_sc/images/default/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/mkj_sc/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/mkj_sc/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/mkj_sc/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/mkj_sc/mkj_sc.css", "downloaded_repos/maxsite_cms/application/maxsite/plugins/multipage/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/multipage/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/multipage/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/open_graph/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/open_graph/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/page_comments/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/page_comments/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/page_comments/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/page_parent/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/page_parent/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/page_parent/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/page_views/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/page_views/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/page_views/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/pagination/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/pagination/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/pagination/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/pagination2/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/pagination2/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/pagination2/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/paginator/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/paginator/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/paginator/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/paginator/paginator.css", "downloaded_repos/maxsite_cms/application/maxsite/plugins/paginator/paginator.js", "downloaded_repos/maxsite_cms/application/maxsite/plugins/paginator/slider_knob.gif", "downloaded_repos/maxsite_cms/application/maxsite/plugins/parser_default/default.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/parser_default/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/parser_default/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/parser_markdown/Parsedown/LICENSE.txt", "downloaded_repos/maxsite_cms/application/maxsite/plugins/parser_markdown/Parsedown/Parsedown.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/parser_markdown/Parsedown/README.md", "downloaded_repos/maxsite_cms/application/maxsite/plugins/parser_markdown/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/parser_markdown/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/parser_simple/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/parser_simple/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/parser_simple/simple.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/perelinks/admin.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/perelinks/change_log.txt", "downloaded_repos/maxsite_cms/application/maxsite/plugins/perelinks/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/perelinks/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/perelinks/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/popup/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/popup/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/protect_pre/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/protect_pre/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/psevdocode/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/psevdocode/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/psevdocode/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/random_gal/gallery.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/random_gal/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/random_gal/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/random_gal/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/random_gal/jquery.nivo.slider.js", "downloaded_repos/maxsite_cms/application/maxsite/plugins/random_gal/random_gal.css", "downloaded_repos/maxsite_cms/application/maxsite/plugins/random_pages/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/random_pages/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/random_pages/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/randomtext/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/randomtext/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/randomtext/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/range_url/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/range_url/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/range_url/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/rater/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/rater/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/rater/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/rater/jquery.rater.js", "downloaded_repos/maxsite_cms/application/maxsite/plugins/rater/rater.css", "downloaded_repos/maxsite_cms/application/maxsite/plugins/rater/ratings-post-ajax.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/rater/star.gif", "downloaded_repos/maxsite_cms/application/maxsite/plugins/rater/star.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/rater/star_small.gif", "downloaded_repos/maxsite_cms/application/maxsite/plugins/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/plugins/redirect/admin.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/redirect/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/redirect/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/redirect/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/rss_get/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/rss_get/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/rss_get/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/rss_get/lastrss.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/run_php/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/run_php/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/run_php/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/TODO.txt", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/admin.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/ajax-ajax.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/ajax-loader.gif", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/change_log.txt", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/css/jTPS.css", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/css/jquery-ui-1.8.16.custom.css", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/css/style.css", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/css/style_admin.css", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/img/1.gif", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/img/arrow.gif", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/img/arrow_down.gif", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/img/arrow_up.gif", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/img/close.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/img/del.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/img/del_ans.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/img/edit.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/img/js/ui-bg_flat_0_aaaaaa_40x100.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/img/js/ui-bg_flat_55_fbec88_40x100.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/img/js/ui-bg_glass_75_d0e5f5_1x400.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/img/js/ui-bg_glass_85_dfeffc_1x400.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/img/js/ui-bg_glass_95_fef1ec_1x400.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/img/js/ui-bg_gloss-wave_55_5c9ccc_500x100.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/img/js/ui-bg_inset-hard_100_f5f8f9_1x100.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/img/js/ui-bg_inset-hard_100_fcfdfd_1x100.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/img/js/ui-icons_217bc0_256x240.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/img/js/ui-icons_2e83ff_256x240.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/img/js/ui-icons_469bdd_256x240.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/img/js/ui-icons_6da8d5_256x240.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/img/js/ui-icons_cd0a0a_256x240.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/img/js/ui-icons_d8e7f3_256x240.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/img/js/ui-icons_f9bd01_256x240.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/img/link.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/img/link_old.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/img/log.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/img/open.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/img/plus.png", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/install.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/install.txt", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/js/admin.js", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/js/jTPS.js", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/js/kernel.js", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/language/_en.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/language/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/list-ajax.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/list.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/logs.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/manage.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/settings.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/sp_kernel.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/sape/admin.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/sape/articles_template.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/sape/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/sape/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/sape/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/search_form/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/search_form/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/search_form/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/shjs/css/sh_bright.min.css", "downloaded_repos/maxsite_cms/application/maxsite/plugins/shjs/css/sh_emacs.min.css", "downloaded_repos/maxsite_cms/application/maxsite/plugins/shjs/css/sh_ide-anjuta.min.css", "downloaded_repos/maxsite_cms/application/maxsite/plugins/shjs/css/sh_ide-codewarrior.min.css", "downloaded_repos/maxsite_cms/application/maxsite/plugins/shjs/css/sh_ide-devcpp.min.css", "downloaded_repos/maxsite_cms/application/maxsite/plugins/shjs/css/sh_ide-eclipse.min.css", "downloaded_repos/maxsite_cms/application/maxsite/plugins/shjs/css/sh_ide-kdev.min.css", "downloaded_repos/maxsite_cms/application/maxsite/plugins/shjs/css/sh_ide-msvcpp.min.css", "downloaded_repos/maxsite_cms/application/maxsite/plugins/shjs/css/sh_kwrite.min.css", "downloaded_repos/maxsite_cms/application/maxsite/plugins/shjs/css/sh_matlab.min.css", "downloaded_repos/maxsite_cms/application/maxsite/plugins/shjs/css/sh_maxsite.min.css", "downloaded_repos/maxsite_cms/application/maxsite/plugins/shjs/css/sh_rand01.min.css", "downloaded_repos/maxsite_cms/application/maxsite/plugins/shjs/css/sh_the.min.css", "downloaded_repos/maxsite_cms/application/maxsite/plugins/shjs/css/sh_typical.min.css", "downloaded_repos/maxsite_cms/application/maxsite/plugins/shjs/css/sh_whitengrey.min.css", "downloaded_repos/maxsite_cms/application/maxsite/plugins/shjs/css/sh_zellner.min.css", "downloaded_repos/maxsite_cms/application/maxsite/plugins/shjs/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/shjs/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/shjs/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/sitemap/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/sitemap/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/sitemap/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/smiles/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/smiles/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/smiles/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/smtp_mail/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/smtp_mail/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/spoiler/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/spoiler/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/spoiler/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/spoiler/style/add.gif", "downloaded_repos/maxsite_cms/application/maxsite/plugins/spoiler/style/del.gif", "downloaded_repos/maxsite_cms/application/maxsite/plugins/spoiler/style/spoiler.css", "downloaded_repos/maxsite_cms/application/maxsite/plugins/spoiler/style/spoiler2.css", "downloaded_repos/maxsite_cms/application/maxsite/plugins/spoiler/style/spoiler3.css", "downloaded_repos/maxsite_cms/application/maxsite/plugins/tabs/-tabs.js", "downloaded_repos/maxsite_cms/application/maxsite/plugins/tabs/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/tabs/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/tabs/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/tabs/tabs-help.txt", "downloaded_repos/maxsite_cms/application/maxsite/plugins/tabs/tabs.js", "downloaded_repos/maxsite_cms/application/maxsite/plugins/tabs/to_css_template.css", "downloaded_repos/maxsite_cms/application/maxsite/plugins/tagclouds/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/tagclouds/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/tagclouds/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/text_block/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/text_block/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/text_block/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/theme_switch/admin.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/theme_switch/css-style.scss", "downloaded_repos/maxsite_cms/application/maxsite/plugins/theme_switch/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/theme_switch/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/theme_switch/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/top_commentators/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/top_commentators/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/top_commentators/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/tweetmeme_com/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/tweetmeme_com/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/tweetmeme_com/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/ushki/admin.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/ushki/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/ushki/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/ushki/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/ushki_to_hook/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/ushki_to_hook/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/ushki_to_hook/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/wpconvert/admin.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/wpconvert/export-max.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/wpconvert/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/wpconvert/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/wpconvert/info.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/wpconvert/magpierss/extlib/Snoopy.class.inc", "downloaded_repos/maxsite_cms/application/maxsite/plugins/wpconvert/magpierss/extlib/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/wpconvert/magpierss/htdocs/AUTHORS", "downloaded_repos/maxsite_cms/application/maxsite/plugins/wpconvert/magpierss/htdocs/README", "downloaded_repos/maxsite_cms/application/maxsite/plugins/wpconvert/magpierss/htdocs/TROUBLESHOOTING", "downloaded_repos/maxsite_cms/application/maxsite/plugins/wpconvert/magpierss/htdocs/cookbook", "downloaded_repos/maxsite_cms/application/maxsite/plugins/wpconvert/magpierss/htdocs/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/wpconvert/magpierss/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/wpconvert/magpierss/rss_cache.inc", "downloaded_repos/maxsite_cms/application/maxsite/plugins/wpconvert/magpierss/rss_fetch.inc", "downloaded_repos/maxsite_cms/application/maxsite/plugins/wpconvert/magpierss/rss_parse.inc", "downloaded_repos/maxsite_cms/application/maxsite/plugins/wpconvert/magpierss/rss_utils.inc", "downloaded_repos/maxsite_cms/application/maxsite/plugins/xml_sitemap/index.html", "downloaded_repos/maxsite_cms/application/maxsite/plugins/xml_sitemap/index.php", "downloaded_repos/maxsite_cms/application/maxsite/plugins/xml_sitemap/info.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/blanks/plugins/admin.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/blanks/plugins/info.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/blanks/plugins/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/shared/blanks/plugins/template for plugins-admin.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/blanks/plugins/template for plugins-all.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/blanks/plugins/template for plugins.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/blanks/plugins/template for widgets.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/blanks/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/shared/main/main-end.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/main/main-start.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/main/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/shared/meta/meta.ini", "downloaded_repos/maxsite_cms/application/maxsite/shared/meta/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/shared/options/default/011_home_units.ini", "downloaded_repos/maxsite_cms/application/maxsite/shared/options/default/01_home.ini", "downloaded_repos/maxsite_cms/application/maxsite/shared/options/default/025_main_template.ini", "downloaded_repos/maxsite_cms/application/maxsite/shared/options/default/027_head_site.ini", "downloaded_repos/maxsite_cms/application/maxsite/shared/options/default/02_general.ini", "downloaded_repos/maxsite_cms/application/maxsite/shared/options/default/04_menus.ini", "downloaded_repos/maxsite_cms/application/maxsite/shared/options/default/05_css.ini", "downloaded_repos/maxsite_cms/application/maxsite/shared/options/default/065_images.ini", "downloaded_repos/maxsite_cms/application/maxsite/shared/options/default/067_social.ini", "downloaded_repos/maxsite_cms/application/maxsite/shared/options/default/06_profiles.ini", "downloaded_repos/maxsite_cms/application/maxsite/shared/options/default/07_blocks.ini", "downloaded_repos/maxsite_cms/application/maxsite/shared/options/options.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/options/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/_def_out/full/full.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/_def_out/full/units/full-default.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/_def_out/list/list.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/_def_out/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/archive/archive.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/archive/units/archive-full.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/archive/units/archive-list.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/author/author.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/author/units/author-default.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/author/units/author-full.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/author/units/author-header.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/author/units/author-list.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/category/category.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/category/units/category-default.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/category/units/category-full.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/category/units/category-header.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/category/units/category-list.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/comments/comments.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/contact/contact.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/feed/category.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/feed/comments.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/feed/home.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/feed/page.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/home/<USER>", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/home/<USER>/home-cat-block-full.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/home/<USER>/home-cat-block-list.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/home/<USER>/home-full.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/home/<USER>/home-last-page.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/home/<USER>/home-list.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/home/<USER>/home-text-top.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/home/<USER>/home-top-page.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/loginform/loginform.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/loginform/units/loginform-common-tmpl.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/loginform/units/loginform-comuser-tmpl.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/loginform/units/loginform-error.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/loginform/units/loginform-tmpl.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/loginform/units/loginform-user-tmpl.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/page/page.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/page/units/page-comment-form-tmpl.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/page/units/page-comment-to-login-tmpl.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/page/units/page-comments-article-tmpl.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/page/units/page-comments-count-tmpl.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/page/units/page-comments-other-system.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/page/units/page-comments.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/page/units/page-password-content-form.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/page_404/page_404.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/page_404/units/page_404.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/password-recovery/password-recovery.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/password-recovery/units/password-recovery-tmpl.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/registration/registration.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/registration/units/registration-tmpl.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/search/search.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/search/units/form-tmpl.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/sitemap/sitemap.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/tag/tag.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/tag/units/tag-default.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/tag/units/tag-full.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/tag/units/tag-header.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/tag/units/tag-list.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/users/units/users-all.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/users/units/users-comments-tmpl.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/users/units/users-form-edit-tmpl.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/users/units/users-form-lost.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/users/units/users-form.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/users/units/users-loginform-tmpl.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/users/units/users-lost-no-activate-tmpl.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/users/units/users-replace-password-tmpl.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/users/units/users-tmpl.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/users/units/users.php", "downloaded_repos/maxsite_cms/application/maxsite/shared/type/users/users.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/css/berry/LICENSE", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/css/berry/README.md", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/css/berry/berry.css", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/css/fonts/opensans.css", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/css/fonts/robotoslab.css", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/css/menu/menu1.css", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/css/menu/menu1alt-gray.css", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/css/menu/menu1alt.css", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/css/mf.css", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/css/mfont.css", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/css/profiles/font-arial.css", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/css/profiles/font-georgia.css", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/css/profiles/font-verdana.css", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/css/profiles/fontawesome5-lazy.css", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/css/profiles/landing.css", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/css/profiles/paragraph-justify.css", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/css/themes/default.css", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/css/themes/green.css", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/css/themes/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/fonts/fontawesome5/fa-brands-400.woff", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/fonts/fontawesome5/fa-brands-400.woff2", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/fonts/fontawesome5/fa-regular-400.woff", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/fonts/fontawesome5/fa-regular-400.woff2", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/fonts/fontawesome5/fa-solid-900.woff", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/fonts/fontawesome5/fa-solid-900.woff2", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/fonts/mfont/mfont.woff", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/fonts/mfont/mfont.woff2", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/fonts/opensans/open-sans-v18-latin_cyrillic-300.woff", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/fonts/opensans/open-sans-v18-latin_cyrillic-300.woff2", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/fonts/opensans/open-sans-v18-latin_cyrillic-300italic.woff", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/fonts/opensans/open-sans-v18-latin_cyrillic-300italic.woff2", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/fonts/opensans/open-sans-v18-latin_cyrillic-600.woff", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/fonts/opensans/open-sans-v18-latin_cyrillic-600.woff2", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/fonts/opensans/open-sans-v18-latin_cyrillic-600italic.woff", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/fonts/opensans/open-sans-v18-latin_cyrillic-600italic.woff2", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/fonts/opensans/open-sans-v18-latin_cyrillic-700.woff", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/fonts/opensans/open-sans-v18-latin_cyrillic-700.woff2", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/fonts/opensans/open-sans-v18-latin_cyrillic-700italic.woff", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/fonts/opensans/open-sans-v18-latin_cyrillic-700italic.woff2", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/fonts/opensans/open-sans-v18-latin_cyrillic-800.woff", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/fonts/opensans/open-sans-v18-latin_cyrillic-800.woff2", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/fonts/opensans/open-sans-v18-latin_cyrillic-800italic.woff", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/fonts/opensans/open-sans-v18-latin_cyrillic-800italic.woff2", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/fonts/opensans/open-sans-v18-latin_cyrillic-italic.woff", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/fonts/opensans/open-sans-v18-latin_cyrillic-italic.woff2", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/fonts/opensans/open-sans-v18-latin_cyrillic-regular.woff", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/fonts/opensans/open-sans-v18-latin_cyrillic-regular.woff2", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/fonts/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/fonts/robotoslab/roboto-slab-v8-cyrillic_latin-700.woff", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/fonts/robotoslab/roboto-slab-v8-cyrillic_latin-700.woff2", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/fonts/robotoslab/roboto-slab-v8-cyrillic_latin-regular.woff", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/fonts/robotoslab/roboto-slab-v8-cyrillic_latin-regular.woff2", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/images/elements/hr1.png", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/images/froala/License.txt", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/images/froala/hero/blue.svg", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/images/froala/hero/purple.svg", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/images/froala/hero/red.svg", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/images/froala/hero/yellow.svg", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/images/froala/shapes/1.svg", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/images/froala/shapes/10.svg", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/images/froala/shapes/2.svg", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/images/froala/shapes/4.svg", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/images/froala/shapes/5.svg", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/images/froala/shapes/6.svg", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/images/froala/shapes/7.svg", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/images/froala/shapes/8.svg", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/images/froala/shapes/9.svg", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/images/gravatar-default.png", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/images/logo.png", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/images/logos/3.png", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/images/logos/Organization_logo.png", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/images/logos/logo01.png", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/images/thumb-placehold.jpg", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/js/autoload/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/js/jquery.pursuingnav.js", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/js/lazy/my.js", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/js/lazy/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/js/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/fonts/opensans.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/fonts/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/fonts/robotoslab.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/menu/core/_dark.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/menu/core/_generation.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/menu/core/_im_variables.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/menu/core/_light-gray.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/menu/core/_light.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/menu/menu1.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/menu/menu1alt-gray.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/menu/menu1alt.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/menu/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/mf/_button.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/mf/_comments.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/mf/_components.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/mf/_default.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/mf/_forms.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/mf/_im_variables.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/mf/_lightgallery.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/mf/_menu-simple.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/mf/_message.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/mf/_page-other-pages.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/mf/_plugins.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/mf/_print.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/mf/_search.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/mf/_social.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/mf/_template.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/mf/_type_file.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/mf/_type_foreach.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/mf/_type_map.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/mf/_widgets.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/mf/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/mf.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/profiles/font-arial.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/profiles/font-georgia.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/profiles/font-verdana.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/profiles/fontawesome5-lazy.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/profiles/landing.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/profiles/paragraph-justify.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/sass/profiles/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/_menu/_menu.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/_social/_social.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/any1/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/any1/options.ini", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/any2/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/any2/options.ini", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/breadcrumbs/_breadcrumbs_link.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/breadcrumbs/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/file1/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/file1/options.ini", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/file2/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/file2/options.ini", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/footer-copy-stat/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/footer-copy-stat/options.ini", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/header11/_default.txt", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/header11/design-block1.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/header11/design-block2.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/header11/effect1.js.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/header11/effect2.js.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/header11/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/header11/options.ini", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/header6/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/header6/modal.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/header6/options.ini", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/header6/search-form.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/header7/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/header7/options.ini", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/lightslider/_default.txt", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/lightslider/img/controls-color.png", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/lightslider/img/controls-dark.png", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/lightslider/img/controls-white.png", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/lightslider/img/controls.png", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/lightslider/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/lightslider/lightslider-shortcode.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/lightslider/lightslider.js", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/lightslider/options.ini", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/lightslider/style.css", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/menu/menu.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/module1/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/module1/options.ini", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/module2/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/module2/options.ini", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/custom/cart/cart-ajax.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/custom/cart/cart.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/custom/cart/js/cart-my.js", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/custom/cart/js/cart.js", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/custom/cart/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/custom/extra/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/custom/head-start.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/custom/head.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/custom/meta/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/custom/my/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/custom/my-category-meta.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/custom/my-editor-files.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/custom/my-template.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/custom/my_meta.ini", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/custom/options/10_general.ini", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/custom/options/20_other.ini", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/custom/options/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/custom/options.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/custom/plugins/_demo/admin.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/custom/plugins/_demo/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/custom/plugins/new_module/admin.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/custom/plugins/new_module/functions.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/custom/plugins/new_module/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/custom/plugins/new_module/post.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/custom/plugins/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/custom/set-options.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/custom/set_val.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/custom/shortcode/include.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/custom/shortcode/lightslider.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/custom/shortcode/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/custom/shortcode/unit-module.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/custom/template.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/distr/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/distr/tiny-slider/LICENSE", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/distr/tiny-slider/README.md", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/distr/tiny-slider/assets/js-my-add.txt", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/distr/tiny-slider/assets/tiny-slider.css", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/distr/tiny-slider/assets/tiny-slider.js", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/distr/tiny-slider/assets/tiny-slider.scss", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/distr/tiny-slider/tiny-slider.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/functions.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/info.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/main/blocks/_end.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/main/blocks/_start.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/main/blocks/body-end.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/main/blocks/body-start.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/main/blocks/content.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/main/blocks/footer-pre.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/main/blocks/footer.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/main/blocks/header-out.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/main/blocks/header.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/main/blocks/main-end.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/main/blocks/main-start.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/main/container-all/main.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/main/content-only/main-function.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/main/content-only/main.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/main/fields/main.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/main/landing/main-function.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/main/landing/main.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/main/landing0/main-function.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/main/landing0/main.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/main/left-sidebar/main.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/main/left-sidebar-fixed/main.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/main/main.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/main/no-sidebar/main.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/main/no-sidebar-full-width/main.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/main/no-sidebar900/main.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/main/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/main/right-sidebar/main.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/modules/pages/pages-2col3/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/modules/pages/pages-2col3/screenshot.png", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/modules/pages/pages2/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/modules/pages/pages2/screenshot.png", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/modules/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/myparts/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/parts/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/parts/widgets/author-1.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/parts/widgets/last_pages_unit-1.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/screenshot.png", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/ads/ad1/content.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/ads/ad1/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/ads/ad1/screenshot.png", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/ads/ad2/content.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/ads/ad2/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/ads/ad2/screenshot.png", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/base/base1/content.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/base/base1/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/base/base1/screenshot.png", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/base/base2/content.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/base/base2/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/base/base2/screenshot.png", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/footer/3col-1/content.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/footer/3col-1/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/footer/3col-1/screenshot.png", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/footer/4col-1/content.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/footer/4col-1/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/footer/4col-1/screenshot.png", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/forms/form1/form-ajax.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/forms/form1/form.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/forms/form1/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/forms/form1/screenshot.png", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/pages/pages-2col1/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/pages/pages-2col1/screenshot.png", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/pages/pages-2col3/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/pages/pages-2col3/screenshot.png", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/pages/pages1/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/pages/pages1/screenshot.png", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/pages/pages2/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/pages/pages2/screenshot.png", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/prices/price1/content.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/prices/price1/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/prices/price1/screenshot.png", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/promo/promo1/content.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/promo/promo1/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/promo/promo1/screenshot.png", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/promo/promo2/content.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/promo/promo2/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/promo/promo2/screenshot.png", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/sliders/readme.txt", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/sliders/tiny-slider-content1/content.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/sliders/tiny-slider-content1/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/sliders/tiny-slider-content1/screenshot.png", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/sliders/tiny-slider-content1/slider.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/sliders/tiny-slider-pages1/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/sliders/tiny-slider-pages1/pages.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/sliders/tiny-slider-pages1/screenshot.png", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/sliders/tiny-slider-pages1/slider.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/tabs/tab1/content.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/tabs/tab1/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/tabs/tab1/screenshot.png", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/testimonials/testimonial1/content.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/testimonials/testimonial1/index.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/store/testimonials/testimonial1/screenshot.png", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/take/category-head-meta.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/take/full-default-content-end.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/take/info-top-full.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/take/info-top-page.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/take/page-content-full.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/type/cart/cart.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/type/home/<USER>/last-pages.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/type/home/<USER>", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/type/mfdesign/colors.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/type/mfdesign/elements.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/type/mfdesign/icons-im-config.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/type/mfdesign/icons-im.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/type/mfdesign/images/nature1.jpg", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/type/mfdesign/images/nature2.jpg", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/type/mfdesign/images/nature3.jpg", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/type/mfdesign/mfdesign.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/type/mfstore/admin-ajax.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/type/mfstore/admin.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/type/mfstore/mfstore.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/type_foreach/comments-do.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/type_foreach/info-top/full-2col.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/type_foreach/info-top/full-3col.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/type_foreach/info-top/full.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/type_foreach/info-top/full2.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/type_foreach/info-top/no-info.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/type_foreach/info-top/page/_next-prev.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/type_foreach/info-top/page/no-info.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/type_foreach/info-top/page/page-category-and-header-next-prev.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/type_foreach/info-top/page/page-category-and-header.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/type_foreach/info-top/page/page.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/type_foreach/info-top/page/page1.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/type_foreach/info-top/page/page2.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/type_foreach/mso-info-bottom-page.php", "downloaded_repos/maxsite_cms/application/maxsite/templates/default/type_foreach/page-other-pages-out.php", "downloaded_repos/maxsite_cms/application/storage/.htaccess", "downloaded_repos/maxsite_cms/application/views/admin.php", "downloaded_repos/maxsite_cms/application/views/ajax.php", "downloaded_repos/maxsite_cms/application/views/codeigniter_welcome.php", "downloaded_repos/maxsite_cms/application/views/index.php", "downloaded_repos/maxsite_cms/application/views/login.php", "downloaded_repos/maxsite_cms/application/views/logout.php", "downloaded_repos/maxsite_cms/application/views/remote.php", "downloaded_repos/maxsite_cms/application/views/require-maxsite.php", "downloaded_repos/maxsite_cms/index.php", "downloaded_repos/maxsite_cms/install/index.php", "downloaded_repos/maxsite_cms/install/installer/css/berry-colors-lite.min.css", "downloaded_repos/maxsite_cms/install/installer/css/berry-normalize.min.css", "downloaded_repos/maxsite_cms/install/installer/css/style.css", "downloaded_repos/maxsite_cms/install/installer/css/style.scss", "downloaded_repos/maxsite_cms/install/installer/distr/htaccess.txt", "downloaded_repos/maxsite_cms/install/installer/distr/model.sql.txt", "downloaded_repos/maxsite_cms/install/installer/distr/robots.txt", "downloaded_repos/maxsite_cms/install/installer/distr/sitemap.xml", "downloaded_repos/maxsite_cms/install/installer/first.php", "downloaded_repos/maxsite_cms/install/installer/form.php", "downloaded_repos/maxsite_cms/install/installer/functions.php", "downloaded_repos/maxsite_cms/install/installer/images/favicon.png", "downloaded_repos/maxsite_cms/install/installer/langs/en.php", "downloaded_repos/maxsite_cms/install/installer/langs/ru.php", "downloaded_repos/maxsite_cms/install/installer/langs/uk.php", "downloaded_repos/maxsite_cms/install/installer/post.php", "downloaded_repos/maxsite_cms/system/.htaccess", "downloaded_repos/maxsite_cms/system/core/Benchmark.php", "downloaded_repos/maxsite_cms/system/core/CodeIgniter.php", "downloaded_repos/maxsite_cms/system/core/Common.php", "downloaded_repos/maxsite_cms/system/core/Config.php", "downloaded_repos/maxsite_cms/system/core/Controller.php", "downloaded_repos/maxsite_cms/system/core/Exceptions.php", "downloaded_repos/maxsite_cms/system/core/Hooks.php", "downloaded_repos/maxsite_cms/system/core/Input.php", "downloaded_repos/maxsite_cms/system/core/Lang.php", "downloaded_repos/maxsite_cms/system/core/Loader.php", "downloaded_repos/maxsite_cms/system/core/Model.php", "downloaded_repos/maxsite_cms/system/core/Output.php", "downloaded_repos/maxsite_cms/system/core/Router.php", "downloaded_repos/maxsite_cms/system/core/Security.php", "downloaded_repos/maxsite_cms/system/core/URI.php", "downloaded_repos/maxsite_cms/system/core/Utf8.php", "downloaded_repos/maxsite_cms/system/core/index.html", "downloaded_repos/maxsite_cms/system/database/DB.php", "downloaded_repos/maxsite_cms/system/database/DB_active_rec.php", "downloaded_repos/maxsite_cms/system/database/DB_cache.php", "downloaded_repos/maxsite_cms/system/database/DB_driver.php", "downloaded_repos/maxsite_cms/system/database/DB_forge.php", "downloaded_repos/maxsite_cms/system/database/DB_result.php", "downloaded_repos/maxsite_cms/system/database/DB_utility.php", "downloaded_repos/maxsite_cms/system/database/drivers/index.html", "downloaded_repos/maxsite_cms/system/database/drivers/mysql/index.html", "downloaded_repos/maxsite_cms/system/database/drivers/mysql/mysql_driver.php", "downloaded_repos/maxsite_cms/system/database/drivers/mysql/mysql_forge.php", "downloaded_repos/maxsite_cms/system/database/drivers/mysql/mysql_result.php", "downloaded_repos/maxsite_cms/system/database/drivers/mysql/mysql_utility.php", "downloaded_repos/maxsite_cms/system/database/drivers/mysqli/index.html", "downloaded_repos/maxsite_cms/system/database/drivers/mysqli/mysqli_driver.php", "downloaded_repos/maxsite_cms/system/database/drivers/mysqli/mysqli_forge.php", "downloaded_repos/maxsite_cms/system/database/drivers/mysqli/mysqli_result.php", "downloaded_repos/maxsite_cms/system/database/drivers/mysqli/mysqli_utility.php", "downloaded_repos/maxsite_cms/system/database/index.html", "downloaded_repos/maxsite_cms/system/fonts/index.html", "downloaded_repos/maxsite_cms/system/helpers/array_helper.php", "downloaded_repos/maxsite_cms/system/helpers/captcha_helper.php", "downloaded_repos/maxsite_cms/system/helpers/cookie_helper.php", "downloaded_repos/maxsite_cms/system/helpers/date_helper.php", "downloaded_repos/maxsite_cms/system/helpers/directory_helper.php", "downloaded_repos/maxsite_cms/system/helpers/download_helper.php", "downloaded_repos/maxsite_cms/system/helpers/email_helper.php", "downloaded_repos/maxsite_cms/system/helpers/file_helper.php", "downloaded_repos/maxsite_cms/system/helpers/form_helper.php", "downloaded_repos/maxsite_cms/system/helpers/html_helper.php", "downloaded_repos/maxsite_cms/system/helpers/index.html", "downloaded_repos/maxsite_cms/system/helpers/inflector_helper.php", "downloaded_repos/maxsite_cms/system/helpers/language_helper.php", "downloaded_repos/maxsite_cms/system/helpers/number_helper.php", "downloaded_repos/maxsite_cms/system/helpers/path_helper.php", "downloaded_repos/maxsite_cms/system/helpers/security_helper.php", "downloaded_repos/maxsite_cms/system/helpers/smiley_helper.php", "downloaded_repos/maxsite_cms/system/helpers/string_helper.php", "downloaded_repos/maxsite_cms/system/helpers/text_helper.php", "downloaded_repos/maxsite_cms/system/helpers/typography_helper.php", "downloaded_repos/maxsite_cms/system/helpers/url_helper.php", "downloaded_repos/maxsite_cms/system/helpers/xml_helper.php", "downloaded_repos/maxsite_cms/system/index.html", "downloaded_repos/maxsite_cms/system/language/english/calendar_lang.php", "downloaded_repos/maxsite_cms/system/language/english/date_lang.php", "downloaded_repos/maxsite_cms/system/language/english/db_lang.php", "downloaded_repos/maxsite_cms/system/language/english/email_lang.php", "downloaded_repos/maxsite_cms/system/language/english/form_validation_lang.php", "downloaded_repos/maxsite_cms/system/language/english/ftp_lang.php", "downloaded_repos/maxsite_cms/system/language/english/imglib_lang.php", "downloaded_repos/maxsite_cms/system/language/english/index.html", "downloaded_repos/maxsite_cms/system/language/english/migration_lang.php", "downloaded_repos/maxsite_cms/system/language/english/number_lang.php", "downloaded_repos/maxsite_cms/system/language/english/profiler_lang.php", "downloaded_repos/maxsite_cms/system/language/english/unit_test_lang.php", "downloaded_repos/maxsite_cms/system/language/english/upload_lang.php", "downloaded_repos/maxsite_cms/system/language/index.html", "downloaded_repos/maxsite_cms/system/libraries/Cache/Cache.php", "downloaded_repos/maxsite_cms/system/libraries/Cache/drivers/Cache_apc.php", "downloaded_repos/maxsite_cms/system/libraries/Cache/drivers/Cache_dummy.php", "downloaded_repos/maxsite_cms/system/libraries/Cache/drivers/Cache_file.php", "downloaded_repos/maxsite_cms/system/libraries/Cache/drivers/Cache_memcached.php", "downloaded_repos/maxsite_cms/system/libraries/Cache/drivers/index.html", "downloaded_repos/maxsite_cms/system/libraries/Cache/index.html", "downloaded_repos/maxsite_cms/system/libraries/Calendar.php", "downloaded_repos/maxsite_cms/system/libraries/Cart.php", "downloaded_repos/maxsite_cms/system/libraries/Driver.php", "downloaded_repos/maxsite_cms/system/libraries/Email.php", "downloaded_repos/maxsite_cms/system/libraries/Encrypt.php", "downloaded_repos/maxsite_cms/system/libraries/Form_validation.php", "downloaded_repos/maxsite_cms/system/libraries/Ftp.php", "downloaded_repos/maxsite_cms/system/libraries/Image_lib.php", "downloaded_repos/maxsite_cms/system/libraries/Javascript.php", "downloaded_repos/maxsite_cms/system/libraries/Log.php", "downloaded_repos/maxsite_cms/system/libraries/Migration.php", "downloaded_repos/maxsite_cms/system/libraries/Pagination.php", "downloaded_repos/maxsite_cms/system/libraries/Parser.php", "downloaded_repos/maxsite_cms/system/libraries/Profiler.php", "downloaded_repos/maxsite_cms/system/libraries/Session.php", "downloaded_repos/maxsite_cms/system/libraries/Sha1.php", "downloaded_repos/maxsite_cms/system/libraries/Table.php", "downloaded_repos/maxsite_cms/system/libraries/Trackback.php", "downloaded_repos/maxsite_cms/system/libraries/Typography.php", "downloaded_repos/maxsite_cms/system/libraries/Unit_test.php", "downloaded_repos/maxsite_cms/system/libraries/Upload.php", "downloaded_repos/maxsite_cms/system/libraries/User_agent.php", "downloaded_repos/maxsite_cms/system/libraries/Xmlrpc.php", "downloaded_repos/maxsite_cms/system/libraries/Xmlrpcs.php", "downloaded_repos/maxsite_cms/system/libraries/Zip.php", "downloaded_repos/maxsite_cms/system/libraries/index.html", "downloaded_repos/maxsite_cms/system/libraries/javascript/Jquery.php", "downloaded_repos/maxsite_cms/system/libraries/javascript/index.html", "downloaded_repos/maxsite_cms/update-maxsite/.htaccess", "downloaded_repos/maxsite_cms/update-maxsite/_key.php", "downloaded_repos/maxsite_cms/update-maxsite/index.php", "downloaded_repos/maxsite_cms/update-maxsite/pclzip.lib.php", "downloaded_repos/maxsite_cms/uploads/.htaccess", "downloaded_repos/maxsite_cms/uploads/_mso_float/index.html", "downloaded_repos/maxsite_cms/uploads/_mso_i/index.html", "downloaded_repos/maxsite_cms/uploads/favicons/favicon1.png", "downloaded_repos/maxsite_cms/uploads/mini/index.html", "downloaded_repos/maxsite_cms/uploads/smiles/angry.gif", "downloaded_repos/maxsite_cms/uploads/smiles/bigsurprise.gif", "downloaded_repos/maxsite_cms/uploads/smiles/blank.gif", "downloaded_repos/maxsite_cms/uploads/smiles/cheese.gif", "downloaded_repos/maxsite_cms/uploads/smiles/confused.gif", "downloaded_repos/maxsite_cms/uploads/smiles/downer.gif", "downloaded_repos/maxsite_cms/uploads/smiles/embarrassed.gif", "downloaded_repos/maxsite_cms/uploads/smiles/exclaim.gif", "downloaded_repos/maxsite_cms/uploads/smiles/grin.gif", "downloaded_repos/maxsite_cms/uploads/smiles/grrr.gif", "downloaded_repos/maxsite_cms/uploads/smiles/gulp.gif", "downloaded_repos/maxsite_cms/uploads/smiles/hmm.gif", "downloaded_repos/maxsite_cms/uploads/smiles/index.html", "downloaded_repos/maxsite_cms/uploads/smiles/kiss.gif", "downloaded_repos/maxsite_cms/uploads/smiles/lol.gif", "downloaded_repos/maxsite_cms/uploads/smiles/longface.gif", "downloaded_repos/maxsite_cms/uploads/smiles/mad.gif", "downloaded_repos/maxsite_cms/uploads/smiles/ohh.gif", "downloaded_repos/maxsite_cms/uploads/smiles/ohoh.gif", "downloaded_repos/maxsite_cms/uploads/smiles/question.gif", "downloaded_repos/maxsite_cms/uploads/smiles/rasberry.gif", "downloaded_repos/maxsite_cms/uploads/smiles/raspberry.gif", "downloaded_repos/maxsite_cms/uploads/smiles/rolleyes.gif", "downloaded_repos/maxsite_cms/uploads/smiles/shade_cheese.gif", "downloaded_repos/maxsite_cms/uploads/smiles/shade_grin.gif", "downloaded_repos/maxsite_cms/uploads/smiles/shade_hmm.gif", "downloaded_repos/maxsite_cms/uploads/smiles/shade_mad.gif", "downloaded_repos/maxsite_cms/uploads/smiles/shade_smile.gif", "downloaded_repos/maxsite_cms/uploads/smiles/shade_smirk.gif", "downloaded_repos/maxsite_cms/uploads/smiles/shock.gif", "downloaded_repos/maxsite_cms/uploads/smiles/shuteye.gif", "downloaded_repos/maxsite_cms/uploads/smiles/sick.gif", "downloaded_repos/maxsite_cms/uploads/smiles/smile.gif", "downloaded_repos/maxsite_cms/uploads/smiles/smirk.gif", "downloaded_repos/maxsite_cms/uploads/smiles/snake.gif", "downloaded_repos/maxsite_cms/uploads/smiles/surprise.gif", "downloaded_repos/maxsite_cms/uploads/smiles/tongue_laugh.gif", "downloaded_repos/maxsite_cms/uploads/smiles/tongue_rolleye.gif", "downloaded_repos/maxsite_cms/uploads/smiles/tongue_wink.gif", "downloaded_repos/maxsite_cms/uploads/smiles/vampire.gif", "downloaded_repos/maxsite_cms/uploads/smiles/wink.gif", "downloaded_repos/maxsite_cms/uploads/smiles/zip.gif"], "skipped": [{"path": "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/admin_home/admin.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/common/jquery/datetimepicker/jquery.datetimepicker.full.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/common/jquery/jquery.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/ar.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/ar.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/bg.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/bg.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/by.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/by.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/ca.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/cs.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/cs.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/da.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/da.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/de.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/de.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/el.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/el.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/es.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/es.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/es_ar.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/es_ar.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/fa.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/fa.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/fi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/fi.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/fr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/fr.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/he.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/he.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/hr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/hr.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/hu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/hu.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/id.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/id.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/it.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/it.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/ja.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/ja.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/ko.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/ko.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/lt.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/lt.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/mn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/mn.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/my.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/my.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/nl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/nl.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/no_nb.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/no_nb.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/ph.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/ph.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/pl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/pl.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/pt.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/pt.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/pt_br.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/pt_br.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/ro.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/ro.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/rs.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/rs.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/rs_latin.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/rs_latin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/ru.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/ru.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/sk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/sk.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/sl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/sl.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/sq.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/sq.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/sv.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/sv.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/th.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/th.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/tr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/tr.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/ua.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/ua.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/vi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/vi.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/zh_cn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/zh_cn.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/zh_tw.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/langs/zh_tw.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/allowtagsfrompaste/trumbowyg.allowtagsfrompaste.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/allowtagsfrompaste/trumbowyg.allowtagsfrompaste.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/base64/trumbowyg.base64.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/base64/trumbowyg.base64.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/cleanpaste/trumbowyg.cleanpaste.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/cleanpaste/trumbowyg.cleanpaste.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/colors/trumbowyg.colors.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/colors/trumbowyg.colors.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/colors/ui/sass/trumbowyg.colors.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/colors/ui/trumbowyg.colors.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/colors/ui/trumbowyg.colors.min.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/emoji/trumbowyg.emoji.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/emoji/trumbowyg.emoji.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/emoji/ui/sass/trumbowyg.emoji.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/emoji/ui/trumbowyg.emoji.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/emoji/ui/trumbowyg.emoji.min.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/fontfamily/trumbowyg.fontfamily.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/fontfamily/trumbowyg.fontfamily.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/fontsize/trumbowyg.fontsize.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/fontsize/trumbowyg.fontsize.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/giphy/trumbowyg.giphy.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/giphy/trumbowyg.giphy.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/giphy/ui/sass/trumbowyg.giphy.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/giphy/ui/trumbowyg.giphy.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/giphy/ui/trumbowyg.giphy.min.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/highlight/trumbowyg.highlight.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/highlight/trumbowyg.highlight.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/highlight/ui/sass/trumbowyg.highlight.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/highlight/ui/trumbowyg.highlight.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/highlight/ui/trumbowyg.highlight.min.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/history/trumbowyg.history.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/history/trumbowyg.history.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/insertaudio/trumbowyg.insertaudio.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/insertaudio/trumbowyg.insertaudio.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/lineheight/trumbowyg.lineheight.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/lineheight/trumbowyg.lineheight.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/mathml/trumbowyg.mathml.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/mathml/trumbowyg.mathml.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/mathml/ui/sass/trumbowyg.mathml.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/mathml/ui/trumbowyg.mathml.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/mathml/ui/trumbowyg.mathml.min.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/mention/trumbowyg.mention.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/mention/trumbowyg.mention.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/mention/ui/sass/trumbowyg.mention.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/mention/ui/trumbowyg.mention.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/mention/ui/trumbowyg.mention.min.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/noembed/trumbowyg.noembed.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/noembed/trumbowyg.noembed.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/pasteembed/trumbowyg.pasteembed.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/pasteembed/trumbowyg.pasteembed.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/pasteimage/trumbowyg.pasteimage.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/pasteimage/trumbowyg.pasteimage.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/preformatted/trumbowyg.preformatted.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/preformatted/trumbowyg.preformatted.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/resizimg/resizable-resolveconflict.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/resizimg/resizable-resolveconflict.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/resizimg/trumbowyg.resizimg.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/resizimg/trumbowyg.resizimg.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/ruby/trumbowyg.ruby.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/ruby/trumbowyg.ruby.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/specialchars/trumbowyg.specialchars.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/specialchars/trumbowyg.specialchars.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/specialchars/ui/sass/trumbowyg.specialchars.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/specialchars/ui/trumbowyg.specialchars.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/specialchars/ui/trumbowyg.specialchars.min.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/table/trumbowyg.table.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/table/trumbowyg.table.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/table/ui/sass/trumbowyg.table.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/table/ui/trumbowyg.table.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/table/ui/trumbowyg.table.min.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/template/trumbowyg.template.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/template/trumbowyg.template.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/upload/trumbowyg.upload.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/plugins/upload/trumbowyg.upload.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/trumbowyg.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/trumbowyg.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/ui/icons.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/ui/sass/trumbowyg.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/ui/trumbowyg.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/editor_trumbowyg/dist/ui/trumbowyg.min.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/samborsky_polls/js/jquery-ui-1.8.16.custom.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/shjs/lang/sh_c.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/shjs/lang/sh_cpp.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/shjs/lang/sh_csharp.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/shjs/lang/sh_css.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/shjs/lang/sh_html.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/shjs/lang/sh_java.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/shjs/lang/sh_javascript.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/shjs/lang/sh_less.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/shjs/lang/sh_pascal.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/shjs/lang/sh_php.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/shjs/lang/sh_python.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/shjs/lang/sh_ruby.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/shjs/lang/sh_sql.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/plugins/shjs/sh_main.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/js/cookie.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/js/jquery-scrolltofixed.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/js/jquery.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/vendor/fontawesome5/LICENSE.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/vendor/fontawesome5/_fontawesome.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/vendor/fontawesome5/_fonts.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/vendor/fontawesome5/_icons.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/assets/vendor/fontawesome5/_variables.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/distr/tiny-slider/assets/tiny-slider.min.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6526191234588623, "profiling_times": {"config_time": 5.91566801071167, "core_time": 18.474125862121582, "ignores_time": 0.0020318031311035156, "total_time": 24.39266610145569}, "parsing_time": {"total_time": 17.30888557434082, "per_file_time": {"mean": 0.02351750757383264, "std_dev": 0.005563158136736962}, "very_slow_stats": {"time_ratio": 0.2461391334569939, "count_ratio": 0.009510869565217392}, "very_slow_files": [{"fpath": "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/js/jquery.tablesorter.js", "ftime": 0.32325196266174316}, {"fpath": "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/jquery.markitup.js", "ftime": 0.3337569236755371}, {"fpath": "downloaded_repos/maxsite_cms/application/maxsite/common/jquery/jscolor/jscolor.js", "ftime": 0.3761568069458008}, {"fpath": "downloaded_repos/maxsite_cms/application/maxsite/plugins/random_gal/jquery.nivo.slider.js", "ftime": 0.38236117362976074}, {"fpath": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/components/lightslider/lightslider.js", "ftime": 0.6196930408477783}, {"fpath": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/distr/tiny-slider/assets/tiny-slider.js", "ftime": 0.9120090007781982}, {"fpath": "downloaded_repos/maxsite_cms/update-maxsite/pclzip.lib.php", "ftime": 1.3131651878356934}]}, "scanning_time": {"total_time": 81.96238565444946, "per_file_time": {"mean": 0.02195617081555033, "std_dev": 0.05431366305204391}, "very_slow_stats": {"time_ratio": 0.37966655077890704, "count_ratio": 0.0024109295472810074}, "very_slow_files": [{"fpath": "downloaded_repos/maxsite_cms/application/maxsite/common/comments.php", "ftime": 1.633112907409668}, {"fpath": "downloaded_repos/maxsite_cms/system/libraries/Session.php", "ftime": 1.6655631065368652}, {"fpath": "downloaded_repos/maxsite_cms/application/maxsite/plugins/random_gal/jquery.nivo.slider.js", "ftime": 2.2239770889282227}, {"fpath": "downloaded_repos/maxsite_cms/application/maxsite/common/language/ua.php", "ftime": 2.310029983520508}, {"fpath": "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/js/jquery.tablesorter.js", "ftime": 2.361978054046631}, {"fpath": "downloaded_repos/maxsite_cms/application/maxsite/admin/plugins/editor_markitup/jquery.markitup.js", "ftime": 2.4050400257110596}, {"fpath": "downloaded_repos/maxsite_cms/update-maxsite/pclzip.lib.php", "ftime": 2.4974288940429688}, {"fpath": "downloaded_repos/maxsite_cms/application/maxsite/common/jquery/jscolor/jscolor.js", "ftime": 4.631657123565674}, {"fpath": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/distr/tiny-slider/assets/tiny-slider.js", "ftime": 11.389589071273804}]}, "matching_time": {"total_time": 36.55372905731201, "per_file_and_rule_time": {"mean": 0.021007890262822966, "std_dev": 0.015103726729515115}, "very_slow_stats": {"time_ratio": 0.6307069334807365, "count_ratio": 0.04195402298850575}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/distr/tiny-slider/assets/tiny-slider.js", "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 0.45787715911865234}, {"fpath": "downloaded_repos/maxsite_cms/update-maxsite/pclzip.lib.php", "rule_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "time": 0.4754190444946289}, {"fpath": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/distr/tiny-slider/assets/tiny-slider.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.49848198890686035}, {"fpath": "downloaded_repos/maxsite_cms/application/maxsite/common/comments.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.6178998947143555}, {"fpath": "downloaded_repos/maxsite_cms/application/maxsite/plugins/random_gal/jquery.nivo.slider.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.6190218925476074}, {"fpath": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/distr/tiny-slider/assets/tiny-slider.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.7004139423370361}, {"fpath": "downloaded_repos/maxsite_cms/application/maxsite/common/jquery/jscolor/jscolor.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.9869379997253418}, {"fpath": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/distr/tiny-slider/assets/tiny-slider.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 2.063328981399536}, {"fpath": "downloaded_repos/maxsite_cms/application/maxsite/common/language/ua.php", "rule_id": "php.lang.security.php-permissive-cors.php-permissive-cors", "time": 2.0841050148010254}, {"fpath": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/distr/tiny-slider/assets/tiny-slider.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 3.5572309494018555}]}, "tainting_time": {"total_time": 12.339491605758667, "per_def_and_rule_time": {"mean": 0.001912506448505683, "std_dev": 8.052539689105922e-05}, "very_slow_stats": {"time_ratio": 0.1877913297037987, "count_ratio": 0.00356478611283323}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/js/jquery.tablesorter.js", "fline": 1, "rule_id": "javascript.express.security.require-request.require-request", "time": 0.08302617073059082}, {"fpath": "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/js/jquery.tablesorter.js", "fline": 1, "rule_id": "javascript.express.security.x-frame-options-misconfiguration.x-frame-options-misconfiguration", "time": 0.09405303001403809}, {"fpath": "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/js/jquery.tablesorter.js", "fline": 1, "rule_id": "javascript.express.security.cors-misconfiguration.cors-misconfiguration", "time": 0.0947418212890625}, {"fpath": "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/js/jquery.tablesorter.js", "fline": 1, "rule_id": "javascript.lang.security.audit.code-string-concat.code-string-concat", "time": 0.09597516059875488}, {"fpath": "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/js/jquery.tablesorter.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.09811687469482422}, {"fpath": "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/js/jquery.tablesorter.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.10552811622619629}, {"fpath": "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/js/jquery.tablesorter.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.13701486587524414}, {"fpath": "downloaded_repos/maxsite_cms/application/maxsite/admin/template/default/assets/js/jquery.tablesorter.js", "fline": 1, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 0.14369511604309082}, {"fpath": "downloaded_repos/maxsite_cms/application/maxsite/templates/default/distr/tiny-slider/assets/tiny-slider.js", "fline": 1, "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 0.2152700424194336}, {"fpath": "downloaded_repos/maxsite_cms/application/maxsite/plugins/random_gal/jquery.nivo.slider.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.5182430744171143}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1129396608}, "engine_requested": "OSS", "skipped_rules": []}