{"version": "1.130.0", "results": [], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/bezhanSalleh_filament-exceptions/.github/workflows/run-tests.yml", "start": {"line": 37, "col": 38, "offset": 903}, "end": {"line": 37, "col": 57, "offset": 922}}, {"path": "downloaded_repos/bezhanSalleh_filament-exceptions/.github/workflows/run-tests.yml", "start": {"line": 38, "col": 24, "offset": 903}, "end": {"line": 38, "col": 43, "offset": 922}}]], "message": "Syntax error at line downloaded_repos/bezhanSalleh_filament-exceptions/.github/workflows/run-tests.yml:37:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ runner.tool_cache` was unexpected", "path": "downloaded_repos/bezhanSalleh_filament-exceptions/.github/workflows/run-tests.yml", "spans": [{"file": "downloaded_repos/bezhanSalleh_filament-exceptions/.github/workflows/run-tests.yml", "start": {"line": 37, "col": 38, "offset": 903}, "end": {"line": 37, "col": 57, "offset": 922}}, {"file": "downloaded_repos/bezhanSalleh_filament-exceptions/.github/workflows/run-tests.yml", "start": {"line": 38, "col": 24, "offset": 903}, "end": {"line": 38, "col": 43, "offset": 922}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/bezhanSalleh_filament-exceptions/.github/workflows/run-tests.yml", "start": {"line": 42, "col": 53, "offset": 1090}, "end": {"line": 42, "col": 69, "offset": 1106}}, {"path": "downloaded_repos/bezhanSalleh_filament-exceptions/.github/workflows/run-tests.yml", "start": {"line": 42, "col": 97, "offset": 1090}, "end": {"line": 42, "col": 115, "offset": 1108}}, {"path": "downloaded_repos/bezhanSalleh_filament-exceptions/.github/workflows/run-tests.yml", "start": {"line": 43, "col": 19, "offset": 1090}, "end": {"line": 43, "col": 22, "offset": 1093}}]], "message": "Syntax error at line downloaded_repos/bezhanSalleh_filament-exceptions/.github/workflows/run-tests.yml:42:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ matrix.laravel` was unexpected", "path": "downloaded_repos/bezhanSalleh_filament-exceptions/.github/workflows/run-tests.yml", "spans": [{"file": "downloaded_repos/bezhanSalleh_filament-exceptions/.github/workflows/run-tests.yml", "start": {"line": 42, "col": 53, "offset": 1090}, "end": {"line": 42, "col": 69, "offset": 1106}}, {"file": "downloaded_repos/bezhanSalleh_filament-exceptions/.github/workflows/run-tests.yml", "start": {"line": 42, "col": 97, "offset": 1090}, "end": {"line": 42, "col": 115, "offset": 1108}}, {"file": "downloaded_repos/bezhanSalleh_filament-exceptions/.github/workflows/run-tests.yml", "start": {"line": 43, "col": 19, "offset": 1090}, "end": {"line": 43, "col": 22, "offset": 1093}}]}], "paths": {"scanned": ["downloaded_repos/bezhanSalleh_filament-exceptions/.blade.format.json", "downloaded_repos/bezhanSalleh_filament-exceptions/.editorconfig", "downloaded_repos/bezhanSalleh_filament-exceptions/.gitattributes", "downloaded_repos/bezhanSalleh_filament-exceptions/.github/FUNDING.yml", "downloaded_repos/bezhanSalleh_filament-exceptions/.github/ISSUE_TEMPLATE/config.yml", "downloaded_repos/bezhanSalleh_filament-exceptions/.github/dependabot.yml", "downloaded_repos/bezhanSalleh_filament-exceptions/.github/workflows/dependabot-auto-merge.yml", "downloaded_repos/bezhanSalleh_filament-exceptions/.github/workflows/fix-php-code-style-issues.yml", "downloaded_repos/bezhanSalleh_filament-exceptions/.github/workflows/run-tests.yml", "downloaded_repos/bezhanSalleh_filament-exceptions/.github/workflows/update-changelog.yml", "downloaded_repos/bezhanSalleh_filament-exceptions/.gitignore", "downloaded_repos/bezhanSalleh_filament-exceptions/.phpunit.cache/test-results", "downloaded_repos/bezhanSalleh_filament-exceptions/.prettierignore", "downloaded_repos/bezhanSalleh_filament-exceptions/CHANGELOG.md", "downloaded_repos/bezhanSalleh_filament-exceptions/LICENSE.md", "downloaded_repos/bezhanSalleh_filament-exceptions/README.md", "downloaded_repos/bezhanSalleh_filament-exceptions/bootstrap/app.php", "downloaded_repos/bezhanSalleh_filament-exceptions/composer.json", "downloaded_repos/bezhanSalleh_filament-exceptions/database/factories/ModelFactory.php", "downloaded_repos/bezhanSalleh_filament-exceptions/database/migrations/create_filament_exceptions_table.php.stub", "downloaded_repos/bezhanSalleh_filament-exceptions/package.json", "downloaded_repos/bezhanSalleh_filament-exceptions/phpunit.xml.dist", "downloaded_repos/bezhanSalleh_filament-exceptions/phpunit.xml.dist.bak", "downloaded_repos/bezhanSalleh_filament-exceptions/pint.json", "downloaded_repos/bezhanSalleh_filament-exceptions/postcss.config.cjs", "downloaded_repos/bezhanSalleh_filament-exceptions/resources/css/filament-exceptions.css", "downloaded_repos/bezhanSalleh_filament-exceptions/resources/lang/cs/filament-exceptions.php", "downloaded_repos/bezhanSalleh_filament-exceptions/resources/lang/en/filament-exceptions.php", "downloaded_repos/bezhanSalleh_filament-exceptions/resources/lang/sk/filament-exceptions.php", "downloaded_repos/bezhanSalleh_filament-exceptions/resources/views/.gitkeep", "downloaded_repos/bezhanSalleh_filament-exceptions/resources/views/body.blade.php", "downloaded_repos/bezhanSalleh_filament-exceptions/resources/views/components/code-preview.blade.php", "downloaded_repos/bezhanSalleh_filament-exceptions/resources/views/components/query-preview.blade.php", "downloaded_repos/bezhanSalleh_filament-exceptions/resources/views/cookies.blade.php", "downloaded_repos/bezhanSalleh_filament-exceptions/resources/views/exception.blade.php", "downloaded_repos/bezhanSalleh_filament-exceptions/resources/views/headers.blade.php", "downloaded_repos/bezhanSalleh_filament-exceptions/resources/views/query.blade.php", "downloaded_repos/bezhanSalleh_filament-exceptions/resources/views/view-exception.blade.php", "downloaded_repos/bezhanSalleh_filament-exceptions/src/Commands/InstallCommand.php", "downloaded_repos/bezhanSalleh_filament-exceptions/src/Concerns/HasLabels.php", "downloaded_repos/bezhanSalleh_filament-exceptions/src/Concerns/HasModelPruneInterval.php", "downloaded_repos/bezhanSalleh_filament-exceptions/src/Concerns/HasNavigation.php", "downloaded_repos/bezhanSalleh_filament-exceptions/src/Concerns/HasTabs.php", "downloaded_repos/bezhanSalleh_filament-exceptions/src/Concerns/HasTenantScope.php", "downloaded_repos/bezhanSalleh_filament-exceptions/src/Facades/FilamentExceptions.php", "downloaded_repos/bezhanSalleh_filament-exceptions/src/FilamentExceptions.php", "downloaded_repos/bezhanSalleh_filament-exceptions/src/FilamentExceptionsPlugin.php", "downloaded_repos/bezhanSalleh_filament-exceptions/src/FilamentExceptionsServiceProvider.php", "downloaded_repos/bezhanSalleh_filament-exceptions/src/Models/Exception.php", "downloaded_repos/bezhanSalleh_filament-exceptions/src/QueryRecorder/Query.php", "downloaded_repos/bezhanSalleh_filament-exceptions/src/QueryRecorder/QueryRecorder.php", "downloaded_repos/bezhanSalleh_filament-exceptions/src/Resources/ExceptionResource/Pages/ListExceptions.php", "downloaded_repos/bezhanSalleh_filament-exceptions/src/Resources/ExceptionResource/Pages/ViewException.php", "downloaded_repos/bezhanSalleh_filament-exceptions/src/Resources/ExceptionResource.php", "downloaded_repos/bezhanSalleh_filament-exceptions/src/Trace/CodeBlock.php", "downloaded_repos/bezhanSalleh_filament-exceptions/src/Trace/Frame.php", "downloaded_repos/bezhanSalleh_filament-exceptions/src/Trace/Parser.php", "downloaded_repos/bezhanSalleh_filament-exceptions/tailwind.config.js"], "skipped": [{"path": "downloaded_repos/bezhanSalleh_filament-exceptions/.github/workflows/run-tests.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/bezhanSalleh_filament-exceptions/resources/dist/filament-exceptions.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/bezhanSalleh_filament-exceptions/resources/dist/filament-exceptions.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/bezhanSalleh_filament-exceptions/tests/AdminPanelProvider.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/bezhanSalleh_filament-exceptions/tests/ArchTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/bezhanSalleh_filament-exceptions/tests/ExampleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/bezhanSalleh_filament-exceptions/tests/Pest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/bezhanSalleh_filament-exceptions/tests/TestCase.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.561316967010498, "profiling_times": {"config_time": 5.538003206253052, "core_time": 2.2260775566101074, "ignores_time": 0.001646280288696289, "total_time": 7.766596794128418}, "parsing_time": {"total_time": 0.4039027690887451, "per_file_time": {"mean": 0.008975617090861004, "std_dev": 0.00015597042076074608}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.9018356800079346, "per_file_time": {"mean": 0.005601463850980961, "std_dev": 0.00013372356537394548}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.15024042129516602, "per_file_and_rule_time": {"mean": 0.0005734367225006337, "std_dev": 2.1952124820725115e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.0, "per_def_and_rule_time": {"mean": 0.0, "std_dev": 0.0}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}