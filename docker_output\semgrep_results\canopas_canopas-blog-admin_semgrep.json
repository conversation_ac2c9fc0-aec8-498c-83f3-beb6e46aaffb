{"version": "1.130.0", "results": [{"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/canopas_canopas-blog-admin/admin/Dockerfile", "start": {"line": 22, "col": 1, "offset": 426}, "end": {"line": 22, "col": 22, "offset": 447}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD [\"yarn\", \"start\"]", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/canopas_canopas-blog-admin/admin/docker-compose.yaml", "start": {"line": 4, "col": 3, "offset": 28}, "end": {"line": 4, "col": 7, "offset": 32}, "extra": {"message": "Service 'blog' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/canopas_canopas-blog-admin/admin/docker-compose.yaml", "start": {"line": 4, "col": 3, "offset": 28}, "end": {"line": 4, "col": 7, "offset": 32}, "extra": {"message": "Service 'blog' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/canopas_canopas-blog-admin/admin/docker-compose.yaml", "start": {"line": 33, "col": 3, "offset": 1005}, "end": {"line": 33, "col": 8, "offset": 1010}, "extra": {"message": "Service 'nginx' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/canopas_canopas-blog-admin/admin/docker-compose.yaml", "start": {"line": 33, "col": 3, "offset": 1005}, "end": {"line": 33, "col": 8, "offset": 1010}, "extra": {"message": "Service 'nginx' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/canopas_canopas-blog-admin/admin/src/api/post/content-types/post/lifecycles.js", "start": {"line": 369, "col": 5, "offset": 10084}, "end": {"line": 369, "col": 32, "offset": 10111}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.puppeteer.security.audit.puppeteer-goto-injection.puppeteer-goto-injection", "path": "downloaded_repos/canopas_canopas-blog-admin/admin/src/api/post/content-types/post/lifecycles.js", "start": {"line": 381, "col": 11, "offset": 10397}, "end": {"line": 381, "col": 60, "offset": 10446}, "extra": {"message": "If unverified user data can reach the `goto` method it can result in Server-Side Request Forgery vulnerabilities", "metadata": {"owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "category": "security", "technology": ["puppeteer"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/javascript.puppeteer.security.audit.puppeteer-goto-injection.puppeteer-goto-injection", "shortlink": "https://sg.run/4xE9"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 50, "col": 29, "offset": 1362}, "end": {"line": 50, "col": 54, "offset": 1387}}, {"path": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 51, "col": 67, "offset": 1362}, "end": {"line": 51, "col": 70, "offset": 1365}}, {"path": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 51, "col": 94, "offset": 1362}, "end": {"line": 51, "col": 97, "offset": 1365}}, {"path": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 52, "col": 74, "offset": 1362}, "end": {"line": 52, "col": 77, "offset": 1365}}, {"path": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 52, "col": 101, "offset": 1362}, "end": {"line": 52, "col": 104, "offset": 1365}}, {"path": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 84, "offset": 1362}, "end": {"line": 53, "col": 87, "offset": 1365}}, {"path": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 111, "offset": 1362}, "end": {"line": 53, "col": 114, "offset": 1365}}, {"path": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 159, "offset": 1362}, "end": {"line": 53, "col": 162, "offset": 1365}}, {"path": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 194, "offset": 1362}, "end": {"line": 53, "col": 197, "offset": 1365}}, {"path": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 231, "offset": 1362}, "end": {"line": 53, "col": 234, "offset": 1365}}, {"path": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 276, "offset": 1362}, "end": {"line": 53, "col": 279, "offset": 1365}}, {"path": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 317, "offset": 1362}, "end": {"line": 53, "col": 320, "offset": 1365}}, {"path": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 357, "offset": 1362}, "end": {"line": 53, "col": 360, "offset": 1365}}, {"path": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 400, "offset": 1362}, "end": {"line": 53, "col": 403, "offset": 1365}}, {"path": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 441, "offset": 1362}, "end": {"line": 53, "col": 444, "offset": 1365}}, {"path": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 482, "offset": 1362}, "end": {"line": 53, "col": 485, "offset": 1365}}, {"path": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 527, "offset": 1362}, "end": {"line": 53, "col": 530, "offset": 1365}}, {"path": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 576, "offset": 1362}, "end": {"line": 53, "col": 579, "offset": 1365}}, {"path": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 620, "offset": 1362}, "end": {"line": 53, "col": 623, "offset": 1365}}, {"path": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 661, "offset": 1362}, "end": {"line": 53, "col": 664, "offset": 1365}}, {"path": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 707, "offset": 1362}, "end": {"line": 53, "col": 710, "offset": 1365}}, {"path": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 760, "offset": 1362}, "end": {"line": 53, "col": 763, "offset": 1365}}, {"path": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 806, "offset": 1362}, "end": {"line": 53, "col": 809, "offset": 1365}}, {"path": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 841, "offset": 1362}, "end": {"line": 53, "col": 844, "offset": 1365}}, {"path": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 880, "offset": 1362}, "end": {"line": 53, "col": 883, "offset": 1365}}, {"path": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 919, "offset": 1362}, "end": {"line": 53, "col": 922, "offset": 1365}}, {"path": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 954, "offset": 1362}, "end": {"line": 53, "col": 957, "offset": 1365}}, {"path": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 991, "offset": 1362}, "end": {"line": 53, "col": 994, "offset": 1365}}]], "message": "Syntax error at line downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml:50:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ secrets.ADMIN_PANEL_URL` was unexpected", "path": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "spans": [{"file": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 50, "col": 29, "offset": 1362}, "end": {"line": 50, "col": 54, "offset": 1387}}, {"file": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 51, "col": 67, "offset": 1362}, "end": {"line": 51, "col": 70, "offset": 1365}}, {"file": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 51, "col": 94, "offset": 1362}, "end": {"line": 51, "col": 97, "offset": 1365}}, {"file": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 52, "col": 74, "offset": 1362}, "end": {"line": 52, "col": 77, "offset": 1365}}, {"file": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 52, "col": 101, "offset": 1362}, "end": {"line": 52, "col": 104, "offset": 1365}}, {"file": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 84, "offset": 1362}, "end": {"line": 53, "col": 87, "offset": 1365}}, {"file": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 111, "offset": 1362}, "end": {"line": 53, "col": 114, "offset": 1365}}, {"file": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 159, "offset": 1362}, "end": {"line": 53, "col": 162, "offset": 1365}}, {"file": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 194, "offset": 1362}, "end": {"line": 53, "col": 197, "offset": 1365}}, {"file": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 231, "offset": 1362}, "end": {"line": 53, "col": 234, "offset": 1365}}, {"file": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 276, "offset": 1362}, "end": {"line": 53, "col": 279, "offset": 1365}}, {"file": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 317, "offset": 1362}, "end": {"line": 53, "col": 320, "offset": 1365}}, {"file": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 357, "offset": 1362}, "end": {"line": 53, "col": 360, "offset": 1365}}, {"file": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 400, "offset": 1362}, "end": {"line": 53, "col": 403, "offset": 1365}}, {"file": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 441, "offset": 1362}, "end": {"line": 53, "col": 444, "offset": 1365}}, {"file": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 482, "offset": 1362}, "end": {"line": 53, "col": 485, "offset": 1365}}, {"file": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 527, "offset": 1362}, "end": {"line": 53, "col": 530, "offset": 1365}}, {"file": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 576, "offset": 1362}, "end": {"line": 53, "col": 579, "offset": 1365}}, {"file": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 620, "offset": 1362}, "end": {"line": 53, "col": 623, "offset": 1365}}, {"file": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 661, "offset": 1362}, "end": {"line": 53, "col": 664, "offset": 1365}}, {"file": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 707, "offset": 1362}, "end": {"line": 53, "col": 710, "offset": 1365}}, {"file": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 760, "offset": 1362}, "end": {"line": 53, "col": 763, "offset": 1365}}, {"file": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 806, "offset": 1362}, "end": {"line": 53, "col": 809, "offset": 1365}}, {"file": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 841, "offset": 1362}, "end": {"line": 53, "col": 844, "offset": 1365}}, {"file": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 880, "offset": 1362}, "end": {"line": 53, "col": 883, "offset": 1365}}, {"file": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 919, "offset": 1362}, "end": {"line": 53, "col": 922, "offset": 1365}}, {"file": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 954, "offset": 1362}, "end": {"line": 53, "col": 957, "offset": 1365}}, {"file": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "start": {"line": 53, "col": 991, "offset": 1362}, "end": {"line": 53, "col": 994, "offset": 1365}}]}], "paths": {"scanned": ["downloaded_repos/canopas_canopas-blog-admin/.githooks/pre-commit", "downloaded_repos/canopas_canopas-blog-admin/.github/pull_request_template.md", "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/build.yml", "downloaded_repos/canopas_canopas-blog-admin/.gitignore", "downloaded_repos/canopas_canopas-blog-admin/.prettierignore", "downloaded_repos/canopas_canopas-blog-admin/CONTRIBUTING.md", "downloaded_repos/canopas_canopas-blog-admin/LICENSE", "downloaded_repos/canopas_canopas-blog-admin/README.md", "downloaded_repos/canopas_canopas-blog-admin/admin/.dockerignore", "downloaded_repos/canopas_canopas-blog-admin/admin/.env.example", "downloaded_repos/canopas_canopas-blog-admin/admin/Dockerfile", "downloaded_repos/canopas_canopas-blog-admin/admin/config/admin.js", "downloaded_repos/canopas_canopas-blog-admin/admin/config/api.js", "downloaded_repos/canopas_canopas-blog-admin/admin/config/database.js", "downloaded_repos/canopas_canopas-blog-admin/admin/config/middlewares.js", "downloaded_repos/canopas_canopas-blog-admin/admin/config/plugins.js", "downloaded_repos/canopas_canopas-blog-admin/admin/config/server.js", "downloaded_repos/canopas_canopas-blog-admin/admin/deploy.sh", "downloaded_repos/canopas_canopas-blog-admin/admin/docker-compose.yaml", "downloaded_repos/canopas_canopas-blog-admin/admin/favicon.ico", "downloaded_repos/canopas_canopas-blog-admin/admin/nginx/conf.d/website-blog.conf", "downloaded_repos/canopas_canopas-blog-admin/admin/nginx/nginx.conf", "downloaded_repos/canopas_canopas-blog-admin/admin/package.json", "downloaded_repos/canopas_canopas-blog-admin/admin/public/assets/css/custom.css", "downloaded_repos/canopas_canopas-blog-admin/admin/public/assets/images/logo_login.png", "downloaded_repos/canopas_canopas-blog-admin/admin/public/emailTemplates/comment.html", "downloaded_repos/canopas_canopas-blog-admin/admin/public/emailTemplates/subscribe.html", "downloaded_repos/canopas_canopas-blog-admin/admin/public/robots.txt", "downloaded_repos/canopas_canopas-blog-admin/admin/src/admin/app.example.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/admin/app.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/admin/extensions/auth-logo.png", "downloaded_repos/canopas_canopas-blog-admin/admin/src/admin/extensions/favicon.ico", "downloaded_repos/canopas_canopas-blog-admin/admin/src/admin/webpack.config.example.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/api/author/content-types/author/lifecycles.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/api/author/content-types/author/schema.json", "downloaded_repos/canopas_canopas-blog-admin/admin/src/api/author/controllers/author.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/api/author/routes/author.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/api/author/services/author.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/api/category/content-types/category/schema.json", "downloaded_repos/canopas_canopas-blog-admin/admin/src/api/category/controllers/category.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/api/category/routes/category.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/api/category/services/category.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/api/comment/content-types/comment/schema.json", "downloaded_repos/canopas_canopas-blog-admin/admin/src/api/comment/controllers/comment.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/api/comment/routes/comment.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/api/comment/services/comment.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/api/cta/content-types/cta/schema.json", "downloaded_repos/canopas_canopas-blog-admin/admin/src/api/cta/controllers/cta.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/api/cta/routes/cta.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/api/cta/services/cta.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/api/post/content-types/post/convertData.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/api/post/content-types/post/lifecycles.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/api/post/content-types/post/schema.json", "downloaded_repos/canopas_canopas-blog-admin/admin/src/api/post/controllers/post.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/api/post/routes/custom.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/api/post/routes/post.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/api/post/services/post.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/api/tag/content-types/tag/schema.json", "downloaded_repos/canopas_canopas-blog-admin/admin/src/api/tag/controllers/tag.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/api/tag/routes/custom.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/api/tag/routes/tag.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/api/tag/services/tag.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/extensions/users-permissions/content-types/user/schema.json", "downloaded_repos/canopas_canopas-blog-admin/admin/src/extensions/users-permissions/strapi-server.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/index.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/plugins/blog-editor/admin/src/components/Wysiwyg/index.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/plugins/blog-editor/admin/src/components/Wysiwyg/wrapper.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/plugins/blog-editor/admin/src/components/editorjs/index.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/plugins/blog-editor/admin/src/components/editorjs/requiredTools.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/plugins/blog-editor/admin/src/components/medialib/adapter.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/plugins/blog-editor/admin/src/components/medialib/component.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/plugins/blog-editor/admin/src/components/medialib/utils.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/plugins/blog-editor/admin/src/config/customTools.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/plugins/blog-editor/admin/src/index.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/plugins/blog-editor/admin/src/pluginId.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/plugins/blog-editor/index.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/plugins/blog-editor/package.json", "downloaded_repos/canopas_canopas-blog-admin/admin/src/plugins/blog-editor/register.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/plugins/blog-editor/server/controllers/editorjs.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/plugins/blog-editor/server/controllers/index.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/plugins/blog-editor/server/index.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/plugins/blog-editor/server/register.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/plugins/blog-editor/server/routes/index.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/plugins/blog-editor/strapi-admin.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/plugins/blog-editor/strapi-server.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/plugins/blog-editor/yarn.lock", "downloaded_repos/canopas_canopas-blog-admin/admin/src/plugins/guidelines/admin/src/components/Initializer/index.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/plugins/guidelines/admin/src/components/PluginIcon/index.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/plugins/guidelines/admin/src/index.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/plugins/guidelines/admin/src/pages/App/index.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/plugins/guidelines/admin/src/pages/HomePage/index.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/plugins/guidelines/admin/src/pages/guidelines.md", "downloaded_repos/canopas_canopas-blog-admin/admin/src/plugins/guidelines/admin/src/pluginId.js", "downloaded_repos/canopas_canopas-blog-admin/admin/src/plugins/guidelines/admin/src/translations/en.json", "downloaded_repos/canopas_canopas-blog-admin/admin/src/plugins/guidelines/admin/src/translations/fr.json", "downloaded_repos/canopas_canopas-blog-admin/admin/src/plugins/guidelines/package.json", "downloaded_repos/canopas_canopas-blog-admin/admin/src/plugins/guidelines/strapi-admin.js", "downloaded_repos/canopas_canopas-blog-admin/admin/yarn.lock", "downloaded_repos/canopas_canopas-blog-admin/assets/banner.png", "downloaded_repos/canopas_canopas-blog-admin/assets/cta.png", "downloaded_repos/canopas_canopas-blog-admin/deploy/deploy-ecr-image.sh"], "skipped": [{"path": "downloaded_repos/canopas_canopas-blog-admin/.github/workflows/admin.yml", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.6539781093597412, "profiling_times": {"config_time": 6.435852766036987, "core_time": 3.319171905517578, "ignores_time": 0.0020720958709716797, "total_time": 9.757968187332153}, "parsing_time": {"total_time": 0.6463630199432373, "per_file_time": {"mean": 0.008079537749290468, "std_dev": 0.00026129169565854756}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 3.993929147720337, "per_file_time": {"mean": 0.01396478722979139, "std_dev": 0.003685056410545076}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 1.1406779289245605, "per_file_and_rule_time": {"mean": 0.0032497946693007427, "std_dev": 6.728810194903114e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.3583669662475586, "per_def_and_rule_time": {"mean": 0.0011486120713062775, "std_dev": 4.00515539810329e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}