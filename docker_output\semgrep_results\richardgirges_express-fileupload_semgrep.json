{"version": "1.130.0", "results": [{"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/richardgirges_express-fileupload/example/index.html", "start": {"line": 3, "col": 3, "offset": 17}, "end": {"line": 10, "col": 10, "offset": 231}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-check-csurf-middleware-usage.express-check-csurf-middleware-usage", "path": "downloaded_repos/richardgirges_express-fileupload/example/server.js", "start": {"line": 3, "col": 7, "offset": 86}, "end": {"line": 3, "col": 22, "offset": 101}, "extra": {"message": "A CSRF middleware was not detected in your express application. Ensure you are either using one such as `csurf` or `csrf` (see rule references) and/or you are properly doing CSRF validation in your routes with a token or cookies.", "metadata": {"category": "security", "references": ["https://www.npmjs.com/package/csurf", "https://www.npmjs.com/package/csrf", "https://cheatsheetseries.owasp.org/cheatsheets/Cross-Site_Request_Forgery_Prevention_Cheat_Sheet.html"], "cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "technology": ["javascript", "typescript", "express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-check-csurf-middleware-usage.express-check-csurf-middleware-usage", "shortlink": "https://sg.run/BxzR"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/richardgirges_express-fileupload/lib/processNested.js", "start": {"line": 30, "col": 9, "offset": 847}, "end": {"line": 30, "col": 29, "offset": 867}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/richardgirges_express-fileupload/lib/tempFileHandler.js", "start": {"line": 13, "col": 34, "offset": 327}, "end": {"line": 13, "col": 37, "offset": 330}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/richardgirges_express-fileupload/.circleci/config.yml", "downloaded_repos/richardgirges_express-fileupload/.eslintignore", "downloaded_repos/richardgirges_express-fileupload/.eslintrc", "downloaded_repos/richardgirges_express-fileupload/.gitignore", "downloaded_repos/richardgirges_express-fileupload/.mocharc.json", "downloaded_repos/richardgirges_express-fileupload/.prettierrc", "downloaded_repos/richardgirges_express-fileupload/LICENSE", "downloaded_repos/richardgirges_express-fileupload/README.md", "downloaded_repos/richardgirges_express-fileupload/SECURITY.md", "downloaded_repos/richardgirges_express-fileupload/example/README.md", "downloaded_repos/richardgirges_express-fileupload/example/index.html", "downloaded_repos/richardgirges_express-fileupload/example/server.js", "downloaded_repos/richardgirges_express-fileupload/example/uploads/placeholder.txt", "downloaded_repos/richardgirges_express-fileupload/lib/fileFactory.js", "downloaded_repos/richardgirges_express-fileupload/lib/index.js", "downloaded_repos/richardgirges_express-fileupload/lib/isEligibleRequest.js", "downloaded_repos/richardgirges_express-fileupload/lib/memHandler.js", "downloaded_repos/richardgirges_express-fileupload/lib/processMultipart.js", "downloaded_repos/richardgirges_express-fileupload/lib/processNested.js", "downloaded_repos/richardgirges_express-fileupload/lib/tempFileHandler.js", "downloaded_repos/richardgirges_express-fileupload/lib/uploadtimer.js", "downloaded_repos/richardgirges_express-fileupload/lib/utilities.js", "downloaded_repos/richardgirges_express-fileupload/package-lock.json", "downloaded_repos/richardgirges_express-fileupload/package.json"], "skipped": [{"path": "downloaded_repos/richardgirges_express-fileupload/test/fileFactory.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/richardgirges_express-fileupload/test/fileLimitUploads.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/richardgirges_express-fileupload/test/isEligibleRequest.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/richardgirges_express-fileupload/test/multipartFields.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/richardgirges_express-fileupload/test/multipartUploads.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/richardgirges_express-fileupload/test/options.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/richardgirges_express-fileupload/test/posttests.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/richardgirges_express-fileupload/test/pretests.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/richardgirges_express-fileupload/test/processNested.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/richardgirges_express-fileupload/test/server.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/richardgirges_express-fileupload/test/tempFile.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/richardgirges_express-fileupload/test/uploadtimer.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/richardgirges_express-fileupload/test/utilities.spec.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.788228988647461, "profiling_times": {"config_time": 6.190613031387329, "core_time": 2.896658420562744, "ignores_time": 0.0019087791442871094, "total_time": 9.090176582336426}, "parsing_time": {"total_time": 0.49647021293640137, "per_file_time": {"mean": 0.03546215806688581, "std_dev": 0.0008393554421142158}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.8255565166473389, "per_file_time": {"mean": 0.02897708756583077, "std_dev": 0.005063342556649571}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.6231138706207275, "per_file_and_rule_time": {"mean": 0.00556351670197078, "std_dev": 8.254668977223788e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.13448095321655273, "per_def_and_rule_time": {"mean": 0.003634620357204128, "std_dev": 3.908647121536817e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}