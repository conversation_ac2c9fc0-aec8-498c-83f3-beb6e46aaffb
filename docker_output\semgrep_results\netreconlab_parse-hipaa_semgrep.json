{"version": "1.130.0", "results": [{"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/netreconlab_parse-hipaa/docker-compose.dashboard.yml", "start": {"line": 4, "col": 5, "offset": 30}, "end": {"line": 4, "col": 10, "offset": 35}, "extra": {"message": "Service 'parse' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/netreconlab_parse-hipaa/docker-compose.dashboard.yml", "start": {"line": 4, "col": 5, "offset": 30}, "end": {"line": 4, "col": 10, "offset": 35}, "extra": {"message": "Service 'parse' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/netreconlab_parse-hipaa/docker-compose.dashboard.yml", "start": {"line": 58, "col": 5, "offset": 3553}, "end": {"line": 58, "col": 7, "offset": 3555}, "extra": {"message": "Service 'db' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/netreconlab_parse-hipaa/docker-compose.dashboard.yml", "start": {"line": 58, "col": 5, "offset": 3553}, "end": {"line": 58, "col": 7, "offset": 3555}, "extra": {"message": "Service 'db' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/netreconlab_parse-hipaa/docker-compose.mongo.dashboard.yml", "start": {"line": 4, "col": 5, "offset": 30}, "end": {"line": 4, "col": 10, "offset": 35}, "extra": {"message": "Service 'parse' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/netreconlab_parse-hipaa/docker-compose.mongo.dashboard.yml", "start": {"line": 4, "col": 5, "offset": 30}, "end": {"line": 4, "col": 10, "offset": 35}, "extra": {"message": "Service 'parse' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/netreconlab_parse-hipaa/docker-compose.mongo.dashboard.yml", "start": {"line": 54, "col": 5, "offset": 3190}, "end": {"line": 54, "col": 7, "offset": 3192}, "extra": {"message": "Service 'db' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/netreconlab_parse-hipaa/docker-compose.mongo.dashboard.yml", "start": {"line": 54, "col": 5, "offset": 3190}, "end": {"line": 54, "col": 7, "offset": 3192}, "extra": {"message": "Service 'db' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/netreconlab_parse-hipaa/docker-compose.mongo.yml", "start": {"line": 4, "col": 5, "offset": 30}, "end": {"line": 4, "col": 10, "offset": 35}, "extra": {"message": "Service 'parse' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/netreconlab_parse-hipaa/docker-compose.mongo.yml", "start": {"line": 4, "col": 5, "offset": 30}, "end": {"line": 4, "col": 10, "offset": 35}, "extra": {"message": "Service 'parse' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/netreconlab_parse-hipaa/docker-compose.mongo.yml", "start": {"line": 45, "col": 5, "offset": 2443}, "end": {"line": 45, "col": 7, "offset": 2445}, "extra": {"message": "Service 'db' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/netreconlab_parse-hipaa/docker-compose.mongo.yml", "start": {"line": 45, "col": 5, "offset": 2443}, "end": {"line": 45, "col": 7, "offset": 2445}, "extra": {"message": "Service 'db' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/netreconlab_parse-hipaa/docker-compose.mongo.yml", "start": {"line": 58, "col": 5, "offset": 3012}, "end": {"line": 58, "col": 14, "offset": 3021}, "extra": {"message": "Service 'dashboard' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/netreconlab_parse-hipaa/docker-compose.mongo.yml", "start": {"line": 58, "col": 5, "offset": 3012}, "end": {"line": 58, "col": 14, "offset": 3021}, "extra": {"message": "Service 'dashboard' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/netreconlab_parse-hipaa/docker-compose.no.hipaa.yml", "start": {"line": 4, "col": 5, "offset": 30}, "end": {"line": 4, "col": 10, "offset": 35}, "extra": {"message": "Service 'parse' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/netreconlab_parse-hipaa/docker-compose.no.hipaa.yml", "start": {"line": 4, "col": 5, "offset": 30}, "end": {"line": 4, "col": 10, "offset": 35}, "extra": {"message": "Service 'parse' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/netreconlab_parse-hipaa/docker-compose.no.hipaa.yml", "start": {"line": 48, "col": 5, "offset": 2782}, "end": {"line": 48, "col": 14, "offset": 2791}, "extra": {"message": "Service 'dashboard' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/netreconlab_parse-hipaa/docker-compose.no.hipaa.yml", "start": {"line": 48, "col": 5, "offset": 2782}, "end": {"line": 48, "col": 14, "offset": 2791}, "extra": {"message": "Service 'dashboard' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/netreconlab_parse-hipaa/docker-compose.no.hipaa.yml", "start": {"line": 61, "col": 5, "offset": 3512}, "end": {"line": 61, "col": 7, "offset": 3514}, "extra": {"message": "Service 'db' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/netreconlab_parse-hipaa/docker-compose.no.hipaa.yml", "start": {"line": 61, "col": 5, "offset": 3512}, "end": {"line": 61, "col": 7, "offset": 3514}, "extra": {"message": "Service 'db' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/netreconlab_parse-hipaa/docker-compose.yml", "start": {"line": 4, "col": 5, "offset": 30}, "end": {"line": 4, "col": 10, "offset": 35}, "extra": {"message": "Service 'parse' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/netreconlab_parse-hipaa/docker-compose.yml", "start": {"line": 4, "col": 5, "offset": 30}, "end": {"line": 4, "col": 10, "offset": 35}, "extra": {"message": "Service 'parse' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/netreconlab_parse-hipaa/docker-compose.yml", "start": {"line": 48, "col": 5, "offset": 2783}, "end": {"line": 48, "col": 7, "offset": 2785}, "extra": {"message": "Service 'db' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/netreconlab_parse-hipaa/docker-compose.yml", "start": {"line": 48, "col": 5, "offset": 2783}, "end": {"line": 48, "col": 7, "offset": 2785}, "extra": {"message": "Service 'db' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/netreconlab_parse-hipaa/docker-compose.yml", "start": {"line": 64, "col": 5, "offset": 3526}, "end": {"line": 64, "col": 14, "offset": 3535}, "extra": {"message": "Service 'dashboard' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/netreconlab_parse-hipaa/docker-compose.yml", "start": {"line": 64, "col": 5, "offset": 3526}, "end": {"line": 64, "col": 14, "offset": 3535}, "extra": {"message": "Service 'dashboard' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.nginx.security.insecure-ssl-version.insecure-ssl-version", "path": "downloaded_repos/netreconlab_parse-hipaa/nginx/sites-enabled/default-ssl.conf", "start": {"line": 11, "col": 5, "offset": 343}, "end": {"line": 11, "col": 48, "offset": 386}, "extra": {"message": "Detected use of an insecure SSL version. Secure SSL versions are TLSv1.2 and TLS1.3; older versions are known to be broken and are susceptible to attacks. Prefer use of TLSv1.2 or later.", "metadata": {"cwe": ["CWE-326: Inadequate Encryption Strength"], "references": ["https://www.acunetix.com/blog/web-security-zone/hardening-nginx/", "https://www.acunetix.com/blog/articles/tls-ssl-cipher-hardening/"], "category": "security", "technology": ["nginx"], "confidence": "HIGH", "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.nginx.security.insecure-ssl-version.insecure-ssl-version", "shortlink": "https://sg.run/gLKy"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/netreconlab_parse-hipaa/parse/docker-compose.test.yml", "start": {"line": 4, "col": 3, "offset": 28}, "end": {"line": 4, "col": 6, "offset": 31}, "extra": {"message": "Service 'sut' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/netreconlab_parse-hipaa/parse/docker-compose.test.yml", "start": {"line": 4, "col": 3, "offset": 28}, "end": {"line": 4, "col": 6, "offset": 31}, "extra": {"message": "Service 'sut' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/netreconlab_parse-hipaa/parse/docker-compose.test.yml", "start": {"line": 37, "col": 3, "offset": 1183}, "end": {"line": 37, "col": 5, "offset": 1185}, "extra": {"message": "Service 'db' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/netreconlab_parse-hipaa/parse/docker-compose.test.yml", "start": {"line": 37, "col": 3, "offset": 1183}, "end": {"line": 37, "col": 5, "offset": 1185}, "extra": {"message": "Service 'db' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-check-csurf-middleware-usage.express-check-csurf-middleware-usage", "path": "downloaded_repos/netreconlab_parse-hipaa/parse/index.js", "start": {"line": 461, "col": 5, "offset": 19131}, "end": {"line": 461, "col": 20, "offset": 19146}, "extra": {"message": "A CSRF middleware was not detected in your express application. Ensure you are either using one such as `csurf` or `csrf` (see rule references) and/or you are properly doing CSRF validation in your routes with a token or cookies.", "metadata": {"category": "security", "references": ["https://www.npmjs.com/package/csurf", "https://www.npmjs.com/package/csrf", "https://cheatsheetseries.owasp.org/cheatsheets/Cross-Site_Request_Forgery_Prevention_Cheat_Sheet.html"], "cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "technology": ["javascript", "typescript", "express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-check-csurf-middleware-usage.express-check-csurf-middleware-usage", "shortlink": "https://sg.run/BxzR"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-open-redirect.express-open-redirect", "path": "downloaded_repos/netreconlab_parse-hipaa/parse/index.js", "start": {"line": 469, "col": 43, "offset": 19379}, "end": {"line": 469, "col": 63, "offset": 19399}, "extra": {"message": "The application redirects to a URL specified by user-supplied input `request` that is not validated. This could redirect users to malicious locations. Consider using an allow-list approach to validate URLs, or warn users they are being redirected to a third-party website.", "metadata": {"technology": ["express"], "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "owasp": ["A01:2021 - Broken Access Control"], "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-open-redirect.express-open-redirect", "shortlink": "https://sg.run/EpoP"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-open-redirect.express-open-redirect", "path": "downloaded_repos/netreconlab_parse-hipaa/parse/index.js", "start": {"line": 491, "col": 45, "offset": 20137}, "end": {"line": 491, "col": 65, "offset": 20157}, "extra": {"message": "The application redirects to a URL specified by user-supplied input `request` that is not validated. This could redirect users to malicious locations. Consider using an allow-list approach to validate URLs, or warn users they are being redirected to a third-party website.", "metadata": {"technology": ["express"], "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "owasp": ["A01:2021 - Broken Access Control"], "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-open-redirect.express-open-redirect", "shortlink": "https://sg.run/EpoP"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-bcrypt-hash.detected-bcrypt-hash", "path": "downloaded_repos/netreconlab_parse-hipaa/parse/parse-dashboard-config.json", "start": {"line": 16, "col": 16, "offset": 492}, "end": {"line": 16, "col": 76, "offset": 552}, "extra": {"message": "bcrypt hash detected", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "category": "security", "technology": ["secrets", "bcrypt"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-bcrypt-hash.detected-bcrypt-hash", "shortlink": "https://sg.run/3A8G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-bcrypt-hash.detected-bcrypt-hash", "path": "downloaded_repos/netreconlab_parse-hipaa/parse/parse-dashboard-config.json", "start": {"line": 19, "col": 16, "offset": 603}, "end": {"line": 19, "col": 76, "offset": 663}, "extra": {"message": "bcrypt hash detected", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "category": "security", "technology": ["secrets", "bcrypt"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-bcrypt-hash.detected-bcrypt-hash", "shortlink": "https://sg.run/3A8G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/build-dashboard.yml", "start": {"line": 98, "col": 44, "offset": 2557}, "end": {"line": 98, "col": 47, "offset": 2560}}, {"path": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/build-dashboard.yml", "start": {"line": 107, "col": 11, "offset": 2557}, "end": {"line": 107, "col": 26, "offset": 2572}}]], "message": "Syntax error at line downloaded_repos/netreconlab_parse-hipaa/.github/workflows/build-dashboard.yml:98:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/build-dashboard.yml", "spans": [{"file": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/build-dashboard.yml", "start": {"line": 98, "col": 44, "offset": 2557}, "end": {"line": 98, "col": 47, "offset": 2560}}, {"file": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/build-dashboard.yml", "start": {"line": 107, "col": 11, "offset": 2557}, "end": {"line": 107, "col": 26, "offset": 2572}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/build-dashboard.yml", "start": {"line": 118, "col": 10, "offset": 3299}, "end": {"line": 118, "col": 13, "offset": 3302}}, {"path": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/build-dashboard.yml", "start": {"line": 118, "col": 68, "offset": 3299}, "end": {"line": 118, "col": 71, "offset": 3302}}]], "message": "Syntax error at line downloaded_repos/netreconlab_parse-hipaa/.github/workflows/build-dashboard.yml:118:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/build-dashboard.yml", "spans": [{"file": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/build-dashboard.yml", "start": {"line": 118, "col": 10, "offset": 3299}, "end": {"line": 118, "col": 13, "offset": 3302}}, {"file": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/build-dashboard.yml", "start": {"line": 118, "col": 68, "offset": 3299}, "end": {"line": 118, "col": 71, "offset": 3302}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/build.yml", "start": {"line": 96, "col": 44, "offset": 2463}, "end": {"line": 96, "col": 47, "offset": 2466}}, {"path": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/build.yml", "start": {"line": 105, "col": 11, "offset": 2463}, "end": {"line": 105, "col": 26, "offset": 2478}}]], "message": "Syntax error at line downloaded_repos/netreconlab_parse-hipaa/.github/workflows/build.yml:96:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/build.yml", "spans": [{"file": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/build.yml", "start": {"line": 96, "col": 44, "offset": 2463}, "end": {"line": 96, "col": 47, "offset": 2466}}, {"file": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/build.yml", "start": {"line": 105, "col": 11, "offset": 2463}, "end": {"line": 105, "col": 26, "offset": 2478}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/build.yml", "start": {"line": 116, "col": 10, "offset": 3205}, "end": {"line": 116, "col": 13, "offset": 3208}}, {"path": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/build.yml", "start": {"line": 116, "col": 68, "offset": 3205}, "end": {"line": 116, "col": 71, "offset": 3208}}]], "message": "Syntax error at line downloaded_repos/netreconlab_parse-hipaa/.github/workflows/build.yml:116:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/build.yml", "spans": [{"file": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/build.yml", "start": {"line": 116, "col": 10, "offset": 3205}, "end": {"line": 116, "col": 13, "offset": 3208}}, {"file": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/build.yml", "start": {"line": 116, "col": 68, "offset": 3205}, "end": {"line": 116, "col": 71, "offset": 3208}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/release-dashboard.yml", "start": {"line": 90, "col": 44, "offset": 2260}, "end": {"line": 90, "col": 47, "offset": 2263}}, {"path": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/release-dashboard.yml", "start": {"line": 99, "col": 11, "offset": 2260}, "end": {"line": 99, "col": 26, "offset": 2275}}]], "message": "Syntax error at line downloaded_repos/netreconlab_parse-hipaa/.github/workflows/release-dashboard.yml:90:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/release-dashboard.yml", "spans": [{"file": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/release-dashboard.yml", "start": {"line": 90, "col": 44, "offset": 2260}, "end": {"line": 90, "col": 47, "offset": 2263}}, {"file": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/release-dashboard.yml", "start": {"line": 99, "col": 11, "offset": 2260}, "end": {"line": 99, "col": 26, "offset": 2275}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/release-dashboard.yml", "start": {"line": 109, "col": 10, "offset": 2914}, "end": {"line": 109, "col": 13, "offset": 2917}}, {"path": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/release-dashboard.yml", "start": {"line": 109, "col": 68, "offset": 2914}, "end": {"line": 109, "col": 71, "offset": 2917}}]], "message": "Syntax error at line downloaded_repos/netreconlab_parse-hipaa/.github/workflows/release-dashboard.yml:109:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/release-dashboard.yml", "spans": [{"file": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/release-dashboard.yml", "start": {"line": 109, "col": 10, "offset": 2914}, "end": {"line": 109, "col": 13, "offset": 2917}}, {"file": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/release-dashboard.yml", "start": {"line": 109, "col": 68, "offset": 2914}, "end": {"line": 109, "col": 71, "offset": 2917}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/release.yml", "start": {"line": 114, "col": 44, "offset": 3013}, "end": {"line": 114, "col": 47, "offset": 3016}}, {"path": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/release.yml", "start": {"line": 123, "col": 11, "offset": 3013}, "end": {"line": 123, "col": 26, "offset": 3028}}]], "message": "Syntax error at line downloaded_repos/netreconlab_parse-hipaa/.github/workflows/release.yml:114:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/release.yml", "spans": [{"file": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/release.yml", "start": {"line": 114, "col": 44, "offset": 3013}, "end": {"line": 114, "col": 47, "offset": 3016}}, {"file": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/release.yml", "start": {"line": 123, "col": 11, "offset": 3013}, "end": {"line": 123, "col": 26, "offset": 3028}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/release.yml", "start": {"line": 133, "col": 10, "offset": 3667}, "end": {"line": 133, "col": 13, "offset": 3670}}, {"path": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/release.yml", "start": {"line": 133, "col": 68, "offset": 3667}, "end": {"line": 133, "col": 71, "offset": 3670}}]], "message": "Syntax error at line downloaded_repos/netreconlab_parse-hipaa/.github/workflows/release.yml:133:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/release.yml", "spans": [{"file": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/release.yml", "start": {"line": 133, "col": 10, "offset": 3667}, "end": {"line": 133, "col": 13, "offset": 3670}}, {"file": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/release.yml", "start": {"line": 133, "col": 68, "offset": 3667}, "end": {"line": 133, "col": 71, "offset": 3670}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/release.yml", "start": {"line": 143, "col": 10, "offset": 4126}, "end": {"line": 143, "col": 13, "offset": 4129}}, {"path": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/release.yml", "start": {"line": 143, "col": 68, "offset": 4126}, "end": {"line": 143, "col": 71, "offset": 4129}}]], "message": "Syntax error at line downloaded_repos/netreconlab_parse-hipaa/.github/workflows/release.yml:143:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/release.yml", "spans": [{"file": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/release.yml", "start": {"line": 143, "col": 10, "offset": 4126}, "end": {"line": 143, "col": 13, "offset": 4129}}, {"file": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/release.yml", "start": {"line": 143, "col": 68, "offset": 4126}, "end": {"line": 143, "col": 71, "offset": 4129}}]}], "paths": {"scanned": ["downloaded_repos/netreconlab_parse-hipaa/.env", "downloaded_repos/netreconlab_parse-hipaa/.github/dependabot.yml", "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/build-dashboard.yml", "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/build.yml", "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/release-dashboard.yml", "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/release.yml", "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/stale.yml", "downloaded_repos/netreconlab_parse-hipaa/.gitignore", "downloaded_repos/netreconlab_parse-hipaa/.slugignore", "downloaded_repos/netreconlab_parse-hipaa/LICENSE", "downloaded_repos/netreconlab_parse-hipaa/README.md", "downloaded_repos/netreconlab_parse-hipaa/Singularity", "downloaded_repos/netreconlab_parse-hipaa/app.json", "downloaded_repos/netreconlab_parse-hipaa/dashboard.env", "downloaded_repos/netreconlab_parse-hipaa/docker-compose.dashboard.yml", "downloaded_repos/netreconlab_parse-hipaa/docker-compose.mongo.dashboard.yml", "downloaded_repos/netreconlab_parse-hipaa/docker-compose.mongo.yml", "downloaded_repos/netreconlab_parse-hipaa/docker-compose.no.hipaa.yml", "downloaded_repos/netreconlab_parse-hipaa/docker-compose.yml", "downloaded_repos/netreconlab_parse-hipaa/general.env", "downloaded_repos/netreconlab_parse-hipaa/heroku.yml", "downloaded_repos/netreconlab_parse-hipaa/nginx/sites-enabled/default-ssl.conf", "downloaded_repos/netreconlab_parse-hipaa/nginx/sites-enabled/default.conf", "downloaded_repos/netreconlab_parse-hipaa/parse/Dockerfile", "downloaded_repos/netreconlab_parse-hipaa/parse/Dockerfile.dashboard", "downloaded_repos/netreconlab_parse-hipaa/parse/Dockerfile.heroku", "downloaded_repos/netreconlab_parse-hipaa/parse/cloud/carePlan.js", "downloaded_repos/netreconlab_parse-hipaa/parse/cloud/contact.js", "downloaded_repos/netreconlab_parse-hipaa/parse/cloud/files.js", "downloaded_repos/netreconlab_parse-hipaa/parse/cloud/main.js", "downloaded_repos/netreconlab_parse-hipaa/parse/cloud/note.js", "downloaded_repos/netreconlab_parse-hipaa/parse/cloud/outcome.js", "downloaded_repos/netreconlab_parse-hipaa/parse/cloud/outcomeValue.js", "downloaded_repos/netreconlab_parse-hipaa/parse/cloud/patient.js", "downloaded_repos/netreconlab_parse-hipaa/parse/cloud/task.js", "downloaded_repos/netreconlab_parse-hipaa/parse/docker-compose.test.yml", "downloaded_repos/netreconlab_parse-hipaa/parse/ecosystem.config.js", "downloaded_repos/netreconlab_parse-hipaa/parse/index.js", "downloaded_repos/netreconlab_parse-hipaa/parse/parse-dashboard-config.json", "downloaded_repos/netreconlab_parse-hipaa/parse/process.yml", "downloaded_repos/netreconlab_parse-hipaa/parse/scripts/parse_idempotency_delete_expired_records.sh", "downloaded_repos/netreconlab_parse-hipaa/parse/scripts/setup-dbs.sh", "downloaded_repos/netreconlab_parse-hipaa/parse/scripts/setup-parse-index.sh", "downloaded_repos/netreconlab_parse-hipaa/parse/scripts/setup-pgaudit.sh", "downloaded_repos/netreconlab_parse-hipaa/parse/scripts/wait-for-postgres.sh", "downloaded_repos/netreconlab_parse-hipaa/scripts/wait-for-postgres.sh", "downloaded_repos/netreconlab_parse-hipaa/singularity-compose.yml"], "skipped": [{"path": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/build-dashboard.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/build.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/release-dashboard.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/netreconlab_parse-hipaa/.github/workflows/release.yml", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.6812779903411865, "profiling_times": {"config_time": 5.784739971160889, "core_time": 3.010019063949585, "ignores_time": 0.001752614974975586, "total_time": 8.797600984573364}, "parsing_time": {"total_time": 0.43970298767089844, "per_file_time": {"mean": 0.012562942504882811, "std_dev": 0.0003152815543242988}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 2.062026262283325, "per_file_time": {"mean": 0.01574065849071241, "std_dev": 0.002902719278621224}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.7464168071746826, "per_file_and_rule_time": {"mean": 0.0013848178240717674, "std_dev": 2.491440001942679e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.11493086814880371, "per_def_and_rule_time": {"mean": 0.0012097986120926708, "std_dev": 2.0350068734968325e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}