{"version": "1.130.0", "results": [], "errors": [], "paths": {"scanned": ["downloaded_repos/delight-im_PHP-Auth/.editorconfig", "downloaded_repos/delight-im_PHP-Auth/.gitignore", "downloaded_repos/delight-im_PHP-Auth/Database/MySQL.sql", "downloaded_repos/delight-im_PHP-Auth/Database/PostgreSQL.sql", "downloaded_repos/delight-im_PHP-Auth/Database/SQLite.sql", "downloaded_repos/delight-im_PHP-Auth/LICENSE", "downloaded_repos/delight-im_PHP-Auth/Migration.md", "downloaded_repos/delight-im_PHP-Auth/README.md", "downloaded_repos/delight-im_PHP-Auth/composer.json", "downloaded_repos/delight-im_PHP-Auth/composer.lock", "downloaded_repos/delight-im_PHP-Auth/src/Administration.php", "downloaded_repos/delight-im_PHP-Auth/src/AmbiguousUsernameException.php", "downloaded_repos/delight-im_PHP-Auth/src/AttemptCancelledException.php", "downloaded_repos/delight-im_PHP-Auth/src/Auth.php", "downloaded_repos/delight-im_PHP-Auth/src/AuthError.php", "downloaded_repos/delight-im_PHP-Auth/src/AuthException.php", "downloaded_repos/delight-im_PHP-Auth/src/ConfirmationRequestNotFound.php", "downloaded_repos/delight-im_PHP-Auth/src/DatabaseError.php", "downloaded_repos/delight-im_PHP-Auth/src/DuplicateUsernameException.php", "downloaded_repos/delight-im_PHP-Auth/src/EmailAddress.php", "downloaded_repos/delight-im_PHP-Auth/src/EmailNotVerifiedException.php", "downloaded_repos/delight-im_PHP-Auth/src/EmailOrUsernameRequiredError.php", "downloaded_repos/delight-im_PHP-Auth/src/HeadersAlreadySentError.php", "downloaded_repos/delight-im_PHP-Auth/src/InvalidEmailException.php", "downloaded_repos/delight-im_PHP-Auth/src/InvalidOneTimePasswordException.php", "downloaded_repos/delight-im_PHP-Auth/src/InvalidPasswordException.php", "downloaded_repos/delight-im_PHP-Auth/src/InvalidPhoneNumberException.php", "downloaded_repos/delight-im_PHP-Auth/src/InvalidSelectorTokenPairException.php", "downloaded_repos/delight-im_PHP-Auth/src/InvalidStateError.php", "downloaded_repos/delight-im_PHP-Auth/src/IpAddress.php", "downloaded_repos/delight-im_PHP-Auth/src/MissingCallbackError.php", "downloaded_repos/delight-im_PHP-Auth/src/NotLoggedInException.php", "downloaded_repos/delight-im_PHP-Auth/src/PasswordHash.php", "downloaded_repos/delight-im_PHP-Auth/src/PhoneNumber.php", "downloaded_repos/delight-im_PHP-Auth/src/ResetDisabledException.php", "downloaded_repos/delight-im_PHP-Auth/src/Role.php", "downloaded_repos/delight-im_PHP-Auth/src/SecondFactorRequiredException.php", "downloaded_repos/delight-im_PHP-Auth/src/Status.php", "downloaded_repos/delight-im_PHP-Auth/src/TokenExpiredException.php", "downloaded_repos/delight-im_PHP-Auth/src/TokenHash.php", "downloaded_repos/delight-im_PHP-Auth/src/TooManyRequestsException.php", "downloaded_repos/delight-im_PHP-Auth/src/TwoFactorMechanismAlreadyEnabledException.php", "downloaded_repos/delight-im_PHP-Auth/src/TwoFactorMechanismNotInitializedException.php", "downloaded_repos/delight-im_PHP-Auth/src/UnknownIdException.php", "downloaded_repos/delight-im_PHP-Auth/src/UnknownUsernameException.php", "downloaded_repos/delight-im_PHP-Auth/src/UserAlreadyExistsException.php", "downloaded_repos/delight-im_PHP-Auth/src/UserManager.php"], "skipped": [{"path": "downloaded_repos/delight-im_PHP-Auth/tests/index.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.640869140625, "profiling_times": {"config_time": 5.60328483581543, "core_time": 3.359779119491577, "ignores_time": 0.001743316650390625, "total_time": 8.96595048904419}, "parsing_time": {"total_time": 0.29131126403808594, "per_file_time": {"mean": 0.007666085895739106, "std_dev": 0.00012174324931310517}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.955733060836792, "per_file_time": {"mean": 0.014816159551793886, "std_dev": 0.009536934301764138}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.7265970706939697, "per_file_and_rule_time": {"mean": 0.01171930759183822, "std_dev": 0.00095334179213337}, "very_slow_stats": {"time_ratio": 0.4471445929526124, "count_ratio": 0.03225806451612903}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/delight-im_PHP-Auth/src/Auth.php", "rule_id": "php.lang.security.injection.tainted-callable.tainted-callable", "time": 0.13448500633239746}, {"fpath": "downloaded_repos/delight-im_PHP-Auth/src/Auth.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.19040894508361816}]}, "tainting_time": {"total_time": 0.30396413803100586, "per_def_and_rule_time": {"mean": 0.00032967910849349873, "std_dev": 2.6075811368033596e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}