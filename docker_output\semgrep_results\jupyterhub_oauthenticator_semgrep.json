{"version": "1.130.0", "results": [{"check_id": "bash.lang.security.ifs-tampering.ifs-tampering", "path": "downloaded_repos/jupyterhub_oauthenticator/examples/full/addusers.sh", "start": {"line": 3, "col": 1, "offset": 11}, "end": {"line": 4, "col": 2, "offset": 18}, "extra": {"message": "The special variable IFS affects how splitting takes place when expanding unquoted variables. Don't set it globally. Prefer a dedicated utility such as 'cut' or 'awk' if you need to split input data. If you must use 'read', set IFS locally using e.g. 'IFS=\",\" read -a my_array'.", "metadata": {"cwe": ["CWE-20: Improper Input Validation"], "category": "security", "technology": ["bash"], "confidence": "LOW", "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/bash.lang.security.ifs-tampering.ifs-tampering", "shortlink": "https://sg.run/Q9pq"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.jwt.security.unverified-jwt-decode.unverified-jwt-decode", "path": "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/azuread.py", "start": {"line": 73, "col": 42, "offset": 2054}, "end": {"line": 73, "col": 47, "offset": 2059}, "extra": {"message": "Detected JWT token decoded with 'verify=False'. This bypasses any integrity checks for the token which means the token could be tampered with by malicious actors. Ensure that the JWT token is verified.", "fix": "True", "metadata": {"owasp": ["A02:2017 - Broken Authentication", "A07:2021 - Identification and Authentication Failures"], "cwe": ["CWE-287: Improper Authentication"], "references": ["https://github.com/we45/Vulnerable-Flask-App/blob/752ee16087c0bfb79073f68802d907569a1f0df7/app/app.py#L96"], "category": "security", "technology": ["jwt"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/python.jwt.security.unverified-jwt-decode.unverified-jwt-decode", "shortlink": "https://sg.run/6nyB"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.deserialization.pickle.avoid-pickle", "path": "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/globus.py", "start": {"line": 220, "col": 44, "offset": 7461}, "end": {"line": 220, "col": 63, "offset": 7480}, "extra": {"message": "Avoid using `pickle`, which is known to lead to code execution vulnerabilities. When unpickling, the serialized data could be manipulated to run arbitrary code. Instead, consider serializing the relevant data as JSON or a similar text-based serialization format.", "metadata": {"owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "references": ["https://docs.python.org/3/library/pickle.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/python.lang.security.deserialization.pickle.avoid-pickle", "shortlink": "https://sg.run/OPwB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/jupyterhub_oauthenticator/examples/templates/403.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 10, "offset": 89}}, {"path": "downloaded_repos/jupyterhub_oauthenticator/examples/templates/403.html", "start": {"line": 27, "col": 1, "offset": 0}, "end": {"line": 27, "col": 65, "offset": 64}}]], "message": "Syntax error at line downloaded_repos/jupyterhub_oauthenticator/examples/templates/403.html:1:\n `{% extends \"templates/error.html\" %} {% block error_detail %} {% if status_code\n== 403 %}` was unexpected", "path": "downloaded_repos/jupyterhub_oauthenticator/examples/templates/403.html", "spans": [{"file": "downloaded_repos/jupyterhub_oauthenticator/examples/templates/403.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 10, "offset": 89}}, {"file": "downloaded_repos/jupyterhub_oauthenticator/examples/templates/403.html", "start": {"line": 27, "col": 1, "offset": 0}, "end": {"line": 27, "col": 65, "offset": 64}}]}], "paths": {"scanned": ["downloaded_repos/jupyterhub_oauthenticator/.flake8", "downloaded_repos/jupyterhub_oauthenticator/.github/dependabot.yaml", "downloaded_repos/jupyterhub_oauthenticator/.github/workflows/release.yaml", "downloaded_repos/jupyterhub_oauthenticator/.github/workflows/test-docs.yaml", "downloaded_repos/jupyterhub_oauthenticator/.github/workflows/test.yml", "downloaded_repos/jupyterhub_oauthenticator/.gitignore", "downloaded_repos/jupyterhub_oauthenticator/.pre-commit-config.yaml", "downloaded_repos/jupyterhub_oauthenticator/.readthedocs.yaml", "downloaded_repos/jupyterhub_oauthenticator/CONTRIBUTING.md", "downloaded_repos/jupyterhub_oauthenticator/LICENSE", "downloaded_repos/jupyterhub_oauthenticator/MANIFEST.in", "downloaded_repos/jupyterhub_oauthenticator/README.md", "downloaded_repos/jupyterhub_oauthenticator/RELEASE.md", "downloaded_repos/jupyterhub_oauthenticator/docs/Makefile", "downloaded_repos/jupyterhub_oauthenticator/docs/make.bat", "downloaded_repos/jupyterhub_oauthenticator/docs/requirements.txt", "downloaded_repos/jupyterhub_oauthenticator/docs/source/_static/images/JupyterHub-OAuth-external-flow.png", "downloaded_repos/jupyterhub_oauthenticator/docs/source/_static/images/logo/favicon.ico", "downloaded_repos/jupyterhub_oauthenticator/docs/source/_static/images/logo/logo.png", "downloaded_repos/jupyterhub_oauthenticator/docs/source/conf.py", "downloaded_repos/jupyterhub_oauthenticator/docs/source/how-to/custom-403.md", "downloaded_repos/jupyterhub_oauthenticator/docs/source/how-to/example-oauthenticator.py", "downloaded_repos/jupyterhub_oauthenticator/docs/source/how-to/migrations/upgrade-to-15.md", "downloaded_repos/jupyterhub_oauthenticator/docs/source/how-to/refresh.md", "downloaded_repos/jupyterhub_oauthenticator/docs/source/how-to/writing-an-oauthenticator.md", "downloaded_repos/jupyterhub_oauthenticator/docs/source/index.md", "downloaded_repos/jupyterhub_oauthenticator/docs/source/reference/api/authenticator.rst.tpl", "downloaded_repos/jupyterhub_oauthenticator/docs/source/reference/api/index.rst.tpl", "downloaded_repos/jupyterhub_oauthenticator/docs/source/reference/changelog.md", "downloaded_repos/jupyterhub_oauthenticator/docs/source/topic/allowing.md", "downloaded_repos/jupyterhub_oauthenticator/docs/source/topic/extending.md", "downloaded_repos/jupyterhub_oauthenticator/docs/source/topic/github.md", "downloaded_repos/jupyterhub_oauthenticator/docs/source/topic/gitlab.md", "downloaded_repos/jupyterhub_oauthenticator/docs/source/topic/google.md", "downloaded_repos/jupyterhub_oauthenticator/docs/source/tutorials/general-setup.md", "downloaded_repos/jupyterhub_oauthenticator/docs/source/tutorials/install.md", "downloaded_repos/jupyterhub_oauthenticator/docs/source/tutorials/provider-specific-setup/index.md", "downloaded_repos/jupyterhub_oauthenticator/docs/source/tutorials/provider-specific-setup/providers/auth0.md", "downloaded_repos/jupyterhub_oauthenticator/docs/source/tutorials/provider-specific-setup/providers/azuread.md", "downloaded_repos/jupyterhub_oauthenticator/docs/source/tutorials/provider-specific-setup/providers/bitbucket.md", "downloaded_repos/jupyterhub_oauthenticator/docs/source/tutorials/provider-specific-setup/providers/cilogon.md", "downloaded_repos/jupyterhub_oauthenticator/docs/source/tutorials/provider-specific-setup/providers/generic.md", "downloaded_repos/jupyterhub_oauthenticator/docs/source/tutorials/provider-specific-setup/providers/github.md", "downloaded_repos/jupyterhub_oauthenticator/docs/source/tutorials/provider-specific-setup/providers/gitlab.md", "downloaded_repos/jupyterhub_oauthenticator/docs/source/tutorials/provider-specific-setup/providers/globus.md", "downloaded_repos/jupyterhub_oauthenticator/docs/source/tutorials/provider-specific-setup/providers/google.md", "downloaded_repos/jupyterhub_oauthenticator/docs/source/tutorials/provider-specific-setup/providers/mediawiki.md", "downloaded_repos/jupyterhub_oauthenticator/docs/source/tutorials/provider-specific-setup/providers/openshift.md", "downloaded_repos/jupyterhub_oauthenticator/examples/auth_state/README.md", "downloaded_repos/jupyterhub_oauthenticator/examples/auth_state/gist-nb.ipynb", "downloaded_repos/jupyterhub_oauthenticator/examples/auth_state/jupyterhub_config.py", "downloaded_repos/jupyterhub_oauthenticator/examples/azuread/run.sh", "downloaded_repos/jupyterhub_oauthenticator/examples/azuread/sample_jupyter_config.py", "downloaded_repos/jupyterhub_oauthenticator/examples/full/Dockerfile", "downloaded_repos/jupyterhub_oauthenticator/examples/full/README.md", "downloaded_repos/jupyterhub_oauthenticator/examples/full/addusers.sh", "downloaded_repos/jupyterhub_oauthenticator/examples/full/env", "downloaded_repos/jupyterhub_oauthenticator/examples/full/jupyterhub_config.py", "downloaded_repos/jupyterhub_oauthenticator/examples/full/ssl/README.md", "downloaded_repos/jupyterhub_oauthenticator/examples/full/userlist", "downloaded_repos/jupyterhub_oauthenticator/examples/mock-provider/README.md", "downloaded_repos/jupyterhub_oauthenticator/examples/mock-provider/jupyterhub_config.py", "downloaded_repos/jupyterhub_oauthenticator/examples/templates/403.html", "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/__init__.py", "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/_version.py", "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/auth0.py", "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/azuread.py", "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/bitbucket.py", "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/cilogon.py", "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/generic.py", "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/github.py", "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/gitlab.py", "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/globus.py", "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/google.py", "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/mediawiki.py", "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/oauth2.py", "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/openshift.py", "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/schemas/cilogon-schema.yaml", "downloaded_repos/jupyterhub_oauthenticator/pyproject.toml", "downloaded_repos/jupyterhub_oauthenticator/requirements.txt", "downloaded_repos/jupyterhub_oauthenticator/setup.py"], "skipped": [{"path": "downloaded_repos/jupyterhub_oauthenticator/examples/templates/403.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/tests/conftest.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/tests/mocks.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/tests/test_auth0.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/tests/test_azuread.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/tests/test_bitbucket.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/tests/test_cilogon.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/tests/test_generic.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/tests/test_github.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/tests/test_gitlab.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/tests/test_globus.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/tests/test_google.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/tests/test_mediawiki.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/tests/test_oauth2.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/tests/test_openshift.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 2.519688129425049, "profiling_times": {"config_time": 11.65789008140564, "core_time": 5.516754388809204, "ignores_time": 0.0030739307403564453, "total_time": 17.178884744644165}, "parsing_time": {"total_time": 0.8787820339202881, "per_file_time": {"mean": 0.02510805811200823, "std_dev": 0.0012197550554170378}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 10.115494012832642, "per_file_time": {"mean": 0.051347685344328105, "std_dev": 0.03929249386793995}, "very_slow_stats": {"time_ratio": 0.1808475110822692, "count_ratio": 0.005076142131979695}, "very_slow_files": [{"fpath": "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/oauth2.py", "ftime": 1.829361915588379}]}, "matching_time": {"total_time": 5.275529146194458, "per_file_and_rule_time": {"mean": 0.00915890476769871, "std_dev": 0.0010750302631170395}, "very_slow_stats": {"time_ratio": 0.40399042390096557, "count_ratio": 0.015625}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/oauth2.py", "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.10596895217895508}, {"fpath": "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/oauth2.py", "rule_id": "python.django.security.injection.path-traversal.path-traversal-open.path-traversal-open", "time": 0.10674309730529785}, {"fpath": "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/mediawiki.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.1926131248474121}, {"fpath": "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/oauth2.py", "rule_id": "python.lang.security.dangerous-system-call.dangerous-system-call", "time": 0.2091989517211914}, {"fpath": "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/google.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.2562401294708252}, {"fpath": "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/github.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.2964060306549072}, {"fpath": "downloaded_repos/jupyterhub_oauthenticator/oauthenticator/oauth2.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.30463385581970215}, {"fpath": "downloaded_repos/jupyterhub_oauthenticator/docs/source/conf.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.3205680847167969}, {"fpath": "downloaded_repos/jupyterhub_oauthenticator/setup.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.33889102935791016}]}, "tainting_time": {"total_time": 0.848651647567749, "per_def_and_rule_time": {"mean": 0.0005489337953219592, "std_dev": 2.1222941157903854e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}