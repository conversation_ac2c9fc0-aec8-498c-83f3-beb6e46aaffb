"""empty message

Revision ID: 8873c9333689
Revises: a4503f708aee
Create Date: 2022-06-19 15:40:26.708272

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '8873c9333689'
down_revision = 'a4503f708aee'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('video_view',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('video_id', sa.String(length=32), nullable=False),
    sa.Column('ip_address', sa.String(length=256), nullable=False),
    sa.ForeignKeyConstraint(['video_id'], ['video.video_id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('video_id', 'ip_address')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('video_view')
    # ### end Alembic commands ###
