{"version": "1.130.0", "results": [{"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/TaleLin_lin-cms-flask/app/cli/plugin/init.py", "start": {"line": 81, "col": 28, "offset": 2888}, "end": {"line": 81, "col": 83, "offset": 2943}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/TaleLin_lin-cms-flask/app/cli/plugin/init.py", "start": {"line": 96, "col": 33, "offset": 3615}, "end": {"line": 96, "col": 101, "offset": 3683}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/TaleLin_lin-cms-flask/app/cli/plugin/init.py", "start": {"line": 115, "col": 19, "offset": 4477}, "end": {"line": 115, "col": 76, "offset": 4534}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "path": "downloaded_repos/TaleLin_lin-cms-flask/app/cli/plugin/init.py", "start": {"line": 154, "col": 49, "offset": 5925}, "end": {"line": 154, "col": 53, "offset": 5929}, "extra": {"message": "Found 'subprocess' function 'check_call' with 'shell=True'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use 'shell=False' instead.", "fix": "False", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b602_subprocess_popen_with_shell_equals_true.html", "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://stackoverflow.com/questions/3172470/actual-meaning-of-shell-true-in-subprocess", "https://docs.python.org/3/library/subprocess.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["secure default"], "likelihood": "HIGH", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "shortlink": "https://sg.run/J92w"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.insecure-uuid-version.insecure-uuid-version", "path": "downloaded_repos/TaleLin_lin-cms-flask/app/lin/file.py", "start": {"line": 56, "col": 20, "offset": 1703}, "end": {"line": 56, "col": 32, "offset": 1715}, "extra": {"message": "Using UUID version 1 for UUID generation can lead to predictable UUIDs based on system information (e.g., MAC address, timestamp). This may lead to security risks such as the sandwich attack. Consider using `uuid.uuid4()` instead for better randomness and security.", "fix": "uuid.uuid4()", "metadata": {"references": ["https://www.landh.tech/blog/20230811-sandwich-attack/"], "cwe": ["CWE-330: Use of Insufficiently Random Values"], "owasp": ["A02:2021 - Cryptographic Failures"], "asvs": {"control_id": "6.3.2 Insecure UUID Generation", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x14-V6-Cryptography.md#v63-random-values", "section": "V6 Stored Cryptography Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/python.lang.security.insecure-uuid-version.insecure-uuid-version", "shortlink": "https://sg.run/BYBgW"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/TaleLin_lin-cms-flask/app/lin/loader.py", "start": {"line": 44, "col": 15, "offset": 1163}, "end": {"line": 44, "col": 34, "offset": 1182}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/TaleLin_lin-cms-flask/app/lin/loader.py", "start": {"line": 60, "col": 20, "offset": 1881}, "end": {"line": 60, "col": 39, "offset": 1900}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/TaleLin_lin-cms-flask/app/lin/loader.py", "start": {"line": 68, "col": 23, "offset": 2178}, "end": {"line": 68, "col": 49, "offset": 2204}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.exec-detected.exec-detected", "path": "downloaded_repos/TaleLin_lin-cms-flask/app/lin/utils.py", "start": {"line": 46, "col": 13, "offset": 1136}, "end": {"line": 46, "col": 72, "offset": 1195}, "extra": {"message": "Detected the use of exec(). exec() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b102_exec_used.html", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.exec-detected.exec-detected", "shortlink": "https://sg.run/ndRX"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/TaleLin_lin-cms-flask/app/lin/utils.py", "start": {"line": 70, "col": 11, "offset": 1900}, "end": {"line": 70, "col": 32, "offset": 1921}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.insecure-uuid-version.insecure-uuid-version", "path": "downloaded_repos/TaleLin_lin-cms-flask/app/plugin/cos/app/model.py", "start": {"line": 82, "col": 20, "offset": 2485}, "end": {"line": 82, "col": 32, "offset": 2497}, "extra": {"message": "Using UUID version 1 for UUID generation can lead to predictable UUIDs based on system information (e.g., MAC address, timestamp). This may lead to security risks such as the sandwich attack. Consider using `uuid.uuid4()` instead for better randomness and security.", "fix": "uuid.uuid4()", "metadata": {"references": ["https://www.landh.tech/blog/20230811-sandwich-attack/"], "cwe": ["CWE-330: Use of Insufficiently Random Values"], "owasp": ["A02:2021 - Cryptographic Failures"], "asvs": {"control_id": "6.3.2 Insecure UUID Generation", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x14-V6-Cryptography.md#v63-random-values", "section": "V6 Stored Cryptography Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/python.lang.security.insecure-uuid-version.insecure-uuid-version", "shortlink": "https://sg.run/BYBgW"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/TaleLin_lin-cms-flask/.development.env", "downloaded_repos/TaleLin_lin-cms-flask/.dockerignore", "downloaded_repos/TaleLin_lin-cms-flask/.editorconfig", "downloaded_repos/TaleLin_lin-cms-flask/.flake8", "downloaded_repos/TaleLin_lin-cms-flask/.flaskenv", "downloaded_repos/TaleLin_lin-cms-flask/.gitignore", "downloaded_repos/TaleLin_lin-cms-flask/.pre-commit-config.yaml", "downloaded_repos/TaleLin_lin-cms-flask/.production.env", "downloaded_repos/TaleLin_lin-cms-flask/.python-version", "downloaded_repos/TaleLin_lin-cms-flask/Dockerfile", "downloaded_repos/TaleLin_lin-cms-flask/LICENSE", "downloaded_repos/TaleLin_lin-cms-flask/README.md", "downloaded_repos/TaleLin_lin-cms-flask/app/__init__.py", "downloaded_repos/TaleLin_lin-cms-flask/app/api/__init__.py", "downloaded_repos/TaleLin_lin-cms-flask/app/api/cms/__init__.py", "downloaded_repos/TaleLin_lin-cms-flask/app/api/cms/admin.py", "downloaded_repos/TaleLin_lin-cms-flask/app/api/cms/exception/__init__.py", "downloaded_repos/TaleLin_lin-cms-flask/app/api/cms/file.py", "downloaded_repos/TaleLin_lin-cms-flask/app/api/cms/log.py", "downloaded_repos/TaleLin_lin-cms-flask/app/api/cms/model/__init__.py", "downloaded_repos/TaleLin_lin-cms-flask/app/api/cms/model/group.py", "downloaded_repos/TaleLin_lin-cms-flask/app/api/cms/model/group_permission.py", "downloaded_repos/TaleLin_lin-cms-flask/app/api/cms/model/permission.py", "downloaded_repos/TaleLin_lin-cms-flask/app/api/cms/model/user.py", "downloaded_repos/TaleLin_lin-cms-flask/app/api/cms/model/user_group.py", "downloaded_repos/TaleLin_lin-cms-flask/app/api/cms/model/user_identity.py", "downloaded_repos/TaleLin_lin-cms-flask/app/api/cms/schema/__init__.py", "downloaded_repos/TaleLin_lin-cms-flask/app/api/cms/schema/admin.py", "downloaded_repos/TaleLin_lin-cms-flask/app/api/cms/schema/log.py", "downloaded_repos/TaleLin_lin-cms-flask/app/api/cms/schema/user.py", "downloaded_repos/TaleLin_lin-cms-flask/app/api/cms/user.py", "downloaded_repos/TaleLin_lin-cms-flask/app/api/cms/validator/__init__.py", "downloaded_repos/TaleLin_lin-cms-flask/app/api/v1/__init__.py", "downloaded_repos/TaleLin_lin-cms-flask/app/api/v1/book.py", "downloaded_repos/TaleLin_lin-cms-flask/app/api/v1/exception/__init__.py", "downloaded_repos/TaleLin_lin-cms-flask/app/api/v1/model/__init__.py", "downloaded_repos/TaleLin_lin-cms-flask/app/api/v1/model/book.py", "downloaded_repos/TaleLin_lin-cms-flask/app/api/v1/schema/__init__.py", "downloaded_repos/TaleLin_lin-cms-flask/app/api/v1/validator/__init__.py", "downloaded_repos/TaleLin_lin-cms-flask/app/cli/__init__.py", "downloaded_repos/TaleLin_lin-cms-flask/app/cli/db/__init__.py", "downloaded_repos/TaleLin_lin-cms-flask/app/cli/db/fake.py", "downloaded_repos/TaleLin_lin-cms-flask/app/cli/db/init.py", "downloaded_repos/TaleLin_lin-cms-flask/app/cli/plugin/__init__.py", "downloaded_repos/TaleLin_lin-cms-flask/app/cli/plugin/generator.py", "downloaded_repos/TaleLin_lin-cms-flask/app/cli/plugin/init.py", "downloaded_repos/TaleLin_lin-cms-flask/app/config/__init__.py", "downloaded_repos/TaleLin_lin-cms-flask/app/config/base.py", "downloaded_repos/TaleLin_lin-cms-flask/app/config/code_message.py", "downloaded_repos/TaleLin_lin-cms-flask/app/config/development.py", "downloaded_repos/TaleLin_lin-cms-flask/app/config/production.py", "downloaded_repos/TaleLin_lin-cms-flask/app/exception/__init__.py", "downloaded_repos/TaleLin_lin-cms-flask/app/extension/file/__init__.py", "downloaded_repos/TaleLin_lin-cms-flask/app/extension/file/config.py", "downloaded_repos/TaleLin_lin-cms-flask/app/extension/file/file.py", "downloaded_repos/TaleLin_lin-cms-flask/app/extension/file/local_uploader.py", "downloaded_repos/TaleLin_lin-cms-flask/app/extension/notify/__init__.py", "downloaded_repos/TaleLin_lin-cms-flask/app/extension/notify/notify.py", "downloaded_repos/TaleLin_lin-cms-flask/app/extension/notify/socketio.py", "downloaded_repos/TaleLin_lin-cms-flask/app/extension/notify/sse.py", "downloaded_repos/TaleLin_lin-cms-flask/app/lin/__init__.py", "downloaded_repos/TaleLin_lin-cms-flask/app/lin/apidoc.py", "downloaded_repos/TaleLin_lin-cms-flask/app/lin/config.py", "downloaded_repos/TaleLin_lin-cms-flask/app/lin/db.py", "downloaded_repos/TaleLin_lin-cms-flask/app/lin/encoder.py", "downloaded_repos/TaleLin_lin-cms-flask/app/lin/enums.py", "downloaded_repos/TaleLin_lin-cms-flask/app/lin/exception.py", "downloaded_repos/TaleLin_lin-cms-flask/app/lin/file.py", "downloaded_repos/TaleLin_lin-cms-flask/app/lin/form.py", "downloaded_repos/TaleLin_lin-cms-flask/app/lin/interface.py", "downloaded_repos/TaleLin_lin-cms-flask/app/lin/jwt.py", "downloaded_repos/TaleLin_lin-cms-flask/app/lin/lin.py", "downloaded_repos/TaleLin_lin-cms-flask/app/lin/loader.py", "downloaded_repos/TaleLin_lin-cms-flask/app/lin/logger.py", "downloaded_repos/TaleLin_lin-cms-flask/app/lin/manager.py", "downloaded_repos/TaleLin_lin-cms-flask/app/lin/model.py", "downloaded_repos/TaleLin_lin-cms-flask/app/lin/plugin.py", "downloaded_repos/TaleLin_lin-cms-flask/app/lin/redprint.py", "downloaded_repos/TaleLin_lin-cms-flask/app/lin/syslogger.py", "downloaded_repos/TaleLin_lin-cms-flask/app/lin/utils.py", "downloaded_repos/TaleLin_lin-cms-flask/app/model/__init__.py", "downloaded_repos/TaleLin_lin-cms-flask/app/plugin/cos/README.md", "downloaded_repos/TaleLin_lin-cms-flask/app/plugin/cos/app/__init__.py", "downloaded_repos/TaleLin_lin-cms-flask/app/plugin/cos/app/controller.py", "downloaded_repos/TaleLin_lin-cms-flask/app/plugin/cos/app/exception.py", "downloaded_repos/TaleLin_lin-cms-flask/app/plugin/cos/app/model.py", "downloaded_repos/TaleLin_lin-cms-flask/app/plugin/cos/app/schema.py", "downloaded_repos/TaleLin_lin-cms-flask/app/plugin/cos/config.py", "downloaded_repos/TaleLin_lin-cms-flask/app/plugin/cos/info.py", "downloaded_repos/TaleLin_lin-cms-flask/app/plugin/cos/requirements.txt", "downloaded_repos/TaleLin_lin-cms-flask/app/plugin/oss/README.md", "downloaded_repos/TaleLin_lin-cms-flask/app/plugin/oss/app/__init__.py", "downloaded_repos/TaleLin_lin-cms-flask/app/plugin/oss/app/controller.py", "downloaded_repos/TaleLin_lin-cms-flask/app/plugin/oss/app/model.py", "downloaded_repos/TaleLin_lin-cms-flask/app/plugin/oss/config.py", "downloaded_repos/TaleLin_lin-cms-flask/app/plugin/oss/info.py", "downloaded_repos/TaleLin_lin-cms-flask/app/plugin/oss/requirements.txt", "downloaded_repos/TaleLin_lin-cms-flask/app/plugin/poem/README.md", "downloaded_repos/TaleLin_lin-cms-flask/app/plugin/poem/app/__init__.py", "downloaded_repos/TaleLin_lin-cms-flask/app/plugin/poem/app/controller.py", "downloaded_repos/TaleLin_lin-cms-flask/app/plugin/poem/app/form.py", "downloaded_repos/TaleLin_lin-cms-flask/app/plugin/poem/app/model.py", "downloaded_repos/TaleLin_lin-cms-flask/app/plugin/poem/config.py", "downloaded_repos/TaleLin_lin-cms-flask/app/plugin/poem/info.py", "downloaded_repos/TaleLin_lin-cms-flask/app/plugin/poem/requirements.txt", "downloaded_repos/TaleLin_lin-cms-flask/app/plugin/qiniu/README.md", "downloaded_repos/TaleLin_lin-cms-flask/app/plugin/qiniu/app/__init__.py", "downloaded_repos/TaleLin_lin-cms-flask/app/plugin/qiniu/app/controller.py", "downloaded_repos/TaleLin_lin-cms-flask/app/plugin/qiniu/app/model.py", "downloaded_repos/TaleLin_lin-cms-flask/app/plugin/qiniu/config.py", "downloaded_repos/TaleLin_lin-cms-flask/app/plugin/qiniu/info.py", "downloaded_repos/TaleLin_lin-cms-flask/app/plugin/qiniu/requirements.txt", "downloaded_repos/TaleLin_lin-cms-flask/app/schema/__init__.py", "downloaded_repos/TaleLin_lin-cms-flask/app/util/__init__.py", "downloaded_repos/TaleLin_lin-cms-flask/app/util/captcha.py", "downloaded_repos/TaleLin_lin-cms-flask/app/util/common.py", "downloaded_repos/TaleLin_lin-cms-flask/app/util/page.py", "downloaded_repos/TaleLin_lin-cms-flask/app/validator/__init__.py", "downloaded_repos/TaleLin_lin-cms-flask/docker-compose.yml", "downloaded_repos/TaleLin_lin-cms-flask/docker-deploy.sh", "downloaded_repos/TaleLin_lin-cms-flask/gunicorn.conf.py", "downloaded_repos/TaleLin_lin-cms-flask/pyproject.toml", "downloaded_repos/TaleLin_lin-cms-flask/requirements.txt", "downloaded_repos/TaleLin_lin-cms-flask/starter.py", "downloaded_repos/TaleLin_lin-cms-flask/uv.lock"], "skipped": [{"path": "downloaded_repos/TaleLin_lin-cms-flask/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TaleLin_lin-cms-flask/tests/config.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TaleLin_lin-cms-flask/tests/test_admin.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TaleLin_lin-cms-flask/tests/test_book.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/TaleLin_lin-cms-flask/tests/test_user.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.8500559329986572, "profiling_times": {"config_time": 6.020665884017944, "core_time": 3.736563205718994, "ignores_time": 0.0018520355224609375, "total_time": 9.759892225265503}, "parsing_time": {"total_time": 0.9283294677734375, "per_file_time": {"mean": 0.008926244882436894, "std_dev": 0.0002284026852142256}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 8.953644275665283, "per_file_time": {"mean": 0.02529278043973244, "std_dev": 0.007708034551643555}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 3.545022487640381, "per_file_and_rule_time": {"mean": 0.004291794779225643, "std_dev": 9.426408379358452e-05}, "very_slow_stats": {"time_ratio": 0.02905399470653583, "count_ratio": 0.0012106537530266344}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/TaleLin_lin-cms-flask/app/cli/plugin/init.py", "rule_id": "python.django.security.injection.command.subprocess-injection.subprocess-injection", "time": 0.1029970645904541}]}, "tainting_time": {"total_time": 1.6379125118255615, "per_def_and_rule_time": {"mean": 0.0006146013177581841, "std_dev": 1.8417470876994772e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090525056}, "engine_requested": "OSS", "skipped_rules": []}