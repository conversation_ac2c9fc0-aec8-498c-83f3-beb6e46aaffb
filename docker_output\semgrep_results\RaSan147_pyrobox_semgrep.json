{"version": "1.130.0", "results": [{"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/html_login.html", "start": {"line": 7, "col": 2, "offset": 186}, "end": {"line": 7, "col": 59, "offset": 243}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/html_login.html", "start": {"line": 55, "col": 2, "offset": 2497}, "end": {"line": 55, "col": 112, "offset": 2607}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/html_login.html", "start": {"line": 70, "col": 5, "offset": 2806}, "end": {"line": 85, "col": 12, "offset": 3490}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/html_page.html", "start": {"line": 6, "col": 2, "offset": 171}, "end": {"line": 6, "col": 59, "offset": 228}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/html_page.html", "start": {"line": 93, "col": 2, "offset": 3014}, "end": {"line": 93, "col": 112, "offset": 3124}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/html_page.html", "start": {"line": 283, "col": 2, "offset": 8624}, "end": {"line": 283, "col": 168, "offset": 8790}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/html_page.html", "start": {"line": 284, "col": 7, "offset": 8797}, "end": {"line": 284, "col": 162, "offset": 8952}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/html_page.html", "start": {"line": 293, "col": 7, "offset": 9095}, "end": {"line": 293, "col": 102, "offset": 9190}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/html_page.html", "start": {"line": 294, "col": 2, "offset": 9196}, "end": {"line": 294, "col": 151, "offset": 9345}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/html_page.html", "start": {"line": 295, "col": 12, "offset": 9357}, "end": {"line": 295, "col": 105, "offset": 9450}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/html_signup.html", "start": {"line": 7, "col": 2, "offset": 186}, "end": {"line": 7, "col": 59, "offset": 243}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/html_signup.html", "start": {"line": 55, "col": 2, "offset": 2499}, "end": {"line": 55, "col": 112, "offset": 2609}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/html_signup.html", "start": {"line": 70, "col": 5, "offset": 2811}, "end": {"line": 85, "col": 12, "offset": 3518}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/local_server (py~3.4).py", "start": {"line": 244, "col": 31, "offset": 5153}, "end": {"line": 244, "col": 35, "offset": 5157}, "extra": {"message": "Found 'subprocess' function 'call' with 'shell=True'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use 'shell=False' instead.", "fix": "False", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b602_subprocess_popen_with_shell_equals_true.html", "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://stackoverflow.com/questions/3172470/actual-meaning-of-shell-true-in-subprocess", "https://docs.python.org/3/library/subprocess.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["secure default"], "likelihood": "HIGH", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "shortlink": "https://sg.run/J92w"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.dynamic-urllib-use-detected.dynamic-urllib-use-detected", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/local_server (py~3.4).py", "start": {"line": 968, "col": 8, "offset": 24307}, "end": {"line": 968, "col": 35, "offset": 24334}, "extra": {"message": "Detected a dynamic value being used with urllib. urllib supports 'file://' schemes, so a dynamic value controlled by a malicious actor may allow them to read arbitrary files. Audit uses of urllib calls to ensure user data cannot control the URLs, or consider using the 'requests' library instead.", "metadata": {"cwe": ["CWE-939: Improper Authorization in Handler for Custom URL Scheme"], "owasp": "A01:2017 - Injection", "source-rule-url": "https://github.com/PyCQA/bandit/blob/b1411bfb43795d3ffd268bef17a839dee954c2b1/bandit/blacklists/calls.py#L163", "bandit-code": "B310", "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://cwe.mitre.org/data/definitions/939.html"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.dynamic-urllib-use-detected.dynamic-urllib-use-detected", "shortlink": "https://sg.run/dKZZ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/script_admin_page.js", "start": {"line": 117, "col": 4, "offset": 2669}, "end": {"line": 118, "col": 37, "offset": 2825}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/script_code_editor.js", "start": {"line": 37, "col": 3, "offset": 945}, "end": {"line": 37, "col": 43, "offset": 985}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/script_file_list.bak.js", "start": {"line": 615, "col": 5, "offset": 16438}, "end": {"line": 615, "col": 48, "offset": 16481}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/script_file_list.bak.js", "start": {"line": 623, "col": 5, "offset": 16726}, "end": {"line": 623, "col": 48, "offset": 16769}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/script_file_list.bak.js", "start": {"line": 629, "col": 5, "offset": 16893}, "end": {"line": 629, "col": 48, "offset": 16936}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/script_file_list.bak.js", "start": {"line": 634, "col": 5, "offset": 17025}, "end": {"line": 634, "col": 48, "offset": 17068}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/script_file_list.bak.js", "start": {"line": 639, "col": 5, "offset": 17157}, "end": {"line": 639, "col": 48, "offset": 17200}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/script_file_list.js", "start": {"line": 455, "col": 3, "offset": 12410}, "end": {"line": 455, "col": 61, "offset": 12468}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/script_file_list.js", "start": {"line": 767, "col": 3, "offset": 20496}, "end": {"line": 767, "col": 45, "offset": 20538}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/script_global copy.js", "start": {"line": 797, "col": 4, "offset": 20095}, "end": {"line": 797, "col": 35, "offset": 20126}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/script_global copy.js", "start": {"line": 802, "col": 4, "offset": 20278}, "end": {"line": 802, "col": 37, "offset": 20311}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/script_global copy.js", "start": {"line": 814, "col": 4, "offset": 20560}, "end": {"line": 814, "col": 34, "offset": 20590}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/script_global copy.js", "start": {"line": 895, "col": 2, "offset": 23120}, "end": {"line": 895, "col": 25, "offset": 23143}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/script_global.js", "start": {"line": 800, "col": 4, "offset": 20180}, "end": {"line": 800, "col": 35, "offset": 20211}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/script_global.js", "start": {"line": 805, "col": 4, "offset": 20363}, "end": {"line": 805, "col": 37, "offset": 20396}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/script_global.js", "start": {"line": 817, "col": 4, "offset": 20645}, "end": {"line": 817, "col": 34, "offset": 20675}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/script_global.js", "start": {"line": 898, "col": 2, "offset": 23223}, "end": {"line": 898, "col": 25, "offset": 23246}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/script_main.js", "start": {"line": 19, "col": 5, "offset": 423}, "end": {"line": 19, "col": 39, "offset": 457}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/script_page_handler.js", "start": {"line": 63, "col": 3, "offset": 1417}, "end": {"line": 63, "col": 41, "offset": 1455}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/script_page_handler.js", "start": {"line": 237, "col": 3, "offset": 5172}, "end": {"line": 237, "col": 45, "offset": 5214}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/script_video_player.js", "start": {"line": 69, "col": 3, "offset": 2167}, "end": {"line": 69, "col": 43, "offset": 2207}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.dynamic-urllib-use-detected.dynamic-urllib-use-detected", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/server.py", "start": {"line": 142, "col": 8, "offset": 3847}, "end": {"line": 142, "col": 46, "offset": 3885}, "extra": {"message": "Detected a dynamic value being used with urllib. urllib supports 'file://' schemes, so a dynamic value controlled by a malicious actor may allow them to read arbitrary files. Audit uses of urllib calls to ensure user data cannot control the URLs, or consider using the 'requests' library instead.", "metadata": {"cwe": ["CWE-939: Improper Authorization in Handler for Custom URL Scheme"], "owasp": "A01:2017 - Injection", "source-rule-url": "https://github.com/PyCQA/bandit/blob/b1411bfb43795d3ffd268bef17a839dee954c2b1/bandit/blacklists/calls.py#L163", "bandit-code": "B310", "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://cwe.mitre.org/data/definitions/939.html"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.dynamic-urllib-use-detected.dynamic-urllib-use-detected", "shortlink": "https://sg.run/dKZZ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.insecure-hash-algorithms.insecure-hash-algorithm-sha1", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/user_mgmt.py", "start": {"line": 381, "col": 9, "offset": 9892}, "end": {"line": 381, "col": 68, "offset": 9951}, "extra": {"message": "Detected SHA1 hash algorithm which is considered insecure. SHA1 is not collision resistant and is therefore not suitable as a cryptographic signature. Use SHA256 or SHA3 instead.", "fix": "hashlib.sha256((str(time.time()) + username).encode(\"utf-8\"))", "metadata": {"source-rule-url": "https://github.com/PyCQA/bandit/blob/d5f8fa0d89d7b11442fc6ec80ca42953974354c8/bandit/blacklists/calls.py#L59", "cwe": ["CWE-327: Use of a Broken or Risky Cryptographic Algorithm"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "bandit-code": "B303", "asvs": {"control_id": "6.2.2 Insecure Custom Algorithm", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x14-V6-Cryptography.md#v62-algorithms", "section": "V6 Stored Cryptography Verification Requirements", "version": "4"}, "references": ["https://www.schneier.com/blog/archives/2012/10/when_will_we_se.html", "https://www.trendmicro.com/vinfo/us/security/news/vulnerabilities-and-exploits/sha-1-collision-signals-the-end-of-the-algorithm-s-viability", "http://2012.sharcs.org/slides/stevens.pdf", "https://pycryptodome.readthedocs.io/en/latest/src/hash/sha3_256.html"], "category": "security", "technology": ["python"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/python.lang.security.insecure-hash-algorithms.insecure-hash-algorithm-sha1", "shortlink": "https://sg.run/ydYx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.unvalidated-password.unvalidated-password", "path": "downloaded_repos/RaSan147_pyrobox/dev_src/user_mgmt.py", "start": {"line": 399, "col": 3, "offset": 10406}, "end": {"line": 399, "col": 30, "offset": 10433}, "extra": {"message": "The password on 'user' is being set without validating the password. Call django.contrib.auth.password_validation.validate_password() with validation functions before setting the password. See https://docs.djangoproject.com/en/3.0/topics/auth/passwords/ for more information.", "fix": "if django.contrib.auth.password_validation.validate_password(password, user=user):\n      user.set_password(password)", "metadata": {"cwe": ["CWE-521: Weak Password Requirements"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://docs.djangoproject.com/en/3.0/topics/auth/passwords/#module-django.contrib.auth.password_validation"], "category": "security", "technology": ["django"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/python.django.security.audit.unvalidated-password.unvalidated-password", "shortlink": "https://sg.run/OPBL"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.dynamic-urllib-use-detected.dynamic-urllib-use-detected", "path": "downloaded_repos/RaSan147_pyrobox/src/server.py", "start": {"line": 158, "col": 8, "offset": 3760}, "end": {"line": 158, "col": 46, "offset": 3798}, "extra": {"message": "Detected a dynamic value being used with urllib. urllib supports 'file://' schemes, so a dynamic value controlled by a malicious actor may allow them to read arbitrary files. Audit uses of urllib calls to ensure user data cannot control the URLs, or consider using the 'requests' library instead.", "metadata": {"cwe": ["CWE-939: Improper Authorization in Handler for Custom URL Scheme"], "owasp": "A01:2017 - Injection", "source-rule-url": "https://github.com/PyCQA/bandit/blob/b1411bfb43795d3ffd268bef17a839dee954c2b1/bandit/blacklists/calls.py#L163", "bandit-code": "B310", "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://cwe.mitre.org/data/definitions/939.html"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.dynamic-urllib-use-detected.dynamic-urllib-use-detected", "shortlink": "https://sg.run/dKZZ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.insecure-hash-algorithms.insecure-hash-algorithm-sha1", "path": "downloaded_repos/RaSan147_pyrobox/src/user_mgmt.py", "start": {"line": 381, "col": 9, "offset": 9894}, "end": {"line": 381, "col": 68, "offset": 9953}, "extra": {"message": "Detected SHA1 hash algorithm which is considered insecure. SHA1 is not collision resistant and is therefore not suitable as a cryptographic signature. Use SHA256 or SHA3 instead.", "fix": "hashlib.sha256((str(time.time()) + username).encode(\"utf-8\"))", "metadata": {"source-rule-url": "https://github.com/PyCQA/bandit/blob/d5f8fa0d89d7b11442fc6ec80ca42953974354c8/bandit/blacklists/calls.py#L59", "cwe": ["CWE-327: Use of a Broken or Risky Cryptographic Algorithm"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "bandit-code": "B303", "asvs": {"control_id": "6.2.2 Insecure Custom Algorithm", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x14-V6-Cryptography.md#v62-algorithms", "section": "V6 Stored Cryptography Verification Requirements", "version": "4"}, "references": ["https://www.schneier.com/blog/archives/2012/10/when_will_we_se.html", "https://www.trendmicro.com/vinfo/us/security/news/vulnerabilities-and-exploits/sha-1-collision-signals-the-end-of-the-algorithm-s-viability", "http://2012.sharcs.org/slides/stevens.pdf", "https://pycryptodome.readthedocs.io/en/latest/src/hash/sha3_256.html"], "category": "security", "technology": ["python"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/python.lang.security.insecure-hash-algorithms.insecure-hash-algorithm-sha1", "shortlink": "https://sg.run/ydYx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.unvalidated-password.unvalidated-password", "path": "downloaded_repos/RaSan147_pyrobox/src/user_mgmt.py", "start": {"line": 399, "col": 3, "offset": 10408}, "end": {"line": 399, "col": 30, "offset": 10435}, "extra": {"message": "The password on 'user' is being set without validating the password. Call django.contrib.auth.password_validation.validate_password() with validation functions before setting the password. See https://docs.djangoproject.com/en/3.0/topics/auth/passwords/ for more information.", "fix": "if django.contrib.auth.password_validation.validate_password(password, user=user):\n      user.set_password(password)", "metadata": {"cwe": ["CWE-521: Weak Password Requirements"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://docs.djangoproject.com/en/3.0/topics/auth/passwords/#module-django.contrib.auth.password_validation"], "category": "security", "technology": ["django"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/python.django.security.audit.unvalidated-password.unvalidated-password", "shortlink": "https://sg.run/OPBL"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/RaSan147_pyrobox/.github/FUNDING.yml", "downloaded_repos/RaSan147_pyrobox/.gitignore", "downloaded_repos/RaSan147_pyrobox/.readthedocs.yaml", "downloaded_repos/RaSan147_pyrobox/.vscode/settings.json", "downloaded_repos/RaSan147_pyrobox/CHANGELOG.MD", "downloaded_repos/RaSan147_pyrobox/LICENSE", "downloaded_repos/RaSan147_pyrobox/NOTE.md", "downloaded_repos/RaSan147_pyrobox/README.md", "downloaded_repos/RaSan147_pyrobox/SECURITY PLAN.MD", "downloaded_repos/RaSan147_pyrobox/VERSION", "downloaded_repos/RaSan147_pyrobox/assets/favicon.ico", "downloaded_repos/RaSan147_pyrobox/assets/favicon.png", "downloaded_repos/RaSan147_pyrobox/assets/links.txt", "downloaded_repos/RaSan147_pyrobox/assets/player.js", "downloaded_repos/RaSan147_pyrobox/assets/video.css", "downloaded_repos/RaSan147_pyrobox/assets/youtube-logo.webp", "downloaded_repos/RaSan147_pyrobox/data_safety.md", "downloaded_repos/RaSan147_pyrobox/dev_src/Task flow Diagram.drawio", "downloaded_repos/RaSan147_pyrobox/dev_src/UX_Tools.py", "downloaded_repos/RaSan147_pyrobox/dev_src/_arg_parser.py", "downloaded_repos/RaSan147_pyrobox/dev_src/_exceptions.py", "downloaded_repos/RaSan147_pyrobox/dev_src/_fs_utils.py", "downloaded_repos/RaSan147_pyrobox/dev_src/_list_maker.py", "downloaded_repos/RaSan147_pyrobox/dev_src/_page_templates.py", "downloaded_repos/RaSan147_pyrobox/dev_src/_sub_extractor.py", "downloaded_repos/RaSan147_pyrobox/dev_src/_zipfly_manager.py", "downloaded_repos/RaSan147_pyrobox/dev_src/clone.py", "downloaded_repos/RaSan147_pyrobox/dev_src/data_types.py", "downloaded_repos/RaSan147_pyrobox/dev_src/html_login.html", "downloaded_repos/RaSan147_pyrobox/dev_src/html_page.html", "downloaded_repos/RaSan147_pyrobox/dev_src/html_signup.html", "downloaded_repos/RaSan147_pyrobox/dev_src/html_text_editor.html", "downloaded_repos/RaSan147_pyrobox/dev_src/html_upload.html", "downloaded_repos/RaSan147_pyrobox/dev_src/list of all py files.py", "downloaded_repos/RaSan147_pyrobox/dev_src/local_server (py~3.4).py", "downloaded_repos/RaSan147_pyrobox/dev_src/pyroDB2.py", "downloaded_repos/RaSan147_pyrobox/dev_src/pyroboxCore.py", "downloaded_repos/RaSan147_pyrobox/dev_src/pyrobox_ServerHost.py", "downloaded_repos/RaSan147_pyrobox/dev_src/script_admin_page.js", "downloaded_repos/RaSan147_pyrobox/dev_src/script_code_editor.js", "downloaded_repos/RaSan147_pyrobox/dev_src/script_error_page.js", "downloaded_repos/RaSan147_pyrobox/dev_src/script_file_list.bak.js", "downloaded_repos/RaSan147_pyrobox/dev_src/script_file_list.js", "downloaded_repos/RaSan147_pyrobox/dev_src/script_global copy.js", "downloaded_repos/RaSan147_pyrobox/dev_src/script_global.js", "downloaded_repos/RaSan147_pyrobox/dev_src/script_main.js", "downloaded_repos/RaSan147_pyrobox/dev_src/script_page_handler.js", "downloaded_repos/RaSan147_pyrobox/dev_src/script_theme.js", "downloaded_repos/RaSan147_pyrobox/dev_src/script_video_player.js", "downloaded_repos/RaSan147_pyrobox/dev_src/script_zip_page.js", "downloaded_repos/RaSan147_pyrobox/dev_src/server.py", "downloaded_repos/RaSan147_pyrobox/dev_src/style_main.css", "downloaded_repos/RaSan147_pyrobox/dev_src/te-st/Folder & with (bad) name!/another empty folder!!!/F!leWith... bad[ name ]", "downloaded_repos/RaSan147_pyrobox/dev_src/te-st/README.md", "downloaded_repos/RaSan147_pyrobox/dev_src/te-st/Utf-8 test.txt", "downloaded_repos/RaSan147_pyrobox/dev_src/te-st/attack_server.py", "downloaded_repos/RaSan147_pyrobox/dev_src/te-st/msgpack test.py", "downloaded_repos/RaSan147_pyrobox/dev_src/te-st/os-scandir vs os-listdir.py", "downloaded_repos/RaSan147_pyrobox/dev_src/te-st/test_pyrobox_module.py", "downloaded_repos/RaSan147_pyrobox/dev_src/te-st/videos/Candle - 1437.mp4", "downloaded_repos/RaSan147_pyrobox/dev_src/te-st/videos/Candle fake.mkv", "downloaded_repos/RaSan147_pyrobox/dev_src/te-st/videos/Candle.mp4", "downloaded_repos/RaSan147_pyrobox/dev_src/test_user_mgmt.py", "downloaded_repos/RaSan147_pyrobox/dev_src/user_mgmt.py", "downloaded_repos/RaSan147_pyrobox/dev_src/video.css", "downloaded_repos/RaSan147_pyrobox/docs/Makefile", "downloaded_repos/RaSan147_pyrobox/docs/conf.py", "downloaded_repos/RaSan147_pyrobox/docs/data_safety.md", "downloaded_repos/RaSan147_pyrobox/docs/index.md", "downloaded_repos/RaSan147_pyrobox/docs/make.bat", "downloaded_repos/RaSan147_pyrobox/docs/readme.md", "downloaded_repos/RaSan147_pyrobox/docs/requirements.txt", "downloaded_repos/RaSan147_pyrobox/docs/version_NOTE.txt", "downloaded_repos/RaSan147_pyrobox/pyproject.toml", "downloaded_repos/RaSan147_pyrobox/run_setup.py", "downloaded_repos/RaSan147_pyrobox/setup.cfg", "downloaded_repos/RaSan147_pyrobox/setup.py", "downloaded_repos/RaSan147_pyrobox/src/__init__.py", "downloaded_repos/RaSan147_pyrobox/src/__main__.py", "downloaded_repos/RaSan147_pyrobox/src/_arg_parser.py", "downloaded_repos/RaSan147_pyrobox/src/_exceptions.py", "downloaded_repos/RaSan147_pyrobox/src/_fs_utils.py", "downloaded_repos/RaSan147_pyrobox/src/_list_maker.py", "downloaded_repos/RaSan147_pyrobox/src/_page_templates.py", "downloaded_repos/RaSan147_pyrobox/src/_sub_extractor.py", "downloaded_repos/RaSan147_pyrobox/src/_zipfly_manager.py", "downloaded_repos/RaSan147_pyrobox/src/clone.py", "downloaded_repos/RaSan147_pyrobox/src/data_types.py", "downloaded_repos/RaSan147_pyrobox/src/pyroDB.py", "downloaded_repos/RaSan147_pyrobox/src/pyroboxCore.py", "downloaded_repos/RaSan147_pyrobox/src/pyrobox_ServerHost.py", "downloaded_repos/RaSan147_pyrobox/src/server.py", "downloaded_repos/RaSan147_pyrobox/src/tools.py", "downloaded_repos/RaSan147_pyrobox/src/user_mgmt.py"], "skipped": [{"path": "downloaded_repos/RaSan147_pyrobox/assets/player.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/RaSan147_pyrobox/assets/plyr.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/RaSan147_pyrobox/dev_src/te-st/videos/MOV file.mov", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/RaSan147_pyrobox/docs/_build/doctrees/data_safety.doctree", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/RaSan147_pyrobox/docs/_build/doctrees/environment.pickle", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/RaSan147_pyrobox/docs/_build/doctrees/index.doctree", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/RaSan147_pyrobox/docs/_build/doctrees/readme.doctree", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/RaSan147_pyrobox/docs/_build/html/_sources/data_safety.md.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/RaSan147_pyrobox/docs/_build/html/_sources/index.md.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/RaSan147_pyrobox/docs/_build/html/_sources/index.rst.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/RaSan147_pyrobox/docs/_build/html/_sources/readme.md.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/RaSan147_pyrobox/docs/_build/html/_static/alabaster.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/RaSan147_pyrobox/docs/_build/html/_static/basic.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/RaSan147_pyrobox/docs/_build/html/_static/custom.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/RaSan147_pyrobox/docs/_build/html/_static/doctools.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/RaSan147_pyrobox/docs/_build/html/_static/documentation_options.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/RaSan147_pyrobox/docs/_build/html/_static/file.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/RaSan147_pyrobox/docs/_build/html/_static/language_data.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/RaSan147_pyrobox/docs/_build/html/_static/minus.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/RaSan147_pyrobox/docs/_build/html/_static/plus.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/RaSan147_pyrobox/docs/_build/html/_static/pygments.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/RaSan147_pyrobox/docs/_build/html/_static/searchtools.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/RaSan147_pyrobox/docs/_build/html/_static/sphinx_highlight.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/RaSan147_pyrobox/docs/_build/html/data_safety.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/RaSan147_pyrobox/docs/_build/html/genindex.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/RaSan147_pyrobox/docs/_build/html/index.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/RaSan147_pyrobox/docs/_build/html/objects.inv", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/RaSan147_pyrobox/docs/_build/html/readme.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/RaSan147_pyrobox/docs/_build/html/search.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/RaSan147_pyrobox/docs/_build/html/searchindex.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.7059299945831299, "profiling_times": {"config_time": 6.3381664752960205, "core_time": 11.771146535873413, "ignores_time": 0.0020799636840820312, "total_time": 18.11215090751648}, "parsing_time": {"total_time": 3.361579418182373, "per_file_time": {"mean": 0.05421902287390925, "std_dev": 0.005184115469410742}, "very_slow_stats": {"time_ratio": 0.11994834421623597, "count_ratio": 0.016129032258064516}, "very_slow_files": [{"fpath": "downloaded_repos/RaSan147_pyrobox/dev_src/pyroDB2.py", "ftime": 0.4032158851623535}]}, "scanning_time": {"total_time": 63.8861243724823, "per_file_time": {"mean": 0.2545263919222403, "std_dev": 1.0748240606535457}, "very_slow_stats": {"time_ratio": 0.766319246199888, "count_ratio": 0.043824701195219126}, "very_slow_files": [{"fpath": "downloaded_repos/RaSan147_pyrobox/dev_src/script_file_list.js", "ftime": 2.208514928817749}, {"fpath": "downloaded_repos/RaSan147_pyrobox/dev_src/script_file_list.bak.js", "ftime": 2.3175108432769775}, {"fpath": "downloaded_repos/RaSan147_pyrobox/dev_src/script_global.js", "ftime": 2.3516340255737305}, {"fpath": "downloaded_repos/RaSan147_pyrobox/dev_src/pyroDB2.py", "ftime": 2.9387309551239014}, {"fpath": "downloaded_repos/RaSan147_pyrobox/dev_src/server.py", "ftime": 4.074547052383423}, {"fpath": "downloaded_repos/RaSan147_pyrobox/src/server.py", "ftime": 4.496150970458984}, {"fpath": "downloaded_repos/RaSan147_pyrobox/src/pyroDB.py", "ftime": 5.581453800201416}, {"fpath": "downloaded_repos/RaSan147_pyrobox/src/pyroboxCore.py", "ftime": 7.214910984039307}, {"fpath": "downloaded_repos/RaSan147_pyrobox/dev_src/pyroboxCore.py", "ftime": 7.381358861923218}, {"fpath": "downloaded_repos/RaSan147_pyrobox/dev_src/local_server (py~3.4).py", "ftime": 8.759493112564087}]}, "matching_time": {"total_time": 38.517413854599, "per_file_and_rule_time": {"mean": 0.04159547932462094, "std_dev": 0.00887750004624474}, "very_slow_stats": {"time_ratio": 0.6873794623254257, "count_ratio": 0.11987041036717062}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/RaSan147_pyrobox/dev_src/local_server (py~3.4).py", "rule_id": "python.lang.security.dangerous-system-call.dangerous-system-call", "time": 0.44880199432373047}, {"fpath": "downloaded_repos/RaSan147_pyrobox/src/pyroDB.py", "rule_id": "python.lang.security.dangerous-system-call.dangerous-system-call", "time": 0.46866583824157715}, {"fpath": "downloaded_repos/RaSan147_pyrobox/dev_src/pyroboxCore.py", "rule_id": "python.lang.security.dangerous-system-call.dangerous-system-call", "time": 0.4781320095062256}, {"fpath": "downloaded_repos/RaSan147_pyrobox/src/server.py", "rule_id": "python.django.security.injection.command.subprocess-injection.subprocess-injection", "time": 0.4856231212615967}, {"fpath": "downloaded_repos/RaSan147_pyrobox/src/server.py", "rule_id": "python.lang.security.dangerous-system-call.dangerous-system-call", "time": 0.5680749416351318}, {"fpath": "downloaded_repos/RaSan147_pyrobox/src/pyroboxCore.py", "rule_id": "python.lang.security.dangerous-system-call.dangerous-system-call", "time": 0.586982011795044}, {"fpath": "downloaded_repos/RaSan147_pyrobox/dev_src/pyroboxCore.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.8024530410766602}, {"fpath": "downloaded_repos/RaSan147_pyrobox/src/pyroboxCore.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.8105628490447998}, {"fpath": "downloaded_repos/RaSan147_pyrobox/dev_src/local_server (py~3.4).py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.9508678913116455}, {"fpath": "downloaded_repos/RaSan147_pyrobox/dev_src/local_server (py~3.4).py", "rule_id": "python.django.security.injection.command.subprocess-injection.subprocess-injection", "time": 0.9951879978179932}]}, "tainting_time": {"total_time": 13.917006254196167, "per_def_and_rule_time": {"mean": 0.00098186865064175, "std_dev": 1.0912475953801263e-05}, "very_slow_stats": {"time_ratio": 0.03944928879189059, "count_ratio": 0.00028220685762664035}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/RaSan147_pyrobox/dev_src/local_server (py~3.4).py", "fline": 2138, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.07120084762573242}, {"fpath": "downloaded_repos/RaSan147_pyrobox/dev_src/_sub_extractor.py", "fline": 25, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.08623003959655762}, {"fpath": "downloaded_repos/RaSan147_pyrobox/src/_sub_extractor.py", "fline": 25, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.16552305221557617}, {"fpath": "downloaded_repos/RaSan147_pyrobox/dev_src/pyroDB2.py", "fline": 4169, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.22606205940246582}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}