{"version": "1.130.0", "results": [{"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/install.php", "start": {"line": 19, "col": 9, "offset": 604}, "end": {"line": 19, "col": 22, "offset": 617}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/install.php", "start": {"line": 187, "col": 13, "offset": 5840}, "end": {"line": 187, "col": 29, "offset": 5856}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/danpros_htmly/install.php", "start": {"line": 202, "col": 49, "offset": 6279}, "end": {"line": 202, "col": 80, "offset": 6310}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 135, "col": 31, "offset": 4386}, "end": {"line": 135, "col": 72, "offset": 4427}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 299, "col": 17, "offset": 10404}, "end": {"line": 299, "col": 35, "offset": 10422}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 337, "col": 31, "offset": 11691}, "end": {"line": 337, "col": 72, "offset": 11732}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 457, "col": 13, "offset": 16120}, "end": {"line": 457, "col": 29, "offset": 16136}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 492, "col": 17, "offset": 17693}, "end": {"line": 492, "col": 33, "offset": 17709}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 663, "col": 17, "offset": 23663}, "end": {"line": 663, "col": 35, "offset": 23681}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 759, "col": 17, "offset": 27011}, "end": {"line": 759, "col": 35, "offset": 27029}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 840, "col": 13, "offset": 29991}, "end": {"line": 840, "col": 29, "offset": 30007}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 850, "col": 13, "offset": 30482}, "end": {"line": 850, "col": 29, "offset": 30498}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 1149, "col": 13, "offset": 41066}, "end": {"line": 1149, "col": 37, "offset": 41090}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 1193, "col": 13, "offset": 42354}, "end": {"line": 1193, "col": 26, "offset": 42367}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 1201, "col": 13, "offset": 42564}, "end": {"line": 1201, "col": 37, "offset": 42588}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 1590, "col": 9, "offset": 55308}, "end": {"line": 1590, "col": 29, "offset": 55328}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 1684, "col": 18, "offset": 59310}, "end": {"line": 1684, "col": 59, "offset": 59351}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 1710, "col": 9, "offset": 60059}, "end": {"line": 1710, "col": 19, "offset": 60069}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 1716, "col": 9, "offset": 60206}, "end": {"line": 1716, "col": 20, "offset": 60217}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 1722, "col": 9, "offset": 60326}, "end": {"line": 1722, "col": 20, "offset": 60337}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 1725, "col": 9, "offset": 60429}, "end": {"line": 1725, "col": 22, "offset": 60442}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 1731, "col": 9, "offset": 60568}, "end": {"line": 1731, "col": 20, "offset": 60579}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 1734, "col": 9, "offset": 60692}, "end": {"line": 1734, "col": 22, "offset": 60705}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 1740, "col": 9, "offset": 60851}, "end": {"line": 1740, "col": 20, "offset": 60862}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 1743, "col": 9, "offset": 60989}, "end": {"line": 1743, "col": 22, "offset": 61002}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 1749, "col": 9, "offset": 61166}, "end": {"line": 1749, "col": 20, "offset": 61177}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 1752, "col": 9, "offset": 61318}, "end": {"line": 1752, "col": 22, "offset": 61331}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 1759, "col": 13, "offset": 61490}, "end": {"line": 1759, "col": 24, "offset": 61501}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 1762, "col": 13, "offset": 61621}, "end": {"line": 1762, "col": 26, "offset": 61634}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 1768, "col": 9, "offset": 61764}, "end": {"line": 1768, "col": 22, "offset": 61777}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 1774, "col": 9, "offset": 61912}, "end": {"line": 1774, "col": 20, "offset": 61923}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 1777, "col": 9, "offset": 62041}, "end": {"line": 1777, "col": 22, "offset": 62054}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 1783, "col": 9, "offset": 62177}, "end": {"line": 1783, "col": 20, "offset": 62188}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 1786, "col": 9, "offset": 62298}, "end": {"line": 1786, "col": 22, "offset": 62311}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 1795, "col": 9, "offset": 62560}, "end": {"line": 1795, "col": 19, "offset": 62570}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 1798, "col": 9, "offset": 62682}, "end": {"line": 1798, "col": 22, "offset": 62695}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 1807, "col": 9, "offset": 62873}, "end": {"line": 1807, "col": 19, "offset": 62883}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/admin.php", "start": {"line": 1814, "col": 9, "offset": 62992}, "end": {"line": 1814, "col": 22, "offset": 63005}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/danpros_htmly/system/admin/editor/js/Markdown.Converter.js", "start": {"line": 940, "col": 22, "offset": 35564}, "end": {"line": 940, "col": 126, "offset": 35668}, "extra": {"message": "RegExp() called with a `list_type` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/danpros_htmly/system/admin/editor/js/Markdown.Converter.js", "start": {"line": 1414, "col": 25, "offset": 53122}, "end": {"line": 1414, "col": 53, "offset": 53150}, "extra": {"message": "RegExp() called with a `charsToEscape` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/danpros_htmly/system/admin/editor/js/Markdown.Editor.js", "start": {"line": 991, "col": 13, "offset": 33558}, "end": {"line": 991, "col": 38, "offset": 33583}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/danpros_htmly/system/admin/editor/js/Markdown.Editor.js", "start": {"line": 999, "col": 13, "offset": 33808}, "end": {"line": 999, "col": 45, "offset": 33840}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/danpros_htmly/system/admin/editor/js/Markdown.Editor.js", "start": {"line": 1163, "col": 13, "offset": 38737}, "end": {"line": 1163, "col": 39, "offset": 38763}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/danpros_htmly/system/admin/views/add-content.html.php", "start": {"line": 16, "col": 14, "offset": 423}, "end": {"line": 16, "col": 55, "offset": 464}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/views/backup.html.php", "start": {"line": 8, "col": 13, "offset": 228}, "end": {"line": 8, "col": 35, "offset": 250}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/danpros_htmly/system/admin/views/backup.html.php", "start": {"line": 8, "col": 20, "offset": 235}, "end": {"line": 8, "col": 34, "offset": 249}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/views/clear-cache.html.php", "start": {"line": 5, "col": 5, "offset": 117}, "end": {"line": 5, "col": 18, "offset": 130}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/admin/views/clear-cache.html.php", "start": {"line": 11, "col": 5, "offset": 223}, "end": {"line": 11, "col": 18, "offset": 236}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/danpros_htmly/system/admin/views/edit-content.html.php", "start": {"line": 61, "col": 14, "offset": 1683}, "end": {"line": 61, "col": 55, "offset": 1724}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/htmly.php", "start": {"line": 1921, "col": 17, "offset": 73555}, "end": {"line": 1921, "col": 30, "offset": 73568}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/danpros_htmly/system/htmly.php", "start": {"line": 1921, "col": 24, "offset": 73562}, "end": {"line": 1921, "col": 29, "offset": 73567}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/htmly.php", "start": {"line": 2014, "col": 17, "offset": 77123}, "end": {"line": 2014, "col": 30, "offset": 77136}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/htmly.php", "start": {"line": 2299, "col": 17, "offset": 87648}, "end": {"line": 2299, "col": 30, "offset": 87661}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/htmly.php", "start": {"line": 2373, "col": 17, "offset": 90384}, "end": {"line": 2373, "col": 30, "offset": 90397}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/danpros_htmly/system/htmly.php", "start": {"line": 2915, "col": 29, "offset": 112044}, "end": {"line": 2915, "col": 34, "offset": 112049}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/danpros_htmly/system/htmly.php", "start": {"line": 2917, "col": 39, "offset": 112136}, "end": {"line": 2917, "col": 44, "offset": 112141}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/htmly.php", "start": {"line": 2981, "col": 17, "offset": 114674}, "end": {"line": 2981, "col": 30, "offset": 114687}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/danpros_htmly/system/htmly.php", "start": {"line": 2981, "col": 24, "offset": 114681}, "end": {"line": 2981, "col": 29, "offset": 114686}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.mcrypt-use.mcrypt-use", "path": "downloaded_repos/danpros_htmly/system/includes/dispatch.php", "start": {"line": 169, "col": 9, "offset": 4680}, "end": {"line": 169, "col": 76, "offset": 4747}, "extra": {"message": "Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL.", "metadata": {"cwe": ["CWE-676: Use of Potentially Dangerous Function"], "references": ["https://www.php.net/manual/en/intro.mcrypt.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/CryptoFunctionsSniff.php"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Dangerous Method or Function"], "source": "https://semgrep.dev/r/php.lang.security.mcrypt-use.mcrypt-use", "shortlink": "https://sg.run/BkZR"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.mcrypt-use.mcrypt-use", "path": "downloaded_repos/danpros_htmly/system/includes/dispatch.php", "start": {"line": 170, "col": 9, "offset": 4756}, "end": {"line": 170, "col": 53, "offset": 4800}, "extra": {"message": "Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL.", "metadata": {"cwe": ["CWE-676: Use of Potentially Dangerous Function"], "references": ["https://www.php.net/manual/en/intro.mcrypt.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/CryptoFunctionsSniff.php"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Dangerous Method or Function"], "source": "https://semgrep.dev/r/php.lang.security.mcrypt-use.mcrypt-use", "shortlink": "https://sg.run/BkZR"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.mcrypt-use.mcrypt-use", "path": "downloaded_repos/danpros_htmly/system/includes/dispatch.php", "start": {"line": 171, "col": 9, "offset": 4809}, "end": {"line": 171, "col": 67, "offset": 4867}, "extra": {"message": "Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL.", "metadata": {"cwe": ["CWE-676: Use of Potentially Dangerous Function"], "references": ["https://www.php.net/manual/en/intro.mcrypt.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/CryptoFunctionsSniff.php"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Dangerous Method or Function"], "source": "https://semgrep.dev/r/php.lang.security.mcrypt-use.mcrypt-use", "shortlink": "https://sg.run/BkZR"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.mcrypt-use.mcrypt-use", "path": "downloaded_repos/danpros_htmly/system/includes/dispatch.php", "start": {"line": 172, "col": 9, "offset": 4876}, "end": {"line": 172, "col": 88, "offset": 4955}, "extra": {"message": "Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL.", "metadata": {"cwe": ["CWE-676: Use of Potentially Dangerous Function"], "references": ["https://www.php.net/manual/en/intro.mcrypt.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/CryptoFunctionsSniff.php"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Dangerous Method or Function"], "source": "https://semgrep.dev/r/php.lang.security.mcrypt-use.mcrypt-use", "shortlink": "https://sg.run/BkZR"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.mcrypt-use.mcrypt-use", "path": "downloaded_repos/danpros_htmly/system/includes/dispatch.php", "start": {"line": 182, "col": 9, "offset": 5240}, "end": {"line": 182, "col": 76, "offset": 5307}, "extra": {"message": "Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL.", "metadata": {"cwe": ["CWE-676: Use of Potentially Dangerous Function"], "references": ["https://www.php.net/manual/en/intro.mcrypt.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/CryptoFunctionsSniff.php"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Dangerous Method or Function"], "source": "https://semgrep.dev/r/php.lang.security.mcrypt-use.mcrypt-use", "shortlink": "https://sg.run/BkZR"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.mcrypt-use.mcrypt-use", "path": "downloaded_repos/danpros_htmly/system/includes/dispatch.php", "start": {"line": 186, "col": 9, "offset": 5453}, "end": {"line": 186, "col": 78, "offset": 5522}, "extra": {"message": "Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL.", "metadata": {"cwe": ["CWE-676: Use of Potentially Dangerous Function"], "references": ["https://www.php.net/manual/en/intro.mcrypt.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/CryptoFunctionsSniff.php"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Dangerous Method or Function"], "source": "https://semgrep.dev/r/php.lang.security.mcrypt-use.mcrypt-use", "shortlink": "https://sg.run/BkZR"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/danpros_htmly/system/includes/dispatch.php", "start": {"line": 428, "col": 35, "offset": 11480}, "end": {"line": 428, "col": 45, "offset": 11490}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/danpros_htmly/system/includes/functions.php", "start": {"line": 18, "col": 15, "offset": 403}, "end": {"line": 18, "col": 51, "offset": 439}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/danpros_htmly/system/includes/functions.php", "start": {"line": 31, "col": 14, "offset": 670}, "end": {"line": 31, "col": 50, "offset": 706}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/danpros_htmly/system/includes/functions.php", "start": {"line": 44, "col": 18, "offset": 967}, "end": {"line": 44, "col": 54, "offset": 1003}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/danpros_htmly/system/includes/functions.php", "start": {"line": 74, "col": 16, "offset": 1741}, "end": {"line": 74, "col": 52, "offset": 1777}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/danpros_htmly/system/includes/functions.php", "start": {"line": 88, "col": 15, "offset": 2006}, "end": {"line": 88, "col": 51, "offset": 2042}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/danpros_htmly/system/includes/functions.php", "start": {"line": 152, "col": 19, "offset": 3532}, "end": {"line": 152, "col": 55, "offset": 3568}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/danpros_htmly/system/includes/functions.php", "start": {"line": 166, "col": 14, "offset": 3818}, "end": {"line": 166, "col": 50, "offset": 3854}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/danpros_htmly/system/includes/functions.php", "start": {"line": 196, "col": 15, "offset": 4383}, "end": {"line": 196, "col": 51, "offset": 4419}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/includes/functions.php", "start": {"line": 367, "col": 9, "offset": 9573}, "end": {"line": 367, "col": 22, "offset": 9586}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/danpros_htmly/system/includes/functions.php", "start": {"line": 1050, "col": 16, "offset": 30413}, "end": {"line": 1050, "col": 57, "offset": 30454}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/danpros_htmly/system/includes/functions.php", "start": {"line": 1656, "col": 18, "offset": 44768}, "end": {"line": 1656, "col": 59, "offset": 44809}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/danpros_htmly/system/includes/functions.php", "start": {"line": 1773, "col": 34, "offset": 48812}, "end": {"line": 1773, "col": 76, "offset": 48854}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/danpros_htmly/system/includes/functions.php", "start": {"line": 1871, "col": 24, "offset": 51926}, "end": {"line": 1871, "col": 65, "offset": 51967}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/danpros_htmly/system/includes/functions.php", "start": {"line": 1884, "col": 25, "offset": 52275}, "end": {"line": 1884, "col": 65, "offset": 52315}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/danpros_htmly/system/includes/functions.php", "start": {"line": 1929, "col": 25, "offset": 54473}, "end": {"line": 1929, "col": 66, "offset": 54514}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/danpros_htmly/system/includes/functions.php", "start": {"line": 1948, "col": 25, "offset": 55422}, "end": {"line": 1948, "col": 65, "offset": 55462}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/danpros_htmly/system/includes/functions.php", "start": {"line": 2002, "col": 31, "offset": 57121}, "end": {"line": 2002, "col": 72, "offset": 57162}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/danpros_htmly/system/includes/functions.php", "start": {"line": 2009, "col": 28, "offset": 57357}, "end": {"line": 2009, "col": 68, "offset": 57397}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/danpros_htmly/system/includes/functions.php", "start": {"line": 3680, "col": 13, "offset": 115863}, "end": {"line": 3680, "col": 31, "offset": 115881}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/danpros_htmly/system/includes/functions.php", "start": {"line": 3775, "col": 39, "offset": 118152}, "end": {"line": 3775, "col": 78, "offset": 118191}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/danpros_htmly/system/includes/functions.php", "start": {"line": 3851, "col": 17, "offset": 120369}, "end": {"line": 3851, "col": 58, "offset": 120410}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/danpros_htmly/system/resources/js/form.builder.js", "start": {"line": 190, "col": 5, "offset": 7012}, "end": {"line": 190, "col": 140, "offset": 7147}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/danpros_htmly/system/resources/js/form.builder.js", "start": {"line": 207, "col": 9, "offset": 7681}, "end": {"line": 207, "col": 92, "offset": 7764}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/danpros_htmly/system/resources/js/form.builder.js", "start": {"line": 250, "col": 13, "offset": 9362}, "end": {"line": 250, "col": 86, "offset": 9435}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/danpros_htmly/themes/twentyfifteen/js/jquery.js", "start": {"line": 2, "col": 13987, "offset": 14069}, "end": {"line": 2, "col": 14024, "offset": 14106}, "extra": {"message": "RegExp() called with a `a` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/danpros_htmly/themes/twentyfifteen/js/jquery.js", "start": {"line": 2, "col": 14789, "offset": 14871}, "end": {"line": 2, "col": 14795, "offset": 14877}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/danpros_htmly/themes/twentyfifteen/js/jquery.js", "start": {"line": 2, "col": 25677, "offset": 25759}, "end": {"line": 2, "col": 25685, "offset": 25767}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/danpros_htmly/themes/twentysixteen/js/html5.js", "start": {"line": 71, "col": 5, "offset": 2398}, "end": {"line": 71, "col": 53, "offset": 2446}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-eval.tainted-eval", "message": "Timeout when running javascript.aws-lambda.security.tainted-eval.tainted-eval on downloaded_repos/danpros_htmly/themes/twentysixteen/js/jquery.js:\n ", "path": "downloaded_repos/danpros_htmly/themes/twentysixteen/js/jquery.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-response.tainted-html-response", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-response.tainted-html-response on downloaded_repos/danpros_htmly/themes/twentysixteen/js/jquery.js:\n ", "path": "downloaded_repos/danpros_htmly/themes/twentysixteen/js/jquery.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/danpros_htmly/themes/twentysixteen/js/jquery.js:\n ", "path": "downloaded_repos/danpros_htmly/themes/twentysixteen/js/jquery.js"}], "paths": {"scanned": ["downloaded_repos/danpros_htmly/.gitattributes", "downloaded_repos/danpros_htmly/.github/FUNDING.yml", "downloaded_repos/danpros_htmly/.github/ISSUE_TEMPLATE/bug_report.md", "downloaded_repos/danpros_htmly/.github/ISSUE_TEMPLATE/feature_request.md", "downloaded_repos/danpros_htmly/.gitignore", "downloaded_repos/danpros_htmly/.htaccess", "downloaded_repos/danpros_htmly/.updateignore", "downloaded_repos/danpros_htmly/COPYRIGHT.txt", "downloaded_repos/danpros_htmly/LICENSE.txt", "downloaded_repos/danpros_htmly/README.md", "downloaded_repos/danpros_htmly/SECURITY.md", "downloaded_repos/danpros_htmly/cache/installedVersion.json", "downloaded_repos/danpros_htmly/composer.json", "downloaded_repos/danpros_htmly/composer.lock", "downloaded_repos/danpros_htmly/config/.htaccess", "downloaded_repos/danpros_htmly/config/config.ini.example", "downloaded_repos/danpros_htmly/config/users/username.ini.example", "downloaded_repos/danpros_htmly/content/.gitkeep", "downloaded_repos/danpros_htmly/favicon.png", "downloaded_repos/danpros_htmly/humans.txt", "downloaded_repos/danpros_htmly/index.php", "downloaded_repos/danpros_htmly/install.php", "downloaded_repos/danpros_htmly/lang/ar_AR.ini", "downloaded_repos/danpros_htmly/lang/be_BY.ini", "downloaded_repos/danpros_htmly/lang/cs_CZ.ini", "downloaded_repos/danpros_htmly/lang/da_DK.ini", "downloaded_repos/danpros_htmly/lang/de_DE.ini", "downloaded_repos/danpros_htmly/lang/de_DE_gender_doppelpunkt.ini", "downloaded_repos/danpros_htmly/lang/de_DE_gender_dudenkonform.ini", "downloaded_repos/danpros_htmly/lang/el_GR.ini", "downloaded_repos/danpros_htmly/lang/en_US.ini", "downloaded_repos/danpros_htmly/lang/eo_EO.ini", "downloaded_repos/danpros_htmly/lang/es_ES.ini", "downloaded_repos/danpros_htmly/lang/fa_IR.ini", "downloaded_repos/danpros_htmly/lang/fr_FR.ini", "downloaded_repos/danpros_htmly/lang/gl_ES.ini", "downloaded_repos/danpros_htmly/lang/gu_GU.ini", "downloaded_repos/danpros_htmly/lang/hi_HI.ini", "downloaded_repos/danpros_htmly/lang/hr_HR.ini", "downloaded_repos/danpros_htmly/lang/hu_HU.ini", "downloaded_repos/danpros_htmly/lang/id_ID.ini", "downloaded_repos/danpros_htmly/lang/it_IT.ini", "downloaded_repos/danpros_htmly/lang/ko_KO.ini", "downloaded_repos/danpros_htmly/lang/ku_KU.ini", "downloaded_repos/danpros_htmly/lang/ms_MY.ini", "downloaded_repos/danpros_htmly/lang/nl_NL.ini", "downloaded_repos/danpros_htmly/lang/pl_PL.ini", "downloaded_repos/danpros_htmly/lang/pt_BR.ini", "downloaded_repos/danpros_htmly/lang/ru_RU.ini", "downloaded_repos/danpros_htmly/lang/sv_SE.ini", "downloaded_repos/danpros_htmly/lang/tr_TR.ini", "downloaded_repos/danpros_htmly/lang/uk_UA.ini", "downloaded_repos/danpros_htmly/lang/zh_CN.ini", "downloaded_repos/danpros_htmly/lang/zh_TW.ini", "downloaded_repos/danpros_htmly/robots.txt", "downloaded_repos/danpros_htmly/system/admin/admin.php", "downloaded_repos/danpros_htmly/system/admin/editor/LICENSE.txt", "downloaded_repos/danpros_htmly/system/admin/editor/css/editor.css", "downloaded_repos/danpros_htmly/system/admin/editor/js/Markdown.Converter.js", "downloaded_repos/danpros_htmly/system/admin/editor/js/Markdown.Editor.js", "downloaded_repos/danpros_htmly/system/admin/editor/js/Markdown.Extra.js", "downloaded_repos/danpros_htmly/system/admin/editor/js/Markdown.Sanitizer.js", "downloaded_repos/danpros_htmly/system/admin/editor/js/editor.js", "downloaded_repos/danpros_htmly/system/admin/editor/js/local/Markdown.local.fr.js", "downloaded_repos/danpros_htmly/system/admin/editor/js/node-pagedown-extra.js", "downloaded_repos/danpros_htmly/system/admin/editor/js/node-pagedown.js", "downloaded_repos/danpros_htmly/system/admin/editor/package.json", "downloaded_repos/danpros_htmly/system/admin/views/.htaccess", "downloaded_repos/danpros_htmly/system/admin/views/404.html.php", "downloaded_repos/danpros_htmly/system/admin/views/add-content.html.php", "downloaded_repos/danpros_htmly/system/admin/views/add-page.html.php", "downloaded_repos/danpros_htmly/system/admin/views/add-user.html.php", "downloaded_repos/danpros_htmly/system/admin/views/backup-start.html.php", "downloaded_repos/danpros_htmly/system/admin/views/backup.html.php", "downloaded_repos/danpros_htmly/system/admin/views/categories.html.php", "downloaded_repos/danpros_htmly/system/admin/views/category-list.html.php", "downloaded_repos/danpros_htmly/system/admin/views/clear-cache.html.php", "downloaded_repos/danpros_htmly/system/admin/views/config-custom.html.php", "downloaded_repos/danpros_htmly/system/admin/views/config-metatags.html.php", "downloaded_repos/danpros_htmly/system/admin/views/config-performance.html.php", "downloaded_repos/danpros_htmly/system/admin/views/config-reading.html.php", "downloaded_repos/danpros_htmly/system/admin/views/config-security.html.php", "downloaded_repos/danpros_htmly/system/admin/views/config-widget.html.php", "downloaded_repos/danpros_htmly/system/admin/views/config-writing.html.php", "downloaded_repos/danpros_htmly/system/admin/views/config.html.php", "downloaded_repos/danpros_htmly/system/admin/views/content-type.html.php", "downloaded_repos/danpros_htmly/system/admin/views/custom-field-page.html.php", "downloaded_repos/danpros_htmly/system/admin/views/custom-field-post.html.php", "downloaded_repos/danpros_htmly/system/admin/views/custom-field-profile.html.php", "downloaded_repos/danpros_htmly/system/admin/views/custom-field-subpage.html.php", "downloaded_repos/danpros_htmly/system/admin/views/custom-field.html.php", "downloaded_repos/danpros_htmly/system/admin/views/delete-category.html.php", "downloaded_repos/danpros_htmly/system/admin/views/delete-page.html.php", "downloaded_repos/danpros_htmly/system/admin/views/delete-post.html.php", "downloaded_repos/danpros_htmly/system/admin/views/delete-user.html.php", "downloaded_repos/danpros_htmly/system/admin/views/denied.html.php", "downloaded_repos/danpros_htmly/system/admin/views/edit-content.html.php", "downloaded_repos/danpros_htmly/system/admin/views/edit-mfa.html.php", "downloaded_repos/danpros_htmly/system/admin/views/edit-page.html.php", "downloaded_repos/danpros_htmly/system/admin/views/edit-password.html.php", "downloaded_repos/danpros_htmly/system/admin/views/edit-user.html.php", "downloaded_repos/danpros_htmly/system/admin/views/import.html.php", "downloaded_repos/danpros_htmly/system/admin/views/layout.html.php", "downloaded_repos/danpros_htmly/system/admin/views/login-mfa.html.php", "downloaded_repos/danpros_htmly/system/admin/views/login.html.php", "downloaded_repos/danpros_htmly/system/admin/views/logout.html.php", "downloaded_repos/danpros_htmly/system/admin/views/main.html.php", "downloaded_repos/danpros_htmly/system/admin/views/menu.html.php", "downloaded_repos/danpros_htmly/system/admin/views/no-posts.html.php", "downloaded_repos/danpros_htmly/system/admin/views/popular-posts.html.php", "downloaded_repos/danpros_htmly/system/admin/views/posts-list.html.php", "downloaded_repos/danpros_htmly/system/admin/views/scheduled.html.php", "downloaded_repos/danpros_htmly/system/admin/views/search-reindex.html.php", "downloaded_repos/danpros_htmly/system/admin/views/search.html.php", "downloaded_repos/danpros_htmly/system/admin/views/static-pages.html.php", "downloaded_repos/danpros_htmly/system/admin/views/static-subpages.html.php", "downloaded_repos/danpros_htmly/system/admin/views/update.html.php", "downloaded_repos/danpros_htmly/system/admin/views/updated-to.html.php", "downloaded_repos/danpros_htmly/system/admin/views/user-draft.html.php", "downloaded_repos/danpros_htmly/system/admin/views/user-posts.html.php", "downloaded_repos/danpros_htmly/system/admin/views/users.html.php", "downloaded_repos/danpros_htmly/system/configList.json", "downloaded_repos/danpros_htmly/system/htmly.php", "downloaded_repos/danpros_htmly/system/includes/.htaccess", "downloaded_repos/danpros_htmly/system/includes/dispatch.php", "downloaded_repos/danpros_htmly/system/includes/functions.php", "downloaded_repos/danpros_htmly/system/includes/opml.php", "downloaded_repos/danpros_htmly/system/includes/session.php", "downloaded_repos/danpros_htmly/system/resources/css/fontawesome.min.css", "downloaded_repos/danpros_htmly/system/resources/css/jquery-ui.css", "downloaded_repos/danpros_htmly/system/resources/css/social-logos.css", "downloaded_repos/danpros_htmly/system/resources/css/social-logos.ttf", "downloaded_repos/danpros_htmly/system/resources/css/social-logos.woff2", "downloaded_repos/danpros_htmly/system/resources/css/solid.min.css", "downloaded_repos/danpros_htmly/system/resources/css/toc.css", "downloaded_repos/danpros_htmly/system/resources/css/toolbar.css", "downloaded_repos/danpros_htmly/system/resources/fonts/glyphicons-halflings-regular.eot", "downloaded_repos/danpros_htmly/system/resources/fonts/glyphicons-halflings-regular.svg", "downloaded_repos/danpros_htmly/system/resources/fonts/glyphicons-halflings-regular.ttf", "downloaded_repos/danpros_htmly/system/resources/fonts/glyphicons-halflings-regular.woff", "downloaded_repos/danpros_htmly/system/resources/images/htmly-gray-bg.png", "downloaded_repos/danpros_htmly/system/resources/images/htmly-small.png", "downloaded_repos/danpros_htmly/system/resources/images/logo-big.png", "downloaded_repos/danpros_htmly/system/resources/images/logo-small.png", "downloaded_repos/danpros_htmly/system/resources/images/logo.png", "downloaded_repos/danpros_htmly/system/resources/images/share-facebook.png", "downloaded_repos/danpros_htmly/system/resources/images/share-twitter.png", "downloaded_repos/danpros_htmly/system/resources/js/form.builder.js", "downloaded_repos/danpros_htmly/system/resources/js/jquery.nestable++.js", "downloaded_repos/danpros_htmly/system/resources/js/jquery.nestable.js", "downloaded_repos/danpros_htmly/system/resources/js/media.uploader.js", "downloaded_repos/danpros_htmly/system/resources/js/save_draft.js", "downloaded_repos/danpros_htmly/system/resources/js/toc.generator.js", "downloaded_repos/danpros_htmly/system/resources/js/toolbar.js", "downloaded_repos/danpros_htmly/system/resources/readme.txt", "downloaded_repos/danpros_htmly/system/resources/webfonts/fa-solid-900.ttf", "downloaded_repos/danpros_htmly/system/resources/webfonts/fa-solid-900.woff2", "downloaded_repos/danpros_htmly/themes/blog/404-search.html.php", "downloaded_repos/danpros_htmly/themes/blog/404.html.php", "downloaded_repos/danpros_htmly/themes/blog/README.md", "downloaded_repos/danpros_htmly/themes/blog/css/bootstrap-theme.min.css", "downloaded_repos/danpros_htmly/themes/blog/css/bootstrap.min.css", "downloaded_repos/danpros_htmly/themes/blog/css/font-awesome.min.css", "downloaded_repos/danpros_htmly/themes/blog/css/styles.css", "downloaded_repos/danpros_htmly/themes/blog/fonts/FontAwesome.otf", "downloaded_repos/danpros_htmly/themes/blog/fonts/fontawesome-webfont.eot", "downloaded_repos/danpros_htmly/themes/blog/fonts/fontawesome-webfont.svg", "downloaded_repos/danpros_htmly/themes/blog/fonts/fontawesome-webfont.ttf", "downloaded_repos/danpros_htmly/themes/blog/fonts/fontawesome-webfont.woff", "downloaded_repos/danpros_htmly/themes/blog/fonts/fontawesome-webfont.woff2", "downloaded_repos/danpros_htmly/themes/blog/fonts/glyphicons-halflings-regular.eot", "downloaded_repos/danpros_htmly/themes/blog/fonts/glyphicons-halflings-regular.svg", "downloaded_repos/danpros_htmly/themes/blog/fonts/glyphicons-halflings-regular.ttf", "downloaded_repos/danpros_htmly/themes/blog/fonts/glyphicons-halflings-regular.woff", "downloaded_repos/danpros_htmly/themes/blog/images/logo.png", "downloaded_repos/danpros_htmly/themes/blog/layout.html.php", "downloaded_repos/danpros_htmly/themes/blog/main.html.php", "downloaded_repos/danpros_htmly/themes/blog/no-posts.html.php", "downloaded_repos/danpros_htmly/themes/blog/post.html.php", "downloaded_repos/danpros_htmly/themes/blog/profile.html.php", "downloaded_repos/danpros_htmly/themes/blog/static.html.php", "downloaded_repos/danpros_htmly/themes/clean/404-search.html.php", "downloaded_repos/danpros_htmly/themes/clean/404.html.php", "downloaded_repos/danpros_htmly/themes/clean/css/style.css", "downloaded_repos/danpros_htmly/themes/clean/layout.html.php", "downloaded_repos/danpros_htmly/themes/clean/main.html.php", "downloaded_repos/danpros_htmly/themes/clean/no-posts.html.php", "downloaded_repos/danpros_htmly/themes/clean/post.html.php", "downloaded_repos/danpros_htmly/themes/clean/profile.html.php", "downloaded_repos/danpros_htmly/themes/clean/static.html.php", "downloaded_repos/danpros_htmly/themes/doks/404-search.html.php", "downloaded_repos/danpros_htmly/themes/doks/404.html.php", "downloaded_repos/danpros_htmly/themes/doks/LICENSE.txt", "downloaded_repos/danpros_htmly/themes/doks/README.md", "downloaded_repos/danpros_htmly/themes/doks/css/style.css", "downloaded_repos/danpros_htmly/themes/doks/fonts/jost/jost-v4-latin-500.woff", "downloaded_repos/danpros_htmly/themes/doks/fonts/jost/jost-v4-latin-500.woff2", "downloaded_repos/danpros_htmly/themes/doks/fonts/jost/jost-v4-latin-500italic.woff", "downloaded_repos/danpros_htmly/themes/doks/fonts/jost/jost-v4-latin-500italic.woff2", "downloaded_repos/danpros_htmly/themes/doks/fonts/jost/jost-v4-latin-700.woff", "downloaded_repos/danpros_htmly/themes/doks/fonts/jost/jost-v4-latin-700.woff2", "downloaded_repos/danpros_htmly/themes/doks/fonts/jost/jost-v4-latin-700italic.woff", "downloaded_repos/danpros_htmly/themes/doks/fonts/jost/jost-v4-latin-700italic.woff2", "downloaded_repos/danpros_htmly/themes/doks/fonts/jost/jost-v4-latin-italic.woff", "downloaded_repos/danpros_htmly/themes/doks/fonts/jost/jost-v4-latin-italic.woff2", "downloaded_repos/danpros_htmly/themes/doks/fonts/jost/jost-v4-latin-regular.woff", "downloaded_repos/danpros_htmly/themes/doks/fonts/jost/jost-v4-latin-regular.woff2", "downloaded_repos/danpros_htmly/themes/doks/img/soundcloud.jpg", "downloaded_repos/danpros_htmly/themes/doks/js/main.js", "downloaded_repos/danpros_htmly/themes/doks/js/toc.js", "downloaded_repos/danpros_htmly/themes/doks/layout--static.html.php", "downloaded_repos/danpros_htmly/themes/doks/layout.html.php", "downloaded_repos/danpros_htmly/themes/doks/main.html.php", "downloaded_repos/danpros_htmly/themes/doks/no-posts.html.php", "downloaded_repos/danpros_htmly/themes/doks/post.html.php", "downloaded_repos/danpros_htmly/themes/doks/profile.html.php", "downloaded_repos/danpros_htmly/themes/doks/static.html.php", "downloaded_repos/danpros_htmly/themes/logs/404-search.html.php", "downloaded_repos/danpros_htmly/themes/logs/404.html.php", "downloaded_repos/danpros_htmly/themes/logs/css/style.css", "downloaded_repos/danpros_htmly/themes/logs/layout.html.php", "downloaded_repos/danpros_htmly/themes/logs/main.html.php", "downloaded_repos/danpros_htmly/themes/logs/no-posts.html.php", "downloaded_repos/danpros_htmly/themes/logs/post.html.php", "downloaded_repos/danpros_htmly/themes/logs/profile.html.php", "downloaded_repos/danpros_htmly/themes/logs/static.html.php", "downloaded_repos/danpros_htmly/themes/readable/404-search.html.php", "downloaded_repos/danpros_htmly/themes/readable/404.html.php", "downloaded_repos/danpros_htmly/themes/readable/css/style.css", "downloaded_repos/danpros_htmly/themes/readable/img/facebook.png", "downloaded_repos/danpros_htmly/themes/readable/img/googleplus.png", "downloaded_repos/danpros_htmly/themes/readable/img/rss.png", "downloaded_repos/danpros_htmly/themes/readable/img/share-facebook.png", "downloaded_repos/danpros_htmly/themes/readable/img/share-googleplus.png", "downloaded_repos/danpros_htmly/themes/readable/img/share-twitter.png", "downloaded_repos/danpros_htmly/themes/readable/img/tumblr.png", "downloaded_repos/danpros_htmly/themes/readable/img/twitter.png", "downloaded_repos/danpros_htmly/themes/readable/layout.html.php", "downloaded_repos/danpros_htmly/themes/readable/main.html.php", "downloaded_repos/danpros_htmly/themes/readable/no-posts.html.php", "downloaded_repos/danpros_htmly/themes/readable/post.html.php", "downloaded_repos/danpros_htmly/themes/readable/profile.html.php", "downloaded_repos/danpros_htmly/themes/readable/static.html.php", "downloaded_repos/danpros_htmly/themes/readme.txt", "downloaded_repos/danpros_htmly/themes/tailwind/404-search.html.php", "downloaded_repos/danpros_htmly/themes/tailwind/404.html.php", "downloaded_repos/danpros_htmly/themes/tailwind/LICENSE.txt", "downloaded_repos/danpros_htmly/themes/tailwind/README.md", "downloaded_repos/danpros_htmly/themes/tailwind/css/style.css", "downloaded_repos/danpros_htmly/themes/tailwind/css/tailwind.css", "downloaded_repos/danpros_htmly/themes/tailwind/css/typography.css", "downloaded_repos/danpros_htmly/themes/tailwind/fonts/2d141e1a38819612-s.p.woff2", "downloaded_repos/danpros_htmly/themes/tailwind/fonts/62328fecf9e80426-s.woff2", "downloaded_repos/danpros_htmly/themes/tailwind/fonts/LICENSE.txt", "downloaded_repos/danpros_htmly/themes/tailwind/fonts/c7eb187887c48af6-s.woff2", "downloaded_repos/danpros_htmly/themes/tailwind/img/soundcloud.jpg", "downloaded_repos/danpros_htmly/themes/tailwind/js/functions.js", "downloaded_repos/danpros_htmly/themes/tailwind/layout.html.php", "downloaded_repos/danpros_htmly/themes/tailwind/logo.png", "downloaded_repos/danpros_htmly/themes/tailwind/main.html.php", "downloaded_repos/danpros_htmly/themes/tailwind/no-posts.html.php", "downloaded_repos/danpros_htmly/themes/tailwind/post.html.php", "downloaded_repos/danpros_htmly/themes/tailwind/profile.html.php", "downloaded_repos/danpros_htmly/themes/tailwind/static--front.html.php", "downloaded_repos/danpros_htmly/themes/tailwind/static.html.php", "downloaded_repos/danpros_htmly/themes/twentyfifteen/404-search.html.php", "downloaded_repos/danpros_htmly/themes/twentyfifteen/404.html.php", "downloaded_repos/danpros_htmly/themes/twentyfifteen/LICENSE.txt", "downloaded_repos/danpros_htmly/themes/twentyfifteen/README.md", "downloaded_repos/danpros_htmly/themes/twentyfifteen/css/font.css", "downloaded_repos/danpros_htmly/themes/twentyfifteen/css/ie.css", "downloaded_repos/danpros_htmly/themes/twentyfifteen/css/ie7.css", "downloaded_repos/danpros_htmly/themes/twentyfifteen/css/style.css", "downloaded_repos/danpros_htmly/themes/twentyfifteen/css/style_v2.css", "downloaded_repos/danpros_htmly/themes/twentyfifteen/genericons/COPYING.txt", "downloaded_repos/danpros_htmly/themes/twentyfifteen/genericons/Genericons.eot", "downloaded_repos/danpros_htmly/themes/twentyfifteen/genericons/Genericons.svg", "downloaded_repos/danpros_htmly/themes/twentyfifteen/genericons/Genericons.ttf", "downloaded_repos/danpros_htmly/themes/twentyfifteen/genericons/Genericons.woff", "downloaded_repos/danpros_htmly/themes/twentyfifteen/genericons/LICENSE.txt", "downloaded_repos/danpros_htmly/themes/twentyfifteen/genericons/README.md", "downloaded_repos/danpros_htmly/themes/twentyfifteen/genericons/genericons.css", "downloaded_repos/danpros_htmly/themes/twentyfifteen/images/avatar.png", "downloaded_repos/danpros_htmly/themes/twentyfifteen/js/functions.js", "downloaded_repos/danpros_htmly/themes/twentyfifteen/js/html5.js", "downloaded_repos/danpros_htmly/themes/twentyfifteen/js/jquery-migrate.js", "downloaded_repos/danpros_htmly/themes/twentyfifteen/js/jquery.js", "downloaded_repos/danpros_htmly/themes/twentyfifteen/js/skip-link-focus-fix.js", "downloaded_repos/danpros_htmly/themes/twentyfifteen/layout.html.php", "downloaded_repos/danpros_htmly/themes/twentyfifteen/main.html.php", "downloaded_repos/danpros_htmly/themes/twentyfifteen/no-posts.html.php", "downloaded_repos/danpros_htmly/themes/twentyfifteen/post.html.php", "downloaded_repos/danpros_htmly/themes/twentyfifteen/profile.html.php", "downloaded_repos/danpros_htmly/themes/twentyfifteen/static.html.php", "downloaded_repos/danpros_htmly/themes/twentysixteen/404-search.html.php", "downloaded_repos/danpros_htmly/themes/twentysixteen/404.html.php", "downloaded_repos/danpros_htmly/themes/twentysixteen/LICENSE.txt", "downloaded_repos/danpros_htmly/themes/twentysixteen/README.md", "downloaded_repos/danpros_htmly/themes/twentysixteen/css/ie.css", "downloaded_repos/danpros_htmly/themes/twentysixteen/css/ie7.css", "downloaded_repos/danpros_htmly/themes/twentysixteen/css/ie8.css", "downloaded_repos/danpros_htmly/themes/twentysixteen/css/rtl.css", "downloaded_repos/danpros_htmly/themes/twentysixteen/css/style.css", "downloaded_repos/danpros_htmly/themes/twentysixteen/genericons/COPYING.txt", "downloaded_repos/danpros_htmly/themes/twentysixteen/genericons/Genericons.eot", "downloaded_repos/danpros_htmly/themes/twentysixteen/genericons/Genericons.svg", "downloaded_repos/danpros_htmly/themes/twentysixteen/genericons/Genericons.ttf", "downloaded_repos/danpros_htmly/themes/twentysixteen/genericons/Genericons.woff", "downloaded_repos/danpros_htmly/themes/twentysixteen/genericons/LICENSE.txt", "downloaded_repos/danpros_htmly/themes/twentysixteen/genericons/README.md", "downloaded_repos/danpros_htmly/themes/twentysixteen/genericons/genericons.css", "downloaded_repos/danpros_htmly/themes/twentysixteen/img/avatar.png", "downloaded_repos/danpros_htmly/themes/twentysixteen/js/functions.js", "downloaded_repos/danpros_htmly/themes/twentysixteen/js/html5.js", "downloaded_repos/danpros_htmly/themes/twentysixteen/js/jquery-migrate.js", "downloaded_repos/danpros_htmly/themes/twentysixteen/js/jquery.js", "downloaded_repos/danpros_htmly/themes/twentysixteen/js/skip-link-focus-fix.js", "downloaded_repos/danpros_htmly/themes/twentysixteen/layout.html.php", "downloaded_repos/danpros_htmly/themes/twentysixteen/main.html.php", "downloaded_repos/danpros_htmly/themes/twentysixteen/no-posts.html.php", "downloaded_repos/danpros_htmly/themes/twentysixteen/post.html.php", "downloaded_repos/danpros_htmly/themes/twentysixteen/profile.html.php", "downloaded_repos/danpros_htmly/themes/twentysixteen/static.html.php", "downloaded_repos/danpros_htmly/upload.php"], "skipped": [{"path": "downloaded_repos/danpros_htmly/system/resources/css/adminlte.min.css", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/danpros_htmly/system/resources/js/adminlte.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/resources/js/bootstrap.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/resources/js/jquery-ui.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/resources/js/jquery.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/autoload.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Common/BitArray.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Common/BitMatrix.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Common/BitUtils.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Common/CharacterSetEci.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Common/EcBlock.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Common/EcBlocks.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Common/ErrorCorrectionLevel.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Common/FormatInformation.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Common/Mode.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Common/ReedSolomonCodec.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Common/Version.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Encoder/BlockPair.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Encoder/ByteMatrix.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Encoder/Encoder.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Encoder/MaskUtil.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Encoder/MatrixUtil.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Encoder/QrCode.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Exception/ExceptionInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Exception/InvalidArgumentException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Exception/OutOfBoundsException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Exception/RuntimeException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Exception/UnexpectedValueException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Exception/WriterException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/Color/Alpha.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/Color/Cmyk.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/Color/ColorInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/Color/Gray.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/Color/Rgb.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/Eye/CompositeEye.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/Eye/EyeInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/Eye/ModuleEye.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/Eye/PointyEye.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/Eye/SimpleCircleEye.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/Eye/SquareEye.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/GDLibRenderer.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/Image/EpsImageBackEnd.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/Image/ImageBackEndInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/Image/ImagickImageBackEnd.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/Image/SvgImageBackEnd.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/Image/TransformationMatrix.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/ImageRenderer.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/Module/DotsModule.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/Module/EdgeIterator/Edge.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/Module/EdgeIterator/EdgeIterator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/Module/ModuleInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/Module/RoundnessModule.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/Module/SquareModule.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/Path/Close.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/Path/Curve.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/Path/EllipticArc.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/Path/Line.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/Path/Move.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/Path/OperationInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/Path/Path.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/PlainTextRenderer.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/RendererInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/RendererStyle/EyeFill.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/RendererStyle/Fill.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/RendererStyle/Gradient.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/RendererStyle/GradientType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Renderer/RendererStyle/RendererStyle.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/bacon/bacon-qr-code/src/Writer.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/composer/ClassLoader.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/composer/InstalledVersions.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/composer/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/composer/autoload_classmap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/composer/autoload_files.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/composer/autoload_namespaces.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/composer/autoload_psr4.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/composer/autoload_real.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/composer/autoload_static.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/composer/ca-bundle/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/composer/ca-bundle/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/composer/ca-bundle/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/composer/ca-bundle/res/cacert.pem", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/composer/ca-bundle/src/CaBundle.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/composer/installed.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/composer/installed.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/composer/platform_check.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/dasprid/enum/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/dasprid/enum/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/dasprid/enum/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/dasprid/enum/src/AbstractEnum.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/dasprid/enum/src/EnumMap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/dasprid/enum/src/Exception/CloneNotSupportedException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/dasprid/enum/src/Exception/ExceptionInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/dasprid/enum/src/Exception/ExpectationException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/dasprid/enum/src/Exception/IllegalArgumentException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/dasprid/enum/src/Exception/MismatchException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/dasprid/enum/src/Exception/SerializeNotSupportedException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/dasprid/enum/src/Exception/UnserializeNotSupportedException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/dasprid/enum/src/NullValue.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/ircmaxell/password-compat/LICENSE.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/ircmaxell/password-compat/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/ircmaxell/password-compat/lib/password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/ircmaxell/password-compat/version-test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/jbroadway/urlify/.github/workflows/ci.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/jbroadway/urlify/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/jbroadway/urlify/.travis.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/jbroadway/urlify/INSTALL", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/jbroadway/urlify/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/jbroadway/urlify/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/jbroadway/urlify/URLify.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/jbroadway/urlify/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/jbroadway/urlify/phpunit.xml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/jbroadway/urlify/scripts/downcode.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/jbroadway/urlify/scripts/filter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/jbroadway/urlify/scripts/transliterate.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/jbroadway/urlify/tests/URLifyTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/jbroadway/urlify/tests/bootstrap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/kanti/hub-updater/.travis.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/kanti/hub-updater/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/kanti/hub-updater/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/kanti/hub-updater/grumphp.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/kanti/hub-updater/src/CacheOneFile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/kanti/hub-updater/src/HelperClass.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/kanti/hub-updater/src/HubUpdater.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/kanti/hub-updater/src/ca_bundle.crt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/.editorconfig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/.scrutinizer.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/.travis.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/License.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/Michelf/Markdown.inc.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/Michelf/Markdown.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/Michelf/MarkdownExtra.inc.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/Michelf/MarkdownExtra.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/Michelf/MarkdownInterface.inc.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/Michelf/MarkdownInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/Readme.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/Readme.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/phpunit.xml.dist", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/bootstrap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/helpers/MarkdownTestHelper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/integration/PhpMarkdownTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Amps and angle encoding.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Amps and angle encoding.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Auto links.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Auto links.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Backslash escapes.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Backslash escapes.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Blockquotes with code blocks.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Blockquotes with code blocks.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Code Blocks.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Code Blocks.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Code Spans.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Code Spans.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Hard-wrapped paragraphs with list-like lines.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Hard-wrapped paragraphs with list-like lines.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Horizontal rules.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Horizontal rules.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Images.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Images.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Inline HTML (Advanced).text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Inline HTML (Advanced).xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Inline HTML (Simple).html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Inline HTML (Simple).text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Inline HTML comments.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Inline HTML comments.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Links, inline style.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Links, inline style.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Links, reference style.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Links, reference style.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Links, shortcut references.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Links, shortcut references.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Literal quotes in titles.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Literal quotes in titles.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Markdown Documentation - Basics.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Markdown Documentation - Basics.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Markdown Documentation - Syntax.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Markdown Documentation - Syntax.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Nested blockquotes.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Nested blockquotes.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Ordered and unordered lists.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Ordered and unordered lists.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Strong and em together.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Strong and em together.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Tabs.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Tabs.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Tidyness.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/markdown.mdtest/Tidyness.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown-extra.mdtest/Abbr.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown-extra.mdtest/Abbr.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown-extra.mdtest/Backtick Fenced Code Blocks Special Cases.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown-extra.mdtest/Backtick Fenced Code Blocks Special Cases.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown-extra.mdtest/Backtick Fenced Code Blocks.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown-extra.mdtest/Backtick Fenced Code Blocks.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown-extra.mdtest/Definition Lists.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown-extra.mdtest/Definition Lists.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown-extra.mdtest/Emphasis.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown-extra.mdtest/Emphasis.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown-extra.mdtest/Footnotes.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown-extra.mdtest/Footnotes.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown-extra.mdtest/Headers with attributes.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown-extra.mdtest/Headers with attributes.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown-extra.mdtest/Inline HTML with Markdown content.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown-extra.mdtest/Inline HTML with Markdown content.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown-extra.mdtest/Link & Image Attributes.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown-extra.mdtest/Link & Image Attributes.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown-extra.mdtest/Tables.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown-extra.mdtest/Tables.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown-extra.mdtest/Tilde Fenced Code Blocks Special Cases.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown-extra.mdtest/Tilde Fenced Code Blocks Special Cases.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown-extra.mdtest/Tilde Fenced Code Blocks.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown-extra.mdtest/Tilde Fenced Code Blocks.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Adjacent Lists.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Adjacent Lists.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Auto Links.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Auto Links.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Backslash escapes.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Backslash escapes.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Code Spans.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Code Spans.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Code block in a list item.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Code block in a list item.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Code block on second line.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Code block on second line.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Code block regressions.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Code block regressions.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Email auto links.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Email auto links.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Emphasis.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Emphasis.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Empty List Item.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Empty List Item.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Headers.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Headers.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Horizontal Rules.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Horizontal Rules.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Inline HTML (Simple).html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Inline HTML (Simple).text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Inline HTML (Span).text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Inline HTML (Span).xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Inline HTML comments.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Inline HTML comments.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Ins & del.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Ins & del.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Links, inline style.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Links, inline style.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/MD5 Hashes.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/MD5 Hashes.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Mixed OLs and ULs.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Mixed OLs and ULs.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Nesting.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Nesting.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/PHP-Specific Bugs.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/PHP-Specific Bugs.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Parens in URL.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Parens in URL.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Quotes in attributes.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Quotes in attributes.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Tight blocks.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/Tight blocks.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/XML empty tag.text", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/resources/php-markdown.mdtest/XML empty tag.xhtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/michelf/php-markdown/test/unit/MarkdownExtraTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/paragonie/constant_time_encoding/LICENSE.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/paragonie/constant_time_encoding/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/paragonie/constant_time_encoding/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/paragonie/constant_time_encoding/src/Base32.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/paragonie/constant_time_encoding/src/Base32Hex.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/paragonie/constant_time_encoding/src/Base64.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/paragonie/constant_time_encoding/src/Base64DotSlash.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/paragonie/constant_time_encoding/src/Base64DotSlashOrdered.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/paragonie/constant_time_encoding/src/Base64UrlSafe.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/paragonie/constant_time_encoding/src/Binary.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/paragonie/constant_time_encoding/src/EncoderInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/paragonie/constant_time_encoding/src/Encoding.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/paragonie/constant_time_encoding/src/Hex.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/paragonie/constant_time_encoding/src/RFC4648.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/pragmarx/google2fa/CHANGELOG.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/pragmarx/google2fa/LICENSE.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/pragmarx/google2fa/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/pragmarx/google2fa/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/pragmarx/google2fa/src/Exceptions/Contracts/Google2FA.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/pragmarx/google2fa/src/Exceptions/Contracts/IncompatibleWithGoogleAuthenticator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/pragmarx/google2fa/src/Exceptions/Contracts/InvalidAlgorithm.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/pragmarx/google2fa/src/Exceptions/Contracts/InvalidCharacters.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/pragmarx/google2fa/src/Exceptions/Contracts/SecretKeyTooShort.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/pragmarx/google2fa/src/Exceptions/Google2FAException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/pragmarx/google2fa/src/Exceptions/IncompatibleWithGoogleAuthenticatorException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/pragmarx/google2fa/src/Exceptions/InvalidAlgorithmException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/pragmarx/google2fa/src/Exceptions/InvalidCharactersException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/pragmarx/google2fa/src/Exceptions/SecretKeyTooShortException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/pragmarx/google2fa/src/Google2FA.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/pragmarx/google2fa/src/Support/Base32.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/pragmarx/google2fa/src/Support/Constants.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/pragmarx/google2fa/src/Support/QRCode.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/suin/php-rss-writer/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/suin/php-rss-writer/.travis.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/suin/php-rss-writer/Makefile", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/suin/php-rss-writer/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/suin/php-rss-writer/Tests/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/suin/php-rss-writer/Tests/Bootstrap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/suin/php-rss-writer/Tests/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/suin/php-rss-writer/Tests/Suin/RSSWriter/ChannelTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/suin/php-rss-writer/Tests/Suin/RSSWriter/FeedTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/suin/php-rss-writer/Tests/Suin/RSSWriter/ItemTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/suin/php-rss-writer/Tests/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/suin/php-rss-writer/Tests/phpunit.xml.dist", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/suin/php-rss-writer/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/suin/php-rss-writer/docker-compose.development.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/suin/php-rss-writer/docker-compose.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/suin/php-rss-writer/examples/simple-feed.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/suin/php-rss-writer/phpunit.xml.dist", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/suin/php-rss-writer/src/Suin/RSSWriter/Channel.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/suin/php-rss-writer/src/Suin/RSSWriter/ChannelInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/suin/php-rss-writer/src/Suin/RSSWriter/Feed.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/suin/php-rss-writer/src/Suin/RSSWriter/FeedInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/suin/php-rss-writer/src/Suin/RSSWriter/Item.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/suin/php-rss-writer/src/Suin/RSSWriter/ItemInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/suin/php-rss-writer/src/Suin/RSSWriter/SimpleXMLElement.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/CHANGELOG.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/LICENSE.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/ASCII.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/ascii_by_languages.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/ascii_extras_by_languages.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/ascii_language_max_key.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/ascii_ord.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x000.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x001.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x002.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x003.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x004.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x005.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x006.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x007.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x009.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x00a.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x00b.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x00c.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x00d.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x00e.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x00f.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x010.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x011.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x012.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x013.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x014.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x015.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x016.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x017.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x018.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x01d.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x01e.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x01f.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x020.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x021.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x022.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x023.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x024.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x025.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x026.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x027.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x028.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x029.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x02a.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x02c.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x02e.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x02f.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x030.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x031.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x032.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x033.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x04d.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x04e.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x04f.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x050.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x051.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x052.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x053.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x054.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x055.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x056.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x057.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x058.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x059.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x05a.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x05b.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x05c.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x05d.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x05e.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x05f.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x060.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x061.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x062.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x063.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x064.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x065.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x066.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x067.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x068.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x069.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x06a.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x06b.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x06c.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x06d.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x06e.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x06f.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x070.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x071.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x072.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x073.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x074.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x075.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x076.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x077.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x078.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x079.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x07a.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x07b.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x07c.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x07d.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x07e.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x07f.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x080.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x081.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x082.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x083.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x084.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x085.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x086.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x087.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x088.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x089.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x08a.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x08b.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x08c.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x08d.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x08e.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x08f.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x090.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x091.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x092.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x093.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x094.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x095.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x096.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x097.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x098.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x099.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x09a.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x09b.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x09c.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x09d.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x09e.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x09f.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0a0.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0a1.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0a2.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0a3.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0a4.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0ac.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0ad.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0ae.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0af.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0b0.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0b1.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0b2.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0b3.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0b4.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0b5.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0b6.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0b7.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0b8.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0b9.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0ba.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0bb.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0bc.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0bd.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0be.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0bf.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0c0.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0c1.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0c2.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0c3.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0c4.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0c5.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0c6.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0c7.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0c8.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0c9.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0ca.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0cb.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0cc.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0cd.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0ce.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0cf.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0d0.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0d1.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0d2.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0d3.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0d4.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0d5.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0d6.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0d7.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0f9.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0fa.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0fb.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0fc.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0fd.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0fe.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x0ff.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x1d4.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x1d5.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x1d6.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x1d7.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/portable-ascii/src/voku/helper/data/x1f1.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/CHANGELOG.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/src/voku/helper/StopWords.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/src/voku/helper/StopWordsLanguageNotExists.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/src/voku/helper/stopwords/ar.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/src/voku/helper/stopwords/bg.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/src/voku/helper/stopwords/ca.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/src/voku/helper/stopwords/cz.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/src/voku/helper/stopwords/da.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/src/voku/helper/stopwords/de.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/src/voku/helper/stopwords/el.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/src/voku/helper/stopwords/en.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/src/voku/helper/stopwords/eo.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/src/voku/helper/stopwords/es.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/src/voku/helper/stopwords/et.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/src/voku/helper/stopwords/fi.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/src/voku/helper/stopwords/fr.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/src/voku/helper/stopwords/hi.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/src/voku/helper/stopwords/hr.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/src/voku/helper/stopwords/hu.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/src/voku/helper/stopwords/id.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/src/voku/helper/stopwords/it.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/src/voku/helper/stopwords/ka.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/src/voku/helper/stopwords/lt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/src/voku/helper/stopwords/lv.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/src/voku/helper/stopwords/nl.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/src/voku/helper/stopwords/no.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/src/voku/helper/stopwords/pl.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/src/voku/helper/stopwords/pt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/src/voku/helper/stopwords/ro.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/src/voku/helper/stopwords/ru.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/src/voku/helper/stopwords/sk.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/src/voku/helper/stopwords/sv.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/src/voku/helper/stopwords/tr.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/src/voku/helper/stopwords/uk.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/system/vendor/voku/stop-words/src/voku/helper/stopwords/vi.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/themes/blog/js/bootstrap.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/themes/blog/js/jquery-latest.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/themes/twentyfifteen/js/theia-sticky-sidebar.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/danpros_htmly/themes/twentysixteen/js/jquery.js", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.598869800567627, "profiling_times": {"config_time": 5.593059062957764, "core_time": 19.99630093574524, "ignores_time": 0.0017201900482177734, "total_time": 25.59187626838684}, "parsing_time": {"total_time": 7.87458062171936, "per_file_time": {"mean": 0.04921612888574602, "std_dev": 0.051037772669165295}, "very_slow_stats": {"time_ratio": 0.6268013710025483, "count_ratio": 0.025}, "very_slow_files": [{"fpath": "downloaded_repos/danpros_htmly/system/includes/functions.php", "ftime": 0.32314109802246094}, {"fpath": "downloaded_repos/danpros_htmly/system/admin/editor/js/Markdown.Editor.js", "ftime": 0.6981759071350098}, {"fpath": "downloaded_repos/danpros_htmly/system/htmly.php", "ftime": 1.870081901550293}, {"fpath": "downloaded_repos/danpros_htmly/themes/twentyfifteen/js/jquery.js", "ftime": 2.0443990230560303}]}, "scanning_time": {"total_time": 62.85605430603027, "per_file_time": {"mean": 0.07769598801734262, "std_dev": 0.6248896793812617}, "very_slow_stats": {"time_ratio": 0.7388393262867863, "count_ratio": 0.00865265760197775}, "very_slow_files": [{"fpath": "downloaded_repos/danpros_htmly/system/admin/editor/js/Markdown.Converter.js", "ftime": 1.5547139644622803}, {"fpath": "downloaded_repos/danpros_htmly/system/admin/admin.php", "ftime": 1.9412050247192383}, {"fpath": "downloaded_repos/danpros_htmly/system/admin/editor/js/Markdown.Editor.js", "ftime": 3.686682939529419}, {"fpath": "downloaded_repos/danpros_htmly/themes/twentysixteen/js/jquery.js", "ftime": 5.089032888412476}, {"fpath": "downloaded_repos/danpros_htmly/system/includes/functions.php", "ftime": 5.120465993881226}, {"fpath": "downloaded_repos/danpros_htmly/system/htmly.php", "ftime": 12.580471992492676}, {"fpath": "downloaded_repos/danpros_htmly/themes/twentyfifteen/js/jquery.js", "ftime": 16.467952013015747}]}, "matching_time": {"total_time": 23.69823980331421, "per_file_and_rule_time": {"mean": 0.040098544506453795, "std_dev": 0.020868811965165702}, "very_slow_stats": {"time_ratio": 0.7246663808516466, "count_ratio": 0.06937394247038917}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/danpros_htmly/themes/twentyfifteen/js/jquery.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.5101401805877686}, {"fpath": "downloaded_repos/danpros_htmly/themes/twentyfifteen/js/jquery.js", "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.5297420024871826}, {"fpath": "downloaded_repos/danpros_htmly/system/admin/admin.php", "rule_id": "php.lang.security.injection.tainted-callable.tainted-callable", "time": 0.6669149398803711}, {"fpath": "downloaded_repos/danpros_htmly/system/includes/functions.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.7072920799255371}, {"fpath": "downloaded_repos/danpros_htmly/themes/twentyfifteen/js/jquery.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.7868390083312988}, {"fpath": "downloaded_repos/danpros_htmly/system/includes/functions.php", "rule_id": "php.lang.security.injection.tainted-callable.tainted-callable", "time": 0.9479091167449951}, {"fpath": "downloaded_repos/danpros_htmly/themes/twentyfifteen/js/jquery.js", "rule_id": "javascript.aws-lambda.security.tainted-eval.tainted-eval", "time": 1.0373570919036865}, {"fpath": "downloaded_repos/danpros_htmly/themes/twentyfifteen/js/jquery.js", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "time": 1.2817041873931885}, {"fpath": "downloaded_repos/danpros_htmly/system/htmly.php", "rule_id": "php.lang.security.injection.tainted-session.tainted-session", "time": 1.2999460697174072}, {"fpath": "downloaded_repos/danpros_htmly/system/htmly.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 1.8280270099639893}]}, "tainting_time": {"total_time": 18.387149333953857, "per_def_and_rule_time": {"mean": 0.0050210675406755475, "std_dev": 0.003381282104536367}, "very_slow_stats": {"time_ratio": 0.6724113705872207, "count_ratio": 0.012015292190060076}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/danpros_htmly/system/htmly.php", "fline": 1, "rule_id": "php.lang.security.tainted-exec.tainted-exec", "time": 0.2621638774871826}, {"fpath": "downloaded_repos/danpros_htmly/themes/twentyfifteen/js/jquery.js", "fline": 1, "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "time": 0.26993608474731445}, {"fpath": "downloaded_repos/danpros_htmly/system/htmly.php", "fline": 1, "rule_id": "php.lang.security.injection.tainted-object-instantiation.tainted-object-instantiation", "time": 0.29360198974609375}, {"fpath": "downloaded_repos/danpros_htmly/system/htmly.php", "fline": 1, "rule_id": "php.lang.security.injection.tainted-exec.tainted-exec", "time": 0.3388020992279053}, {"fpath": "downloaded_repos/danpros_htmly/system/htmly.php", "fline": 1, "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.3693881034851074}, {"fpath": "downloaded_repos/danpros_htmly/system/htmly.php", "fline": 1, "rule_id": "php.lang.security.injection.printed-request.printed-request", "time": 0.5694169998168945}, {"fpath": "downloaded_repos/danpros_htmly/themes/twentyfifteen/js/jquery.js", "fline": 1, "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 0.9145658016204834}, {"fpath": "downloaded_repos/danpros_htmly/themes/twentyfifteen/js/jquery.js", "fline": 1, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 0.9714138507843018}, {"fpath": "downloaded_repos/danpros_htmly/themes/twentyfifteen/js/jquery.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 1.6380059719085693}, {"fpath": "downloaded_repos/danpros_htmly/system/htmly.php", "fline": 1, "rule_id": "php.lang.security.injection.tainted-session.tainted-session", "time": 2.5455689430236816}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}