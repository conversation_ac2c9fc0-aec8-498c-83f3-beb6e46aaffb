{"version": "1.130.0", "results": [{"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/mbraak_django-file-form/django_file_form/static/file_form/file_form.js", "start": {"line": 1188, "col": 8, "offset": 37471}, "end": {"line": 1188, "col": 25, "offset": 37488}, "extra": {"message": "RegExp() called with a `args` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/mbraak_django-file-form/django_file_form/static/file_form/file_form.js", "start": {"line": 2572, "col": 15, "offset": 77656}, "end": {"line": 2572, "col": 73, "offset": 77714}, "extra": {"message": "RegExp() called with a `options` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/mbraak_django-file-form/django_file_form/static/file_form/file_form.js", "start": {"line": 2572, "col": 15, "offset": 77656}, "end": {"line": 2572, "col": 73, "offset": 77714}, "extra": {"message": "RegExp() called with a `source` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/mbraak_django-file-form/django_file_form/static/file_form/file_form.js", "start": {"line": 3003, "col": 7, "offset": 90727}, "end": {"line": 3003, "col": 45, "offset": 90765}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.injection.request-data-write.request-data-write", "path": "downloaded_repos/mbraak_django-file-form/django_file_form/tus/views.py", "start": {"line": 154, "col": 13, "offset": 4654}, "end": {"line": 154, "col": 37, "offset": 4678}, "extra": {"message": "Found user-controlled request data passed into '.write(...)'. This could be dangerous if a malicious actor is able to control data into sensitive files. For example, a malicious actor could force rolling of critical log files, or cause a denial-of-service by using up available disk space. Instead, ensure that request data is properly escaped or sanitized.", "metadata": {"cwe": ["CWE-93: Improper Neutralization of CRLF Sequences ('CRLF Injection')"], "owasp": ["A03:2021 - Injection"], "category": "security", "technology": ["django"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/python.django.security.injection.request-data-write.request-data-write", "shortlink": "https://sg.run/0Q6j"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/mbraak_django-file-form/frontend/src/render_upload_file.ts", "start": {"line": 194, "col": 5, "offset": 5160}, "end": {"line": 194, "col": 43, "offset": 5198}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/templates/example_form.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 22, "offset": 128}}, {"path": "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/templates/example_form.html", "start": {"line": 8, "col": 54, "offset": 0}, "end": {"line": 8, "col": 73, "offset": 19}}, {"path": "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/templates/example_form.html", "start": {"line": 9, "col": 56, "offset": 0}, "end": {"line": 9, "col": 61, "offset": 5}}, {"path": "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/templates/example_form.html", "start": {"line": 40, "col": 5, "offset": 0}, "end": {"line": 40, "col": 22, "offset": 17}}, {"path": "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/templates/example_form.html", "start": {"line": 42, "col": 9, "offset": 0}, "end": {"line": 42, "col": 45, "offset": 36}}, {"path": "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/templates/example_form.html", "start": {"line": 44, "col": 9, "offset": 0}, "end": {"line": 46, "col": 15, "offset": 43}}]], "message": "Syntax error at line downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/templates/example_form.html:1:\n `{% extends 'base.html' %}\n{% load static %}\n\n{% block title %}Form example{% endblock %}\n\n{% block head %}\n    {{ block.super }}` was unexpected", "path": "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/templates/example_form.html", "spans": [{"file": "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/templates/example_form.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 22, "offset": 128}}, {"file": "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/templates/example_form.html", "start": {"line": 8, "col": 54, "offset": 0}, "end": {"line": 8, "col": 73, "offset": 19}}, {"file": "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/templates/example_form.html", "start": {"line": 9, "col": 56, "offset": 0}, "end": {"line": 9, "col": 61, "offset": 5}}, {"file": "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/templates/example_form.html", "start": {"line": 40, "col": 5, "offset": 0}, "end": {"line": 40, "col": 22, "offset": 17}}, {"file": "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/templates/example_form.html", "start": {"line": 42, "col": 9, "offset": 0}, "end": {"line": 42, "col": 45, "offset": 36}}, {"file": "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/templates/example_form.html", "start": {"line": 44, "col": 9, "offset": 0}, "end": {"line": 46, "col": 15, "offset": 43}}]}], "paths": {"scanned": ["downloaded_repos/mbraak_django-file-form/.github/dependabot.yml", "downloaded_repos/mbraak_django-file-form/.github/workflows/ci.yml", "downloaded_repos/mbraak_django-file-form/.github/workflows/codeql-analysis.yml", "downloaded_repos/mbraak_django-file-form/.github/workflows/mkdocs.yml", "downloaded_repos/mbraak_django-file-form/.gitignore", "downloaded_repos/mbraak_django-file-form/LICENSE.rst", "downloaded_repos/mbraak_django-file-form/MANIFEST.in", "downloaded_repos/mbraak_django-file-form/README.md", "downloaded_repos/mbraak_django-file-form/coverage/.nycrc.json", "downloaded_repos/mbraak_django-file-form/coverage/package-lock.json", "downloaded_repos/mbraak_django-file-form/coverage/package.json", "downloaded_repos/mbraak_django-file-form/django_file_form/__init__.py", "downloaded_repos/mbraak_django-file-form/django_file_form/admin.py", "downloaded_repos/mbraak_django-file-form/django_file_form/ajaxuploader/__init__.py", "downloaded_repos/mbraak_django-file-form/django_file_form/ajaxuploader/static/ajaxuploader/css/fileuploader.css", "downloaded_repos/mbraak_django-file-form/django_file_form/ajaxuploader/static/ajaxuploader/js/fileuploader.js", "downloaded_repos/mbraak_django-file-form/django_file_form/conf.py", "downloaded_repos/mbraak_django-file-form/django_file_form/django_util.py", "downloaded_repos/mbraak_django-file-form/django_file_form/fields.py", "downloaded_repos/mbraak_django-file-form/django_file_form/forms.py", "downloaded_repos/mbraak_django-file-form/django_file_form/locale/de/LC_MESSAGES/django.mo", "downloaded_repos/mbraak_django-file-form/django_file_form/locale/de/LC_MESSAGES/django.po", "downloaded_repos/mbraak_django-file-form/django_file_form/locale/es/LC_MESSAGES/django.mo", "downloaded_repos/mbraak_django-file-form/django_file_form/locale/es/LC_MESSAGES/django.po", "downloaded_repos/mbraak_django-file-form/django_file_form/locale/fi/LC_MESSAGES/django.mo", "downloaded_repos/mbraak_django-file-form/django_file_form/locale/fi/LC_MESSAGES/django.po", "downloaded_repos/mbraak_django-file-form/django_file_form/locale/fr/LC_MESSAGES/django.mo", "downloaded_repos/mbraak_django-file-form/django_file_form/locale/fr/LC_MESSAGES/django.po", "downloaded_repos/mbraak_django-file-form/django_file_form/locale/nl/LC_MESSAGES/django.mo", "downloaded_repos/mbraak_django-file-form/django_file_form/locale/nl/LC_MESSAGES/django.po", "downloaded_repos/mbraak_django-file-form/django_file_form/management/__init__.py", "downloaded_repos/mbraak_django-file-form/django_file_form/management/commands/__init__.py", "downloaded_repos/mbraak_django-file-form/django_file_form/management/commands/delete_unused_files.py", "downloaded_repos/mbraak_django-file-form/django_file_form/migrations/0001_initial.py", "downloaded_repos/mbraak_django-file-form/django_file_form/migrations/0002_auto_20170316_0901.py", "downloaded_repos/mbraak_django-file-form/django_file_form/migrations/0003_auto_20170317_1230.py", "downloaded_repos/mbraak_django-file-form/django_file_form/migrations/0004_auto_20170423_0329.py", "downloaded_repos/mbraak_django-file-form/django_file_form/migrations/0005_auto_20200407_0814.py", "downloaded_repos/mbraak_django-file-form/django_file_form/migrations/0006_auto_20200501_0908.py", "downloaded_repos/mbraak_django-file-form/django_file_form/migrations/0007_auto_20210119_0104.py", "downloaded_repos/mbraak_django-file-form/django_file_form/migrations/0008_auto_20220519_0204.py", "downloaded_repos/mbraak_django-file-form/django_file_form/migrations/0009_rename_temporaryuploadedfile_form_id_field_name_form_id_field_name_idx.py", "downloaded_repos/mbraak_django-file-form/django_file_form/migrations/__init__.py", "downloaded_repos/mbraak_django-file-form/django_file_form/model_admin.py", "downloaded_repos/mbraak_django-file-form/django_file_form/model_manager.py", "downloaded_repos/mbraak_django-file-form/django_file_form/models.py", "downloaded_repos/mbraak_django-file-form/django_file_form/s3_multipart/__init__.py", "downloaded_repos/mbraak_django-file-form/django_file_form/s3_multipart/urls.py", "downloaded_repos/mbraak_django-file-form/django_file_form/s3_multipart/utils.py", "downloaded_repos/mbraak_django-file-form/django_file_form/s3_multipart/views.py", "downloaded_repos/mbraak_django-file-form/django_file_form/static/file_form/auto_init.js", "downloaded_repos/mbraak_django-file-form/django_file_form/static/file_form/file_form.css", "downloaded_repos/mbraak_django-file-form/django_file_form/static/file_form/file_form.js", "downloaded_repos/mbraak_django-file-form/django_file_form/static/file_form/file_form.js.map", "downloaded_repos/mbraak_django-file-form/django_file_form/static/file_form/file_form.min.js.map", "downloaded_repos/mbraak_django-file-form/django_file_form/templates/django_file_form/admin_change_form.html", "downloaded_repos/mbraak_django-file-form/django_file_form/templates/django_file_form/upload_widget.html", "downloaded_repos/mbraak_django-file-form/django_file_form/tus/__init__.py", "downloaded_repos/mbraak_django-file-form/django_file_form/tus/urls.py", "downloaded_repos/mbraak_django-file-form/django_file_form/tus/utils.py", "downloaded_repos/mbraak_django-file-form/django_file_form/tus/views.py", "downloaded_repos/mbraak_django-file-form/django_file_form/type_util.py", "downloaded_repos/mbraak_django-file-form/django_file_form/uploaded_file.py", "downloaded_repos/mbraak_django-file-form/django_file_form/urls.py", "downloaded_repos/mbraak_django-file-form/django_file_form/util.py", "downloaded_repos/mbraak_django-file-form/django_file_form/widgets/__init__.py", "downloaded_repos/mbraak_django-file-form/django_file_form/widgets/base_upload_widget.py", "downloaded_repos/mbraak_django-file-form/django_file_form/widgets/form_data.py", "downloaded_repos/mbraak_django-file-form/django_file_form/widgets/upload_multiple_widget.py", "downloaded_repos/mbraak_django-file-form/django_file_form/widgets/upload_widget.py", "downloaded_repos/mbraak_django-file-form/docs/admin.md", "downloaded_repos/mbraak_django-file-form/docs/auto_initialize.md", "downloaded_repos/mbraak_django-file-form/docs/changelog.md", "downloaded_repos/mbraak_django-file-form/docs/customization.md", "downloaded_repos/mbraak_django-file-form/docs/details.md", "downloaded_repos/mbraak_django-file-form/docs/form_sets.md", "downloaded_repos/mbraak_django-file-form/docs/index.md", "downloaded_repos/mbraak_django-file-form/docs/internals.md", "downloaded_repos/mbraak_django-file-form/docs/javascript_events.md", "downloaded_repos/mbraak_django-file-form/docs/javascript_options.md", "downloaded_repos/mbraak_django-file-form/docs/model_forms.md", "downloaded_repos/mbraak_django-file-form/docs/python_settings.md", "downloaded_repos/mbraak_django-file-form/docs/translations.md", "downloaded_repos/mbraak_django-file-form/docs/upgrade.md", "downloaded_repos/mbraak_django-file-form/docs/usage.md", "downloaded_repos/mbraak_django-file-form/frontend/.prettierrc", "downloaded_repos/mbraak_django-file-form/frontend/babel.config.json", "downloaded_repos/mbraak_django-file-form/frontend/babel.coverage.config.json", "downloaded_repos/mbraak_django-file-form/frontend/eslint.config.mjs", "downloaded_repos/mbraak_django-file-form/frontend/package-lock.json", "downloaded_repos/mbraak_django-file-form/frontend/package.json", "downloaded_repos/mbraak_django-file-form/frontend/rollup.config.mjs", "downloaded_repos/mbraak_django-file-form/frontend/src/accepted_file_types.test.ts", "downloaded_repos/mbraak_django-file-form/frontend/src/accepted_file_types.ts", "downloaded_repos/mbraak_django-file-form/frontend/src/auto_init_file_forms.ts", "downloaded_repos/mbraak_django-file-form/frontend/src/drop_area.test.ts", "downloaded_repos/mbraak_django-file-form/frontend/src/drop_area.ts", "downloaded_repos/mbraak_django-file-form/frontend/src/file_field.ts", "downloaded_repos/mbraak_django-file-form/frontend/src/file_form.ts", "downloaded_repos/mbraak_django-file-form/frontend/src/init_form_set.test.ts", "downloaded_repos/mbraak_django-file-form/frontend/src/init_form_set.ts", "downloaded_repos/mbraak_django-file-form/frontend/src/init_upload_fields.ts", "downloaded_repos/mbraak_django-file-form/frontend/src/picomatch.d.ts", "downloaded_repos/mbraak_django-file-form/frontend/src/render_upload_file.ts", "downloaded_repos/mbraak_django-file-form/frontend/src/uploads/base_upload.ts", "downloaded_repos/mbraak_django-file-form/frontend/src/uploads/s3_upload.test.ts", "downloaded_repos/mbraak_django-file-form/frontend/src/uploads/s3_upload.ts", "downloaded_repos/mbraak_django-file-form/frontend/src/uploads/s3_utils.ts", "downloaded_repos/mbraak_django-file-form/frontend/src/uploads/tus_upload.ts", "downloaded_repos/mbraak_django-file-form/frontend/src/uploads/tus_utils.ts", "downloaded_repos/mbraak_django-file-form/frontend/src/uploads/uploaded_file.ts", "downloaded_repos/mbraak_django-file-form/frontend/src/util.test.ts", "downloaded_repos/mbraak_django-file-form/frontend/src/util.ts", "downloaded_repos/mbraak_django-file-form/frontend/style/file_form.scss", "downloaded_repos/mbraak_django-file-form/frontend/testSetup/vitestSetup.ts", "downloaded_repos/mbraak_django-file-form/frontend/tsconfig.json", "downloaded_repos/mbraak_django-file-form/frontend/vitest.config.ts", "downloaded_repos/mbraak_django-file-form/mkdocs.yml", "downloaded_repos/mbraak_django-file-form/requirements_docs.txt", "downloaded_repos/mbraak_django-file-form/setup.py", "downloaded_repos/mbraak_django-file-form/testproject/.bowerrc", "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/__init__.py", "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/admin.py", "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/forms.py", "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/migrations/0001_initial.py", "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/migrations/0002_alter_example_input_file.py", "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/migrations/__init__.py", "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/models.py", "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/static/eventemitter3.js", "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/static/example.css", "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/static/example_form.js", "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/static/example_form_custom_widget.js", "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/static/example_form_set.js", "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/static/wizard_form.js", "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/templates/base.html", "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/templates/example_form.html", "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/templates/form_set.html", "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/templates/placeholder_form.html", "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/templates/success.html", "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/templates/wizard.html", "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/urls.py", "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/views.py", "downloaded_repos/mbraak_django-file-form/testproject/manage.py", "downloaded_repos/mbraak_django-file-form/testproject/requirements.txt", "downloaded_repos/mbraak_django-file-form/testproject/requirements_all.txt", "downloaded_repos/mbraak_django-file-form/testproject/requirements_ci.txt", "downloaded_repos/mbraak_django-file-form/testproject/requirements_django.txt", "downloaded_repos/mbraak_django-file-form/testproject/requirements_test.txt", "downloaded_repos/mbraak_django-file-form/testproject/run_tests", "downloaded_repos/mbraak_django-file-form/testproject/testproject/__init__.py", "downloaded_repos/mbraak_django-file-form/testproject/testproject/settings.py", "downloaded_repos/mbraak_django-file-form/testproject/testproject/settings_default.py", "downloaded_repos/mbraak_django-file-form/testproject/testproject/urls.py", "downloaded_repos/mbraak_django-file-form/testproject/wsgi.py", "downloaded_repos/mbraak_django-file-form/tox.ini"], "skipped": [{"path": "downloaded_repos/mbraak_django-file-form/django_file_form/static/file_form/file_form.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/templates/example_form.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/tests/live/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/tests/live/test_admin.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/tests/live/test_s3.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/tests/live/test_with_ajax.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/tests/live/test_without_ajax.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/tests/test_data/wikipedia.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/tests/test_form.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/tests/test_model.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/tests/test_views.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/tests/utils/admin_page.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/tests/utils/base_live_testcase.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/tests/utils/base_page.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/tests/utils/page.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/tests/utils/temp_file.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mbraak_django-file-form/testproject/django_file_form_example/tests/utils/test_utils.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.9665870666503906, "profiling_times": {"config_time": 5.846255779266357, "core_time": 16.210591077804565, "ignores_time": 0.0018014907836914062, "total_time": 22.0595281124115}, "parsing_time": {"total_time": 2.662552833557129, "per_file_time": {"mean": 0.025850027510263385, "std_dev": 0.011155248397361215}, "very_slow_stats": {"time_ratio": 0.5181677172695619, "count_ratio": 0.019417475728155338}, "very_slow_files": [{"fpath": "downloaded_repos/mbraak_django-file-form/frontend/package-lock.json", "ftime": 0.37978100776672363}, {"fpath": "downloaded_repos/mbraak_django-file-form/django_file_form/static/file_form/file_form.js", "ftime": 0.9998679161071777}]}, "scanning_time": {"total_time": 22.70597505569458, "per_file_time": {"mean": 0.05406184537070137, "std_dev": 0.39623332540520884}, "very_slow_stats": {"time_ratio": 0.5654459193595784, "count_ratio": 0.002380952380952381}, "very_slow_files": [{"fpath": "downloaded_repos/mbraak_django-file-form/django_file_form/static/file_form/file_form.js", "ftime": 12.839000940322876}]}, "matching_time": {"total_time": 10.052330255508423, "per_file_and_rule_time": {"mean": 0.015880458539507782, "std_dev": 0.005887657351781056}, "very_slow_stats": {"time_ratio": 0.6432240580255516, "count_ratio": 0.026856240126382307}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/mbraak_django-file-form/django_file_form/static/file_form/file_form.js", "rule_id": "problem-based-packs.insecure-transport.js-node.using-http-server.using-http-server", "time": 0.2673609256744385}, {"fpath": "downloaded_repos/mbraak_django-file-form/django_file_form/static/file_form/file_form.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.28356003761291504}, {"fpath": "downloaded_repos/mbraak_django-file-form/django_file_form/static/file_form/file_form.js", "rule_id": "javascript.lang.security.detect-eval-with-expression.detect-eval-with-expression", "time": 0.28827714920043945}, {"fpath": "downloaded_repos/mbraak_django-file-form/django_file_form/static/file_form/file_form.js", "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 0.39359402656555176}, {"fpath": "downloaded_repos/mbraak_django-file-form/django_file_form/static/file_form/file_form.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.41588401794433594}, {"fpath": "downloaded_repos/mbraak_django-file-form/django_file_form/static/file_form/file_form.js", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 0.4376380443572998}, {"fpath": "downloaded_repos/mbraak_django-file-form/django_file_form/static/file_form/file_form.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.6706039905548096}, {"fpath": "downloaded_repos/mbraak_django-file-form/django_file_form/static/file_form/file_form.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.772258996963501}, {"fpath": "downloaded_repos/mbraak_django-file-form/django_file_form/static/file_form/file_form.js", "rule_id": "javascript.aws-lambda.security.tainted-eval.tainted-eval", "time": 0.8519690036773682}, {"fpath": "downloaded_repos/mbraak_django-file-form/django_file_form/static/file_form/file_form.js", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "time": 1.0228519439697266}]}, "tainting_time": {"total_time": 4.943702220916748, "per_def_and_rule_time": {"mean": 0.0007397429628784598, "std_dev": 2.8749618905289792e-05}, "very_slow_stats": {"time_ratio": 0.2679558474665777, "count_ratio": 0.001945234176268143}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/mbraak_django-file-form/django_file_form/static/file_form/file_form.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.07180404663085938}, {"fpath": "downloaded_repos/mbraak_django-file-form/django_file_form/static/file_form/file_form.js", "fline": 1, "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 0.08531689643859863}, {"fpath": "downloaded_repos/mbraak_django-file-form/django_file_form/static/file_form/file_form.js", "fline": 1155, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.08812499046325684}, {"fpath": "downloaded_repos/mbraak_django-file-form/django_file_form/static/file_form/file_form.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.08829307556152344}, {"fpath": "downloaded_repos/mbraak_django-file-form/django_file_form/static/file_form/file_form.js", "fline": 1155, "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 0.08946895599365234}, {"fpath": "downloaded_repos/mbraak_django-file-form/django_file_form/static/file_form/file_form.js", "fline": 1, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 0.0974130630493164}, {"fpath": "downloaded_repos/mbraak_django-file-form/django_file_form/static/file_form/file_form.js", "fline": 1, "rule_id": "javascript.lang.security.audit.sqli.node-postgres-sqli.node-postgres-sqli", "time": 0.11586689949035645}, {"fpath": "downloaded_repos/mbraak_django-file-form/django_file_form/static/file_form/file_form.js", "fline": 1155, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 0.13555598258972168}, {"fpath": "downloaded_repos/mbraak_django-file-form/django_file_form/static/file_form/file_form.js", "fline": 1155, "rule_id": "javascript.lang.security.audit.sqli.node-postgres-sqli.node-postgres-sqli", "time": 0.14341497421264648}, {"fpath": "downloaded_repos/mbraak_django-file-form/django_file_form/static/file_form/file_form.js", "fline": 1155, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.23197102546691895}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089476480}, "engine_requested": "OSS", "skipped_rules": []}