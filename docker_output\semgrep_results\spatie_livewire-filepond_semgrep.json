{"version": "1.130.0", "results": [], "errors": [{"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/spatie_livewire-filepond/.github/workflows/run-tests.yml:6:\n (approximate error location; error nearby after) error calling parser: did not find expected alphabetic or numeric character character 0 position 0 returned: 0", "path": "downloaded_repos/spatie_livewire-filepond/.github/workflows/run-tests.yml"}], "paths": {"scanned": ["downloaded_repos/spatie_livewire-filepond/.editorconfig", "downloaded_repos/spatie_livewire-filepond/.gitattributes", "downloaded_repos/spatie_livewire-filepond/.github/FUNDING.yml", "downloaded_repos/spatie_livewire-filepond/.github/ISSUE_TEMPLATE/bug.yml", "downloaded_repos/spatie_livewire-filepond/.github/ISSUE_TEMPLATE/config.yml", "downloaded_repos/spatie_livewire-filepond/.github/dependabot.yml", "downloaded_repos/spatie_livewire-filepond/.github/workflows/dependabot-auto-merge.yml", "downloaded_repos/spatie_livewire-filepond/.github/workflows/fix-php-code-style-issues.yml", "downloaded_repos/spatie_livewire-filepond/.github/workflows/generate-assets.yml", "downloaded_repos/spatie_livewire-filepond/.github/workflows/phpstan.yml", "downloaded_repos/spatie_livewire-filepond/.github/workflows/run-tests.yml", "downloaded_repos/spatie_livewire-filepond/.github/workflows/update-changelog.yml", "downloaded_repos/spatie_livewire-filepond/.gitignore", "downloaded_repos/spatie_livewire-filepond/CHANGELOG.md", "downloaded_repos/spatie_livewire-filepond/LICENSE.md", "downloaded_repos/spatie_livewire-filepond/README.md", "downloaded_repos/spatie_livewire-filepond/composer.json", "downloaded_repos/spatie_livewire-filepond/docs/images/upload.png", "downloaded_repos/spatie_livewire-filepond/package-lock.json", "downloaded_repos/spatie_livewire-filepond/package.json", "downloaded_repos/spatie_livewire-filepond/phpstan-baseline.neon", "downloaded_repos/spatie_livewire-filepond/phpstan.neon.dist", "downloaded_repos/spatie_livewire-filepond/phpunit.xml.dist", "downloaded_repos/spatie_livewire-filepond/resources/js/filepond.js", "downloaded_repos/spatie_livewire-filepond/resources/lang/ar/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/az/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/ca/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/ckb/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/cs/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/da/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/de/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/el/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/en/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/es/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/et/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/fa/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/fi/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/fr/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/he/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/hr/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/hu/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/id/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/it/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/ja/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/km/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/ko/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/lt/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/lv/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/nb/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/nl/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/pl/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/pt/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/pt_BR/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/ro/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/ru/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/sk/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/sv/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/tk/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/tr/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/uk/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/vi/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/zh_CN/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/lang/zh_TW/filepond.php", "downloaded_repos/spatie_livewire-filepond/resources/views/upload.blade.php", "downloaded_repos/spatie_livewire-filepond/src/LivewireFilepondServiceProvider.php", "downloaded_repos/spatie_livewire-filepond/src/WithFilePond.php", "downloaded_repos/spatie_livewire-filepond/vite.config.js", "downloaded_repos/spatie_livewire-filepond/workbench/app/Providers/WorkbenchServiceProvider.php"], "skipped": [{"path": "downloaded_repos/spatie_livewire-filepond/.github/workflows/run-tests.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/spatie_livewire-filepond/resources/dist/filepond.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spatie_livewire-filepond/resources/dist/filepond.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spatie_livewire-filepond/resources/dist/filepond.js.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spatie_livewire-filepond/tests/ArchTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spatie_livewire-filepond/tests/Pest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spatie_livewire-filepond/tests/TestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spatie_livewire-filepond/tests/TestSupport/Components/TestComponent.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spatie_livewire-filepond/tests/WithFilePondTest.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6638829708099365, "profiling_times": {"config_time": 5.769777774810791, "core_time": 2.446845531463623, "ignores_time": 0.0018551349639892578, "total_time": 8.2196044921875}, "parsing_time": {"total_time": 0.3176918029785156, "per_file_time": {"mean": 0.0055735404031318535, "std_dev": 5.410336309697256e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.191577672958374, "per_file_time": {"mean": 0.006142152953393678, "std_dev": 0.00015276185412515535}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.2915678024291992, "per_file_and_rule_time": {"mean": 0.0009111493825912476, "std_dev": 1.1903621554045782e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.002717733383178711, "per_def_and_rule_time": {"mean": 0.0005435466766357422, "std_dev": 2.575527878434514e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1085719040}, "engine_requested": "OSS", "skipped_rules": []}