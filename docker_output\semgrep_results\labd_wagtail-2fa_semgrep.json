{"version": "1.130.0", "results": [{"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/labd_wagtail-2fa/docs/_templates/sidebar-intro.html", "start": {"line": 7, "col": 9, "offset": 250}, "end": {"line": 7, "col": 78, "offset": 319}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/labd_wagtail-2fa/docs/_templates/sidebar-intro.html", "start": {"line": 8, "col": 9, "offset": 333}, "end": {"line": 8, "col": 81, "offset": 405}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/labd_wagtail-2fa/docs/_templates/sidebar-intro.html", "start": {"line": 9, "col": 9, "offset": 419}, "end": {"line": 9, "col": 78, "offset": 488}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/apps/home/<USER>/home/<USER>", "start": {"line": 10, "col": 76, "offset": 398}, "end": {"line": 10, "col": 136, "offset": 458}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.unvalidated-password.unvalidated-password", "path": "downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/apps/user/forms.py", "start": {"line": 35, "col": 9, "offset": 1150}, "end": {"line": 35, "col": 58, "offset": 1199}, "extra": {"message": "The password on 'user' is being set without validating the password. Call django.contrib.auth.password_validation.validate_password() with validation functions before setting the password. See https://docs.djangoproject.com/en/3.0/topics/auth/passwords/ for more information.", "fix": "if django.contrib.auth.password_validation.validate_password(self.cleaned_data['password1'], user=user):\n            user.set_password(self.cleaned_data['password1'])", "metadata": {"cwe": ["CWE-521: Weak Password Requirements"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://docs.djangoproject.com/en/3.0/topics/auth/passwords/#module-django.contrib.auth.password_validation"], "category": "security", "technology": ["django"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/python.django.security.audit.unvalidated-password.unvalidated-password", "shortlink": "https://sg.run/OPBL"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-translate-as-no-escape.template-translate-as-no-escape", "path": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/device_form.html", "start": {"line": 4, "col": 1, "offset": 56}, "end": {"line": 5, "col": 35, "offset": 135}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-translate-as-no-escape.template-translate-as-no-escape", "shortlink": "https://sg.run/PJDz"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/device_form.html", "start": {"line": 13, "col": 13, "offset": 382}, "end": {"line": 13, "col": 29, "offset": 398}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/legacy/otp_form.html", "start": {"line": 15, "col": 43, "offset": 626}, "end": {"line": 15, "col": 59, "offset": 642}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/otp_form.html", "start": {"line": 15, "col": 43, "offset": 626}, "end": {"line": 15, "col": 59, "offset": 642}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/otp_form_v6.html", "start": {"line": 15, "col": 43, "offset": 620}, "end": {"line": 15, "col": 59, "offset": 636}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/apps/home/<USER>/home/<USER>", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 20, "offset": 101}}, {"path": "downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/apps/home/<USER>/home/<USER>", "start": {"line": 11, "col": 1, "offset": 0}, "end": {"line": 11, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/apps/home/<USER>/home/<USER>\n `{% extends \"base.html\" %}\n\n{% block body_class %}template-homepage{% endblock %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/apps/home/<USER>/home/<USER>", "spans": [{"file": "downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/apps/home/<USER>/home/<USER>", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 20, "offset": 101}}, {"file": "downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/apps/home/<USER>/home/<USER>", "start": {"line": 11, "col": 1, "offset": 0}, "end": {"line": 11, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/templates/base.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 33, "offset": 32}}]], "message": "Syntax error at line downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/templates/base.html:1:\n `{% load static wagtailuserbar %}` was unexpected", "path": "downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/templates/base.html", "spans": [{"file": "downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/templates/base.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 33, "offset": 32}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/device_confirm_delete.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 9, "col": 31, "offset": 297}}, {"path": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/device_confirm_delete.html", "start": {"line": 17, "col": 5, "offset": 0}, "end": {"line": 18, "col": 15, "offset": 21}}]], "message": "Syntax error at line downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/device_confirm_delete.html:1:\n `{% extends \"wagtailadmin/base.html\" %}\n{% load i18n %}\n\n{% block titletag %}{% trans \"Delete 2FA device\" %}{% endblock %}\n{% block content %}\n    {% trans \"Two Factor Settings\" as prefs_str %}\n    {% include \"wagtailadmin/shared/header.html\" with title=prefs_str %}\n\n    <div class=\"nice-padding\">` was unexpected", "path": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/device_confirm_delete.html", "spans": [{"file": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/device_confirm_delete.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 9, "col": 31, "offset": 297}}, {"file": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/device_confirm_delete.html", "start": {"line": 17, "col": 5, "offset": 0}, "end": {"line": 18, "col": 15, "offset": 21}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/device_form.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 13, "col": 29, "offset": 398}}, {"path": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/device_form.html", "start": {"line": 43, "col": 13, "offset": 0}, "end": {"line": 44, "col": 15, "offset": 34}}, {"path": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/device_form.html", "start": {"line": 86, "col": 5, "offset": 0}, "end": {"line": 87, "col": 15, "offset": 21}}]], "message": "Syntax error at line downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/device_form.html:1:\n `{% extends \"wagtailadmin/base.html\" %}\n{% load i18n %}\n\n{% trans \"Add new 2FA device\" as titletag %}\n{% block titletag %}{{ titletag }}{% endblock %}\n{% block content %}\n    {% trans \"Two Factor Settings\" as prefs_str %}\n    {% include \"wagtailadmin/shared/header.html\" with title=prefs_str %}\n\n\n    <div class=\"nice-padding\">\n        <div class=\"help-block help-info\">\n            {% blocktrans %}` was unexpected", "path": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/device_form.html", "spans": [{"file": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/device_form.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 13, "col": 29, "offset": 398}}, {"file": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/device_form.html", "start": {"line": 43, "col": 13, "offset": 0}, "end": {"line": 44, "col": 15, "offset": 34}}, {"file": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/device_form.html", "start": {"line": 86, "col": 5, "offset": 0}, "end": {"line": 87, "col": 15, "offset": 21}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/device_list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 10, "col": 32, "offset": 334}}, {"path": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/device_list.html", "start": {"line": 45, "col": 13, "offset": 0}, "end": {"line": 48, "col": 44, "offset": 115}}, {"path": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/device_list.html", "start": {"line": 50, "col": 9, "offset": 0}, "end": {"line": 52, "col": 15, "offset": 37}}]], "message": "Syntax error at line downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/device_list.html:1:\n `{% extends \"wagtailadmin/base.html\" %}\n{% load i18n %}\n\n{% block titletag %}{% trans \"Registered 2FA devices\" %}{% endblock %}\n{% block content %}\n    {% trans \"Two Factor Settings\" as prefs_str %}\n    {% include \"wagtailadmin/shared/header.html\" with title=prefs_str %}\n\n    <div class=\"nice-padding\">\n        <table class=\"listing\">` was unexpected", "path": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/device_list.html", "spans": [{"file": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/device_list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 10, "col": 32, "offset": 334}}, {"file": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/device_list.html", "start": {"line": 45, "col": 13, "offset": 0}, "end": {"line": 48, "col": 44, "offset": 115}}, {"file": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/device_list.html", "start": {"line": 50, "col": 9, "offset": 0}, "end": {"line": 52, "col": 15, "offset": 37}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/legacy/otp_form.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 22, "offset": 198}}, {"path": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/legacy/otp_form.html", "start": {"line": 62, "col": 1, "offset": 0}, "end": {"line": 62, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/legacy/otp_form.html:1:\n `{% extends \"wagtailadmin/admin_base.html\" %}\n{% load i18n wagtailadmin_tags %}\n{% block titletag %}{% trans \"Sign in\" %}{% endblock %}\n{% block bodyclass %}login{% endblock %}\n\n{% block furniture %}` was unexpected", "path": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/legacy/otp_form.html", "spans": [{"file": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/legacy/otp_form.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 22, "offset": 198}}, {"file": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/legacy/otp_form.html", "start": {"line": 62, "col": 1, "offset": 0}, "end": {"line": 62, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/otp_form.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 22, "offset": 198}}, {"path": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/otp_form.html", "start": {"line": 62, "col": 1, "offset": 0}, "end": {"line": 62, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/otp_form.html:1:\n `{% extends \"wagtailadmin/admin_base.html\" %}\n{% load i18n wagtailadmin_tags %}\n{% block titletag %}{% trans \"Sign in\" %}{% endblock %}\n{% block bodyclass %}login{% endblock %}\n\n{% block furniture %}` was unexpected", "path": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/otp_form.html", "spans": [{"file": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/otp_form.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 22, "offset": 198}}, {"file": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/otp_form.html", "start": {"line": 62, "col": 1, "offset": 0}, "end": {"line": 62, "col": 15, "offset": 14}}]}], "paths": {"scanned": ["downloaded_repos/labd_wagtail-2fa/.editorconfig", "downloaded_repos/labd_wagtail-2fa/.github/dependabot.yml", "downloaded_repos/labd_wagtail-2fa/.github/workflows/python-release.yml", "downloaded_repos/labd_wagtail-2fa/.github/workflows/python-tox.yml", "downloaded_repos/labd_wagtail-2fa/.gitignore", "downloaded_repos/labd_wagtail-2fa/CHANGES", "downloaded_repos/labd_wagtail-2fa/LICENSE", "downloaded_repos/labd_wagtail-2fa/MANIFEST.in", "downloaded_repos/labd_wagtail-2fa/Makefile", "downloaded_repos/labd_wagtail-2fa/README.rst", "downloaded_repos/labd_wagtail-2fa/docs/Makefile", "downloaded_repos/labd_wagtail-2fa/docs/_templates/sidebar-intro.html", "downloaded_repos/labd_wagtail-2fa/docs/conf.py", "downloaded_repos/labd_wagtail-2fa/docs/index.rst", "downloaded_repos/labd_wagtail-2fa/docs/make.bat", "downloaded_repos/labd_wagtail-2fa/docs/requirements.txt", "downloaded_repos/labd_wagtail-2fa/sandbox/exampledata/users.json", "downloaded_repos/labd_wagtail-2fa/sandbox/manage.py", "downloaded_repos/labd_wagtail-2fa/sandbox/requirements.txt", "downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/__init__.py", "downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/apps/__init__.py", "downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/apps/home/<USER>", "downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/apps/home/<USER>/0001_initial.py", "downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/apps/home/<USER>/0002_create_homepage.py", "downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/apps/home/<USER>/__init__.py", "downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/apps/home/<USER>", "downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/apps/home/<USER>/home/<USER>", "downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/apps/user/__init__.py", "downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/apps/user/admin.py", "downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/apps/user/forms.py", "downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/apps/user/migrations/0001_initial.py", "downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/apps/user/migrations/__init__.py", "downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/apps/user/models.py", "downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/settings.py", "downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/static/css/sandbox.css", "downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/static/js/sandbox.js", "downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/templates/404.html", "downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/templates/500.html", "downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/templates/base.html", "downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/urls.py", "downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/wsgi.py", "downloaded_repos/labd_wagtail-2fa/setup.cfg", "downloaded_repos/labd_wagtail-2fa/setup.py", "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/__init__.py", "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/apps.py", "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/forms.py", "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/locale/en/LC_MESSAGES/django.mo", "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/locale/en/LC_MESSAGES/django.po", "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/locale/nl/LC_MESSAGES/django.mo", "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/locale/nl/LC_MESSAGES/django.po", "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/locale/pt_BR/LC_MESSAGES/django.mo", "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/locale/pt_BR/LC_MESSAGES/django.po", "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/locale/sv/LC_MESSAGES/django.mo", "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/locale/sv/LC_MESSAGES/django.po", "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/middleware.py", "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/migrations/0001_initial.py", "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/migrations/__init__.py", "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/mixins.py", "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/device_confirm_delete.html", "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/device_form.html", "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/device_list.html", "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/legacy/otp_form.html", "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/otp_form.html", "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/otp_form_v6.html", "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/urls.py", "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/utils.py", "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/views.py", "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/wagtail_hooks.py", "downloaded_repos/labd_wagtail-2fa/tox.ini"], "skipped": [{"path": "downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/apps/home/<USER>/home/<USER>", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/labd_wagtail-2fa/sandbox/sandbox/templates/base.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/device_confirm_delete.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/device_form.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/device_list.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/legacy/otp_form.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/labd_wagtail-2fa/src/wagtail_2fa/templates/wagtail_2fa/otp_form.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/labd_wagtail-2fa/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/labd_wagtail-2fa/tests/conftest.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/labd_wagtail-2fa/tests/test_apps.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/labd_wagtail-2fa/tests/test_hooks.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/labd_wagtail-2fa/tests/test_middleware.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/labd_wagtail-2fa/tests/test_mixins.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/labd_wagtail-2fa/tests/test_views.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/labd_wagtail-2fa/tests/urls.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.947737216949463, "profiling_times": {"config_time": 9.010403156280518, "core_time": 3.3493332862854004, "ignores_time": 0.0017349720001220703, "total_time": 12.36335563659668}, "parsing_time": {"total_time": 0.5540199279785156, "per_file_time": {"mean": 0.012884184371593384, "std_dev": 0.000270744180136674}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 3.587019681930542, "per_file_time": {"mean": 0.019494672184405124, "std_dev": 0.002329942115482414}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.525428056716919, "per_file_and_rule_time": {"mean": 0.0018307597795014596, "std_dev": 2.9740063410339215e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.12055134773254395, "per_def_and_rule_time": {"mean": 0.000353523013878428, "std_dev": 6.436464742490748e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089364160}, "engine_requested": "OSS", "skipped_rules": []}