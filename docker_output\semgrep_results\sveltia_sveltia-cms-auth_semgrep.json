{"version": "1.130.0", "results": [{"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/sveltia_sveltia-cms-auth/src/index.js", "start": {"line": 86, "col": 28, "offset": 2899}, "end": {"line": 86, "col": 92, "offset": 2963}, "extra": {"message": "RegExp() called with a `str` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/sveltia_sveltia-cms-auth/.editorconfig", "downloaded_repos/sveltia_sveltia-cms-auth/.eslintignore", "downloaded_repos/sveltia_sveltia-cms-auth/.eslintrc.yaml", "downloaded_repos/sveltia_sveltia-cms-auth/.gitattributes", "downloaded_repos/sveltia_sveltia-cms-auth/.github/CODEOWNERS", "downloaded_repos/sveltia_sveltia-cms-auth/.github/workflows/deploy.yml", "downloaded_repos/sveltia_sveltia-cms-auth/.gitignore", "downloaded_repos/sveltia_sveltia-cms-auth/.ncurc.yaml", "downloaded_repos/sveltia_sveltia-cms-auth/.npmrc", "downloaded_repos/sveltia_sveltia-cms-auth/.prettierignore", "downloaded_repos/sveltia_sveltia-cms-auth/.prettierrc.yaml", "downloaded_repos/sveltia_sveltia-cms-auth/.vscode/extensions.json", "downloaded_repos/sveltia_sveltia-cms-auth/.vscode/settings.json", "downloaded_repos/sveltia_sveltia-cms-auth/LICENSE.txt", "downloaded_repos/sveltia_sveltia-cms-auth/README.md", "downloaded_repos/sveltia_sveltia-cms-auth/cspell.config.yaml", "downloaded_repos/sveltia_sveltia-cms-auth/jsconfig.json", "downloaded_repos/sveltia_sveltia-cms-auth/package.json", "downloaded_repos/sveltia_sveltia-cms-auth/pnpm-lock.yaml", "downloaded_repos/sveltia_sveltia-cms-auth/src/index.js", "downloaded_repos/sveltia_sveltia-cms-auth/wrangler.toml"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.7376680374145508, "profiling_times": {"config_time": 5.918497562408447, "core_time": 2.81787109375, "ignores_time": 0.12001323699951172, "total_time": 8.857191562652588}, "parsing_time": {"total_time": 0.24356698989868164, "per_file_time": {"mean": 0.022142453627152878, "std_dev": 0.00031889929746923803}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.1616308689117432, "per_file_time": {"mean": 0.02191756356437251, "std_dev": 0.006531561451051192}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.6684002876281738, "per_file_and_rule_time": {"mean": 0.0032764719981773225, "std_dev": 4.868354502697694e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.026428937911987305, "per_def_and_rule_time": {"mean": 0.0016518086194992065, "std_dev": 2.19038838067398e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089476480}, "engine_requested": "OSS", "skipped_rules": []}