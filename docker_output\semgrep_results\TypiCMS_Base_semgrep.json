{"version": "1.130.0", "results": [], "errors": [], "paths": {"scanned": ["downloaded_repos/TypiCMS_Base/.editorconfig", "downloaded_repos/TypiCMS_Base/.env.example", "downloaded_repos/TypiCMS_Base/.gitattributes", "downloaded_repos/TypiCMS_Base/.github/FUNDING.yml", "downloaded_repos/TypiCMS_Base/.gitignore", "downloaded_repos/TypiCMS_Base/.prettierignore", "downloaded_repos/TypiCMS_Base/.prettierrc", "downloaded_repos/TypiCMS_Base/CHANGELOG.md", "downloaded_repos/TypiCMS_Base/CONTRIBUTING.md", "downloaded_repos/TypiCMS_Base/LICENCE.md", "downloaded_repos/TypiCMS_Base/Modules/.gitignore", "downloaded_repos/TypiCMS_Base/README.md", "downloaded_repos/TypiCMS_Base/SECURITY.md", "downloaded_repos/TypiCMS_Base/app/Http/Controllers/Controller.php", "downloaded_repos/TypiCMS_Base/app/Providers/AppServiceProvider.php", "downloaded_repos/TypiCMS_Base/app/helpers.php", "downloaded_repos/TypiCMS_Base/artisan", "downloaded_repos/TypiCMS_Base/bootstrap/app.php", "downloaded_repos/TypiCMS_Base/bootstrap/cache/.gitignore", "downloaded_repos/TypiCMS_Base/bootstrap/providers.php", "downloaded_repos/TypiCMS_Base/bun.lock", "downloaded_repos/TypiCMS_Base/composer.json", "downloaded_repos/TypiCMS_Base/composer.lock", "downloaded_repos/TypiCMS_Base/config/app.php", "downloaded_repos/TypiCMS_Base/config/auth.php", "downloaded_repos/TypiCMS_Base/config/cache.php", "downloaded_repos/TypiCMS_Base/config/croppa.php", "downloaded_repos/TypiCMS_Base/config/database.php", "downloaded_repos/TypiCMS_Base/config/eloquent-sortable.php", "downloaded_repos/TypiCMS_Base/config/file.php", "downloaded_repos/TypiCMS_Base/config/filesystems.php", "downloaded_repos/TypiCMS_Base/config/javascript.php", "downloaded_repos/TypiCMS_Base/config/logging.php", "downloaded_repos/TypiCMS_Base/config/mail.php", "downloaded_repos/TypiCMS_Base/config/permission.php", "downloaded_repos/TypiCMS_Base/config/queue.php", "downloaded_repos/TypiCMS_Base/config/services.php", "downloaded_repos/TypiCMS_Base/config/session.php", "downloaded_repos/TypiCMS_Base/config/translatable-bootforms.php", "downloaded_repos/TypiCMS_Base/database/.gitignore", "downloaded_repos/TypiCMS_Base/database/factories/UserFactory.php", "downloaded_repos/TypiCMS_Base/lang/.gitignore", "downloaded_repos/TypiCMS_Base/package.json", "downloaded_repos/TypiCMS_Base/phpstan.neon", "downloaded_repos/TypiCMS_Base/phpunit.xml", "downloaded_repos/TypiCMS_Base/pint.json", "downloaded_repos/TypiCMS_Base/public/.htaccess", "downloaded_repos/TypiCMS_Base/public/favicon.ico", "downloaded_repos/TypiCMS_Base/public/index.php", "downloaded_repos/TypiCMS_Base/public/robots.txt", "downloaded_repos/TypiCMS_Base/routes/console.php", "downloaded_repos/TypiCMS_Base/routes/web.php", "downloaded_repos/TypiCMS_Base/storage/app/.gitignore", "downloaded_repos/TypiCMS_Base/storage/app/private/.gitignore", "downloaded_repos/TypiCMS_Base/storage/app/public/.gitignore", "downloaded_repos/TypiCMS_Base/storage/debugbar/.gitignore", "downloaded_repos/TypiCMS_Base/storage/framework/.gitignore", "downloaded_repos/TypiCMS_Base/storage/framework/cache/.gitignore", "downloaded_repos/TypiCMS_Base/storage/framework/cache/data/.gitignore", "downloaded_repos/TypiCMS_Base/storage/framework/sessions/.gitignore", "downloaded_repos/TypiCMS_Base/storage/framework/testing/.gitignore", "downloaded_repos/TypiCMS_Base/storage/framework/views/.gitignore", "downloaded_repos/TypiCMS_Base/storage/logs/.gitignore", "downloaded_repos/TypiCMS_Base/tsconfig.json", "downloaded_repos/TypiCMS_Base/vite.config.js"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.6575188636779785, "profiling_times": {"config_time": 5.718816518783569, "core_time": 2.554455518722534, "ignores_time": 0.002131223678588867, "total_time": 8.276324987411499}, "parsing_time": {"total_time": 0.2732512950897217, "per_file_time": {"mean": 0.008539102971553799, "std_dev": 7.47782258012086e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.0849454402923584, "per_file_time": {"mean": 0.006697194075878754, "std_dev": 0.0004520734604133676}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.09999513626098633, "per_file_and_rule_time": {"mean": 0.0012819889264229017, "std_dev": 7.775415080234142e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.0018677711486816406, "per_def_and_rule_time": {"mean": 0.0006225903828938802, "std_dev": 5.987326150918508e-08}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}