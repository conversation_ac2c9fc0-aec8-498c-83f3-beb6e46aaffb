{"version": "1.130.0", "results": [], "errors": [], "paths": {"scanned": ["downloaded_repos/TypiCMS_Base/.editorconfig", "downloaded_repos/TypiCMS_Base/.env.example", "downloaded_repos/TypiCMS_Base/.gitattributes", "downloaded_repos/TypiCMS_Base/.github/FUNDING.yml", "downloaded_repos/TypiCMS_Base/.gitignore", "downloaded_repos/TypiCMS_Base/.prettierignore", "downloaded_repos/TypiCMS_Base/.prettierrc", "downloaded_repos/TypiCMS_Base/CHANGELOG.md", "downloaded_repos/TypiCMS_Base/CONTRIBUTING.md", "downloaded_repos/TypiCMS_Base/LICENCE.md", "downloaded_repos/TypiCMS_Base/Modules/.gitignore", "downloaded_repos/TypiCMS_Base/README.md", "downloaded_repos/TypiCMS_Base/SECURITY.md", "downloaded_repos/TypiCMS_Base/app/Http/Controllers/Controller.php", "downloaded_repos/TypiCMS_Base/app/Providers/AppServiceProvider.php", "downloaded_repos/TypiCMS_Base/app/helpers.php", "downloaded_repos/TypiCMS_Base/artisan", "downloaded_repos/TypiCMS_Base/bootstrap/app.php", "downloaded_repos/TypiCMS_Base/bootstrap/cache/.gitignore", "downloaded_repos/TypiCMS_Base/bootstrap/providers.php", "downloaded_repos/TypiCMS_Base/bun.lock", "downloaded_repos/TypiCMS_Base/composer.json", "downloaded_repos/TypiCMS_Base/composer.lock", "downloaded_repos/TypiCMS_Base/config/app.php", "downloaded_repos/TypiCMS_Base/config/auth.php", "downloaded_repos/TypiCMS_Base/config/cache.php", "downloaded_repos/TypiCMS_Base/config/croppa.php", "downloaded_repos/TypiCMS_Base/config/database.php", "downloaded_repos/TypiCMS_Base/config/eloquent-sortable.php", "downloaded_repos/TypiCMS_Base/config/file.php", "downloaded_repos/TypiCMS_Base/config/filesystems.php", "downloaded_repos/TypiCMS_Base/config/javascript.php", "downloaded_repos/TypiCMS_Base/config/logging.php", "downloaded_repos/TypiCMS_Base/config/mail.php", "downloaded_repos/TypiCMS_Base/config/permission.php", "downloaded_repos/TypiCMS_Base/config/queue.php", "downloaded_repos/TypiCMS_Base/config/services.php", "downloaded_repos/TypiCMS_Base/config/session.php", "downloaded_repos/TypiCMS_Base/config/translatable-bootforms.php", "downloaded_repos/TypiCMS_Base/database/.gitignore", "downloaded_repos/TypiCMS_Base/database/factories/UserFactory.php", "downloaded_repos/TypiCMS_Base/lang/.gitignore", "downloaded_repos/TypiCMS_Base/package.json", "downloaded_repos/TypiCMS_Base/phpstan.neon", "downloaded_repos/TypiCMS_Base/phpunit.xml", "downloaded_repos/TypiCMS_Base/pint.json", "downloaded_repos/TypiCMS_Base/public/.htaccess", "downloaded_repos/TypiCMS_Base/public/favicon.ico", "downloaded_repos/TypiCMS_Base/public/index.php", "downloaded_repos/TypiCMS_Base/public/robots.txt", "downloaded_repos/TypiCMS_Base/routes/console.php", "downloaded_repos/TypiCMS_Base/routes/web.php", "downloaded_repos/TypiCMS_Base/storage/app/.gitignore", "downloaded_repos/TypiCMS_Base/storage/app/private/.gitignore", "downloaded_repos/TypiCMS_Base/storage/app/public/.gitignore", "downloaded_repos/TypiCMS_Base/storage/debugbar/.gitignore", "downloaded_repos/TypiCMS_Base/storage/framework/.gitignore", "downloaded_repos/TypiCMS_Base/storage/framework/cache/.gitignore", "downloaded_repos/TypiCMS_Base/storage/framework/cache/data/.gitignore", "downloaded_repos/TypiCMS_Base/storage/framework/sessions/.gitignore", "downloaded_repos/TypiCMS_Base/storage/framework/testing/.gitignore", "downloaded_repos/TypiCMS_Base/storage/framework/views/.gitignore", "downloaded_repos/TypiCMS_Base/storage/logs/.gitignore", "downloaded_repos/TypiCMS_Base/tsconfig.json", "downloaded_repos/TypiCMS_Base/vite.config.js"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.5684709548950195, "profiling_times": {"config_time": 5.630780935287476, "core_time": 2.380268096923828, "ignores_time": 0.0016586780548095703, "total_time": 8.013472318649292}, "parsing_time": {"total_time": 0.3104836940765381, "per_file_time": {"mean": 0.009702615439891813, "std_dev": 0.00011801336436872978}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.1156187057495117, "per_file_time": {"mean": 0.006886535220675999, "std_dev": 0.0004199087007757529}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.10287022590637207, "per_file_and_rule_time": {"mean": 0.0013188490500816931, "std_dev": 8.205603502998401e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.0015828609466552734, "per_def_and_rule_time": {"mean": 0.0005276203155517578, "std_dev": 3.968424759174619e-08}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1085719040}, "engine_requested": "OSS", "skipped_rules": []}