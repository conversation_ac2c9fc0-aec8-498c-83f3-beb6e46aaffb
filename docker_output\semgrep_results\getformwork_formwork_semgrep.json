{"version": "1.130.0", "results": [{"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/getformwork_formwork/formwork/server.php", "start": {"line": 7, "col": 39, "offset": 170}, "end": {"line": 7, "col": 52, "offset": 183}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/getformwork_formwork/formwork/src/Controllers/AbstractController.php", "start": {"line": 73, "col": 16, "offset": 2178}, "end": {"line": 73, "col": 68, "offset": 2230}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/getformwork_formwork/formwork/src/Controllers/PageController.php", "start": {"line": 61, "col": 28, "offset": 2048}, "end": {"line": 61, "col": 85, "offset": 2105}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/AuthenticationController.php", "start": {"line": 31, "col": 20, "offset": 914}, "end": {"line": 31, "col": 72, "offset": 966}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/AuthenticationController.php", "start": {"line": 88, "col": 28, "offset": 3355}, "end": {"line": 88, "col": 80, "offset": 3407}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/AuthenticationController.php", "start": {"line": 126, "col": 16, "offset": 4650}, "end": {"line": 126, "col": 68, "offset": 4702}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/FilesController.php", "start": {"line": 82, "col": 16, "offset": 2381}, "end": {"line": 82, "col": 74, "offset": 2439}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/FilesController.php", "start": {"line": 126, "col": 24, "offset": 3728}, "end": {"line": 126, "col": 106, "offset": 3810}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/FilesController.php", "start": {"line": 166, "col": 16, "offset": 5238}, "end": {"line": 166, "col": 74, "offset": 5296}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/FilesController.php", "start": {"line": 195, "col": 20, "offset": 6366}, "end": {"line": 195, "col": 102, "offset": 6448}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/FilesController.php", "start": {"line": 235, "col": 16, "offset": 8153}, "end": {"line": 235, "col": 127, "offset": 8264}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/FilesController.php", "start": {"line": 266, "col": 24, "offset": 9432}, "end": {"line": 266, "col": 106, "offset": 9514}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/FilesController.php", "start": {"line": 282, "col": 24, "offset": 10281}, "end": {"line": 282, "col": 106, "offset": 10363}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/FilesController.php", "start": {"line": 304, "col": 16, "offset": 11294}, "end": {"line": 304, "col": 98, "offset": 11376}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/OptionsController.php", "start": {"line": 33, "col": 16, "offset": 739}, "end": {"line": 33, "col": 75, "offset": 798}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/OptionsController.php", "start": {"line": 64, "col": 20, "offset": 1958}, "end": {"line": 64, "col": 81, "offset": 2019}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/OptionsController.php", "start": {"line": 106, "col": 20, "offset": 3556}, "end": {"line": 106, "col": 79, "offset": 3615}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/PagesController.php", "start": {"line": 93, "col": 16, "offset": 3200}, "end": {"line": 93, "col": 112, "offset": 3296}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/PagesController.php", "start": {"line": 117, "col": 24, "offset": 4173}, "end": {"line": 117, "col": 120, "offset": 4269}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/PagesController.php", "start": {"line": 127, "col": 24, "offset": 4716}, "end": {"line": 127, "col": 176, "offset": 4868}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/PagesController.php", "start": {"line": 138, "col": 20, "offset": 5308}, "end": {"line": 138, "col": 154, "offset": 5442}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/PagesController.php", "start": {"line": 186, "col": 28, "offset": 7382}, "end": {"line": 186, "col": 163, "offset": 7517}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/PagesController.php", "start": {"line": 188, "col": 24, "offset": 7560}, "end": {"line": 188, "col": 109, "offset": 7645}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/PagesController.php", "start": {"line": 332, "col": 16, "offset": 13630}, "end": {"line": 332, "col": 68, "offset": 13682}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/RegisterController.php", "start": {"line": 44, "col": 28, "offset": 1486}, "end": {"line": 44, "col": 80, "offset": 1538}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/RegisterController.php", "start": {"line": 69, "col": 24, "offset": 2742}, "end": {"line": 69, "col": 76, "offset": 2794}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/ToolsController.php", "start": {"line": 31, "col": 16, "offset": 675}, "end": {"line": 31, "col": 76, "offset": 735}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/UsersController.php", "start": {"line": 54, "col": 20, "offset": 1600}, "end": {"line": 54, "col": 72, "offset": 1652}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/UsersController.php", "start": {"line": 64, "col": 20, "offset": 1997}, "end": {"line": 64, "col": 72, "offset": 2049}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/UsersController.php", "start": {"line": 72, "col": 20, "offset": 2367}, "end": {"line": 72, "col": 72, "offset": 2419}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/UsersController.php", "start": {"line": 84, "col": 16, "offset": 2921}, "end": {"line": 84, "col": 68, "offset": 2973}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/UsersController.php", "start": {"line": 124, "col": 16, "offset": 4672}, "end": {"line": 124, "col": 68, "offset": 4724}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/UsersController.php", "start": {"line": 159, "col": 16, "offset": 6101}, "end": {"line": 159, "col": 107, "offset": 6192}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/UsersController.php", "start": {"line": 179, "col": 20, "offset": 6768}, "end": {"line": 179, "col": 72, "offset": 6820}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/UsersController.php", "start": {"line": 205, "col": 20, "offset": 7968}, "end": {"line": 205, "col": 111, "offset": 8059}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/FileSystem.php", "start": {"line": 404, "col": 14, "offset": 11299}, "end": {"line": 404, "col": 27, "offset": 11312}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/FileSystem.php", "start": {"line": 444, "col": 14, "offset": 12933}, "end": {"line": 444, "col": 27, "offset": 12946}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/fileslist.ts", "start": {"line": 139, "col": 48, "offset": 6132}, "end": {"line": 139, "col": 112, "offset": 6196}, "extra": {"message": "RegExp() called with a `event` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/fileslist.ts", "start": {"line": 142, "col": 29, "offset": 6303}, "end": {"line": 142, "col": 86, "offset": 6360}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/fileslist.ts", "start": {"line": 145, "col": 29, "offset": 6461}, "end": {"line": 145, "col": 51, "offset": 6483}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/fileslist.ts", "start": {"line": 214, "col": 29, "offset": 9291}, "end": {"line": 214, "col": 71, "offset": 9333}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/color-input.ts", "start": {"line": 26, "col": 17, "offset": 594}, "end": {"line": 26, "col": 57, "offset": 634}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/date-input.ts", "start": {"line": 414, "col": 9, "offset": 12927}, "end": {"line": 414, "col": 466, "offset": 13384}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/date-input.ts", "start": {"line": 417, "col": 13, "offset": 13437}, "end": {"line": 417, "col": 714, "offset": 14138}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/date-input.ts", "start": {"line": 587, "col": 9, "offset": 19350}, "end": {"line": 587, "col": 93, "offset": 19434}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/date-input.ts", "start": {"line": 590, "col": 13, "offset": 19487}, "end": {"line": 590, "col": 180, "offset": 19654}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/date-input.ts", "start": {"line": 591, "col": 13, "offset": 19667}, "end": {"line": 591, "col": 114, "offset": 19768}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/duration-input.ts", "start": {"line": 119, "col": 13, "offset": 4234}, "end": {"line": 119, "col": 122, "offset": 4343}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/editor-input.ts", "start": {"line": 12, "col": 29, "offset": 485}, "end": {"line": 12, "col": 96, "offset": 552}, "extra": {"message": "RegExp() called with a `baseUri` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/editor/code/menu.ts", "start": {"line": 12, "col": 27, "offset": 476}, "end": {"line": 12, "col": 52, "offset": 501}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/editor/markdown/inputrules.ts", "start": {"line": 26, "col": 35, "offset": 868}, "end": {"line": 26, "col": 72, "offset": 905}, "extra": {"message": "RegExp() called with a `maxLevel` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/editor/markdown/menu.ts", "start": {"line": 255, "col": 27, "offset": 8926}, "end": {"line": 255, "col": 52, "offset": 8951}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/editor/markdown/menu.ts", "start": {"line": 262, "col": 5, "offset": 9101}, "end": {"line": 262, "col": 27, "offset": 9123}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/range-input.ts", "start": {"line": 26, "col": 17, "offset": 746}, "end": {"line": 26, "col": 57, "offset": 786}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/select-input.ts", "start": {"line": 359, "col": 28, "offset": 10618}, "end": {"line": 359, "col": 98, "offset": 10688}, "extra": {"message": "RegExp() called with a `value` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/tags-input.ts", "start": {"line": 158, "col": 9, "offset": 5038}, "end": {"line": 158, "col": 39, "offset": 5068}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/tags-input.ts", "start": {"line": 399, "col": 9, "offset": 14587}, "end": {"line": 399, "col": 31, "offset": 14609}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/tags-input.ts", "start": {"line": 480, "col": 28, "offset": 16997}, "end": {"line": 480, "col": 98, "offset": 17067}, "extra": {"message": "RegExp() called with a `value` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/upload-input.ts", "start": {"line": 120, "col": 25, "offset": 4426}, "end": {"line": 120, "col": 92, "offset": 4493}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/upload-input.ts", "start": {"line": 185, "col": 13, "offset": 7872}, "end": {"line": 185, "col": 67, "offset": 7926}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/upload-input.ts", "start": {"line": 193, "col": 13, "offset": 8228}, "end": {"line": 193, "col": 68, "offset": 8283}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/notification.ts", "start": {"line": 62, "col": 13, "offset": 2035}, "end": {"line": 62, "col": 43, "offset": 2065}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/tooltip.ts", "start": {"line": 51, "col": 9, "offset": 1371}, "end": {"line": 51, "col": 44, "offset": 1406}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/views/backups.ts", "start": {"line": 54, "col": 33, "offset": 2083}, "end": {"line": 54, "col": 108, "offset": 2158}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/views/backups.ts", "start": {"line": 56, "col": 33, "offset": 2192}, "end": {"line": 56, "col": 105, "offset": 2264}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/views/backups.ts", "start": {"line": 57, "col": 33, "offset": 2297}, "end": {"line": 57, "col": 105, "offset": 2369}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/views/backups.ts", "start": {"line": 60, "col": 33, "offset": 2525}, "end": {"line": 60, "col": 115, "offset": 2607}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/views/pages.ts", "start": {"line": 85, "col": 25, "offset": 3283}, "end": {"line": 85, "col": 71, "offset": 3329}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/views/pages.ts", "start": {"line": 92, "col": 36, "offset": 3697}, "end": {"line": 92, "col": 107, "offset": 3768}, "extra": {"message": "RegExp() called with a `event` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/views/pages.ts", "start": {"line": 100, "col": 29, "offset": 4161}, "end": {"line": 100, "col": 87, "offset": 4219}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/views/updates.ts", "start": {"line": 23, "col": 17, "offset": 1046}, "end": {"line": 23, "col": 49, "offset": 1078}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/views/updates.ts", "start": {"line": 36, "col": 17, "offset": 1539}, "end": {"line": 36, "col": 73, "offset": 1595}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/views/updates.ts", "start": {"line": 50, "col": 25, "offset": 2064}, "end": {"line": 50, "col": 67, "offset": 2106}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/views/updates.ts", "start": {"line": 69, "col": 17, "offset": 2877}, "end": {"line": 69, "col": 88, "offset": 2948}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getformwork_formwork/panel/src/ts/components/views/updates.ts", "start": {"line": 81, "col": 25, "offset": 3443}, "end": {"line": 81, "col": 71, "offset": 3489}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/getformwork_formwork/formwork/translations/pl.yaml:3:\n (approximate error location; error nearby after) error calling parser: did not find expected ',' or ']' character 0 position 0 returned: 0", "path": "downloaded_repos/getformwork_formwork/formwork/translations/pl.yaml"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/getformwork_formwork/panel/translations/pl.yaml:92:\n (approximate error location; error nearby after) error calling parser: did not find expected key character 0 position 0 returned: 0", "path": "downloaded_repos/getformwork_formwork/panel/translations/pl.yaml"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/getformwork_formwork/formwork/translations/de.yaml:48:\n (approximate error location; error nearby after) error calling parser: found character that cannot start any token character 0 position 0 returned: 0", "path": "downloaded_repos/getformwork_formwork/formwork/translations/de.yaml"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/getformwork_formwork/panel/translations/pt.yaml:17:\n (approximate error location; error nearby after) error calling parser: found character that cannot start any token character 0 position 0 returned: 0", "path": "downloaded_repos/getformwork_formwork/panel/translations/pt.yaml"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/getformwork_formwork/panel/translations/de.yaml:28:\n (approximate error location; error nearby after) error calling parser: found character that cannot start any token character 0 position 0 returned: 0", "path": "downloaded_repos/getformwork_formwork/panel/translations/de.yaml"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/getformwork_formwork/panel/translations/es.yaml:95:\n (approximate error location; error nearby after) error calling parser: found character that cannot start any token character 0 position 0 returned: 0", "path": "downloaded_repos/getformwork_formwork/panel/translations/es.yaml"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/getformwork_formwork/panel/translations/it.yaml:119:\n (approximate error location; error nearby after) error calling parser: found character that cannot start any token character 0 position 0 returned: 0", "path": "downloaded_repos/getformwork_formwork/panel/translations/it.yaml"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/getformwork_formwork/panel/translations/fr.yaml:197:\n (approximate error location; error nearby after) error calling parser: found character that cannot start any token character 0 position 0 returned: 0", "path": "downloaded_repos/getformwork_formwork/panel/translations/fr.yaml"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/getformwork_formwork/formwork/translations/fr.yaml:95:\n (approximate error location; error nearby after) error calling parser: found character that cannot start any token character 0 position 0 returned: 0", "path": "downloaded_repos/getformwork_formwork/formwork/translations/fr.yaml"}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/.github/workflows/release.yaml", "start": {"line": 40, "col": 31, "offset": 730}, "end": {"line": 40, "col": 34, "offset": 733}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/.github/workflows/release.yaml:40:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/getformwork_formwork/.github/workflows/release.yaml", "spans": [{"file": "downloaded_repos/getformwork_formwork/.github/workflows/release.yaml", "start": {"line": 40, "col": 31, "offset": 730}, "end": {"line": 40, "col": 34, "offset": 733}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/config/views/methods.php", "start": {"line": 15, "col": 36, "offset": 0}, "end": {"line": 15, "col": 39, "offset": 3}}, {"path": "downloaded_repos/getformwork_formwork/formwork/config/views/methods.php", "start": {"line": 17, "col": 36, "offset": 0}, "end": {"line": 17, "col": 39, "offset": 3}}, {"path": "downloaded_repos/getformwork_formwork/formwork/config/views/methods.php", "start": {"line": 19, "col": 33, "offset": 0}, "end": {"line": 19, "col": 36, "offset": 3}}, {"path": "downloaded_repos/getformwork_formwork/formwork/config/views/methods.php", "start": {"line": 21, "col": 41, "offset": 0}, "end": {"line": 21, "col": 44, "offset": 3}}, {"path": "downloaded_repos/getformwork_formwork/formwork/config/views/methods.php", "start": {"line": 23, "col": 41, "offset": 0}, "end": {"line": 23, "col": 44, "offset": 3}}, {"path": "downloaded_repos/getformwork_formwork/formwork/config/views/methods.php", "start": {"line": 25, "col": 29, "offset": 0}, "end": {"line": 25, "col": 32, "offset": 3}}, {"path": "downloaded_repos/getformwork_formwork/formwork/config/views/methods.php", "start": {"line": 27, "col": 33, "offset": 0}, "end": {"line": 27, "col": 36, "offset": 3}}, {"path": "downloaded_repos/getformwork_formwork/formwork/config/views/methods.php", "start": {"line": 29, "col": 35, "offset": 0}, "end": {"line": 29, "col": 38, "offset": 3}}, {"path": "downloaded_repos/getformwork_formwork/formwork/config/views/methods.php", "start": {"line": 31, "col": 42, "offset": 0}, "end": {"line": 31, "col": 45, "offset": 3}}, {"path": "downloaded_repos/getformwork_formwork/formwork/config/views/methods.php", "start": {"line": 33, "col": 38, "offset": 0}, "end": {"line": 33, "col": 41, "offset": 3}}, {"path": "downloaded_repos/getformwork_formwork/formwork/config/views/methods.php", "start": {"line": 35, "col": 48, "offset": 0}, "end": {"line": 35, "col": 51, "offset": 3}}, {"path": "downloaded_repos/getformwork_formwork/formwork/config/views/methods.php", "start": {"line": 37, "col": 44, "offset": 0}, "end": {"line": 37, "col": 47, "offset": 3}}, {"path": "downloaded_repos/getformwork_formwork/formwork/config/views/methods.php", "start": {"line": 39, "col": 40, "offset": 0}, "end": {"line": 39, "col": 43, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/config/views/methods.php:15:\n `...` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/config/views/methods.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/config/views/methods.php", "start": {"line": 15, "col": 36, "offset": 0}, "end": {"line": 15, "col": 39, "offset": 3}}, {"file": "downloaded_repos/getformwork_formwork/formwork/config/views/methods.php", "start": {"line": 17, "col": 36, "offset": 0}, "end": {"line": 17, "col": 39, "offset": 3}}, {"file": "downloaded_repos/getformwork_formwork/formwork/config/views/methods.php", "start": {"line": 19, "col": 33, "offset": 0}, "end": {"line": 19, "col": 36, "offset": 3}}, {"file": "downloaded_repos/getformwork_formwork/formwork/config/views/methods.php", "start": {"line": 21, "col": 41, "offset": 0}, "end": {"line": 21, "col": 44, "offset": 3}}, {"file": "downloaded_repos/getformwork_formwork/formwork/config/views/methods.php", "start": {"line": 23, "col": 41, "offset": 0}, "end": {"line": 23, "col": 44, "offset": 3}}, {"file": "downloaded_repos/getformwork_formwork/formwork/config/views/methods.php", "start": {"line": 25, "col": 29, "offset": 0}, "end": {"line": 25, "col": 32, "offset": 3}}, {"file": "downloaded_repos/getformwork_formwork/formwork/config/views/methods.php", "start": {"line": 27, "col": 33, "offset": 0}, "end": {"line": 27, "col": 36, "offset": 3}}, {"file": "downloaded_repos/getformwork_formwork/formwork/config/views/methods.php", "start": {"line": 29, "col": 35, "offset": 0}, "end": {"line": 29, "col": 38, "offset": 3}}, {"file": "downloaded_repos/getformwork_formwork/formwork/config/views/methods.php", "start": {"line": 31, "col": 42, "offset": 0}, "end": {"line": 31, "col": 45, "offset": 3}}, {"file": "downloaded_repos/getformwork_formwork/formwork/config/views/methods.php", "start": {"line": 33, "col": 38, "offset": 0}, "end": {"line": 33, "col": 41, "offset": 3}}, {"file": "downloaded_repos/getformwork_formwork/formwork/config/views/methods.php", "start": {"line": 35, "col": 48, "offset": 0}, "end": {"line": 35, "col": 51, "offset": 3}}, {"file": "downloaded_repos/getformwork_formwork/formwork/config/views/methods.php", "start": {"line": 37, "col": 44, "offset": 0}, "end": {"line": 37, "col": 47, "offset": 3}}, {"file": "downloaded_repos/getformwork_formwork/formwork/config/views/methods.php", "start": {"line": 39, "col": 40, "offset": 0}, "end": {"line": 39, "col": 43, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/fields/dynamic/vars.php", "start": {"line": 25, "col": 61, "offset": 0}, "end": {"line": 25, "col": 64, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/fields/dynamic/vars.php:25:\n `...` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/fields/dynamic/vars.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/fields/dynamic/vars.php", "start": {"line": 25, "col": 61, "offset": 0}, "end": {"line": 25, "col": 64, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/fields/files.php", "start": {"line": 46, "col": 45, "offset": 0}, "end": {"line": 46, "col": 48, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/fields/files.php:46:\n `...` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/fields/files.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/fields/files.php", "start": {"line": 46, "col": 45, "offset": 0}, "end": {"line": 46, "col": 48, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/fields/images.php", "start": {"line": 48, "col": 45, "offset": 0}, "end": {"line": 48, "col": 48, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/fields/images.php:48:\n `...` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/fields/images.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/fields/images.php", "start": {"line": 48, "col": 45, "offset": 0}, "end": {"line": 48, "col": 48, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/fields/tags.php", "start": {"line": 27, "col": 45, "offset": 0}, "end": {"line": 27, "col": 48, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/fields/tags.php:27:\n `...` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/fields/tags.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/fields/tags.php", "start": {"line": 27, "col": 45, "offset": 0}, "end": {"line": 27, "col": 48, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/fields/upload.php", "start": {"line": 25, "col": 66, "offset": 0}, "end": {"line": 25, "col": 69, "offset": 3}}, {"path": "downloaded_repos/getformwork_formwork/formwork/fields/upload.php", "start": {"line": 78, "col": 90, "offset": 0}, "end": {"line": 78, "col": 93, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/fields/upload.php:25:\n `...` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/fields/upload.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/fields/upload.php", "start": {"line": 25, "col": 66, "offset": 0}, "end": {"line": 25, "col": 69, "offset": 3}}, {"file": "downloaded_repos/getformwork_formwork/formwork/fields/upload.php", "start": {"line": 78, "col": 90, "offset": 0}, "end": {"line": 78, "col": 93, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Cms/App.php", "start": {"line": 52, "col": 18, "offset": 0}, "end": {"line": 52, "col": 24, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Cms/App.php:52:\n `string` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Cms/App.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Cms/App.php", "start": {"line": 52, "col": 18, "offset": 0}, "end": {"line": 52, "col": 24, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Cms/Site.php", "start": {"line": 34, "col": 21, "offset": 0}, "end": {"line": 34, "col": 27, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Cms/Site.php:34:\n `string` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Cms/Site.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Cms/Site.php", "start": {"line": 34, "col": 21, "offset": 0}, "end": {"line": 34, "col": 27, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Config/Config.php", "start": {"line": 18, "col": 21, "offset": 0}, "end": {"line": 18, "col": 27, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Config/Config.php", "start": {"line": 71, "col": 33, "offset": 0}, "end": {"line": 71, "col": 40, "offset": 7}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Config/Config.php:18:\n `string` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Config/Config.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Config/Config.php", "start": {"line": 18, "col": 21, "offset": 0}, "end": {"line": 18, "col": 27, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Config/Config.php", "start": {"line": 71, "col": 33, "offset": 0}, "end": {"line": 71, "col": 40, "offset": 7}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Controllers/AbstractController.php", "start": {"line": 23, "col": 24, "offset": 0}, "end": {"line": 23, "col": 30, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Controllers/AbstractController.php", "start": {"line": 26, "col": 17, "offset": 0}, "end": {"line": 26, "col": 25, "offset": 8}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Controllers/AbstractController.php", "start": {"line": 27, "col": 28, "offset": 0}, "end": {"line": 27, "col": 31, "offset": 3}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Controllers/AbstractController.php", "start": {"line": 28, "col": 28, "offset": 0}, "end": {"line": 28, "col": 34, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Controllers/AbstractController.php", "start": {"line": 29, "col": 19, "offset": 0}, "end": {"line": 29, "col": 27, "offset": 8}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Controllers/AbstractController.php", "start": {"line": 30, "col": 28, "offset": 0}, "end": {"line": 30, "col": 35, "offset": 7}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Controllers/AbstractController.php", "start": {"line": 88, "col": 58, "offset": 0}, "end": {"line": 88, "col": 61, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Controllers/AbstractController.php:23:\n `string` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Controllers/AbstractController.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Controllers/AbstractController.php", "start": {"line": 23, "col": 24, "offset": 0}, "end": {"line": 23, "col": 30, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Controllers/AbstractController.php", "start": {"line": 26, "col": 17, "offset": 0}, "end": {"line": 26, "col": 25, "offset": 8}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Controllers/AbstractController.php", "start": {"line": 27, "col": 28, "offset": 0}, "end": {"line": 27, "col": 31, "offset": 3}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Controllers/AbstractController.php", "start": {"line": 28, "col": 28, "offset": 0}, "end": {"line": 28, "col": 34, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Controllers/AbstractController.php", "start": {"line": 29, "col": 19, "offset": 0}, "end": {"line": 29, "col": 27, "offset": 8}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Controllers/AbstractController.php", "start": {"line": 30, "col": 28, "offset": 0}, "end": {"line": 30, "col": 35, "offset": 7}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Controllers/AbstractController.php", "start": {"line": 88, "col": 58, "offset": 0}, "end": {"line": 88, "col": 61, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Controllers/PageController.php", "start": {"line": 26, "col": 52, "offset": 0}, "end": {"line": 26, "col": 55, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Controllers/PageController.php:26:\n `...` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Controllers/PageController.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Controllers/PageController.php", "start": {"line": 26, "col": 52, "offset": 0}, "end": {"line": 26, "col": 55, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Debug/CodeDumper.php", "start": {"line": 301, "col": 47, "offset": 0}, "end": {"line": 301, "col": 48, "offset": 1}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Debug/CodeDumper.php:301:\n `,` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Debug/CodeDumper.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Debug/CodeDumper.php", "start": {"line": 301, "col": 47, "offset": 0}, "end": {"line": 301, "col": 48, "offset": 1}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Debug/Debug.php", "start": {"line": 19, "col": 19, "offset": 0}, "end": {"line": 19, "col": 22, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Debug/Debug.php:19:\n `int` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Debug/Debug.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Debug/Debug.php", "start": {"line": 19, "col": 19, "offset": 0}, "end": {"line": 19, "col": 22, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Fields/Field.php", "start": {"line": 38, "col": 21, "offset": 0}, "end": {"line": 38, "col": 26, "offset": 5}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Fields/Field.php:38:\n `array` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Fields/Field.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Fields/Field.php", "start": {"line": 38, "col": 21, "offset": 0}, "end": {"line": 38, "col": 26, "offset": 5}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Files/File.php", "start": {"line": 21, "col": 18, "offset": 0}, "end": {"line": 21, "col": 24, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Files/File.php", "start": {"line": 26, "col": 21, "offset": 0}, "end": {"line": 26, "col": 27, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Files/File.php:21:\n `string` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Files/File.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Files/File.php", "start": {"line": 21, "col": 18, "offset": 0}, "end": {"line": 21, "col": 24, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Files/File.php", "start": {"line": 26, "col": 21, "offset": 0}, "end": {"line": 26, "col": 27, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Files/FileFactory.php", "start": {"line": 41, "col": 76, "offset": 0}, "end": {"line": 41, "col": 79, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Files/FileFactory.php:41:\n `...` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Files/FileFactory.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Files/FileFactory.php", "start": {"line": 41, "col": 76, "offset": 0}, "end": {"line": 41, "col": 79, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Http/Client.php", "start": {"line": 17, "col": 21, "offset": 0}, "end": {"line": 17, "col": 27, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Http/Client.php", "start": {"line": 22, "col": 21, "offset": 0}, "end": {"line": 22, "col": 27, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Http/Client.php:17:\n `string` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Http/Client.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Http/Client.php", "start": {"line": 17, "col": 21, "offset": 0}, "end": {"line": 17, "col": 27, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Http/Client.php", "start": {"line": 22, "col": 21, "offset": 0}, "end": {"line": 22, "col": 27, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Http/FileResponse.php", "start": {"line": 14, "col": 21, "offset": 0}, "end": {"line": 14, "col": 24, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Http/FileResponse.php:14:\n `int` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Http/FileResponse.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Http/FileResponse.php", "start": {"line": 14, "col": 21, "offset": 0}, "end": {"line": 14, "col": 24, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Http/Header.php", "start": {"line": 97, "col": 59, "offset": 0}, "end": {"line": 97, "col": 62, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Http/Header.php:97:\n `...` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Http/Header.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Http/Header.php", "start": {"line": 97, "col": 59, "offset": 0}, "end": {"line": 97, "col": 62, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Http/Request.php", "start": {"line": 19, "col": 18, "offset": 0}, "end": {"line": 19, "col": 23, "offset": 5}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Http/Request.php", "start": {"line": 26, "col": 21, "offset": 0}, "end": {"line": 26, "col": 26, "offset": 5}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Http/Request.php", "start": {"line": 33, "col": 21, "offset": 0}, "end": {"line": 33, "col": 26, "offset": 5}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Http/Request.php", "start": {"line": 473, "col": 53, "offset": 0}, "end": {"line": 473, "col": 56, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Http/Request.php:19:\n `array` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Http/Request.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Http/Request.php", "start": {"line": 19, "col": 18, "offset": 0}, "end": {"line": 19, "col": 23, "offset": 5}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Http/Request.php", "start": {"line": 26, "col": 21, "offset": 0}, "end": {"line": 26, "col": 26, "offset": 5}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Http/Request.php", "start": {"line": 33, "col": 21, "offset": 0}, "end": {"line": 33, "col": 26, "offset": 5}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Http/Request.php", "start": {"line": 473, "col": 53, "offset": 0}, "end": {"line": 473, "col": 56, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Http/Session/Session.php", "start": {"line": 31, "col": 21, "offset": 0}, "end": {"line": 31, "col": 27, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Http/Session/Session.php", "start": {"line": 36, "col": 21, "offset": 0}, "end": {"line": 36, "col": 27, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Http/Session/Session.php", "start": {"line": 41, "col": 21, "offset": 0}, "end": {"line": 41, "col": 27, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Http/Session/Session.php:31:\n `string` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Http/Session/Session.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Http/Session/Session.php", "start": {"line": 31, "col": 21, "offset": 0}, "end": {"line": 31, "col": 27, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Http/Session/Session.php", "start": {"line": 36, "col": 21, "offset": 0}, "end": {"line": 36, "col": 27, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Http/Session/Session.php", "start": {"line": 41, "col": 21, "offset": 0}, "end": {"line": 41, "col": 27, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Http/Utils/Cookie.php", "start": {"line": 16, "col": 18, "offset": 0}, "end": {"line": 16, "col": 24, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Http/Utils/Cookie.php", "start": {"line": 21, "col": 18, "offset": 0}, "end": {"line": 21, "col": 24, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Http/Utils/Cookie.php", "start": {"line": 26, "col": 18, "offset": 0}, "end": {"line": 26, "col": 24, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Http/Utils/Cookie.php", "start": {"line": 31, "col": 19, "offset": 0}, "end": {"line": 31, "col": 25, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Http/Utils/Cookie.php:16:\n `string` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Http/Utils/Cookie.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Http/Utils/Cookie.php", "start": {"line": 16, "col": 18, "offset": 0}, "end": {"line": 16, "col": 24, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Http/Utils/Cookie.php", "start": {"line": 21, "col": 18, "offset": 0}, "end": {"line": 21, "col": 24, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Http/Utils/Cookie.php", "start": {"line": 26, "col": 18, "offset": 0}, "end": {"line": 26, "col": 24, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Http/Utils/Cookie.php", "start": {"line": 31, "col": 19, "offset": 0}, "end": {"line": 31, "col": 25, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Http/Utils/IpAnonymizer.php", "start": {"line": 15, "col": 19, "offset": 0}, "end": {"line": 15, "col": 25, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Http/Utils/IpAnonymizer.php", "start": {"line": 20, "col": 19, "offset": 0}, "end": {"line": 20, "col": 25, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Http/Utils/IpAnonymizer.php:15:\n `string` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Http/Utils/IpAnonymizer.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Http/Utils/IpAnonymizer.php", "start": {"line": 15, "col": 19, "offset": 0}, "end": {"line": 15, "col": 25, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Http/Utils/IpAnonymizer.php", "start": {"line": 20, "col": 19, "offset": 0}, "end": {"line": 20, "col": 25, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/ColorProfile/ColorProfile.php", "start": {"line": 14, "col": 21, "offset": 0}, "end": {"line": 14, "col": 27, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/ColorProfile/ColorProfile.php", "start": {"line": 19, "col": 21, "offset": 0}, "end": {"line": 19, "col": 24, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Images/ColorProfile/ColorProfile.php:14:\n `string` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Images/ColorProfile/ColorProfile.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Images/ColorProfile/ColorProfile.php", "start": {"line": 14, "col": 21, "offset": 0}, "end": {"line": 14, "col": 27, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Images/ColorProfile/ColorProfile.php", "start": {"line": 19, "col": 21, "offset": 0}, "end": {"line": 19, "col": 24, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Decoder/GifDecoder.php", "start": {"line": 16, "col": 19, "offset": 0}, "end": {"line": 16, "col": 24, "offset": 5}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Images/Decoder/GifDecoder.php:16:\n `array` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Decoder/GifDecoder.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Images/Decoder/GifDecoder.php", "start": {"line": 16, "col": 19, "offset": 0}, "end": {"line": 16, "col": 24, "offset": 5}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Decoder/PngDecoder.php", "start": {"line": 14, "col": 19, "offset": 0}, "end": {"line": 14, "col": 25, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Images/Decoder/PngDecoder.php:14:\n `string` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Decoder/PngDecoder.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Images/Decoder/PngDecoder.php", "start": {"line": 14, "col": 19, "offset": 0}, "end": {"line": 14, "col": 25, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Decoder/WebpDecoder.php", "start": {"line": 14, "col": 19, "offset": 0}, "end": {"line": 14, "col": 25, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Decoder/WebpDecoder.php", "start": {"line": 19, "col": 19, "offset": 0}, "end": {"line": 19, "col": 25, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Images/Decoder/WebpDecoder.php:14:\n `string` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Decoder/WebpDecoder.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Images/Decoder/WebpDecoder.php", "start": {"line": 14, "col": 19, "offset": 0}, "end": {"line": 14, "col": 25, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Images/Decoder/WebpDecoder.php", "start": {"line": 19, "col": 19, "offset": 0}, "end": {"line": 19, "col": 25, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Exif/ExifDateTime.php", "start": {"line": 13, "col": 25, "offset": 0}, "end": {"line": 13, "col": 29, "offset": 4}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Exif/ExifDateTime.php", "start": {"line": 18, "col": 18, "offset": 0}, "end": {"line": 18, "col": 24, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Images/Exif/ExifDateTime.php:13:\n `EXIF` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Exif/ExifDateTime.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Images/Exif/ExifDateTime.php", "start": {"line": 13, "col": 25, "offset": 0}, "end": {"line": 13, "col": 29, "offset": 4}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Images/Exif/ExifDateTime.php", "start": {"line": 18, "col": 18, "offset": 0}, "end": {"line": 18, "col": 24, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Exif/ExifReader.php", "start": {"line": 14, "col": 21, "offset": 0}, "end": {"line": 14, "col": 27, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Exif/ExifReader.php", "start": {"line": 19, "col": 21, "offset": 0}, "end": {"line": 19, "col": 27, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Exif/ExifReader.php", "start": {"line": 24, "col": 21, "offset": 0}, "end": {"line": 24, "col": 27, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Exif/ExifReader.php", "start": {"line": 29, "col": 21, "offset": 0}, "end": {"line": 29, "col": 27, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Exif/ExifReader.php", "start": {"line": 34, "col": 21, "offset": 0}, "end": {"line": 34, "col": 27, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Exif/ExifReader.php", "start": {"line": 39, "col": 21, "offset": 0}, "end": {"line": 39, "col": 27, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Exif/ExifReader.php", "start": {"line": 46, "col": 21, "offset": 0}, "end": {"line": 46, "col": 26, "offset": 5}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Exif/ExifReader.php", "start": {"line": 65, "col": 21, "offset": 0}, "end": {"line": 65, "col": 26, "offset": 5}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Exif/ExifReader.php", "start": {"line": 96, "col": 21, "offset": 0}, "end": {"line": 96, "col": 26, "offset": 5}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Images/Exif/ExifReader.php:14:\n `string` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Exif/ExifReader.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Images/Exif/ExifReader.php", "start": {"line": 14, "col": 21, "offset": 0}, "end": {"line": 14, "col": 27, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Images/Exif/ExifReader.php", "start": {"line": 19, "col": 21, "offset": 0}, "end": {"line": 19, "col": 27, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Images/Exif/ExifReader.php", "start": {"line": 24, "col": 21, "offset": 0}, "end": {"line": 24, "col": 27, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Images/Exif/ExifReader.php", "start": {"line": 29, "col": 21, "offset": 0}, "end": {"line": 29, "col": 27, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Images/Exif/ExifReader.php", "start": {"line": 34, "col": 21, "offset": 0}, "end": {"line": 34, "col": 27, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Images/Exif/ExifReader.php", "start": {"line": 39, "col": 21, "offset": 0}, "end": {"line": 39, "col": 27, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Images/Exif/ExifReader.php", "start": {"line": 46, "col": 21, "offset": 0}, "end": {"line": 46, "col": 26, "offset": 5}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Images/Exif/ExifReader.php", "start": {"line": 65, "col": 21, "offset": 0}, "end": {"line": 65, "col": 26, "offset": 5}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Images/Exif/ExifReader.php", "start": {"line": 96, "col": 21, "offset": 0}, "end": {"line": 96, "col": 26, "offset": 5}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/GifHandler.php", "start": {"line": 20, "col": 19, "offset": 0}, "end": {"line": 20, "col": 25, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/GifHandler.php:20:\n `string` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/GifHandler.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/GifHandler.php", "start": {"line": 20, "col": 19, "offset": 0}, "end": {"line": 20, "col": 25, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/JpegHandler.php", "start": {"line": 19, "col": 19, "offset": 0}, "end": {"line": 19, "col": 22, "offset": 3}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/JpegHandler.php", "start": {"line": 24, "col": 19, "offset": 0}, "end": {"line": 24, "col": 25, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/JpegHandler.php", "start": {"line": 29, "col": 19, "offset": 0}, "end": {"line": 29, "col": 25, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/JpegHandler.php:19:\n `int` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/JpegHandler.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/JpegHandler.php", "start": {"line": 19, "col": 19, "offset": 0}, "end": {"line": 19, "col": 22, "offset": 3}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/JpegHandler.php", "start": {"line": 24, "col": 19, "offset": 0}, "end": {"line": 24, "col": 25, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/JpegHandler.php", "start": {"line": 29, "col": 19, "offset": 0}, "end": {"line": 29, "col": 25, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/WebpHandler.php", "start": {"line": 20, "col": 19, "offset": 0}, "end": {"line": 20, "col": 25, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/WebpHandler.php", "start": {"line": 25, "col": 19, "offset": 0}, "end": {"line": 25, "col": 22, "offset": 3}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/WebpHandler.php", "start": {"line": 30, "col": 19, "offset": 0}, "end": {"line": 30, "col": 22, "offset": 3}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/WebpHandler.php", "start": {"line": 35, "col": 19, "offset": 0}, "end": {"line": 35, "col": 22, "offset": 3}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/WebpHandler.php", "start": {"line": 40, "col": 19, "offset": 0}, "end": {"line": 40, "col": 22, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/WebpHandler.php:20:\n `string` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/WebpHandler.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/WebpHandler.php", "start": {"line": 20, "col": 19, "offset": 0}, "end": {"line": 20, "col": 25, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/WebpHandler.php", "start": {"line": 25, "col": 19, "offset": 0}, "end": {"line": 25, "col": 22, "offset": 3}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/WebpHandler.php", "start": {"line": 30, "col": 19, "offset": 0}, "end": {"line": 30, "col": 22, "offset": 3}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/WebpHandler.php", "start": {"line": 35, "col": 19, "offset": 0}, "end": {"line": 35, "col": 22, "offset": 3}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/WebpHandler.php", "start": {"line": 40, "col": 19, "offset": 0}, "end": {"line": 40, "col": 22, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Image.php", "start": {"line": 41, "col": 18, "offset": 0}, "end": {"line": 41, "col": 24, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Image.php", "start": {"line": 43, "col": 21, "offset": 0}, "end": {"line": 43, "col": 27, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Images/Image.php:41:\n `string` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Image.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Images/Image.php", "start": {"line": 41, "col": 18, "offset": 0}, "end": {"line": 41, "col": 24, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Images/Image.php", "start": {"line": 43, "col": 21, "offset": 0}, "end": {"line": 43, "col": 27, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Interpolator/NodeInterpolator.php", "start": {"line": 101, "col": 28, "offset": 0}, "end": {"line": 101, "col": 46, "offset": 18}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Interpolator/NodeInterpolator.php:101:\n `= $parent::{$name}` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Interpolator/NodeInterpolator.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Interpolator/NodeInterpolator.php", "start": {"line": 101, "col": 28, "offset": 0}, "end": {"line": 101, "col": 46, "offset": 18}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Interpolator/Nodes/NumberNode.php", "start": {"line": 7, "col": 25, "offset": 0}, "end": {"line": 7, "col": 29, "offset": 4}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Interpolator/Nodes/NumberNode.php:7:\n `TYPE` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Interpolator/Nodes/NumberNode.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Interpolator/Nodes/NumberNode.php", "start": {"line": 7, "col": 25, "offset": 0}, "end": {"line": 7, "col": 29, "offset": 4}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Model/Model.php", "start": {"line": 24, "col": 21, "offset": 0}, "end": {"line": 24, "col": 27, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Model/Model.php:24:\n `string` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Model/Model.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Model/Model.php", "start": {"line": 24, "col": 21, "offset": 0}, "end": {"line": 24, "col": 27, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Pages/Page.php", "start": {"line": 44, "col": 18, "offset": 0}, "end": {"line": 44, "col": 24, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Pages/Page.php", "start": {"line": 49, "col": 18, "offset": 0}, "end": {"line": 49, "col": 24, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Pages/Page.php", "start": {"line": 54, "col": 18, "offset": 0}, "end": {"line": 54, "col": 24, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Pages/Page.php", "start": {"line": 56, "col": 21, "offset": 0}, "end": {"line": 56, "col": 27, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Pages/Page.php", "start": {"line": 63, "col": 21, "offset": 0}, "end": {"line": 63, "col": 26, "offset": 5}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Pages/Page.php", "start": {"line": 70, "col": 21, "offset": 0}, "end": {"line": 70, "col": 26, "offset": 5}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Pages/Page.php", "start": {"line": 75, "col": 21, "offset": 0}, "end": {"line": 75, "col": 27, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Pages/Page.php", "start": {"line": 80, "col": 21, "offset": 0}, "end": {"line": 80, "col": 27, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Pages/Page.php:44:\n `string` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Pages/Page.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Pages/Page.php", "start": {"line": 44, "col": 18, "offset": 0}, "end": {"line": 44, "col": 24, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Pages/Page.php", "start": {"line": 49, "col": 18, "offset": 0}, "end": {"line": 49, "col": 24, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Pages/Page.php", "start": {"line": 54, "col": 18, "offset": 0}, "end": {"line": 54, "col": 24, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Pages/Page.php", "start": {"line": 56, "col": 21, "offset": 0}, "end": {"line": 56, "col": 27, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Pages/Page.php", "start": {"line": 63, "col": 21, "offset": 0}, "end": {"line": 63, "col": 26, "offset": 5}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Pages/Page.php", "start": {"line": 70, "col": 21, "offset": 0}, "end": {"line": 70, "col": 26, "offset": 5}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Pages/Page.php", "start": {"line": 75, "col": 21, "offset": 0}, "end": {"line": 75, "col": 27, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Pages/Page.php", "start": {"line": 80, "col": 21, "offset": 0}, "end": {"line": 80, "col": 27, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/ContentHistory/ContentHistory.php", "start": {"line": 14, "col": 18, "offset": 0}, "end": {"line": 14, "col": 24, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/ContentHistory/ContentHistory.php", "start": {"line": 19, "col": 18, "offset": 0}, "end": {"line": 19, "col": 21, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Panel/ContentHistory/ContentHistory.php:14:\n `string` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/ContentHistory/ContentHistory.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Panel/ContentHistory/ContentHistory.php", "start": {"line": 14, "col": 18, "offset": 0}, "end": {"line": 14, "col": 24, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Panel/ContentHistory/ContentHistory.php", "start": {"line": 19, "col": 18, "offset": 0}, "end": {"line": 19, "col": 21, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/AbstractController.php", "start": {"line": 20, "col": 28, "offset": 0}, "end": {"line": 20, "col": 34, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/AbstractController.php", "start": {"line": 21, "col": 19, "offset": 0}, "end": {"line": 21, "col": 27, "offset": 8}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/AbstractController.php", "start": {"line": 22, "col": 19, "offset": 0}, "end": {"line": 22, "col": 27, "offset": 8}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/AbstractController.php", "start": {"line": 23, "col": 19, "offset": 0}, "end": {"line": 23, "col": 27, "offset": 8}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/AbstractController.php", "start": {"line": 24, "col": 28, "offset": 0}, "end": {"line": 24, "col": 32, "offset": 4}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/AbstractController.php", "start": {"line": 25, "col": 28, "offset": 0}, "end": {"line": 25, "col": 33, "offset": 5}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/AbstractController.php", "start": {"line": 27, "col": 52, "offset": 0}, "end": {"line": 27, "col": 55, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/AbstractController.php:20:\n `Router` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/AbstractController.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/AbstractController.php", "start": {"line": 20, "col": 28, "offset": 0}, "end": {"line": 20, "col": 34, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/AbstractController.php", "start": {"line": 21, "col": 19, "offset": 0}, "end": {"line": 21, "col": 27, "offset": 8}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/AbstractController.php", "start": {"line": 22, "col": 19, "offset": 0}, "end": {"line": 22, "col": 27, "offset": 8}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/AbstractController.php", "start": {"line": 23, "col": 19, "offset": 0}, "end": {"line": 23, "col": 27, "offset": 8}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/AbstractController.php", "start": {"line": 24, "col": 28, "offset": 0}, "end": {"line": 24, "col": 32, "offset": 4}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/AbstractController.php", "start": {"line": 25, "col": 28, "offset": 0}, "end": {"line": 25, "col": 33, "offset": 5}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/AbstractController.php", "start": {"line": 27, "col": 52, "offset": 0}, "end": {"line": 27, "col": 55, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/AuthenticationController.php", "start": {"line": 23, "col": 18, "offset": 0}, "end": {"line": 23, "col": 24, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/AuthenticationController.php:23:\n `string` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/AuthenticationController.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/AuthenticationController.php", "start": {"line": 23, "col": 18, "offset": 0}, "end": {"line": 23, "col": 24, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Panel.php", "start": {"line": 26, "col": 19, "offset": 0}, "end": {"line": 26, "col": 25, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Panel.php", "start": {"line": 39, "col": 17, "offset": 0}, "end": {"line": 39, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Panel/Panel.php:26:\n `string` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Panel.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Panel.php", "start": {"line": 26, "col": 19, "offset": 0}, "end": {"line": 26, "col": 25, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Panel.php", "start": {"line": 39, "col": 17, "offset": 0}, "end": {"line": 39, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Parsers/Json.php", "start": {"line": 10, "col": 19, "offset": 0}, "end": {"line": 10, "col": 22, "offset": 3}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Parsers/Json.php", "start": {"line": 15, "col": 19, "offset": 0}, "end": {"line": 15, "col": 22, "offset": 3}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Parsers/Json.php", "start": {"line": 22, "col": 19, "offset": 0}, "end": {"line": 22, "col": 24, "offset": 5}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Parsers/Json.php:10:\n `int` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Parsers/Json.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Parsers/Json.php", "start": {"line": 10, "col": 19, "offset": 0}, "end": {"line": 10, "col": 22, "offset": 3}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Parsers/Json.php", "start": {"line": 15, "col": 19, "offset": 0}, "end": {"line": 15, "col": 22, "offset": 3}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Parsers/Json.php", "start": {"line": 22, "col": 19, "offset": 0}, "end": {"line": 22, "col": 24, "offset": 5}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Router/Router.php", "start": {"line": 27, "col": 21, "offset": 0}, "end": {"line": 27, "col": 26, "offset": 5}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Router/Router.php", "start": {"line": 34, "col": 21, "offset": 0}, "end": {"line": 34, "col": 26, "offset": 5}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Router/Router.php", "start": {"line": 39, "col": 21, "offset": 0}, "end": {"line": 39, "col": 27, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Router/Router.php", "start": {"line": 44, "col": 21, "offset": 0}, "end": {"line": 44, "col": 27, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Router/Router.php", "start": {"line": 51, "col": 21, "offset": 0}, "end": {"line": 51, "col": 26, "offset": 5}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Router/Router.php", "start": {"line": 414, "col": 36, "offset": 0}, "end": {"line": 414, "col": 39, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Router/Router.php:27:\n `array` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Router/Router.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Router/Router.php", "start": {"line": 27, "col": 21, "offset": 0}, "end": {"line": 27, "col": 26, "offset": 5}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Router/Router.php", "start": {"line": 34, "col": 21, "offset": 0}, "end": {"line": 34, "col": 26, "offset": 5}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Router/Router.php", "start": {"line": 39, "col": 21, "offset": 0}, "end": {"line": 39, "col": 27, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Router/Router.php", "start": {"line": 44, "col": 21, "offset": 0}, "end": {"line": 44, "col": 27, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Router/Router.php", "start": {"line": 51, "col": 21, "offset": 0}, "end": {"line": 51, "col": 26, "offset": 5}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Router/Router.php", "start": {"line": 414, "col": 36, "offset": 0}, "end": {"line": 414, "col": 39, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Statistics/Statistics.php", "start": {"line": 22, "col": 19, "offset": 0}, "end": {"line": 22, "col": 25, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Statistics/Statistics.php", "start": {"line": 27, "col": 19, "offset": 0}, "end": {"line": 27, "col": 22, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Statistics/Statistics.php:22:\n `string` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Statistics/Statistics.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Statistics/Statistics.php", "start": {"line": 22, "col": 19, "offset": 0}, "end": {"line": 22, "col": 25, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Statistics/Statistics.php", "start": {"line": 27, "col": 19, "offset": 0}, "end": {"line": 27, "col": 22, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Users/<USER>", "start": {"line": 21, "col": 18, "offset": 0}, "end": {"line": 21, "col": 24, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Users/<USER>", "start": {"line": 23, "col": 21, "offset": 0}, "end": {"line": 23, "col": 27, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Users/<USER>\n `string` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Users/<USER>", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Users/<USER>", "start": {"line": 21, "col": 18, "offset": 0}, "end": {"line": 21, "col": 24, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Users/<USER>", "start": {"line": 23, "col": 21, "offset": 0}, "end": {"line": 23, "col": 27, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Constraint.php", "start": {"line": 16, "col": 18, "offset": 0}, "end": {"line": 16, "col": 23, "offset": 5}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Constraint.php", "start": {"line": 23, "col": 18, "offset": 0}, "end": {"line": 23, "col": 23, "offset": 5}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Constraint.php", "start": {"line": 30, "col": 18, "offset": 0}, "end": {"line": 30, "col": 23, "offset": 5}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Utils/Constraint.php:16:\n `array` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Constraint.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Constraint.php", "start": {"line": 16, "col": 18, "offset": 0}, "end": {"line": 16, "col": 23, "offset": 5}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Constraint.php", "start": {"line": 23, "col": 18, "offset": 0}, "end": {"line": 23, "col": 23, "offset": 5}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Constraint.php", "start": {"line": 30, "col": 18, "offset": 0}, "end": {"line": 30, "col": 23, "offset": 5}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Date.php", "start": {"line": 19, "col": 19, "offset": 0}, "end": {"line": 19, "col": 25, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Date.php", "start": {"line": 24, "col": 19, "offset": 0}, "end": {"line": 24, "col": 25, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Date.php", "start": {"line": 29, "col": 19, "offset": 0}, "end": {"line": 29, "col": 25, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Date.php", "start": {"line": 36, "col": 19, "offset": 0}, "end": {"line": 36, "col": 24, "offset": 5}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Date.php", "start": {"line": 71, "col": 19, "offset": 0}, "end": {"line": 71, "col": 24, "offset": 5}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Utils/Date.php:19:\n `string` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Date.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Date.php", "start": {"line": 19, "col": 19, "offset": 0}, "end": {"line": 19, "col": 25, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Date.php", "start": {"line": 24, "col": 19, "offset": 0}, "end": {"line": 24, "col": 25, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Date.php", "start": {"line": 29, "col": 19, "offset": 0}, "end": {"line": 29, "col": 25, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Date.php", "start": {"line": 36, "col": 19, "offset": 0}, "end": {"line": 36, "col": 24, "offset": 5}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Date.php", "start": {"line": 71, "col": 19, "offset": 0}, "end": {"line": 71, "col": 24, "offset": 5}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/FileSystem.php", "start": {"line": 18, "col": 18, "offset": 0}, "end": {"line": 18, "col": 21, "offset": 3}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/FileSystem.php", "start": {"line": 23, "col": 18, "offset": 0}, "end": {"line": 23, "col": 21, "offset": 3}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/FileSystem.php", "start": {"line": 28, "col": 18, "offset": 0}, "end": {"line": 28, "col": 21, "offset": 3}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/FileSystem.php", "start": {"line": 33, "col": 18, "offset": 0}, "end": {"line": 33, "col": 21, "offset": 3}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/FileSystem.php", "start": {"line": 38, "col": 18, "offset": 0}, "end": {"line": 38, "col": 21, "offset": 3}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/FileSystem.php", "start": {"line": 43, "col": 18, "offset": 0}, "end": {"line": 43, "col": 21, "offset": 3}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/FileSystem.php", "start": {"line": 48, "col": 18, "offset": 0}, "end": {"line": 48, "col": 21, "offset": 3}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/FileSystem.php", "start": {"line": 53, "col": 18, "offset": 0}, "end": {"line": 53, "col": 21, "offset": 3}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/FileSystem.php", "start": {"line": 58, "col": 19, "offset": 0}, "end": {"line": 58, "col": 22, "offset": 3}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/FileSystem.php", "start": {"line": 63, "col": 19, "offset": 0}, "end": {"line": 63, "col": 22, "offset": 3}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/FileSystem.php", "start": {"line": 70, "col": 19, "offset": 0}, "end": {"line": 70, "col": 24, "offset": 5}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/FileSystem.php", "start": {"line": 77, "col": 19, "offset": 0}, "end": {"line": 77, "col": 24, "offset": 5}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Utils/FileSystem.php:18:\n `int` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/FileSystem.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Utils/FileSystem.php", "start": {"line": 18, "col": 18, "offset": 0}, "end": {"line": 18, "col": 21, "offset": 3}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Utils/FileSystem.php", "start": {"line": 23, "col": 18, "offset": 0}, "end": {"line": 23, "col": 21, "offset": 3}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Utils/FileSystem.php", "start": {"line": 28, "col": 18, "offset": 0}, "end": {"line": 28, "col": 21, "offset": 3}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Utils/FileSystem.php", "start": {"line": 33, "col": 18, "offset": 0}, "end": {"line": 33, "col": 21, "offset": 3}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Utils/FileSystem.php", "start": {"line": 38, "col": 18, "offset": 0}, "end": {"line": 38, "col": 21, "offset": 3}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Utils/FileSystem.php", "start": {"line": 43, "col": 18, "offset": 0}, "end": {"line": 43, "col": 21, "offset": 3}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Utils/FileSystem.php", "start": {"line": 48, "col": 18, "offset": 0}, "end": {"line": 48, "col": 21, "offset": 3}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Utils/FileSystem.php", "start": {"line": 53, "col": 18, "offset": 0}, "end": {"line": 53, "col": 21, "offset": 3}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Utils/FileSystem.php", "start": {"line": 58, "col": 19, "offset": 0}, "end": {"line": 58, "col": 22, "offset": 3}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Utils/FileSystem.php", "start": {"line": 63, "col": 19, "offset": 0}, "end": {"line": 63, "col": 22, "offset": 3}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Utils/FileSystem.php", "start": {"line": 70, "col": 19, "offset": 0}, "end": {"line": 70, "col": 24, "offset": 5}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Utils/FileSystem.php", "start": {"line": 77, "col": 19, "offset": 0}, "end": {"line": 77, "col": 24, "offset": 5}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/MimeType.php", "start": {"line": 17, "col": 19, "offset": 0}, "end": {"line": 17, "col": 25, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/MimeType.php", "start": {"line": 22, "col": 19, "offset": 0}, "end": {"line": 22, "col": 24, "offset": 5}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/MimeType.php", "start": {"line": 33, "col": 19, "offset": 0}, "end": {"line": 33, "col": 24, "offset": 5}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Utils/MimeType.php:17:\n `string` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/MimeType.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Utils/MimeType.php", "start": {"line": 17, "col": 19, "offset": 0}, "end": {"line": 17, "col": 25, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Utils/MimeType.php", "start": {"line": 22, "col": 19, "offset": 0}, "end": {"line": 22, "col": 24, "offset": 5}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Utils/MimeType.php", "start": {"line": 33, "col": 19, "offset": 0}, "end": {"line": 33, "col": 24, "offset": 5}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Str.php", "start": {"line": 19, "col": 19, "offset": 0}, "end": {"line": 19, "col": 24, "offset": 5}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Str.php", "start": {"line": 143, "col": 19, "offset": 0}, "end": {"line": 143, "col": 25, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Utils/Str.php:19:\n `array` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Str.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Str.php", "start": {"line": 19, "col": 19, "offset": 0}, "end": {"line": 19, "col": 24, "offset": 5}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Str.php", "start": {"line": 143, "col": 19, "offset": 0}, "end": {"line": 143, "col": 25, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Text.php", "start": {"line": 15, "col": 19, "offset": 0}, "end": {"line": 15, "col": 25, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Text.php", "start": {"line": 20, "col": 19, "offset": 0}, "end": {"line": 20, "col": 25, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Text.php", "start": {"line": 25, "col": 19, "offset": 0}, "end": {"line": 25, "col": 25, "offset": 6}}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Text.php", "start": {"line": 30, "col": 19, "offset": 0}, "end": {"line": 30, "col": 22, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Utils/Text.php:15:\n `string` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Text.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Text.php", "start": {"line": 15, "col": 19, "offset": 0}, "end": {"line": 15, "col": 25, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Text.php", "start": {"line": 20, "col": 19, "offset": 0}, "end": {"line": 20, "col": 25, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Text.php", "start": {"line": 25, "col": 19, "offset": 0}, "end": {"line": 25, "col": 25, "offset": 6}}, {"file": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Text.php", "start": {"line": 30, "col": 19, "offset": 0}, "end": {"line": 30, "col": 22, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Uri.php", "start": {"line": 17, "col": 19, "offset": 0}, "end": {"line": 17, "col": 24, "offset": 5}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Utils/Uri.php:17:\n `array` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Uri.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Uri.php", "start": {"line": 17, "col": 19, "offset": 0}, "end": {"line": 17, "col": 24, "offset": 5}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/scripts/update-mime-types.php", "start": {"line": 53, "col": 38, "offset": 0}, "end": {"line": 53, "col": 41, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/Utils/scripts/update-mime-types.php:53:\n `...` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/scripts/update-mime-types.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/Utils/scripts/update-mime-types.php", "start": {"line": 53, "col": 38, "offset": 0}, "end": {"line": 53, "col": 41, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/formwork/src/View/View.php", "start": {"line": 20, "col": 28, "offset": 0}, "end": {"line": 20, "col": 32, "offset": 4}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/formwork/src/View/View.php:20:\n `TYPE` was unexpected", "path": "downloaded_repos/getformwork_formwork/formwork/src/View/View.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/formwork/src/View/View.php", "start": {"line": 20, "col": 28, "offset": 0}, "end": {"line": 20, "col": 32, "offset": 4}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/getformwork_formwork/panel/config/views/methods.php", "start": {"line": 7, "col": 36, "offset": 0}, "end": {"line": 7, "col": 39, "offset": 3}}, {"path": "downloaded_repos/getformwork_formwork/panel/config/views/methods.php", "start": {"line": 9, "col": 36, "offset": 0}, "end": {"line": 9, "col": 39, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/getformwork_formwork/panel/config/views/methods.php:7:\n `...` was unexpected", "path": "downloaded_repos/getformwork_formwork/panel/config/views/methods.php", "spans": [{"file": "downloaded_repos/getformwork_formwork/panel/config/views/methods.php", "start": {"line": 7, "col": 36, "offset": 0}, "end": {"line": 7, "col": 39, "offset": 3}}, {"file": "downloaded_repos/getformwork_formwork/panel/config/views/methods.php", "start": {"line": 9, "col": 36, "offset": 0}, "end": {"line": 9, "col": 39, "offset": 3}}]}], "paths": {"scanned": ["downloaded_repos/getformwork_formwork/.editorconfig", "downloaded_repos/getformwork_formwork/.github/dependabot.yml", "downloaded_repos/getformwork_formwork/.github/workflows/check.yaml", "downloaded_repos/getformwork_formwork/.github/workflows/panel.yaml", "downloaded_repos/getformwork_formwork/.github/workflows/release.yaml", "downloaded_repos/getformwork_formwork/.gitignore", "downloaded_repos/getformwork_formwork/.htaccess", "downloaded_repos/getformwork_formwork/.vscode/extensions.json", "downloaded_repos/getformwork_formwork/.vscode/settings.json", "downloaded_repos/getformwork_formwork/CHANGELOG.md", "downloaded_repos/getformwork_formwork/CONTRIBUTING.md", "downloaded_repos/getformwork_formwork/LICENSE", "downloaded_repos/getformwork_formwork/README.md", "downloaded_repos/getformwork_formwork/SECURITY.md", "downloaded_repos/getformwork_formwork/backup/.gitkeep", "downloaded_repos/getformwork_formwork/bin/serve", "downloaded_repos/getformwork_formwork/cache/.gitkeep", "downloaded_repos/getformwork_formwork/composer.json", "downloaded_repos/getformwork_formwork/composer.lock", "downloaded_repos/getformwork_formwork/formwork/.php-cs-fixer.php", "downloaded_repos/getformwork_formwork/formwork/.rector.php", "downloaded_repos/getformwork_formwork/formwork/bootstrap.php", "downloaded_repos/getformwork_formwork/formwork/config/routes/routes.php", "downloaded_repos/getformwork_formwork/formwork/config/site.yaml", "downloaded_repos/getformwork_formwork/formwork/config/system.yaml", "downloaded_repos/getformwork_formwork/formwork/config/views/methods.php", "downloaded_repos/getformwork_formwork/formwork/fields/array.php", "downloaded_repos/getformwork_formwork/formwork/fields/checkbox.php", "downloaded_repos/getformwork_formwork/formwork/fields/color.php", "downloaded_repos/getformwork_formwork/formwork/fields/date.php", "downloaded_repos/getformwork_formwork/formwork/fields/duration.php", "downloaded_repos/getformwork_formwork/formwork/fields/dynamic/vars.php", "downloaded_repos/getformwork_formwork/formwork/fields/email.php", "downloaded_repos/getformwork_formwork/formwork/fields/file.php", "downloaded_repos/getformwork_formwork/formwork/fields/files.php", "downloaded_repos/getformwork_formwork/formwork/fields/image.php", "downloaded_repos/getformwork_formwork/formwork/fields/images.php", "downloaded_repos/getformwork_formwork/formwork/fields/markdown.php", "downloaded_repos/getformwork_formwork/formwork/fields/number.php", "downloaded_repos/getformwork_formwork/formwork/fields/page.php", "downloaded_repos/getformwork_formwork/formwork/fields/password.php", "downloaded_repos/getformwork_formwork/formwork/fields/range.php", "downloaded_repos/getformwork_formwork/formwork/fields/select.php", "downloaded_repos/getformwork_formwork/formwork/fields/slug.php", "downloaded_repos/getformwork_formwork/formwork/fields/tags.php", "downloaded_repos/getformwork_formwork/formwork/fields/template.php", "downloaded_repos/getformwork_formwork/formwork/fields/text.php", "downloaded_repos/getformwork_formwork/formwork/fields/textarea.php", "downloaded_repos/getformwork_formwork/formwork/fields/togglegroup.php", "downloaded_repos/getformwork_formwork/formwork/fields/upload.php", "downloaded_repos/getformwork_formwork/formwork/phpstan-baseline.neon", "downloaded_repos/getformwork_formwork/formwork/phpstan.neon", "downloaded_repos/getformwork_formwork/formwork/schemes/config/system.yaml", "downloaded_repos/getformwork_formwork/formwork/server.php", "downloaded_repos/getformwork_formwork/formwork/src/Assets/Asset.php", "downloaded_repos/getformwork_formwork/formwork/src/Assets/AssetCollection.php", "downloaded_repos/getformwork_formwork/formwork/src/Assets/Assets.php", "downloaded_repos/getformwork_formwork/formwork/src/Assets/Exceptions/AssetNotFoundException.php", "downloaded_repos/getformwork_formwork/formwork/src/Backup/Backupper.php", "downloaded_repos/getformwork_formwork/formwork/src/Backup/Utils/ZipErrors.php", "downloaded_repos/getformwork_formwork/formwork/src/Cache/AbstractCache.php", "downloaded_repos/getformwork_formwork/formwork/src/Cache/CacheItem.php", "downloaded_repos/getformwork_formwork/formwork/src/Cache/CacheItemInterface.php", "downloaded_repos/getformwork_formwork/formwork/src/Cache/FilesCache.php", "downloaded_repos/getformwork_formwork/formwork/src/Cms/App.php", "downloaded_repos/getformwork_formwork/formwork/src/Cms/Site.php", "downloaded_repos/getformwork_formwork/formwork/src/Commands/ServeCommand.php", "downloaded_repos/getformwork_formwork/formwork/src/Config/Config.php", "downloaded_repos/getformwork_formwork/formwork/src/Config/Exceptions/ConfigLoadingException.php", "downloaded_repos/getformwork_formwork/formwork/src/Config/Exceptions/ConfigResolutionException.php", "downloaded_repos/getformwork_formwork/formwork/src/Config/Exceptions/UnresolvedConfigException.php", "downloaded_repos/getformwork_formwork/formwork/src/Controllers/AbstractController.php", "downloaded_repos/getformwork_formwork/formwork/src/Controllers/AssetsController.php", "downloaded_repos/getformwork_formwork/formwork/src/Controllers/ErrorsController.php", "downloaded_repos/getformwork_formwork/formwork/src/Controllers/ErrorsControllerInterface.php", "downloaded_repos/getformwork_formwork/formwork/src/Controllers/FilesController.php", "downloaded_repos/getformwork_formwork/formwork/src/Controllers/PageController.php", "downloaded_repos/getformwork_formwork/formwork/src/Data/AbstractCollection.php", "downloaded_repos/getformwork_formwork/formwork/src/Data/Collection.php", "downloaded_repos/getformwork_formwork/formwork/src/Data/CollectionDataProxy.php", "downloaded_repos/getformwork_formwork/formwork/src/Data/Contracts/ArraySerializable.php", "downloaded_repos/getformwork_formwork/formwork/src/Data/Contracts/Arrayable.php", "downloaded_repos/getformwork_formwork/formwork/src/Data/Contracts/Paginable.php", "downloaded_repos/getformwork_formwork/formwork/src/Data/Exceptions/InvalidValueException.php", "downloaded_repos/getformwork_formwork/formwork/src/Data/Pagination.php", "downloaded_repos/getformwork_formwork/formwork/src/Data/Traits/DataArrayable.php", "downloaded_repos/getformwork_formwork/formwork/src/Data/Traits/DataCountable.php", "downloaded_repos/getformwork_formwork/formwork/src/Data/Traits/DataCountableIterator.php", "downloaded_repos/getformwork_formwork/formwork/src/Data/Traits/DataGetter.php", "downloaded_repos/getformwork_formwork/formwork/src/Data/Traits/DataIterator.php", "downloaded_repos/getformwork_formwork/formwork/src/Data/Traits/DataMultipleGetter.php", "downloaded_repos/getformwork_formwork/formwork/src/Data/Traits/DataMultipleSetter.php", "downloaded_repos/getformwork_formwork/formwork/src/Data/Traits/DataSetter.php", "downloaded_repos/getformwork_formwork/formwork/src/Debug/CodeDumper.php", "downloaded_repos/getformwork_formwork/formwork/src/Debug/Debug.php", "downloaded_repos/getformwork_formwork/formwork/src/Exceptions/RecursionException.php", "downloaded_repos/getformwork_formwork/formwork/src/Exceptions/TranslatedException.php", "downloaded_repos/getformwork_formwork/formwork/src/Fields/Dynamic/DynamicFieldValue.php", "downloaded_repos/getformwork_formwork/formwork/src/Fields/Exceptions/ValidationException.php", "downloaded_repos/getformwork_formwork/formwork/src/Fields/Field.php", "downloaded_repos/getformwork_formwork/formwork/src/Fields/FieldCollection.php", "downloaded_repos/getformwork_formwork/formwork/src/Fields/FieldFactory.php", "downloaded_repos/getformwork_formwork/formwork/src/Fields/Layout/Layout.php", "downloaded_repos/getformwork_formwork/formwork/src/Fields/Layout/Section.php", "downloaded_repos/getformwork_formwork/formwork/src/Fields/Layout/SectionCollection.php", "downloaded_repos/getformwork_formwork/formwork/src/Files/Exceptions/FileUriGenerationException.php", "downloaded_repos/getformwork_formwork/formwork/src/Files/File.php", "downloaded_repos/getformwork_formwork/formwork/src/Files/FileCollection.php", "downloaded_repos/getformwork_formwork/formwork/src/Files/FileFactory.php", "downloaded_repos/getformwork_formwork/formwork/src/Files/FileUriGenerator.php", "downloaded_repos/getformwork_formwork/formwork/src/Files/Services/FileUploader.php", "downloaded_repos/getformwork_formwork/formwork/src/Http/Client.php", "downloaded_repos/getformwork_formwork/formwork/src/Http/Exceptions/ConnectionException.php", "downloaded_repos/getformwork_formwork/formwork/src/Http/FileResponse.php", "downloaded_repos/getformwork_formwork/formwork/src/Http/Files/UploadedFile.php", "downloaded_repos/getformwork_formwork/formwork/src/Http/FilesData.php", "downloaded_repos/getformwork_formwork/formwork/src/Http/Header.php", "downloaded_repos/getformwork_formwork/formwork/src/Http/HeadersData.php", "downloaded_repos/getformwork_formwork/formwork/src/Http/JsonResponse.php", "downloaded_repos/getformwork_formwork/formwork/src/Http/RedirectResponse.php", "downloaded_repos/getformwork_formwork/formwork/src/Http/Request.php", "downloaded_repos/getformwork_formwork/formwork/src/Http/RequestData.php", "downloaded_repos/getformwork_formwork/formwork/src/Http/RequestMethod.php", "downloaded_repos/getformwork_formwork/formwork/src/Http/RequestType.php", "downloaded_repos/getformwork_formwork/formwork/src/Http/Response.php", "downloaded_repos/getformwork_formwork/formwork/src/Http/ResponseHeaders.php", "downloaded_repos/getformwork_formwork/formwork/src/Http/ResponseInterface.php", "downloaded_repos/getformwork_formwork/formwork/src/Http/ResponseStatus.php", "downloaded_repos/getformwork_formwork/formwork/src/Http/ResponseStatusType.php", "downloaded_repos/getformwork_formwork/formwork/src/Http/ServerData.php", "downloaded_repos/getformwork_formwork/formwork/src/Http/Session/MessageType.php", "downloaded_repos/getformwork_formwork/formwork/src/Http/Session/Messages.php", "downloaded_repos/getformwork_formwork/formwork/src/Http/Session/Session.php", "downloaded_repos/getformwork_formwork/formwork/src/Http/Utils/Cookie.php", "downloaded_repos/getformwork_formwork/formwork/src/Http/Utils/DeviceType.php", "downloaded_repos/getformwork_formwork/formwork/src/Http/Utils/Header.php", "downloaded_repos/getformwork_formwork/formwork/src/Http/Utils/IpAnonymizer.php", "downloaded_repos/getformwork_formwork/formwork/src/Http/Utils/Visitor.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/ColorProfile/ColorProfile.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/ColorProfile/ColorSpace.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/ColorProfile/DeviceClass.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/ColorProfile/RenderingIntent.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Decoder/DecoderInterface.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Decoder/GifDecoder.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Decoder/JpegDecoder.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Decoder/PngDecoder.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Decoder/SvgDecoder.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Decoder/WebpDecoder.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Exif/ExifData.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Exif/ExifDateTime.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Exif/ExifReader.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Exif/tables/Exif.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/AbstractHandler.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/Exceptions/UnsupportedFeatureException.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/GifHandler.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/HandlerInterface.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/JpegHandler.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/PngHandler.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/SvgHandler.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/WebpHandler.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Image.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/ImageFactory.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/ImageInfo.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Transform/AbstractTransform.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Transform/Blur.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Transform/BlurMode.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Transform/Brightness.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Transform/Colorize.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Transform/Contrast.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Transform/Crop.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Transform/Desaturate.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Transform/EdgeDetect.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Transform/Emboss.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Transform/Flip.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Transform/FlipDirection.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Transform/Invert.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Transform/Pixelate.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Transform/Resize.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Transform/ResizeMode.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Transform/Rotate.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Transform/Scale.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Transform/Sharpen.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Transform/Smoothen.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Transform/TransformCollection.php", "downloaded_repos/getformwork_formwork/formwork/src/Images/Transform/TransformInterface.php", "downloaded_repos/getformwork_formwork/formwork/src/Interpolator/Errors/SyntaxError.php", "downloaded_repos/getformwork_formwork/formwork/src/Interpolator/Exceptions/InterpolationException.php", "downloaded_repos/getformwork_formwork/formwork/src/Interpolator/Interpolator.php", "downloaded_repos/getformwork_formwork/formwork/src/Interpolator/NodeInterpolator.php", "downloaded_repos/getformwork_formwork/formwork/src/Interpolator/Nodes/AbstractNode.php", "downloaded_repos/getformwork_formwork/formwork/src/Interpolator/Nodes/ArgumentsNode.php", "downloaded_repos/getformwork_formwork/formwork/src/Interpolator/Nodes/ArrayKeysNode.php", "downloaded_repos/getformwork_formwork/formwork/src/Interpolator/Nodes/ArrayNode.php", "downloaded_repos/getformwork_formwork/formwork/src/Interpolator/Nodes/IdentifierNode.php", "downloaded_repos/getformwork_formwork/formwork/src/Interpolator/Nodes/ImplicitArrayKeyNode.php", "downloaded_repos/getformwork_formwork/formwork/src/Interpolator/Nodes/NumberNode.php", "downloaded_repos/getformwork_formwork/formwork/src/Interpolator/Nodes/StringNode.php", "downloaded_repos/getformwork_formwork/formwork/src/Interpolator/Parser.php", "downloaded_repos/getformwork_formwork/formwork/src/Interpolator/ParserInterface.php", "downloaded_repos/getformwork_formwork/formwork/src/Interpolator/Token.php", "downloaded_repos/getformwork_formwork/formwork/src/Interpolator/TokenStream.php", "downloaded_repos/getformwork_formwork/formwork/src/Interpolator/Tokenizer.php", "downloaded_repos/getformwork_formwork/formwork/src/Interpolator/TokenizerInterface.php", "downloaded_repos/getformwork_formwork/formwork/src/Languages/Language.php", "downloaded_repos/getformwork_formwork/formwork/src/Languages/LanguageCodes.php", "downloaded_repos/getformwork_formwork/formwork/src/Languages/LanguageCollection.php", "downloaded_repos/getformwork_formwork/formwork/src/Languages/Languages.php", "downloaded_repos/getformwork_formwork/formwork/src/Languages/LanguagesFactory.php", "downloaded_repos/getformwork_formwork/formwork/src/Log/Log.php", "downloaded_repos/getformwork_formwork/formwork/src/Log/Registry.php", "downloaded_repos/getformwork_formwork/formwork/src/Metadata/Metadata.php", "downloaded_repos/getformwork_formwork/formwork/src/Metadata/MetadataCollection.php", "downloaded_repos/getformwork_formwork/formwork/src/Model/Attributes/ReadonlyModelProperty.php", "downloaded_repos/getformwork_formwork/formwork/src/Model/Model.php", "downloaded_repos/getformwork_formwork/formwork/src/Pages/ContentFile.php", "downloaded_repos/getformwork_formwork/formwork/src/Pages/Exceptions/PageNotFoundException.php", "downloaded_repos/getformwork_formwork/formwork/src/Pages/Page.php", "downloaded_repos/getformwork_formwork/formwork/src/Pages/PageCollection.php", "downloaded_repos/getformwork_formwork/formwork/src/Pages/PageCollectionFactory.php", "downloaded_repos/getformwork_formwork/formwork/src/Pages/PageFactory.php", "downloaded_repos/getformwork_formwork/formwork/src/Pages/Pagination.php", "downloaded_repos/getformwork_formwork/formwork/src/Pages/PaginationFactory.php", "downloaded_repos/getformwork_formwork/formwork/src/Pages/Traits/PageStatus.php", "downloaded_repos/getformwork_formwork/formwork/src/Pages/Traits/PageTraversal.php", "downloaded_repos/getformwork_formwork/formwork/src/Pages/Traits/PageUid.php", "downloaded_repos/getformwork_formwork/formwork/src/Pages/Traits/PageUri.php", "downloaded_repos/getformwork_formwork/formwork/src/Pages/Traits/PaginationUri.php", "downloaded_repos/getformwork_formwork/formwork/src/Panel/ContentHistory/ContentHistory.php", "downloaded_repos/getformwork_formwork/formwork/src/Panel/ContentHistory/ContentHistoryEvent.php", "downloaded_repos/getformwork_formwork/formwork/src/Panel/ContentHistory/ContentHistoryItem.php", "downloaded_repos/getformwork_formwork/formwork/src/Panel/ContentHistory/ContentHistoryItemCollection.php", "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/AbstractController.php", "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/AssetsController.php", "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/AuthenticationController.php", "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/BackupController.php", "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/CacheController.php", "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/DashboardController.php", "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/ErrorsController.php", "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/FilesController.php", "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/OptionsController.php", "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/PagesController.php", "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/RegisterController.php", "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/StatisticsController.php", "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/ToolsController.php", "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/UpdatesController.php", "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/UsersController.php", "downloaded_repos/getformwork_formwork/formwork/src/Panel/Modals/Modal.php", "downloaded_repos/getformwork_formwork/formwork/src/Panel/Modals/ModalButton.php", "downloaded_repos/getformwork_formwork/formwork/src/Panel/Modals/ModalButtonCollection.php", "downloaded_repos/getformwork_formwork/formwork/src/Panel/Modals/ModalCollection.php", "downloaded_repos/getformwork_formwork/formwork/src/Panel/Modals/ModalFactory.php", "downloaded_repos/getformwork_formwork/formwork/src/Panel/Modals/Modals.php", "downloaded_repos/getformwork_formwork/formwork/src/Panel/Navigation/NavigationItem.php", "downloaded_repos/getformwork_formwork/formwork/src/Panel/Navigation/NavigationItemCollection.php", "downloaded_repos/getformwork_formwork/formwork/src/Panel/Panel.php", "downloaded_repos/getformwork_formwork/formwork/src/Panel/Security/AccessLimiter.php", "downloaded_repos/getformwork_formwork/formwork/src/Panel/Security/Password.php", "downloaded_repos/getformwork_formwork/formwork/src/Panel/Utils/DateFormats.php", "downloaded_repos/getformwork_formwork/formwork/src/Parsers/AbstractEncoder.php", "downloaded_repos/getformwork_formwork/formwork/src/Parsers/AbstractParser.php", "downloaded_repos/getformwork_formwork/formwork/src/Parsers/Extensions/CommonMark/FormworkExtension.php", "downloaded_repos/getformwork_formwork/formwork/src/Parsers/Extensions/CommonMark/ImageAltProcessor.php", "downloaded_repos/getformwork_formwork/formwork/src/Parsers/Extensions/CommonMark/ImageRenderer.php", "downloaded_repos/getformwork_formwork/formwork/src/Parsers/Extensions/CommonMark/LinkBaseProcessor.php", "downloaded_repos/getformwork_formwork/formwork/src/Parsers/Json.php", "downloaded_repos/getformwork_formwork/formwork/src/Parsers/Markdown.php", "downloaded_repos/getformwork_formwork/formwork/src/Parsers/Php.php", "downloaded_repos/getformwork_formwork/formwork/src/Parsers/Yaml.php", "downloaded_repos/getformwork_formwork/formwork/src/Router/CompiledRoute.php", "downloaded_repos/getformwork_formwork/formwork/src/Router/Exceptions/InvalidRouteException.php", "downloaded_repos/getformwork_formwork/formwork/src/Router/Exceptions/RouteNotFoundException.php", "downloaded_repos/getformwork_formwork/formwork/src/Router/Route.php", "downloaded_repos/getformwork_formwork/formwork/src/Router/RouteCollection.php", "downloaded_repos/getformwork_formwork/formwork/src/Router/RouteFilter.php", "downloaded_repos/getformwork_formwork/formwork/src/Router/RouteFilterCollection.php", "downloaded_repos/getformwork_formwork/formwork/src/Router/RouteParams.php", "downloaded_repos/getformwork_formwork/formwork/src/Router/Router.php", "downloaded_repos/getformwork_formwork/formwork/src/Sanitizer/DomSanitizer.php", "downloaded_repos/getformwork_formwork/formwork/src/Sanitizer/HtmlSanitizer.php", "downloaded_repos/getformwork_formwork/formwork/src/Sanitizer/Parser/DomParserInterface.php", "downloaded_repos/getformwork_formwork/formwork/src/Sanitizer/Parser/Html5Parser.php", "downloaded_repos/getformwork_formwork/formwork/src/Sanitizer/Parser/PhpDomParser.php", "downloaded_repos/getformwork_formwork/formwork/src/Sanitizer/Reference/HtmlReference.php", "downloaded_repos/getformwork_formwork/formwork/src/Sanitizer/Reference/SvgReference.php", "downloaded_repos/getformwork_formwork/formwork/src/Sanitizer/SanitizeElementsMethod.php", "downloaded_repos/getformwork_formwork/formwork/src/Sanitizer/SvgSanitizer.php", "downloaded_repos/getformwork_formwork/formwork/src/Schemes/Scheme.php", "downloaded_repos/getformwork_formwork/formwork/src/Schemes/SchemeFactory.php", "downloaded_repos/getformwork_formwork/formwork/src/Schemes/SchemeOptions.php", "downloaded_repos/getformwork_formwork/formwork/src/Schemes/Schemes.php", "downloaded_repos/getformwork_formwork/formwork/src/Security/CsrfToken.php", "downloaded_repos/getformwork_formwork/formwork/src/Services/Container.php", "downloaded_repos/getformwork_formwork/formwork/src/Services/Exceptions/ServiceResolutionException.php", "downloaded_repos/getformwork_formwork/formwork/src/Services/Loaders/ConfigServiceLoader.php", "downloaded_repos/getformwork_formwork/formwork/src/Services/Loaders/PanelServiceLoader.php", "downloaded_repos/getformwork_formwork/formwork/src/Services/Loaders/SchemesServiceLoader.php", "downloaded_repos/getformwork_formwork/formwork/src/Services/Loaders/SiteServiceLoader.php", "downloaded_repos/getformwork_formwork/formwork/src/Services/Loaders/TemplatesServiceLoader.php", "downloaded_repos/getformwork_formwork/formwork/src/Services/Loaders/TranslationsServiceLoader.php", "downloaded_repos/getformwork_formwork/formwork/src/Services/Loaders/UsersServiceLoader.php", "downloaded_repos/getformwork_formwork/formwork/src/Services/ResolutionAwareServiceLoaderInterface.php", "downloaded_repos/getformwork_formwork/formwork/src/Services/ServiceDefinition.php", "downloaded_repos/getformwork_formwork/formwork/src/Services/ServiceLoaderInterface.php", "downloaded_repos/getformwork_formwork/formwork/src/Statistics/Statistics.php", "downloaded_repos/getformwork_formwork/formwork/src/Templates/Template.php", "downloaded_repos/getformwork_formwork/formwork/src/Templates/TemplateCollection.php", "downloaded_repos/getformwork_formwork/formwork/src/Templates/TemplateFactory.php", "downloaded_repos/getformwork_formwork/formwork/src/Templates/Templates.php", "downloaded_repos/getformwork_formwork/formwork/src/Traits/Methods.php", "downloaded_repos/getformwork_formwork/formwork/src/Traits/SingletonClass.php", "downloaded_repos/getformwork_formwork/formwork/src/Traits/StaticClass.php", "downloaded_repos/getformwork_formwork/formwork/src/Translations/Translation.php", "downloaded_repos/getformwork_formwork/formwork/src/Translations/Translations.php", "downloaded_repos/getformwork_formwork/formwork/src/Updater/SemVer.php", "downloaded_repos/getformwork_formwork/formwork/src/Updater/Updater.php", "downloaded_repos/getformwork_formwork/formwork/src/Users/<USER>", "downloaded_repos/getformwork_formwork/formwork/src/Users/<USER>/AuthenticationFailedException.php", "downloaded_repos/getformwork_formwork/formwork/src/Users/<USER>/UserImageNotFoundException.php", "downloaded_repos/getformwork_formwork/formwork/src/Users/<USER>/UserNotFoundException.php", "downloaded_repos/getformwork_formwork/formwork/src/Users/<USER>/UserNotLoggedException.php", "downloaded_repos/getformwork_formwork/formwork/src/Users/<USER>", "downloaded_repos/getformwork_formwork/formwork/src/Users/<USER>", "downloaded_repos/getformwork_formwork/formwork/src/Users/<USER>", "downloaded_repos/getformwork_formwork/formwork/src/Users/<USER>", "downloaded_repos/getformwork_formwork/formwork/src/Users/<USER>", "downloaded_repos/getformwork_formwork/formwork/src/Users/<USER>", "downloaded_repos/getformwork_formwork/formwork/src/Users/<USER>", "downloaded_repos/getformwork_formwork/formwork/src/Users/<USER>", "downloaded_repos/getformwork_formwork/formwork/src/Utils/Arr.php", "downloaded_repos/getformwork_formwork/formwork/src/Utils/Constraint.php", "downloaded_repos/getformwork_formwork/formwork/src/Utils/Date.php", "downloaded_repos/getformwork_formwork/formwork/src/Utils/Exceptions/FileNotFoundException.php", "downloaded_repos/getformwork_formwork/formwork/src/Utils/Exceptions/FileSystemException.php", "downloaded_repos/getformwork_formwork/formwork/src/Utils/FileSystem.php", "downloaded_repos/getformwork_formwork/formwork/src/Utils/Html.php", "downloaded_repos/getformwork_formwork/formwork/src/Utils/MimeType.php", "downloaded_repos/getformwork_formwork/formwork/src/Utils/Path.php", "downloaded_repos/getformwork_formwork/formwork/src/Utils/Str.php", "downloaded_repos/getformwork_formwork/formwork/src/Utils/Text.php", "downloaded_repos/getformwork_formwork/formwork/src/Utils/Uri.php", "downloaded_repos/getformwork_formwork/formwork/src/Utils/scripts/update-mime-types.php", "downloaded_repos/getformwork_formwork/formwork/src/View/Exceptions/RenderingException.php", "downloaded_repos/getformwork_formwork/formwork/src/View/Renderer.php", "downloaded_repos/getformwork_formwork/formwork/src/View/View.php", "downloaded_repos/getformwork_formwork/formwork/src/View/ViewFactory.php", "downloaded_repos/getformwork_formwork/formwork/translations/de.yaml", "downloaded_repos/getformwork_formwork/formwork/translations/en.yaml", "downloaded_repos/getformwork_formwork/formwork/translations/es.yaml", "downloaded_repos/getformwork_formwork/formwork/translations/fr.yaml", "downloaded_repos/getformwork_formwork/formwork/translations/it.yaml", "downloaded_repos/getformwork_formwork/formwork/translations/pl.yaml", "downloaded_repos/getformwork_formwork/formwork/translations/pt.yaml", "downloaded_repos/getformwork_formwork/formwork/translations/ru.yaml", "downloaded_repos/getformwork_formwork/formwork/translations/uk.yaml", "downloaded_repos/getformwork_formwork/formwork/views/errors/error.php", "downloaded_repos/getformwork_formwork/formwork/views/errors/install.php", "downloaded_repos/getformwork_formwork/formwork/views/errors/maintenance.php", "downloaded_repos/getformwork_formwork/formwork/views/errors/panel/assets.php", "downloaded_repos/getformwork_formwork/formwork/views/errors/partials/debug.php", "downloaded_repos/getformwork_formwork/formwork/views/errors/partials/footer.php", "downloaded_repos/getformwork_formwork/formwork/views/errors/partials/header.php", "downloaded_repos/getformwork_formwork/formwork/views/errors/phpversion.php", "downloaded_repos/getformwork_formwork/index.php", "downloaded_repos/getformwork_formwork/panel/.prettierrc", "downloaded_repos/getformwork_formwork/panel/.stylelintrc.yaml", "downloaded_repos/getformwork_formwork/panel/.yarnrc.yml", "downloaded_repos/getformwork_formwork/panel/assets/css/.gitkeep", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/arrow-arrow-left-right-up-down.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/arrow-down.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/arrow-left-circle.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/arrow-left-down-right-up.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/arrow-left-down.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/arrow-left-right.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/arrow-left-up-right-down.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/arrow-left-up.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/arrow-left.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/arrow-right-circle.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/arrow-right-down.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/arrow-right-up-box.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/arrow-right-up.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/arrow-right.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/arrow-up-down-left-right.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/arrow-up-down.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/arrow-up.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/arrows-rotate-clockwise.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/arrows-rotate-counterclockwise.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/bars.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/blockquote.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/bold.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/bolt.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/cache-clear.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/cache.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/calendar-clock.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/calendar.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/camera-flash.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/camera-metering-average.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/camera-metering-evaluative.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/camera-metering-partial.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/camera-metering-spot.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/camera-no-flash.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/check-circle.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/check-square.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/check.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/chevron-down.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/chevron-left.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/chevron-right.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/chevron-up.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/circle-small-fill.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/circle-small.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/circle.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/clock-rotate-left.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/clock-rotate-right.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/clock.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/cloud-download.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/cloud-upload.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/cloud.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/code.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/desktop.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/ellipsis-h.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/ellipsis-v.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/exclamation-circle.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/exclamation-octagon.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/exclamation-square.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/exclamation-triangle.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/exclamation.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/eye-slash.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/eye.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/file-archive.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/file-backup.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/file-binary.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/file-document.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/file-exclamation.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/file-icons.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/file-image.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/file-list.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/file-page.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/file-pdf.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/file-presentation.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/file-spreadsheet.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/file-text.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/file-video.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/file.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/globe.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/grabber.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/home.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/hourglass.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/image.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/indent-decrease.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/indent-increase.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/info-circle.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/info-square.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/info.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/italic.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/link.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/list-ordered.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/list-unordered.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/list.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/markdown.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/minus-circle.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/minus-square.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/minus.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/mobile.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/movie.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/octagon.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/page-blank.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/page-compile.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/page-down.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/page-error.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/page-exclamation.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/page-home.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/page-listing.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/page-up.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/page.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/pencil.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/plus-circle.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/plus-square.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/plus.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/quotes.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/readmore.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/reorder-v.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/rotate-left.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/rotate-right.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/search.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/section.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/sitemap.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/sparks.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/square.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/tablet.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/tag.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/template.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/times-circle.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/times-octagon.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/times-square.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/times.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/translate.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/trash.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/triangle.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/user-image-slash.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/user-image.svg", "downloaded_repos/getformwork_formwork/panel/assets/icons/svg/user.svg", "downloaded_repos/getformwork_formwork/panel/assets/images/icon.png", "downloaded_repos/getformwork_formwork/panel/assets/images/icon.svg", "downloaded_repos/getformwork_formwork/panel/assets/js/.gitkeep", "downloaded_repos/getformwork_formwork/panel/config/app.php", "downloaded_repos/getformwork_formwork/panel/config/navigation.php", "downloaded_repos/getformwork_formwork/panel/config/routes/routes.php", "downloaded_repos/getformwork_formwork/panel/config/views/methods.php", "downloaded_repos/getformwork_formwork/panel/eslint.config.js", "downloaded_repos/getformwork_formwork/panel/logs/.gitkeep", "downloaded_repos/getformwork_formwork/panel/modals/changes.yaml", "downloaded_repos/getformwork_formwork/panel/modals/deleteFile.yaml", "downloaded_repos/getformwork_formwork/panel/modals/deleteFileItem.yaml", "downloaded_repos/getformwork_formwork/panel/modals/deletePage.yaml", "downloaded_repos/getformwork_formwork/panel/modals/deleteUser.yaml", "downloaded_repos/getformwork_formwork/panel/modals/deleteUserImage.yaml", "downloaded_repos/getformwork_formwork/panel/modals/images.yaml", "downloaded_repos/getformwork_formwork/panel/modals/link.yaml", "downloaded_repos/getformwork_formwork/panel/modals/newPage.yaml", "downloaded_repos/getformwork_formwork/panel/modals/newUser.yaml", "downloaded_repos/getformwork_formwork/panel/modals/renameFile.yaml", "downloaded_repos/getformwork_formwork/panel/modals/renameFileItem.yaml", "downloaded_repos/getformwork_formwork/panel/modals/uploadFile.yaml", "downloaded_repos/getformwork_formwork/panel/package.json", "downloaded_repos/getformwork_formwork/panel/schemes/forms/login.yaml", "downloaded_repos/getformwork_formwork/panel/schemes/forms/register.yaml", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_animations.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_badges.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_base.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_buttons.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_caption.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_charts.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_colors.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_columns.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_dropdowns.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_errors.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_files-list.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_files.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_forms.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_functions.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_header.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_icon.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_login.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_logo.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_mixins.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_modals.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_notifications.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_options.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_pages-tree.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_pages.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_panel.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_section.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_sidebar.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_sortable.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_spinner.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_statistics.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_table.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_tabs.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_tooltip.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_typography.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_users.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_utils.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/_variables.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/forms/_forms-array.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/forms/_forms-base.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/forms/_forms-checkbox.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/forms/_forms-color.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/forms/_forms-date.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/forms/_forms-duration.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/forms/_forms-editor.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/forms/_forms-image.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/forms/_forms-range.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/forms/_forms-tags.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/forms/_forms-togglegroup.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/components/forms/_forms-upload.scss", "downloaded_repos/getformwork_formwork/panel/src/scss/panel.scss", "downloaded_repos/getformwork_formwork/panel/src/ts/app.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/color-scheme.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/dropdowns.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/files.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/fileslist.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/form.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/forms.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/icons.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/array-input.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/color-input.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/date-input.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/duration-input.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/editor/code/menu.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/editor/code/view.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/editor/markdown/commands.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/editor/markdown/inputrules.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/editor/markdown/keymap.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/editor/markdown/linktooltip.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/editor/markdown/menu.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/editor/markdown/view.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/editor-input.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/image-picker.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/range-input.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/select-input.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/slug-input.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/tags-input.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/togglegroup-input.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/inputs/upload-input.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/modal.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/modals.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/navigation.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/notification.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/notifications.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/sections.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/statistics-chart.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/tooltip.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/tooltips.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/views/backups.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/views/dashboard.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/views/pages.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/views/statistics.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/components/views/updates.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/polyfills/request-submit.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/utils/arrays.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/utils/cookies.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/utils/dimensions.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/utils/events.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/utils/forms.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/utils/math.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/utils/numbers.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/utils/request.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/utils/selectors.ts", "downloaded_repos/getformwork_formwork/panel/src/ts/utils/validation.ts", "downloaded_repos/getformwork_formwork/panel/translations/de.yaml", "downloaded_repos/getformwork_formwork/panel/translations/en.yaml", "downloaded_repos/getformwork_formwork/panel/translations/es.yaml", "downloaded_repos/getformwork_formwork/panel/translations/fr.yaml", "downloaded_repos/getformwork_formwork/panel/translations/it.yaml", "downloaded_repos/getformwork_formwork/panel/translations/pl.yaml", "downloaded_repos/getformwork_formwork/panel/translations/pt.yaml", "downloaded_repos/getformwork_formwork/panel/translations/ru.yaml", "downloaded_repos/getformwork_formwork/panel/translations/uk.yaml", "downloaded_repos/getformwork_formwork/panel/tsconfig.json", "downloaded_repos/getformwork_formwork/panel/views/authentication/login.php", "downloaded_repos/getformwork_formwork/panel/views/dashboard/index.php", "downloaded_repos/getformwork_formwork/panel/views/errors/error.php", "downloaded_repos/getformwork_formwork/panel/views/fields/array.php", "downloaded_repos/getformwork_formwork/panel/views/fields/checkbox.php", "downloaded_repos/getformwork_formwork/panel/views/fields/color.php", "downloaded_repos/getformwork_formwork/panel/views/fields/date.php", "downloaded_repos/getformwork_formwork/panel/views/fields/duration.php", "downloaded_repos/getformwork_formwork/panel/views/fields/email.php", "downloaded_repos/getformwork_formwork/panel/views/fields/file.php", "downloaded_repos/getformwork_formwork/panel/views/fields/files.php", "downloaded_repos/getformwork_formwork/panel/views/fields/image.php", "downloaded_repos/getformwork_formwork/panel/views/fields/images.php", "downloaded_repos/getformwork_formwork/panel/views/fields/layout/default.php", "downloaded_repos/getformwork_formwork/panel/views/fields/layout/sections.php", "downloaded_repos/getformwork_formwork/panel/views/fields/markdown.php", "downloaded_repos/getformwork_formwork/panel/views/fields/number.php", "downloaded_repos/getformwork_formwork/panel/views/fields/page/imagepicker.php", "downloaded_repos/getformwork_formwork/panel/views/fields/page.php", "downloaded_repos/getformwork_formwork/panel/views/fields/partials/description.php", "downloaded_repos/getformwork_formwork/panel/views/fields/partials/label.php", "downloaded_repos/getformwork_formwork/panel/views/fields/password.php", "downloaded_repos/getformwork_formwork/panel/views/fields/range.php", "downloaded_repos/getformwork_formwork/panel/views/fields/select.php", "downloaded_repos/getformwork_formwork/panel/views/fields/slug.php", "downloaded_repos/getformwork_formwork/panel/views/fields/tags.php", "downloaded_repos/getformwork_formwork/panel/views/fields/template.php", "downloaded_repos/getformwork_formwork/panel/views/fields/text.php", "downloaded_repos/getformwork_formwork/panel/views/fields/textarea.php", "downloaded_repos/getformwork_formwork/panel/views/fields/togglegroup.php", "downloaded_repos/getformwork_formwork/panel/views/fields/upload.php", "downloaded_repos/getformwork_formwork/panel/views/fields.php", "downloaded_repos/getformwork_formwork/panel/views/files/edit.php", "downloaded_repos/getformwork_formwork/panel/views/files/index.php", "downloaded_repos/getformwork_formwork/panel/views/layouts/fields/field.php", "downloaded_repos/getformwork_formwork/panel/views/layouts/login.php", "downloaded_repos/getformwork_formwork/panel/views/layouts/panel.php", "downloaded_repos/getformwork_formwork/panel/views/modals/modal.php", "downloaded_repos/getformwork_formwork/panel/views/options/site.php", "downloaded_repos/getformwork_formwork/panel/views/options/system.php", "downloaded_repos/getformwork_formwork/panel/views/options/tabs.php", "downloaded_repos/getformwork_formwork/panel/views/pages/editor.php", "downloaded_repos/getformwork_formwork/panel/views/pages/index.php", "downloaded_repos/getformwork_formwork/panel/views/pages/tree.php", "downloaded_repos/getformwork_formwork/panel/views/partials/files/file/item.php", "downloaded_repos/getformwork_formwork/panel/views/partials/files/file/list.php", "downloaded_repos/getformwork_formwork/panel/views/partials/files/images/exif/data.php", "downloaded_repos/getformwork_formwork/panel/views/partials/files/images/info/exif.php", "downloaded_repos/getformwork_formwork/panel/views/partials/files/images/info/info.php", "downloaded_repos/getformwork_formwork/panel/views/partials/files/images/position/map.php", "downloaded_repos/getformwork_formwork/panel/views/partials/login/notification.php", "downloaded_repos/getformwork_formwork/panel/views/partials/pages/info.php", "downloaded_repos/getformwork_formwork/panel/views/partials/pages/status.php", "downloaded_repos/getformwork_formwork/panel/views/partials/scripts.php", "downloaded_repos/getformwork_formwork/panel/views/partials/sidebar.php", "downloaded_repos/getformwork_formwork/panel/views/partials/user-image.php", "downloaded_repos/getformwork_formwork/panel/views/register/register.php", "downloaded_repos/getformwork_formwork/panel/views/statistics/index.php", "downloaded_repos/getformwork_formwork/panel/views/tools/backups.php", "downloaded_repos/getformwork_formwork/panel/views/tools/info.php", "downloaded_repos/getformwork_formwork/panel/views/tools/tabs.php", "downloaded_repos/getformwork_formwork/panel/views/tools/updates.php", "downloaded_repos/getformwork_formwork/panel/views/users/index.php", "downloaded_repos/getformwork_formwork/panel/views/users/profile.php", "downloaded_repos/getformwork_formwork/panel/yarn.lock", "downloaded_repos/getformwork_formwork/site/config/site.yaml", "downloaded_repos/getformwork_formwork/site/config/system.yaml", "downloaded_repos/getformwork_formwork/site/files/.gitkeep", "downloaded_repos/getformwork_formwork/site/pages/1-about/page.md", "downloaded_repos/getformwork_formwork/site/pages/2-blog/20180615-hello-world/nasa-vhsz50aafas-unsplash.jpg", "downloaded_repos/getformwork_formwork/site/pages/2-blog/20180615-hello-world/post.md", "downloaded_repos/getformwork_formwork/site/pages/2-blog/20220624-another-blog-post/post.md", "downloaded_repos/getformwork_formwork/site/pages/2-blog/20220624-another-blog-post/vruyr-martirosyan-0n632k-mow4-unsplash.jpg", "downloaded_repos/getformwork_formwork/site/pages/2-blog/20250505-a-walk-in-the-park/naoki-suzuki-v4zkouv36-4-unsplash.jpg.meta.yaml", "downloaded_repos/getformwork_formwork/site/pages/2-blog/20250505-a-walk-in-the-park/post.md", "downloaded_repos/getformwork_formwork/site/pages/2-blog/20250505-a-walk-in-the-park/spencer-demera-opsimocytr0-unsplash.jpg", "downloaded_repos/getformwork_formwork/site/pages/2-blog/blog.md", "downloaded_repos/getformwork_formwork/site/pages/error/page.md", "downloaded_repos/getformwork_formwork/site/pages/index/formwork.png.meta.yaml", "downloaded_repos/getformwork_formwork/site/pages/index/page.md", "downloaded_repos/getformwork_formwork/site/schemes/config/site.yaml", "downloaded_repos/getformwork_formwork/site/schemes/files/file.yaml", "downloaded_repos/getformwork_formwork/site/schemes/files/image.yaml", "downloaded_repos/getformwork_formwork/site/schemes/pages/blog.yaml", "downloaded_repos/getformwork_formwork/site/schemes/pages/default.yaml", "downloaded_repos/getformwork_formwork/site/schemes/pages/page.yaml", "downloaded_repos/getformwork_formwork/site/schemes/pages/post.yaml", "downloaded_repos/getformwork_formwork/site/schemes/users/user.yaml", "downloaded_repos/getformwork_formwork/site/statistics/.gitkeep", "downloaded_repos/getformwork_formwork/site/templates/assets/css/style.css", "downloaded_repos/getformwork_formwork/site/templates/assets/icons/svg/bars.svg", "downloaded_repos/getformwork_formwork/site/templates/assets/js/script.js", "downloaded_repos/getformwork_formwork/site/templates/blog.php", "downloaded_repos/getformwork_formwork/site/templates/controllers/blog.php", "downloaded_repos/getformwork_formwork/site/templates/default.php", "downloaded_repos/getformwork_formwork/site/templates/layouts/site.php", "downloaded_repos/getformwork_formwork/site/templates/page.php", "downloaded_repos/getformwork_formwork/site/templates/partials/cover-image.php", "downloaded_repos/getformwork_formwork/site/templates/partials/menu.php", "downloaded_repos/getformwork_formwork/site/templates/partials/meta.php", "downloaded_repos/getformwork_formwork/site/templates/partials/pagination.php", "downloaded_repos/getformwork_formwork/site/templates/partials/tags.php", "downloaded_repos/getformwork_formwork/site/templates/post.php", "downloaded_repos/getformwork_formwork/site/translations/.gitkeep", "downloaded_repos/getformwork_formwork/site/users/accounts/.gitkeep", "downloaded_repos/getformwork_formwork/site/users/images/.gitkeep", "downloaded_repos/getformwork_formwork/site/users/roles/admin.yaml", "downloaded_repos/getformwork_formwork/site/users/roles/editor.yaml", "downloaded_repos/getformwork_formwork/site/users/roles/user.yaml"], "skipped": [{"path": "downloaded_repos/getformwork_formwork/.github/workflows/release.yaml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/config/views/methods.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/fields/dynamic/vars.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/fields/files.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/fields/images.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/fields/tags.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/fields/upload.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Cms/App.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Cms/Site.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Config/Config.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Controllers/AbstractController.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Controllers/PageController.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Debug/CodeDumper.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Debug/Debug.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Fields/Field.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Files/File.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Files/FileFactory.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Http/Client.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Http/FileResponse.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Http/Header.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Http/Request.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Http/Session/Session.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Http/Utils/Cookie.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Http/Utils/IpAnonymizer.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/ColorProfile/ColorProfile.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Decoder/GifDecoder.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Decoder/PngDecoder.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Decoder/WebpDecoder.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Exif/ExifDateTime.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Exif/ExifReader.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/GifHandler.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/JpegHandler.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Handler/WebpHandler.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Images/Image.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Interpolator/NodeInterpolator.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Interpolator/Nodes/NumberNode.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Model/Model.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Pages/Page.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/ContentHistory/ContentHistory.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/AbstractController.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Controllers/AuthenticationController.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Panel/Panel.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Parsers/Json.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Router/Router.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Statistics/Statistics.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Users/<USER>", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Constraint.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Date.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/FileSystem.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/MimeType.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Str.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Text.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/Uri.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/Utils/scripts/update-mime-types.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/src/View/View.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/translations/de.yaml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/translations/fr.yaml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/formwork/translations/pl.yaml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/panel/config/views/methods.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/panel/src/scss/vendor/chartist.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/getformwork_formwork/panel/translations/de.yaml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/panel/translations/es.yaml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/panel/translations/fr.yaml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/panel/translations/it.yaml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/panel/translations/pl.yaml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/panel/translations/pt.yaml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/getformwork_formwork/site/pages/index/formwork.png", "reason": "exceeded_size_limit"}]}, "time": {"rules": [], "rules_parse_time": 1.769197940826416, "profiling_times": {"config_time": 6.494867563247681, "core_time": 5.993212461471558, "ignores_time": 0.0018870830535888672, "total_time": 12.491137981414795}, "parsing_time": {"total_time": 7.000732421875, "per_file_time": {"mean": 0.013514927455357135, "std_dev": 0.0009609812187539492}, "very_slow_stats": {"time_ratio": 0.045935763132083696, "count_ratio": 0.0019305019305019305}, "very_slow_files": [{"fpath": "downloaded_repos/getformwork_formwork/formwork/src/Utils/MimeType.php", "ftime": 0.32158398628234863}]}, "scanning_time": {"total_time": 20.536473751068115, "per_file_time": {"mean": 0.010161540698202923, "std_dev": 0.0022614900838868975}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 5.555302619934082, "per_file_and_rule_time": {"mean": 0.0023499588070787177, "std_dev": 7.134925960006224e-05}, "very_slow_stats": {"time_ratio": 0.05298037460621233, "count_ratio": 0.0008460236886632825}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/getformwork_formwork/panel/src/ts/components/views/pages.ts", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.1290111541748047}, {"fpath": "downloaded_repos/getformwork_formwork/panel/src/ts/components/fileslist.ts", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.16531085968017578}]}, "tainting_time": {"total_time": 1.6270132064819336, "per_def_and_rule_time": {"mean": 0.0009431960617286573, "std_dev": 6.316367696482279e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1098010496}, "engine_requested": "OSS", "skipped_rules": []}