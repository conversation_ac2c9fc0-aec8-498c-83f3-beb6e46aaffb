
#!/usr/bin/env python3

import argparse
import requests
import datetime
import urllib.parse
import csv
import os
import subprocess
from pathlib import Path
from langdetect import detect

# -------------------- CONFIGURATION --------------------
CONFIG = {
    "languages": ["PHP", "JavaScript"],
    "min_stars": 100,
    "max_stars": 1000,
    "updated_days_ago": 90,
    "keywords": [],
    "github_token": os.getenv("GITHUB_TOKEN", "your_github_token_here"),
    "search_pages": 3,
    "clone_and_scan": False,
    "semgrep_config": "auto",
    "output_csv": "vulnerable_projects.csv",
    "skipped_log_file": "skipped_projects.txt",
    "dockerhub_keywords": ["php", "webshell", "vulnerable"],
    "github_dorks": [
        "filename:config.php password",
        "filename:.env DB_PASSWORD",
        "extension:php upload file",
        "filename:index.php admin login",
        "eval(base64_decode(",
        "inurl:/admin ext:php"
    ],
    "google_dorks": [
        "inurl:/admin intitle:login",
        "intitle:index.of admin",
        "inurl:upload ext:php",
        "inurl:/phpmyadmin",
        "ext:sql intext:password"
    ],
    "log_google_dorks_to_file": True,
    "google_dork_log_file": "google_dorks_results.txt"
}

HEADERS = {"Authorization": f"token {CONFIG['github_token']}"}

EXCLUDE_KEYWORDS = ["plugin", "DEPRECATED","A list of", "module", "command-line", "template","laravel","symfony","package", "sdk", "starter", "portfolio", "extension", "UI", "demo", "wrapper", "personal website", "boilerplate", "library", "framework", "template", "example", "demo", "test", "sample", "tutorial", "example", "demo", "test", "sample", "tutorial", "collection"]
HIGH_RISK_KEYWORDS = ["auth", "login", "password", "token", "upload", "admin", "jwt", "reset"]

def is_english(text):
    try:
        return detect(text) == "en"
    except:
        return False

def is_relevant_description(desc, topics):
    if not desc:
        return False
    desc = desc.lower()
    if any(noise in desc for noise in EXCLUDE_KEYWORDS):
        return False
    if any(noise in (topics or []) for noise in EXCLUDE_KEYWORDS):
        return False
    return True

def calculate_risk_score(desc):
    desc = desc.lower() if desc else ""
    return sum(1 for word in HIGH_RISK_KEYWORDS if word in desc)

def github_repo_search(language, page):
    repo_api = "https://api.github.com/search/repositories"
    since_date = (datetime.datetime.utcnow() - datetime.timedelta(days=CONFIG["updated_days_ago"])).strftime("%Y-%m-%dT%H:%M:%SZ")
    keyword_str = " ".join([f"{kw} in:description" for kw in CONFIG["keywords"]])
    query = (
        f"language:{language} stars:{CONFIG['min_stars']}..{CONFIG['max_stars']} "
        f"pushed:>={since_date} {keyword_str}"
    )
    params = {
        "q": query,
        "sort": "stars",
        "order": "desc",
        "per_page": 100,
        "page": page
    }
    r = requests.get(repo_api, headers=HEADERS, params=params)
    if r.status_code != 200:
        print(f"GitHub API Error: {r.status_code}")
        return []
    return r.json().get("items", [])

def github_dork_search():
    results = []
    for dork in CONFIG["github_dorks"]:
        print(f"Running GitHub dork: {dork}")
        params = {
            "q": dork,
            "sort": "indexed",
            "order": "desc",
            "per_page": 10
        }
        r = requests.get("https://api.github.com/search/code", headers=HEADERS, params=params)
        if r.status_code == 200:
            results.extend(r.json().get("items", []))
        else:
            print(f"Error on dork: {r.status_code} - {r.text}")
    return results

def google_dork_search():
    print("\\nGoogle Dork Queries:")
    log_entries = []
    for dork in CONFIG["google_dorks"]:
        query = urllib.parse.quote(dork)
        url = f"https://www.google.com/search?q={query}"
        print(f"🔎 {dork}")
        print(f"🌐 {url}")
        log_entries.append(f"{dork} => {url}")

    if CONFIG.get("log_google_dorks_to_file"):
        with open(CONFIG["google_dork_log_file"], "w") as f:
            for entry in log_entries:
                f.write(entry + "\\n")
        print(f"\\n✅ Google dork results saved to {CONFIG['google_dork_log_file']}")

def clone_and_scan(repo_url, name):
    folder = name.replace("/", "_")
    print(f"Cloning {repo_url} into {folder}...")
    os.system(f"git clone {repo_url}.git {folder}")
    print(f"Scanning {folder} with Semgrep...")
    subprocess.run(["semgrep", "--config", CONFIG["semgrep_config"], folder])

def save_github_to_csv(repos):
    with open(CONFIG["output_csv"], mode="w", newline="", encoding="utf-8") as file:
        writer = csv.writer(file)
        writer.writerow(["Name", "URL", "Stars", "Issues", "Updated", "Risk", "Description"])
        for repo in repos:
            writer.writerow([
                repo["full_name"],
                repo["html_url"],
                repo["stargazers_count"],
                repo["open_issues_count"],
                repo["pushed_at"],
                repo["risk_score"],
                repo.get("description", "")
            ])

def main():
    all_results = []
    skipped_log = open(CONFIG["skipped_log_file"], "w", encoding="utf-8")
    for lang in CONFIG["languages"]:
        for page in range(1, CONFIG["search_pages"] + 1):
            repos = github_repo_search(lang, page)
            for repo in repos:
                desc = repo.get("description", "")
                topics = repo.get("topics", [])
                if not is_relevant_description(desc, topics) or not is_english(desc):
                    skipped_log.write(f"{repo['full_name']} - {desc}\\n")
                    continue

                repo["risk_score"] = calculate_risk_score(desc)
                all_results.append(repo)

                print(f"\\n📦 {repo['full_name']} | ⭐ {repo['stargazers_count']} | 🐞 {repo['open_issues_count']} | ⚠️ Risk: {repo['risk_score']}")
                print(f"🔗 {repo['html_url']}")
                print(f"📝 {desc}")

                if CONFIG["clone_and_scan"]:
                    clone_and_scan(repo["clone_url"], repo["full_name"])
    skipped_log.close()

    dork_results = github_dork_search()
    for dork in dork_results:
        print(f"\\n🔍 Dork Match: {dork['html_url']}")

    google_dork_search()
    save_github_to_csv(all_results)

if __name__ == "__main__":
    main()
