{"version": "1.130.0", "results": [{"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/yezz123_authx/docs/js/termynal.js", "start": {"line": 226, "col": 13, "offset": 8406}, "end": {"line": 226, "col": 90, "offset": 8483}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/yezz123_authx/.github/workflows/release.yml", "start": {"line": 54, "col": 15, "offset": 988}, "end": {"line": 79, "col": 33, "offset": 1831}}]], "message": "Syntax error at line downloaded_repos/yezz123_authx/.github/workflows/release.yml:54:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `import os\nimport re\nfrom typing import Optional\n\nimport tweepy\n\ndef get_version(file_path: str = 'authx/__init__.py') -> Optional[str]:\n    version_pattern = r'__version__\\s*=\\s*\"(?P<version>[\\d\\.]+)\"'\n    try:\n        with open(file_path, 'r') as file:\n            content = file.read()\n            if match := re.search(version_pattern, content):\n                return match['version']\n    except FileNotFoundError:\n        print(f\"File {file_path} not found.\")\n    return None\n\nclient = tweepy.Client(\n    access_token=os.getenv(\"TWITTER_ACCESS_TOKEN\"),\n    access_token_secret=os.getenv(\"TWITTER_ACCESS_TOKEN_SECRET\"),\n    consumer_key=os.getenv(\"TWITTER_CONSUMER_KEY\"),\n    consumer_secret=os.getenv(\"TWITTER_CONSUMER_SECRET\"),\n)\nversion = get_version()\ntweet = os.getenv(\"TWEET\").format(version=version)\nclient.create_tweet(text=tweet)\n` was unexpected", "path": "downloaded_repos/yezz123_authx/.github/workflows/release.yml", "spans": [{"file": "downloaded_repos/yezz123_authx/.github/workflows/release.yml", "start": {"line": 54, "col": 15, "offset": 988}, "end": {"line": 79, "col": 33, "offset": 1831}}]}], "paths": {"scanned": ["downloaded_repos/yezz123_authx/.all-contributorsrc", "downloaded_repos/yezz123_authx/.github/FUNDING.yml", "downloaded_repos/yezz123_authx/.github/SECURITY.md", "downloaded_repos/yezz123_authx/.github/dependabot.yml", "downloaded_repos/yezz123_authx/.github/workflows/ci.yml", "downloaded_repos/yezz123_authx/.github/workflows/latest-changes.yml", "downloaded_repos/yezz123_authx/.github/workflows/release.yml", "downloaded_repos/yezz123_authx/.gitignore", "downloaded_repos/yezz123_authx/.pre-commit-config.yaml", "downloaded_repos/yezz123_authx/LICENSE", "downloaded_repos/yezz123_authx/README.md", "downloaded_repos/yezz123_authx/authx/__init__.py", "downloaded_repos/yezz123_authx/authx/_internal/__init__.py", "downloaded_repos/yezz123_authx/authx/_internal/_callback.py", "downloaded_repos/yezz123_authx/authx/_internal/_error.py", "downloaded_repos/yezz123_authx/authx/_internal/_logger.py", "downloaded_repos/yezz123_authx/authx/_internal/_signature.py", "downloaded_repos/yezz123_authx/authx/_internal/_utils.py", "downloaded_repos/yezz123_authx/authx/config.py", "downloaded_repos/yezz123_authx/authx/core.py", "downloaded_repos/yezz123_authx/authx/dependencies.py", "downloaded_repos/yezz123_authx/authx/exceptions.py", "downloaded_repos/yezz123_authx/authx/main.py", "downloaded_repos/yezz123_authx/authx/py.typed", "downloaded_repos/yezz123_authx/authx/schema.py", "downloaded_repos/yezz123_authx/authx/token.py", "downloaded_repos/yezz123_authx/authx/types.py", "downloaded_repos/yezz123_authx/docs/api/config.md", "downloaded_repos/yezz123_authx/docs/api/dependencies.md", "downloaded_repos/yezz123_authx/docs/api/exceptions.md", "downloaded_repos/yezz123_authx/docs/api/extra/cache.md", "downloaded_repos/yezz123_authx/docs/api/extra/metrics.md", "downloaded_repos/yezz123_authx/docs/api/extra/oauth2.md", "downloaded_repos/yezz123_authx/docs/api/extra/profiler.md", "downloaded_repos/yezz123_authx/docs/api/extra/session.md", "downloaded_repos/yezz123_authx/docs/api/internal/callback.md", "downloaded_repos/yezz123_authx/docs/api/internal/errors.md", "downloaded_repos/yezz123_authx/docs/api/internal/extra/memory.md", "downloaded_repos/yezz123_authx/docs/api/internal/signature.md", "downloaded_repos/yezz123_authx/docs/api/main.md", "downloaded_repos/yezz123_authx/docs/api/reference.md", "downloaded_repos/yezz123_authx/docs/api/request.md", "downloaded_repos/yezz123_authx/docs/api/token.md", "downloaded_repos/yezz123_authx/docs/callbacks/token.md", "downloaded_repos/yezz123_authx/docs/callbacks/user.md", "downloaded_repos/yezz123_authx/docs/css/custom.css", "downloaded_repos/yezz123_authx/docs/css/termynal.css", "downloaded_repos/yezz123_authx/docs/dependencies/aliases.md", "downloaded_repos/yezz123_authx/docs/dependencies/bundle.md", "downloaded_repos/yezz123_authx/docs/dependencies/dependencies.md", "downloaded_repos/yezz123_authx/docs/dependencies/injection.md", "downloaded_repos/yezz123_authx/docs/development/contributing.md", "downloaded_repos/yezz123_authx/docs/extra/Cache.md", "downloaded_repos/yezz123_authx/docs/extra/Metrics.md", "downloaded_repos/yezz123_authx/docs/extra/OAuth2.md", "downloaded_repos/yezz123_authx/docs/extra/Sessions.md", "downloaded_repos/yezz123_authx/docs/extra/profiler.md", "downloaded_repos/yezz123_authx/docs/faq/code_of_conduct.md", "downloaded_repos/yezz123_authx/docs/faq/faq.md", "downloaded_repos/yezz123_authx/docs/faq/help.md", "downloaded_repos/yezz123_authx/docs/faq/license.md", "downloaded_repos/yezz123_authx/docs/get-started/basic-usage.md", "downloaded_repos/yezz123_authx/docs/get-started/installation.md", "downloaded_repos/yezz123_authx/docs/get-started/location.md", "downloaded_repos/yezz123_authx/docs/get-started/payload.md", "downloaded_repos/yezz123_authx/docs/get-started/refresh.md", "downloaded_repos/yezz123_authx/docs/get-started/token.md", "downloaded_repos/yezz123_authx/docs/img/data/1.png", "downloaded_repos/yezz123_authx/docs/img/data/2.png", "downloaded_repos/yezz123_authx/docs/img/data/3.png", "downloaded_repos/yezz123_authx/docs/img/header.svg", "downloaded_repos/yezz123_authx/docs/img/icon.ico", "downloaded_repos/yezz123_authx/docs/img/license.png", "downloaded_repos/yezz123_authx/docs/img/logo.png", "downloaded_repos/yezz123_authx/docs/index.md", "downloaded_repos/yezz123_authx/docs/js/custom.js", "downloaded_repos/yezz123_authx/docs/js/termynal.js", "downloaded_repos/yezz123_authx/docs/release.md", "downloaded_repos/yezz123_authx/examples/README.md", "downloaded_repos/yezz123_authx/examples/examples/__init__.py", "downloaded_repos/yezz123_authx/examples/examples/basic_auth.py", "downloaded_repos/yezz123_authx/examples/examples/fresh_token.py", "downloaded_repos/yezz123_authx/examples/examples/refresh_token.py", "downloaded_repos/yezz123_authx/examples/examples/token_blocklist.py", "downloaded_repos/yezz123_authx/examples/examples/token_locations.py", "downloaded_repos/yezz123_authx/examples/pyproject.toml", "downloaded_repos/yezz123_authx/examples/uv.lock", "downloaded_repos/yezz123_authx/mkdocs.yml", "downloaded_repos/yezz123_authx/pyproject.toml", "downloaded_repos/yezz123_authx/scripts/clean.sh", "downloaded_repos/yezz123_authx/scripts/docs_build.sh", "downloaded_repos/yezz123_authx/scripts/docs_serve.sh", "downloaded_repos/yezz123_authx/scripts/format.sh", "downloaded_repos/yezz123_authx/scripts/mypy.sh", "downloaded_repos/yezz123_authx/scripts/requirements.sh", "downloaded_repos/yezz123_authx/scripts/test.sh", "downloaded_repos/yezz123_authx/scripts/test_extra.sh", "downloaded_repos/yezz123_authx/uv.lock"], "skipped": [{"path": "downloaded_repos/yezz123_authx/.github/workflows/release.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/yezz123_authx/examples/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/yezz123_authx/examples/tests/conftest.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/yezz123_authx/examples/tests/test_examples.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/yezz123_authx/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/yezz123_authx/tests/app/conftest.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/yezz123_authx/tests/app/test_access_location.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/yezz123_authx/tests/app/test_blocklist_token.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/yezz123_authx/tests/app/test_fresh_token.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/yezz123_authx/tests/app/test_get_subject.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/yezz123_authx/tests/app/test_token_protected_access.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/yezz123_authx/tests/extend/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/yezz123_authx/tests/extend/test_authx_extend.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/yezz123_authx/tests/internal/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/yezz123_authx/tests/internal/test_logger.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/yezz123_authx/tests/internal/test_utils.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/yezz123_authx/tests/test_authx.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/yezz123_authx/tests/test_callback.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/yezz123_authx/tests/test_config.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/yezz123_authx/tests/test_core.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/yezz123_authx/tests/test_dependencies.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/yezz123_authx/tests/test_errors.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/yezz123_authx/tests/test_implicit_middleware.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/yezz123_authx/tests/test_schema.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/yezz123_authx/tests/test_token.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/yezz123_authx/tests/utils.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.773758888244629, "profiling_times": {"config_time": 6.161427974700928, "core_time": 3.0470659732818604, "ignores_time": 0.001996755599975586, "total_time": 9.211379528045654}, "parsing_time": {"total_time": 0.5666770935058594, "per_file_time": {"mean": 0.014912555092259454, "std_dev": 0.00026926725039329866}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 4.275847911834717, "per_file_time": {"mean": 0.018272854324079987, "std_dev": 0.003176301814835061}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 1.2889320850372314, "per_file_and_rule_time": {"mean": 0.0024979303973589754, "std_dev": 3.443373385150924e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.5034022331237793, "per_def_and_rule_time": {"mean": 0.00037178894617708954, "std_dev": 4.7068077915520116e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}