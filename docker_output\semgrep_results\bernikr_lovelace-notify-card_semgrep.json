{"version": "1.130.0", "results": [{"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/bernikr_lovelace-notify-card/notify-card.js", "start": {"line": 34, "col": 7, "offset": 1144}, "end": {"line": 38, "col": 7, "offset": 1317}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/bernikr_lovelace-notify-card/notify-card.js", "start": {"line": 42, "col": 5, "offset": 1388}, "end": {"line": 49, "col": 7, "offset": 1679}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/bernik<PERSON>_lovelace-notify-card/LICENSE", "downloaded_repos/bernikr_lovelace-notify-card/README.md", "downloaded_repos/bernikr_lovelace-notify-card/card.jpg", "downloaded_repos/bernikr_lovelace-notify-card/hacs.json", "downloaded_repos/bernikr_lovelace-notify-card/notify-card.js"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.9816219806671143, "profiling_times": {"config_time": 6.***************, "core_time": 2.888864278793335, "ignores_time": 0.0019114017486572266, "total_time": 9.380301475524902}, "parsing_time": {"total_time": 0.0327601432800293, "per_file_time": {"mean": 0.01638007164001465, "std_dev": 4.9857786507345736e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.1526503562927246, "per_file_time": {"mean": 0.012720863024393717, "std_dev": 0.00107554782981683}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.06258654594421387, "per_file_and_rule_time": {"mean": 0.003911659121513367, "std_dev": 3.425610678475799e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.017663240432739258, "per_def_and_rule_time": {"mean": 0.00036798417568206787, "std_dev": 2.7441899870990965e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089476480}, "engine_requested": "OSS", "skipped_rules": []}