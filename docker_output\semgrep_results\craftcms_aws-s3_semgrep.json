{"version": "1.130.0", "results": [], "errors": [], "paths": {"scanned": ["downloaded_repos/craftcms_aws-s3/.ddev/config.yaml", "downloaded_repos/craftcms_aws-s3/.gitattributes", "downloaded_repos/craftcms_aws-s3/.github/CODEOWNERS", "downloaded_repos/craftcms_aws-s3/.github/workflows/ci.yml", "downloaded_repos/craftcms_aws-s3/.github/workflows/create-release.yml", "downloaded_repos/craftcms_aws-s3/.github/workflows/issues.yml", "downloaded_repos/craftcms_aws-s3/.gitignore", "downloaded_repos/craftcms_aws-s3/.lintstagedrc.json", "downloaded_repos/craftcms_aws-s3/.prettierignore", "downloaded_repos/craftcms_aws-s3/.prettierrc.json", "downloaded_repos/craftcms_aws-s3/CHANGELOG.md", "downloaded_repos/craftcms_aws-s3/LICENSE.md", "downloaded_repos/craftcms_aws-s3/README.md", "downloaded_repos/craftcms_aws-s3/composer.json", "downloaded_repos/craftcms_aws-s3/composer.lock", "downloaded_repos/craftcms_aws-s3/ecs.php", "downloaded_repos/craftcms_aws-s3/package-lock.json", "downloaded_repos/craftcms_aws-s3/package.json", "downloaded_repos/craftcms_aws-s3/phpstan.neon", "downloaded_repos/craftcms_aws-s3/src/AwsS3Bundle.php", "downloaded_repos/craftcms_aws-s3/src/Fs.php", "downloaded_repos/craftcms_aws-s3/src/Plugin.php", "downloaded_repos/craftcms_aws-s3/src/S3Client.php", "downloaded_repos/craftcms_aws-s3/src/controllers/BucketsController.php", "downloaded_repos/craftcms_aws-s3/src/icon.svg", "downloaded_repos/craftcms_aws-s3/src/migrations/Install.php", "downloaded_repos/craftcms_aws-s3/src/migrations/m220119_204627_update_fs_configs.php", "downloaded_repos/craftcms_aws-s3/src/resources/js/editVolume.js", "downloaded_repos/craftcms_aws-s3/src/templates/fsSettings.html"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.7623589038848877, "profiling_times": {"config_time": 6.641403913497925, "core_time": 2.579787492752075, "ignores_time": 0.0020143985748291016, "total_time": 9.224163293838501}, "parsing_time": {"total_time": 0.2987232208251953, "per_file_time": {"mean": 0.01659573449028863, "std_dev": 0.0002020620029284539}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.9029726982116699, "per_file_time": {"mean": 0.01172691815859312, "std_dev": 0.0008200974767692393}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.16231203079223633, "per_file_and_rule_time": {"mean": 0.0010967029107583535, "std_dev": 7.2366278157818615e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.0038313865661621094, "per_def_and_rule_time": {"mean": 0.0001665820246157439, "std_dev": 3.3205189077656314e-08}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}