{"version": "1.130.0", "results": [{"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/feincms_feincms/feincms/admin/filters.py", "start": {"line": 68, "col": 28, "offset": 2289}, "end": {"line": 68, "col": 55, "offset": 2316}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/feincms_feincms/feincms/admin/filters.py", "start": {"line": 116, "col": 28, "offset": 3919}, "end": {"line": 116, "col": 55, "offset": 3946}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/feincms_feincms/feincms/admin/tree_editor.py", "start": {"line": 49, "col": 12, "offset": 1563}, "end": {"line": 49, "col": 74, "offset": 1625}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/feincms_feincms/feincms/admin/tree_editor.py", "start": {"line": 105, "col": 12, "offset": 3576}, "end": {"line": 105, "col": 33, "offset": 3597}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/feincms_feincms/feincms/admin/tree_editor.py", "start": {"line": 287, "col": 16, "offset": 10055}, "end": {"line": 287, "col": 28, "offset": 10067}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.direct-use-of-httpresponse.direct-use-of-httpresponse", "path": "downloaded_repos/feincms_feincms/feincms/admin/tree_editor.py", "start": {"line": 342, "col": 20, "offset": 11921}, "end": {"line": 344, "col": 14, "offset": 12027}, "extra": {"message": "Detected data rendered directly to the end user via 'HttpResponse' or a similar object. This bypasses Django's built-in cross-site scripting (XSS) defenses and could result in an XSS vulnerability. Use Django's template engine to safely render HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.1/intro/tutorial03/#a-shortcut-render", "https://docs.djangoproject.com/en/3.1/topics/http/shortcuts/#render"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.direct-use-of-httpresponse.direct-use-of-httpresponse", "shortlink": "https://sg.run/EknN"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.direct-use-of-httpresponse.direct-use-of-httpresponse", "path": "downloaded_repos/feincms_feincms/feincms/admin/tree_editor.py", "start": {"line": 349, "col": 20, "offset": 12144}, "end": {"line": 349, "col": 77, "offset": 12201}, "extra": {"message": "Detected data rendered directly to the end user via 'HttpResponse' or a similar object. This bypasses Django's built-in cross-site scripting (XSS) defenses and could result in an XSS vulnerability. Use Django's template engine to safely render HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.1/intro/tutorial03/#a-shortcut-render", "https://docs.djangoproject.com/en/3.1/topics/http/shortcuts/#render"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.direct-use-of-httpresponse.direct-use-of-httpresponse", "shortlink": "https://sg.run/EknN"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.direct-use-of-httpresponse.direct-use-of-httpresponse", "path": "downloaded_repos/feincms_feincms/feincms/admin/tree_editor.py", "start": {"line": 363, "col": 20, "offset": 12668}, "end": {"line": 365, "col": 14, "offset": 12774}, "extra": {"message": "Detected data rendered directly to the end user via 'HttpResponse' or a similar object. This bypasses Django's built-in cross-site scripting (XSS) defenses and could result in an XSS vulnerability. Use Django's template engine to safely render HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.1/intro/tutorial03/#a-shortcut-render", "https://docs.djangoproject.com/en/3.1/topics/http/shortcuts/#render"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.direct-use-of-httpresponse.direct-use-of-httpresponse", "shortlink": "https://sg.run/EknN"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.direct-use-of-httpresponse.direct-use-of-httpresponse", "path": "downloaded_repos/feincms_feincms/feincms/admin/tree_editor.py", "start": {"line": 388, "col": 20, "offset": 13452}, "end": {"line": 388, "col": 80, "offset": 13512}, "extra": {"message": "Detected data rendered directly to the end user via 'HttpResponse' or a similar object. This bypasses Django's built-in cross-site scripting (XSS) defenses and could result in an XSS vulnerability. Use Django's template engine to safely render HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.1/intro/tutorial03/#a-shortcut-render", "https://docs.djangoproject.com/en/3.1/topics/http/shortcuts/#render"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.direct-use-of-httpresponse.direct-use-of-httpresponse", "shortlink": "https://sg.run/EknN"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/feincms_feincms/feincms/admin/tree_editor.py", "start": {"line": 421, "col": 43, "offset": 14763}, "end": {"line": 423, "col": 10, "offset": 14857}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/feincms_feincms/feincms/admin/tree_editor.py", "start": {"line": 424, "col": 40, "offset": 14897}, "end": {"line": 432, "col": 10, "offset": 15147}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/feincms_feincms/feincms/admin/tree_editor.py", "start": {"line": 517, "col": 16, "offset": 18427}, "end": {"line": 517, "col": 67, "offset": 18478}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/feincms_feincms/feincms/content/application/models.py", "start": {"line": 348, "col": 44, "offset": 11968}, "end": {"line": 348, "col": 85, "offset": 12009}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/feincms_feincms/feincms/content/application/models.py", "start": {"line": 366, "col": 36, "offset": 12710}, "end": {"line": 366, "col": 53, "offset": 12727}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/feincms_feincms/feincms/content/raw/models.py", "start": {"line": 22, "col": 16, "offset": 569}, "end": {"line": 22, "col": 36, "offset": 589}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/feincms_feincms/feincms/extensions/translations.py", "start": {"line": 307, "col": 20, "offset": 11877}, "end": {"line": 307, "col": 48, "offset": 11905}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.sqlalchemy.security.sqlalchemy-execute-raw-query.sqlalchemy-execute-raw-query", "path": "downloaded_repos/feincms_feincms/feincms/models.py", "start": {"line": 177, "col": 13, "offset": 5581}, "end": {"line": 177, "col": 38, "offset": 5606}, "extra": {"message": "Avoiding SQL string concatenation: untrusted input concatenated with raw SQL query can result in SQL Injection. In order to execute raw query safely, prepared statement should be used. SQLAlchemy provides TextualSQL to easily used prepared statement with named parameters. For complex SQL composition, use SQL Expression Language or Schema Definition Language. In most cases, SQLAlchemy ORM will be a better option.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://docs.sqlalchemy.org/en/14/core/tutorial.html#using-textual-sql", "https://www.tutorialspoint.com/sqlalchemy/sqlalchemy_quick_guide.htm", "https://docs.sqlalchemy.org/en/14/core/tutorial.html#using-more-specific-text-with-table-expression-literal-column-and-expression-column"], "category": "security", "technology": ["sqlalchemy"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/python.sqlalchemy.security.sqlalchemy-execute-raw-query.sqlalchemy-execute-raw-query", "shortlink": "https://sg.run/2b1L"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/feincms_feincms/feincms/module/medialibrary/modeladmins.py", "start": {"line": 135, "col": 20, "offset": 4997}, "end": {"line": 141, "col": 14, "offset": 5223}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/feincms_feincms/feincms/module/medialibrary/modeladmins.py", "start": {"line": 177, "col": 16, "offset": 6320}, "end": {"line": 177, "col": 28, "offset": 6332}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/feincms_feincms/feincms/module/medialibrary/modeladmins.py", "start": {"line": 191, "col": 16, "offset": 6751}, "end": {"line": 205, "col": 10, "offset": 7248}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/feincms_feincms/feincms/module/page/forms.py", "start": {"line": 142, "col": 29, "offset": 5399}, "end": {"line": 145, "col": 30, "offset": 5591}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-translate-as-no-escape.template-translate-as-no-escape", "path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/recover_form.html", "start": {"line": 7, "col": 51, "offset": 184}, "end": {"line": 10, "col": 157, "offset": 605}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-translate-as-no-escape.template-translate-as-no-escape", "shortlink": "https://sg.run/PJDz"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/recover_form.html", "start": {"line": 10, "col": 70, "offset": 518}, "end": {"line": 10, "col": 125, "offset": 573}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/recover_form.html", "start": {"line": 18, "col": 12, "offset": 767}, "end": {"line": 18, "col": 28, "offset": 783}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-translate-as-no-escape.template-translate-as-no-escape", "path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/revision_form.html", "start": {"line": 12, "col": 76, "offset": 707}, "end": {"line": 13, "col": 95, "offset": 836}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-translate-as-no-escape.template-translate-as-no-escape", "shortlink": "https://sg.run/PJDz"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/revision_form.html", "start": {"line": 13, "col": 17, "offset": 758}, "end": {"line": 13, "col": 72, "offset": 813}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/revision_form.html", "start": {"line": 20, "col": 12, "offset": 959}, "end": {"line": 20, "col": 28, "offset": 975}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/feincms_feincms/feincms/templates/admin/filter.html", "start": {"line": 3, "col": 5, "offset": 149}, "end": {"line": 3, "col": 48, "offset": 192}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/feincms_feincms/feincms/templates/admin/medialibrary/add_to_category.html", "start": {"line": 13, "col": 1, "offset": 293}, "end": {"line": 25, "col": 8, "offset": 738}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/feincms_feincms/feincms/templates/admin/medialibrary/mediafile/change_list.html", "start": {"line": 7, "col": 5, "offset": 119}, "end": {"line": 25, "col": 12, "offset": 838}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/feincms_feincms/feincms/templatetags/feincms_tags.py", "start": {"line": 69, "col": 12, "offset": 2184}, "end": {"line": 74, "col": 6, "offset": 2365}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/feincms_feincms/feincms/utils/__init__.py", "start": {"line": 23, "col": 16, "offset": 679}, "end": {"line": 23, "col": 35, "offset": 698}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/feincms_feincms/feincms/utils/__init__.py", "start": {"line": 29, "col": 28, "offset": 849}, "end": {"line": 29, "col": 46, "offset": 867}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/feincms_feincms/.github/workflows/tests.yml", "start": {"line": 34, "col": 50, "offset": 763}, "end": {"line": 34, "col": 73, "offset": 786}}]], "message": "Syntax error at line downloaded_repos/feincms_feincms/.github/workflows/tests.yml:34:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ matrix.python-version` was unexpected", "path": "downloaded_repos/feincms_feincms/.github/workflows/tests.yml", "spans": [{"file": "downloaded_repos/feincms_feincms/.github/workflows/tests.yml", "start": {"line": 34, "col": 50, "offset": 763}, "end": {"line": 34, "col": 73, "offset": 786}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/content_inline.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 53, "offset": 52}}]], "message": "Syntax error at line downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/content_inline.html:1:\n `{% load i18n admin_urls static feincms_admin_tags %}` was unexpected", "path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/content_inline.html", "spans": [{"file": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/content_inline.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 53, "offset": 52}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/item_editor.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 15, "offset": 220}}, {"path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/item_editor.html", "start": {"line": 12, "col": 1, "offset": 0}, "end": {"line": 16, "col": 86, "offset": 238}}, {"path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/item_editor.html", "start": {"line": 23, "col": 1, "offset": 0}, "end": {"line": 30, "col": 27, "offset": 280}}, {"path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/item_editor.html", "start": {"line": 42, "col": 5, "offset": 0}, "end": {"line": 48, "col": 20, "offset": 151}}, {"path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/item_editor.html", "start": {"line": 57, "col": 1, "offset": 0}, "end": {"line": 57, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/item_editor.html:1:\n `{% extends \"admin/change_form.html\" %}\n{% load i18n admin_modify admin_urls static %}\n\n{% block extrahead %}{{ block.super }}\n{% block feincms_jquery_ui %}\n{% include \"admin/feincms/load-jquery.include\" %}\n{% endblock %}` was unexpected", "path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/item_editor.html", "spans": [{"file": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/item_editor.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 15, "offset": 220}}, {"file": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/item_editor.html", "start": {"line": 12, "col": 1, "offset": 0}, "end": {"line": 16, "col": 86, "offset": 238}}, {"file": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/item_editor.html", "start": {"line": 23, "col": 1, "offset": 0}, "end": {"line": 30, "col": 27, "offset": 280}}, {"file": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/item_editor.html", "start": {"line": 42, "col": 5, "offset": 0}, "end": {"line": 48, "col": 20, "offset": 151}}, {"file": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/item_editor.html", "start": {"line": 57, "col": 1, "offset": 0}, "end": {"line": 57, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/page/page/item_editor.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 18, "offset": 107}}, {"path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/page/page/item_editor.html", "start": {"line": 15, "col": 1, "offset": 0}, "end": {"line": 17, "col": 24, "offset": 39}}, {"path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/page/page/item_editor.html", "start": {"line": 31, "col": 1, "offset": 0}, "end": {"line": 31, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/page/page/item_editor.html:1:\n `{% extends \"admin/feincms/item_editor.html\" %}\n\n{% load i18n %}\n\n{% block object-tools %}\n{{ block.super }}` was unexpected", "path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/page/page/item_editor.html", "spans": [{"file": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/page/page/item_editor.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 18, "offset": 107}}, {"file": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/page/page/item_editor.html", "start": {"line": 15, "col": 1, "offset": 0}, "end": {"line": 17, "col": 24, "offset": 39}}, {"file": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/page/page/item_editor.html", "start": {"line": 31, "col": 1, "offset": 0}, "end": {"line": 31, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/page/page/tree_editor.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 24, "offset": 88}}, {"path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/page/page/tree_editor.html", "start": {"line": 10, "col": 1, "offset": 0}, "end": {"line": 10, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/page/page/tree_editor.html:1:\n `{% extends \"admin/feincms/tree_editor.html\" %}\n\n{% load i18n %}\n\n{% block breadcrumbs %}` was unexpected", "path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/page/page/tree_editor.html", "spans": [{"file": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/page/page/tree_editor.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 24, "offset": 88}}, {"file": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/page/page/tree_editor.html", "start": {"line": 10, "col": 1, "offset": 0}, "end": {"line": 10, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/recover_form.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 24, "offset": 99}}, {"path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/recover_form.html", "start": {"line": 13, "col": 1, "offset": 0}, "end": {"line": 17, "col": 21, "offset": 76}}, {"path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/recover_form.html", "start": {"line": 19, "col": 1, "offset": 0}, "end": {"line": 22, "col": 97, "offset": 206}}]], "message": "Syntax error at line downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/recover_form.html:1:\n `{% extends \"admin/feincms/item_editor.html\" %}\n\n{% load i18n admin_urls %}\n\n{% block breadcrumbs %}` was unexpected", "path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/recover_form.html", "spans": [{"file": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/recover_form.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 24, "offset": 99}}, {"file": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/recover_form.html", "start": {"line": 13, "col": 1, "offset": 0}, "end": {"line": 17, "col": 21, "offset": 76}}, {"file": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/recover_form.html", "start": {"line": 19, "col": 1, "offset": 0}, "end": {"line": 22, "col": 97, "offset": 206}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/revision_form.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 24, "offset": 163}}, {"path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/revision_form.html", "start": {"line": 15, "col": 1, "offset": 0}, "end": {"line": 19, "col": 21, "offset": 76}}, {"path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/revision_form.html", "start": {"line": 21, "col": 1, "offset": 0}, "end": {"line": 24, "col": 97, "offset": 206}}]], "message": "Syntax error at line downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/revision_form.html:1:\n `{% extends \"admin/feincms/item_editor.html\" %}\n\n{% load i18n admin_urls %}\n\n{# override of breadcrumbs only required up to Reversion 2.0 #}\n{% block breadcrumbs %}` was unexpected", "path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/revision_form.html", "spans": [{"file": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/revision_form.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 24, "offset": 163}}, {"file": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/revision_form.html", "start": {"line": 15, "col": 1, "offset": 0}, "end": {"line": 19, "col": 21, "offset": 76}}, {"file": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/revision_form.html", "start": {"line": 21, "col": 1, "offset": 0}, "end": {"line": 24, "col": 97, "offset": 206}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/tree_editor.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 18, "offset": 113}}, {"path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/tree_editor.html", "start": {"line": 16, "col": 1, "offset": 0}, "end": {"line": 19, "col": 20, "offset": 36}}, {"path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/tree_editor.html", "start": {"line": 32, "col": 1, "offset": 0}, "end": {"line": 32, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/tree_editor.html:1:\n `{% extends \"admin/change_list.html\" %}\n{% load admin_list i18n static %}\n\n{% block extrahead %}\n{{ block.super }}` was unexpected", "path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/tree_editor.html", "spans": [{"file": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/tree_editor.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 18, "offset": 113}}, {"file": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/tree_editor.html", "start": {"line": 16, "col": 1, "offset": 0}, "end": {"line": 19, "col": 20, "offset": 36}}, {"file": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/tree_editor.html", "start": {"line": 32, "col": 1, "offset": 0}, "end": {"line": 32, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/feincms_feincms/feincms/templates/admin/filter.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 16, "offset": 15}}]], "message": "Syntax error at line downloaded_repos/feincms_feincms/feincms/templates/admin/filter.html:1:\n `{% load i18n %}` was unexpected", "path": "downloaded_repos/feincms_feincms/feincms/templates/admin/filter.html", "spans": [{"file": "downloaded_repos/feincms_feincms/feincms/templates/admin/filter.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 16, "offset": 15}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/feincms_feincms/feincms/templates/admin/medialibrary/mediafile/change_list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 37, "offset": 93}}, {"path": "downloaded_repos/feincms_feincms/feincms/templates/admin/medialibrary/mediafile/change_list.html", "start": {"line": 7, "col": 54, "offset": 0}, "end": {"line": 7, "col": 66, "offset": 12}}, {"path": "downloaded_repos/feincms_feincms/feincms/templates/admin/medialibrary/mediafile/change_list.html", "start": {"line": 23, "col": 51, "offset": 0}, "end": {"line": 23, "col": 56, "offset": 5}}, {"path": "downloaded_repos/feincms_feincms/feincms/templates/admin/medialibrary/mediafile/change_list.html", "start": {"line": 39, "col": 1, "offset": 0}, "end": {"line": 39, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/feincms_feincms/feincms/templates/admin/medialibrary/mediafile/change_list.html:1:\n `{% extends \"admin/change_list.html\" %}\n\n{% load i18n %}\n\n{% block content %}{{ block.super }}` was unexpected", "path": "downloaded_repos/feincms_feincms/feincms/templates/admin/medialibrary/mediafile/change_list.html", "spans": [{"file": "downloaded_repos/feincms_feincms/feincms/templates/admin/medialibrary/mediafile/change_list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 37, "offset": 93}}, {"file": "downloaded_repos/feincms_feincms/feincms/templates/admin/medialibrary/mediafile/change_list.html", "start": {"line": 7, "col": 54, "offset": 0}, "end": {"line": 7, "col": 66, "offset": 12}}, {"file": "downloaded_repos/feincms_feincms/feincms/templates/admin/medialibrary/mediafile/change_list.html", "start": {"line": 23, "col": 51, "offset": 0}, "end": {"line": 23, "col": 56, "offset": 5}}, {"file": "downloaded_repos/feincms_feincms/feincms/templates/admin/medialibrary/mediafile/change_list.html", "start": {"line": 39, "col": 1, "offset": 0}, "end": {"line": 39, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/feincms_feincms/feincms/templates/content/filer/download.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 39, "offset": 38}}, {"path": "downloaded_repos/feincms_feincms/feincms/templates/content/filer/download.html", "start": {"line": 8, "col": 1, "offset": 0}, "end": {"line": 8, "col": 14, "offset": 13}}]], "message": "Syntax error at line downloaded_repos/feincms_feincms/feincms/templates/content/filer/download.html:1:\n `{% with mediafile=content.mediafile %}` was unexpected", "path": "downloaded_repos/feincms_feincms/feincms/templates/content/filer/download.html", "spans": [{"file": "downloaded_repos/feincms_feincms/feincms/templates/content/filer/download.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 39, "offset": 38}}, {"file": "downloaded_repos/feincms_feincms/feincms/templates/content/filer/download.html", "start": {"line": 8, "col": 1, "offset": 0}, "end": {"line": 8, "col": 14, "offset": 13}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/feincms_feincms/feincms/templates/content/section/default.html", "start": {"line": 3, "col": 1, "offset": 0}, "end": {"line": 3, "col": 27, "offset": 26}}, {"path": "downloaded_repos/feincms_feincms/feincms/templates/content/section/default.html", "start": {"line": 5, "col": 1, "offset": 0}, "end": {"line": 7, "col": 28, "offset": 40}}]], "message": "Syntax error at line downloaded_repos/feincms_feincms/feincms/templates/content/section/default.html:3:\n `{% if content.mediafile %}` was unexpected", "path": "downloaded_repos/feincms_feincms/feincms/templates/content/section/default.html", "spans": [{"file": "downloaded_repos/feincms_feincms/feincms/templates/content/section/default.html", "start": {"line": 3, "col": 1, "offset": 0}, "end": {"line": 3, "col": 27, "offset": 26}}, {"file": "downloaded_repos/feincms_feincms/feincms/templates/content/section/default.html", "start": {"line": 5, "col": 1, "offset": 0}, "end": {"line": 7, "col": 28, "offset": 40}}]}], "paths": {"scanned": ["downloaded_repos/feincms_feincms/.editorconfig", "downloaded_repos/feincms_feincms/.eslintrc.js", "downloaded_repos/feincms_feincms/.github/workflows/tests.yml", "downloaded_repos/feincms_feincms/.gitignore", "downloaded_repos/feincms_feincms/.mailmap", "downloaded_repos/feincms_feincms/.pre-commit-config.yaml", "downloaded_repos/feincms_feincms/.prettierignore", "downloaded_repos/feincms_feincms/.readthedocs.yaml", "downloaded_repos/feincms_feincms/.tx/config", "downloaded_repos/feincms_feincms/AUTHORS", "downloaded_repos/feincms_feincms/CHANGELOG.rst", "downloaded_repos/feincms_feincms/CONTRIBUTING.rst", "downloaded_repos/feincms_feincms/LICENSE", "downloaded_repos/feincms_feincms/MANIFEST.in", "downloaded_repos/feincms_feincms/README.rst", "downloaded_repos/feincms_feincms/docs/Makefile", "downloaded_repos/feincms_feincms/docs/admin.rst", "downloaded_repos/feincms_feincms/docs/advanced/base.rst", "downloaded_repos/feincms_feincms/docs/advanced/caching.rst", "downloaded_repos/feincms_feincms/docs/advanced/designdecisions.rst", "downloaded_repos/feincms_feincms/docs/advanced/index.rst", "downloaded_repos/feincms_feincms/docs/advanced/utils.rst", "downloaded_repos/feincms_feincms/docs/conf.py", "downloaded_repos/feincms_feincms/docs/contenttypes.rst", "downloaded_repos/feincms_feincms/docs/contributing.rst", "downloaded_repos/feincms_feincms/docs/deprecation.rst", "downloaded_repos/feincms_feincms/docs/extensions.rst", "downloaded_repos/feincms_feincms/docs/faq.rst", "downloaded_repos/feincms_feincms/docs/images/item_editor_content.png", "downloaded_repos/feincms_feincms/docs/images/item_editor_properties.png", "downloaded_repos/feincms_feincms/docs/images/tree_editor.png", "downloaded_repos/feincms_feincms/docs/index.rst", "downloaded_repos/feincms_feincms/docs/installation.rst", "downloaded_repos/feincms_feincms/docs/integration.rst", "downloaded_repos/feincms_feincms/docs/medialibrary.rst", "downloaded_repos/feincms_feincms/docs/migrations.rst", "downloaded_repos/feincms_feincms/docs/page.rst", "downloaded_repos/feincms_feincms/docs/releases/1.10.rst", "downloaded_repos/feincms_feincms/docs/releases/1.11.rst", "downloaded_repos/feincms_feincms/docs/releases/1.12.rst", "downloaded_repos/feincms_feincms/docs/releases/1.13.rst", "downloaded_repos/feincms_feincms/docs/releases/1.2.rst", "downloaded_repos/feincms_feincms/docs/releases/1.3.rst", "downloaded_repos/feincms_feincms/docs/releases/1.4.rst", "downloaded_repos/feincms_feincms/docs/releases/1.5.rst", "downloaded_repos/feincms_feincms/docs/releases/1.6.rst", "downloaded_repos/feincms_feincms/docs/releases/1.7.rst", "downloaded_repos/feincms_feincms/docs/releases/1.8.rst", "downloaded_repos/feincms_feincms/docs/releases/1.9.rst", "downloaded_repos/feincms_feincms/docs/settings.rst", "downloaded_repos/feincms_feincms/docs/templatetags.rst", "downloaded_repos/feincms_feincms/docs/versioning.rst", "downloaded_repos/feincms_feincms/feincms/__init__.py", "downloaded_repos/feincms_feincms/feincms/_internal.py", "downloaded_repos/feincms_feincms/feincms/admin/__init__.py", "downloaded_repos/feincms_feincms/feincms/admin/filters.py", "downloaded_repos/feincms_feincms/feincms/admin/item_editor.py", "downloaded_repos/feincms_feincms/feincms/admin/tree_editor.py", "downloaded_repos/feincms_feincms/feincms/apps.py", "downloaded_repos/feincms_feincms/feincms/content/__init__.py", "downloaded_repos/feincms_feincms/feincms/content/application/__init__.py", "downloaded_repos/feincms_feincms/feincms/content/application/models.py", "downloaded_repos/feincms_feincms/feincms/content/contactform/__init__.py", "downloaded_repos/feincms_feincms/feincms/content/contactform/models.py", "downloaded_repos/feincms_feincms/feincms/content/file/__init__.py", "downloaded_repos/feincms_feincms/feincms/content/file/models.py", "downloaded_repos/feincms_feincms/feincms/content/filer/__init__.py", "downloaded_repos/feincms_feincms/feincms/content/filer/models.py", "downloaded_repos/feincms_feincms/feincms/content/image/__init__.py", "downloaded_repos/feincms_feincms/feincms/content/image/models.py", "downloaded_repos/feincms_feincms/feincms/content/medialibrary/__init__.py", "downloaded_repos/feincms_feincms/feincms/content/medialibrary/models.py", "downloaded_repos/feincms_feincms/feincms/content/raw/__init__.py", "downloaded_repos/feincms_feincms/feincms/content/raw/models.py", "downloaded_repos/feincms_feincms/feincms/content/richtext/__init__.py", "downloaded_repos/feincms_feincms/feincms/content/richtext/models.py", "downloaded_repos/feincms_feincms/feincms/content/section/__init__.py", "downloaded_repos/feincms_feincms/feincms/content/section/models.py", "downloaded_repos/feincms_feincms/feincms/content/template/__init__.py", "downloaded_repos/feincms_feincms/feincms/content/template/models.py", "downloaded_repos/feincms_feincms/feincms/content/video/__init__.py", "downloaded_repos/feincms_feincms/feincms/content/video/models.py", "downloaded_repos/feincms_feincms/feincms/contents.py", "downloaded_repos/feincms_feincms/feincms/context_processors.py", "downloaded_repos/feincms_feincms/feincms/contrib/__init__.py", "downloaded_repos/feincms_feincms/feincms/contrib/fields.py", "downloaded_repos/feincms_feincms/feincms/contrib/preview/__init__.py", "downloaded_repos/feincms_feincms/feincms/contrib/preview/urls.py", "downloaded_repos/feincms_feincms/feincms/contrib/preview/views.py", "downloaded_repos/feincms_feincms/feincms/contrib/richtext.py", "downloaded_repos/feincms_feincms/feincms/contrib/tagging.py", "downloaded_repos/feincms_feincms/feincms/default_settings.py", "downloaded_repos/feincms_feincms/feincms/extensions/__init__.py", "downloaded_repos/feincms_feincms/feincms/extensions/base.py", "downloaded_repos/feincms_feincms/feincms/extensions/changedate.py", "downloaded_repos/feincms_feincms/feincms/extensions/ct_tracker.py", "downloaded_repos/feincms_feincms/feincms/extensions/datepublisher.py", "downloaded_repos/feincms_feincms/feincms/extensions/featured.py", "downloaded_repos/feincms_feincms/feincms/extensions/seo.py", "downloaded_repos/feincms_feincms/feincms/extensions/translations.py", "downloaded_repos/feincms_feincms/feincms/locale/ca/LC_MESSAGES/django.mo", "downloaded_repos/feincms_feincms/feincms/locale/ca/LC_MESSAGES/django.po", "downloaded_repos/feincms_feincms/feincms/locale/cs/LC_MESSAGES/django.mo", "downloaded_repos/feincms_feincms/feincms/locale/cs/LC_MESSAGES/django.po", "downloaded_repos/feincms_feincms/feincms/locale/de/LC_MESSAGES/django.mo", "downloaded_repos/feincms_feincms/feincms/locale/de/LC_MESSAGES/django.po", "downloaded_repos/feincms_feincms/feincms/locale/en/LC_MESSAGES/django.mo", "downloaded_repos/feincms_feincms/feincms/locale/en/LC_MESSAGES/django.po", "downloaded_repos/feincms_feincms/feincms/locale/es/LC_MESSAGES/django.mo", "downloaded_repos/feincms_feincms/feincms/locale/es/LC_MESSAGES/django.po", "downloaded_repos/feincms_feincms/feincms/locale/fr/LC_MESSAGES/django.mo", "downloaded_repos/feincms_feincms/feincms/locale/fr/LC_MESSAGES/django.po", "downloaded_repos/feincms_feincms/feincms/locale/hr/LC_MESSAGES/django.mo", "downloaded_repos/feincms_feincms/feincms/locale/hr/LC_MESSAGES/django.po", "downloaded_repos/feincms_feincms/feincms/locale/it/LC_MESSAGES/django.mo", "downloaded_repos/feincms_feincms/feincms/locale/it/LC_MESSAGES/django.po", "downloaded_repos/feincms_feincms/feincms/locale/nb/LC_MESSAGES/django.mo", "downloaded_repos/feincms_feincms/feincms/locale/nb/LC_MESSAGES/django.po", "downloaded_repos/feincms_feincms/feincms/locale/nl/LC_MESSAGES/django.mo", "downloaded_repos/feincms_feincms/feincms/locale/nl/LC_MESSAGES/django.po", "downloaded_repos/feincms_feincms/feincms/locale/pl/LC_MESSAGES/django.mo", "downloaded_repos/feincms_feincms/feincms/locale/pl/LC_MESSAGES/django.po", "downloaded_repos/feincms_feincms/feincms/locale/pt/LC_MESSAGES/django.mo", "downloaded_repos/feincms_feincms/feincms/locale/pt/LC_MESSAGES/django.po", "downloaded_repos/feincms_feincms/feincms/locale/pt/LC_MESSAGES/djangojs.mo", "downloaded_repos/feincms_feincms/feincms/locale/pt/LC_MESSAGES/djangojs.po", "downloaded_repos/feincms_feincms/feincms/locale/pt_BR/LC_MESSAGES/django.mo", "downloaded_repos/feincms_feincms/feincms/locale/pt_BR/LC_MESSAGES/django.po", "downloaded_repos/feincms_feincms/feincms/locale/ro/LC_MESSAGES/django.mo", "downloaded_repos/feincms_feincms/feincms/locale/ro/LC_MESSAGES/django.po", "downloaded_repos/feincms_feincms/feincms/locale/ru/LC_MESSAGES/django.mo", "downloaded_repos/feincms_feincms/feincms/locale/ru/LC_MESSAGES/django.po", "downloaded_repos/feincms_feincms/feincms/locale/tr/LC_MESSAGES/django.mo", "downloaded_repos/feincms_feincms/feincms/locale/tr/LC_MESSAGES/django.po", "downloaded_repos/feincms_feincms/feincms/locale/zh_CN/LC_MESSAGES/django.mo", "downloaded_repos/feincms_feincms/feincms/locale/zh_CN/LC_MESSAGES/django.po", "downloaded_repos/feincms_feincms/feincms/management/__init__.py", "downloaded_repos/feincms_feincms/feincms/management/commands/__init__.py", "downloaded_repos/feincms_feincms/feincms/management/commands/medialibrary_orphans.py", "downloaded_repos/feincms_feincms/feincms/management/commands/medialibrary_to_filer.py", "downloaded_repos/feincms_feincms/feincms/management/commands/rebuild_mptt.py", "downloaded_repos/feincms_feincms/feincms/models.py", "downloaded_repos/feincms_feincms/feincms/module/__init__.py", "downloaded_repos/feincms_feincms/feincms/module/extensions/__init__.py", "downloaded_repos/feincms_feincms/feincms/module/extensions/changedate.py", "downloaded_repos/feincms_feincms/feincms/module/extensions/ct_tracker.py", "downloaded_repos/feincms_feincms/feincms/module/extensions/datepublisher.py", "downloaded_repos/feincms_feincms/feincms/module/extensions/featured.py", "downloaded_repos/feincms_feincms/feincms/module/extensions/seo.py", "downloaded_repos/feincms_feincms/feincms/module/extensions/translations.py", "downloaded_repos/feincms_feincms/feincms/module/medialibrary/__init__.py", "downloaded_repos/feincms_feincms/feincms/module/medialibrary/admin.py", "downloaded_repos/feincms_feincms/feincms/module/medialibrary/contents.py", "downloaded_repos/feincms_feincms/feincms/module/medialibrary/fields.py", "downloaded_repos/feincms_feincms/feincms/module/medialibrary/forms.py", "downloaded_repos/feincms_feincms/feincms/module/medialibrary/modeladmins.py", "downloaded_repos/feincms_feincms/feincms/module/medialibrary/models.py", "downloaded_repos/feincms_feincms/feincms/module/medialibrary/thumbnail.py", "downloaded_repos/feincms_feincms/feincms/module/medialibrary/zip.py", "downloaded_repos/feincms_feincms/feincms/module/mixins.py", "downloaded_repos/feincms_feincms/feincms/module/page/__init__.py", "downloaded_repos/feincms_feincms/feincms/module/page/admin.py", "downloaded_repos/feincms_feincms/feincms/module/page/extensions/__init__.py", "downloaded_repos/feincms_feincms/feincms/module/page/extensions/excerpt.py", "downloaded_repos/feincms_feincms/feincms/module/page/extensions/navigation.py", "downloaded_repos/feincms_feincms/feincms/module/page/extensions/navigationgroups.py", "downloaded_repos/feincms_feincms/feincms/module/page/extensions/relatedpages.py", "downloaded_repos/feincms_feincms/feincms/module/page/extensions/sites.py", "downloaded_repos/feincms_feincms/feincms/module/page/extensions/symlinks.py", "downloaded_repos/feincms_feincms/feincms/module/page/extensions/titles.py", "downloaded_repos/feincms_feincms/feincms/module/page/forms.py", "downloaded_repos/feincms_feincms/feincms/module/page/modeladmins.py", "downloaded_repos/feincms_feincms/feincms/module/page/models.py", "downloaded_repos/feincms_feincms/feincms/module/page/processors.py", "downloaded_repos/feincms_feincms/feincms/module/page/sitemap.py", "downloaded_repos/feincms_feincms/feincms/shortcuts.py", "downloaded_repos/feincms_feincms/feincms/signals.py", "downloaded_repos/feincms_feincms/feincms/static/feincms/img/arrow-move.png", "downloaded_repos/feincms_feincms/feincms/static/feincms/img/contenttypes.png", "downloaded_repos/feincms_feincms/feincms/static/feincms/img/default-bg.gif", "downloaded_repos/feincms_feincms/feincms/static/feincms/img/disclosure-down.png", "downloaded_repos/feincms_feincms/feincms/static/feincms/img/disclosure-right.png", "downloaded_repos/feincms_feincms/feincms/static/feincms/img/icon-no.gif", "downloaded_repos/feincms_feincms/feincms/static/feincms/img/icon-unknown.gif", "downloaded_repos/feincms_feincms/feincms/static/feincms/img/icon-yes.gif", "downloaded_repos/feincms_feincms/feincms/static/feincms/img/icon_addlink.gif", "downloaded_repos/feincms_feincms/feincms/static/feincms/img/icon_deletelink.gif", "downloaded_repos/feincms_feincms/feincms/static/feincms/img/selector-search.gif", "downloaded_repos/feincms_feincms/feincms/static/feincms/item_editor.css", "downloaded_repos/feincms_feincms/feincms/static/feincms/item_editor.js", "downloaded_repos/feincms_feincms/feincms/static/feincms/js.cookie.js", "downloaded_repos/feincms_feincms/feincms/static/feincms/tree_editor.css", "downloaded_repos/feincms_feincms/feincms/static/feincms/tree_editor.js", "downloaded_repos/feincms_feincms/feincms/templates/admin/content/richtext/init_ckeditor.html", "downloaded_repos/feincms_feincms/feincms/templates/admin/content/richtext/init_tinymce.html", "downloaded_repos/feincms_feincms/feincms/templates/admin/content/richtext/init_tinymce4.html", "downloaded_repos/feincms_feincms/feincms/templates/admin/content/richtext/init_tinymce7.html", "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/_content_type_buttons.html", "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/_messages_js.html", "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/_regions_js.html", "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/content_editor.html", "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/content_inline.html", "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/content_type_selection_widget.html", "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/item_editor.html", "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/load-jquery.include", "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/page/page/item_editor.html", "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/page/page/tree_editor.html", "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/recover_form.html", "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/revision_form.html", "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/tree_editor.html", "downloaded_repos/feincms_feincms/feincms/templates/admin/filter.html", "downloaded_repos/feincms_feincms/feincms/templates/admin/medialibrary/add_to_category.html", "downloaded_repos/feincms_feincms/feincms/templates/admin/medialibrary/mediafile/change_list.html", "downloaded_repos/feincms_feincms/feincms/templates/breadcrumbs.html", "downloaded_repos/feincms_feincms/feincms/templates/content/contactform/email.txt", "downloaded_repos/feincms_feincms/feincms/templates/content/contactform/form.html", "downloaded_repos/feincms_feincms/feincms/templates/content/contactform/thanks.html", "downloaded_repos/feincms_feincms/feincms/templates/content/file/default.html", "downloaded_repos/feincms_feincms/feincms/templates/content/filer/default.html", "downloaded_repos/feincms_feincms/feincms/templates/content/filer/download.html", "downloaded_repos/feincms_feincms/feincms/templates/content/filer/image.html", "downloaded_repos/feincms_feincms/feincms/templates/content/image/default.html", "downloaded_repos/feincms_feincms/feincms/templates/content/mediafile/default.html", "downloaded_repos/feincms_feincms/feincms/templates/content/mediafile/image.html", "downloaded_repos/feincms_feincms/feincms/templates/content/mediafile/mp3.html", "downloaded_repos/feincms_feincms/feincms/templates/content/richtext/default.html", "downloaded_repos/feincms_feincms/feincms/templates/content/section/default.html", "downloaded_repos/feincms_feincms/feincms/templates/content/section/image.html", "downloaded_repos/feincms_feincms/feincms/templates/content/video/sf.html", "downloaded_repos/feincms_feincms/feincms/templates/content/video/unknown.html", "downloaded_repos/feincms_feincms/feincms/templates/content/video/vimeo.html", "downloaded_repos/feincms_feincms/feincms/templates/content/video/youtube.html", "downloaded_repos/feincms_feincms/feincms/templatetags/__init__.py", "downloaded_repos/feincms_feincms/feincms/templatetags/applicationcontent_tags.py", "downloaded_repos/feincms_feincms/feincms/templatetags/feincms_admin_tags.py", "downloaded_repos/feincms_feincms/feincms/templatetags/feincms_page_tags.py", "downloaded_repos/feincms_feincms/feincms/templatetags/feincms_tags.py", "downloaded_repos/feincms_feincms/feincms/templatetags/feincms_thumbnail.py", "downloaded_repos/feincms_feincms/feincms/templatetags/fragment_tags.py", "downloaded_repos/feincms_feincms/feincms/translations.py", "downloaded_repos/feincms_feincms/feincms/urls.py", "downloaded_repos/feincms_feincms/feincms/utils/__init__.py", "downloaded_repos/feincms_feincms/feincms/utils/managers.py", "downloaded_repos/feincms_feincms/feincms/utils/queryset_transform.py", "downloaded_repos/feincms_feincms/feincms/utils/templatetags.py", "downloaded_repos/feincms_feincms/feincms/utils/tuple.py", "downloaded_repos/feincms_feincms/feincms/views/__init__.py", "downloaded_repos/feincms_feincms/feincms/views/decorators.py", "downloaded_repos/feincms_feincms/setup.cfg", "downloaded_repos/feincms_feincms/setup.py", "downloaded_repos/feincms_feincms/tox.ini"], "skipped": [{"path": "downloaded_repos/feincms_feincms/.github/workflows/tests.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/feincms_feincms/feincms/static/feincms/jquery-1.11.3.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms/feincms/static/feincms/jquery-ui-1.10.3.custom.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/content_inline.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/item_editor.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/page/page/item_editor.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/page/page/tree_editor.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/recover_form.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/revision_form.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/feincms_feincms/feincms/templates/admin/feincms/tree_editor.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/feincms_feincms/feincms/templates/admin/filter.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/feincms_feincms/feincms/templates/admin/medialibrary/mediafile/change_list.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/feincms_feincms/feincms/templates/content/filer/download.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/feincms_feincms/feincms/templates/content/section/default.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/feincms_feincms/tests/manage.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms/tests/testapp/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms/tests/testapp/applicationcontent_urls.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms/tests/testapp/content.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms/tests/testapp/context_processors.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms/tests/testapp/media/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms/tests/testapp/media/somefile.jpg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms/tests/testapp/migrate/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms/tests/testapp/migrate/medialibrary/0001_initial.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms/tests/testapp/migrate/medialibrary/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms/tests/testapp/migrate/page/0001_initial.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms/tests/testapp/migrate/page/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms/tests/testapp/migrations/0001_initial.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms/tests/testapp/migrations/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms/tests/testapp/models.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms/tests/testapp/navigation_extensions.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms/tests/testapp/settings.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms/tests/testapp/templates/404.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms/tests/testapp/templates/alias_reverse_test.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms/tests/testapp/templates/base.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms/tests/testapp/templates/feincms_base.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms/tests/testapp/templates/fragment.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms/tests/testapp/templates/full_reverse_test.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms/tests/testapp/templates/inheritance20.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms/tests/testapp/templates/templatecontent_1.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms/tests/testapp/tests/.gitattributes", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms/tests/testapp/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms/tests/testapp/tests/test_cms.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms/tests/testapp/tests/test_extensions.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms/tests/testapp/tests/test_page.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms/tests/testapp/tests/test_stuff.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms/tests/testapp/tests/utils.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/feincms_feincms/tests/testapp/urls.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.9809110164642334, "profiling_times": {"config_time": 7.171452522277832, "core_time": 5.472208499908447, "ignores_time": 0.0027070045471191406, "total_time": 12.648824453353882}, "parsing_time": {"total_time": 1.5568561553955078, "per_file_time": {"mean": 0.01197581657996544, "std_dev": 0.0010174616112521885}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 21.214329481124878, "per_file_time": {"mean": 0.03248748771994621, "std_dev": 0.02684518193037517}, "very_slow_stats": {"time_ratio": 0.3001442570712505, "count_ratio": 0.006125574272588055}, "very_slow_files": [{"fpath": "downloaded_repos/feincms_feincms/feincms/models.py", "ftime": 1.5071661472320557}, {"fpath": "downloaded_repos/feincms_feincms/feincms/static/feincms/item_editor.js", "ftime": 1.5831210613250732}, {"fpath": "downloaded_repos/feincms_feincms/feincms/admin/tree_editor.py", "ftime": 1.6200919151306152}, {"fpath": "downloaded_repos/feincms_feincms/feincms/static/feincms/tree_editor.js", "ftime": 1.656980037689209}]}, "matching_time": {"total_time": 9.982051849365234, "per_file_and_rule_time": {"mean": 0.01068742168026256, "std_dev": 0.0010668465058216077}, "very_slow_stats": {"time_ratio": 0.3418195050320342, "count_ratio": 0.020342612419700215}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/feincms_feincms/feincms/static/feincms/item_editor.js", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 0.13972806930541992}, {"fpath": "downloaded_repos/feincms_feincms/feincms/module/page/modeladmins.py", "rule_id": "python.django.security.injection.open-redirect.open-redirect", "time": 0.1446211338043213}, {"fpath": "downloaded_repos/feincms_feincms/feincms/static/feincms/tree_editor.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.15552306175231934}, {"fpath": "downloaded_repos/feincms_feincms/feincms/extensions/ct_tracker.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.1598498821258545}, {"fpath": "downloaded_repos/feincms_feincms/feincms/admin/tree_editor.py", "rule_id": "python.django.security.injection.raw-html-format.raw-html-format", "time": 0.16129112243652344}, {"fpath": "downloaded_repos/feincms_feincms/feincms/extensions/translations.py", "rule_id": "python.django.security.injection.open-redirect.open-redirect", "time": 0.1644589900970459}, {"fpath": "downloaded_repos/feincms_feincms/feincms/content/application/models.py", "rule_id": "python.lang.security.dangerous-system-call.dangerous-system-call", "time": 0.17841196060180664}, {"fpath": "downloaded_repos/feincms_feincms/feincms/static/feincms/item_editor.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.28877806663513184}, {"fpath": "downloaded_repos/feincms_feincms/feincms/extensions/translations.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.40629100799560547}, {"fpath": "downloaded_repos/feincms_feincms/feincms/admin/tree_editor.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.5577960014343262}]}, "tainting_time": {"total_time": 3.867432117462158, "per_def_and_rule_time": {"mean": 0.0012391644080301698, "std_dev": 3.658331082217654e-05}, "very_slow_stats": {"time_ratio": 0.20775071563817837, "count_ratio": 0.00256328099967959}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/feincms_feincms/feincms/static/feincms/tree_editor.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.05398201942443848}, {"fpath": "downloaded_repos/feincms_feincms/feincms/static/feincms/item_editor.js", "fline": 1, "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 0.05699014663696289}, {"fpath": "downloaded_repos/feincms_feincms/feincms/static/feincms/tree_editor.js", "fline": 1, "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 0.07043004035949707}, {"fpath": "downloaded_repos/feincms_feincms/feincms/static/feincms/tree_editor.js", "fline": 1, "rule_id": "javascript.express.security.cors-misconfiguration.cors-misconfiguration", "time": 0.08449792861938477}, {"fpath": "downloaded_repos/feincms_feincms/feincms/module/medialibrary/zip.py", "fline": 28, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.10104990005493164}, {"fpath": "downloaded_repos/feincms_feincms/feincms/static/feincms/tree_editor.js", "fline": 1, "rule_id": "javascript.express.security.x-frame-options-misconfiguration.x-frame-options-misconfiguration", "time": 0.12688708305358887}, {"fpath": "downloaded_repos/feincms_feincms/feincms/static/feincms/tree_editor.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.13268589973449707}, {"fpath": "downloaded_repos/feincms_feincms/feincms/static/feincms/tree_editor.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.17693877220153809}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089476480}, "engine_requested": "OSS", "skipped_rules": []}