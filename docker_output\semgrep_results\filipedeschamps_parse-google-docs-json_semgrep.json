{"version": "1.130.0", "results": [], "errors": [], "paths": {"scanned": ["downloaded_repos/filipedeschamps_parse-google-docs-json/.gitignore", "downloaded_repos/filipedeschamps_parse-google-docs-json/LICENSE", "downloaded_repos/filipedeschamps_parse-google-docs-json/README.md", "downloaded_repos/filipedeschamps_parse-google-docs-json/index.d.ts", "downloaded_repos/filipedeschamps_parse-google-docs-json/package-lock.json", "downloaded_repos/filipedeschamps_parse-google-docs-json/package.json", "downloaded_repos/filipedeschamps_parse-google-docs-json/source/index.js", "downloaded_repos/filipedeschamps_parse-google-docs-json/source/parser.js"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 2.1176908016204834, "profiling_times": {"config_time": 7.462782621383667, "core_time": 2.946918487548828, "ignores_time": 0.003813028335571289, "total_time": 10.415552377700806}, "parsing_time": {"total_time": 0.0979769229888916, "per_file_time": {"mean": 0.01959538459777832, "std_dev": 0.00011673889612211497}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.31043219566345215, "per_file_time": {"mean": 0.014782485507783435, "std_dev": 0.0009509738671389502}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.08820080757141113, "per_file_and_rule_time": {"mean": 0.0038348177204961366, "std_dev": 2.7284250092887197e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.01853775978088379, "per_def_and_rule_time": {"mean": 0.000285196304321289, "std_dev": 2.5370056881421566e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}