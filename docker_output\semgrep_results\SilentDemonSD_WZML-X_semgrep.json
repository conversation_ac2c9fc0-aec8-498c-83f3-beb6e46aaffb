{"version": "1.130.0", "results": [{"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/SilentDemonSD_WZML-X/Dockerfile", "start": {"line": 12, "col": 1, "offset": 209}, "end": {"line": 12, "col": 25, "offset": 233}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD [\"bash\", \"start.sh\"]", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.deserialization.pickle.avoid-pickle", "path": "downloaded_repos/SilentDemonSD_WZML-X/add_to_team_drive.py", "start": {"line": 63, "col": 17, "offset": 1825}, "end": {"line": 63, "col": 35, "offset": 1843}, "extra": {"message": "Avoid using `pickle`, which is known to lead to code execution vulnerabilities. When unpickling, the serialized data could be manipulated to run arbitrary code. Instead, consider serializing the relevant data as JSON or a similar text-based serialization format.", "metadata": {"owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "references": ["https://docs.python.org/3/library/pickle.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/python.lang.security.deserialization.pickle.avoid-pickle", "shortlink": "https://sg.run/OPwB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.deserialization.pickle.avoid-pickle", "path": "downloaded_repos/SilentDemonSD_WZML-X/add_to_team_drive.py", "start": {"line": 80, "col": 9, "offset": 2515}, "end": {"line": 80, "col": 34, "offset": 2540}, "extra": {"message": "Avoid using `pickle`, which is known to lead to code execution vulnerabilities. When unpickling, the serialized data could be manipulated to run arbitrary code. Instead, consider serializing the relevant data as JSON or a similar text-based serialization format.", "metadata": {"owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "references": ["https://docs.python.org/3/library/pickle.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/python.lang.security.deserialization.pickle.avoid-pickle", "shortlink": "https://sg.run/OPwB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "path": "downloaded_repos/SilentDemonSD_WZML-X/bot/__init__.py", "start": {"line": 804, "col": 15, "offset": 25567}, "end": {"line": 804, "col": 19, "offset": 25571}, "extra": {"message": "Found 'subprocess' function 'Popen' with 'shell=True'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use 'shell=False' instead.", "fix": "False", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b602_subprocess_popen_with_shell_equals_true.html", "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://stackoverflow.com/questions/3172470/actual-meaning-of-shell-true-in-subprocess", "https://docs.python.org/3/library/subprocess.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["secure default"], "likelihood": "HIGH", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "shortlink": "https://sg.run/J92w"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.eval-detected.eval-detected", "path": "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/ext_utils/leech_utils.py", "start": {"line": 46, "col": 14, "offset": 1469}, "end": {"line": 46, "col": 29, "offset": 1484}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/blacklists/blacklist_calls.html#b307-eval", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.eval-detected.eval-detected", "shortlink": "https://sg.run/ZvrD"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.eval-detected.eval-detected", "path": "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/ext_utils/leech_utils.py", "start": {"line": 80, "col": 16, "offset": 2474}, "end": {"line": 80, "col": 31, "offset": 2489}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/blacklists/blacklist_calls.html#b307-eval", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.eval-detected.eval-detected", "shortlink": "https://sg.run/ZvrD"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.eval-detected.eval-detected", "path": "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/ext_utils/leech_utils.py", "start": {"line": 146, "col": 14, "offset": 5231}, "end": {"line": 146, "col": 29, "offset": 5246}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/blacklists/blacklist_calls.html#b307-eval", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.eval-detected.eval-detected", "shortlink": "https://sg.run/ZvrD"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.eval-detected.eval-detected", "path": "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/download_utils/yt_dlp_download.py", "start": {"line": 372, "col": 25, "offset": 13892}, "end": {"line": 372, "col": 36, "offset": 13903}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/blacklists/blacklist_calls.html#b307-eval", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.eval-detected.eval-detected", "shortlink": "https://sg.run/ZvrD"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.deserialization.pickle.avoid-pickle", "path": "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/upload_utils/gdriveTools.py", "start": {"line": 99, "col": 31, "offset": 3383}, "end": {"line": 99, "col": 39, "offset": 3391}, "extra": {"message": "Avoid using `pickle`, which is known to lead to code execution vulnerabilities. When unpickling, the serialized data could be manipulated to run arbitrary code. Instead, consider serializing the relevant data as JSON or a similar text-based serialization format.", "metadata": {"owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "references": ["https://docs.python.org/3/library/pickle.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/python.lang.security.deserialization.pickle.avoid-pickle", "shortlink": "https://sg.run/OPwB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.deserialization.pickle.avoid-pickle", "path": "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/upload_utils/gdriveTools.py", "start": {"line": 110, "col": 35, "offset": 3834}, "end": {"line": 110, "col": 43, "offset": 3842}, "extra": {"message": "Avoid using `pickle`, which is known to lead to code execution vulnerabilities. When unpickling, the serialized data could be manipulated to run arbitrary code. Instead, consider serializing the relevant data as JSON or a similar text-based serialization format.", "metadata": {"owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "references": ["https://docs.python.org/3/library/pickle.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/python.lang.security.deserialization.pickle.avoid-pickle", "shortlink": "https://sg.run/OPwB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.eval-detected.eval-detected", "path": "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/upload_utils/gdriveTools.py", "start": {"line": 469, "col": 25, "offset": 17764}, "end": {"line": 469, "col": 42, "offset": 17781}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/blacklists/blacklist_calls.html#b307-eval", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.eval-detected.eval-detected", "shortlink": "https://sg.run/ZvrD"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.eval-detected.eval-detected", "path": "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/upload_utils/gdriveTools.py", "start": {"line": 609, "col": 26, "offset": 24053}, "end": {"line": 609, "col": 43, "offset": 24070}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/blacklists/blacklist_calls.html#b307-eval", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.eval-detected.eval-detected", "shortlink": "https://sg.run/ZvrD"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.eval-detected.eval-detected", "path": "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/upload_utils/gdriveTools.py", "start": {"line": 1020, "col": 25, "offset": 41582}, "end": {"line": 1020, "col": 42, "offset": 41599}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/blacklists/blacklist_calls.html#b307-eval", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.eval-detected.eval-detected", "shortlink": "https://sg.run/ZvrD"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/themes/__init__.py", "start": {"line": 11, "col": 35, "offset": 352}, "end": {"line": 11, "col": 83, "offset": 400}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.exec-detected.exec-detected", "path": "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/eval.py", "start": {"line": 83, "col": 9, "offset": 2323}, "end": {"line": 83, "col": 30, "offset": 2344}, "extra": {"message": "Detected the use of exec(). exec() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b102_exec_used.html", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.exec-detected.exec-detected", "shortlink": "https://sg.run/ndRX"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.eval-detected.eval-detected", "path": "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/eval.py", "start": {"line": 103, "col": 38, "offset": 2883}, "end": {"line": 103, "col": 53, "offset": 2898}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/blacklists/blacklist_calls.html#b307-eval", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.eval-detected.eval-detected", "shortlink": "https://sg.run/ZvrD"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.eval-detected.eval-detected", "path": "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/gd_list.py", "start": {"line": 70, "col": 32, "offset": 2404}, "end": {"line": 70, "col": 45, "offset": 2417}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/blacklists/blacklist_calls.html#b307-eval", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.eval-detected.eval-detected", "shortlink": "https://sg.run/ZvrD"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.eval-detected.eval-detected", "path": "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/gd_list.py", "start": {"line": 78, "col": 19, "offset": 2768}, "end": {"line": 78, "col": 32, "offset": 2781}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/blacklists/blacklist_calls.html#b307-eval", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.eval-detected.eval-detected", "shortlink": "https://sg.run/ZvrD"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.eval-detected.eval-detected", "path": "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/torrent_search.py", "start": {"line": 31, "col": 23, "offset": 1040}, "end": {"line": 31, "col": 43, "offset": 1060}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/blacklists/blacklist_calls.html#b307-eval", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.eval-detected.eval-detected", "shortlink": "https://sg.run/ZvrD"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.eval-detected.eval-detected", "path": "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/ytdlp.py", "start": {"line": 578, "col": 25, "offset": 21890}, "end": {"line": 578, "col": 36, "offset": 21901}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/blacklists/blacklist_calls.html#b307-eval", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.eval-detected.eval-detected", "shortlink": "https://sg.run/ZvrD"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.deserialization.pickle.avoid-pickle", "path": "downloaded_repos/SilentDemonSD_WZML-X/gen_sa_accounts.py", "start": {"line": 203, "col": 21, "offset": 5946}, "end": {"line": 203, "col": 35, "offset": 5960}, "extra": {"message": "Avoid using `pickle`, which is known to lead to code execution vulnerabilities. When unpickling, the serialized data could be manipulated to run arbitrary code. Instead, consider serializing the relevant data as JSON or a similar text-based serialization format.", "metadata": {"owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "references": ["https://docs.python.org/3/library/pickle.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/python.lang.security.deserialization.pickle.avoid-pickle", "shortlink": "https://sg.run/OPwB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.deserialization.pickle.avoid-pickle", "path": "downloaded_repos/SilentDemonSD_WZML-X/gen_sa_accounts.py", "start": {"line": 213, "col": 13, "offset": 6312}, "end": {"line": 213, "col": 34, "offset": 6333}, "extra": {"message": "Avoid using `pickle`, which is known to lead to code execution vulnerabilities. When unpickling, the serialized data could be manipulated to run arbitrary code. Instead, consider serializing the relevant data as JSON or a similar text-based serialization format.", "metadata": {"owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "references": ["https://docs.python.org/3/library/pickle.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/python.lang.security.deserialization.pickle.avoid-pickle", "shortlink": "https://sg.run/OPwB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.deserialization.pickle.avoid-pickle", "path": "downloaded_repos/SilentDemonSD_WZML-X/generate_drive_token.py", "start": {"line": 11, "col": 23, "offset": 357}, "end": {"line": 11, "col": 37, "offset": 371}, "extra": {"message": "Avoid using `pickle`, which is known to lead to code execution vulnerabilities. When unpickling, the serialized data could be manipulated to run arbitrary code. Instead, consider serializing the relevant data as JSON or a similar text-based serialization format.", "metadata": {"owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "references": ["https://docs.python.org/3/library/pickle.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/python.lang.security.deserialization.pickle.avoid-pickle", "shortlink": "https://sg.run/OPwB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.deserialization.pickle.avoid-pickle", "path": "downloaded_repos/SilentDemonSD_WZML-X/generate_drive_token.py", "start": {"line": 25, "col": 5, "offset": 859}, "end": {"line": 25, "col": 36, "offset": 890}, "extra": {"message": "Avoid using `pickle`, which is known to lead to code execution vulnerabilities. When unpickling, the serialized data could be manipulated to run arbitrary code. Instead, consider serializing the relevant data as JSON or a similar text-based serialization format.", "metadata": {"owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "references": ["https://docs.python.org/3/library/pickle.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/python.lang.security.deserialization.pickle.avoid-pickle", "shortlink": "https://sg.run/OPwB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "path": "downloaded_repos/SilentDemonSD_WZML-X/update.py", "start": {"line": 70, "col": 66, "offset": 2109}, "end": {"line": 70, "col": 70, "offset": 2113}, "extra": {"message": "Found 'subprocess' function 'scall' with 'shell=True'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use 'shell=False' instead.", "fix": "False", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b602_subprocess_popen_with_shell_equals_true.html", "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://stackoverflow.com/questions/3172470/actual-meaning-of-shell-true-in-subprocess", "https://docs.python.org/3/library/subprocess.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["secure default"], "likelihood": "HIGH", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "shortlink": "https://sg.run/J92w"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "path": "downloaded_repos/SilentDemonSD_WZML-X/update.py", "start": {"line": 95, "col": 15, "offset": 2925}, "end": {"line": 95, "col": 19, "offset": 2929}, "extra": {"message": "Found 'subprocess' function 'srun' with 'shell=True'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use 'shell=False' instead.", "fix": "False", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b602_subprocess_popen_with_shell_equals_true.html", "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://stackoverflow.com/questions/3172470/actual-meaning-of-shell-true-in-subprocess", "https://docs.python.org/3/library/subprocess.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["secure default"], "likelihood": "HIGH", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "shortlink": "https://sg.run/J92w"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/SilentDemonSD_WZML-X/.github/ISSUE_TEMPLATE/bug_report.md", "downloaded_repos/SilentDemonSD_WZML-X/.github/ISSUE_TEMPLATE/feature_request.md", "downloaded_repos/SilentDemonSD_WZML-X/.gitignore", "downloaded_repos/SilentDemonSD_WZML-X/Dockerfile", "downloaded_repos/SilentDemonSD_WZML-X/LICENSE", "downloaded_repos/SilentDemonSD_WZML-X/README.md", "downloaded_repos/SilentDemonSD_WZML-X/add_to_team_drive.py", "downloaded_repos/SilentDemonSD_WZML-X/aria.sh", "downloaded_repos/SilentDemonSD_WZML-X/bot/__init__.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/__main__.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/__init__.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/ext_utils/__init__.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/ext_utils/bot_utils.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/ext_utils/bulk_links.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/ext_utils/db_handler.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/ext_utils/exceptions.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/ext_utils/fs_utils.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/ext_utils/help_messages.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/ext_utils/leech_utils.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/ext_utils/shortners.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/ext_utils/task_manager.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/ext_utils/telegraph_helper.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/listeners/__init__.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/listeners/aria2_listener.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/listeners/direct_listener.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/listeners/qbit_listener.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/listeners/tasks_listener.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/__init__.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/download_utils/__init__.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/download_utils/aria2_download.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/download_utils/direct_downloader.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/download_utils/direct_link_generator.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/download_utils/direct_link_generator_license.md", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/download_utils/gd_download.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/download_utils/mega_download.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/download_utils/qbit_download.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/download_utils/rclone_download.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/download_utils/telegram_download.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/download_utils/yt_dlp_download.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/rclone_utils/__init__.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/rclone_utils/list.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/rclone_utils/serve.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/rclone_utils/transfer.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/status_utils/__init__.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/status_utils/aria2_status.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/status_utils/ddl_status.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/status_utils/direct_status.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/status_utils/extract_status.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/status_utils/gdrive_status.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/status_utils/mega_download_status.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/status_utils/metadata_status.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/status_utils/qbit_status.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/status_utils/queue_status.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/status_utils/rclone_status.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/status_utils/split_status.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/status_utils/telegram_status.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/status_utils/yt_dlp_download_status.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/status_utils/zip_status.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/upload_utils/__init__.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/upload_utils/ddlEngine.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/upload_utils/ddlserver/gofile.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/upload_utils/ddlserver/streamtape.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/upload_utils/gdriveTools.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/upload_utils/pyrogramEngine.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/telegram_helper/__init__.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/telegram_helper/bot_commands.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/telegram_helper/button_build.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/telegram_helper/filters.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/telegram_helper/message_utils.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/themes/README.md", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/themes/__init__.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/themes/wzml_minimal.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/__init__.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/anilist.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/authorize.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/bot_settings.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/broadcast.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/cancel_mirror.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/category_select.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/clone.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/eval.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/gd_clean.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/gd_count.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/gd_delete.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/gd_list.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/gen_pyro_sess.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/images.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/imdb.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/mediainfo.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/mirror_leech.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/mydramalist.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/rss.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/save_msg.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/shell.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/speedtest.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/status.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/torrent_search.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/torrent_select.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/users_settings.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/ytdlp.py", "downloaded_repos/SilentDemonSD_WZML-X/bot/version.py", "downloaded_repos/SilentDemonSD_WZML-X/captain-definition", "downloaded_repos/SilentDemonSD_WZML-X/config_sample.env", "downloaded_repos/SilentDemonSD_WZML-X/docker-compose.yml", "downloaded_repos/SilentDemonSD_WZML-X/driveid.py", "downloaded_repos/SilentDemonSD_WZML-X/gen_sa_accounts.py", "downloaded_repos/SilentDemonSD_WZML-X/generate_drive_token.py", "downloaded_repos/SilentDemonSD_WZML-X/qBittorrent/config/qBittorrent.conf", "downloaded_repos/SilentDemonSD_WZML-X/requirements-cli.txt", "downloaded_repos/SilentDemonSD_WZML-X/requirements.txt", "downloaded_repos/SilentDemonSD_WZML-X/start.sh", "downloaded_repos/SilentDemonSD_WZML-X/update.py", "downloaded_repos/SilentDemonSD_WZML-X/web/__init__.py", "downloaded_repos/SilentDemonSD_WZML-X/web/nodes.py", "downloaded_repos/SilentDemonSD_WZML-X/web/wserver.py"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.****************, "profiling_times": {"config_time": 6.***************, "core_time": 8.***************, "ignores_time": 0.0018513202667236328, "total_time": 14.***************}, "parsing_time": {"total_time": 3.***************, "per_file_time": {"mean": 0.*****************, "std_dev": 0.0031511224112171572}, "very_slow_stats": {"time_ratio": 0.****************, "count_ratio": 0.009708737864077669}, "very_slow_files": [{"fpath": "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/bot_settings.py", "ftime": 0.*****************}]}, "scanning_time": {"total_time": 45.**************, "per_file_time": {"mean": 0.*****************, "std_dev": 0.****************}, "very_slow_stats": {"time_ratio": 0.****************, "count_ratio": 0.029940119760479042}, "very_slow_files": [{"fpath": "downloaded_repos/SilentDemonSD_WZML-X/gen_sa_accounts.py", "ftime": 1.****************}, {"fpath": "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/users_settings.py", "ftime": 1.****************}, {"fpath": "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/mirror_leech.py", "ftime": 2.****************}, {"fpath": "downloaded_repos/SilentDemonSD_WZML-X/bot/__main__.py", "ftime": 2.***************}, {"fpath": "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/anilist.py", "ftime": 2.****************}, {"fpath": "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/bot_settings.py", "ftime": 2.***************}, {"fpath": "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/upload_utils/gdriveTools.py", "ftime": 3.****************}, {"fpath": "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/listeners/tasks_listener.py", "ftime": 3.***************}, {"fpath": "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/ext_utils/bot_utils.py", "ftime": 3.***************}, {"fpath": "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/download_utils/direct_link_generator.py", "ftime": 4.927587032318115}]}, "matching_time": {"total_time": 24.96796226501465, "per_file_and_rule_time": {"mean": 0.0224532034757326, "std_dev": 0.005329642222119521}, "very_slow_stats": {"time_ratio": 0.5575063177963275, "count_ratio": 0.04946043165467626}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/listeners/tasks_listener.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.****************}, {"fpath": "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/ext_utils/bot_utils.py", "rule_id": "python.django.security.injection.command.subprocess-injection.subprocess-injection", "time": 0.****************}, {"fpath": "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/ext_utils/bot_utils.py", "rule_id": "python.lang.security.dangerous-subprocess-use.dangerous-subprocess-use", "time": 0.***************}, {"fpath": "downloaded_repos/SilentDemonSD_WZML-X/gen_sa_accounts.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.****************}, {"fpath": "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/listeners/tasks_listener.py", "rule_id": "python.lang.security.dangerous-system-call.dangerous-system-call", "time": 0.****************}, {"fpath": "downloaded_repos/SilentDemonSD_WZML-X/bot/__main__.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.****************}, {"fpath": "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/ext_utils/bot_utils.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.****************}, {"fpath": "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/download_utils/direct_link_generator.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.8246819972991943}, {"fpath": "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/mydramalist.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.8373498916625977}, {"fpath": "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/anilist.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 1.370028018951416}]}, "tainting_time": {"total_time": 10.867382764816284, "per_def_and_rule_time": {"mean": 0.0037603400570298557, "std_dev": 0.00020077529430114272}, "very_slow_stats": {"time_ratio": 0.3138651283743504, "count_ratio": 0.010034602076124567}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/users_settings.py", "fline": 878, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.12514400482177734}, {"fpath": "downloaded_repos/SilentDemonSD_WZML-X/bot/__init__.py", "fline": 1, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.155181884765625}, {"fpath": "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/bot_settings.py", "fline": 102, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.18199610710144043}, {"fpath": "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/users_settings.py", "fline": 151, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.20377802848815918}, {"fpath": "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/rss.py", "fline": 623, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.20520305633544922}, {"fpath": "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/rss.py", "fline": 69, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.20713019371032715}, {"fpath": "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/mirror_utils/upload_utils/gdriveTools.py", "fline": 745, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.21601605415344238}, {"fpath": "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/ytdlp.py", "fline": 275, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.21790695190429688}, {"fpath": "downloaded_repos/SilentDemonSD_WZML-X/bot/helper/listeners/tasks_listener.py", "fline": 243, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.22440004348754883}, {"fpath": "downloaded_repos/SilentDemonSD_WZML-X/bot/modules/mirror_leech.py", "fline": 78, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.22491216659545898}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}