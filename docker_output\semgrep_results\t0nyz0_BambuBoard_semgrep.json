{"version": "1.130.0", "results": [{"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/t0nyz0_BambuBoard/Dockerfile", "start": {"line": 31, "col": 1, "offset": 694}, "end": {"line": 31, "col": 37, "offset": 730}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD [ \"node\", \"bambuConnection.js\" ]", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-check-csurf-middleware-usage.express-check-csurf-middleware-usage", "path": "downloaded_repos/t0nyz0_BambuBoard/bambuConnection.js", "start": {"line": 25, "col": 7, "offset": 1122}, "end": {"line": 25, "col": 22, "offset": 1137}, "extra": {"message": "A CSRF middleware was not detected in your express application. Ensure you are either using one such as `csurf` or `csrf` (see rule references) and/or you are properly doing CSRF validation in your routes with a token or cookies.", "metadata": {"category": "security", "references": ["https://www.npmjs.com/package/csurf", "https://www.npmjs.com/package/csrf", "https://cheatsheetseries.owasp.org/cheatsheets/Cross-Site_Request_Forgery_Prevention_Cheat_Sheet.html"], "cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "technology": ["javascript", "typescript", "express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-check-csurf-middleware-usage.express-check-csurf-middleware-usage", "shortlink": "https://sg.run/BxzR"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "path": "downloaded_repos/t0nyz0_BambuBoard/bambuConnection.js", "start": {"line": 88, "col": 53, "offset": 2829}, "end": {"line": 88, "col": 76, "offset": 2852}, "extra": {"message": "Possible writing outside of the destination, make sure that the target path is nested in the intended destination", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["express", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "shortlink": "https://sg.run/weRn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/t0nyz0_BambuBoard/bambuConnection.js", "start": {"line": 88, "col": 53, "offset": 2829}, "end": {"line": 88, "col": 76, "offset": 2852}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/t0nyz0_BambuBoard/bambuConnection.js", "start": {"line": 95, "col": 23, "offset": 3026}, "end": {"line": 95, "col": 74, "offset": 3077}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "path": "downloaded_repos/t0nyz0_BambuBoard/bambuConnection.js", "start": {"line": 408, "col": 11, "offset": 11905}, "end": {"line": 413, "col": 5, "offset": 12059}, "extra": {"message": "Detected directly writing to a Response object from user-defined input. This bypasses any HTML escaping and may expose your application to a Cross-Site-scripting (XSS) vulnerability. Instead, use 'resp.render()' to render safely escaped HTML.", "metadata": {"interfile": true, "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Cross_Site_Scripting_Prevention_Cheat_Sheet.html"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "category": "security", "technology": ["express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "source": "https://semgrep.dev/r/javascript.express.security.audit.xss.direct-response-write.direct-response-write", "shortlink": "https://sg.run/vzGl"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/t0nyz0_BambuBoard/bambuConnection.js", "start": {"line": 419, "col": 19, "offset": 12189}, "end": {"line": 419, "col": 63, "offset": 12233}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/t0nyz0_BambuBoard/bambuConnection.js", "start": {"line": 440, "col": 21, "offset": 12956}, "end": {"line": 440, "col": 80, "offset": 13015}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/t0nyz0_BambuBoard/bambuConnection.js", "start": {"line": 451, "col": 19, "offset": 13296}, "end": {"line": 451, "col": 61, "offset": 13338}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "path": "downloaded_repos/t0nyz0_BambuBoard/bambuConnection.js", "start": {"line": 465, "col": 18, "offset": 13749}, "end": {"line": 465, "col": 23, "offset": 13754}, "extra": {"message": "Insecure WebSocket Detected. WebSocket Secure (wss) should be used for all WebSocket connections.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "13.5.1 Insecure WebSocket", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x21-V13-API.md#v135-websocket-security-requirements", "section": "V13: API and Web Service Verification Requirements", "version": "4"}, "category": "security", "technology": ["regex"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "shortlink": "https://sg.run/GWyz"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "problem-based-packs.insecure-transport.js-node.bypass-tls-verification.bypass-tls-verification", "path": "downloaded_repos/t0nyz0_BambuBoard/bambuConnection.js", "start": {"line": 511, "col": 57, "offset": 15420}, "end": {"line": 519, "col": 4, "offset": 15593}, "extra": {"message": "Checks for setting the environment variable NODE_TLS_REJECT_UNAUTHORIZED to 0, which disables TLS verification. This should only be used for debugging purposes. Setting the option rejectUnauthorized to false bypasses verification against the list of trusted CAs, which also leads to insecure transport. These options lead to vulnerability to MTM attacks, and should not be used.", "metadata": {"likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "category": "security", "cwe": "CWE-319: Cleartext Transmission of Sensitive Information", "owasp": "A03:2017 - Sensitive Data Exposure", "references": ["https://nodejs.org/api/https.html#https_https_request_options_callback", "https://stackoverflow.com/questions/20433287/node-js-request-cert-has-expired#answer-29397100"], "subcategory": ["vuln"], "technology": ["node.js"], "vulnerability": "Insecure Transport", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/problem-based-packs.insecure-transport.js-node.bypass-tls-verification.bypass-tls-verification", "shortlink": "https://sg.run/9oxr"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/t0nyz0_BambuBoard/bambuConnection.js", "start": {"line": 560, "col": 57, "offset": 17009}, "end": {"line": 560, "col": 81, "offset": 17033}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/t0nyz0_BambuBoard/bambuConnection.js", "start": {"line": 593, "col": 46, "offset": 18392}, "end": {"line": 593, "col": 71, "offset": 18417}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/t0nyz0_BambuBoard/docker-compose.yml", "start": {"line": 3, "col": 3, "offset": 25}, "end": {"line": 3, "col": 13, "offset": 35}, "extra": {"message": "Service 'bambuboard' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/t0nyz0_BambuBoard/docker-compose.yml", "start": {"line": 3, "col": 3, "offset": 25}, "end": {"line": 3, "col": 13, "offset": 35}, "extra": {"message": "Service 'bambuboard' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/t0nyz0_BambuBoard/public/script.js", "start": {"line": 132, "col": 23, "offset": 4206}, "end": {"line": 132, "col": 68, "offset": 4251}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/t0nyz0_BambuBoard/public/script.js", "start": {"line": 1628, "col": 5, "offset": 62719}, "end": {"line": 1628, "col": 36, "offset": 62750}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-check-csurf-middleware-usage.express-check-csurf-middleware-usage", "path": "downloaded_repos/t0nyz0_BambuBoard/videoStreamer.js", "start": {"line": 13, "col": 7, "offset": 298}, "end": {"line": 13, "col": 22, "offset": 313}, "extra": {"message": "A CSRF middleware was not detected in your express application. Ensure you are either using one such as `csurf` or `csrf` (see rule references) and/or you are properly doing CSRF validation in your routes with a token or cookies.", "metadata": {"category": "security", "references": ["https://www.npmjs.com/package/csurf", "https://www.npmjs.com/package/csrf", "https://cheatsheetseries.owasp.org/cheatsheets/Cross-Site_Request_Forgery_Prevention_Cheat_Sheet.html"], "cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "technology": ["javascript", "typescript", "express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-check-csurf-middleware-usage.express-check-csurf-middleware-usage", "shortlink": "https://sg.run/BxzR"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "path": "downloaded_repos/t0nyz0_BambuBoard/videoStreamer.js", "start": {"line": 48, "col": 61, "offset": 1587}, "end": {"line": 48, "col": 70, "offset": 1596}, "extra": {"message": "Possible writing outside of the destination, make sure that the target path is nested in the intended destination", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["express", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "shortlink": "https://sg.run/weRn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/t0nyz0_BambuBoard/videoStreamer.js", "start": {"line": 48, "col": 61, "offset": 1587}, "end": {"line": 48, "col": 70, "offset": 1596}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "path": "downloaded_repos/t0nyz0_BambuBoard/videoStreamer.js", "start": {"line": 68, "col": 36, "offset": 2176}, "end": {"line": 68, "col": 45, "offset": 2185}, "extra": {"message": "Possible writing outside of the destination, make sure that the target path is nested in the intended destination", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["express", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "shortlink": "https://sg.run/weRn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/t0nyz0_BambuBoard/videoStreamer.js", "start": {"line": 68, "col": 36, "offset": 2176}, "end": {"line": 68, "col": 45, "offset": 2185}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "path": "downloaded_repos/t0nyz0_BambuBoard/videoStreamer.js", "start": {"line": 82, "col": 36, "offset": 2638}, "end": {"line": 82, "col": 45, "offset": 2647}, "extra": {"message": "Possible writing outside of the destination, make sure that the target path is nested in the intended destination", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["express", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "shortlink": "https://sg.run/weRn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/t0nyz0_BambuBoard/videoStreamer.js", "start": {"line": 82, "col": 36, "offset": 2638}, "end": {"line": 82, "col": 45, "offset": 2647}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "path": "downloaded_repos/t0nyz0_BambuBoard/videoStreamer.js", "start": {"line": 98, "col": 38, "offset": 3173}, "end": {"line": 98, "col": 47, "offset": 3182}, "extra": {"message": "Possible writing outside of the destination, make sure that the target path is nested in the intended destination", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["express", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "shortlink": "https://sg.run/weRn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/t0nyz0_BambuBoard/videoStreamer.js", "start": {"line": 98, "col": 38, "offset": 3173}, "end": {"line": 98, "col": 47, "offset": 3182}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "path": "downloaded_repos/t0nyz0_BambuBoard/videoStreamer.js", "start": {"line": 114, "col": 27, "offset": 3790}, "end": {"line": 114, "col": 33, "offset": 3796}, "extra": {"message": "Possible writing outside of the destination, make sure that the target path is nested in the intended destination", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["express", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "shortlink": "https://sg.run/weRn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/t0nyz0_BambuBoard/videoStreamer.js", "start": {"line": 114, "col": 27, "offset": 3790}, "end": {"line": 114, "col": 33, "offset": 3796}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/t0nyz0_BambuBoard/videoStreamer.js", "start": {"line": 122, "col": 17, "offset": 3980}, "end": {"line": 122, "col": 69, "offset": 4032}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/t0nyz0_BambuBoard/videoStreamer.js", "start": {"line": 141, "col": 21, "offset": 4540}, "end": {"line": 141, "col": 51, "offset": 4570}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/t0nyz0_BambuBoard/videoStreamer.js", "start": {"line": 145, "col": 21, "offset": 4672}, "end": {"line": 145, "col": 51, "offset": 4702}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/t0nyz0_BambuBoard/videoStreamer.js", "start": {"line": 154, "col": 23, "offset": 4986}, "end": {"line": 154, "col": 62, "offset": 5025}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/t0nyz0_BambuBoard/.github/workflows/docker-publish.yml", "start": {"line": 17, "col": 73, "offset": 244}, "end": {"line": 17, "col": 76, "offset": 247}}]], "message": "Syntax error at line downloaded_repos/t0nyz0_BambuBoard/.github/workflows/docker-publish.yml:17:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/t0nyz0_BambuBoard/.github/workflows/docker-publish.yml", "spans": [{"file": "downloaded_repos/t0nyz0_BambuBoard/.github/workflows/docker-publish.yml", "start": {"line": 17, "col": 73, "offset": 244}, "end": {"line": 17, "col": 76, "offset": 247}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/t0nyz0_BambuBoard/.github/workflows/docker-publish.yml", "start": {"line": 20, "col": 38, "offset": 385}, "end": {"line": 20, "col": 41, "offset": 388}}]], "message": "Syntax error at line downloaded_repos/t0nyz0_BambuBoard/.github/workflows/docker-publish.yml:20:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/t0nyz0_BambuBoard/.github/workflows/docker-publish.yml", "spans": [{"file": "downloaded_repos/t0nyz0_BambuBoard/.github/workflows/docker-publish.yml", "start": {"line": 20, "col": 38, "offset": 385}, "end": {"line": 20, "col": 41, "offset": 388}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/t0nyz0_BambuBoard/.github/workflows/docker-publish.yml", "start": {"line": 23, "col": 32, "offset": 510}, "end": {"line": 23, "col": 35, "offset": 513}}]], "message": "Syntax error at line downloaded_repos/t0nyz0_BambuBoard/.github/workflows/docker-publish.yml:23:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/t0nyz0_BambuBoard/.github/workflows/docker-publish.yml", "spans": [{"file": "downloaded_repos/t0nyz0_BambuBoard/.github/workflows/docker-publish.yml", "start": {"line": 23, "col": 32, "offset": 510}, "end": {"line": 23, "col": 35, "offset": 513}}]}], "paths": {"scanned": ["downloaded_repos/t0nyz0_BambuBoard/.github/ISSUE_TEMPLATE/bug_report.md", "downloaded_repos/t0nyz0_BambuBoard/.github/ISSUE_TEMPLATE/feature_request.md", "downloaded_repos/t0nyz0_BambuBoard/.github/workflows/docker-publish.yml", "downloaded_repos/t0nyz0_BambuBoard/.gitignore", "downloaded_repos/t0nyz0_BambuBoard/.vscode/settings.json", "downloaded_repos/t0nyz0_BambuBoard/Dockerfile", "downloaded_repos/t0nyz0_BambuBoard/LICENSE", "downloaded_repos/t0nyz0_BambuBoard/MULTI_PRINTER_SETUP.md", "downloaded_repos/t0nyz0_BambuBoard/OBS_settings/Archived/BambuBoard_1.0.6.json", "downloaded_repos/t0nyz0_BambuBoard/OBS_settings/BambuBoard_1.1.9.json", "downloaded_repos/t0nyz0_BambuBoard/OFFLINE_SETUP.md", "downloaded_repos/t0nyz0_BambuBoard/README.md", "downloaded_repos/t0nyz0_BambuBoard/VIDEO_STREAMING_SETUP.md", "downloaded_repos/t0nyz0_BambuBoard/accessToken.json", "downloaded_repos/t0nyz0_BambuBoard/bambuConnection.js", "downloaded_repos/t0nyz0_BambuBoard/bambuboard.service", "downloaded_repos/t0nyz0_BambuBoard/config.json", "downloaded_repos/t0nyz0_BambuBoard/deploy.sh", "downloaded_repos/t0nyz0_BambuBoard/docker-compose.yml", "downloaded_repos/t0nyz0_BambuBoard/example.config.json", "downloaded_repos/t0nyz0_BambuBoard/note.json", "downloaded_repos/t0nyz0_BambuBoard/package.json", "downloaded_repos/t0nyz0_BambuBoard/pnpm-lock.yaml", "downloaded_repos/t0nyz0_BambuBoard/public/assets/css/bootstrap.min.css", "downloaded_repos/t0nyz0_BambuBoard/public/assets/css/google-fonts.css", "downloaded_repos/t0nyz0_BambuBoard/public/assets/css/material-symbols.css", "downloaded_repos/t0nyz0_BambuBoard/public/assets/fonts/ibm-plex-mono-100.ttf", "downloaded_repos/t0nyz0_BambuBoard/public/assets/fonts/ibm-plex-mono-400.ttf", "downloaded_repos/t0nyz0_BambuBoard/public/assets/fonts/material-symbols-outlined.ttf", "downloaded_repos/t0nyz0_BambuBoard/public/assets/fonts/open-sans-400.ttf", "downloaded_repos/t0nyz0_BambuBoard/public/assets/fonts/open-sans-600.ttf", "downloaded_repos/t0nyz0_BambuBoard/public/assets/fonts/oxygen-400.ttf", "downloaded_repos/t0nyz0_BambuBoard/public/assets/fonts/red-hat-display-300.ttf", "downloaded_repos/t0nyz0_BambuBoard/public/assets/fonts/red-hat-display-400.ttf", "downloaded_repos/t0nyz0_BambuBoard/public/data_mercury.json", "downloaded_repos/t0nyz0_BambuBoard/public/data_pluto.json", "downloaded_repos/t0nyz0_BambuBoard/public/data_printer1.json", "downloaded_repos/t0nyz0_BambuBoard/public/data_printer2.json", "downloaded_repos/t0nyz0_BambuBoard/public/data_saturn.json", "downloaded_repos/t0nyz0_BambuBoard/public/index.html", "downloaded_repos/t0nyz0_BambuBoard/public/login.html", "downloaded_repos/t0nyz0_BambuBoard/public/plate.png", "downloaded_repos/t0nyz0_BambuBoard/public/printer-grid.html", "downloaded_repos/t0nyz0_BambuBoard/public/script.js", "downloaded_repos/t0nyz0_BambuBoard/public/styles.css", "downloaded_repos/t0nyz0_BambuBoard/public/widgets/ams/AMS_script.js", "downloaded_repos/t0nyz0_BambuBoard/public/widgets/ams/index.html", "downloaded_repos/t0nyz0_BambuBoard/public/widgets/ams-temp/AMS_temp_script.js", "downloaded_repos/t0nyz0_BambuBoard/public/widgets/ams-temp/index.html", "downloaded_repos/t0nyz0_BambuBoard/public/widgets/bed-temp/BED_script.js", "downloaded_repos/t0nyz0_BambuBoard/public/widgets/bed-temp/index.html", "downloaded_repos/t0nyz0_BambuBoard/public/widgets/chamber-temp/CHAMBER_script.js", "downloaded_repos/t0nyz0_BambuBoard/public/widgets/chamber-temp/index.html", "downloaded_repos/t0nyz0_BambuBoard/public/widgets/fans/FANS_script.js", "downloaded_repos/t0nyz0_BambuBoard/public/widgets/fans/index.html", "downloaded_repos/t0nyz0_BambuBoard/public/widgets/model-image/index.html", "downloaded_repos/t0nyz0_BambuBoard/public/widgets/model-image/model-script.js", "downloaded_repos/t0nyz0_BambuBoard/public/widgets/notes/edit.html", "downloaded_repos/t0nyz0_BambuBoard/public/widgets/notes/index.html", "downloaded_repos/t0nyz0_BambuBoard/public/widgets/nozzle-info/NOZZLE_info_script.js", "downloaded_repos/t0nyz0_BambuBoard/public/widgets/nozzle-info/index.html", "downloaded_repos/t0nyz0_BambuBoard/public/widgets/nozzle-temp/NOZZLE_script.js", "downloaded_repos/t0nyz0_BambuBoard/public/widgets/nozzle-temp/index.html", "downloaded_repos/t0nyz0_BambuBoard/public/widgets/print-info/INFO_script.js", "downloaded_repos/t0nyz0_BambuBoard/public/widgets/print-info/index.html", "downloaded_repos/t0nyz0_BambuBoard/public/widgets/printer-info/PRINTER_info_script.js", "downloaded_repos/t0nyz0_BambuBoard/public/widgets/printer-info/index.html", "downloaded_repos/t0nyz0_BambuBoard/public/widgets/profile-info/PROFILE_info_script.js", "downloaded_repos/t0nyz0_BambuBoard/public/widgets/profile-info/index.html", "downloaded_repos/t0nyz0_BambuBoard/public/widgets/progress-info/PROGRESS_script.js", "downloaded_repos/t0nyz0_BambuBoard/public/widgets/progress-info/index.html", "downloaded_repos/t0nyz0_BambuBoard/public/widgets/styles.css", "downloaded_repos/t0nyz0_BambuBoard/public/widgets/version/VERSION_script.js", "downloaded_repos/t0nyz0_BambuBoard/public/widgets/version/index.html", "downloaded_repos/t0nyz0_BambuBoard/public/widgets/wifi/WIFI_script.js", "downloaded_repos/t0nyz0_BambuBoard/public/widgets/wifi/index.html", "downloaded_repos/t0nyz0_BambuBoard/videoStreamer.js"], "skipped": [{"path": "downloaded_repos/t0nyz0_BambuBoard/.github/workflows/docker-publish.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/t0nyz0_BambuBoard/public/assets/js/bootstrap.bundle.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/t0nyz0_BambuBoard/public/assets/js/jquery-3.6.0.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/t0nyz0_BambuBoard/public/assets/js/jsmpeg.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/t0nyz0_BambuBoard/video_streams/mercury/stream.mjpeg", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/t0nyz0_BambuBoard/video_streams/saturn/stream.mjpeg", "reason": "exceeded_size_limit"}]}, "time": {"rules": [], "rules_parse_time": 1.7350471019744873, "profiling_times": {"config_time": 5.998218297958374, "core_time": 3.9823575019836426, "ignores_time": 0.10939192771911621, "total_time": 10.090734481811523}, "parsing_time": {"total_time": 1.8064908981323242, "per_file_time": {"mean": 0.033453535150598596, "std_dev": 0.003705642902994722}, "very_slow_stats": {"time_ratio": 0.20441952273282785, "count_ratio": 0.018518518518518517}, "very_slow_files": [{"fpath": "downloaded_repos/t0nyz0_BambuBoard/public/script.js", "ftime": 0.3692820072174072}]}, "scanning_time": {"total_time": 7.477878093719482, "per_file_time": {"mean": 0.03595133698903597, "std_dev": 0.018396834559025237}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 2.7267119884490967, "per_file_and_rule_time": {"mean": 0.008547686484166449, "std_dev": 0.00025775255117867457}, "very_slow_stats": {"time_ratio": 0.13287578129242114, "count_ratio": 0.009404388714733543}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/t0nyz0_BambuBoard/public/script.js", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 0.1000981330871582}, {"fpath": "downloaded_repos/t0nyz0_BambuBoard/public/script.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.10246801376342773}, {"fpath": "downloaded_repos/t0nyz0_BambuBoard/bambuConnection.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.15974783897399902}]}, "tainting_time": {"total_time": 1.1645092964172363, "per_def_and_rule_time": {"mean": 0.0015843663896833147, "std_dev": 2.0881698044392585e-05}, "very_slow_stats": {"time_ratio": 0.06205016639006647, "count_ratio": 0.0013605442176870747}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/t0nyz0_BambuBoard/public/script.js", "fline": 801, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.07225799560546875}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}