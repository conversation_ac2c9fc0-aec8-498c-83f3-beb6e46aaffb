{"version": "1.130.0", "results": [], "errors": [], "paths": {"scanned": ["downloaded_repos/s-cart_s-cart/.editorconfig", "downloaded_repos/s-cart_s-cart/.env.example", "downloaded_repos/s-cart_s-cart/.gitattributes", "downloaded_repos/s-cart_s-cart/.github/FUNDING.yml", "downloaded_repos/s-cart_s-cart/.gitignore", "downloaded_repos/s-cart_s-cart/app/Console/Commands/Information.php", "downloaded_repos/s-cart_s-cart/app/Console/Commands/Install.php", "downloaded_repos/s-cart_s-cart/app/Console/Commands/Sample.php", "downloaded_repos/s-cart_s-cart/app/Console/Commands/Update.php", "downloaded_repos/s-cart_s-cart/app/GP247/.gitignore", "downloaded_repos/s-cart_s-cart/app/GP247/Core/Api/Controllers/AdminAuthController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Core/Api/Controllers/AdminController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Core/Controllers/AdminApiConnectionController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Core/Controllers/AdminCacheConfigController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Core/Controllers/AdminConfigGlobalController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Core/Controllers/AdminCustomFieldController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Core/Controllers/AdminHomeLayoutController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Core/Controllers/AdminLanguageController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Core/Controllers/AdminLanguageManagerController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Core/Controllers/AdminLogController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Core/Controllers/AdminMenuController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Core/Controllers/AdminNoticeController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Core/Controllers/AdminPasswordPolicyController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Core/Controllers/AdminPluginsController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Core/Controllers/AdminPluginsOnlineController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Core/Controllers/AdminServerInfoController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Core/Controllers/AdminStoreConfigController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Core/Controllers/AdminStoreInfoController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Core/Controllers/AdminStoreMaintainController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Core/Controllers/Auth/ForgotPasswordController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Core/Controllers/Auth/LoginController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Core/Controllers/Auth/PermissionController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Core/Controllers/Auth/ResetPasswordController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Core/Controllers/Auth/RoleController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Core/Controllers/Auth/UsersController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Core/Controllers/HomeController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Front/Admin/Controllers/AdminBannerController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Front/Admin/Controllers/AdminBannerTypeController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Front/Admin/Controllers/AdminLayoutBlockController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Front/Admin/Controllers/AdminLinkController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Front/Admin/Controllers/AdminLinkGroupController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Front/Admin/Controllers/AdminPageController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Front/Admin/Controllers/AdminTemplateController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Front/Admin/Controllers/AdminTemplateOnlineController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Front/Controllers/HomeController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Shop/Controllers/Auth/ForgotPasswordController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Shop/Controllers/Auth/LoginController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Shop/Controllers/Auth/RegisterController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Shop/Controllers/Auth/ResetPasswordController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Shop/Controllers/ShopAccountController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Shop/Controllers/ShopBrandController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Shop/Controllers/ShopCartController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Shop/Controllers/ShopCategoryController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Shop/Controllers/ShopProductController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Shop/Controllers/ShopStoreController.php", "downloaded_repos/s-cart_s-cart/app/GP247/Templates/Default/AppConfig.php", "downloaded_repos/s-cart_s-cart/app/GP247/Templates/Default/Lang/en/lang.php", "downloaded_repos/s-cart_s-cart/app/GP247/Templates/Default/Lang/vi/lang.php", "downloaded_repos/s-cart_s-cart/app/GP247/Templates/Default/Provider.php", "downloaded_repos/s-cart_s-cart/app/GP247/Templates/Default/Route.php", "downloaded_repos/s-cart_s-cart/app/GP247/Templates/Default/blocks/banner_image.blade.php", "downloaded_repos/s-cart_s-cart/app/GP247/Templates/Default/common/breadcrumb.blade.php", "downloaded_repos/s-cart_s-cart/app/GP247/Templates/Default/common/css.blade.php", "downloaded_repos/s-cart_s-cart/app/GP247/Templates/Default/common/item_single.blade.php", "downloaded_repos/s-cart_s-cart/app/GP247/Templates/Default/common/item_single_long.blade.php", "downloaded_repos/s-cart_s-cart/app/GP247/Templates/Default/common/js.blade.php", "downloaded_repos/s-cart_s-cart/app/GP247/Templates/Default/common/notice.blade.php", "downloaded_repos/s-cart_s-cart/app/GP247/Templates/Default/common/pagination.blade.php", "downloaded_repos/s-cart_s-cart/app/GP247/Templates/Default/common/pagination_result.blade.php", "downloaded_repos/s-cart_s-cart/app/GP247/Templates/Default/common/render_form_custom_field.blade.php", "downloaded_repos/s-cart_s-cart/app/GP247/Templates/Default/config.php", "downloaded_repos/s-cart_s-cart/app/GP247/Templates/Default/function.php", "downloaded_repos/s-cart_s-cart/app/GP247/Templates/Default/gp247.json", "downloaded_repos/s-cart_s-cart/app/GP247/Templates/Default/layout/block_bottom.blade.php", "downloaded_repos/s-cart_s-cart/app/GP247/Templates/Default/layout/block_footer.blade.php", "downloaded_repos/s-cart_s-cart/app/GP247/Templates/Default/layout/block_menu.blade.php", "downloaded_repos/s-cart_s-cart/app/GP247/Templates/Default/layout/main.blade.php", "downloaded_repos/s-cart_s-cart/app/GP247/Templates/Default/layout/maintenance.blade.php", "downloaded_repos/s-cart_s-cart/app/GP247/Templates/Default/layout/maintenance_note.blade.php", "downloaded_repos/s-cart_s-cart/app/GP247/Templates/Default/layout.blade.php", "downloaded_repos/s-cart_s-cart/app/GP247/Templates/Default/readme.md", "downloaded_repos/s-cart_s-cart/app/GP247/Templates/Default/screen/404.blade.php", "downloaded_repos/s-cart_s-cart/app/GP247/Templates/Default/screen/front_search.blade.php", "downloaded_repos/s-cart_s-cart/app/GP247/Templates/Default/screen/home.blade.php", "downloaded_repos/s-cart_s-cart/app/GP247/Templates/Default/screen/notfound.blade.php", "downloaded_repos/s-cart_s-cart/app/GP247/Templates/Default/screen/page_detail.blade.php", "downloaded_repos/s-cart_s-cart/app/GP247/Templates/Default/screen/page_list.blade.php", "downloaded_repos/s-cart_s-cart/app/Http/Controllers/Controller.php", "downloaded_repos/s-cart_s-cart/app/Models/User.php", "downloaded_repos/s-cart_s-cart/app/Providers/AppServiceProvider.php", "downloaded_repos/s-cart_s-cart/artisan", "downloaded_repos/s-cart_s-cart/bootstrap/app.php", "downloaded_repos/s-cart_s-cart/bootstrap/cache/.gitignore", "downloaded_repos/s-cart_s-cart/bootstrap/providers.php", "downloaded_repos/s-cart_s-cart/composer.json", "downloaded_repos/s-cart_s-cart/config/app.php", "downloaded_repos/s-cart_s-cart/config/auth.php", "downloaded_repos/s-cart_s-cart/config/cache.php", "downloaded_repos/s-cart_s-cart/config/database.php", "downloaded_repos/s-cart_s-cart/config/filesystems.php", "downloaded_repos/s-cart_s-cart/config/gp247_functions_except.php", "downloaded_repos/s-cart_s-cart/config/logging.php", "downloaded_repos/s-cart_s-cart/config/mail.php", "downloaded_repos/s-cart_s-cart/config/queue.php", "downloaded_repos/s-cart_s-cart/config/services.php", "downloaded_repos/s-cart_s-cart/config/session.php", "downloaded_repos/s-cart_s-cart/database/.gitignore", "downloaded_repos/s-cart_s-cart/database/factories/UserFactory.php", "downloaded_repos/s-cart_s-cart/database/migrations/0001_01_01_000000_create_users_table.php", "downloaded_repos/s-cart_s-cart/database/migrations/0001_01_01_000001_create_cache_table.php", "downloaded_repos/s-cart_s-cart/database/migrations/0001_01_01_000002_create_jobs_table.php", "downloaded_repos/s-cart_s-cart/database/seeders/DatabaseSeeder.php", "downloaded_repos/s-cart_s-cart/package.json", "downloaded_repos/s-cart_s-cart/phpunit.xml", "downloaded_repos/s-cart_s-cart/postcss.config.js", "downloaded_repos/s-cart_s-cart/public/.htaccess", "downloaded_repos/s-cart_s-cart/public/GP247/Core/avatar/admin.png", "downloaded_repos/s-cart_s-cart/public/GP247/Core/avatar/user.png", "downloaded_repos/s-cart_s-cart/public/GP247/Core/images/bg-system.jpg", "downloaded_repos/s-cart_s-cart/public/GP247/Core/images/loading.gif", "downloaded_repos/s-cart_s-cart/public/GP247/Core/images/maintenance.jpg", "downloaded_repos/s-cart_s-cart/public/GP247/Core/images/no-image.jpg", "downloaded_repos/s-cart_s-cart/public/GP247/Core/images/org.jpg", "downloaded_repos/s-cart_s-cart/public/GP247/Core/img/loading.gif", "downloaded_repos/s-cart_s-cart/public/GP247/Core/language/flag_uk.png", "downloaded_repos/s-cart_s-cart/public/GP247/Core/language/flag_vn.png", "downloaded_repos/s-cart_s-cart/public/GP247/Core/logo/icon.png", "downloaded_repos/s-cart_s-cart/public/GP247/Core/logo/logo.png", "downloaded_repos/s-cart_s-cart/public/favicon.ico", "downloaded_repos/s-cart_s-cart/public/index.php", "downloaded_repos/s-cart_s-cart/public/robots.txt", "downloaded_repos/s-cart_s-cart/readme.md", "downloaded_repos/s-cart_s-cart/readme_vi.md", "downloaded_repos/s-cart_s-cart/resources/css/app.css", "downloaded_repos/s-cart_s-cart/resources/js/app.js", "downloaded_repos/s-cart_s-cart/resources/js/bootstrap.js", "downloaded_repos/s-cart_s-cart/resources/views/welcome.blade.php", "downloaded_repos/s-cart_s-cart/routes/console.php", "downloaded_repos/s-cart_s-cart/routes/web.php", "downloaded_repos/s-cart_s-cart/storage/app/.gitignore", "downloaded_repos/s-cart_s-cart/storage/app/private/.gitignore", "downloaded_repos/s-cart_s-cart/storage/app/public/.gitignore", "downloaded_repos/s-cart_s-cart/storage/framework/.gitignore", "downloaded_repos/s-cart_s-cart/storage/framework/cache/.gitignore", "downloaded_repos/s-cart_s-cart/storage/framework/cache/data/.gitignore", "downloaded_repos/s-cart_s-cart/storage/framework/sessions/.gitignore", "downloaded_repos/s-cart_s-cart/storage/framework/testing/.gitignore", "downloaded_repos/s-cart_s-cart/storage/framework/views/.gitignore", "downloaded_repos/s-cart_s-cart/storage/logs/.gitignore", "downloaded_repos/s-cart_s-cart/tailwind.config.js", "downloaded_repos/s-cart_s-cart/vite.config.js"], "skipped": [{"path": "downloaded_repos/s-cart_s-cart/tests/Feature/ExampleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/s-cart_s-cart/tests/TestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/s-cart_s-cart/tests/Unit/ExampleTest.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.868427038192749, "profiling_times": {"config_time": 6.680334568023682, "core_time": 3.2460849285125732, "ignores_time": 0.003551483154296875, "total_time": 9.930813789367676}, "parsing_time": {"total_time": 0.5440161228179932, "per_file_time": {"mean": 0.004814301971840649, "std_dev": 9.508501458616129e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.756746530532837, "per_file_time": {"mean": 0.004233124169958641, "std_dev": 0.00010813915523831721}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.19560718536376953, "per_file_and_rule_time": {"mean": 0.001137251077696334, "std_dev": 1.0318985638981643e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.007895946502685547, "per_def_and_rule_time": {"mean": 0.00032899777094523114, "std_dev": 1.0287928810094449e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}