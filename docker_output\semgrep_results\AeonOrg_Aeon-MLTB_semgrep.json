{"version": "1.130.0", "results": [{"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/AeonOrg_Aeon-MLTB/Dockerfile", "start": {"line": 11, "col": 1, "offset": 174}, "end": {"line": 11, "col": 25, "offset": 198}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD [\"bash\", \"start.sh\"]", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.eval-detected.eval-detected", "path": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/ext_utils/bot_utils.py", "start": {"line": 215, "col": 58, "offset": 6533}, "end": {"line": 215, "col": 69, "offset": 6544}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/blacklists/blacklist_calls.html#b307-eval", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.eval-detected.eval-detected", "shortlink": "https://sg.run/ZvrD"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.eval-detected.eval-detected", "path": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/ext_utils/media_utils.py", "start": {"line": 53, "col": 18, "offset": 1510}, "end": {"line": 53, "col": 33, "offset": 1525}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/blacklists/blacklist_calls.html#b307-eval", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.eval-detected.eval-detected", "shortlink": "https://sg.run/ZvrD"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.eval-detected.eval-detected", "path": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/ext_utils/media_utils.py", "start": {"line": 105, "col": 18, "offset": 3313}, "end": {"line": 105, "col": 33, "offset": 3328}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/blacklists/blacklist_calls.html#b307-eval", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.eval-detected.eval-detected", "shortlink": "https://sg.run/ZvrD"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.eval-detected.eval-detected", "path": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/gdrive_utils/clone.py", "start": {"line": 153, "col": 21, "offset": 5856}, "end": {"line": 153, "col": 38, "offset": 5873}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/blacklists/blacklist_calls.html#b307-eval", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.eval-detected.eval-detected", "shortlink": "https://sg.run/ZvrD"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.eval-detected.eval-detected", "path": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/gdrive_utils/download.py", "start": {"line": 143, "col": 25, "offset": 5479}, "end": {"line": 143, "col": 42, "offset": 5496}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/blacklists/blacklist_calls.html#b307-eval", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.eval-detected.eval-detected", "shortlink": "https://sg.run/ZvrD"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.deserialization.pickle.avoid-pickle", "path": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/gdrive_utils/helper.py", "start": {"line": 94, "col": 31, "offset": 3057}, "end": {"line": 94, "col": 39, "offset": 3065}, "extra": {"message": "Avoid using `pickle`, which is known to lead to code execution vulnerabilities. When unpickling, the serialized data could be manipulated to run arbitrary code. Instead, consider serializing the relevant data as JSON or a similar text-based serialization format.", "metadata": {"owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "references": ["https://docs.python.org/3/library/pickle.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/python.lang.security.deserialization.pickle.avoid-pickle", "shortlink": "https://sg.run/OPwB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.eval-detected.eval-detected", "path": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/gdrive_utils/upload.py", "start": {"line": 212, "col": 25, "offset": 7427}, "end": {"line": 212, "col": 42, "offset": 7444}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/blacklists/blacklist_calls.html#b307-eval", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.eval-detected.eval-detected", "shortlink": "https://sg.run/ZvrD"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.deserialization.pickle.avoid-pickle", "path": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/youtube_utils/youtube_helper.py", "start": {"line": 75, "col": 31, "offset": 2238}, "end": {"line": 75, "col": 39, "offset": 2246}, "extra": {"message": "Avoid using `pickle`, which is known to lead to code execution vulnerabilities. When unpickling, the serialized data could be manipulated to run arbitrary code. Instead, consider serializing the relevant data as JSON or a similar text-based serialization format.", "metadata": {"owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "references": ["https://docs.python.org/3/library/pickle.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/python.lang.security.deserialization.pickle.avoid-pickle", "shortlink": "https://sg.run/OPwB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.eval-detected.eval-detected", "path": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/bot_settings.py", "start": {"line": 248, "col": 17, "offset": 10107}, "end": {"line": 248, "col": 28, "offset": 10118}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/blacklists/blacklist_calls.html#b307-eval", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.eval-detected.eval-detected", "shortlink": "https://sg.run/ZvrD"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.eval-detected.eval-detected", "path": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/bot_settings.py", "start": {"line": 279, "col": 30, "offset": 11066}, "end": {"line": 279, "col": 41, "offset": 11077}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/blacklists/blacklist_calls.html#b307-eval", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.eval-detected.eval-detected", "shortlink": "https://sg.run/ZvrD"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.eval-detected.eval-detected", "path": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/bot_settings.py", "start": {"line": 298, "col": 25, "offset": 11708}, "end": {"line": 298, "col": 36, "offset": 11719}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/blacklists/blacklist_calls.html#b307-eval", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.eval-detected.eval-detected", "shortlink": "https://sg.run/ZvrD"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.exec-detected.exec-detected", "path": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/exec.py", "start": {"line": 77, "col": 13, "offset": 2062}, "end": {"line": 77, "col": 60, "offset": 2109}, "extra": {"message": "Detected the use of exec(). exec() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b102_exec_used.html", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.exec-detected.exec-detected", "shortlink": "https://sg.run/ndRX"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.exec-detected.exec-detected", "path": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/exec.py", "start": {"line": 79, "col": 13, "offset": 2136}, "end": {"line": 79, "col": 66, "offset": 2189}, "extra": {"message": "Detected the use of exec(). exec() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b102_exec_used.html", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.exec-detected.exec-detected", "shortlink": "https://sg.run/ndRX"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.eval-detected.eval-detected", "path": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/gd_search.py", "start": {"line": 74, "col": 33, "offset": 2470}, "end": {"line": 74, "col": 46, "offset": 2483}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/blacklists/blacklist_calls.html#b307-eval", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.eval-detected.eval-detected", "shortlink": "https://sg.run/ZvrD"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.eval-detected.eval-detected", "path": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/gd_search.py", "start": {"line": 75, "col": 61, "offset": 2545}, "end": {"line": 75, "col": 74, "offset": 2558}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/blacklists/blacklist_calls.html#b307-eval", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.eval-detected.eval-detected", "shortlink": "https://sg.run/ZvrD"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.eval-detected.eval-detected", "path": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/gd_search.py", "start": {"line": 79, "col": 31, "offset": 2719}, "end": {"line": 79, "col": 44, "offset": 2732}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/blacklists/blacklist_calls.html#b307-eval", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.eval-detected.eval-detected", "shortlink": "https://sg.run/ZvrD"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.eval-detected.eval-detected", "path": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/gd_search.py", "start": {"line": 80, "col": 47, "offset": 2780}, "end": {"line": 80, "col": 60, "offset": 2793}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/blacklists/blacklist_calls.html#b307-eval", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.eval-detected.eval-detected", "shortlink": "https://sg.run/ZvrD"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.eval-detected.eval-detected", "path": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/gd_search.py", "start": {"line": 87, "col": 20, "offset": 3078}, "end": {"line": 87, "col": 33, "offset": 3091}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/blacklists/blacklist_calls.html#b307-eval", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.eval-detected.eval-detected", "shortlink": "https://sg.run/ZvrD"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.eval-detected.eval-detected", "path": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/gd_search.py", "start": {"line": 88, "col": 18, "offset": 3109}, "end": {"line": 88, "col": 31, "offset": 3122}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/blacklists/blacklist_calls.html#b307-eval", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.eval-detected.eval-detected", "shortlink": "https://sg.run/ZvrD"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.eval-detected.eval-detected", "path": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/users_settings.py", "start": {"line": 433, "col": 21, "offset": 15901}, "end": {"line": 433, "col": 32, "offset": 15912}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/blacklists/blacklist_calls.html#b307-eval", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.eval-detected.eval-detected", "shortlink": "https://sg.run/ZvrD"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.eval-detected.eval-detected", "path": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/users_settings.py", "start": {"line": 479, "col": 25, "offset": 17393}, "end": {"line": 479, "col": 36, "offset": 17404}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/blacklists/blacklist_calls.html#b307-eval", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.eval-detected.eval-detected", "shortlink": "https://sg.run/ZvrD"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.eval-detected.eval-detected", "path": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/ytdlp.py", "start": {"line": 381, "col": 19, "offset": 14088}, "end": {"line": 381, "col": 37, "offset": 14106}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/blacklists/blacklist_calls.html#b307-eval", "cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dyanmic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.eval-detected.eval-detected", "shortlink": "https://sg.run/ZvrD"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.deserialization.pickle.avoid-pickle", "path": "downloaded_repos/AeonOrg_Aeon-MLTB/dev/token.py", "start": {"line": 17, "col": 23, "offset": 532}, "end": {"line": 17, "col": 37, "offset": 546}, "extra": {"message": "Avoid using `pickle`, which is known to lead to code execution vulnerabilities. When unpickling, the serialized data could be manipulated to run arbitrary code. Instead, consider serializing the relevant data as JSON or a similar text-based serialization format.", "metadata": {"owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "references": ["https://docs.python.org/3/library/pickle.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/python.lang.security.deserialization.pickle.avoid-pickle", "shortlink": "https://sg.run/OPwB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.deserialization.pickle.avoid-pickle", "path": "downloaded_repos/AeonOrg_Aeon-MLTB/dev/token.py", "start": {"line": 32, "col": 5, "offset": 1008}, "end": {"line": 32, "col": 36, "offset": 1039}, "extra": {"message": "Avoid using `pickle`, which is known to lead to code execution vulnerabilities. When unpickling, the serialized data could be manipulated to run arbitrary code. Instead, consider serializing the relevant data as JSON or a similar text-based serialization format.", "metadata": {"owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "references": ["https://docs.python.org/3/library/pickle.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/python.lang.security.deserialization.pickle.avoid-pickle", "shortlink": "https://sg.run/OPwB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "path": "downloaded_repos/AeonOrg_Aeon-MLTB/update.py", "start": {"line": 133, "col": 15, "offset": 3568}, "end": {"line": 133, "col": 19, "offset": 3572}, "extra": {"message": "Found 'subprocess' function 'srun' with 'shell=True'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use 'shell=False' instead.", "fix": "False", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b602_subprocess_popen_with_shell_equals_true.html", "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://stackoverflow.com/questions/3172470/actual-meaning-of-shell-true-in-subprocess", "https://docs.python.org/3/library/subprocess.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["secure default"], "likelihood": "HIGH", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "shortlink": "https://sg.run/J92w"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/AeonOrg_Aeon-MLTB/web/templates/page.html", "start": {"line": 8, "col": 5, "offset": 183}, "end": {"line": 8, "col": 105, "offset": 283}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/AeonOrg_Aeon-MLTB/web/templates/page.html", "start": {"line": 9, "col": 5, "offset": 288}, "end": {"line": 9, "col": 109, "offset": 392}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/AeonOrg_Aeon-MLTB/.github/workflows/ruff_format.yml", "start": {"line": 42, "col": 19, "offset": 687}, "end": {"line": 42, "col": 22, "offset": 690}}]], "message": "Syntax error at line downloaded_repos/AeonOrg_Aeon-MLTB/.github/workflows/ruff_format.yml:42:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/AeonOrg_Aeon-MLTB/.github/workflows/ruff_format.yml", "spans": [{"file": "downloaded_repos/AeonOrg_Aeon-MLTB/.github/workflows/ruff_format.yml", "start": {"line": 42, "col": 19, "offset": 687}, "end": {"line": 42, "col": 22, "offset": 690}}]}], "paths": {"scanned": ["downloaded_repos/AeonOrg_Aeon-MLTB/.github/FUNDING.yml", "downloaded_repos/AeonOrg_Aeon-MLTB/.github/workflows/deploy.yml", "downloaded_repos/AeonOrg_Aeon-MLTB/.github/workflows/notification.yml", "downloaded_repos/AeonOrg_Aeon-MLTB/.github/workflows/ruff_format.yml", "downloaded_repos/AeonOrg_Aeon-MLTB/.gitignore", "downloaded_repos/AeonOrg_Aeon-MLTB/.idx/dev.nix", "downloaded_repos/AeonOrg_Aeon-MLTB/.pyspelling.yml", "downloaded_repos/AeonOrg_Aeon-MLTB/.python-version", "downloaded_repos/AeonOrg_Aeon-MLTB/Dockerfile", "downloaded_repos/AeonOrg_Aeon-MLTB/LICENSE", "downloaded_repos/AeonOrg_Aeon-MLTB/README.md", "downloaded_repos/AeonOrg_Aeon-MLTB/alive.py", "downloaded_repos/AeonOrg_Aeon-MLTB/aria.sh", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/__init__.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/__main__.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/core/__init__.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/core/aeon_client.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/core/config_manager.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/core/handlers.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/core/jdownloader_booter.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/core/startup.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/core/torrent_manager.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/__init__.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/aeon_utils/__init__.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/aeon_utils/access_check.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/aeon_utils/caption_gen.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/aeon_utils/command_gen.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/aeon_utils/shorteners.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/common.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/ext_utils/__init__.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/ext_utils/bot_utils.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/ext_utils/bulk_links.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/ext_utils/db_handler.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/ext_utils/exceptions.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/ext_utils/files_utils.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/ext_utils/help_messages.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/ext_utils/links_utils.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/ext_utils/media_utils.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/ext_utils/status_utils.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/ext_utils/task_manager.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/ext_utils/telegraph_helper.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/listeners/__init__.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/listeners/aria2_listener.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/listeners/direct_listener.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/listeners/jdownloader_listener.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/listeners/nzb_listener.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/listeners/qbit_listener.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/listeners/task_listener.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/__init__.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/download_utils/__init__.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/download_utils/aria2_download.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/download_utils/direct_downloader.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/download_utils/gd_download.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/download_utils/jd_download.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/download_utils/nzb_downloader.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/download_utils/qbit_download.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/download_utils/rclone_download.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/download_utils/telegram_download.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/download_utils/yt_dlp_download.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/gdrive_utils/__init__.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/gdrive_utils/clone.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/gdrive_utils/count.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/gdrive_utils/delete.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/gdrive_utils/download.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/gdrive_utils/helper.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/gdrive_utils/list.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/gdrive_utils/search.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/gdrive_utils/upload.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/rclone_utils/__init__.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/rclone_utils/list.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/rclone_utils/serve.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/rclone_utils/transfer.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/status_utils/__init__.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/status_utils/aria2_status.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/status_utils/direct_status.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/status_utils/ffmpeg_status.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/status_utils/gdrive_status.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/status_utils/jdownloader_status.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/status_utils/nzb_status.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/status_utils/qbit_status.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/status_utils/queue_status.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/status_utils/rclone_status.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/status_utils/sevenz_status.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/status_utils/telegram_status.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/status_utils/yt_dlp_status.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/status_utils/yt_status.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/telegram_uploader.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/youtube_utils/youtube_helper.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/youtube_utils/youtube_upload.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/telegram_helper/__init__.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/telegram_helper/bot_commands.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/telegram_helper/button_build.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/telegram_helper/filters.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/telegram_helper/message_utils.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/__init__.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/bot_settings.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/broadcast.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/cancel_task.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/chat_permission.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/clone.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/exec.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/file_selector.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/force_start.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/gd_count.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/gd_delete.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/gd_search.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/help.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/mediainfo.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/mirror_leech.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/nzb_search.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/restart.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/rss.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/search.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/services.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/shell.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/sox.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/speedtest.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/stats.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/status.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/users_settings.py", "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/ytdlp.py", "downloaded_repos/AeonOrg_Aeon-MLTB/config_sample.py", "downloaded_repos/AeonOrg_Aeon-MLTB/default.otf", "downloaded_repos/AeonOrg_Aeon-MLTB/dev/token.py", "downloaded_repos/AeonOrg_Aeon-MLTB/docs/COMMANDS.md", "downloaded_repos/AeonOrg_Aeon-MLTB/docs/CONFIGURATIONS.md", "downloaded_repos/AeonOrg_Aeon-MLTB/docs/DEPLOYMENT.md", "downloaded_repos/AeonOrg_Aeon-MLTB/docs/EXTRAS.md", "downloaded_repos/AeonOrg_Aeon-MLTB/docs/FEATURES.md", "downloaded_repos/AeonOrg_Aeon-MLTB/docs/README.md", "downloaded_repos/AeonOrg_Aeon-MLTB/myjd/__init__.py", "downloaded_repos/AeonOrg_Aeon-MLTB/myjd/const.py", "downloaded_repos/AeonOrg_Aeon-MLTB/myjd/exception.py", "downloaded_repos/AeonOrg_Aeon-MLTB/myjd/myjdapi.py", "downloaded_repos/AeonOrg_Aeon-MLTB/pyproject.toml", "downloaded_repos/AeonOrg_Aeon-MLTB/qBittorrent/config/qBittorrent.conf", "downloaded_repos/AeonOrg_Aeon-MLTB/requirements.txt", "downloaded_repos/AeonOrg_Aeon-MLTB/sabnzbd/SABnzbd.ini", "downloaded_repos/AeonOrg_Aeon-MLTB/sabnzbdapi/__init__.py", "downloaded_repos/AeonOrg_Aeon-MLTB/sabnzbdapi/bound_methods.py", "downloaded_repos/AeonOrg_Aeon-MLTB/sabnzbdapi/exception.py", "downloaded_repos/AeonOrg_Aeon-MLTB/sabnzbdapi/job_functions.py", "downloaded_repos/AeonOrg_Aeon-MLTB/sabnzbdapi/requests.py", "downloaded_repos/AeonOrg_Aeon-MLTB/start.sh", "downloaded_repos/AeonOrg_Aeon-MLTB/update.py", "downloaded_repos/AeonOrg_Aeon-MLTB/web/__init__.py", "downloaded_repos/AeonOrg_Aeon-MLTB/web/nodes.py", "downloaded_repos/AeonOrg_Aeon-MLTB/web/templates/page.html", "downloaded_repos/AeonOrg_Aeon-MLTB/web/wserver.py"], "skipped": [{"path": "downloaded_repos/AeonOrg_Aeon-MLTB/.github/workflows/ruff_format.yml", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.786163091659546, "profiling_times": {"config_time": 6.4702489376068115, "core_time": 5.0231239795684814, "ignores_time": 0.0018224716186523438, "total_time": 11.496204614639282}, "parsing_time": {"total_time": 2.664731025695801, "per_file_time": {"mean": 0.020035571621772934, "std_dev": 0.0009524599483805059}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 24.61006259918213, "per_file_time": {"mean": 0.05696773749810676, "std_dev": 0.04022806658982155}, "very_slow_stats": {"time_ratio": 0.1444520438087648, "count_ratio": 0.004629629629629629}, "very_slow_files": [{"fpath": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/listeners/task_listener.py", "ftime": 1.762420892715454}, {"fpath": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/common.py", "ftime": 1.7925529479980469}]}, "matching_time": {"total_time": 10.154159545898438, "per_file_and_rule_time": {"mean": 0.007751266828930105, "std_dev": 0.0006903416257463644}, "very_slow_stats": {"time_ratio": 0.2862193315030287, "count_ratio": 0.012213740458015267}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/AeonOrg_Aeon-MLTB/myjd/myjdapi.py", "rule_id": "python.lang.security.dangerous-system-call.dangerous-system-call", "time": 0.13196086883544922}, {"fpath": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/youtube_utils/youtube_upload.py", "rule_id": "python.django.security.injection.sql.sql-injection-using-db-cursor-execute.sql-injection-db-cursor-execute", "time": 0.13286805152893066}, {"fpath": "downloaded_repos/AeonOrg_Aeon-MLTB/myjd/myjdapi.py", "rule_id": "python.django.security.injection.command.command-injection-os-system.command-injection-os-system", "time": 0.1331930160522461}, {"fpath": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/common.py", "rule_id": "python.aws-lambda.security.tainted-html-string.tainted-html-string", "time": 0.14110398292541504}, {"fpath": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/rclone_utils/transfer.py", "rule_id": "python.django.security.injection.command.subprocess-injection.subprocess-injection", "time": 0.16582703590393066}, {"fpath": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/bot_settings.py", "rule_id": "python.sqlalchemy.security.sqlalchemy-sql-injection.sqlalchemy-sql-injection", "time": 0.16785192489624023}, {"fpath": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/bot_settings.py", "rule_id": "python.django.security.injection.command.subprocess-injection.subprocess-injection", "time": 0.20122003555297852}, {"fpath": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/ext_utils/media_utils.py", "rule_id": "python.django.security.injection.command.subprocess-injection.subprocess-injection", "time": 0.2270948886871338}, {"fpath": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/youtube_utils/youtube_upload.py", "rule_id": "python.django.security.injection.raw-html-format.raw-html-format", "time": 0.2320718765258789}, {"fpath": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/listeners/task_listener.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.6683218479156494}]}, "tainting_time": {"total_time": 6.723049640655518, "per_def_and_rule_time": {"mean": 0.0026753082533448147, "std_dev": 0.00018009319976314847}, "very_slow_stats": {"time_ratio": 0.34899367392196623, "count_ratio": 0.006366892160764027}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/youtube_utils/youtube_upload.py", "fline": 178, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.13936591148376465}, {"fpath": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/ytdlp.py", "fline": 301, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.15665793418884277}, {"fpath": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/status.py", "fline": 82, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.17119407653808594}, {"fpath": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/ext_utils/media_utils.py", "fline": 674, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.17958712577819824}, {"fpath": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/gdrive_utils/search.py", "fline": 99, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.20618987083435059}, {"fpath": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/rss.py", "fline": 80, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.21052908897399902}, {"fpath": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/rss.py", "fline": 677, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.21077704429626465}, {"fpath": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/download_utils/jd_download.py", "fline": 121, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.21108794212341309}, {"fpath": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/common.py", "fline": 782, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.2182319164276123}, {"fpath": "downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/mirror_leech.py", "fline": 82, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.2254490852355957}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}