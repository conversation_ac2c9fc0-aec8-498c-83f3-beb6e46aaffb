{"version": "1.130.0", "results": [{"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/azerothcore_acore-cms/data/docker/docker-compose.override.yml", "start": {"line": 11, "col": 3, "offset": 348}, "end": {"line": 11, "col": 13, "offset": 358}, "extra": {"message": "Service 'phpmyadmin' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/azerothcore_acore-cms/data/docker/docker-compose.override.yml", "start": {"line": 11, "col": 3, "offset": 348}, "end": {"line": 11, "col": 13, "offset": 358}, "extra": {"message": "Service 'phpmyadmin' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.doctrine.security.audit.doctrine-dbal-dangerous-query.doctrine-dbal-dangerous-query", "path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Components/CharactersMenu/CharactersController.php", "start": {"line": 31, "col": 24, "offset": 961}, "end": {"line": 31, "col": 51, "offset": 988}, "extra": {"message": "Detected string concatenation with a non-literal variable in a Doctrine DBAL query method. This could lead to SQL injection if the variable is user-controlled and not properly sanitized. In order to prevent SQL injection, use parameterized queries or prepared statements instead.", "metadata": {"category": "security", "cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://www.doctrine-project.org/projects/doctrine-dbal/en/current/reference/security.html", "https://cheatsheetseries.owasp.org/cheatsheets/SQL_Injection_Prevention_Cheat_Sheet.html"], "technology": ["doctrine"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.doctrine.security.audit.doctrine-dbal-dangerous-query.doctrine-dbal-dangerous-query", "shortlink": "https://sg.run/KXWn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.doctrine.security.audit.doctrine-dbal-dangerous-query.doctrine-dbal-dangerous-query", "path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Components/UnstuckMenu/UnstuckController.php", "start": {"line": 61, "col": 24, "offset": 1879}, "end": {"line": 61, "col": 51, "offset": 1906}, "extra": {"message": "Detected string concatenation with a non-literal variable in a Doctrine DBAL query method. This could lead to SQL injection if the variable is user-controlled and not properly sanitized. In order to prevent SQL injection, use parameterized queries or prepared statements instead.", "metadata": {"category": "security", "cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://www.doctrine-project.org/projects/doctrine-dbal/en/current/reference/security.html", "https://cheatsheetseries.owasp.org/cheatsheets/SQL_Injection_Prevention_Cheat_Sheet.html"], "technology": ["doctrine"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.doctrine.security.audit.doctrine-dbal-dangerous-query.doctrine-dbal-dangerous-query", "shortlink": "https://sg.run/KXWn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.doctrine.security.audit.doctrine-dbal-dangerous-query.doctrine-dbal-dangerous-query", "path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Components/UnstuckMenu/UnstuckController.php", "start": {"line": 84, "col": 21, "offset": 2638}, "end": {"line": 84, "col": 43, "offset": 2660}, "extra": {"message": "Detected string concatenation with a non-literal variable in a Doctrine DBAL query method. This could lead to SQL injection if the variable is user-controlled and not properly sanitized. In order to prevent SQL injection, use parameterized queries or prepared statements instead.", "metadata": {"category": "security", "cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://www.doctrine-project.org/projects/doctrine-dbal/en/current/reference/security.html", "https://cheatsheetseries.owasp.org/cheatsheets/SQL_Injection_Prevention_Cheat_Sheet.html"], "technology": ["doctrine"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.doctrine.security.audit.doctrine-dbal-dangerous-query.doctrine-dbal-dangerous-query", "shortlink": "https://sg.run/KXWn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.doctrine.security.audit.doctrine-dbal-dangerous-query.doctrine-dbal-dangerous-query", "path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Components/UserPanel/UserController.php", "start": {"line": 105, "col": 24, "offset": 4648}, "end": {"line": 105, "col": 51, "offset": 4675}, "extra": {"message": "Detected string concatenation with a non-literal variable in a Doctrine DBAL query method. This could lead to SQL injection if the variable is user-controlled and not properly sanitized. In order to prevent SQL injection, use parameterized queries or prepared statements instead.", "metadata": {"category": "security", "cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://www.doctrine-project.org/projects/doctrine-dbal/en/current/reference/security.html", "https://cheatsheetseries.owasp.org/cheatsheets/SQL_Injection_Prevention_Cheat_Sheet.html"], "technology": ["doctrine"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.doctrine.security.audit.doctrine-dbal-dangerous-query.doctrine-dbal-dangerous-query", "shortlink": "https://sg.run/KXWn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.doctrine.security.audit.doctrine-dbal-dangerous-query.doctrine-dbal-dangerous-query", "path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Components/UserPanel/UserController.php", "start": {"line": 113, "col": 24, "offset": 5002}, "end": {"line": 113, "col": 51, "offset": 5029}, "extra": {"message": "Detected string concatenation with a non-literal variable in a Doctrine DBAL query method. This could lead to SQL injection if the variable is user-controlled and not properly sanitized. In order to prevent SQL injection, use parameterized queries or prepared statements instead.", "metadata": {"category": "security", "cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://www.doctrine-project.org/projects/doctrine-dbal/en/current/reference/security.html", "https://cheatsheetseries.owasp.org/cheatsheets/SQL_Injection_Prevention_Cheat_Sheet.html"], "technology": ["doctrine"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.doctrine.security.audit.doctrine-dbal-dangerous-query.doctrine-dbal-dangerous-query", "shortlink": "https://sg.run/KXWn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.doctrine.security.audit.doctrine-dbal-dangerous-query.doctrine-dbal-dangerous-query", "path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Components/UserPanel/UserController.php", "start": {"line": 124, "col": 24, "offset": 5469}, "end": {"line": 124, "col": 51, "offset": 5496}, "extra": {"message": "Detected string concatenation with a non-literal variable in a Doctrine DBAL query method. This could lead to SQL injection if the variable is user-controlled and not properly sanitized. In order to prevent SQL injection, use parameterized queries or prepared statements instead.", "metadata": {"category": "security", "cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://www.doctrine-project.org/projects/doctrine-dbal/en/current/reference/security.html", "https://cheatsheetseries.owasp.org/cheatsheets/SQL_Injection_Prevention_Cheat_Sheet.html"], "technology": ["doctrine"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.doctrine.security.audit.doctrine-dbal-dangerous-query.doctrine-dbal-dangerous-query", "shortlink": "https://sg.run/KXWn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.doctrine.security.audit.doctrine-dbal-dangerous-query.doctrine-dbal-dangerous-query", "path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Components/UserPanel/UserController.php", "start": {"line": 140, "col": 24, "offset": 6139}, "end": {"line": 146, "col": 10, "offset": 6392}, "extra": {"message": "Detected string concatenation with a non-literal variable in a Doctrine DBAL query method. This could lead to SQL injection if the variable is user-controlled and not properly sanitized. In order to prevent SQL injection, use parameterized queries or prepared statements instead.", "metadata": {"category": "security", "cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://www.doctrine-project.org/projects/doctrine-dbal/en/current/reference/security.html", "https://cheatsheetseries.owasp.org/cheatsheets/SQL_Injection_Prevention_Cheat_Sheet.html"], "technology": ["doctrine"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.doctrine.security.audit.doctrine-dbal-dangerous-query.doctrine-dbal-dangerous-query", "shortlink": "https://sg.run/KXWn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.doctrine.security.audit.doctrine-dbal-dangerous-query.doctrine-dbal-dangerous-query", "path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Hooks/WooCommerce/NameUnlock.php", "start": {"line": 129, "col": 29, "offset": 5353}, "end": {"line": 132, "col": 22, "offset": 5478}, "extra": {"message": "Detected string concatenation with a non-literal variable in a Doctrine DBAL query method. This could lead to SQL injection if the variable is user-controlled and not properly sanitized. In order to prevent SQL injection, use parameterized queries or prepared statements instead.", "metadata": {"category": "security", "cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://www.doctrine-project.org/projects/doctrine-dbal/en/current/reference/security.html", "https://cheatsheetseries.owasp.org/cheatsheets/SQL_Injection_Prevention_Cheat_Sheet.html"], "technology": ["doctrine"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.doctrine.security.audit.doctrine-dbal-dangerous-query.doctrine-dbal-dangerous-query", "shortlink": "https://sg.run/KXWn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/azerothcore_acore-cms/apps/db_exporter/db_import.sh", "start": {"line": 3, "col": 1, "offset": 0}, "end": {"line": 11, "col": 3, "offset": 298}}]], "message": "Syntax error at line downloaded_repos/azerothcore_acore-cms/apps/db_exporter/db_import.sh:3:\n `CURPATH=\"$( cd \"$( dirname \"${BASH_SOURCE[0]}\" )\" && pwd )\"\r\n\r\nsource \"$CURPATH/shared.sh\"\r\n\r\nfor d in \"$SQL_PATH/*/\"\" ; do\r\n    echo \"Importing $d\"\r\n    find \"$SQL_PATH/$d/\" -name '*.sql' | awk '{ print \"source\",$0 }' | mysql --batch -u \"$MYSQL_USER\" -p \"$MYSQL_PASS\" \"$d\" && echo \"done\"\r\ndone\r\n\r\n` was unexpected", "path": "downloaded_repos/azerothcore_acore-cms/apps/db_exporter/db_import.sh", "spans": [{"file": "downloaded_repos/azerothcore_acore-cms/apps/db_exporter/db_import.sh", "start": {"line": 3, "col": 1, "offset": 0}, "end": {"line": 11, "col": 3, "offset": 298}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/azerothcore_acore-cms/apps/init/healthcheck.sh", "start": {"line": 4, "col": 25, "offset": 0}, "end": {"line": 4, "col": 28, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/azerothcore_acore-cms/apps/init/healthcheck.sh:4:\n ` &&` was unexpected", "path": "downloaded_repos/azerothcore_acore-cms/apps/init/healthcheck.sh", "spans": [{"file": "downloaded_repos/azerothcore_acore-cms/apps/init/healthcheck.sh", "start": {"line": 4, "col": 25, "offset": 0}, "end": {"line": 4, "col": 28, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/azerothcore_acore-cms/data/docker/Wordpress.Dockerfile", "start": {"line": 8, "col": 3, "offset": 0}, "end": {"line": 66, "col": 56, "offset": 1855}}]], "message": "Syntax error at line downloaded_repos/azerothcore_acore-cms/data/docker/Wordpress.Dockerfile:8:\n `&& apt-get install -y \\\r\n     libfreetype6-dev \\\r\n     libmcrypt-dev \\\r\n     git \\\r\n     libxml2-dev \\\r\n     libpng-dev \\\r\n     zlib1g-dev \\\r\n     libgd3 \\\r\n     libonig-dev \\\r\n     libgd-dev \\\r\n     libicu-dev \\\r\n     libgmp-dev \\\r\n     redis-tools \\\r\n     procps \\\r\n     wget \\\r\n     netcat-openbsd \\\r\n     sendmail \\\r\n     socat \\\r\n     acl \\\r\n     libfcgi0ldbl \\\r\n     && apt-get clean -y\r\n\r\n# Install PHP extensions\r\nRUN docker-php-ext-install soap \\\r\n    && docker-php-ext-install mbstring \\\r\n    && docker-php-ext-install pdo_mysql \\\r\n    && docker-php-ext-install opcache \\\r\n    && docker-php-ext-install mysqli \\\r\n    && docker-php-ext-configure intl \\\r\n    && docker-php-ext-install intl \\\r\n    && docker-php-ext-configure gd --with-freetype=/usr/include/ --with-jpeg=/usr/include/ \\\r\n    && docker-php-ext-install -j$(nproc) gd \\\r\n    && docker-php-ext-install gmp\r\n\r\n# Install Redis PHP extension\r\nRUN pecl install redis \\\r\n    && docker-php-ext-enable redis\r\n\r\n# Install Composer\r\nRUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer\r\n\r\nRUN wget --no-check-certificate -O /usr/local/bin/wp \\\r\n       https://raw.githubusercontent.com/wp-cli/builds/gh-pages/phar/wp-cli.phar \\\r\n    && chmod +x /usr/local/bin/wp\r\n\r\n# Add init script for runtime configuration\r\nRUN mkdir -p /usr/local/bin/apps/\r\nCOPY apps/init /usr/local/bin/apps/init\r\nCOPY apps/bash-lib /usr/local/bin/apps/bash-lib\r\nRUN chmod +x /usr/local/bin/apps/init/init.sh\r\nRUN chmod +x /usr/local/bin/apps/init/healthcheck.sh\r\n\r\n# Remove and re-add www-data user with specified UID and GID\r\nRUN deluser www-data \\\r\n    && addgroup --gid $GROUP_ID www-data \\\r\n    && adduser --disabled-password --gecos '' --uid $USER_ID --gid $GROUP_ID www-data \\\r\n    && passwd -d www-data\r\n\r\nENTRYPOINT [\"bash\", \"/usr/local/bin/apps/init/init.sh\"]` was unexpected", "path": "downloaded_repos/azerothcore_acore-cms/data/docker/Wordpress.Dockerfile", "spans": [{"file": "downloaded_repos/azerothcore_acore-cms/data/docker/Wordpress.Dockerfile", "start": {"line": 8, "col": 3, "offset": 0}, "end": {"line": 66, "col": 56, "offset": 1855}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/azerothcore_acore-cms/docs/_includes/sidebar.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 7, "offset": 6}}, {"path": "downloaded_repos/azerothcore_acore-cms/docs/_includes/sidebar.html", "start": {"line": 1, "col": 120, "offset": 0}, "end": {"line": 1, "col": 122, "offset": 2}}]], "message": "Syntax error at line downloaded_repos/azerothcore_acore-cms/docs/_includes/sidebar.html:1:\n `<PERSON>u (` was unexpected", "path": "downloaded_repos/azerothcore_acore-cms/docs/_includes/sidebar.html", "spans": [{"file": "downloaded_repos/azerothcore_acore-cms/docs/_includes/sidebar.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 7, "offset": 6}}, {"file": "downloaded_repos/azerothcore_acore-cms/docs/_includes/sidebar.html", "start": {"line": 1, "col": 120, "offset": 0}, "end": {"line": 1, "col": 122, "offset": 2}}]}], "paths": {"scanned": ["downloaded_repos/azerothcore_acore-cms/.env.docker", "downloaded_repos/azerothcore_acore-cms/.github/workflows/acore-ci-cd.yml", "downloaded_repos/azerothcore_acore-cms/.github/workflows/sync-plugins.yml", "downloaded_repos/azerothcore_acore-cms/.gitignore", "downloaded_repos/azerothcore_acore-cms/.gitmodules", "downloaded_repos/azerothcore_acore-cms/.htaccess", "downloaded_repos/azerothcore_acore-cms/.vscode/extensions.json", "downloaded_repos/azerothcore_acore-cms/acore-cms.code-workspace", "downloaded_repos/azerothcore_acore-cms/apps/azerothcore/mysql-tools/.gitignore", "downloaded_repos/azerothcore_acore-cms/apps/azerothcore/mysql-tools/README", "downloaded_repos/azerothcore_acore-cms/apps/azerothcore/mysql-tools/bin/dump-parser", "downloaded_repos/azerothcore_acore-cms/apps/azerothcore/mysql-tools/build-dump-parser.sh", "downloaded_repos/azerothcore_acore-cms/apps/azerothcore/mysql-tools/dump-parser.c", "downloaded_repos/azerothcore_acore-cms/apps/azerothcore/mysql-tools/mysql-config.dist", "downloaded_repos/azerothcore_acore-cms/apps/azerothcore/mysql-tools/mysql-dump", "downloaded_repos/azerothcore_acore-cms/apps/azerothcore/mysql-tools/mysql-import", "downloaded_repos/azerothcore_acore-cms/apps/azerothcore/mysql-tools/mysql-tools", "downloaded_repos/azerothcore_acore-cms/apps/azerothcore/mysql-tools/shared-def", "downloaded_repos/azerothcore_acore-cms/apps/ci/test-setup.sh", "downloaded_repos/azerothcore_acore-cms/apps/db_exporter/.gitignore", "downloaded_repos/azerothcore_acore-cms/apps/db_exporter/README.md", "downloaded_repos/azerothcore_acore-cms/apps/db_exporter/db_export.sh", "downloaded_repos/azerothcore_acore-cms/apps/db_exporter/db_import.sh", "downloaded_repos/azerothcore_acore-cms/apps/db_exporter/shared.sh", "downloaded_repos/azerothcore_acore-cms/apps/import-srv.sh", "downloaded_repos/azerothcore_acore-cms/apps/init/healthcheck.sh", "downloaded_repos/azerothcore_acore-cms/apps/init/init.lib.sh", "downloaded_repos/azerothcore_acore-cms/apps/init/init.sh", "downloaded_repos/azerothcore_acore-cms/conf/init/.gitkeep", "downloaded_repos/azerothcore_acore-cms/data/docker/Wordpress.Dockerfile", "downloaded_repos/azerothcore_acore-cms/data/docker/docker-compose.override.yml", "downloaded_repos/azerothcore_acore-cms/data/plugins/.gitkeep", "downloaded_repos/azerothcore_acore-cms/docker-compose.yml", "downloaded_repos/azerothcore_acore-cms/docs/.env-files/Gemfile.gitlab", "downloaded_repos/azerothcore_acore-cms/docs/README.md", "downloaded_repos/azerothcore_acore-cms/docs/_config.yml", "downloaded_repos/azerothcore_acore-cms/docs/_includes/sidebar.html", "downloaded_repos/azerothcore_acore-cms/docs/configure-acore-docker.md", "downloaded_repos/azerothcore_acore-cms/docs/configure-cms.md", "downloaded_repos/azerothcore_acore-cms/docs/dashboard.png", "downloaded_repos/azerothcore_acore-cms/docs/logo.png", "downloaded_repos/azerothcore_acore-cms/docs/nginx-for-multisite.md", "downloaded_repos/azerothcore_acore-cms/docs/themes.md", "downloaded_repos/azerothcore_acore-cms/init.sh", "downloaded_repos/azerothcore_acore-cms/jsconfig.json", "downloaded_repos/azerothcore_acore-cms/package.json", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/LICENSE", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/README.md", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/acore-wp-plugin.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/app.sh", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/composer.json", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/composer.lock", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/data/languages/.gitkeep", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Components/AdminPanel/AdminPanel.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Components/AdminPanel/Pages/ElunaSettings.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Components/AdminPanel/Pages/Home.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Components/AdminPanel/Pages/PVPRewards.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Components/AdminPanel/Pages/RealmSettings.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Components/AdminPanel/Pages/Tools.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Components/AdminPanel/SettingsController.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Components/AdminPanel/SettingsView.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Components/CharactersMenu/CharactersController.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Components/CharactersMenu/CharactersMenu.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Components/CharactersMenu/CharactersView.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Components/ServerInfo/ServerInfo.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Components/ServerInfo/ServerInfoApi.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Components/ServerInfo/ServerInfoWidget.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Components/Tools/ToolsApi.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Components/Tools/ToolsInfo.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Components/UnstuckMenu/UnstuckApi.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Components/UnstuckMenu/UnstuckController.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Components/UnstuckMenu/UnstuckMenu.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Components/UnstuckMenu/UnstuckView.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Components/UserPanel/Pages/ItemRestorationPage.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Components/UserPanel/Pages/RafProgressPage.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Components/UserPanel/UserController.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Components/UserPanel/UserMenu.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Components/UserPanel/UserView.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Deps/class-tgm-plugin-activation.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Hooks/Head/head.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Hooks/Subscriptions/sync_subscription.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Hooks/User/Include.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Hooks/User/User.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Hooks/Various/ItemSku.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Hooks/Various/tgmplugin_activator.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Hooks/WooCommerce/CarbonCopy.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Hooks/WooCommerce/CartValidation.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Hooks/WooCommerce/CharChange.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Hooks/WooCommerce/CharTransfer.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Hooks/WooCommerce/FieldElements.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Hooks/WooCommerce/GuildChange.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Hooks/WooCommerce/ItemRestoration.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Hooks/WooCommerce/ItemSend.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Hooks/WooCommerce/NameUnlock.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Hooks/WooCommerce/Smartstone.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Hooks/WooCommerce/TransmogItemSend.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Hooks/WooCommerce/TransmogItemsetSend.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Hooks/WooCommerce/WooCommerce.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Lib/WpClass.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Manager/ACoreServices.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Manager/AcoreConnector/AcoreManager.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Manager/AcoreConnector/AcoreManagerTrait.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Manager/AcoreConnector/AcoreRepository.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Manager/Auth/AuthManager.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Manager/Auth/Entity/AccountAccessEntity.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Manager/Auth/Entity/AccountBannedEntity.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Manager/Auth/Entity/AccountEntity.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Manager/Auth/Repository/AccountAccessRepository.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Manager/Auth/Repository/AccountBannedRepository.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Manager/Auth/Repository/AccountRepository.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Manager/Character/CharacterManager.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Manager/Character/Entity/CharacterBannedEntity.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Manager/Character/Entity/CharacterEntity.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Manager/Character/Repository/CharacterBannedRepository.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Manager/Character/Repository/CharacterRepository.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Manager/Common.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Manager/Opts.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Manager/Soap/AccountService.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Manager/Soap/AcoreSoap.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Manager/Soap/AcoreSoapTrait.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Manager/Soap/CharacterService.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Manager/Soap/GuildService.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Manager/Soap/MailService.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Manager/Soap/ServerService.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Manager/Soap/SmartstoneService.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Manager/Soap/TransmogService.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Manager/Soap/UnstuckService.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Manager/UserValidator.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Manager/World/WorldManager.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Utils/AcoreUtils.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/boot.php", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/admin_logo.svg", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/class/1.webp", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/class/11.webp", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/class/2.webp", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/class/3.webp", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/class/4.webp", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/class/5.webp", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/class/6.webp", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/class/7.webp", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/class/8.webp", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/class/9.webp", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/css/main.css", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/logo.png", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/logo.svg", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/race/10f.webp", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/race/10m.webp", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/race/11f.webp", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/race/11m.webp", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/race/1f.webp", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/race/1m.webp", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/race/2f.webp", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/race/2m.webp", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/race/3f.webp", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/race/3m.webp", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/race/4f.webp", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/race/4m.webp", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/race/5f.webp", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/race/5m.webp", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/race/6f.webp", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/race/6m.webp", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/race/7f.webp", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/race/7m.webp", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/race/8f.webp", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/race/8m.webp", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/unstuck/hearthstone.jpg", "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/web/assets/unstuck/unstuck.js"], "skipped": [{"path": "downloaded_repos/azerothcore_acore-cms/apps/azerothcore/mysql-tools/bin/mysql.exe", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/azerothcore_acore-cms/apps/azerothcore/mysql-tools/bin/mysqldump.exe", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/azerothcore_acore-cms/apps/azerothcore/mysql-tools/bin/mysqlimport.exe", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/azerothcore_acore-cms/apps/db_exporter/db_import.sh", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/azerothcore_acore-cms/apps/init/healthcheck.sh", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/azerothcore_acore-cms/conf/dist/certs/cert.crt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/conf/dist/certs/cert.key", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/conf/dist/conf.sh", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/conf/dist/maintenance/conf/default.conf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/conf/dist/maintenance/html/index.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/conf/dist/nginx-conf/wordpress.conf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/conf/dist/php-conf/upload.ini", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/data/docker/Wordpress.Dockerfile", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/azerothcore_acore-cms/docs/_includes/sidebar.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/azerothcore_acore-cms/docs/shop.png", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/autoload.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/bin/doctrine", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/bin/doctrine-dbal", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/bin/doctrine-dbal.bat", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/bin/doctrine.bat", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/composer/ClassLoader.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/composer/InstalledVersions.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/composer/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/composer/autoload_classmap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/composer/autoload_files.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/composer/autoload_namespaces.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/composer/autoload_psr4.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/composer/autoload_real.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/composer/autoload_static.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/composer/installed.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/composer/installed.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/composer/package-versions-deprecated/CHANGELOG.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/composer/package-versions-deprecated/CONTRIBUTING.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/composer/package-versions-deprecated/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/composer/package-versions-deprecated/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/composer/package-versions-deprecated/SECURITY.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/composer/package-versions-deprecated/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/composer/package-versions-deprecated/composer.lock", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/composer/package-versions-deprecated/src/PackageVersions/FallbackVersions.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/composer/package-versions-deprecated/src/PackageVersions/Installer.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/composer/package-versions-deprecated/src/PackageVersions/Versions.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/composer/platform_check.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/annotations/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/annotations/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/annotations/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/annotations/lib/Doctrine/Common/Annotations/Annotation/Attribute.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/annotations/lib/Doctrine/Common/Annotations/Annotation/Attributes.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/annotations/lib/Doctrine/Common/Annotations/Annotation/Enum.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/annotations/lib/Doctrine/Common/Annotations/Annotation/IgnoreAnnotation.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/annotations/lib/Doctrine/Common/Annotations/Annotation/NamedArgumentConstructor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/annotations/lib/Doctrine/Common/Annotations/Annotation/Required.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/annotations/lib/Doctrine/Common/Annotations/Annotation/Target.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/annotations/lib/Doctrine/Common/Annotations/Annotation.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/annotations/lib/Doctrine/Common/Annotations/AnnotationException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/annotations/lib/Doctrine/Common/Annotations/AnnotationReader.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/annotations/lib/Doctrine/Common/Annotations/AnnotationRegistry.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/annotations/lib/Doctrine/Common/Annotations/CachedReader.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/annotations/lib/Doctrine/Common/Annotations/DocLexer.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/annotations/lib/Doctrine/Common/Annotations/DocParser.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/annotations/lib/Doctrine/Common/Annotations/FileCacheReader.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/annotations/lib/Doctrine/Common/Annotations/ImplicitlyIgnoredAnnotationNames.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/annotations/lib/Doctrine/Common/Annotations/IndexedReader.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/annotations/lib/Doctrine/Common/Annotations/NamedArgumentConstructorAnnotation.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/annotations/lib/Doctrine/Common/Annotations/PhpParser.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/annotations/lib/Doctrine/Common/Annotations/PsrCachedReader.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/annotations/lib/Doctrine/Common/Annotations/Reader.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/annotations/lib/Doctrine/Common/Annotations/SimpleAnnotationReader.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/annotations/lib/Doctrine/Common/Annotations/TokenParser.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/cache/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/cache/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/cache/UPGRADE-1.11.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/cache/UPGRADE-1.4.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/cache/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/cache/lib/Doctrine/Common/Cache/Cache.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/cache/lib/Doctrine/Common/Cache/CacheProvider.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/cache/lib/Doctrine/Common/Cache/ClearableCache.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/cache/lib/Doctrine/Common/Cache/FlushableCache.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/cache/lib/Doctrine/Common/Cache/MultiDeleteCache.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/cache/lib/Doctrine/Common/Cache/MultiGetCache.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/cache/lib/Doctrine/Common/Cache/MultiOperationCache.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/cache/lib/Doctrine/Common/Cache/MultiPutCache.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/cache/lib/Doctrine/Common/Cache/Psr6/CacheAdapter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/cache/lib/Doctrine/Common/Cache/Psr6/CacheItem.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/cache/lib/Doctrine/Common/Cache/Psr6/DoctrineProvider.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/cache/lib/Doctrine/Common/Cache/Psr6/InvalidArgument.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/cache/lib/Doctrine/Common/Cache/Psr6/TypedCacheItem.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/collections/.doctrine-project.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/collections/CONTRIBUTING.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/collections/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/collections/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/collections/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/collections/lib/Doctrine/Common/Collections/AbstractLazyCollection.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/collections/lib/Doctrine/Common/Collections/ArrayCollection.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/collections/lib/Doctrine/Common/Collections/Collection.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/collections/lib/Doctrine/Common/Collections/Criteria.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/collections/lib/Doctrine/Common/Collections/Expr/ClosureExpressionVisitor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/collections/lib/Doctrine/Common/Collections/Expr/Comparison.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/collections/lib/Doctrine/Common/Collections/Expr/CompositeExpression.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/collections/lib/Doctrine/Common/Collections/Expr/Expression.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/collections/lib/Doctrine/Common/Collections/Expr/ExpressionVisitor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/collections/lib/Doctrine/Common/Collections/Expr/Value.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/collections/lib/Doctrine/Common/Collections/ExpressionBuilder.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/collections/lib/Doctrine/Common/Collections/Selectable.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/collections/phpstan.neon.dist", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/collections/psalm.xml.dist", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/common/.doctrine-project.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/common/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/common/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/common/UPGRADE_TO_2_1", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/common/UPGRADE_TO_2_2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/common/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/common/humbug.json.dist", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/common/lib/Doctrine/Common/ClassLoader.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/common/lib/Doctrine/Common/CommonException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/common/lib/Doctrine/Common/Comparable.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/common/lib/Doctrine/Common/Proxy/AbstractProxyFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/common/lib/Doctrine/Common/Proxy/Autoloader.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/common/lib/Doctrine/Common/Proxy/Exception/InvalidArgumentException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/common/lib/Doctrine/Common/Proxy/Exception/OutOfBoundsException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/common/lib/Doctrine/Common/Proxy/Exception/ProxyException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/common/lib/Doctrine/Common/Proxy/Exception/UnexpectedValueException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/common/lib/Doctrine/Common/Proxy/Proxy.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/common/lib/Doctrine/Common/Proxy/ProxyDefinition.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/common/lib/Doctrine/Common/Proxy/ProxyGenerator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/common/lib/Doctrine/Common/Util/ClassUtils.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/common/lib/Doctrine/Common/Util/Debug.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/common/phpstan.neon.dist", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/common/psalm.xml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/CONTRIBUTING.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/bin/doctrine-dbal", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/bin/doctrine-dbal.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/ArrayParameters/Exception/MissingNamedParameter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/ArrayParameters/Exception/MissingPositionalParameter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/ArrayParameters/Exception.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Cache/ArrayResult.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Cache/CacheException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Cache/CachingResult.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Cache/QueryCacheProfile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/ColumnCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Configuration.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Connection.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/ConnectionException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Connections/PrimaryReadReplicaConnection.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/API/ExceptionConverter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/API/IBMDB2/ExceptionConverter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/API/MySQL/ExceptionConverter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/API/OCI/ExceptionConverter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/API/PostgreSQL/ExceptionConverter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/API/SQLSrv/ExceptionConverter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/API/SQLite/ExceptionConverter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/AbstractDB2Driver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/AbstractException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/AbstractMySQLDriver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/AbstractOracleDriver/EasyConnectString.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/AbstractOracleDriver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/AbstractPostgreSQLDriver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/AbstractSQLServerDriver/Exception/PortWithoutHost.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/AbstractSQLServerDriver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/AbstractSQLiteDriver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/Connection.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/Exception/UnknownParameterType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/Exception.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/FetchUtils.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/IBMDB2/Connection.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/IBMDB2/DataSourceName.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/IBMDB2/Driver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/IBMDB2/Exception/CannotCopyStreamToStream.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/IBMDB2/Exception/CannotCreateTemporaryFile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/IBMDB2/Exception/CannotWriteToTemporaryFile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/IBMDB2/Exception/ConnectionError.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/IBMDB2/Exception/ConnectionFailed.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/IBMDB2/Exception/PrepareFailed.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/IBMDB2/Exception/StatementError.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/IBMDB2/Result.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/IBMDB2/Statement.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/Middleware.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/Mysqli/Connection.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/Mysqli/Driver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/Mysqli/Exception/ConnectionError.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/Mysqli/Exception/ConnectionFailed.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/Mysqli/Exception/FailedReadingStreamOffset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/Mysqli/Exception/HostRequired.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/Mysqli/Exception/InvalidCharset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/Mysqli/Exception/InvalidOption.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/Mysqli/Exception/NonStreamResourceUsedAsLargeObject.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/Mysqli/Exception/StatementError.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/Mysqli/Initializer/Charset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/Mysqli/Initializer/Options.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/Mysqli/Initializer/Secure.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/Mysqli/Initializer.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/Mysqli/Result.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/Mysqli/Statement.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/OCI8/Connection.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/OCI8/ConvertPositionalToNamedPlaceholders.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/OCI8/Driver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/OCI8/Exception/ConnectionFailed.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/OCI8/Exception/Error.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/OCI8/Exception/NonTerminatedStringLiteral.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/OCI8/Exception/SequenceDoesNotExist.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/OCI8/Exception/UnknownParameterIndex.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/OCI8/ExecutionMode.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/OCI8/Result.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/OCI8/Statement.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/PDO/Connection.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/PDO/Exception.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/PDO/MySQL/Driver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/PDO/OCI/Driver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/PDO/PgSQL/Driver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/PDO/Result.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/PDO/SQLSrv/Connection.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/PDO/SQLSrv/Driver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/PDO/SQLSrv/Statement.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/PDO/SQLite/Driver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/PDO/Statement.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/Result.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/SQLSrv/Connection.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/SQLSrv/Driver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/SQLSrv/Exception/Error.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/SQLSrv/Result.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/SQLSrv/Statement.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/ServerInfoAwareConnection.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver/Statement.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Driver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/DriverManager.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Event/ConnectionEventArgs.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Event/Listeners/OracleSessionInit.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Event/Listeners/SQLSessionInit.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Event/SchemaAlterTableAddColumnEventArgs.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Event/SchemaAlterTableChangeColumnEventArgs.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Event/SchemaAlterTableEventArgs.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Event/SchemaAlterTableRemoveColumnEventArgs.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Event/SchemaAlterTableRenameColumnEventArgs.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Event/SchemaColumnDefinitionEventArgs.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Event/SchemaCreateTableColumnEventArgs.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Event/SchemaCreateTableEventArgs.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Event/SchemaDropTableEventArgs.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Event/SchemaEventArgs.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Event/SchemaIndexDefinitionEventArgs.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Events.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Exception/ConnectionException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Exception/ConnectionLost.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Exception/ConstraintViolationException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Exception/DatabaseObjectExistsException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Exception/DatabaseObjectNotFoundException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Exception/DeadlockException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Exception/DriverException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Exception/ForeignKeyConstraintViolationException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Exception/InvalidArgumentException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Exception/InvalidFieldNameException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Exception/InvalidLockMode.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Exception/LockWaitTimeoutException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Exception/NoKeyValue.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Exception/NonUniqueFieldNameException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Exception/NotNullConstraintViolationException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Exception/ReadOnlyException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Exception/RetryableException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Exception/ServerException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Exception/SyntaxErrorException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Exception/TableExistsException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Exception/TableNotFoundException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Exception/UniqueConstraintViolationException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Exception.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/ExpandArrayParameters.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/FetchMode.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Id/TableGenerator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Id/TableGeneratorSchemaVisitor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/LockMode.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Logging/DebugStack.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Logging/LoggerChain.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Logging/SQLLogger.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/ParameterType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Platforms/AbstractPlatform.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Platforms/DB2Platform.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Platforms/DateIntervalUnit.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Platforms/Keywords/DB2Keywords.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Platforms/Keywords/KeywordList.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Platforms/Keywords/MariaDb102Keywords.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Platforms/Keywords/MySQL57Keywords.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Platforms/Keywords/MySQL80Keywords.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Platforms/Keywords/MySQLKeywords.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Platforms/Keywords/OracleKeywords.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Platforms/Keywords/PostgreSQL100Keywords.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Platforms/Keywords/PostgreSQL94Keywords.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Platforms/Keywords/ReservedKeywordsValidator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Platforms/Keywords/SQLServer2012Keywords.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Platforms/Keywords/SQLiteKeywords.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Platforms/MariaDb1027Platform.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Platforms/MySQL57Platform.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Platforms/MySQL80Platform.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Platforms/MySQLPlatform.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Platforms/OraclePlatform.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Platforms/PostgreSQL100Platform.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Platforms/PostgreSQL94Platform.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Platforms/SQLServer2012Platform.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Platforms/SqlitePlatform.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Platforms/TrimMode.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Portability/Connection.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Portability/Converter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Portability/Driver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Portability/Middleware.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Portability/OptimizeFlags.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Portability/Result.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Portability/Statement.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Query/Expression/CompositeExpression.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Query/Expression/ExpressionBuilder.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Query/QueryBuilder.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Query/QueryException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Query.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Result.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/SQL/Parser/Visitor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/SQL/Parser.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Schema/AbstractAsset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Schema/AbstractSchemaManager.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Schema/Column.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Schema/ColumnDiff.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Schema/Comparator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Schema/Constraint.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Schema/DB2SchemaManager.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Schema/Exception/InvalidTableName.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Schema/Exception/UnknownColumnOption.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Schema/ForeignKeyConstraint.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Schema/Identifier.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Schema/Index.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Schema/MySQLSchemaManager.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Schema/OracleSchemaManager.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Schema/PostgreSQLSchemaManager.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Schema/SQLServerSchemaManager.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Schema/Schema.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Schema/SchemaConfig.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Schema/SchemaDiff.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Schema/SchemaException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Schema/Sequence.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Schema/SqliteSchemaManager.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Schema/Table.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Schema/TableDiff.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Schema/UniqueConstraint.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Schema/View.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Schema/Visitor/AbstractVisitor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Schema/Visitor/CreateSchemaSqlCollector.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Schema/Visitor/DropSchemaSqlCollector.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Schema/Visitor/Graphviz.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Schema/Visitor/NamespaceVisitor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Schema/Visitor/RemoveNamespacedAssets.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Schema/Visitor/SchemaDiffVisitor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Schema/Visitor/Visitor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Statement.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Tools/Console/Command/ReservedWordsCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Tools/Console/Command/RunSqlCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Tools/Console/ConnectionNotFound.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Tools/Console/ConnectionProvider/SingleConnectionProvider.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Tools/Console/ConnectionProvider.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Tools/Console/ConsoleRunner.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Tools/Dumper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/TransactionIsolationLevel.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Types/ArrayType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Types/AsciiStringType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Types/BigIntType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Types/BinaryType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Types/BlobType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Types/BooleanType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Types/ConversionException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Types/DateImmutableType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Types/DateIntervalType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Types/DateTimeImmutableType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Types/DateTimeType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Types/DateTimeTzImmutableType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Types/DateTimeTzType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Types/DateType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Types/DecimalType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Types/FloatType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Types/GuidType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Types/IntegerType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Types/JsonType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Types/ObjectType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Types/PhpDateTimeMappingType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Types/PhpIntegerMappingType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Types/SimpleArrayType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Types/SmallIntType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Types/StringType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Types/TextType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Types/TimeImmutableType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Types/TimeType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Types/Type.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Types/TypeRegistry.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Types/Types.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Types/VarDateTimeImmutableType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/Types/VarDateTimeType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/src/VersionAwarePlatformDriver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/dbal/static-analysis/driver-manager-retrieves-correct-connection-type.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/deprecations/.github/workflows/ci.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/deprecations/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/deprecations/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/deprecations/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/deprecations/lib/Doctrine/Deprecations/Deprecation.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/deprecations/lib/Doctrine/Deprecations/PHPUnit/VerifyDeprecations.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/deprecations/phpcs.xml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/deprecations/phpunit.xml.dist", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/deprecations/test_fixtures/src/Foo.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/deprecations/test_fixtures/src/RootDeprecation.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/event-manager/.doctrine-project.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/event-manager/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/event-manager/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/event-manager/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/event-manager/lib/Doctrine/Common/EventArgs.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/event-manager/lib/Doctrine/Common/EventManager.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/event-manager/lib/Doctrine/Common/EventSubscriber.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/CachedWordInflector.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/GenericLanguageInflectorFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/Inflector.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/InflectorFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/Language.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/LanguageInflectorFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/NoopWordInflector.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/Rules/English/Inflectible.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/Rules/English/InflectorFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/Rules/English/Rules.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/Rules/English/Uninflected.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/Rules/French/Inflectible.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/Rules/French/InflectorFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/Rules/French/Rules.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/Rules/French/Uninflected.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/Rules/NorwegianBokmal/Inflectible.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/Rules/NorwegianBokmal/InflectorFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/Rules/NorwegianBokmal/Rules.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/Rules/NorwegianBokmal/Uninflected.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/Rules/Pattern.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/Rules/Patterns.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/Rules/Portuguese/Inflectible.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/Rules/Portuguese/InflectorFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/Rules/Portuguese/Rules.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/Rules/Portuguese/Uninflected.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/Rules/Ruleset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/Rules/Spanish/Inflectible.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/Rules/Spanish/InflectorFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/Rules/Spanish/Rules.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/Rules/Spanish/Uninflected.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/Rules/Substitution.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/Rules/Substitutions.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/Rules/Transformation.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/Rules/Transformations.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/Rules/Turkish/Inflectible.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/Rules/Turkish/InflectorFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/Rules/Turkish/Rules.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/Rules/Turkish/Uninflected.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/Rules/Word.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/RulesetInflector.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/lib/Doctrine/Inflector/WordInflector.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/phpstan.neon.dist", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/inflector/psalm.xml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/instantiator/.doctrine-project.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/instantiator/.github/FUNDING.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/instantiator/.github/workflows/coding-standards.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/instantiator/.github/workflows/continuous-integration.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/instantiator/.github/workflows/phpbench.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/instantiator/.github/workflows/release-on-milestone-closed.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/instantiator/.github/workflows/static-analysis.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/instantiator/CONTRIBUTING.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/instantiator/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/instantiator/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/instantiator/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/instantiator/phpbench.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/instantiator/phpcs.xml.dist", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/instantiator/phpstan.neon.dist", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/instantiator/src/Doctrine/Instantiator/Exception/ExceptionInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/instantiator/src/Doctrine/Instantiator/Exception/InvalidArgumentException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/instantiator/src/Doctrine/Instantiator/Exception/UnexpectedValueException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/instantiator/src/Doctrine/Instantiator/Instantiator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/instantiator/src/Doctrine/Instantiator/InstantiatorInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/lexer/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/lexer/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/lexer/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/lexer/lib/Doctrine/Common/Lexer/AbstractLexer.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/.gitmodules", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/SECURITY.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/UPGRADE.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/bin/doctrine", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/bin/doctrine-pear.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/bin/doctrine.bat", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/bin/doctrine.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/ci/github/phpunit/mysqli.xml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/ci/github/phpunit/pdo_mysql.xml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/ci/github/phpunit/pdo_pgsql.xml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/ci/github/phpunit/sqlite.xml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/doctrine-mapping.xsd", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/AbstractQuery.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/AssociationCacheEntry.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/CacheConfiguration.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/CacheEntry.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/CacheException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/CacheFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/CacheKey.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/CollectionCacheEntry.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/CollectionCacheKey.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/CollectionHydrator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/ConcurrentRegion.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/DefaultCache.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/DefaultCacheFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/DefaultCollectionHydrator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/DefaultEntityHydrator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/DefaultQueryCache.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/EntityCacheEntry.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/EntityCacheKey.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/EntityHydrator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/Exception/CacheException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/Exception/CannotUpdateReadOnlyCollection.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/Exception/CannotUpdateReadOnlyEntity.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/Exception/FeatureNotImplemented.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/Exception/InvalidResultCacheDriver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/Exception/MetadataCacheNotConfigured.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/Exception/MetadataCacheUsesNonPersistentCache.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/Exception/NonCacheableEntity.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/Exception/NonCacheableEntityAssociation.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/Exception/QueryCacheNotConfigured.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/Exception/QueryCacheUsesNonPersistentCache.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/Lock.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/LockException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/Logging/CacheLogger.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/Logging/CacheLoggerChain.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/Logging/StatisticsCacheLogger.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/MultiGetRegion.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/Persister/CachedPersister.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/Persister/Collection/AbstractCollectionPersister.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/Persister/Collection/CachedCollectionPersister.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/Persister/Collection/NonStrictReadWriteCachedCollectionPersister.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/Persister/Collection/ReadOnlyCachedCollectionPersister.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/Persister/Collection/ReadWriteCachedCollectionPersister.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/Persister/Entity/AbstractEntityPersister.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/Persister/Entity/CachedEntityPersister.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/Persister/Entity/NonStrictReadWriteCachedEntityPersister.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/Persister/Entity/ReadOnlyCachedEntityPersister.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/Persister/Entity/ReadWriteCachedEntityPersister.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/QueryCache.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/QueryCacheEntry.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/QueryCacheKey.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/QueryCacheValidator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/Region/DefaultMultiGetRegion.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/Region/DefaultRegion.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/Region/FileLockRegion.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/Region/UpdateTimestampCache.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/Region.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/RegionsConfiguration.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/TimestampCacheEntry.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/TimestampCacheKey.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/TimestampQueryCacheValidator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache/TimestampRegion.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Cache.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Configuration.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Decorator/EntityManagerDecorator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/EntityManager.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/EntityManagerInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/EntityNotFoundException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/EntityRepository.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Event/LifecycleEventArgs.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Event/ListenersInvoker.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Event/LoadClassMetadataEventArgs.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Event/OnClassMetadataNotFoundEventArgs.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Event/OnClearEventArgs.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Event/OnFlushEventArgs.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Event/PostFlushEventArgs.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Event/PreFlushEventArgs.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Event/PreUpdateEventArgs.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Events.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Exception/ConfigurationException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Exception/EntityManagerClosed.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Exception/EntityMissingAssignedId.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Exception/InvalidEntityRepository.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Exception/InvalidHydrationMode.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Exception/ManagerException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Exception/MismatchedEventManager.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Exception/MissingIdentifierField.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Exception/MissingMappingDriverImplementation.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Exception/MultipleSelectorsFoundException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Exception/NamedNativeQueryNotFound.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Exception/NamedQueryNotFound.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Exception/NotSupported.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Exception/ORMException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Exception/PersisterException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Exception/ProxyClassesAlwaysRegenerating.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Exception/RepositoryException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Exception/SchemaToolException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Exception/UnexpectedAssociationValue.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Exception/UnknownEntityNamespace.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Exception/UnrecognizedIdentifierFields.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Id/AbstractIdGenerator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Id/AssignedGenerator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Id/BigIntegerIdentityGenerator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Id/IdentityGenerator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Id/SequenceGenerator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Id/TableGenerator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Id/UuidGenerator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Internal/CommitOrderCalculator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Internal/Hydration/AbstractHydrator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Internal/Hydration/ArrayHydrator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Internal/Hydration/HydrationException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Internal/Hydration/IterableResult.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Internal/Hydration/ObjectHydrator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Internal/Hydration/ScalarColumnHydrator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Internal/Hydration/ScalarHydrator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Internal/Hydration/SimpleObjectHydrator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Internal/Hydration/SingleScalarHydrator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Internal/HydrationCompleteHandler.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Internal/SQLResultCasing.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/LazyCriteriaCollection.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/Annotation.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/AnsiQuoteStrategy.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/AssociationOverride.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/AssociationOverrides.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/AttributeOverride.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/AttributeOverrides.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/Builder/AssociationBuilder.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/Builder/ClassMetadataBuilder.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/Builder/EmbeddedBuilder.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/Builder/EntityListenerBuilder.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/Builder/FieldBuilder.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/Builder/ManyToManyAssociationBuilder.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/Builder/OneToManyAssociationBuilder.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/Cache.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/ChangeTrackingPolicy.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/ClassMetadata.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/ClassMetadataFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/ClassMetadataInfo.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/Column.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/ColumnResult.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/CustomIdGenerator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/DefaultEntityListenerResolver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/DefaultNamingStrategy.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/DefaultQuoteStrategy.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/DiscriminatorColumn.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/DiscriminatorMap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/Driver/AnnotationDriver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/Driver/AttributeDriver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/Driver/AttributeReader.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/Driver/DatabaseDriver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/Driver/DoctrineAnnotations.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/Driver/DriverChain.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/Driver/PHPDriver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/Driver/RepeatableAttributeCollection.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/Driver/SimplifiedXmlDriver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/Driver/SimplifiedYamlDriver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/Driver/StaticPHPDriver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/Driver/XmlDriver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/Driver/YamlDriver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/Embeddable.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/Embedded.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/Entity.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/EntityListenerResolver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/EntityListeners.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/EntityResult.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/Exception/CannotGenerateIds.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/Exception/InvalidCustomGenerator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/Exception/UnknownGeneratorType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/FieldResult.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/GeneratedValue.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/HasLifecycleCallbacks.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/Id.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/Index.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/InheritanceType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/InverseJoinColumn.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/JoinColumn.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/JoinColumns.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/JoinTable.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/ManyToMany.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/ManyToOne.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/MappedSuperclass.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/MappingException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/NamedNativeQueries.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/NamedNativeQuery.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/NamedQueries.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/NamedQuery.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/NamingStrategy.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/OneToMany.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/OneToOne.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/OrderBy.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/PostLoad.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/PostPersist.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/PostRemove.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/PostUpdate.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/PreFlush.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/PrePersist.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/PreRemove.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/PreUpdate.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/QuoteStrategy.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/Reflection/ReflectionPropertiesGetter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/ReflectionEmbeddedProperty.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/SequenceGenerator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/SqlResultSetMapping.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/SqlResultSetMappings.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/Table.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/UnderscoreNamingStrategy.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/UniqueConstraint.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Mapping/Version.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/NativeQuery.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/NoResultException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/NonUniqueResultException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/ORMException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/ORMInvalidArgumentException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/OptimisticLockException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/PersistentCollection.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Persisters/Collection/AbstractCollectionPersister.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Persisters/Collection/CollectionPersister.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Persisters/Collection/ManyToManyPersister.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Persisters/Collection/OneToManyPersister.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Persisters/Entity/AbstractEntityInheritancePersister.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Persisters/Entity/BasicEntityPersister.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Persisters/Entity/CachedPersisterContext.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Persisters/Entity/EntityPersister.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Persisters/Entity/JoinedSubclassPersister.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Persisters/Entity/SingleTablePersister.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Persisters/Exception/CantUseInOperatorOnCompositeKeys.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Persisters/Exception/InvalidOrientation.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Persisters/Exception/UnrecognizedField.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Persisters/MatchingAssociationFieldRequiresObject.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Persisters/PersisterException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Persisters/SqlExpressionVisitor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Persisters/SqlValueVisitor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/PessimisticLockException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Proxy/Autoloader.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Proxy/Proxy.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Proxy/ProxyFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/ASTException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/AggregateExpression.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/ArithmeticExpression.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/ArithmeticFactor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/ArithmeticTerm.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/BetweenExpression.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/CoalesceExpression.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/CollectionMemberExpression.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/ComparisonExpression.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/ConditionalExpression.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/ConditionalFactor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/ConditionalPrimary.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/ConditionalTerm.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/DeleteClause.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/DeleteStatement.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/EmptyCollectionComparisonExpression.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/ExistsExpression.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/FromClause.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/Functions/AbsFunction.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/Functions/AvgFunction.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/Functions/BitAndFunction.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/Functions/BitOrFunction.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/Functions/ConcatFunction.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/Functions/CountFunction.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/Functions/CurrentDateFunction.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/Functions/CurrentTimeFunction.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/Functions/CurrentTimestampFunction.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/Functions/DateAddFunction.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/Functions/DateDiffFunction.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/Functions/DateSubFunction.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/Functions/FunctionNode.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/Functions/IdentityFunction.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/Functions/LengthFunction.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/Functions/LocateFunction.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/Functions/LowerFunction.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/Functions/MaxFunction.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/Functions/MinFunction.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/Functions/ModFunction.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/Functions/SizeFunction.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/Functions/SqrtFunction.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/Functions/SubstringFunction.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/Functions/SumFunction.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/Functions/TrimFunction.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/Functions/UpperFunction.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/GeneralCaseExpression.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/GroupByClause.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/HavingClause.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/IdentificationVariableDeclaration.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/InExpression.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/IndexBy.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/InputParameter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/InstanceOfExpression.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/Join.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/JoinAssociationDeclaration.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/JoinAssociationPathExpression.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/JoinClassPathExpression.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/JoinVariableDeclaration.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/LikeExpression.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/Literal.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/NewObjectExpression.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/Node.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/NullComparisonExpression.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/NullIfExpression.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/OrderByClause.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/OrderByItem.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/ParenthesisExpression.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/PartialObjectExpression.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/PathExpression.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/QuantifiedExpression.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/RangeVariableDeclaration.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/SelectClause.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/SelectExpression.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/SelectStatement.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/SimpleArithmeticExpression.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/SimpleCaseExpression.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/SimpleSelectClause.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/SimpleSelectExpression.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/SimpleWhenClause.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/Subselect.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/SubselectFromClause.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/SubselectIdentificationVariableDeclaration.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/TypedExpression.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/UpdateClause.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/UpdateItem.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/UpdateStatement.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/WhenClause.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/AST/WhereClause.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/Exec/AbstractSqlExecutor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/Exec/MultiTableDeleteExecutor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/Exec/MultiTableUpdateExecutor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/Exec/SingleSelectExecutor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/Exec/SingleTableDeleteUpdateExecutor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/Expr/Andx.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/Expr/Base.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/Expr/Comparison.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/Expr/Composite.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/Expr/From.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/Expr/Func.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/Expr/GroupBy.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/Expr/Join.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/Expr/Literal.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/Expr/Math.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/Expr/OrderBy.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/Expr/Orx.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/Expr/Select.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/Expr.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/Filter/FilterException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/Filter/SQLFilter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/FilterCollection.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/Lexer.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/Parameter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/ParameterTypeInferer.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/Parser.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/ParserResult.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/Printer.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/QueryException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/QueryExpressionVisitor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/ResultSetMapping.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/ResultSetMappingBuilder.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/SqlWalker.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/TreeWalker.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/TreeWalkerAdapter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/TreeWalkerChain.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query/TreeWalkerChainIterator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Query.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/QueryBuilder.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Repository/DefaultRepositoryFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Repository/Exception/InvalidFindByCall.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Repository/Exception/InvalidMagicMethodCall.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Repository/RepositoryFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/AttachEntityListenersListener.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Console/Command/AbstractEntityManagerCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Console/Command/ClearCache/CollectionRegionCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Console/Command/ClearCache/EntityRegionCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Console/Command/ClearCache/MetadataCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Console/Command/ClearCache/QueryCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Console/Command/ClearCache/QueryRegionCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Console/Command/ClearCache/ResultCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Console/Command/ConvertDoctrine1SchemaCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Console/Command/ConvertMappingCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Console/Command/EnsureProductionSettingsCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Console/Command/GenerateEntitiesCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Console/Command/GenerateProxiesCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Console/Command/GenerateRepositoriesCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Console/Command/InfoCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Console/Command/MappingDescribeCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Console/Command/RunDqlCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Console/Command/SchemaTool/AbstractCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Console/Command/SchemaTool/CreateCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Console/Command/SchemaTool/DropCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Console/Command/SchemaTool/UpdateCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Console/Command/ValidateSchemaCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Console/ConsoleRunner.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Console/EntityManagerProvider/ConnectionFromManagerProvider.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Console/EntityManagerProvider/HelperSetManagerProvider.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Console/EntityManagerProvider/SingleManagerProvider.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Console/EntityManagerProvider/UnknownManagerException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Console/EntityManagerProvider.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Console/Helper/EntityManagerHelper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Console/MetadataFilter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/ConvertDoctrine1Schema.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/DebugUnitOfWorkListener.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/DisconnectedClassMetadataFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/EntityGenerator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/EntityRepositoryGenerator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Event/GenerateSchemaEventArgs.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Event/GenerateSchemaTableEventArgs.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Exception/MissingColumnException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Exception/NotSupported.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Export/ClassMetadataExporter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Export/Driver/AbstractExporter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Export/Driver/AnnotationExporter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Export/Driver/PhpExporter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Export/Driver/XmlExporter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Export/Driver/YamlExporter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Export/ExportException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Pagination/CountOutputWalker.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Pagination/CountWalker.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Pagination/Exception/RowNumberOverFunctionNotEnabled.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Pagination/LimitSubqueryOutputWalker.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Pagination/LimitSubqueryWalker.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Pagination/Paginator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Pagination/RowNumberOverFunction.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Pagination/WhereInWalker.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/ResolveTargetEntityListener.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/SchemaTool.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/SchemaValidator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/Setup.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/ToolEvents.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Tools/ToolsException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/TransactionRequiredException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/UnexpectedResultException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/UnitOfWork.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Utility/HierarchyDiscriminatorResolver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Utility/IdentifierFlattener.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Utility/PersisterHelper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/lib/Doctrine/ORM/Version.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/phpstan-dbal2.neon", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/orm/phpstan-params.neon", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/CONTRIBUTING.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/UPGRADE-1.2.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/UPGRADE-2.2.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Common/Persistence/PersistentObject.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/AbstractManagerRegistry.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/ConnectionRegistry.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/Event/LifecycleEventArgs.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/Event/LoadClassMetadataEventArgs.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/Event/ManagerEventArgs.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/Event/OnClearEventArgs.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/Event/PreUpdateEventArgs.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/ManagerRegistry.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/Mapping/AbstractClassMetadataFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/Mapping/ClassMetadata.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/Mapping/ClassMetadataFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/Mapping/Driver/AnnotationDriver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/Mapping/Driver/DefaultFileLocator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/Mapping/Driver/FileDriver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/Mapping/Driver/FileLocator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/Mapping/Driver/MappingDriver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/Mapping/Driver/MappingDriverChain.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/Mapping/Driver/PHPDriver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/Mapping/Driver/StaticPHPDriver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/Mapping/Driver/SymfonyFileLocator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/Mapping/MappingException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/Mapping/ProxyClassNameResolver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/Mapping/ReflectionService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/Mapping/RuntimeReflectionService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/Mapping/StaticReflectionService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/NotifyPropertyChanged.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/ObjectManager.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/ObjectManagerAware.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/ObjectManagerDecorator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/ObjectRepository.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/PropertyChangedListener.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/Proxy.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/Reflection/RuntimePublicReflectionProperty.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/Reflection/TypedNoDefaultReflectionProperty.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/Reflection/TypedNoDefaultReflectionPropertyBase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/doctrine/persistence/lib/Doctrine/Persistence/Reflection/TypedNoDefaultRuntimePublicReflectionProperty.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/psr/cache/CHANGELOG.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/psr/cache/LICENSE.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/psr/cache/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/psr/cache/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/psr/cache/src/CacheException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/psr/cache/src/CacheItemInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/psr/cache/src/CacheItemPoolInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/psr/cache/src/InvalidArgumentException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/psr/container/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/psr/container/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/psr/container/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/psr/container/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/psr/container/src/ContainerExceptionInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/psr/container/src/ContainerInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/psr/container/src/NotFoundExceptionInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/psr/log/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/psr/log/Psr/Log/AbstractLogger.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/psr/log/Psr/Log/InvalidArgumentException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/psr/log/Psr/Log/LogLevel.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/psr/log/Psr/Log/LoggerAwareInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/psr/log/Psr/Log/LoggerAwareTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/psr/log/Psr/Log/LoggerInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/psr/log/Psr/Log/LoggerTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/psr/log/Psr/Log/NullLogger.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/psr/log/Psr/Log/Test/DummyTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/psr/log/Psr/Log/Test/LoggerInterfaceTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/psr/log/Psr/Log/Test/TestLogger.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/psr/log/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/psr/log/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Adapter/AbstractAdapter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Adapter/AbstractTagAwareAdapter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Adapter/AdapterInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Adapter/ApcuAdapter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Adapter/ArrayAdapter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Adapter/ChainAdapter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Adapter/CouchbaseBucketAdapter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Adapter/DoctrineAdapter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Adapter/FilesystemAdapter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Adapter/FilesystemTagAwareAdapter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Adapter/MemcachedAdapter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Adapter/NullAdapter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Adapter/ParameterNormalizer.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Adapter/PdoAdapter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Adapter/PhpArrayAdapter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Adapter/PhpFilesAdapter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Adapter/ProxyAdapter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Adapter/Psr16Adapter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Adapter/RedisAdapter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Adapter/RedisTagAwareAdapter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Adapter/TagAwareAdapter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Adapter/TagAwareAdapterInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Adapter/TraceableAdapter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Adapter/TraceableTagAwareAdapter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/CHANGELOG.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/CacheItem.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/DataCollector/CacheDataCollector.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/DependencyInjection/CacheCollectorPass.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/DependencyInjection/CachePoolClearerPass.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/DependencyInjection/CachePoolPass.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/DependencyInjection/CachePoolPrunerPass.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/DoctrineProvider.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Exception/CacheException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Exception/InvalidArgumentException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Exception/LogicException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/LockRegistry.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Marshaller/DefaultMarshaller.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Marshaller/DeflateMarshaller.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Marshaller/MarshallerInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Marshaller/SodiumMarshaller.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Marshaller/TagAwareMarshaller.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Messenger/EarlyExpirationDispatcher.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Messenger/EarlyExpirationHandler.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Messenger/EarlyExpirationMessage.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/PruneableInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Psr16Cache.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/ResettableInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Traits/AbstractAdapterTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Traits/ContractsTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Traits/FilesystemCommonTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Traits/FilesystemTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Traits/ProxyTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Traits/RedisClusterNodeProxy.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Traits/RedisClusterProxy.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Traits/RedisProxy.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/Traits/RedisTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache-contracts/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache-contracts/CHANGELOG.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache-contracts/CacheInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache-contracts/CacheTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache-contracts/CallbackInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache-contracts/ItemInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache-contracts/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache-contracts/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache-contracts/TagAwareCacheInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/cache-contracts/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Application.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Attribute/AsCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/CHANGELOG.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/CI/GithubActionReporter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Color.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Command/Command.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Command/HelpCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Command/LazyCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Command/ListCommand.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Command/LockableTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Command/SignalableCommandInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/CommandLoader/CommandLoaderInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/CommandLoader/ContainerCommandLoader.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/CommandLoader/FactoryCommandLoader.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/ConsoleEvents.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Cursor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/DependencyInjection/AddConsoleCommandPass.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Descriptor/ApplicationDescription.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Descriptor/Descriptor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Descriptor/DescriptorInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Descriptor/JsonDescriptor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Descriptor/MarkdownDescriptor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Descriptor/TextDescriptor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Descriptor/XmlDescriptor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Event/ConsoleCommandEvent.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Event/ConsoleErrorEvent.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Event/ConsoleEvent.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Event/ConsoleSignalEvent.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Event/ConsoleTerminateEvent.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/EventListener/ErrorListener.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Exception/CommandNotFoundException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Exception/ExceptionInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Exception/InvalidArgumentException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Exception/InvalidOptionException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Exception/LogicException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Exception/MissingInputException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Exception/NamespaceNotFoundException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Exception/RuntimeException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Formatter/NullOutputFormatter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Formatter/NullOutputFormatterStyle.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Formatter/OutputFormatter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Formatter/OutputFormatterInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Formatter/OutputFormatterStyle.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Formatter/OutputFormatterStyleInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Formatter/OutputFormatterStyleStack.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Formatter/WrappableOutputFormatterInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Helper/DebugFormatterHelper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Helper/DescriptorHelper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Helper/Dumper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Helper/FormatterHelper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Helper/Helper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Helper/HelperInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Helper/HelperSet.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Helper/InputAwareHelper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Helper/ProcessHelper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Helper/ProgressBar.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Helper/ProgressIndicator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Helper/QuestionHelper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Helper/SymfonyQuestionHelper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Helper/Table.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Helper/TableCell.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Helper/TableCellStyle.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Helper/TableRows.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Helper/TableSeparator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Helper/TableStyle.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Input/ArgvInput.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Input/ArrayInput.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Input/Input.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Input/InputArgument.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Input/InputAwareInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Input/InputDefinition.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Input/InputInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Input/InputOption.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Input/StreamableInputInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Input/StringInput.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Logger/ConsoleLogger.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Output/BufferedOutput.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Output/ConsoleOutput.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Output/ConsoleOutputInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Output/ConsoleSectionOutput.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Output/NullOutput.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Output/Output.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Output/OutputInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Output/StreamOutput.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Output/TrimmedBufferOutput.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Question/ChoiceQuestion.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Question/ConfirmationQuestion.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Question/Question.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Resources/bin/hiddeninput.exe", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/SignalRegistry/SignalRegistry.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/SingleCommandApplication.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Style/OutputStyle.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Style/StyleInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Style/SymfonyStyle.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Terminal.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Tester/ApplicationTester.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Tester/CommandTester.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/Tester/TesterTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/console/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/deprecation-contracts/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/deprecation-contracts/CHANGELOG.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/deprecation-contracts/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/deprecation-contracts/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/deprecation-contracts/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/deprecation-contracts/function.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-ctype/Ctype.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-ctype/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-ctype/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-ctype/bootstrap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-ctype/bootstrap80.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-ctype/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-intl-grapheme/Grapheme.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-intl-grapheme/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-intl-grapheme/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-intl-grapheme/bootstrap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-intl-grapheme/bootstrap80.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-intl-grapheme/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-intl-normalizer/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-intl-normalizer/Normalizer.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-intl-normalizer/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-intl-normalizer/Resources/stubs/Normalizer.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-intl-normalizer/Resources/unidata/canonicalComposition.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-intl-normalizer/Resources/unidata/canonicalDecomposition.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-intl-normalizer/Resources/unidata/combiningClass.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-intl-normalizer/Resources/unidata/compatibilityDecomposition.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-intl-normalizer/bootstrap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-intl-normalizer/bootstrap80.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-intl-normalizer/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-mbstring/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-mbstring/Mbstring.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-mbstring/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-mbstring/Resources/unidata/lowerCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-mbstring/Resources/unidata/titleCaseRegexp.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-mbstring/Resources/unidata/upperCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-mbstring/bootstrap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-mbstring/bootstrap80.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-mbstring/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-php72/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-php72/Php72.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-php72/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-php72/bootstrap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-php72/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-php73/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-php73/Php73.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-php73/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-php73/Resources/stubs/JsonException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-php73/bootstrap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-php73/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-php80/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-php80/Php80.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-php80/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-php80/Resources/stubs/Attribute.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-php80/Resources/stubs/Stringable.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-php80/Resources/stubs/UnhandledMatchError.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-php80/Resources/stubs/ValueError.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-php80/bootstrap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/polyfill-php80/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/service-contracts/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/service-contracts/Attribute/Required.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/service-contracts/CHANGELOG.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/service-contracts/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/service-contracts/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/service-contracts/ResetInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/service-contracts/ServiceLocatorTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/service-contracts/ServiceProviderInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/service-contracts/ServiceSubscriberInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/service-contracts/ServiceSubscriberTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/service-contracts/Test/ServiceLocatorTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/service-contracts/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/string/AbstractString.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/string/AbstractUnicodeString.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/string/ByteString.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/string/CHANGELOG.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/string/CodePointString.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/string/Exception/ExceptionInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/string/Exception/InvalidArgumentException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/string/Exception/RuntimeException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/string/Inflector/EnglishInflector.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/string/Inflector/FrenchInflector.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/string/Inflector/InflectorInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/string/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/string/LazyString.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/string/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/string/Resources/data/wcswidth_table_wide.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/string/Resources/data/wcswidth_table_zero.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/string/Resources/functions.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/string/Slugger/AsciiSlugger.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/string/Slugger/SluggerInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/string/UnicodeString.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/string/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/var-exporter/CHANGELOG.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/var-exporter/Exception/ClassNotFoundException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/var-exporter/Exception/ExceptionInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/var-exporter/Exception/NotInstantiableTypeException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/var-exporter/Instantiator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/var-exporter/Internal/Exporter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/var-exporter/Internal/Hydrator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/var-exporter/Internal/Reference.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/var-exporter/Internal/Registry.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/var-exporter/Internal/Values.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/var-exporter/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/var-exporter/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/var-exporter/VarExporter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/vendor/symfony/var-exporter/composer.json", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.5889739990234375, "profiling_times": {"config_time": 5.9350197315216064, "core_time": 4.853650331497192, "ignores_time": 0.002113819122314453, "total_time": 10.7916579246521}, "parsing_time": {"total_time": 1.1978728771209717, "per_file_time": {"mean": 0.011091415528897886, "std_dev": 0.0005971816768696683}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 8.150662183761597, "per_file_time": {"mean": 0.018440412180456098, "std_dev": 0.008856310862702289}, "very_slow_stats": {"time_ratio": 0.19400029631705398, "count_ratio": 0.0022624434389140274}, "very_slow_files": [{"fpath": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Deps/class-tgm-plugin-activation.php", "ftime": 1.581230878829956}]}, "matching_time": {"total_time": 3.2877678871154785, "per_file_and_rule_time": {"mean": 0.00755808709681719, "std_dev": 0.00041426018601771675}, "very_slow_stats": {"time_ratio": 0.1963635989185707, "count_ratio": 0.009195402298850575}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Deps/class-tgm-plugin-activation.php", "rule_id": "php.lang.security.injection.tainted-exec.tainted-exec", "time": 0.10709500312805176}, {"fpath": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Hooks/User/User.php", "rule_id": "php.laravel.security.laravel-sql-injection.laravel-sql-injection", "time": 0.10882902145385742}, {"fpath": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Deps/class-tgm-plugin-activation.php", "rule_id": "php.lang.security.injection.tainted-callable.tainted-callable", "time": 0.17463994026184082}, {"fpath": "downloaded_repos/azerothcore_acore-cms/src/acore-wp-plugin/src/Deps/class-tgm-plugin-activation.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.2550339698791504}]}, "tainting_time": {"total_time": 1.518324613571167, "per_def_and_rule_time": {"mean": 0.0007243915141083812, "std_dev": 4.897130476908899e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}