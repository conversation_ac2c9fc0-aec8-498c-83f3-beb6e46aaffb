{"version": "1.130.0", "results": [{"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/models.py", "start": {"line": 183, "col": 16, "offset": 6225}, "end": {"line": 184, "col": 120, "offset": 6355}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/chooser/results.html", "start": {"line": 49, "col": 13, "offset": 1781}, "end": {"line": 49, "col": 37, "offset": 1805}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/homepage/videos_summary.html", "start": {"line": 6, "col": 9, "offset": 123}, "end": {"line": 6, "col": 96, "offset": 210}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/multiple/add.html", "start": {"line": 18, "col": 13, "offset": 581}, "end": {"line": 36, "col": 20, "offset": 1735}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-translate-as-no-escape.template-translate-as-no-escape", "path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/add.html", "start": {"line": 3, "col": 21, "offset": 75}, "end": {"line": 11, "col": 63, "offset": 334}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-translate-as-no-escape.template-translate-as-no-escape", "shortlink": "https://sg.run/PJDz"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/add.html", "start": {"line": 22, "col": 3, "offset": 547}, "end": {"line": 34, "col": 10, "offset": 982}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/edit.html", "start": {"line": 5, "col": 21, "offset": 121}, "end": {"line": 5, "col": 60, "offset": 160}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/edit.html", "start": {"line": 18, "col": 9, "offset": 609}, "end": {"line": 37, "col": 16, "offset": 1653}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/edit.html", "start": {"line": 58, "col": 57, "offset": 2672}, "end": {"line": 58, "col": 113, "offset": 2728}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/edit.html", "start": {"line": 74, "col": 25, "offset": 3579}, "end": {"line": 74, "col": 108, "offset": 3662}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/results.html", "start": {"line": 30, "col": 13, "offset": 1004}, "end": {"line": 30, "col": 29, "offset": 1020}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/results.html", "start": {"line": 35, "col": 16, "offset": 1268}, "end": {"line": 35, "col": 32, "offset": 1284}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/results.html", "start": {"line": 37, "col": 16, "offset": 1461}, "end": {"line": 37, "col": 32, "offset": 1477}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/usage.html", "start": {"line": 3, "col": 21, "offset": 75}, "end": {"line": 3, "col": 60, "offset": 114}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templatetags/wagtailvideos_tags.py", "start": {"line": 43, "col": 12, "offset": 1277}, "end": {"line": 43, "col": 59, "offset": 1324}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/chooser/chooser.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 24, "offset": 117}}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/chooser/chooser.html", "start": {"line": 28, "col": 1, "offset": 0}, "end": {"line": 28, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/chooser/chooser.html:1:\n `{% extends \"wagtailadmin/generic/chooser/chooser.html\" %}\n\n{% load i18n wagtailadmin_tags %}\n\n{% block filter_form %}` was unexpected", "path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/chooser/chooser.html", "spans": [{"file": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/chooser/chooser.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 24, "offset": 117}}, {"file": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/chooser/chooser.html", "start": {"line": 28, "col": 1, "offset": 0}, "end": {"line": 28, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/chooser/results.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 26, "offset": 118}}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/chooser/results.html", "start": {"line": 4, "col": 62, "offset": 0}, "end": {"line": 6, "col": 28, "offset": 43}}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/chooser/results.html", "start": {"line": 39, "col": 1, "offset": 0}, "end": {"line": 41, "col": 29, "offset": 44}}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/chooser/results.html", "start": {"line": 54, "col": 1, "offset": 0}, "end": {"line": 54, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/chooser/results.html:1:\n `{% extends \"wagtailadmin/generic/chooser/results.html\" %}\n{% load i18n wagtailadmin_tags %}\n\n{% block listing_title %}` was unexpected", "path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/chooser/results.html", "spans": [{"file": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/chooser/results.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 26, "offset": 118}}, {"file": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/chooser/results.html", "start": {"line": 4, "col": 62, "offset": 0}, "end": {"line": 6, "col": 28, "offset": 43}}, {"file": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/chooser/results.html", "start": {"line": 39, "col": 1, "offset": 0}, "end": {"line": 41, "col": 29, "offset": 44}}, {"file": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/chooser/results.html", "start": {"line": 54, "col": 1, "offset": 0}, "end": {"line": 54, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/homepage/videos_summary.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}]], "message": "Syntax error at line downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/homepage/videos_summary.html:1:\n `{% load i18n wagtailadmin_tags %}` was unexpected", "path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/homepage/videos_summary.html", "spans": [{"file": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/homepage/videos_summary.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 34, "offset": 33}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/_file_field.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 23, "offset": 85}}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/_file_field.html", "start": {"line": 6, "col": 5, "offset": 0}, "end": {"line": 8, "col": 15, "offset": 63}}]], "message": "Syntax error at line downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/_file_field.html:1:\n `{% extends \"wagtailadmin/shared/field.html\" %}\n{% load i18n %}\n{% block form_field %}` was unexpected", "path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/_file_field.html", "spans": [{"file": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/_file_field.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 23, "offset": 85}}, {"file": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/_file_field.html", "start": {"line": 6, "col": 5, "offset": 0}, "end": {"line": 8, "col": 15, "offset": 63}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/edit.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 26, "col": 29, "offset": 1090}}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/edit.html", "start": {"line": 28, "col": 19, "offset": 0}, "end": {"line": 29, "col": 29, "offset": 40}}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/edit.html", "start": {"line": 36, "col": 13, "offset": 0}, "end": {"line": 38, "col": 11, "offset": 32}}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/edit.html", "start": {"line": 132, "col": 1, "offset": 0}, "end": {"line": 133, "col": 15, "offset": 21}}]], "message": "Syntax error at line downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/edit.html:1:\n `{% extends \"wagtailadmin/base.html\" %}\n\n{% load static wagtailadmin_tags i18n wagtailvideos_tags %}\n\n{% block titletag %}{% blocktrans with title=video.title %}Editing video {{ title }}{% endblocktrans %}{% endblock %}\n\n{% block extra_css %}\n{{ block.super }}\n<link rel=\"stylesheet\" href=\"{% static 'wagtailvideos/css/edit-video.css' %}\" type=\"text/css\" /> {% endblock %}\n\n{% block content %}\n{% trans \"Editing\" as editing_str %}\n\n{% include \"wagtailadmin/shared/header.html\" with title=editing_str subtitle=video.title icon=\"media\" %}\n\n<div class=\"row row-flush nice-padding\">\n    <div class=\"col6\">\n        <form action=\"{% url 'wagtailvideos:edit' video.id %}\" method=\"POST\" enctype=\"multipart/form-data\">\n            {% csrf_token %}\n            <ul class=\"fields\">\n                {% for field in form %}\n                  {% if field.name == 'file' %}\n                    {% include \"wagtailvideos/videos/_file_field_as_li.html\"  with li_classes=\"label-above label-uppercase\" %}\n                  {% elif field.is_hidden %}\n                    {{ field }}\n                  {% else %}` was unexpected", "path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/edit.html", "spans": [{"file": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/edit.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 26, "col": 29, "offset": 1090}}, {"file": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/edit.html", "start": {"line": 28, "col": 19, "offset": 0}, "end": {"line": 29, "col": 29, "offset": 40}}, {"file": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/edit.html", "start": {"line": 36, "col": 13, "offset": 0}, "end": {"line": 38, "col": 11, "offset": 32}}, {"file": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/edit.html", "start": {"line": 132, "col": 1, "offset": 0}, "end": {"line": 133, "col": 15, "offset": 21}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/results.html", "start": {"line": 2, "col": 1, "offset": 0}, "end": {"line": 6, "col": 20, "offset": 121}}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/results.html", "start": {"line": 25, "col": 1, "offset": 0}, "end": {"line": 29, "col": 26, "offset": 77}}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/results.html", "start": {"line": 31, "col": 9, "offset": 0}, "end": {"line": 34, "col": 36, "offset": 147}}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/results.html", "start": {"line": 36, "col": 9, "offset": 0}, "end": {"line": 36, "col": 19, "offset": 10}}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/results.html", "start": {"line": 38, "col": 9, "offset": 0}, "end": {"line": 40, "col": 15, "offset": 42}}]], "message": "Syntax error at line downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/results.html:2:\n `{% extends \"wagtailadmin/generic/index_results.html\" %}\n{% load wagtailadmin_tags %}\n{% load i18n %}\n\n{% block results %}` was unexpected", "path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/results.html", "spans": [{"file": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/results.html", "start": {"line": 2, "col": 1, "offset": 0}, "end": {"line": 6, "col": 20, "offset": 121}}, {"file": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/results.html", "start": {"line": 25, "col": 1, "offset": 0}, "end": {"line": 29, "col": 26, "offset": 77}}, {"file": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/results.html", "start": {"line": 31, "col": 9, "offset": 0}, "end": {"line": 34, "col": 36, "offset": 147}}, {"file": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/results.html", "start": {"line": 36, "col": 9, "offset": 0}, "end": {"line": 36, "col": 19, "offset": 10}}, {"file": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/results.html", "start": {"line": 38, "col": 9, "offset": 0}, "end": {"line": 40, "col": 15, "offset": 42}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/usage.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 13, "col": 31, "offset": 498}}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/usage.html", "start": {"line": 42, "col": 9, "offset": 0}, "end": {"line": 45, "col": 15, "offset": 125}}]], "message": "Syntax error at line downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/usage.html:1:\n `{% extends \"wagtailadmin/base.html\" %}\n{% load i18n %}\n{% block titletag %}{% blocktrans with title=video.title %}Usage of {{ title }}{% endblocktrans %}{% endblock %}\n{% block content %}\n    {% trans \"Usage of\" as usage_str %}\n    {% include \"wagtailadmin/shared/header.html\" with title=usage_str subtitle=video.title %}\n\n    <div class=\"nice-padding\">\n        <table class=\"listing\">\n            <col />\n            <col width=\"30%\"/>\n            <col width=\"15%\"/>\n            <col width=\"15%\"/>` was unexpected", "path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/usage.html", "spans": [{"file": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/usage.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 13, "col": 31, "offset": 498}}, {"file": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/usage.html", "start": {"line": 42, "col": 9, "offset": 0}, "end": {"line": 45, "col": 15, "offset": 125}}]}], "paths": {"scanned": ["downloaded_repos/neon-jungle_wagtail-videos/.flake8", "downloaded_repos/neon-jungle_wagtail-videos/.gitignore", "downloaded_repos/neon-jungle_wagtail-videos/.gitlab-ci.yml", "downloaded_repos/neon-jungle_wagtail-videos/CHANGELOG.rst", "downloaded_repos/neon-jungle_wagtail-videos/CONTRIBUTORS", "downloaded_repos/neon-jungle_wagtail-videos/LICENSE", "downloaded_repos/neon-jungle_wagtail-videos/MANIFEST.in", "downloaded_repos/neon-jungle_wagtail-videos/README.rst", "downloaded_repos/neon-jungle_wagtail-videos/manage.py", "downloaded_repos/neon-jungle_wagtail-videos/runtests.py", "downloaded_repos/neon-jungle_wagtail-videos/settings.py", "downloaded_repos/neon-jungle_wagtail-videos/setup.cfg", "downloaded_repos/neon-jungle_wagtail-videos/setup.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/__init__.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/apps.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/blocks.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/edit_handlers.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/enums.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/fields.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/forms.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/jinja2tags.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/locale/ru_RU/LC_MESSAGES/django.mo", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/locale/ru_RU/LC_MESSAGES/django.po", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/migrations/0001_initial.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/migrations/0002_auto_20160321_1610.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/migrations/0003_auto_20160705_1646.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/migrations/0004_auto_20160706_1153.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/migrations/0005_videotranscode_error_message.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/migrations/0006_auto_20160707_1413.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/migrations/0007_video_duration.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/migrations/0008_auto_20160728_1523.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/migrations/0009_videotranscode_quality.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/migrations/0010_video_ordering.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/migrations/0011_video_tracks.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/migrations/0012_remove_unique_constraint.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/migrations/0013_add_choose_permissions.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/migrations/0014_alter_videotrack_file_alter_videotrack_kind_and_more.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/migrations/0015_video_height_video_width.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/migrations/__init__.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/models.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/permissions.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/signals.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/static/wagtailvideos/css/edit-video.css", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/static/wagtailvideos/css/summary-override.css", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/static/wagtailvideos/js/add-multiple.js", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/static/wagtailvideos/js/video-chooser-telepath.js", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/static/wagtailvideos/js/video-chooser.js", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/chooser/chooser.html", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/chooser/results.html", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/homepage/videos_summary.html", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/multiple/add.html", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/multiple/edit_form.html", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/permissions/includes/video_permissions_formset.html", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/_file_field.html", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/_file_field_as_li.html", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/add.html", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/confirm_delete.html", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/edit.html", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/index.html", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/results.html", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/usage.html", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/widgets/video_chooser.html", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templatetags/__init__.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templatetags/wagtailvideos_tags.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/transcoders/__init__.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/transcoders/base/__init__.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/transcoders/ffmpeg/__init__.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/transcoders/ffmpeg/checks.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/transcoders/ffmpeg/ffmpeg.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/urls.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/views/__init__.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/views/chooser.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/views/multiple.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/views/videos.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/wagtail_hooks.py", "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/widgets.py"], "skipped": [{"path": "downloaded_repos/neon-jungle_wagtail-videos/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/tests/app/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/tests/app/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/tests/app/migrations/0001_initial.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/tests/app/migrations/0002_alter_testpage_video_streamfield.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/tests/app/migrations/0003_alter_customvideotrack_file_and_more.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/tests/app/migrations/0004_customvideomodel_height_customvideomodel_width.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/tests/app/migrations/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/tests/app/models.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/tests/app/settings.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/tests/app/test_block.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/tests/app/urls.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/tests/small.mp4", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/tests/small.vtt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/tests/storage.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/tests/templates/app/test_page.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/tests/test_admin_views.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/tests/test_custom_model.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/tests/test_template_tag.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/tests/utils.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/chooser/chooser.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/chooser/results.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/homepage/videos_summary.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/_file_field.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/edit.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/results.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/templates/wagtailvideos/videos/usage.html", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.96628999710083, "profiling_times": {"config_time": 6.627206087112427, "core_time": 3.6094870567321777, "ignores_time": 0.002424478530883789, "total_time": 10.240009069442749}, "parsing_time": {"total_time": 0.7080156803131104, "per_file_time": {"mean": 0.0126431371484484, "std_dev": 0.00039942314055154744}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 5.843400239944458, "per_file_time": {"mean": 0.027052778888631734, "std_dev": 0.008338884181139187}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 2.0168001651763916, "per_file_and_rule_time": {"mean": 0.006548052484338935, "std_dev": 0.000886712125901428}, "very_slow_stats": {"time_ratio": 0.4884217218600667, "count_ratio": 0.012987012987012988}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/views/videos.py", "rule_id": "python.django.security.injection.open-redirect.open-redirect", "time": 0.12595009803771973}, {"fpath": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/migrations/0012_remove_unique_constraint.py", "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.26070404052734375}, {"fpath": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/migrations/0014_alter_videotrack_file_alter_videotrack_kind_and_more.py", "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.289031982421875}, {"fpath": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/migrations/0011_video_tracks.py", "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.30936288833618164}]}, "tainting_time": {"total_time": 0.864403486251831, "per_def_and_rule_time": {"mean": 0.0014078232675111251, "std_dev": 0.00023627627085325738}, "very_slow_stats": {"time_ratio": 0.7651751958519085, "count_ratio": 0.004885993485342019}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/migrations/0014_alter_videotrack_file_alter_videotrack_kind_and_more.py", "fline": 7, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.21349692344665527}, {"fpath": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/migrations/0012_remove_unique_constraint.py", "fline": 6, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.21886515617370605}, {"fpath": "downloaded_repos/neon-jungle_wagtail-videos/wagtailvideos/migrations/0011_video_tracks.py", "fline": 9, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.22905802726745605}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}