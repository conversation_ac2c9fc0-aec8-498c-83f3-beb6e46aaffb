{"version": "1.130.0", "results": [], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nette_security/src/Security/SimpleAuthenticator.php", "start": {"line": 25, "col": 11, "offset": 0}, "end": {"line": 25, "col": 16, "offset": 5}}]], "message": "Syntax error at line downloaded_repos/nette_security/src/Security/SimpleAuthenticator.php:25:\n `array` was unexpected", "path": "downloaded_repos/nette_security/src/Security/SimpleAuthenticator.php", "spans": [{"file": "downloaded_repos/nette_security/src/Security/SimpleAuthenticator.php", "start": {"line": 25, "col": 11, "offset": 0}, "end": {"line": 25, "col": 16, "offset": 5}}]}], "paths": {"scanned": ["downloaded_repos/nette_security/.gitattributes", "downloaded_repos/nette_security/.github/workflows/coding-style.yml", "downloaded_repos/nette_security/.github/workflows/static-analysis.yml", "downloaded_repos/nette_security/.github/workflows/tests.yml", "downloaded_repos/nette_security/.gitignore", "downloaded_repos/nette_security/composer.json", "downloaded_repos/nette_security/license.md", "downloaded_repos/nette_security/phpstan.neon", "downloaded_repos/nette_security/readme.md", "downloaded_repos/nette_security/src/Bridges/SecurityDI/SecurityExtension.php", "downloaded_repos/nette_security/src/Bridges/SecurityHttp/CookieStorage.php", "downloaded_repos/nette_security/src/Bridges/SecurityHttp/SessionStorage.php", "downloaded_repos/nette_security/src/Bridges/SecurityTracy/UserPanel.php", "downloaded_repos/nette_security/src/Bridges/SecurityTracy/panel.latte", "downloaded_repos/nette_security/src/Bridges/SecurityTracy/tab.latte", "downloaded_repos/nette_security/src/Security/AuthenticationException.php", "downloaded_repos/nette_security/src/Security/Authenticator.php", "downloaded_repos/nette_security/src/Security/Authorizator.php", "downloaded_repos/nette_security/src/Security/IIdentity.php", "downloaded_repos/nette_security/src/Security/IdentityHandler.php", "downloaded_repos/nette_security/src/Security/Passwords.php", "downloaded_repos/nette_security/src/Security/Permission.php", "downloaded_repos/nette_security/src/Security/Resource.php", "downloaded_repos/nette_security/src/Security/Role.php", "downloaded_repos/nette_security/src/Security/SimpleAuthenticator.php", "downloaded_repos/nette_security/src/Security/SimpleIdentity.php", "downloaded_repos/nette_security/src/Security/User.php", "downloaded_repos/nette_security/src/Security/UserStorage.php", "downloaded_repos/nette_security/src/compatibility-intf.php", "downloaded_repos/nette_security/src/compatibility.php"], "skipped": [{"path": "downloaded_repos/nette_security/src/Bridges/SecurityTracy/dist/panel.phtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/src/Bridges/SecurityTracy/dist/tab.phtml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/src/Security/SimpleAuthenticator.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nette_security/tests/.coveralls.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/MockUserStorage.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Passwords.hash().phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Passwords.needsRehash().phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Passwords.verify().phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.CMSExample.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.DefaultAssert.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.DefaultDeny.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.DefaultRuleSet.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.IsAllowedNonExistent.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.PrivilegeAllow.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.PrivilegeAssert.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.PrivilegeDeny.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.Privileges.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.RemoveDefaultAllow.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.RemoveDefaultAllowNonExistent.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.RemoveDefaultDeny.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.RemoveDefaultDenyAssert.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.RemoveDefaultDenyNonExistent.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.RemovingRoleAfterItWasAllowedAccessToAllResources.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.ResourceAddAndGetOne.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.ResourceAddInheritsNonExistent.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.ResourceDuplicate.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.ResourceInherits.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.ResourceInheritsNonExistent.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.ResourceRemoveAll.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.ResourceRemoveOneNonExistent.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.RoleDefaultAllowRuleWithPrivilegeDenyRule.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.RoleDefaultAllowRuleWithResourceDenyRule.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.RoleDefaultRuleSet.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.RoleDefaultRuleSetPrivilege.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.RolePrivilegeAllow.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.RolePrivilegeAssert.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.RolePrivilegeDeny.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.RolePrivileges.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.RoleRegistryAddAndGetOne.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.RoleRegistryAddInheritsNonExistent.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.RoleRegistryDuplicate.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.RoleRegistryInherits.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.RoleRegistryInheritsMultiple.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.RoleRegistryInheritsNonExistent.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.RoleRegistryRemoveAll.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.RoleRegistryRemoveOneNonExistent.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.RuleRoleRemove.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.RuleRoleRemoveAll.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.RulesRemove.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.RulesResourceRemove.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/Permission.RulesResourceRemoveAll.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/SimpleAuthenticator.Data.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/SimpleAuthenticator.Roles.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/SimpleAuthenticator.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/SimpleIdentity.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/User.authentication.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security/User.authorization.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security.DI/SecurityExtension.authenticator.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security.DI/SecurityExtension.authorizator.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security.DI/SecurityExtension.cookieStorage.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security.DI/SecurityExtension.passwords.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security.DI/SecurityExtension.sessionStorage.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security.DI/SecurityExtension.user.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security.Http/CookieStorage.authentication.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/Security.Http/CookieStorage.getState.phpt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nette_security/tests/bootstrap.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.7874200344085693, "profiling_times": {"config_time": 7.36710524559021, "core_time": 2.4695870876312256, "ignores_time": 0.0018491744995117188, "total_time": 9.839429140090942}, "parsing_time": {"total_time": 0.6045782566070557, "per_file_time": {"mean": 0.026286011156828503, "std_dev": 0.0006699477255446449}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.1170878410339355, "per_file_time": {"mean": 0.013458889651011273, "std_dev": 0.0006382488601030258}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.17601490020751953, "per_file_and_rule_time": {"mean": 0.001557653984137341, "std_dev": 1.0474645497462734e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.002208232879638672, "per_def_and_rule_time": {"mean": 0.000184019406636556, "std_dev": 7.979462976790172e-08}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}