<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="mask-icon" href="/safari-pinned-tab.svg" color="#5bbad5">
    <meta name="msapplication-TileColor" content="#da532c">
    <meta name="theme-color" content="#ffffff">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="theme-color" content="#000000">
    <meta name="description" content="{{ video.info.description or 'Self host your media and share with unique links.' }}">
    <link rel="apple-touch-icon" href="/logo192.png">
    <link rel="manifest" href="/manifest.json">
    <meta property="og:type" content="video" data-react-helmet="true">
    <meta property="og:url" content="{{ domain }}/#/w/{{ video.video_id }}" data-react-helmet="true">
    <meta property="og:image" content="{{ domain }}/_content/derived/{{ video.video_id }}/poster.jpg" data-react-helmet="true">
    <meta property="og:video" content="{{ domain }}/_content/video/{{ video.video_id }}{{ video.extension }}" data-react-helmet="true">
    <meta property="og:video:secure_url" content="{{ domain }}/_content/video/{{ video.video_id }}{{ video.extension }}" data-react-helmet="true">
    <meta property="og:site_name" content="Fireshare" data-react-helmet="true">
    <meta property="og:title" content="{{ video.info.title }}" data-react-helmet="true">
    <meta property="og:video:width" content="{{ video.info.width }}" data-react-helmet="true">
    <meta property="og:video:height" content="{{ video.info.height }}" data-react-helmet="true">
    <link itemprop="thumbnailUrl" href="{{ domain }}/_content/derived/{{ video.video_id }}/poster.jpg">
    <title>{{ video.info.title }}</title>
</head>

<body>
    <script>
        window.location.href = "/#/w/{{ video.video_id }}" + window.location.search;
    </script>
</body>

</html>