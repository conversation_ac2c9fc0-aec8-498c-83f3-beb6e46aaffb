{"version": "1.130.0", "results": [{"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/color-picker.js", "start": {"line": 1, "col": 2184, "offset": 2183}, "end": {"line": 1, "col": 2255, "offset": 2254}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/rich-editor.js", "start": {"line": 42, "col": 29746, "offset": 40177}, "end": {"line": 42, "col": 29781, "offset": 40212}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/rich-editor.js", "start": {"line": 43, "col": 931, "offset": 46776}, "end": {"line": 43, "col": 944, "offset": 46789}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/rich-editor.js", "start": {"line": 43, "col": 2631, "offset": 48476}, "end": {"line": 43, "col": 2755, "offset": 48600}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/rich-editor.js", "start": {"line": 93, "col": 58, "offset": 189374}, "end": {"line": 93, "col": 115, "offset": 189431}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Button.vue", "start": {"line": 206, "col": 13, "offset": 7046}, "end": {"line": 206, "col": 42, "offset": 7075}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Button.vue", "start": {"line": 240, "col": 13, "offset": 8118}, "end": {"line": 240, "col": 42, "offset": 8147}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Pagination.vue", "start": {"line": 5, "col": 9, "offset": 146}, "end": {"line": 5, "col": 132, "offset": 269}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Pagination.vue", "start": {"line": 6, "col": 9, "offset": 293}, "end": {"line": 6, "col": 238, "offset": 522}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/abdessamadbettal_lara4/resources/js/Pages/Post/Post.vue", "start": {"line": 149, "col": 29, "offset": 6214}, "end": {"line": 149, "col": 58, "offset": 6243}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/abdessamadbettal_lara4/public/js/filament/filament/echo.js:\n ", "path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/filament/echo.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/file-upload.js:\n ", "path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/file-upload.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "message": "Timeout when running javascript.express.security.audit.remote-property-injection.remote-property-injection on downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/file-upload.js:\n ", "path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/file-upload.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/file-upload.js:\n ", "path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/file-upload.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-open-redirect.express-open-redirect", "message": "Timeout when running javascript.express.security.audit.express-open-redirect.express-open-redirect on downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/markdown-editor.js:\n ", "path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/markdown-editor.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-res-sendfile.express-res-sendfile", "message": "Timeout when running javascript.express.security.audit.express-res-sendfile.express-res-sendfile on downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/markdown-editor.js:\n ", "path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/markdown-editor.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/markdown-editor.js:\n ", "path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/markdown-editor.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/rich-editor.js:\n ", "path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/rich-editor.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/select.js:\n ", "path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/select.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "message": "Timeout when running javascript.express.security.audit.remote-property-injection.remote-property-injection on downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/select.js:\n ", "path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/select.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/select.js:\n ", "path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/select.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/abdessamadbettal_lara4/public/js/filament/support/support.js:\n ", "path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/support/support.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/abdessamadbettal_lara4/public/js/filament/support/support.js:\n ", "path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/support/support.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/abdessamadbettal_lara4/public/js/filament/support/support.js:\n ", "path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/support/support.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/abdessamadbettal_lara4/public/js/filament/widgets/components/chart.js:\n ", "path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/widgets/components/chart.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/abdessamadbettal_lara4/public/js/filament/widgets/components/chart.js:\n ", "path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/widgets/components/chart.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "message": "Timeout when running javascript.express.security.injection.raw-html-format.raw-html-format on downloaded_repos/abdessamadbettal_lara4/public/js/filament/widgets/components/chart.js:\n ", "path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/widgets/components/chart.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/abdessamadbettal_lara4/public/js/filament/widgets/components/stats-overview/stat/chart.js:\n ", "path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/widgets/components/stats-overview/stat/chart.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "message": "Timeout when running javascript.express.security.audit.remote-property-injection.remote-property-injection on downloaded_repos/abdessamadbettal_lara4/public/js/filament/widgets/components/stats-overview/stat/chart.js:\n ", "path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/widgets/components/stats-overview/stat/chart.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "message": "Timeout when running javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp on downloaded_repos/abdessamadbettal_lara4/public/js/filament/widgets/components/stats-overview/stat/chart.js:\n ", "path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/widgets/components/stats-overview/stat/chart.js"}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/color-picker.js", "start": {"line": 1, "col": 2695, "offset": 0}, "end": {"line": 1, "col": 2698, "offset": 3}}, {"path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/color-picker.js", "start": {"line": 1, "col": 2718, "offset": 0}, "end": {"line": 1, "col": 2721, "offset": 3}}, {"path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/color-picker.js", "start": {"line": 1, "col": 2764, "offset": 0}, "end": {"line": 1, "col": 2767, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/color-picker.js:1:\n `01:` was unexpected", "path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/color-picker.js", "spans": [{"file": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/color-picker.js", "start": {"line": 1, "col": 2695, "offset": 0}, "end": {"line": 1, "col": 2698, "offset": 3}}, {"file": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/color-picker.js", "start": {"line": 1, "col": 2718, "offset": 0}, "end": {"line": 1, "col": 2721, "offset": 3}}, {"file": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/color-picker.js", "start": {"line": 1, "col": 2764, "offset": 0}, "end": {"line": 1, "col": 2767, "offset": 3}}]}], "paths": {"scanned": ["downloaded_repos/abdessamadbettal_lara4/.editorconfig", "downloaded_repos/abdessamadbettal_lara4/.env.example", "downloaded_repos/abdessamadbettal_lara4/.eslintrc.cjs", "downloaded_repos/abdessamadbettal_lara4/.gitattributes", "downloaded_repos/abdessamadbettal_lara4/.github/FUNDING.yml", "downloaded_repos/abdessamadbettal_lara4/.github/ISSUE_TEMPLATE/bug_report.md", "downloaded_repos/abdessamadbettal_lara4/.github/ISSUE_TEMPLATE/feature_request.md", "downloaded_repos/abdessamadbettal_lara4/.github/screenshots/home.png", "downloaded_repos/abdessamadbettal_lara4/.github/workflows/deploy.yml", "downloaded_repos/abdessamadbettal_lara4/.github/workflows/testing.yml", "downloaded_repos/abdessamadbettal_lara4/.gitignore", "downloaded_repos/abdessamadbettal_lara4/.htaccess", "downloaded_repos/abdessamadbettal_lara4/.phpstorm.meta.php", "downloaded_repos/abdessamadbettal_lara4/.prettierrc", "downloaded_repos/abdessamadbettal_lara4/CHANGELOG.md", "downloaded_repos/abdessamadbettal_lara4/CODE_OF_CONDUCT.md", "downloaded_repos/abdessamadbettal_lara4/CONTRIBUTING.md", "downloaded_repos/abdessamadbettal_lara4/LICENCE", "downloaded_repos/abdessamadbettal_lara4/README.md", "downloaded_repos/abdessamadbettal_lara4/SECURITY.md", "downloaded_repos/abdessamadbettal_lara4/app/Console/Commands/AssignUserRoles.php", "downloaded_repos/abdessamadbettal_lara4/app/Console/Commands/GenerateSitemap.php", "downloaded_repos/abdessamadbettal_lara4/app/Console/Commands/SendReservationReminders.php", "downloaded_repos/abdessamadbettal_lara4/app/Console/Commands/SetupStarter.php", "downloaded_repos/abdessamadbettal_lara4/app/Events/UserUpdated.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Clusters/Networking/Resources/CommentResource/Pages/CreateComment.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Clusters/Networking/Resources/CommentResource/Pages/EditComment.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Clusters/Networking/Resources/CommentResource/Pages/ListComments.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Clusters/Networking/Resources/CommentResource/Pages/ViewComment.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Clusters/Networking/Resources/CommentResource.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Clusters/Networking/Resources/NewsletterResource/Pages/CreateNewsletter.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Clusters/Networking/Resources/NewsletterResource/Pages/EditNewsletter.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Clusters/Networking/Resources/NewsletterResource/Pages/ListNewsletters.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Clusters/Networking/Resources/NewsletterResource/Pages/ViewNewsletter.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Clusters/Networking/Resources/NewsletterResource.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Clusters/Networking/Resources/PhoneResource/Pages/CreatePhone.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Clusters/Networking/Resources/PhoneResource/Pages/EditPhone.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Clusters/Networking/Resources/PhoneResource/Pages/ListPhones.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Clusters/Networking/Resources/PhoneResource/Pages/ViewPhone.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Clusters/Networking/Resources/PhoneResource.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Clusters/Networking.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Clusters/Settings/Pages/ManageAbout.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Clusters/Settings/Pages/ManageContact.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Clusters/Settings/Pages/ManageFooter.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Clusters/Settings/Pages/ManageGeneral.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Clusters/Settings/Pages/ManageHeader.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Clusters/Settings/Pages/ManageHero.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Clusters/Settings/Pages/ManagePrivacy.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Clusters/Settings/Pages/ManageSeo.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Clusters/Settings/Pages/ManageSocial.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Clusters/Settings/Pages/ManageTerm.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Clusters/Settings.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Resources/CategoryResource/Pages/CreateCategory.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Resources/CategoryResource/Pages/EditCategory.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Resources/CategoryResource/Pages/ListCategories.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Resources/CategoryResource/Pages/ViewCategory.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Resources/CategoryResource.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Resources/PostResource/Pages/CreatePost.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Resources/PostResource/Pages/EditPost.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Resources/PostResource/Pages/ListPosts.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Resources/PostResource/Pages/ViewPost.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Resources/PostResource.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Resources/TagResource/Pages/CreateTag.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Resources/TagResource/Pages/EditTag.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Resources/TagResource/Pages/ListTags.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Resources/TagResource/Pages/ViewTag.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Resources/TagResource.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Resources/UserResource/Pages/CreateUser.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Resources/UserResource/Pages/EditUser.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Resources/UserResource/Pages/ListUsers.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Resources/UserResource/Pages/ViewUser.php", "downloaded_repos/abdessamadbettal_lara4/app/Filament/Resources/UserResource.php", "downloaded_repos/abdessamadbettal_lara4/app/Helpers/MarkdownHelper.php", "downloaded_repos/abdessamadbettal_lara4/app/Http/Controllers/Auth/AuthenticatedSessionController.php", "downloaded_repos/abdessamadbettal_lara4/app/Http/Controllers/Auth/ConfirmablePasswordController.php", "downloaded_repos/abdessamadbettal_lara4/app/Http/Controllers/Auth/EmailVerificationNotificationController.php", "downloaded_repos/abdessamadbettal_lara4/app/Http/Controllers/Auth/EmailVerificationPromptController.php", "downloaded_repos/abdessamadbettal_lara4/app/Http/Controllers/Auth/NewPasswordController.php", "downloaded_repos/abdessamadbettal_lara4/app/Http/Controllers/Auth/PasswordController.php", "downloaded_repos/abdessamadbettal_lara4/app/Http/Controllers/Auth/PasswordResetLinkController.php", "downloaded_repos/abdessamadbettal_lara4/app/Http/Controllers/Auth/ProviderController.php", "downloaded_repos/abdessamadbettal_lara4/app/Http/Controllers/Auth/RegisteredUserController.php", "downloaded_repos/abdessamadbettal_lara4/app/Http/Controllers/Auth/VerifyEmailController.php", "downloaded_repos/abdessamadbettal_lara4/app/Http/Controllers/CategoryController.php", "downloaded_repos/abdessamadbettal_lara4/app/Http/Controllers/CommentController.php", "downloaded_repos/abdessamadbettal_lara4/app/Http/Controllers/ContactMessageController.php", "downloaded_repos/abdessamadbettal_lara4/app/Http/Controllers/Controller.php", "downloaded_repos/abdessamadbettal_lara4/app/Http/Controllers/HomeController.php", "downloaded_repos/abdessamadbettal_lara4/app/Http/Controllers/NewsletterController.php", "downloaded_repos/abdessamadbettal_lara4/app/Http/Controllers/PhoneController.php", "downloaded_repos/abdessamadbettal_lara4/app/Http/Controllers/PostController.php", "downloaded_repos/abdessamadbettal_lara4/app/Http/Controllers/ProfileController.php", "downloaded_repos/abdessamadbettal_lara4/app/Http/Controllers/UserController.php", "downloaded_repos/abdessamadbettal_lara4/app/Http/Middleware/EnsureEmailIsVerified.php", "downloaded_repos/abdessamadbettal_lara4/app/Http/Middleware/ForceJsonResponse.php", "downloaded_repos/abdessamadbettal_lara4/app/Http/Middleware/HandleInertiaRequests.php", "downloaded_repos/abdessamadbettal_lara4/app/Http/Middleware/SetLocale.php", "downloaded_repos/abdessamadbettal_lara4/app/Http/Requests/Auth/LoginRequest.php", "downloaded_repos/abdessamadbettal_lara4/app/Http/Requests/ContactUs/StoreContactMessageRequest.php", "downloaded_repos/abdessamadbettal_lara4/app/Http/Requests/ProfileUpdateRequest.php", "downloaded_repos/abdessamadbettal_lara4/app/Http/Requests/User/StoreUserRequest.php", "downloaded_repos/abdessamadbettal_lara4/app/Http/Requests/User/UpdateUserRequest.php", "downloaded_repos/abdessamadbettal_lara4/app/Http/Resources/CategoryResource.php", "downloaded_repos/abdessamadbettal_lara4/app/Http/Resources/PostResource.php", "downloaded_repos/abdessamadbettal_lara4/app/Http/Resources/TagResource.php", "downloaded_repos/abdessamadbettal_lara4/app/Http/Resources/UserResource.php", "downloaded_repos/abdessamadbettal_lara4/app/Listeners/LogSuccessfulLogin.php", "downloaded_repos/abdessamadbettal_lara4/app/Listeners/UserUpdatedNotifyUser.php", "downloaded_repos/abdessamadbettal_lara4/app/Mail/ContactMessageMail.php", "downloaded_repos/abdessamadbettal_lara4/app/Mail/ForgotPasswordMail.php", "downloaded_repos/abdessamadbettal_lara4/app/Mail/ReservationConfirmationEmail.php", "downloaded_repos/abdessamadbettal_lara4/app/Mail/ReservationReceptionEmail.php", "downloaded_repos/abdessamadbettal_lara4/app/Mail/ReservationReminderEmail.php", "downloaded_repos/abdessamadbettal_lara4/app/Mail/WelcomeEmail.php", "downloaded_repos/abdessamadbettal_lara4/app/Models/Category.php", "downloaded_repos/abdessamadbettal_lara4/app/Models/Comment.php", "downloaded_repos/abdessamadbettal_lara4/app/Models/ContactMessage.php", "downloaded_repos/abdessamadbettal_lara4/app/Models/Newsletter.php", "downloaded_repos/abdessamadbettal_lara4/app/Models/Phone.php", "downloaded_repos/abdessamadbettal_lara4/app/Models/Post.php", "downloaded_repos/abdessamadbettal_lara4/app/Models/Provider.php", "downloaded_repos/abdessamadbettal_lara4/app/Models/Tag.php", "downloaded_repos/abdessamadbettal_lara4/app/Models/User.php", "downloaded_repos/abdessamadbettal_lara4/app/Notifications/InvoicePaid.php", "downloaded_repos/abdessamadbettal_lara4/app/Notifications/UserAccountUpdated.php", "downloaded_repos/abdessamadbettal_lara4/app/Notifications/WelcomeUser.php", "downloaded_repos/abdessamadbettal_lara4/app/Policies/CategoryPolicy.php", "downloaded_repos/abdessamadbettal_lara4/app/Policies/CommentPolicy.php", "downloaded_repos/abdessamadbettal_lara4/app/Policies/PhonePolicy.php", "downloaded_repos/abdessamadbettal_lara4/app/Policies/PostPolicy.php", "downloaded_repos/abdessamadbettal_lara4/app/Policies/UserPolicy.php", "downloaded_repos/abdessamadbettal_lara4/app/Providers/AppServiceProvider.php", "downloaded_repos/abdessamadbettal_lara4/app/Providers/Filament/AdminPanelProvider.php", "downloaded_repos/abdessamadbettal_lara4/app/Settings/AboutSettings.php", "downloaded_repos/abdessamadbettal_lara4/app/Settings/ContactSettings.php", "downloaded_repos/abdessamadbettal_lara4/app/Settings/FooterSettings.php", "downloaded_repos/abdessamadbettal_lara4/app/Settings/GeneralSettings.php", "downloaded_repos/abdessamadbettal_lara4/app/Settings/HeaderSettings.php", "downloaded_repos/abdessamadbettal_lara4/app/Settings/HeroSettings.php", "downloaded_repos/abdessamadbettal_lara4/app/Settings/PrivacySettings.php", "downloaded_repos/abdessamadbettal_lara4/app/Settings/SeoSettings.php", "downloaded_repos/abdessamadbettal_lara4/app/Settings/SocialSettings.php", "downloaded_repos/abdessamadbettal_lara4/app/Settings/TermSettings.php", "downloaded_repos/abdessamadbettal_lara4/app/Traits/TracksUserDevices.php", "downloaded_repos/abdessamadbettal_lara4/artisan", "downloaded_repos/abdessamadbettal_lara4/bootstrap/app.php", "downloaded_repos/abdessamadbettal_lara4/bootstrap/cache/.gitignore", "downloaded_repos/abdessamadbettal_lara4/bootstrap/providers.php", "downloaded_repos/abdessamadbettal_lara4/composer.json", "downloaded_repos/abdessamadbettal_lara4/composer.lock", "downloaded_repos/abdessamadbettal_lara4/config/activitylog.php", "downloaded_repos/abdessamadbettal_lara4/config/app.php", "downloaded_repos/abdessamadbettal_lara4/config/auth.php", "downloaded_repos/abdessamadbettal_lara4/config/backup.php", "downloaded_repos/abdessamadbettal_lara4/config/cache.php", "downloaded_repos/abdessamadbettal_lara4/config/cors.php", "downloaded_repos/abdessamadbettal_lara4/config/database.php", "downloaded_repos/abdessamadbettal_lara4/config/feed.php", "downloaded_repos/abdessamadbettal_lara4/config/filament.php", "downloaded_repos/abdessamadbettal_lara4/config/filesystems.php", "downloaded_repos/abdessamadbettal_lara4/config/ide-helper.php", "downloaded_repos/abdessamadbettal_lara4/config/laravellocalization.php", "downloaded_repos/abdessamadbettal_lara4/config/location.php", "downloaded_repos/abdessamadbettal_lara4/config/log-viewer.php", "downloaded_repos/abdessamadbettal_lara4/config/logging.php", "downloaded_repos/abdessamadbettal_lara4/config/mail.php", "downloaded_repos/abdessamadbettal_lara4/config/media-library.php", "downloaded_repos/abdessamadbettal_lara4/config/missing-page-redirector.php", "downloaded_repos/abdessamadbettal_lara4/config/permission.php", "downloaded_repos/abdessamadbettal_lara4/config/queue.php", "downloaded_repos/abdessamadbettal_lara4/config/sanctum.php", "downloaded_repos/abdessamadbettal_lara4/config/services.php", "downloaded_repos/abdessamadbettal_lara4/config/session.php", "downloaded_repos/abdessamadbettal_lara4/config/settings.php", "downloaded_repos/abdessamadbettal_lara4/config/sitemap.php", "downloaded_repos/abdessamadbettal_lara4/config/tags.php", "downloaded_repos/abdessamadbettal_lara4/config/vue-i18n-generator.php", "downloaded_repos/abdessamadbettal_lara4/database/.gitignore", "downloaded_repos/abdessamadbettal_lara4/database/factories/CategoryFactory.php", "downloaded_repos/abdessamadbettal_lara4/database/factories/CommentFactory.php", "downloaded_repos/abdessamadbettal_lara4/database/factories/ContactMessageFactory.php", "downloaded_repos/abdessamadbettal_lara4/database/factories/NewsletterFactory.php", "downloaded_repos/abdessamadbettal_lara4/database/factories/PhoneFactory.php", "downloaded_repos/abdessamadbettal_lara4/database/factories/PostFactory.php", "downloaded_repos/abdessamadbettal_lara4/database/factories/ProviderFactory.php", "downloaded_repos/abdessamadbettal_lara4/database/factories/TagFactory.php", "downloaded_repos/abdessamadbettal_lara4/database/factories/UserFactory.php", "downloaded_repos/abdessamadbettal_lara4/database/migrations/0001_01_01_000000_create_users_table.php", "downloaded_repos/abdessamadbettal_lara4/database/migrations/0001_01_01_000001_create_cache_table.php", "downloaded_repos/abdessamadbettal_lara4/database/migrations/0001_01_01_000002_create_jobs_table.php", "downloaded_repos/abdessamadbettal_lara4/database/migrations/2022_12_14_083707_create_settings_table.php", "downloaded_repos/abdessamadbettal_lara4/database/migrations/2024_10_09_121957_create_categories_table.php", "downloaded_repos/abdessamadbettal_lara4/database/migrations/2024_11_07_140427_create_personal_access_tokens_table.php", "downloaded_repos/abdessamadbettal_lara4/database/migrations/2024_11_08_094923_create_media_table.php", "downloaded_repos/abdessamadbettal_lara4/database/migrations/2024_11_08_103222_create_permission_tables.php", "downloaded_repos/abdessamadbettal_lara4/database/migrations/2024_11_08_112102_create_tag_tables.php", "downloaded_repos/abdessamadbettal_lara4/database/migrations/2024_11_08_112324_create_seo_table.php", "downloaded_repos/abdessamadbettal_lara4/database/migrations/2024_11_08_115910_create_activity_log_table.php", "downloaded_repos/abdessamadbettal_lara4/database/migrations/2024_11_08_115911_add_event_column_to_activity_log_table.php", "downloaded_repos/abdessamadbettal_lara4/database/migrations/2024_11_08_115912_add_batch_uuid_column_to_activity_log_table.php", "downloaded_repos/abdessamadbettal_lara4/database/migrations/2024_11_09_093928_create_posts_table.php", "downloaded_repos/abdessamadbettal_lara4/database/migrations/2024_11_09_124659_create_phones_table.php", "downloaded_repos/abdessamadbettal_lara4/database/migrations/2024_11_11_093649_create_comments_table.php", "downloaded_repos/abdessamadbettal_lara4/database/migrations/2024_11_24_120648_create_contact_messages_table.php", "downloaded_repos/abdessamadbettal_lara4/database/migrations/2024_12_05_100421_create_language_lines_table.php", "downloaded_repos/abdessamadbettal_lara4/database/migrations/2024_12_06_165723_create_newsletters_table.php", "downloaded_repos/abdessamadbettal_lara4/database/seeders/CategorySeeder.php", "downloaded_repos/abdessamadbettal_lara4/database/seeders/CommentSeeder.php", "downloaded_repos/abdessamadbettal_lara4/database/seeders/ContactMessageSeeder.php", "downloaded_repos/abdessamadbettal_lara4/database/seeders/DatabaseSeeder.php", "downloaded_repos/abdessamadbettal_lara4/database/seeders/NewsletterSeeder.php", "downloaded_repos/abdessamadbettal_lara4/database/seeders/PhoneSeeder.php", "downloaded_repos/abdessamadbettal_lara4/database/seeders/PostSeeder.php", "downloaded_repos/abdessamadbettal_lara4/database/seeders/ProviderSeeder.php", "downloaded_repos/abdessamadbettal_lara4/database/seeders/RolesAndPermissionsSeeder.php", "downloaded_repos/abdessamadbettal_lara4/database/seeders/TagSeeder.php", "downloaded_repos/abdessamadbettal_lara4/database/seeders/UserSeeder.php", "downloaded_repos/abdessamadbettal_lara4/database/settings/2024_11_08_101059_create_general_settings.php", "downloaded_repos/abdessamadbettal_lara4/database/settings/2024_11_08_101139_create_header_settings.php", "downloaded_repos/abdessamadbettal_lara4/database/settings/2024_11_08_101214_create_footer_settings.php", "downloaded_repos/abdessamadbettal_lara4/database/settings/2024_11_08_101355_create_about_settings.php", "downloaded_repos/abdessamadbettal_lara4/database/settings/2024_11_08_101411_create_privacy_settings.php", "downloaded_repos/abdessamadbettal_lara4/database/settings/2024_11_08_101439_create_term_settings.php", "downloaded_repos/abdessamadbettal_lara4/database/settings/2024_11_08_101458_create_social_settings.php", "downloaded_repos/abdessamadbettal_lara4/database/settings/2024_11_08_101524_create_seo_settings.php", "downloaded_repos/abdessamadbettal_lara4/database/settings/2024_11_08_101612_create_contact_settings.php", "downloaded_repos/abdessamadbettal_lara4/database/settings/2024_11_08_102630_create_hero_settings.php", "downloaded_repos/abdessamadbettal_lara4/docker-compose.yml", "downloaded_repos/abdessamadbettal_lara4/jsconfig.json", "downloaded_repos/abdessamadbettal_lara4/lang/.gitignore", "downloaded_repos/abdessamadbettal_lara4/lang/ar/auth.php", "downloaded_repos/abdessamadbettal_lara4/lang/ar/home.php", "downloaded_repos/abdessamadbettal_lara4/lang/ar/mail.php", "downloaded_repos/abdessamadbettal_lara4/lang/ar/pagination.php", "downloaded_repos/abdessamadbettal_lara4/lang/ar/passwords.php", "downloaded_repos/abdessamadbettal_lara4/lang/ar/validation.php", "downloaded_repos/abdessamadbettal_lara4/lang/en/auth.php", "downloaded_repos/abdessamadbettal_lara4/lang/en/home.php", "downloaded_repos/abdessamadbettal_lara4/lang/en/mail.php", "downloaded_repos/abdessamadbettal_lara4/lang/en/pagination.php", "downloaded_repos/abdessamadbettal_lara4/lang/en/passwords.php", "downloaded_repos/abdessamadbettal_lara4/lang/en/validation.php", "downloaded_repos/abdessamadbettal_lara4/lang/es/auth.php", "downloaded_repos/abdessamadbettal_lara4/lang/es/home.php", "downloaded_repos/abdessamadbettal_lara4/lang/es/mail.php", "downloaded_repos/abdessamadbettal_lara4/lang/es/pagination.php", "downloaded_repos/abdessamadbettal_lara4/lang/es/passwords.php", "downloaded_repos/abdessamadbettal_lara4/lang/es/validation.php", "downloaded_repos/abdessamadbettal_lara4/lang/fr/auth.php", "downloaded_repos/abdessamadbettal_lara4/lang/fr/home.php", "downloaded_repos/abdessamadbettal_lara4/lang/fr/mail.php", "downloaded_repos/abdessamadbettal_lara4/lang/fr/pagination.php", "downloaded_repos/abdessamadbettal_lara4/lang/fr/passwords.php", "downloaded_repos/abdessamadbettal_lara4/lang/fr/validation.php", "downloaded_repos/abdessamadbettal_lara4/package-lock.json", "downloaded_repos/abdessamadbettal_lara4/package.json", "downloaded_repos/abdessamadbettal_lara4/phpunit.xml", "downloaded_repos/abdessamadbettal_lara4/postcss.config.js", "downloaded_repos/abdessamadbettal_lara4/public/.htaccess", "downloaded_repos/abdessamadbettal_lara4/public/apple-touch-icon.png", "downloaded_repos/abdessamadbettal_lara4/public/assets/images/partners/filament.svg", "downloaded_repos/abdessamadbettal_lara4/public/assets/images/partners/inertia.svg", "downloaded_repos/abdessamadbettal_lara4/public/assets/images/partners/laravel.svg", "downloaded_repos/abdessamadbettal_lara4/public/assets/images/partners/mirage.svg", "downloaded_repos/abdessamadbettal_lara4/public/assets/images/partners/statamic.svg", "downloaded_repos/abdessamadbettal_lara4/public/assets/images/partners/statickit.svg", "downloaded_repos/abdessamadbettal_lara4/public/assets/images/partners/transistor.svg", "downloaded_repos/abdessamadbettal_lara4/public/assets/images/partners/tuple.svg", "downloaded_repos/abdessamadbettal_lara4/public/browserconfig.xml", "downloaded_repos/abdessamadbettal_lara4/public/css/bezhansalleh/filament-language-switch/filament-language-switch.css", "downloaded_repos/abdessamadbettal_lara4/public/css/filament/filament/app.css", "downloaded_repos/abdessamadbettal_lara4/public/css/filament/forms/forms.css", "downloaded_repos/abdessamadbettal_lara4/public/css/filament/support/support.css", "downloaded_repos/abdessamadbettal_lara4/public/favicon-96x96.png", "downloaded_repos/abdessamadbettal_lara4/public/favicon.ico", "downloaded_repos/abdessamadbettal_lara4/public/favicon.png", "downloaded_repos/abdessamadbettal_lara4/public/favicon.svg", "downloaded_repos/abdessamadbettal_lara4/public/favicon.zip", "downloaded_repos/abdessamadbettal_lara4/public/index.php", "downloaded_repos/abdessamadbettal_lara4/public/js/filament/filament/app.js", "downloaded_repos/abdessamadbettal_lara4/public/js/filament/filament/echo.js", "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/color-picker.js", "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/date-time-picker.js", "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/file-upload.js", "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/key-value.js", "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/markdown-editor.js", "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/rich-editor.js", "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/select.js", "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/tags-input.js", "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/textarea.js", "downloaded_repos/abdessamadbettal_lara4/public/js/filament/notifications/notifications.js", "downloaded_repos/abdessamadbettal_lara4/public/js/filament/support/async-alpine.js", "downloaded_repos/abdessamadbettal_lara4/public/js/filament/support/support.js", "downloaded_repos/abdessamadbettal_lara4/public/js/filament/tables/components/table.js", "downloaded_repos/abdessamadbettal_lara4/public/js/filament/tables/tables.js", "downloaded_repos/abdessamadbettal_lara4/public/js/filament/widgets/components/chart.js", "downloaded_repos/abdessamadbettal_lara4/public/js/filament/widgets/components/stats-overview/stat/chart.js", "downloaded_repos/abdessamadbettal_lara4/public/robots.txt", "downloaded_repos/abdessamadbettal_lara4/public/site.webmanifest", "downloaded_repos/abdessamadbettal_lara4/public/web-app-manifest-192x192.png", "downloaded_repos/abdessamadbettal_lara4/public/web-app-manifest-512x512.png", "downloaded_repos/abdessamadbettal_lara4/resources/css/app.css", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/ApplicationLogo.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Blog/AuthorCard.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Blog/BlogCard.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Blog/BlogSection.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Blog/RelatedPosts.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Blog/ShareButtons.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Blog/TableOfContents.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Blog/Tag.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Button.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Container.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/DropdownLink.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Forms/Checkbox.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Forms/Dropdown.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Forms/InputError.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Forms/InputLabel.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Forms/TextInput.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Home/Hero.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Home/StackSection.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Home/Trusted.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Icons/IconBars.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Icons/IconCheck.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Icons/IconGithub.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Icons/IconPlay.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Icons/IconQuotes.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Icons/IconTaskContacts.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Icons/IconTaskInventory.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Icons/IconTaskReporting.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Icons/IconTextDecoration.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Icons/IconTextDecoration2.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Icons/IconX.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Icons/Logo.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/LanguageSwitcher.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Layout/Footer.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Layout/Header.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Layout/MobileNav.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Layout/SideBar.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Modal.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/NavLink.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/Pagination.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/ResponsiveNavLink.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Components/ThemeSwitcher.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Layouts/AuthenticatedLayout.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Layouts/GeneralLayout.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Layouts/GuestLayout.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Mixins/defaultLayoutMixin.js", "downloaded_repos/abdessamadbettal_lara4/resources/js/Pages/Auth/ConfirmPassword.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Pages/Auth/ForgotPassword.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Pages/Auth/Login.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Pages/Auth/Partials/ProvidersAuth.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Pages/Auth/Register.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Pages/Auth/ResetPassword.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Pages/Auth/VerifyEmail.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Pages/Dashboard.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Pages/Post/CreatePost.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Pages/Post/EditPost.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Pages/Post/Post.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Pages/Post/Posts.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Pages/Profile/Edit.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Pages/Profile/Partials/DeleteUserForm.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Pages/Profile/Partials/UpdatePasswordForm.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Pages/Profile/Partials/UpdateProfileInformationForm.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/Pages/Welcome.vue", "downloaded_repos/abdessamadbettal_lara4/resources/js/app.js", "downloaded_repos/abdessamadbettal_lara4/resources/js/bootstrap.js", "downloaded_repos/abdessamadbettal_lara4/resources/js/i18n/locales.js", "downloaded_repos/abdessamadbettal_lara4/resources/js/i18n.js", "downloaded_repos/abdessamadbettal_lara4/resources/js/ssr.js", "downloaded_repos/abdessamadbettal_lara4/resources/views/.gitkeep", "downloaded_repos/abdessamadbettal_lara4/resources/views/app.blade.php", "downloaded_repos/abdessamadbettal_lara4/resources/views/components/button.blade.php", "downloaded_repos/abdessamadbettal_lara4/resources/views/components/hero.blade.php", "downloaded_repos/abdessamadbettal_lara4/resources/views/layouts/app.blade.php", "downloaded_repos/abdessamadbettal_lara4/resources/views/mail/auth/forgot.blade.php", "downloaded_repos/abdessamadbettal_lara4/resources/views/mail/contactus/contact_message.blade.php", "downloaded_repos/abdessamadbettal_lara4/resources/views/mail/invoice/paid.blade.php", "downloaded_repos/abdessamadbettal_lara4/resources/views/mail/reservation/confirmation.blade.php", "downloaded_repos/abdessamadbettal_lara4/resources/views/mail/reservation/reception.blade.php", "downloaded_repos/abdessamadbettal_lara4/resources/views/mail/reservation/reminder.blade.php", "downloaded_repos/abdessamadbettal_lara4/resources/views/mail/user/updated.blade.php", "downloaded_repos/abdessamadbettal_lara4/resources/views/mail/user/welcome.blade.php", "downloaded_repos/abdessamadbettal_lara4/resources/views/partials/footer.blade.php", "downloaded_repos/abdessamadbettal_lara4/resources/views/partials/header.blade.php", "downloaded_repos/abdessamadbettal_lara4/resources/views/reservation/confirmation.blade.php", "downloaded_repos/abdessamadbettal_lara4/resources/views/reservation/reception.blade.php", "downloaded_repos/abdessamadbettal_lara4/resources/views/reservation/reminder.blade.php", "downloaded_repos/abdessamadbettal_lara4/routes/api.php", "downloaded_repos/abdessamadbettal_lara4/routes/auth.php", "downloaded_repos/abdessamadbettal_lara4/routes/console.php", "downloaded_repos/abdessamadbettal_lara4/routes/web.php", "downloaded_repos/abdessamadbettal_lara4/storage/app/.gitignore", "downloaded_repos/abdessamadbettal_lara4/storage/app/images/covers/1.jpg", "downloaded_repos/abdessamadbettal_lara4/storage/app/images/covers/10.jpg", "downloaded_repos/abdessamadbettal_lara4/storage/app/images/covers/12.jpg", "downloaded_repos/abdessamadbettal_lara4/storage/app/images/covers/13.jpg", "downloaded_repos/abdessamadbettal_lara4/storage/app/images/covers/14.jpg", "downloaded_repos/abdessamadbettal_lara4/storage/app/images/covers/15.jpg", "downloaded_repos/abdessamadbettal_lara4/storage/app/images/covers/16.jpg", "downloaded_repos/abdessamadbettal_lara4/storage/app/images/covers/17.jpg", "downloaded_repos/abdessamadbettal_lara4/storage/app/images/covers/18.jpg", "downloaded_repos/abdessamadbettal_lara4/storage/app/images/covers/19.jpg", "downloaded_repos/abdessamadbettal_lara4/storage/app/images/covers/2.jpg", "downloaded_repos/abdessamadbettal_lara4/storage/app/images/covers/3.jpg", "downloaded_repos/abdessamadbettal_lara4/storage/app/images/covers/4.jpg", "downloaded_repos/abdessamadbettal_lara4/storage/app/images/covers/5.jpg", "downloaded_repos/abdessamadbettal_lara4/storage/app/images/covers/6.jpg", "downloaded_repos/abdessamadbettal_lara4/storage/app/images/covers/7.jpg", "downloaded_repos/abdessamadbettal_lara4/storage/app/images/covers/8.jpg", "downloaded_repos/abdessamadbettal_lara4/storage/app/images/covers/9.jpg", "downloaded_repos/abdessamadbettal_lara4/storage/app/images/covers/cta.jpg", "downloaded_repos/abdessamadbettal_lara4/storage/app/images/users/01.jpg", "downloaded_repos/abdessamadbettal_lara4/storage/app/images/users/02.jpg", "downloaded_repos/abdessamadbettal_lara4/storage/app/images/users/03.jpg", "downloaded_repos/abdessamadbettal_lara4/storage/app/images/users/04.jpg", "downloaded_repos/abdessamadbettal_lara4/storage/app/images/users/05.jpg", "downloaded_repos/abdessamadbettal_lara4/storage/app/images/users/06.jpg", "downloaded_repos/abdessamadbettal_lara4/storage/app/images/users/07.jpg", "downloaded_repos/abdessamadbettal_lara4/storage/app/images/users/16.jpg", "downloaded_repos/abdessamadbettal_lara4/storage/app/private/.gitignore", "downloaded_repos/abdessamadbettal_lara4/storage/app/public/.gitignore", "downloaded_repos/abdessamadbettal_lara4/storage/app/public/test.jpg", "downloaded_repos/abdessamadbettal_lara4/storage/app/temp/.gitignore", "downloaded_repos/abdessamadbettal_lara4/storage/dotenv-editor/.gitignore", "downloaded_repos/abdessamadbettal_lara4/storage/framework/.gitignore", "downloaded_repos/abdessamadbettal_lara4/storage/framework/cache/.gitignore", "downloaded_repos/abdessamadbettal_lara4/storage/framework/cache/data/.gitignore", "downloaded_repos/abdessamadbettal_lara4/storage/framework/sessions/.gitignore", "downloaded_repos/abdessamadbettal_lara4/storage/framework/testing/.gitignore", "downloaded_repos/abdessamadbettal_lara4/storage/framework/views/.gitignore", "downloaded_repos/abdessamadbettal_lara4/storage/logs/.gitignore", "downloaded_repos/abdessamadbettal_lara4/tailwind.config.js", "downloaded_repos/abdessamadbettal_lara4/vite.config.js"], "skipped": [{"path": "downloaded_repos/abdessamadbettal_lara4/.github/screenshots/Animation.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/abdessamadbettal_lara4/_ide_helper.php", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/backup/ar/notifications.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/backup/bg/notifications.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/backup/bn/notifications.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/backup/cs/notifications.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/backup/da/notifications.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/backup/de/notifications.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/backup/en/notifications.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/backup/es/notifications.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/backup/fa/notifications.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/backup/fi/notifications.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/backup/fr/notifications.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessa<PERSON>bettal_lara4/lang/vendor/backup/he/notifications.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/backup/hi/notifications.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/backup/hr/notifications.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/backup/id/notifications.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/backup/it/notifications.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/backup/ja/notifications.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/backup/kk/notifications.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/backup/ko/notifications.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/backup/nl/notifications.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/backup/no/notifications.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/backup/pl/notifications.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/backup/pt/notifications.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/backup/pt-BR/notifications.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/backup/ro/notifications.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/backup/ru/notifications.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/backup/sk/notifications.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/backup/tr/notifications.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/backup/uk/notifications.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/backup/zh-CN/notifications.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/backup/zh-TW/notifications.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ar/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ar/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ar/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ar/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ar/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ar/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ar/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ar/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ar/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ar/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ar/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ar/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ar/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ar/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ar/unsaved-changes-alert.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ar/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ar/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/az/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/az/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/az/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/az/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/az/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/az/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/az/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/az/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/az/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/az/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/az/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/az/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/az/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/az/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/az/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/az/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bg/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bg/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bg/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bg/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bg/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bg/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bg/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bg/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bg/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bg/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bg/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bg/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bg/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bg/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bg/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bg/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bn/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bn/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bn/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bn/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bn/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bn/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bn/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bn/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bn/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bn/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bs/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bs/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bs/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bs/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bs/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bs/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bs/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bs/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bs/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/bs/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ca/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ca/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ca/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ca/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ca/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ca/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ca/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ca/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ca/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ca/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ca/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ca/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ca/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ca/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ca/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ca/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ckb/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ckb/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ckb/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ckb/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ckb/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ckb/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ckb/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ckb/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ckb/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ckb/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ckb/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ckb/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ckb/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ckb/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ckb/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ckb/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/cs/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/cs/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/cs/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/cs/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/cs/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/cs/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/cs/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/cs/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/cs/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/cs/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/cs/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/cs/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/cs/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/cs/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/cs/unsaved-changes-alert.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/cs/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/cs/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/da/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/da/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/da/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/da/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/da/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/da/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/da/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/da/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/da/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/da/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/da/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/da/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/da/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/da/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/da/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/da/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/de/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/de/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/de/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/de/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/de/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/de/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/de/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/de/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/de/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/de/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/de/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/de/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/de/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/de/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/de/unsaved-changes-alert.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/de/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/de/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/el/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/el/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/el/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/el/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/el/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/el/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/el/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/el/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/el/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/el/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/el/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/el/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/el/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/el/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/el/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/en/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/en/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/en/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/en/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/en/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/en/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/en/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/en/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/en/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/en/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/en/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/en/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/en/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/en/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/en/unsaved-changes-alert.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/en/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/en/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/es/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/es/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/es/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/es/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/es/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/es/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/es/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/es/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/es/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/es/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/es/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/es/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/es/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/es/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/es/unsaved-changes-alert.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/es/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/es/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fa/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fa/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fa/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fa/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fa/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fa/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fa/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fa/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fa/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fa/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fa/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fa/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fa/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fa/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fa/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fa/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fi/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fi/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fi/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fi/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fi/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fi/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fi/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fi/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fi/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fi/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fi/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fi/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fi/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fi/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fi/unsaved-changes-alert.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fi/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fi/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fr/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fr/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fr/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fr/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fr/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fr/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fr/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fr/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fr/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fr/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fr/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fr/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fr/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fr/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fr/unsaved-changes-alert.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fr/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/fr/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/he/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/he/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/he/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/he/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/he/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessa<PERSON>bettal_lara4/lang/vendor/filament-panels/he/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessa<PERSON>bettal_lara4/lang/vendor/filament-panels/he/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/he/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/he/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/he/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/he/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/he/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/he/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/he/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/he/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/he/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hi/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hi/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hi/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hi/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hi/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hi/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hi/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hi/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hi/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hi/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hr/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hr/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hr/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hr/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hr/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hr/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hr/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hr/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hr/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hr/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hr/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hr/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hr/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hr/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hr/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hr/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hu/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hu/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hu/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hu/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hu/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hu/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hu/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hu/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hu/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hu/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hu/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hu/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hu/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hu/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hu/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hu/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hy/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hy/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hy/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hy/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hy/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hy/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hy/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hy/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hy/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/hy/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/id/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/id/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/id/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/id/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/id/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/id/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/id/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/id/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/id/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/id/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/id/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/id/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/id/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/id/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/id/unsaved-changes-alert.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/id/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/id/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/it/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/it/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/it/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/it/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/it/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/it/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/it/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/it/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/it/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/it/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/it/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/it/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/it/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/it/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/it/unsaved-changes-alert.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/it/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/it/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ja/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ja/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ja/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ja/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ja/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ja/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ja/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ja/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ja/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ja/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ja/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ja/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ja/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ja/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ja/unsaved-changes-alert.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ja/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ja/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ka/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ka/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ka/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ka/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ka/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ka/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ka/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ka/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ka/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ka/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ka/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ka/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ka/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ka/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ka/unsaved-changes-alert.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ka/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ka/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/km/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/km/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/km/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/km/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/km/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/km/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/km/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/km/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/km/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/km/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/km/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/km/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/km/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/km/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/km/unsaved-changes-alert.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/km/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/km/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ko/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ko/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ko/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ko/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ko/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ko/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ko/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ko/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ko/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ko/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ko/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ko/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ko/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ko/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ko/unsaved-changes-alert.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ko/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ko/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ku/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ku/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ku/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ku/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ku/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ku/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ku/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ku/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ku/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ku/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ku/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ku/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ku/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ku/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ku/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ku/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/lt/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/lt/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/lt/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/lt/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/lt/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/lt/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/lt/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/lt/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/lt/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/lt/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/lt/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/lt/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/lt/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/lt/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/lt/unsaved-changes-alert.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/lt/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/lt/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/lv/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/lv/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/lv/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/lv/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/lv/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/lv/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/lv/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/lv/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/lv/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/lv/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/lv/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/lv/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/lv/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/lv/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/lv/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/lv/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/mn/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/mn/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/mn/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/mn/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/mn/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/mn/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/mn/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/mn/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/mn/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/mn/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/mn/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/mn/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/mn/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/mn/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/mn/unsaved-changes-alert.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/mn/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/mn/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ms/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ms/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ms/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ms/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ms/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ms/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ms/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ms/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ms/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ms/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ms/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ms/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ms/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ms/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ms/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ms/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/my/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/my/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/my/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/my/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/my/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/my/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/my/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/my/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/my/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/my/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/nl/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/nl/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/nl/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/nl/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/nl/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/nl/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/nl/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/nl/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/nl/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/nl/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/nl/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/nl/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/nl/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/nl/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/nl/unsaved-changes-alert.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/nl/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/nl/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/no/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/no/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/no/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/no/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/no/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/no/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/no/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/no/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/no/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/no/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/no/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/no/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/no/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/no/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/no/unsaved-changes-alert.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/no/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/no/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/np/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/np/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/np/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/np/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/np/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/np/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/np/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/np/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/np/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/np/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/np/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/np/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/np/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/np/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/np/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/np/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pl/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pl/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pl/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pl/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pl/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pl/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pl/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pl/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pl/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pl/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pl/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pl/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pl/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pl/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pl/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pl/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pt_BR/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pt_BR/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pt_BR/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pt_BR/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pt_BR/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pt_BR/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pt_BR/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pt_BR/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pt_BR/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pt_BR/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pt_BR/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pt_BR/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pt_BR/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pt_BR/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pt_BR/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pt_BR/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pt_PT/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pt_PT/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pt_PT/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pt_PT/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pt_PT/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pt_PT/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pt_PT/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pt_PT/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pt_PT/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pt_PT/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pt_PT/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pt_PT/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pt_PT/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pt_PT/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pt_PT/unsaved-changes-alert.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pt_PT/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/pt_PT/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ro/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ro/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ro/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ro/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ro/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ro/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ro/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ro/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ro/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ro/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ro/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ro/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ro/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ro/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ro/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ro/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ru/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ru/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ru/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ru/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ru/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ru/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ru/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ru/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ru/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ru/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ru/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ru/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ru/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ru/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ru/unsaved-changes-alert.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ru/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/ru/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sk/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sk/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sk/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sk/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sk/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sk/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sk/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sk/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sk/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sk/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sk/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sk/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sk/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sk/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sk/unsaved-changes-alert.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sk/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sk/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sl/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sl/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sl/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sl/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sl/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sl/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sl/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sl/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sl/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sl/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sl/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sl/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sl/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sl/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sl/unsaved-changes-alert.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sl/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sl/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sq/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sq/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sq/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sq/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sq/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sq/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sq/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sq/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sq/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sq/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sq/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sq/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sq/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sq/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sq/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sq/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sv/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sv/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sv/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sv/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sv/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sv/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sv/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sv/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sv/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sv/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sv/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sv/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sv/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sv/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sv/unsaved-changes-alert.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sv/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sv/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sw/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sw/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sw/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sw/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sw/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sw/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sw/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sw/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sw/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/sw/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/th/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/th/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/th/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/th/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/th/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/th/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/th/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/th/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/th/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/th/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/th/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/th/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/th/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/th/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/th/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/th/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/tr/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/tr/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/tr/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/tr/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/tr/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/tr/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/tr/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/tr/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/tr/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/tr/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/tr/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/tr/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/tr/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/tr/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/tr/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/tr/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/uk/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/uk/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/uk/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/uk/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/uk/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/uk/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/uk/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/uk/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/uk/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/uk/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/uk/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/uk/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/uk/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/uk/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/uk/unsaved-changes-alert.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/uk/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/uk/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/uz/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/uz/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/uz/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/uz/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/uz/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/uz/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/uz/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/uz/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/uz/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/uz/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/uz/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/uz/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/uz/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/uz/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/uz/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/uz/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/vi/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/vi/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/vi/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/vi/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/vi/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/vi/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/vi/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/vi/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/vi/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/vi/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/vi/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/vi/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/vi/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/vi/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/vi/unsaved-changes-alert.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/vi/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/vi/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/zh_CN/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/zh_CN/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/zh_CN/pages/auth/edit-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/zh_CN/pages/auth/email-verification/email-verification-prompt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/zh_CN/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/zh_CN/pages/auth/password-reset/request-password-reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/zh_CN/pages/auth/password-reset/reset-password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/zh_CN/pages/auth/register.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/zh_CN/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/zh_CN/pages/tenancy/edit-tenant-profile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/zh_CN/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/zh_CN/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/zh_CN/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/zh_CN/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/zh_CN/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/zh_CN/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/zh_TW/global-search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/zh_TW/layout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/zh_TW/pages/auth/login.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/zh_TW/pages/dashboard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/zh_TW/resources/pages/create-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/zh_TW/resources/pages/edit-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/zh_TW/resources/pages/list-records.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/zh_TW/resources/pages/view-record.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/zh_TW/widgets/account-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-panels/zh_TW/widgets/filament-info-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-password-input/ar/password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-password-input/en/password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-password-input/pt_BR/password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/lang/vendor/filament-password-input/pt_PT/password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/filament/echo.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/color-picker.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/file-upload.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/markdown-editor.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/rich-editor.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/select.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/support/support.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/widgets/components/chart.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/widgets/components/stats-overview/stat/chart.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/abdessamadbettal_lara4/public/vendor/feed/atom.xsl", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/public/vendor/feed/style.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/public/vendor/log-viewer/app.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/public/vendor/log-viewer/app.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/public/vendor/log-viewer/app.js.LICENSE.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/public/vendor/log-viewer/img/log-viewer-128.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/public/vendor/log-viewer/img/log-viewer-32.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/public/vendor/log-viewer/img/log-viewer-64.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/public/vendor/log-viewer/mix-manifest.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/resources/views/vendor/filament-language-switch/.gitkeep", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/resources/views/vendor/filament-language-switch/components/flag.blade.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/resources/views/vendor/filament-language-switch/language-switch.blade.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/resources/views/vendor/filament-language-switch/switch.blade.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/tests/Feature/Auth/AuthenticationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/tests/Feature/Auth/EmailVerificationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/tests/Feature/Auth/PasswordResetTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/tests/Feature/Auth/RegistrationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/tests/Feature/FirstTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/tests/Pest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/tests/TestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abdessamadbettal_lara4/tests/Unit/ExampleTest.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 2.2193031311035156, "profiling_times": {"config_time": 6.02126145362854, "core_time": 48.98304748535156, "ignores_time": 0.00225067138671875, "total_time": 55.007888078689575}, "parsing_time": {"total_time": 3.8617053031921387, "per_file_time": {"mean": 0.013891026270475315, "std_dev": 0.0130453944522179}, "very_slow_stats": {"time_ratio": 0.4877246426936663, "count_ratio": 0.0035971223021582736}, "very_slow_files": [{"fpath": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/date-time-picker.js", "ftime": 1.883448839187622}]}, "scanning_time": {"total_time": 197.09114599227905, "per_file_time": {"mean": 0.17108606422940892, "std_dev": 4.800385286303956}, "very_slow_stats": {"time_ratio": 0.9475071837922393, "count_ratio": 0.008680555555555556}, "very_slow_files": [{"fpath": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/notifications/notifications.js", "ftime": 1.552497148513794}, {"fpath": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/support/support.js", "ftime": 5.140405178070068}, {"fpath": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/widgets/components/chart.js", "ftime": 5.233873128890991}, {"fpath": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/markdown-editor.js", "ftime": 5.412382125854492}, {"fpath": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/date-time-picker.js", "ftime": 8.89874792098999}, {"fpath": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/file-upload.js", "ftime": 24.85722303390503}, {"fpath": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/widgets/components/stats-overview/stat/chart.js", "ftime": 27.088347911834717}, {"fpath": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/select.js", "ftime": 29.355444192886353}, {"fpath": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/rich-editor.js", "ftime": 34.917495012283325}, {"fpath": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/filament/echo.js", "ftime": 44.28886103630066}]}, "matching_time": {"total_time": 34.04629182815552, "per_file_and_rule_time": {"mean": 0.05890361907985388, "std_dev": 0.07131449618152788}, "very_slow_stats": {"time_ratio": 0.9235011211235412, "count_ratio": 0.0847750865051903}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/rich-editor.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 1.027250051498413}, {"fpath": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/filament/echo.js", "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 1.159447193145752}, {"fpath": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/rich-editor.js", "rule_id": "problem-based-packs.insecure-transport.js-node.using-http-server.using-http-server", "time": 1.2731502056121826}, {"fpath": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/filament/echo.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 1.879624843597412}, {"fpath": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/filament/echo.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 1.900651216506958}, {"fpath": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/rich-editor.js", "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 1.9091310501098633}, {"fpath": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/date-time-picker.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 2.0660629272460938}, {"fpath": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/rich-editor.js", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 2.181917190551758}, {"fpath": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/filament/echo.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 2.3525140285491943}, {"fpath": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/forms/components/rich-editor.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 2.558759927749634}]}, "tainting_time": {"total_time": 39.41914772987366, "per_def_and_rule_time": {"mean": 0.0018653770457066846, "std_dev": 0.002681616344448681}, "very_slow_stats": {"time_ratio": 0.8023690125741544, "count_ratio": 0.0022241150861253074}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/filament/echo.js", "fline": 1, "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 1.3549079895019531}, {"fpath": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/filament/echo.js", "fline": 1, "rule_id": "javascript.express.security.x-frame-options-misconfiguration.x-frame-options-misconfiguration", "time": 1.5312440395355225}, {"fpath": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/filament/echo.js", "fline": 1, "rule_id": "javascript.express.security.express-data-exfiltration.express-data-exfiltration", "time": 1.5803179740905762}, {"fpath": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/filament/echo.js", "fline": 1, "rule_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "time": 1.692831039428711}, {"fpath": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/filament/echo.js", "fline": 1, "rule_id": "javascript.express.security.cors-misconfiguration.cors-misconfiguration", "time": 1.7538330554962158}, {"fpath": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/filament/echo.js", "fline": 1, "rule_id": "javascript.lang.security.insecure-object-assign.insecure-object-assign", "time": 1.8267130851745605}, {"fpath": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/filament/echo.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 2.322597026824951}, {"fpath": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/filament/echo.js", "fline": 1, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 2.7322750091552734}, {"fpath": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/filament/echo.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 3.065056085586548}, {"fpath": "downloaded_repos/abdessamadbettal_lara4/public/js/filament/filament/echo.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 3.3025381565093994}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}