{"version": "1.130.0", "results": [{"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/meceware_wapy.dev/Dockerfile", "start": {"line": 47, "col": 1, "offset": 1115}, "end": {"line": 47, "col": 35, "offset": 1149}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD [\"/bin/sh\", \"./entrypoint.sh\"]", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/meceware_wapy.dev/.dockerignore", "downloaded_repos/meceware_wapy.dev/.env.example", "downloaded_repos/meceware_wapy.dev/.eslintrc.json", "downloaded_repos/meceware_wapy.dev/.github/workflows/docker-build.yml", "downloaded_repos/meceware_wapy.dev/.gitignore", "downloaded_repos/meceware_wapy.dev/Dockerfile", "downloaded_repos/meceware_wapy.dev/LICENSE", "downloaded_repos/meceware_wapy.dev/README.md", "downloaded_repos/meceware_wapy.dev/components.json", "downloaded_repos/meceware_wapy.dev/docker-compose-dev.yml", "downloaded_repos/meceware_wapy.dev/docker-compose.yml", "downloaded_repos/meceware_wapy.dev/jsconfig.json", "downloaded_repos/meceware_wapy.dev/media/icon-2600.png", "downloaded_repos/meceware_wapy.dev/media/screenshots/account-page.png", "downloaded_repos/meceware_wapy.dev/media/screenshots/edit-page.png", "downloaded_repos/meceware_wapy.dev/media/screenshots/home-page-filter.png", "downloaded_repos/meceware_wapy.dev/media/screenshots/home-page.png", "downloaded_repos/meceware_wapy.dev/media/screenshots/reports-page.png", "downloaded_repos/meceware_wapy.dev/media/screenshots/view-page.png", "downloaded_repos/meceware_wapy.dev/next.config.mjs", "downloaded_repos/meceware_wapy.dev/package-lock.json", "downloaded_repos/meceware_wapy.dev/package.json", "downloaded_repos/meceware_wapy.dev/postcss.config.mjs", "downloaded_repos/meceware_wapy.dev/prisma/migrations/20241223193222_initial_database/migration.sql", "downloaded_repos/meceware_wapy.dev/prisma/migrations/20250111134032_otp_token_table_is_added/migration.sql", "downloaded_repos/meceware_wapy.dev/prisma/migrations/20250201200330_paddle_integration/migration.sql", "downloaded_repos/meceware_wapy.dev/prisma/migrations/20250517112459_webhook_integration/migration.sql", "downloaded_repos/meceware_wapy.dev/prisma/migrations/20250713190536_past_payments/migration.sql", "downloaded_repos/meceware_wapy.dev/prisma/migrations/migration_lock.toml", "downloaded_repos/meceware_wapy.dev/prisma/schema.prisma", "downloaded_repos/meceware_wapy.dev/public/.well-known/apple-developer-merchantid-domain-association", "downloaded_repos/meceware_wapy.dev/public/apple-touch-icon.png", "downloaded_repos/meceware_wapy.dev/public/favicon.ico", "downloaded_repos/meceware_wapy.dev/public/favicon.svg", "downloaded_repos/meceware_wapy.dev/public/icon.png", "downloaded_repos/meceware_wapy.dev/public/icons/icon-192.png", "downloaded_repos/meceware_wapy.dev/public/icons/icon-256.png", "downloaded_repos/meceware_wapy.dev/public/icons/icon-32.png", "downloaded_repos/meceware_wapy.dev/public/icons/icon-512.png", "downloaded_repos/meceware_wapy.dev/public/icons/icon-96.png", "downloaded_repos/meceware_wapy.dev/public/icons/icon-notification-dismiss.png", "downloaded_repos/meceware_wapy.dev/public/icons/icon-notification-mark-as-paid.png", "downloaded_repos/meceware_wapy.dev/public/icons/icon-notification-now.png", "downloaded_repos/meceware_wapy.dev/public/icons/icon-notification-upcoming.png", "downloaded_repos/meceware_wapy.dev/public/images/banner.png", "downloaded_repos/meceware_wapy.dev/public/images/og.png", "downloaded_repos/meceware_wapy.dev/public/images/x.png", "downloaded_repos/meceware_wapy.dev/public/sw.js", "downloaded_repos/meceware_wapy.dev/public/web-app-manifest-192x192.png", "downloaded_repos/meceware_wapy.dev/public/web-app-manifest-512x512.png", "downloaded_repos/meceware_wapy.dev/scripts/backup.sh", "downloaded_repos/meceware_wapy.dev/scripts/entrypoint-dev.sh", "downloaded_repos/meceware_wapy.dev/scripts/entrypoint.sh", "downloaded_repos/meceware_wapy.dev/scripts/restore.sh", "downloaded_repos/meceware_wapy.dev/scripts/setup.sh", "downloaded_repos/meceware_wapy.dev/scripts/upload.sh", "downloaded_repos/meceware_wapy.dev/src/app/404/page.js", "downloaded_repos/meceware_wapy.dev/src/app/account/account-container.js", "downloaded_repos/meceware_wapy.dev/src/app/account/actions.js", "downloaded_repos/meceware_wapy.dev/src/app/account/page.js", "downloaded_repos/meceware_wapy.dev/src/app/account/schema.js", "downloaded_repos/meceware_wapy.dev/src/app/api/auth/[...nextauth]/route.js", "downloaded_repos/meceware_wapy.dev/src/app/api/cron/route.js", "downloaded_repos/meceware_wapy.dev/src/app/api/mark-as-paid/route.js", "downloaded_repos/meceware_wapy.dev/src/app/api/paddle/route.js", "downloaded_repos/meceware_wapy.dev/src/app/api/webhook/route.js", "downloaded_repos/meceware_wapy.dev/src/app/contact/actions.js", "downloaded_repos/meceware_wapy.dev/src/app/contact/form.js", "downloaded_repos/meceware_wapy.dev/src/app/contact/page.js", "downloaded_repos/meceware_wapy.dev/src/app/default.js", "downloaded_repos/meceware_wapy.dev/src/app/edit/[slug]/page.js", "downloaded_repos/meceware_wapy.dev/src/app/edit/new/page.js", "downloaded_repos/meceware_wapy.dev/src/app/edit/page.js", "downloaded_repos/meceware_wapy.dev/src/app/globals.css", "downloaded_repos/meceware_wapy.dev/src/app/layout.js", "downloaded_repos/meceware_wapy.dev/src/app/loading.js", "downloaded_repos/meceware_wapy.dev/src/app/login/action.js", "downloaded_repos/meceware_wapy.dev/src/app/login/page.js", "downloaded_repos/meceware_wapy.dev/src/app/login/schema.js", "downloaded_repos/meceware_wapy.dev/src/app/login/signin-form.js", "downloaded_repos/meceware_wapy.dev/src/app/manifest.js", "downloaded_repos/meceware_wapy.dev/src/app/not-found.js", "downloaded_repos/meceware_wapy.dev/src/app/page.js", "downloaded_repos/meceware_wapy.dev/src/app/pricing/page.js", "downloaded_repos/meceware_wapy.dev/src/app/privacy/page.js", "downloaded_repos/meceware_wapy.dev/src/app/privacy/policy.js", "downloaded_repos/meceware_wapy.dev/src/app/refund-policy/page.js", "downloaded_repos/meceware_wapy.dev/src/app/refund-policy/refund-policy.js", "downloaded_repos/meceware_wapy.dev/src/app/reports/page.js", "downloaded_repos/meceware_wapy.dev/src/app/robots.js", "downloaded_repos/meceware_wapy.dev/src/app/signout/route.js", "downloaded_repos/meceware_wapy.dev/src/app/sitemap.js", "downloaded_repos/meceware_wapy.dev/src/app/terms-of-service/page.js", "downloaded_repos/meceware_wapy.dev/src/app/terms-of-service/terms-of-service.js", "downloaded_repos/meceware_wapy.dev/src/app/view/[slug]/page.js", "downloaded_repos/meceware_wapy.dev/src/app/view/page.js", "downloaded_repos/meceware_wapy.dev/src/components/add-to-home-screen.js", "downloaded_repos/meceware_wapy.dev/src/components/config.js", "downloaded_repos/meceware_wapy.dev/src/components/cookie-consent.js", "downloaded_repos/meceware_wapy.dev/src/components/filtered-icons.js", "downloaded_repos/meceware_wapy.dev/src/components/footer.js", "downloaded_repos/meceware_wapy.dev/src/components/header-member.js", "downloaded_repos/meceware_wapy.dev/src/components/header-visitor.js", "downloaded_repos/meceware_wapy.dev/src/components/header.js", "downloaded_repos/meceware_wapy.dev/src/components/home-member.js", "downloaded_repos/meceware_wapy.dev/src/components/home-visitor.js", "downloaded_repos/meceware_wapy.dev/src/components/icons.js", "downloaded_repos/meceware_wapy.dev/src/components/mode-toggle.js", "downloaded_repos/meceware_wapy.dev/src/components/not-found.js", "downloaded_repos/meceware_wapy.dev/src/components/notifications/actions.js", "downloaded_repos/meceware_wapy.dev/src/components/notifications/notification-bell.js", "downloaded_repos/meceware_wapy.dev/src/components/notifications/notification-context.js", "downloaded_repos/meceware_wapy.dev/src/components/notifications/notification-toggle.js", "downloaded_repos/meceware_wapy.dev/src/components/pricing-table.js", "downloaded_repos/meceware_wapy.dev/src/components/providers.js", "downloaded_repos/meceware_wapy.dev/src/components/search-bar.js", "downloaded_repos/meceware_wapy.dev/src/components/subscription-guard.js", "downloaded_repos/meceware_wapy.dev/src/components/subscriptions/actions.js", "downloaded_repos/meceware_wapy.dev/src/components/subscriptions/card.js", "downloaded_repos/meceware_wapy.dev/src/components/subscriptions/edit.js", "downloaded_repos/meceware_wapy.dev/src/components/subscriptions/filter.js", "downloaded_repos/meceware_wapy.dev/src/components/subscriptions/form/field-category.js", "downloaded_repos/meceware_wapy.dev/src/components/subscriptions/form/field-currency.js", "downloaded_repos/meceware_wapy.dev/src/components/subscriptions/form/field-cycle.js", "downloaded_repos/meceware_wapy.dev/src/components/subscriptions/form/field-name.js", "downloaded_repos/meceware_wapy.dev/src/components/subscriptions/form/field-notes.js", "downloaded_repos/meceware_wapy.dev/src/components/subscriptions/form/field-notifications.js", "downloaded_repos/meceware_wapy.dev/src/components/subscriptions/form/field-payment-date.js", "downloaded_repos/meceware_wapy.dev/src/components/subscriptions/form/field-price.js", "downloaded_repos/meceware_wapy.dev/src/components/subscriptions/form/field-timezone.js", "downloaded_repos/meceware_wapy.dev/src/components/subscriptions/form/field-until-date.js", "downloaded_repos/meceware_wapy.dev/src/components/subscriptions/form/field-url.js", "downloaded_repos/meceware_wapy.dev/src/components/subscriptions/form/form-logo.js", "downloaded_repos/meceware_wapy.dev/src/components/subscriptions/lib.js", "downloaded_repos/meceware_wapy.dev/src/components/subscriptions/list.js", "downloaded_repos/meceware_wapy.dev/src/components/subscriptions/reports.js", "downloaded_repos/meceware_wapy.dev/src/components/subscriptions/schema.js", "downloaded_repos/meceware_wapy.dev/src/components/subscriptions/utils.js", "downloaded_repos/meceware_wapy.dev/src/components/subscriptions/view.js", "downloaded_repos/meceware_wapy.dev/src/components/subscriptions/webhook.js", "downloaded_repos/meceware_wapy.dev/src/components/ui/accordion.jsx", "downloaded_repos/meceware_wapy.dev/src/components/ui/alert.jsx", "downloaded_repos/meceware_wapy.dev/src/components/ui/avatar.jsx", "downloaded_repos/meceware_wapy.dev/src/components/ui/badge.jsx", "downloaded_repos/meceware_wapy.dev/src/components/ui/button.jsx", "downloaded_repos/meceware_wapy.dev/src/components/ui/calendar.jsx", "downloaded_repos/meceware_wapy.dev/src/components/ui/card.jsx", "downloaded_repos/meceware_wapy.dev/src/components/ui/checkbox.jsx", "downloaded_repos/meceware_wapy.dev/src/components/ui/collapsible.jsx", "downloaded_repos/meceware_wapy.dev/src/components/ui/command.jsx", "downloaded_repos/meceware_wapy.dev/src/components/ui/datetime-picker.jsx", "downloaded_repos/meceware_wapy.dev/src/components/ui/dialog.jsx", "downloaded_repos/meceware_wapy.dev/src/components/ui/divider.jsx", "downloaded_repos/meceware_wapy.dev/src/components/ui/drawer.jsx", "downloaded_repos/meceware_wapy.dev/src/components/ui/dropdown-menu.jsx", "downloaded_repos/meceware_wapy.dev/src/components/ui/form.jsx", "downloaded_repos/meceware_wapy.dev/src/components/ui/icon-picker.jsx", "downloaded_repos/meceware_wapy.dev/src/components/ui/input-otp.jsx", "downloaded_repos/meceware_wapy.dev/src/components/ui/input.jsx", "downloaded_repos/meceware_wapy.dev/src/components/ui/label.jsx", "downloaded_repos/meceware_wapy.dev/src/components/ui/popover.jsx", "downloaded_repos/meceware_wapy.dev/src/components/ui/responsive-dialog.jsx", "downloaded_repos/meceware_wapy.dev/src/components/ui/scroll-area.jsx", "downloaded_repos/meceware_wapy.dev/src/components/ui/select.jsx", "downloaded_repos/meceware_wapy.dev/src/components/ui/separator.jsx", "downloaded_repos/meceware_wapy.dev/src/components/ui/simple-select.jsx", "downloaded_repos/meceware_wapy.dev/src/components/ui/skeleton.jsx", "downloaded_repos/meceware_wapy.dev/src/components/ui/sonner.jsx", "downloaded_repos/meceware_wapy.dev/src/components/ui/switch.jsx", "downloaded_repos/meceware_wapy.dev/src/components/ui/textarea.jsx", "downloaded_repos/meceware_wapy.dev/src/components/ui/toggle-group.jsx", "downloaded_repos/meceware_wapy.dev/src/components/ui/toggle.jsx", "downloaded_repos/meceware_wapy.dev/src/components/ui/tooltip.jsx", "downloaded_repos/meceware_wapy.dev/src/config/categories.js", "downloaded_repos/meceware_wapy.dev/src/config/colors.js", "downloaded_repos/meceware_wapy.dev/src/config/companies.js", "downloaded_repos/meceware_wapy.dev/src/config/currencies.js", "downloaded_repos/meceware_wapy.dev/src/lib/auth.js", "downloaded_repos/meceware_wapy.dev/src/lib/cookies.js", "downloaded_repos/meceware_wapy.dev/src/lib/hooks.js", "downloaded_repos/meceware_wapy.dev/src/lib/mail.js", "downloaded_repos/meceware_wapy.dev/src/lib/paddle/actions.js", "downloaded_repos/meceware_wapy.dev/src/lib/paddle/enum.js", "downloaded_repos/meceware_wapy.dev/src/lib/paddle/status.js", "downloaded_repos/meceware_wapy.dev/src/lib/prisma.js", "downloaded_repos/meceware_wapy.dev/src/lib/utils.js", "downloaded_repos/meceware_wapy.dev/src/lib/with-auth.js"], "skipped": [{"path": "downloaded_repos/meceware_wapy.dev/media/psd/favicon.psd", "reason": "exceeded_size_limit"}]}, "time": {"rules": [], "rules_parse_time": 1.7717008590698242, "profiling_times": {"config_time": 5.876997947692871, "core_time": 4.347680568695068, "ignores_time": 0.0021333694458007812, "total_time": 10.227879047393799}, "parsing_time": {"total_time": 3.7230074405670166, "per_file_time": {"mean": 0.025155455679506864, "std_dev": 0.002790320963431133}, "very_slow_stats": {"time_ratio": 0.10557861857746427, "count_ratio": 0.006756756756756757}, "very_slow_files": [{"fpath": "downloaded_repos/meceware_wapy.dev/package-lock.json", "ftime": 0.3930699825286865}]}, "scanning_time": {"total_time": 15.102265357971191, "per_file_time": {"mean": 0.028931542831362435, "std_dev": 0.011604674634260204}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 6.245765686035156, "per_file_and_rule_time": {"mean": 0.011133272167620598, "std_dev": 0.0009635093560373272}, "very_slow_stats": {"time_ratio": 0.3347984703381808, "count_ratio": 0.0213903743315508}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/meceware_wapy.dev/src/components/ui/datetime-picker.jsx", "rule_id": "problem-based-packs.insecure-transport.js-node.using-http-server.using-http-server", "time": 0.11423897743225098}, {"fpath": "downloaded_repos/meceware_wapy.dev/src/app/api/cron/route.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.12367606163024902}, {"fpath": "downloaded_repos/meceware_wapy.dev/src/components/subscriptions/filter.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.12748312950134277}, {"fpath": "downloaded_repos/meceware_wapy.dev/src/components/subscriptions/form/field-notifications.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.12992501258850098}, {"fpath": "downloaded_repos/meceware_wapy.dev/src/app/login/signin-form.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.1423342227935791}, {"fpath": "downloaded_repos/meceware_wapy.dev/src/components/subscriptions/card.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.****************}, {"fpath": "downloaded_repos/meceware_wapy.dev/src/app/api/paddle/route.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.*****************}, {"fpath": "downloaded_repos/meceware_wapy.dev/src/components/subscriptions/reports.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.****************}, {"fpath": "downloaded_repos/meceware_wapy.dev/src/app/account/account-container.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.*****************}, {"fpath": "downloaded_repos/meceware_wapy.dev/src/components/ui/datetime-picker.jsx", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.*****************}]}, "tainting_time": {"total_time": 1.****************, "per_def_and_rule_time": {"mean": 0.0052680979977856885, "std_dev": 8.501030153022902e-05}, "very_slow_stats": {"time_ratio": 0.*****************, "count_ratio": 0.013513513513513514}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/meceware_wapy.dev/src/components/subscriptions/reports.js", "fline": 261, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.053360939025878906}, {"fpath": "downloaded_repos/meceware_wapy.dev/src/components/subscriptions/view.js", "fline": 28, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.*****************}, {"fpath": "downloaded_repos/meceware_wapy.dev/src/app/account/account-container.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.058897972106933594}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": **********}, "engine_requested": "OSS", "skipped_rules": []}