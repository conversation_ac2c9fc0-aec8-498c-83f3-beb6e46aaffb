{"version": "1.130.0", "results": [], "errors": [], "paths": {"scanned": ["downloaded_repos/michbeck100_pv-monitoring/.env-sample", "downloaded_repos/michbeck100_pv-monitoring/.gitignore", "downloaded_repos/michbeck100_pv-monitoring/LICENSE", "downloaded_repos/michbeck100_pv-monitoring/README.md", "downloaded_repos/michbeck100_pv-monitoring/docker-compose.yml", "downloaded_repos/michbeck100_pv-monitoring/evcc/evcc.yaml", "downloaded_repos/michbeck100_pv-monitoring/grafana/dashboards/pv-monitoring.json", "downloaded_repos/michbeck100_pv-monitoring/grafana/provisioning/dashboards/all.yml", "downloaded_repos/michbeck100_pv-monitoring/grafana/provisioning/datasources/all.yml", "downloaded_repos/michbeck100_pv-monitoring/influxdb/influxdb-init.iql", "downloaded_repos/michbeck100_pv-monitoring/modbus-proxy/modbus-proxy.yml", "downloaded_repos/michbeck100_pv-monitoring/mosquitto/mosquitto.conf", "downloaded_repos/michbeck100_pv-monitoring/pvforecast/config.ini", "downloaded_repos/michbeck100_pv-monitoring/scriptable/pv-monitoring.js", "downloaded_repos/michbeck100_pv-monitoring/sungather/config.yaml", "downloaded_repos/michbeck100_pv-monitoring/telegraf/telegraf.conf"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 2.442379951477051, "profiling_times": {"config_time": 6.236043214797974, "core_time": 3.2544424533843994, "ignores_time": 0.0016980171203613281, "total_time": 9.493179082870483}, "parsing_time": {"total_time": 0.22892212867736816, "per_file_time": {"mean": 0.028615266084671017, "std_dev": 0.000979151973132808}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.7528536319732666, "per_file_time": {"mean": 0.01792508647555397, "std_dev": 0.0012662508143184699}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.23695158958435059, "per_file_and_rule_time": {"mean": 0.0012603807956614393, "std_dev": 1.1600406894305532e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.010202407836914062, "per_def_and_rule_time": {"mean": 0.00024291447230747769, "std_dev": 5.63389485993254e-08}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}