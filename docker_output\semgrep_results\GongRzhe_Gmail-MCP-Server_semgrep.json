{"version": "1.130.0", "results": [{"check_id": "dockerfile.security.missing-user-entrypoint.missing-user-entrypoint", "path": "downloaded_repos/GongRzhe_Gmail-MCP-Server/Dockerfile", "start": {"line": 28, "col": 1, "offset": 414}, "end": {"line": 28, "col": 37, "offset": 450}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nENTRYPOINT [\"node\", \"dist/index.js\"]", "metadata": {"cwe": ["CWE-269: Improper Privilege Management"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user-entrypoint.missing-user-entrypoint", "shortlink": "https://sg.run/k281"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/GongRzhe_Gmail-MCP-Server/src/index.ts", "start": {"line": 487, "col": 35, "offset": 19830}, "end": {"line": 487, "col": 110, "offset": 19905}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/GongRzhe_Gmail-MCP-Server/src/index.ts", "start": {"line": 938, "col": 52, "offset": 40838}, "end": {"line": 938, "col": 60, "offset": 40846}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/GongRzhe_Gmail-MCP-Server/src/index.ts", "start": {"line": 938, "col": 62, "offset": 40848}, "end": {"line": 938, "col": 70, "offset": 40856}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/GongRzhe_Gmail-MCP-Server/.gitignore", "downloaded_repos/GongRzhe_Gmail-MCP-Server/.npmignore", "downloaded_repos/GongRzhe_Gmail-MCP-Server/Dockerfile", "downloaded_repos/GongRzhe_Gmail-MCP-Server/LICENSE", "downloaded_repos/GongRzhe_Gmail-MCP-Server/README.md", "downloaded_repos/GongRzhe_Gmail-MCP-Server/docker-compose.yml", "downloaded_repos/GongRzhe_Gmail-MCP-Server/llms-install.md", "downloaded_repos/GongRzhe_Gmail-MCP-Server/mcp-config.json", "downloaded_repos/GongRzhe_Gmail-MCP-Server/package-lock.json", "downloaded_repos/GongRzhe_Gmail-MCP-Server/package.json", "downloaded_repos/GongRzhe_Gmail-MCP-Server/setup.js", "downloaded_repos/GongRzhe_Gmail-MCP-Server/smithery.yaml", "downloaded_repos/GongRzhe_Gmail-MCP-Server/src/evals/evals.ts", "downloaded_repos/GongRzhe_Gmail-MCP-Server/src/index.ts", "downloaded_repos/GongRzhe_Gmail-MCP-Server/src/label-manager.ts", "downloaded_repos/GongRzhe_Gmail-MCP-Server/src/utl.ts", "downloaded_repos/GongRzhe_Gmail-MCP-Server/tsconfig.json"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.7956218719482422, "profiling_times": {"config_time": 6.616639137268066, "core_time": 3.447629690170288, "ignores_time": 0.0017769336700439453, "total_time": 10.06728744506836}, "parsing_time": {"total_time": 0.3941519260406494, "per_file_time": {"mean": 0.03284599383672078, "std_dev": 0.0006421996900812922}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.8341076374053955, "per_file_time": {"mean": 0.03987190516098687, "std_dev": 0.021508028581316705}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.7895138263702393, "per_file_and_rule_time": {"mean": 0.006367046986856768, "std_dev": 0.0003423690295664632}, "very_slow_stats": {"time_ratio": 0.17598304796583744, "count_ratio": 0.008064516129032258}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/GongRzhe_Gmail-MCP-Server/src/index.ts", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.13894104957580566}]}, "tainting_time": {"total_time": 0.29259395599365234, "per_def_and_rule_time": {"mean": 0.0017110757660447505, "std_dev": 1.5245747756176582e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}