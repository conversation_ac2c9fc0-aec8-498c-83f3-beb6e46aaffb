{"version": "1.130.0", "results": [{"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/signup.html", "start": {"line": 42, "col": 1, "offset": 1431}, "end": {"line": 42, "col": 76, "offset": 1506}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/authorization-area.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 17, "offset": 43}}, {"path": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/authorization-area.html", "start": {"line": 46, "col": 1, "offset": 0}, "end": {"line": 46, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/authorization-area.html:1:\n `{% extends \"page.html\" %}\n\n{% block main %}` was unexpected", "path": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/authorization-area.html", "spans": [{"file": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/authorization-area.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 17, "offset": 43}}, {"file": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/authorization-area.html", "start": {"line": 46, "col": 1, "offset": 0}, "end": {"line": 46, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/my_message.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 17, "offset": 43}}, {"path": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/my_message.html", "start": {"line": 11, "col": 1, "offset": 0}, "end": {"line": 11, "col": 20, "offset": 19}}]], "message": "Syntax error at line downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/my_message.html:1:\n `{% extends \"page.html\" %}\n\n{% block main %}` was unexpected", "path": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/my_message.html", "spans": [{"file": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/my_message.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 17, "offset": 43}}, {"file": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/my_message.html", "start": {"line": 11, "col": 1, "offset": 0}, "end": {"line": 11, "col": 20, "offset": 19}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/native-login.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 8, "col": 14, "offset": 146}}, {"path": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/native-login.html", "start": {"line": 30, "col": 1, "offset": 0}, "end": {"line": 33, "col": 18, "offset": 57}}, {"path": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/native-login.html", "start": {"line": 83, "col": 1, "offset": 0}, "end": {"line": 84, "col": 20, "offset": 40}}]], "message": "Syntax error at line downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/native-login.html:1:\n `{% extends \"page.html\" %}\n\n{% if announcement_login %}\n  {% set announcement = announcement_login %}\n{% endif %}\n\n{% block script %}\n{{ super() }}` was unexpected", "path": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/native-login.html", "spans": [{"file": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/native-login.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 8, "col": 14, "offset": 146}}, {"file": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/native-login.html", "start": {"line": 30, "col": 1, "offset": 0}, "end": {"line": 33, "col": 18, "offset": 57}}, {"file": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/native-login.html", "start": {"line": 83, "col": 1, "offset": 0}, "end": {"line": 84, "col": 20, "offset": 40}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/page.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 18, "offset": 85}}, {"path": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/page.html", "start": {"line": 6, "col": 5, "offset": 0}, "end": {"line": 6, "col": 24, "offset": 19}}, {"path": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/page.html", "start": {"line": 8, "col": 5, "offset": 0}, "end": {"line": 9, "col": 15, "offset": 26}}]], "message": "Syntax error at line downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/page.html:1:\n `{% extends \"templates/page.html\" %}\n\n{% block nav_bar_left_items %}\n    {{ super() }}` was unexpected", "path": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/page.html", "spans": [{"file": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/page.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 18, "offset": 85}}, {"file": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/page.html", "start": {"line": 6, "col": 5, "offset": 0}, "end": {"line": 6, "col": 24, "offset": 19}}, {"file": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/page.html", "start": {"line": 8, "col": 5, "offset": 0}, "end": {"line": 9, "col": 15, "offset": 26}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/signup.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 14, "offset": 59}}, {"path": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/signup.html", "start": {"line": 41, "col": 1, "offset": 0}, "end": {"line": 41, "col": 23, "offset": 22}}, {"path": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/signup.html", "start": {"line": 43, "col": 1, "offset": 0}, "end": {"line": 48, "col": 18, "offset": 70}}, {"path": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/signup.html", "start": {"line": 129, "col": 1, "offset": 0}, "end": {"line": 130, "col": 20, "offset": 40}}]], "message": "Syntax error at line downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/signup.html:1:\n `{% extends \"page.html\" %}\n\n{% block script %}\n{{ super() }}` was unexpected", "path": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/signup.html", "spans": [{"file": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/signup.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 14, "offset": 59}}, {"file": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/signup.html", "start": {"line": 41, "col": 1, "offset": 0}, "end": {"line": 41, "col": 23, "offset": 22}}, {"file": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/signup.html", "start": {"line": 43, "col": 1, "offset": 0}, "end": {"line": 48, "col": 18, "offset": 70}}, {"file": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/signup.html", "start": {"line": 129, "col": 1, "offset": 0}, "end": {"line": 130, "col": 20, "offset": 40}}]}], "paths": {"scanned": ["downloaded_repos/jupyterhub_nativeauthenticator/.flake8", "downloaded_repos/jupyterhub_nativeauthenticator/.github/dependabot.yaml", "downloaded_repos/jupyterhub_nativeauthenticator/.github/workflows/release.yaml", "downloaded_repos/jupyterhub_nativeauthenticator/.github/workflows/test.yaml", "downloaded_repos/jupyterhub_nativeauthenticator/.gitignore", "downloaded_repos/jupyterhub_nativeauthenticator/.pre-commit-config.yaml", "downloaded_repos/jupyterhub_nativeauthenticator/.prettierignore", "downloaded_repos/jupyterhub_nativeauthenticator/.readthedocs.yaml", "downloaded_repos/jupyterhub_nativeauthenticator/CHANGELOG.md", "downloaded_repos/jupyterhub_nativeauthenticator/CONTRIBUTING.md", "downloaded_repos/jupyterhub_nativeauthenticator/LICENSE", "downloaded_repos/jupyterhub_nativeauthenticator/MANIFEST.in", "downloaded_repos/jupyterhub_nativeauthenticator/README.md", "downloaded_repos/jupyterhub_nativeauthenticator/RELEASE.md", "downloaded_repos/jupyterhub_nativeauthenticator/dev-jupyterhub_config.py", "downloaded_repos/jupyterhub_nativeauthenticator/docs/Makefile", "downloaded_repos/jupyterhub_nativeauthenticator/docs/make.bat", "downloaded_repos/jupyterhub_nativeauthenticator/docs/requirements.txt", "downloaded_repos/jupyterhub_nativeauthenticator/docs/source/_static/authorization_area.png", "downloaded_repos/jupyterhub_nativeauthenticator/docs/source/_static/change_password_self.png", "downloaded_repos/jupyterhub_nativeauthenticator/docs/source/_static/change_password_user.png", "downloaded_repos/jupyterhub_nativeauthenticator/docs/source/_static/login-two-factor-auth.png", "downloaded_repos/jupyterhub_nativeauthenticator/docs/source/_static/signup-two-factor-auth.png", "downloaded_repos/jupyterhub_nativeauthenticator/docs/source/_static/wrong_signup.png", "downloaded_repos/jupyterhub_nativeauthenticator/docs/source/conf.py", "downloaded_repos/jupyterhub_nativeauthenticator/docs/source/index.md", "downloaded_repos/jupyterhub_nativeauthenticator/docs/source/options.md", "downloaded_repos/jupyterhub_nativeauthenticator/docs/source/quickstart.md", "downloaded_repos/jupyterhub_nativeauthenticator/docs/source/troubleshooting.md", "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/__init__.py", "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/common-credentials.txt", "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/crypto/LICENSE", "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/crypto/__init__.py", "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/crypto/crypto.py", "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/crypto/encoding.py", "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/crypto/signing.py", "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/handlers.py", "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/nativeauthenticator.py", "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/orm.py", "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/authorization-area.html", "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/change-password-admin.html", "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/change-password.html", "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/my_message.html", "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/native-login.html", "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/page.html", "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/signup.html", "downloaded_repos/jupyterhub_nativeauthenticator/pyproject.toml", "downloaded_repos/jupyterhub_nativeauthenticator/setup.py"], "skipped": [{"path": "downloaded_repos/jupyterhub_nativeauthenticator/docs/source/_static/block_user_failed_logins.png", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/jupyterhub_nativeauthenticator/docs/source/_static/native_auth_flow.png", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/authorization-area.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/my_message.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/native-login.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/page.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/templates/signup.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/tests/conftest.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/tests/test_authenticator.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/tests/test_orm.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 2.1390461921691895, "profiling_times": {"config_time": 7.626544952392578, "core_time": 3.680849313735962, "ignores_time": 0.004054069519042969, "total_time": 11.312590837478638}, "parsing_time": {"total_time": 0.585766077041626, "per_file_time": {"mean": 0.027893622716267902, "std_dev": 0.0005457277883748709}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 3.4719488620758057, "per_file_time": {"mean": 0.029176040857779875, "std_dev": 0.005793939567310797}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.6452276706695557, "per_file_and_rule_time": {"mean": 0.0024627010330899083, "std_dev": 7.465053410948637e-05}, "very_slow_stats": {"time_ratio": 0.16403655052819718, "count_ratio": 0.003816793893129771}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/jupyterhub_nativeauthenticator/nativeauthenticator/handlers.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.10584092140197754}]}, "tainting_time": {"total_time": 0.09903335571289062, "per_def_and_rule_time": {"mean": 0.0003438658184475369, "std_dev": 5.208074247968574e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089364160}, "engine_requested": "OSS", "skipped_rules": []}