🐳 UNIFIED VULNERABILITY SCANNER - CONTAINER MODE
============================================================
Running inside Docker container with integrated Semgrep

✅ Using direct Semgrep execution (container mode)
🔍 Searching GitHub for vulnerable repositories...
  📋 Scanning PHP repositories...
    ⚠️ Rate limited, waiting...
    ⚠️ Rate limited, waiting...
  📋 Scanning JavaScript repositories...
    ⚠️ Rate limited, waiting...
    ⚠️ Rate limited, waiting...
    ⚠️ Rate limited, waiting...
  📋 Scanning Python repositories...
    ⚠️ Rate limited, waiting...
    ⚠️ Rate limited, waiting...
  📋 Scanning Ruby repositories...
    ⚠️ Rate limited, waiting...
    ⚠️ Rate limited, waiting...
    ⚠️ Rate limited, waiting...
  📋 Scanning Java repositories...
    ⚠️ Rate limited, waiting...
    ⚠️ Rate limited, waiting...
  ✅ Found 439 potentially vulnerable repositories
🎯 Limited to 300 repositories for analysis (will keep top 10)

🚀 Processing 300 repositories...
   Max concurrent scans: 1

🎯 Processing: uasoft-indonesia/badaso
    🔗 Clone URL: https://github.com/uasoft-indonesia/badaso.git
    📥 Cloning uasoft-indonesia/badaso...
    ✅ Successfully cloned uasoft-indonesia/badaso
    🔍 Running Semgrep on uasoft-indonesia/badaso...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/uasoft-indonesia_badaso_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 88 potential vulnerabilities
    🧹 Cleaned up uasoft-indonesia/badaso
    ✅ uasoft-indonesia/badaso - Final Score: 100.0 (Risk: 59, Semgrep: 100)

🎯 Processing: moonshine-software/moonshine
    🔗 Clone URL: https://github.com/moonshine-software/moonshine.git
    📥 Cloning moonshine-software/moonshine...  ✅ Completed: uasoft-indonesia/badaso (Score: 100.0)

    ✅ Successfully cloned moonshine-software/moonshine
    🔍 Running Semgrep on moonshine-software/moonshine...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/moonshine-software_moonshine_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 16 potential vulnerabilities
    🧹 Cleaned up moonshine-software/moonshine
    ✅ moonshine-software/moonshine - Final Score: 100.0 (Risk: 56, Semgrep: 100)

🎯 Processing: alperenersoy/filament-export
    🔗 Clone URL: https://github.com/alperenersoy/filament-export.git
    📥 Cloning alperenersoy/filament-export...
  ✅ Completed: moonshine-software/moonshine (Score: 100.0)
    ✅ Successfully cloned alperenersoy/filament-export
    🔍 Running Semgrep on alperenersoy/filament-export...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/alperenersoy_filament-export_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up alperenersoy/filament-export
    ✅ alperenersoy/filament-export - Final Score: 44.0 (Risk: 44, Semgrep: 0)

🎯 Processing: stephenjude/filament-blog  ✅ Completed: alperenersoy/filament-export (Score: 44.0)

    🔗 Clone URL: https://github.com/stephenjude/filament-blog.git
    📥 Cloning stephenjude/filament-blog...
    ✅ Successfully cloned stephenjude/filament-blog
    🔍 Running Semgrep on stephenjude/filament-blog...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/stephenjude_filament-blog_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up stephenjude/filament-blog
    ✅ stephenjude/filament-blog - Final Score: 40.0 (Risk: 40, Semgrep: 0)

🎯 Processing: bezhanSalleh/filament-exceptions
    🔗 Clone URL: https://github.com/bezhanSalleh/filament-exceptions.git
    📥 Cloning bezhanSalleh/filament-exceptions...
  ✅ Completed: stephenjude/filament-blog (Score: 40.0)
    ✅ Successfully cloned bezhanSalleh/filament-exceptions
    🔍 Running Semgrep on bezhanSalleh/filament-exceptions...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/bezhanSalleh_filament-exceptions_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up bezhanSalleh/filament-exceptions
    ✅ bezhanSalleh/filament-exceptions - Final Score: 40.0 (Risk: 40, Semgrep: 0)

🎯 Processing: JibayMcs/filament-tour  ✅ Completed: bezhanSalleh/filament-exceptions (Score: 40.0)

    🔗 Clone URL: https://github.com/JibayMcs/filament-tour.git
    📥 Cloning JibayMcs/filament-tour...
    ✅ Successfully cloned JibayMcs/filament-tour
    🔍 Running Semgrep on JibayMcs/filament-tour...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/JibayMcs_filament-tour_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 4 potential vulnerabilities
    🧹 Cleaned up JibayMcs/filament-tour
    ✅ JibayMcs/filament-tour - Final Score: 50.5 (Risk: 35, Semgrep: 31)

🎯 Processing: et-nik/gameap
    🔗 Clone URL: https://github.com/et-nik/gameap.git
    📥 Cloning et-nik/gameap...
  ✅ Completed: JibayMcs/filament-tour (Score: 50.5)
    ✅ Successfully cloned et-nik/gameap
    🔍 Running Semgrep on et-nik/gameap...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/et-nik_gameap_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 24 potential vulnerabilities
    🧹 Cleaned up et-nik/gameap
    ✅ et-nik/gameap - Final Score: 90.0 (Risk: 40, Semgrep: 100)

🎯 Processing: saade/filament-laravel-log
    🔗 Clone URL: https://github.com/saade/filament-laravel-log.git
    📥 Cloning saade/filament-laravel-log...
  ✅ Completed: et-nik/gameap (Score: 90.0)
    ✅ Successfully cloned saade/filament-laravel-log
    🔍 Running Semgrep on saade/filament-laravel-log...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/saade_filament-laravel-log_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up saade/filament-laravel-log
    ✅ saade/filament-laravel-log - Final Score: 40.5 (Risk: 40, Semgrep: 1)

🎯 Processing: aymanalhattami/filament-context-menu  ✅ Completed: saade/filament-laravel-log (Score: 40.5)
    🔗 Clone URL: https://github.com/aymanalhattami/filament-context-menu.git
    📥 Cloning aymanalhattami/filament-context-menu...

    ✅ Successfully cloned aymanalhattami/filament-context-menu
    🔍 Running Semgrep on aymanalhattami/filament-context-menu...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/aymanalhattami_filament-context-menu_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 5 potential vulnerabilities
    🧹 Cleaned up aymanalhattami/filament-context-menu
    ✅ aymanalhattami/filament-context-menu - Final Score: 38.0 (Risk: 25, Semgrep: 26)

🎯 Processing: stephenjude/filament-debugger
    🔗 Clone URL: https://github.com/stephenjude/filament-debugger.git
    📥 Cloning stephenjude/filament-debugger...
  ✅ Completed: aymanalhattami/filament-context-menu (Score: 38.0)
    ✅ Successfully cloned stephenjude/filament-debugger
    🔍 Running Semgrep on stephenjude/filament-debugger...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/stephenjude_filament-debugger_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up stephenjude/filament-debugger
    ✅ stephenjude/filament-debugger - Final Score: 30.0 (Risk: 30, Semgrep: 0)

🎯 Processing: codepress/admin-columns
    🔗 Clone URL: https://github.com/codepress/admin-columns.git
    📥 Cloning codepress/admin-columns...
  ✅ Completed: stephenjude/filament-debugger (Score: 30.0)
    ✅ Successfully cloned codepress/admin-columns
    🔍 Running Semgrep on codepress/admin-columns...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/codepress_admin-columns_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 36 potential vulnerabilities
    🧹 Cleaned up codepress/admin-columns
    ✅ codepress/admin-columns - Final Score: 83.0 (Risk: 33, Semgrep: 100)

🎯 Processing: ashraf-kabir/personal-blog
    🔗 Clone URL: https://github.com/ashraf-kabir/personal-blog.git
    📥 Cloning ashraf-kabir/personal-blog...
  ✅ Completed: codepress/admin-columns (Score: 83.0)
    ✅ Successfully cloned ashraf-kabir/personal-blog
    🔍 Running Semgrep on ashraf-kabir/personal-blog...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/ashraf-kabir_personal-blog_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 6 potential vulnerabilities
    🧹 Cleaned up ashraf-kabir/personal-blog
    ✅ ashraf-kabir/personal-blog - Final Score: 58.5 (Risk: 36, Semgrep: 45)

🎯 Processing: fsi-open/admin-bundle
    🔗 Clone URL: https://github.com/fsi-open/admin-bundle.git
    📥 Cloning fsi-open/admin-bundle...
  ✅ Completed: ashraf-kabir/personal-blog (Score: 58.5)
    ✅ Successfully cloned fsi-open/admin-bundle
    🔍 Running Semgrep on fsi-open/admin-bundle...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/fsi-open_admin-bundle_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 8 potential vulnerabilities
    🧹 Cleaned up fsi-open/admin-bundle
    ✅ fsi-open/admin-bundle - Final Score: 45.0 (Risk: 25, Semgrep: 40)

🎯 Processing: solutionforest/filament-scaffold  ✅ Completed: fsi-open/admin-bundle (Score: 45.0)

    🔗 Clone URL: https://github.com/solutionforest/filament-scaffold.git
    📥 Cloning solutionforest/filament-scaffold...
    ✅ Successfully cloned solutionforest/filament-scaffold
    🔍 Running Semgrep on solutionforest/filament-scaffold...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/solutionforest_filament-scaffold_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up solutionforest/filament-scaffold
    ✅ solutionforest/filament-scaffold - Final Score: 13.5 (Risk: 13, Semgrep: 1)

🎯 Processing: dustin10/VichUploaderBundle
    🔗 Clone URL: https://github.com/dustin10/VichUploaderBundle.git
    📥 Cloning dustin10/VichUploaderBundle...
  ✅ Completed: solutionforest/filament-scaffold (Score: 13.5)
    ✅ Successfully cloned dustin10/VichUploaderBundle
    🔍 Running Semgrep on dustin10/VichUploaderBundle...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/dustin10_VichUploaderBundle_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up dustin10/VichUploaderBundle
    ✅ dustin10/VichUploaderBundle - Final Score: 30.0 (Risk: 30, Semgrep: 0)

🎯 Processing: spatie/livewire-filepond
    🔗 Clone URL: https://github.com/spatie/livewire-filepond.git
    📥 Cloning spatie/livewire-filepond...  ✅ Completed: dustin10/VichUploaderBundle (Score: 30.0)

    ✅ Successfully cloned spatie/livewire-filepond
    🔍 Running Semgrep on spatie/livewire-filepond...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/spatie_livewire-filepond_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up spatie/livewire-filepond
    ✅ spatie/livewire-filepond - Final Score: 31.0 (Risk: 31, Semgrep: 0)

🎯 Processing: kleeja-official/kleeja
    🔗 Clone URL: https://github.com/kleeja-official/kleeja.git
    📥 Cloning kleeja-official/kleeja...
  ✅ Completed: spatie/livewire-filepond (Score: 31.0)
    ✅ Successfully cloned kleeja-official/kleeja
    🔍 Running Semgrep on kleeja-official/kleeja...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/kleeja-official_kleeja_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 83 potential vulnerabilities
    🧹 Cleaned up kleeja-official/kleeja
    ✅ kleeja-official/kleeja - Final Score: 85.0 (Risk: 35, Semgrep: 100)

🎯 Processing: Ecodev/graphql-upload  ✅ Completed: kleeja-official/kleeja (Score: 85.0)
    🔗 Clone URL: https://github.com/Ecodev/graphql-upload.git
    📥 Cloning Ecodev/graphql-upload...

    ✅ Successfully cloned Ecodev/graphql-upload
    🔍 Running Semgrep on Ecodev/graphql-upload...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/Ecodev_graphql-upload_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up Ecodev/graphql-upload
    ✅ Ecodev/graphql-upload - Final Score: 25.0 (Risk: 25, Semgrep: 0)

🎯 Processing: mostafaznv/larupload  ✅ Completed: Ecodev/graphql-upload (Score: 25.0)
    🔗 Clone URL: https://github.com/mostafaznv/larupload.git
    📥 Cloning mostafaznv/larupload...

    ✅ Successfully cloned mostafaznv/larupload
    🔍 Running Semgrep on mostafaznv/larupload...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/mostafaznv_larupload_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 4 potential vulnerabilities
    🧹 Cleaned up mostafaznv/larupload
    ✅ mostafaznv/larupload - Final Score: 35.0 (Risk: 25, Semgrep: 20)

🎯 Processing: TypiCMS/Base  ✅ Completed: mostafaznv/larupload (Score: 35.0)
    🔗 Clone URL: https://github.com/TypiCMS/Base.git
    📥 Cloning TypiCMS/Base...

    ✅ Successfully cloned TypiCMS/Base
    🔍 Running Semgrep on TypiCMS/Base...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/TypiCMS_Base_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up TypiCMS/Base
    ✅ TypiCMS/Base - Final Score: 28.0 (Risk: 28, Semgrep: 0)

🎯 Processing: danpros/htmly
    🔗 Clone URL: https://github.com/danpros/htmly.git
    📥 Cloning danpros/htmly...
  ✅ Completed: TypiCMS/Base (Score: 28.0)
    ✅ Successfully cloned danpros/htmly
    🔍 Running Semgrep on danpros/htmly...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/danpros_htmly_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 93 potential vulnerabilities
    🧹 Cleaned up danpros/htmly
    ✅ danpros/htmly - Final Score: 78.0 (Risk: 28, Semgrep: 100)

🎯 Processing: processwire/processwire
    🔗 Clone URL: https://github.com/processwire/processwire.git
    📥 Cloning processwire/processwire...
  ✅ Completed: danpros/htmly (Score: 78.0)
    ✅ Successfully cloned processwire/processwire
    🔍 Running Semgrep on processwire/processwire...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/processwire_processwire_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 101 potential vulnerabilities
    🧹 Cleaned up processwire/processwire
    ✅ processwire/processwire - Final Score: 88.0 (Risk: 38, Semgrep: 100)

🎯 Processing: ClassicPress/ClassicPress
    🔗 Clone URL: https://github.com/ClassicPress/ClassicPress.git
    📥 Cloning ClassicPress/ClassicPress...
  ✅ Completed: processwire/processwire (Score: 88.0)
    ✅ Successfully cloned ClassicPress/ClassicPress
    🔍 Running Semgrep on ClassicPress/ClassicPress...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/ClassicPress_ClassicPress_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 340 potential vulnerabilities
    🧹 Cleaned up ClassicPress/ClassicPress
    ✅ ClassicPress/ClassicPress - Final Score: 91.0 (Risk: 41, Semgrep: 100)

🎯 Processing: concretecms/concretecms  ✅ Completed: ClassicPress/ClassicPress (Score: 91.0)

    🔗 Clone URL: https://github.com/concretecms/concretecms.git
    📥 Cloning concretecms/concretecms...
    ✅ Successfully cloned concretecms/concretecms
    🔍 Running Semgrep on concretecms/concretecms...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/concretecms_concretecms_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 597 potential vulnerabilities
    🧹 Cleaned up concretecms/concretecms
    ✅ concretecms/concretecms - Final Score: 88.0 (Risk: 38, Semgrep: 100)

🎯 Processing: s-cart/s-cart  ✅ Completed: concretecms/concretecms (Score: 88.0)

    🔗 Clone URL: https://github.com/s-cart/s-cart.git
    📥 Cloning s-cart/s-cart...
    ✅ Successfully cloned s-cart/s-cart
    🔍 Running Semgrep on s-cart/s-cart...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/s-cart_s-cart_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up s-cart/s-cart
    ✅ s-cart/s-cart - Final Score: 33.0 (Risk: 33, Semgrep: 0)

🎯 Processing: WonderCMS/wondercms
    🔗 Clone URL: https://github.com/WonderCMS/wondercms.git
    📥 Cloning WonderCMS/wondercms...
  ✅ Completed: s-cart/s-cart (Score: 33.0)
    ✅ Successfully cloned WonderCMS/wondercms
    🔍 Running Semgrep on WonderCMS/wondercms...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/WonderCMS_wondercms_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 10 potential vulnerabilities
    🧹 Cleaned up WonderCMS/wondercms
    ✅ WonderCMS/wondercms - Final Score: 53.0 (Risk: 28, Semgrep: 50)

🎯 Processing: aimeos/ai-cms-grapesjs  ✅ Completed: WonderCMS/wondercms (Score: 53.0)

    🔗 Clone URL: https://github.com/aimeos/ai-cms-grapesjs.git
    📥 Cloning aimeos/ai-cms-grapesjs...
    ✅ Successfully cloned aimeos/ai-cms-grapesjs
    🔍 Running Semgrep on aimeos/ai-cms-grapesjs...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/aimeos_ai-cms-grapesjs_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 2 potential vulnerabilities
    🧹 Cleaned up aimeos/ai-cms-grapesjs
    ✅ aimeos/ai-cms-grapesjs - Final Score: 38.0 (Risk: 33, Semgrep: 10)

🎯 Processing: webreinvent/vaahcms
    🔗 Clone URL: https://github.com/webreinvent/vaahcms.git
    📥 Cloning webreinvent/vaahcms...
  ✅ Completed: aimeos/ai-cms-grapesjs (Score: 38.0)
    ✅ Successfully cloned webreinvent/vaahcms
    🔍 Running Semgrep on webreinvent/vaahcms...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/webreinvent_vaahcms_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 29 potential vulnerabilities
    🧹 Cleaned up webreinvent/vaahcms
    ✅ webreinvent/vaahcms - Final Score: 83.0 (Risk: 33, Semgrep: 100)

🎯 Processing: oc-shopaholic/oc-shopaholic-plugin
    🔗 Clone URL: https://github.com/oc-shopaholic/oc-shopaholic-plugin.git
    📥 Cloning oc-shopaholic/oc-shopaholic-plugin...  ✅ Completed: webreinvent/vaahcms (Score: 83.0)

    ✅ Successfully cloned oc-shopaholic/oc-shopaholic-plugin
    🔍 Running Semgrep on oc-shopaholic/oc-shopaholic-plugin...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/oc-shopaholic_oc-shopaholic-plugin_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up oc-shopaholic/oc-shopaholic-plugin
    ✅ oc-shopaholic/oc-shopaholic-plugin - Final Score: 37.0 (Risk: 37, Semgrep: 0)

🎯 Processing: spicywebau/craft-neo
    🔗 Clone URL: https://github.com/spicywebau/craft-neo.git
    📥 Cloning spicywebau/craft-neo...  ✅ Completed: oc-shopaholic/oc-shopaholic-plugin (Score: 37.0)

    ✅ Successfully cloned spicywebau/craft-neo
    🔍 Running Semgrep on spicywebau/craft-neo...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/spicywebau_craft-neo_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 2 potential vulnerabilities
    🧹 Cleaned up spicywebau/craft-neo
    ✅ spicywebau/craft-neo - Final Score: 33.0 (Risk: 28, Semgrep: 10)

🎯 Processing: redaxo/redaxo
    🔗 Clone URL: https://github.com/redaxo/redaxo.git
    📥 Cloning redaxo/redaxo...  ✅ Completed: spicywebau/craft-neo (Score: 33.0)

    ✅ Successfully cloned redaxo/redaxo
    🔍 Running Semgrep on redaxo/redaxo...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/redaxo_redaxo_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 48 potential vulnerabilities
    🧹 Cleaned up redaxo/redaxo
    ✅ redaxo/redaxo - Final Score: 88.0 (Risk: 38, Semgrep: 100)

🎯 Processing: e107inc/e107
  ✅ Completed: redaxo/redaxo (Score: 88.0)    🔗 Clone URL: https://github.com/e107inc/e107.git
    📥 Cloning e107inc/e107...

    ✅ Successfully cloned e107inc/e107
    🔍 Running Semgrep on e107inc/e107...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/e107inc_e107_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 289 potential vulnerabilities
    🧹 Cleaned up e107inc/e107
    ✅ e107inc/e107 - Final Score: 94.0 (Risk: 44, Semgrep: 100)

🎯 Processing: zenphoto/zenphoto
    🔗 Clone URL: https://github.com/zenphoto/zenphoto.git
    📥 Cloning zenphoto/zenphoto...  ✅ Completed: e107inc/e107 (Score: 94.0)

    ✅ Successfully cloned zenphoto/zenphoto
    🔍 Running Semgrep on zenphoto/zenphoto...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/zenphoto_zenphoto_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 353 potential vulnerabilities
    🧹 Cleaned up zenphoto/zenphoto
    ✅ zenphoto/zenphoto - Final Score: 86.0 (Risk: 36, Semgrep: 100)

🎯 Processing: craftcms/commerce
    🔗 Clone URL: https://github.com/craftcms/commerce.git
    📥 Cloning craftcms/commerce...
  ✅ Completed: zenphoto/zenphoto (Score: 86.0)
    ✅ Successfully cloned craftcms/commerce
    🔍 Running Semgrep on craftcms/commerce...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/craftcms_commerce_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 29 potential vulnerabilities
    🧹 Cleaned up craftcms/commerce
    ✅ craftcms/commerce - Final Score: 78.0 (Risk: 28, Semgrep: 100)

🎯 Processing: pluxml/PluXml  ✅ Completed: craftcms/commerce (Score: 78.0)

    🔗 Clone URL: https://github.com/pluxml/PluXml.git
    📥 Cloning pluxml/PluXml...
    ✅ Successfully cloned pluxml/PluXml
    🔍 Running Semgrep on pluxml/PluXml...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/pluxml_PluXml_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 190 potential vulnerabilities
    🧹 Cleaned up pluxml/PluXml
    ✅ pluxml/PluXml - Final Score: 78.0 (Risk: 28, Semgrep: 100)

🎯 Processing: lara-zeus/sky  ✅ Completed: pluxml/PluXml (Score: 78.0)

    🔗 Clone URL: https://github.com/lara-zeus/sky.git
    📥 Cloning lara-zeus/sky...
    ✅ Successfully cloned lara-zeus/sky
    🔍 Running Semgrep on lara-zeus/sky...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/lara-zeus_sky_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up lara-zeus/sky
    ✅ lara-zeus/sky - Final Score: 44.0 (Risk: 44, Semgrep: 0)

🎯 Processing: rainlab/builder-plugin  ✅ Completed: lara-zeus/sky (Score: 44.0)

    🔗 Clone URL: https://github.com/rainlab/builder-plugin.git
    📥 Cloning rainlab/builder-plugin...
    ✅ Successfully cloned rainlab/builder-plugin
    🔍 Running Semgrep on rainlab/builder-plugin...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/rainlab_builder-plugin_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 12 potential vulnerabilities
    🧹 Cleaned up rainlab/builder-plugin
    ✅ rainlab/builder-plugin - Final Score: 63.0 (Risk: 28, Semgrep: 70)

🎯 Processing: spicywebau/craft-embedded-assets  ✅ Completed: rainlab/builder-plugin (Score: 63.0)

    🔗 Clone URL: https://github.com/spicywebau/craft-embedded-assets.git
    📥 Cloning spicywebau/craft-embedded-assets...
    ✅ Successfully cloned spicywebau/craft-embedded-assets
    🔍 Running Semgrep on spicywebau/craft-embedded-assets...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/spicywebau_craft-embedded-assets_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up spicywebau/craft-embedded-assets
    ✅ spicywebau/craft-embedded-assets - Final Score: 28.0 (Risk: 28, Semgrep: 0)

🎯 Processing: nystudio107/craft-seomatic
    🔗 Clone URL: https://github.com/nystudio107/craft-seomatic.git
    📥 Cloning nystudio107/craft-seomatic...
  ✅ Completed: spicywebau/craft-embedded-assets (Score: 28.0)
    ✅ Successfully cloned nystudio107/craft-seomatic
    🔍 Running Semgrep on nystudio107/craft-seomatic...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/nystudio107_craft-seomatic_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 10 potential vulnerabilities
    🧹 Cleaned up nystudio107/craft-seomatic
    ✅ nystudio107/craft-seomatic - Final Score: 70.5 (Risk: 33, Semgrep: 75)

🎯 Processing: nukeviet/nukeviet  ✅ Completed: nystudio107/craft-seomatic (Score: 70.5)
    🔗 Clone URL: https://github.com/nukeviet/nukeviet.git

    📥 Cloning nukeviet/nukeviet...
    ✅ Successfully cloned nukeviet/nukeviet
    🔍 Running Semgrep on nukeviet/nukeviet...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/nukeviet_nukeviet_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 400 potential vulnerabilities
    🧹 Cleaned up nukeviet/nukeviet
    ✅ nukeviet/nukeviet - Final Score: 86.0 (Risk: 36, Semgrep: 100)

🎯 Processing: putyourlightson/craft-blitz
    🔗 Clone URL: https://github.com/putyourlightson/craft-blitz.git
    📥 Cloning putyourlightson/craft-blitz...
  ✅ Completed: nukeviet/nukeviet (Score: 86.0)
    ✅ Successfully cloned putyourlightson/craft-blitz
    🔍 Running Semgrep on putyourlightson/craft-blitz...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/putyourlightson_craft-blitz_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 6 potential vulnerabilities
    🧹 Cleaned up putyourlightson/craft-blitz
    ✅ putyourlightson/craft-blitz - Final Score: 53.0 (Risk: 28, Semgrep: 50)

🎯 Processing: maxsite/cms
    🔗 Clone URL: https://github.com/maxsite/cms.git
    📥 Cloning maxsite/cms...
  ✅ Completed: putyourlightson/craft-blitz (Score: 53.0)
    ✅ Successfully cloned maxsite/cms
    🔍 Running Semgrep on maxsite/cms...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/maxsite_cms_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 181 potential vulnerabilities
    🧹 Cleaned up maxsite/cms
    ✅ maxsite/cms - Final Score: 86.0 (Risk: 36, Semgrep: 100)

🎯 Processing: nextcloud/cms_pico  ✅ Completed: maxsite/cms (Score: 86.0)

    🔗 Clone URL: https://github.com/nextcloud/cms_pico.git
    📥 Cloning nextcloud/cms_pico...
    ✅ Successfully cloned nextcloud/cms_pico
    🔍 Running Semgrep on nextcloud/cms_pico...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/nextcloud_cms_pico_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 2 potential vulnerabilities
    🧹 Cleaned up nextcloud/cms_pico
    ✅ nextcloud/cms_pico - Final Score: 33.0 (Risk: 28, Semgrep: 10)

🎯 Processing: lautaroangelico/WebEngine
    🔗 Clone URL: https://github.com/lautaroangelico/WebEngine.git
    📥 Cloning lautaroangelico/WebEngine...
  ✅ Completed: nextcloud/cms_pico (Score: 33.0)
    ✅ Successfully cloned lautaroangelico/WebEngine
    🔍 Running Semgrep on lautaroangelico/WebEngine...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/lautaroangelico_WebEngine_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 30 potential vulnerabilities
    🧹 Cleaned up lautaroangelico/WebEngine
    ✅ lautaroangelico/WebEngine - Final Score: 78.0 (Risk: 28, Semgrep: 100)

🎯 Processing: haxtheweb/haxcms-php  ✅ Completed: lautaroangelico/WebEngine (Score: 78.0)

    🔗 Clone URL: https://github.com/haxtheweb/haxcms-php.git
    📥 Cloning haxtheweb/haxcms-php...
    ✅ Successfully cloned haxtheweb/haxcms-php
    🔍 Running Semgrep on haxtheweb/haxcms-php...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/haxtheweb_haxcms-php_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 63 potential vulnerabilities
    🧹 Cleaned up haxtheweb/haxcms-php
    ✅ haxtheweb/haxcms-php - Final Score: 78.0 (Risk: 28, Semgrep: 100)

🎯 Processing: silverstripe/silverstripe-elemental
    🔗 Clone URL: https://github.com/silverstripe/silverstripe-elemental.git
    📥 Cloning silverstripe/silverstripe-elemental...  ✅ Completed: haxtheweb/haxcms-php (Score: 78.0)

    ✅ Successfully cloned silverstripe/silverstripe-elemental
    🔍 Running Semgrep on silverstripe/silverstripe-elemental...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/silverstripe_silverstripe-elemental_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up silverstripe/silverstripe-elemental
    ✅ silverstripe/silverstripe-elemental - Final Score: 38.0 (Risk: 38, Semgrep: 0)

🎯 Processing: FusionGen/FusionGEN
    🔗 Clone URL: https://github.com/FusionGen/FusionGEN.git
    📥 Cloning FusionGen/FusionGEN...
  ✅ Completed: silverstripe/silverstripe-elemental (Score: 38.0)
    ✅ Successfully cloned FusionGen/FusionGEN
    🔍 Running Semgrep on FusionGen/FusionGEN...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/FusionGen_FusionGEN_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 177 potential vulnerabilities
    🧹 Cleaned up FusionGen/FusionGEN
    ✅ FusionGen/FusionGEN - Final Score: 83.0 (Risk: 33, Semgrep: 100)

🎯 Processing: concretecms-community-store/community_store
    🔗 Clone URL: https://github.com/concretecms-community-store/community_store.git
    📥 Cloning concretecms-community-store/community_store...
  ✅ Completed: FusionGen/FusionGEN (Score: 83.0)
    ✅ Successfully cloned concretecms-community-store/community_store
    🔍 Running Semgrep on concretecms-community-store/community_store...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/concretecms-community-store_community_store_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 4 potential vulnerabilities
    🧹 Cleaned up concretecms-community-store/community_store
    ✅ concretecms-community-store/community_store - Final Score: 48.0 (Risk: 33, Semgrep: 30)

🎯 Processing: Flute-CMS/cms  ✅ Completed: concretecms-community-store/community_store (Score: 48.0)
    🔗 Clone URL: https://github.com/Flute-CMS/cms.git

    📥 Cloning Flute-CMS/cms...
    ✅ Successfully cloned Flute-CMS/cms
    🔍 Running Semgrep on Flute-CMS/cms...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/Flute-CMS_cms_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 104 potential vulnerabilities
    🧹 Cleaned up Flute-CMS/cms
    ✅ Flute-CMS/cms - Final Score: 78.0 (Risk: 28, Semgrep: 100)

🎯 Processing: TeamEasy/EasyCMS
    🔗 Clone URL: https://github.com/TeamEasy/EasyCMS.git
    📥 Cloning TeamEasy/EasyCMS...
  ✅ Completed: Flute-CMS/cms (Score: 78.0)
    ✅ Successfully cloned TeamEasy/EasyCMS
    🔍 Running Semgrep on TeamEasy/EasyCMS...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/TeamEasy_EasyCMS_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 186 potential vulnerabilities
    🧹 Cleaned up TeamEasy/EasyCMS
    ✅ TeamEasy/EasyCMS - Final Score: 78.0 (Risk: 28, Semgrep: 100)

🎯 Processing: tomatophp/filament-cms  ✅ Completed: TeamEasy/EasyCMS (Score: 78.0)

    🔗 Clone URL: https://github.com/tomatophp/filament-cms.git
    📥 Cloning tomatophp/filament-cms...
    ✅ Successfully cloned tomatophp/filament-cms
    🔍 Running Semgrep on tomatophp/filament-cms...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/tomatophp_filament-cms_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up tomatophp/filament-cms
    ✅ tomatophp/filament-cms - Final Score: 28.0 (Risk: 28, Semgrep: 0)

🎯 Processing: Cotonti/Cotonti  ✅ Completed: tomatophp/filament-cms (Score: 28.0)
    🔗 Clone URL: https://github.com/Cotonti/Cotonti.git
    📥 Cloning Cotonti/Cotonti...

    ✅ Successfully cloned Cotonti/Cotonti
    🔍 Running Semgrep on Cotonti/Cotonti...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/Cotonti_Cotonti_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 64 potential vulnerabilities
    🧹 Cleaned up Cotonti/Cotonti
    ✅ Cotonti/Cotonti - Final Score: 78.0 (Risk: 28, Semgrep: 100)

🎯 Processing: arnoson/kirby-vite
    🔗 Clone URL: https://github.com/arnoson/kirby-vite.git
    📥 Cloning arnoson/kirby-vite...
  ✅ Completed: Cotonti/Cotonti (Score: 78.0)
    ✅ Successfully cloned arnoson/kirby-vite
    🔍 Running Semgrep on arnoson/kirby-vite...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/arnoson_kirby-vite_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up arnoson/kirby-vite
    ✅ arnoson/kirby-vite - Final Score: 23.0 (Risk: 23, Semgrep: 0)

🎯 Processing: enhavo/enhavo
    🔗 Clone URL: https://github.com/enhavo/enhavo.git
    📥 Cloning enhavo/enhavo...
  ✅ Completed: arnoson/kirby-vite (Score: 23.0)
    ✅ Successfully cloned enhavo/enhavo
    🔍 Running Semgrep on enhavo/enhavo...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/enhavo_enhavo_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 50 potential vulnerabilities
    🧹 Cleaned up enhavo/enhavo
    ✅ enhavo/enhavo - Final Score: 68.0 (Risk: 18, Semgrep: 100)

🎯 Processing: 10up/eight-day-week  ✅ Completed: enhavo/enhavo (Score: 68.0)

    🔗 Clone URL: https://github.com/10up/eight-day-week.git
    📥 Cloning 10up/eight-day-week...
    ✅ Successfully cloned 10up/eight-day-week
    🔍 Running Semgrep on 10up/eight-day-week...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/10up_eight-day-week_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up 10up/eight-day-week
    ✅ 10up/eight-day-week - Final Score: 26.0 (Risk: 26, Semgrep: 0)

🎯 Processing: FriendsOfTYPO3/content-blocks  ✅ Completed: 10up/eight-day-week (Score: 26.0)
    🔗 Clone URL: https://github.com/FriendsOfTYPO3/content-blocks.git
    📥 Cloning FriendsOfTYPO3/content-blocks...

    ✅ Successfully cloned FriendsOfTYPO3/content-blocks
    🔍 Running Semgrep on FriendsOfTYPO3/content-blocks...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/FriendsOfTYPO3_content-blocks_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up FriendsOfTYPO3/content-blocks
    ✅ FriendsOfTYPO3/content-blocks - Final Score: 26.0 (Risk: 26, Semgrep: 0)

🎯 Processing: PHPVibe/PHPVibe-Video-Sharing-CMS
    🔗 Clone URL: https://github.com/PHPVibe/PHPVibe-Video-Sharing-CMS.git
    📥 Cloning PHPVibe/PHPVibe-Video-Sharing-CMS...
  ✅ Completed: FriendsOfTYPO3/content-blocks (Score: 26.0)
    ✅ Successfully cloned PHPVibe/PHPVibe-Video-Sharing-CMS
    🔍 Running Semgrep on PHPVibe/PHPVibe-Video-Sharing-CMS...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/PHPVibe_PHPVibe-Video-Sharing-CMS_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 340 potential vulnerabilities
    🧹 Cleaned up PHPVibe/PHPVibe-Video-Sharing-CMS
    ✅ PHPVibe/PHPVibe-Video-Sharing-CMS - Final Score: 73.0 (Risk: 23, Semgrep: 100)

🎯 Processing: OFFLINE-GmbH/oc-bootstrapper  ✅ Completed: PHPVibe/PHPVibe-Video-Sharing-CMS (Score: 73.0)

    🔗 Clone URL: https://github.com/OFFLINE-GmbH/oc-bootstrapper.git
    📥 Cloning OFFLINE-GmbH/oc-bootstrapper...
    ✅ Successfully cloned OFFLINE-GmbH/oc-bootstrapper
    🔍 Running Semgrep on OFFLINE-GmbH/oc-bootstrapper...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/OFFLINE-GmbH_oc-bootstrapper_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 7 potential vulnerabilities
    🧹 Cleaned up OFFLINE-GmbH/oc-bootstrapper
    ✅ OFFLINE-GmbH/oc-bootstrapper - Final Score: 40.5 (Risk: 18, Semgrep: 45)

🎯 Processing: liberu-cms/cms-laravel
    🔗 Clone URL: https://github.com/liberu-cms/cms-laravel.git  ✅ Completed: OFFLINE-GmbH/oc-bootstrapper (Score: 40.5)
    📥 Cloning liberu-cms/cms-laravel...

    ✅ Successfully cloned liberu-cms/cms-laravel
    🔍 Running Semgrep on liberu-cms/cms-laravel...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/liberu-cms_cms-laravel_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 8 potential vulnerabilities
    🧹 Cleaned up liberu-cms/cms-laravel
    ✅ liberu-cms/cms-laravel - Final Score: 58.5 (Risk: 26, Semgrep: 65)

🎯 Processing: xpertbot/craft-wheelform  ✅ Completed: liberu-cms/cms-laravel (Score: 58.5)
    🔗 Clone URL: https://github.com/xpertbot/craft-wheelform.git
    📥 Cloning xpertbot/craft-wheelform...

    ✅ Successfully cloned xpertbot/craft-wheelform
    🔍 Running Semgrep on xpertbot/craft-wheelform...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/xpertbot_craft-wheelform_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up xpertbot/craft-wheelform
    ✅ xpertbot/craft-wheelform - Final Score: 20.5 (Risk: 18, Semgrep: 5)

🎯 Processing: romadebrian/WEB-Sekolah
    🔗 Clone URL: https://github.com/romadebrian/WEB-Sekolah.git
    📥 Cloning romadebrian/WEB-Sekolah...
  ✅ Completed: xpertbot/craft-wheelform (Score: 20.5)
    ✅ Successfully cloned romadebrian/WEB-Sekolah
    🔍 Running Semgrep on romadebrian/WEB-Sekolah...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/romadebrian_WEB-Sekolah_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 54 potential vulnerabilities
    🧹 Cleaned up romadebrian/WEB-Sekolah
    ✅ romadebrian/WEB-Sekolah - Final Score: 68.0 (Risk: 18, Semgrep: 100)

🎯 Processing: plan2net/webp
    🔗 Clone URL: https://github.com/plan2net/webp.git
    📥 Cloning plan2net/webp...
  ✅ Completed: romadebrian/WEB-Sekolah (Score: 68.0)
    ✅ Successfully cloned plan2net/webp
    🔍 Running Semgrep on plan2net/webp...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/plan2net_webp_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up plan2net/webp
    ✅ plan2net/webp - Final Score: 18.0 (Risk: 18, Semgrep: 0)

🎯 Processing: putyourlightson/craft-campaign
  ✅ Completed: plan2net/webp (Score: 18.0)
    🔗 Clone URL: https://github.com/putyourlightson/craft-campaign.git
    📥 Cloning putyourlightson/craft-campaign...
    ✅ Successfully cloned putyourlightson/craft-campaign
    🔍 Running Semgrep on putyourlightson/craft-campaign...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/putyourlightson_craft-campaign_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up putyourlightson/craft-campaign
    ✅ putyourlightson/craft-campaign - Final Score: 20.5 (Risk: 18, Semgrep: 5)

🎯 Processing: azerothcore/acore-cms
    🔗 Clone URL: https://github.com/azerothcore/acore-cms.git
    📥 Cloning azerothcore/acore-cms...
  ✅ Completed: putyourlightson/craft-campaign (Score: 20.5)
    ✅ Successfully cloned azerothcore/acore-cms
    🔍 Running Semgrep on azerothcore/acore-cms...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/azerothcore_acore-cms_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 10 potential vulnerabilities
    🧹 Cleaned up azerothcore/acore-cms
    ✅ azerothcore/acore-cms - Final Score: 56.0 (Risk: 31, Semgrep: 50)

🎯 Processing: craftcms/aws-s3  ✅ Completed: azerothcore/acore-cms (Score: 56.0)

    🔗 Clone URL: https://github.com/craftcms/aws-s3.git
    📥 Cloning craftcms/aws-s3...
    ✅ Successfully cloned craftcms/aws-s3
    🔍 Running Semgrep on craftcms/aws-s3...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/craftcms_aws-s3_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up craftcms/aws-s3
    ✅ craftcms/aws-s3 - Final Score: 18.0 (Risk: 18, Semgrep: 0)

🎯 Processing: modxcms/fred
    🔗 Clone URL: https://github.com/modxcms/fred.git
    📥 Cloning modxcms/fred...
  ✅ Completed: craftcms/aws-s3 (Score: 18.0)
    ✅ Successfully cloned modxcms/fred
    🔍 Running Semgrep on modxcms/fred...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/modxcms_fred_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 12 potential vulnerabilities
    🧹 Cleaned up modxcms/fred
    ✅ modxcms/fred - Final Score: 50.5 (Risk: 18, Semgrep: 65)

🎯 Processing: getformwork/formwork
    🔗 Clone URL: https://github.com/getformwork/formwork.git
    📥 Cloning getformwork/formwork...
  ✅ Completed: modxcms/fred (Score: 50.5)
    ✅ Successfully cloned getformwork/formwork
    🔍 Running Semgrep on getformwork/formwork...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/getformwork_formwork_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 75 potential vulnerabilities
    🧹 Cleaned up getformwork/formwork
    ✅ getformwork/formwork - Final Score: 68.0 (Risk: 18, Semgrep: 100)

🎯 Processing: oveleon/contao-cookiebar  ✅ Completed: getformwork/formwork (Score: 68.0)
    🔗 Clone URL: https://github.com/oveleon/contao-cookiebar.git
    📥 Cloning oveleon/contao-cookiebar...

    ✅ Successfully cloned oveleon/contao-cookiebar
    🔍 Running Semgrep on oveleon/contao-cookiebar...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/oveleon_contao-cookiebar_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 3 potential vulnerabilities
    🧹 Cleaned up oveleon/contao-cookiebar
    ✅ oveleon/contao-cookiebar - Final Score: 38.0 (Risk: 23, Semgrep: 30)

🎯 Processing: justitems/midrub_cms
    🔗 Clone URL: https://github.com/justitems/midrub_cms.git
    📥 Cloning justitems/midrub_cms...
  ✅ Completed: oveleon/contao-cookiebar (Score: 38.0)
    ✅ Successfully cloned justitems/midrub_cms
    🔍 Running Semgrep on justitems/midrub_cms...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/justitems_midrub_cms_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 125 potential vulnerabilities
    🧹 Cleaned up justitems/midrub_cms
    ✅ justitems/midrub_cms - Final Score: 73.0 (Risk: 23, Semgrep: 100)

🎯 Processing: FriendsOfREDAXO/cke5
    🔗 Clone URL: https://github.com/FriendsOfREDAXO/cke5.git
    📥 Cloning FriendsOfREDAXO/cke5...
  ✅ Completed: justitems/midrub_cms (Score: 73.0)
    ✅ Successfully cloned FriendsOfREDAXO/cke5
    🔍 Running Semgrep on FriendsOfREDAXO/cke5...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/FriendsOfREDAXO_cke5_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 10 potential vulnerabilities
    🧹 Cleaned up FriendsOfREDAXO/cke5
    ✅ FriendsOfREDAXO/cke5 - Final Score: 43.0 (Risk: 18, Semgrep: 50)

🎯 Processing: abdessamadbettal/lara4
    🔗 Clone URL: https://github.com/abdessamadbettal/lara4.git
    📥 Cloning abdessamadbettal/lara4...
  ✅ Completed: FriendsOfREDAXO/cke5 (Score: 43.0)
    ✅ Successfully cloned abdessamadbettal/lara4
    🔍 Running Semgrep on abdessamadbettal/lara4...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/abdessamadbettal_lara4_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 10 potential vulnerabilities
    🧹 Cleaned up abdessamadbettal/lara4
    ✅ abdessamadbettal/lara4 - Final Score: 55.5 (Risk: 18, Semgrep: 75)

🎯 Processing: simplygoodwork/craft-donkeytail
    🔗 Clone URL: https://github.com/simplygoodwork/craft-donkeytail.git
  ✅ Completed: abdessamadbettal/lara4 (Score: 55.5)
    📥 Cloning simplygoodwork/craft-donkeytail...
    ✅ Successfully cloned simplygoodwork/craft-donkeytail
    🔍 Running Semgrep on simplygoodwork/craft-donkeytail...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/simplygoodwork_craft-donkeytail_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up simplygoodwork/craft-donkeytail
    ✅ simplygoodwork/craft-donkeytail - Final Score: 18.0 (Risk: 18, Semgrep: 0)

🎯 Processing: laravel/fortify  ✅ Completed: simplygoodwork/craft-donkeytail (Score: 18.0)
    🔗 Clone URL: https://github.com/laravel/fortify.git
    📥 Cloning laravel/fortify...

    ✅ Successfully cloned laravel/fortify
    🔍 Running Semgrep on laravel/fortify...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/laravel_fortify_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up laravel/fortify
    ✅ laravel/fortify - Final Score: 22.0 (Risk: 22, Semgrep: 0)

🎯 Processing: delight-im/PHP-Auth  ✅ Completed: laravel/fortify (Score: 22.0)

    🔗 Clone URL: https://github.com/delight-im/PHP-Auth.git
    📥 Cloning delight-im/PHP-Auth...
    ✅ Successfully cloned delight-im/PHP-Auth
    🔍 Running Semgrep on delight-im/PHP-Auth...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/delight-im_PHP-Auth_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up delight-im/PHP-Auth
    ✅ delight-im/PHP-Auth - Final Score: 27.0 (Risk: 27, Semgrep: 0)

🎯 Processing: simplesamlphp/simplesamlphp  ✅ Completed: delight-im/PHP-Auth (Score: 27.0)

    🔗 Clone URL: https://github.com/simplesamlphp/simplesamlphp.git
    📥 Cloning simplesamlphp/simplesamlphp...
    ✅ Successfully cloned simplesamlphp/simplesamlphp
    🔍 Running Semgrep on simplesamlphp/simplesamlphp...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/simplesamlphp_simplesamlphp_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 18 potential vulnerabilities
    🧹 Cleaned up simplesamlphp/simplesamlphp
    ✅ simplesamlphp/simplesamlphp - Final Score: 87.0 (Risk: 37, Semgrep: 100)

🎯 Processing: rappasoft/laravel-authentication-log  ✅ Completed: simplesamlphp/simplesamlphp (Score: 87.0)
    🔗 Clone URL: https://github.com/rappasoft/laravel-authentication-log.git

    📥 Cloning rappasoft/laravel-authentication-log...
    ✅ Successfully cloned rappasoft/laravel-authentication-log
    🔍 Running Semgrep on rappasoft/laravel-authentication-log...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/rappasoft_laravel-authentication-log_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up rappasoft/laravel-authentication-log
    ✅ rappasoft/laravel-authentication-log - Final Score: 32.0 (Risk: 32, Semgrep: 0)

🎯 Processing: PHPAuth/PHPAuth
  ✅ Completed: rappasoft/laravel-authentication-log (Score: 32.0)    🔗 Clone URL: https://github.com/PHPAuth/PHPAuth.git
    📥 Cloning PHPAuth/PHPAuth...

    ✅ Successfully cloned PHPAuth/PHPAuth
    🔍 Running Semgrep on PHPAuth/PHPAuth...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/PHPAuth_PHPAuth_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 2 potential vulnerabilities
    🧹 Cleaned up PHPAuth/PHPAuth
    ✅ PHPAuth/PHPAuth - Final Score: 34.5 (Risk: 27, Semgrep: 15)

🎯 Processing: WordPress/two-factor  ✅ Completed: PHPAuth/PHPAuth (Score: 34.5)

    🔗 Clone URL: https://github.com/WordPress/two-factor.git
    📥 Cloning WordPress/two-factor...
    ✅ Successfully cloned WordPress/two-factor
    🔍 Running Semgrep on WordPress/two-factor...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/WordPress_two-factor_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up WordPress/two-factor
    ✅ WordPress/two-factor - Final Score: 42.0 (Risk: 42, Semgrep: 0)

🎯 Processing: mattiverse/Laravel-Userstamps
    🔗 Clone URL: https://github.com/mattiverse/Laravel-Userstamps.git
    📥 Cloning mattiverse/Laravel-Userstamps...  ✅ Completed: WordPress/two-factor (Score: 42.0)

    ✅ Successfully cloned mattiverse/Laravel-Userstamps
    🔍 Running Semgrep on mattiverse/Laravel-Userstamps...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/mattiverse_Laravel-Userstamps_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up mattiverse/Laravel-Userstamps
    ✅ mattiverse/Laravel-Userstamps - Final Score: 23.0 (Risk: 23, Semgrep: 0)

🎯 Processing: scheb/2fa
    🔗 Clone URL: https://github.com/scheb/2fa.git
    📥 Cloning scheb/2fa...
  ✅ Completed: mattiverse/Laravel-Userstamps (Score: 23.0)
    ✅ Successfully cloned scheb/2fa
    🔍 Running Semgrep on scheb/2fa...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/scheb_2fa_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 4 potential vulnerabilities
    🧹 Cleaned up scheb/2fa
    ✅ scheb/2fa - Final Score: 52.0 (Risk: 32, Semgrep: 40)

🎯 Processing: DirectoryTree/LdapRecord-Laravel
    🔗 Clone URL: https://github.com/DirectoryTree/LdapRecord-Laravel.git
    📥 Cloning DirectoryTree/LdapRecord-Laravel...  ✅ Completed: scheb/2fa (Score: 52.0)

    ✅ Successfully cloned DirectoryTree/LdapRecord-Laravel
    🔍 Running Semgrep on DirectoryTree/LdapRecord-Laravel...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/DirectoryTree_LdapRecord-Laravel_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up DirectoryTree/LdapRecord-Laravel
    ✅ DirectoryTree/LdapRecord-Laravel - Final Score: 38.5 (Risk: 36, Semgrep: 5)

🎯 Processing: ollieread/multiauth  ✅ Completed: DirectoryTree/LdapRecord-Laravel (Score: 38.5)

    🔗 Clone URL: https://github.com/ollieread/multiauth.git
    📥 Cloning ollieread/multiauth...
    ✅ Successfully cloned ollieread/multiauth
    🔍 Running Semgrep on ollieread/multiauth...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/ollieread_multiauth_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up ollieread/multiauth
    ✅ ollieread/multiauth - Final Score: 27.0 (Risk: 27, Semgrep: 0)

🎯 Processing: cesargb/laravel-magiclink
    🔗 Clone URL: https://github.com/cesargb/laravel-magiclink.git
    📥 Cloning cesargb/laravel-magiclink...
  ✅ Completed: ollieread/multiauth (Score: 27.0)
    ✅ Successfully cloned cesargb/laravel-magiclink
    🔍 Running Semgrep on cesargb/laravel-magiclink...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/cesargb_laravel-magiclink_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 3 potential vulnerabilities
    🧹 Cleaned up cesargb/laravel-magiclink
    ✅ cesargb/laravel-magiclink - Final Score: 33.5 (Risk: 26, Semgrep: 15)

🎯 Processing: codeigniter4/shield
    🔗 Clone URL: https://github.com/codeigniter4/shield.git
    📥 Cloning codeigniter4/shield...  ✅ Completed: cesargb/laravel-magiclink (Score: 33.5)

    ✅ Successfully cloned codeigniter4/shield
    🔍 Running Semgrep on codeigniter4/shield...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/codeigniter4_shield_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 3 potential vulnerabilities
    🧹 Cleaned up codeigniter4/shield
    ✅ codeigniter4/shield - Final Score: 47.0 (Risk: 32, Semgrep: 30)

🎯 Processing: nette/security
    🔗 Clone URL: https://github.com/nette/security.git  ✅ Completed: codeigniter4/shield (Score: 47.0)
    📥 Cloning nette/security...

    ✅ Successfully cloned nette/security
    🔍 Running Semgrep on nette/security...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/nette_security_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up nette/security
    ✅ nette/security - Final Score: 27.0 (Risk: 27, Semgrep: 0)

🎯 Processing: Laragear/WebAuthn
    🔗 Clone URL: https://github.com/Laragear/WebAuthn.git
    📥 Cloning Laragear/WebAuthn...
  ✅ Completed: nette/security (Score: 27.0)
    ✅ Successfully cloned Laragear/WebAuthn
    🔍 Running Semgrep on Laragear/WebAuthn...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/Laragear_WebAuthn_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up Laragear/WebAuthn
    ✅ Laragear/WebAuthn - Final Score: 28.0 (Risk: 28, Semgrep: 0)

🎯 Processing: andrewdwallo/filament-companies
    🔗 Clone URL: https://github.com/andrewdwallo/filament-companies.git
    📥 Cloning andrewdwallo/filament-companies...
  ✅ Completed: Laragear/WebAuthn (Score: 28.0)
    ✅ Successfully cloned andrewdwallo/filament-companies
    🔍 Running Semgrep on andrewdwallo/filament-companies...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/andrewdwallo_filament-companies_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up andrewdwallo/filament-companies
    ✅ andrewdwallo/filament-companies - Final Score: 32.0 (Risk: 32, Semgrep: 0)

🎯 Processing: renoki-co/php-k8s
    🔗 Clone URL: https://github.com/renoki-co/php-k8s.git
    📥 Cloning renoki-co/php-k8s...
  ✅ Completed: andrewdwallo/filament-companies (Score: 32.0)
    ✅ Successfully cloned renoki-co/php-k8s
    🔍 Running Semgrep on renoki-co/php-k8s...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/renoki-co_php-k8s_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 3 potential vulnerabilities
    🧹 Cleaned up renoki-co/php-k8s
    ✅ renoki-co/php-k8s - Final Score: 48.0 (Risk: 38, Semgrep: 20)

🎯 Processing: DirectoryTree/Bartender
    🔗 Clone URL: https://github.com/DirectoryTree/Bartender.git
    📥 Cloning DirectoryTree/Bartender...
  ✅ Completed: renoki-co/php-k8s (Score: 48.0)
    ✅ Successfully cloned DirectoryTree/Bartender
    🔍 Running Semgrep on DirectoryTree/Bartender...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/DirectoryTree_Bartender_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up DirectoryTree/Bartender
    ✅ DirectoryTree/Bartender - Final Score: 23.0 (Risk: 23, Semgrep: 0)

🎯 Processing: KeyAuth/KeyAuth-Source-Code
  ✅ Completed: DirectoryTree/Bartender (Score: 23.0)
    🔗 Clone URL: https://github.com/KeyAuth/KeyAuth-Source-Code.git
    📥 Cloning KeyAuth/KeyAuth-Source-Code...
    ✅ Successfully cloned KeyAuth/KeyAuth-Source-Code
    🔍 Running Semgrep on KeyAuth/KeyAuth-Source-Code...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/KeyAuth_KeyAuth-Source-Code_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 67 potential vulnerabilities
    🧹 Cleaned up KeyAuth/KeyAuth-Source-Code
    ✅ KeyAuth/KeyAuth-Source-Code - Final Score: 82.0 (Risk: 32, Semgrep: 100)

🎯 Processing: KABBOUCHI/nova-impersonate  ✅ Completed: KeyAuth/KeyAuth-Source-Code (Score: 82.0)

    🔗 Clone URL: https://github.com/KABBOUCHI/nova-impersonate.git
    📥 Cloning KABBOUCHI/nova-impersonate...
    ✅ Successfully cloned KABBOUCHI/nova-impersonate
    🔍 Running Semgrep on KABBOUCHI/nova-impersonate...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/KABBOUCHI_nova-impersonate_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up KABBOUCHI/nova-impersonate
    ✅ KABBOUCHI/nova-impersonate - Final Score: 28.0 (Risk: 28, Semgrep: 0)

🎯 Processing: coderello/laravel-passport-social-grant  ✅ Completed: KABBOUCHI/nova-impersonate (Score: 28.0)

    🔗 Clone URL: https://github.com/coderello/laravel-passport-social-grant.git
    📥 Cloning coderello/laravel-passport-social-grant...
    ✅ Successfully cloned coderello/laravel-passport-social-grant
    🔍 Running Semgrep on coderello/laravel-passport-social-grant...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/coderello_laravel-passport-social-grant_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up coderello/laravel-passport-social-grant
    ✅ coderello/laravel-passport-social-grant - Final Score: 30.0 (Risk: 30, Semgrep: 0)

🎯 Processing: maize-tech/laravel-magic-login  ✅ Completed: coderello/laravel-passport-social-grant (Score: 30.0)

    🔗 Clone URL: https://github.com/maize-tech/laravel-magic-login.git
    📥 Cloning maize-tech/laravel-magic-login...
    ✅ Successfully cloned maize-tech/laravel-magic-login
    🔍 Running Semgrep on maize-tech/laravel-magic-login...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/maize-tech_laravel-magic-login_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up maize-tech/laravel-magic-login
    ✅ maize-tech/laravel-magic-login - Final Score: 35.0 (Risk: 35, Semgrep: 0)

🎯 Processing: Really-Simple-Plugins/really-simple-ssl  ✅ Completed: maize-tech/laravel-magic-login (Score: 35.0)

    🔗 Clone URL: https://github.com/Really-Simple-Plugins/really-simple-ssl.git
    📥 Cloning Really-Simple-Plugins/really-simple-ssl...
    ✅ Successfully cloned Really-Simple-Plugins/really-simple-ssl
    🔍 Running Semgrep on Really-Simple-Plugins/really-simple-ssl...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/Really-Simple-Plugins_really-simple-ssl_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 32 potential vulnerabilities
    🧹 Cleaned up Really-Simple-Plugins/really-simple-ssl
    ✅ Really-Simple-Plugins/really-simple-ssl - Final Score: 88.0 (Risk: 38, Semgrep: 100)

🎯 Processing: usefulteam/jwt-auth  ✅ Completed: Really-Simple-Plugins/really-simple-ssl (Score: 88.0)

    🔗 Clone URL: https://github.com/usefulteam/jwt-auth.git
    📥 Cloning usefulteam/jwt-auth...
    ✅ Successfully cloned usefulteam/jwt-auth
    🔍 Running Semgrep on usefulteam/jwt-auth...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/usefulteam_jwt-auth_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 4 potential vulnerabilities
    🧹 Cleaned up usefulteam/jwt-auth
    ✅ usefulteam/jwt-auth - Final Score: 68.0 (Risk: 48, Semgrep: 40)

🎯 Processing: spatie/laravel-one-time-passwords
    🔗 Clone URL: https://github.com/spatie/laravel-one-time-passwords.git  ✅ Completed: usefulteam/jwt-auth (Score: 68.0)

    📥 Cloning spatie/laravel-one-time-passwords...
    ✅ Successfully cloned spatie/laravel-one-time-passwords
    🔍 Running Semgrep on spatie/laravel-one-time-passwords...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/spatie_laravel-one-time-passwords_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up spatie/laravel-one-time-passwords
    ✅ spatie/laravel-one-time-passwords - Final Score: 28.5 (Risk: 26, Semgrep: 5)

🎯 Processing: nextcloud/user_saml  ✅ Completed: spatie/laravel-one-time-passwords (Score: 28.5)

    🔗 Clone URL: https://github.com/nextcloud/user_saml.git
    📥 Cloning nextcloud/user_saml...
    ✅ Successfully cloned nextcloud/user_saml
    🔍 Running Semgrep on nextcloud/user_saml...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/nextcloud_user_saml_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up nextcloud/user_saml
    ✅ nextcloud/user_saml - Final Score: 23.0 (Risk: 23, Semgrep: 0)

🎯 Processing: pantheon-systems/wp-saml-auth
    🔗 Clone URL: https://github.com/pantheon-systems/wp-saml-auth.git
    📥 Cloning pantheon-systems/wp-saml-auth...  ✅ Completed: nextcloud/user_saml (Score: 23.0)

    ✅ Successfully cloned pantheon-systems/wp-saml-auth
    🔍 Running Semgrep on pantheon-systems/wp-saml-auth...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/pantheon-systems_wp-saml-auth_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up pantheon-systems/wp-saml-auth
    ✅ pantheon-systems/wp-saml-auth - Final Score: 27.5 (Risk: 25, Semgrep: 5)

🎯 Processing: corbado/passkeys-php-laravel
    🔗 Clone URL: https://github.com/corbado/passkeys-php-laravel.git  ✅ Completed: pantheon-systems/wp-saml-auth (Score: 27.5)

    📥 Cloning corbado/passkeys-php-laravel...
    ✅ Successfully cloned corbado/passkeys-php-laravel
    🔍 Running Semgrep on corbado/passkeys-php-laravel...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/corbado_passkeys-php-laravel_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up corbado/passkeys-php-laravel
    ✅ corbado/passkeys-php-laravel - Final Score: 17.0 (Risk: 17, Semgrep: 0)

🎯 Processing: stephenjude/filament-two-factor-authentication
    🔗 Clone URL: https://github.com/stephenjude/filament-two-factor-authentication.git
    📥 Cloning stephenjude/filament-two-factor-authentication...
  ✅ Completed: corbado/passkeys-php-laravel (Score: 17.0)
    ✅ Successfully cloned stephenjude/filament-two-factor-authentication
    🔍 Running Semgrep on stephenjude/filament-two-factor-authentication...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/stephenjude_filament-two-factor-authentication_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up stephenjude/filament-two-factor-authentication
    ✅ stephenjude/filament-two-factor-authentication - Final Score: 17.5 (Risk: 17, Semgrep: 1)

🎯 Processing: heiglandreas/authLdap
    🔗 Clone URL: https://github.com/heiglandreas/authLdap.git  ✅ Completed: stephenjude/filament-two-factor-authentication (Score: 17.5)

    📥 Cloning heiglandreas/authLdap...
    ✅ Successfully cloned heiglandreas/authLdap
    🔍 Running Semgrep on heiglandreas/authLdap...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/heiglandreas_authLdap_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 27 potential vulnerabilities
    🧹 Cleaned up heiglandreas/authLdap
    ✅ heiglandreas/authLdap - Final Score: 79.0 (Risk: 29, Semgrep: 100)

🎯 Processing: ekapusta/oauth2-esia
    🔗 Clone URL: https://github.com/ekapusta/oauth2-esia.git
    📥 Cloning ekapusta/oauth2-esia...  ✅ Completed: heiglandreas/authLdap (Score: 79.0)

    ✅ Successfully cloned ekapusta/oauth2-esia
    🔍 Running Semgrep on ekapusta/oauth2-esia...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/ekapusta_oauth2-esia_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 8 potential vulnerabilities
    🧹 Cleaned up ekapusta/oauth2-esia
    ✅ ekapusta/oauth2-esia - Final Score: 48.0 (Risk: 13, Semgrep: 70)

🎯 Processing: scheb/2fa-bundle
    🔗 Clone URL: https://github.com/scheb/2fa-bundle.git
    📥 Cloning scheb/2fa-bundle...  ✅ Completed: ekapusta/oauth2-esia (Score: 48.0)

    ✅ Successfully cloned scheb/2fa-bundle
    🔍 Running Semgrep on scheb/2fa-bundle...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/scheb_2fa-bundle_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up scheb/2fa-bundle
    ✅ scheb/2fa-bundle - Final Score: 22.0 (Risk: 22, Semgrep: 0)

🎯 Processing: francis-njenga/Face-Recognition-Attendance-System  ✅ Completed: scheb/2fa-bundle (Score: 22.0)

    🔗 Clone URL: https://github.com/francis-njenga/Face-Recognition-Attendance-System.git
    📥 Cloning francis-njenga/Face-Recognition-Attendance-System...
    ✅ Successfully cloned francis-njenga/Face-Recognition-Attendance-System
    🔍 Running Semgrep on francis-njenga/Face-Recognition-Attendance-System...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/francis-njenga_Face-Recognition-Attendance-System_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 20 potential vulnerabilities
    🧹 Cleaned up francis-njenga/Face-Recognition-Attendance-System
    ✅ francis-njenga/Face-Recognition-Attendance-System - Final Score: 63.0 (Risk: 13, Semgrep: 100)

🎯 Processing: Liturgical-Calendar/LiturgicalCalendarAPI  ✅ Completed: francis-njenga/Face-Recognition-Attendance-System (Score: 63.0)
    🔗 Clone URL: https://github.com/Liturgical-Calendar/LiturgicalCalendarAPI.git

    📥 Cloning Liturgical-Calendar/LiturgicalCalendarAPI...
    ✅ Successfully cloned Liturgical-Calendar/LiturgicalCalendarAPI
    🔍 Running Semgrep on Liturgical-Calendar/LiturgicalCalendarAPI...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/Liturgical-Calendar_LiturgicalCalendarAPI_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 9 potential vulnerabilities
    🧹 Cleaned up Liturgical-Calendar/LiturgicalCalendarAPI
    ✅ Liturgical-Calendar/LiturgicalCalendarAPI - Final Score: 42.0 (Risk: 17, Semgrep: 50)

🎯 Processing: always-open/laravel-totem
    🔗 Clone URL: https://github.com/always-open/laravel-totem.git  ✅ Completed: Liturgical-Calendar/LiturgicalCalendarAPI (Score: 42.0)

    📥 Cloning always-open/laravel-totem...
    ✅ Successfully cloned always-open/laravel-totem
    🔍 Running Semgrep on always-open/laravel-totem...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/always-open_laravel-totem_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 5 potential vulnerabilities
    🧹 Cleaned up always-open/laravel-totem
    ✅ always-open/laravel-totem - Final Score: 30.5 (Risk: 18, Semgrep: 25)

🎯 Processing: laravel/pulse  ✅ Completed: always-open/laravel-totem (Score: 30.5)

    🔗 Clone URL: https://github.com/laravel/pulse.git
    📥 Cloning laravel/pulse...
    ✅ Successfully cloned laravel/pulse
    🔍 Running Semgrep on laravel/pulse...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/laravel_pulse_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up laravel/pulse
    ✅ laravel/pulse - Final Score: 25.5 (Risk: 23, Semgrep: 5)

🎯 Processing: elementor/activity-log  ✅ Completed: laravel/pulse (Score: 25.5)

    🔗 Clone URL: https://github.com/elementor/activity-log.git
    📥 Cloning elementor/activity-log...
    ✅ Successfully cloned elementor/activity-log
    🔍 Running Semgrep on elementor/activity-log...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/elementor_activity-log_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 3 potential vulnerabilities
    🧹 Cleaned up elementor/activity-log
    ✅ elementor/activity-log - Final Score: 48.5 (Risk: 36, Semgrep: 25)

🎯 Processing: Automattic/woocommerce-payments
    🔗 Clone URL: https://github.com/Automattic/woocommerce-payments.git
    📥 Cloning Automattic/woocommerce-payments...
  ✅ Completed: elementor/activity-log (Score: 48.5)
    ✅ Successfully cloned Automattic/woocommerce-payments
    🔍 Running Semgrep on Automattic/woocommerce-payments...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/Automattic_woocommerce-payments_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 40 potential vulnerabilities
    🧹 Cleaned up Automattic/woocommerce-payments
    ✅ Automattic/woocommerce-payments - Final Score: 100.0 (Risk: 51, Semgrep: 100)

🎯 Processing: AndyTaylorTweet/Pi-Star_DV_Dash
    🔗 Clone URL: https://github.com/AndyTaylorTweet/Pi-Star_DV_Dash.git  ✅ Completed: Automattic/woocommerce-payments (Score: 100.0)

    📥 Cloning AndyTaylorTweet/Pi-Star_DV_Dash...
    ✅ Successfully cloned AndyTaylorTweet/Pi-Star_DV_Dash
    🔍 Running Semgrep on AndyTaylorTweet/Pi-Star_DV_Dash...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/AndyTaylorTweet_Pi-Star_DV_Dash_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 192 potential vulnerabilities
    🧹 Cleaned up AndyTaylorTweet/Pi-Star_DV_Dash
    ✅ AndyTaylorTweet/Pi-Star_DV_Dash - Final Score: 78.0 (Risk: 28, Semgrep: 100)

🎯 Processing: tighten/nova-stripe  ✅ Completed: AndyTaylorTweet/Pi-Star_DV_Dash (Score: 78.0)
    🔗 Clone URL: https://github.com/tighten/nova-stripe.git
    📥 Cloning tighten/nova-stripe...

    ✅ Successfully cloned tighten/nova-stripe
    🔍 Running Semgrep on tighten/nova-stripe...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/tighten_nova-stripe_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up tighten/nova-stripe
    ✅ tighten/nova-stripe - Final Score: 33.0 (Risk: 33, Semgrep: 0)

🎯 Processing: mdutt247/laravel-news  ✅ Completed: tighten/nova-stripe (Score: 33.0)

    🔗 Clone URL: https://github.com/mdutt247/laravel-news.git
    📥 Cloning mdutt247/laravel-news...
    ✅ Successfully cloned mdutt247/laravel-news
    🔍 Running Semgrep on mdutt247/laravel-news...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/mdutt247_laravel-news_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 2 potential vulnerabilities
    🧹 Cleaned up mdutt247/laravel-news
    ✅ mdutt247/laravel-news - Final Score: 36.5 (Risk: 29, Semgrep: 15)

🎯 Processing: 10up/ads-txt  ✅ Completed: mdutt247/laravel-news (Score: 36.5)
    🔗 Clone URL: https://github.com/10up/ads-txt.git
    📥 Cloning 10up/ads-txt...

    ✅ Successfully cloned 10up/ads-txt
    🔍 Running Semgrep on 10up/ads-txt...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/10up_ads-txt_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up 10up/ads-txt
    ✅ 10up/ads-txt - Final Score: 26.0 (Risk: 26, Semgrep: 0)

🎯 Processing: woocommerce/google-listings-and-ads
    🔗 Clone URL: https://github.com/woocommerce/google-listings-and-ads.git
    📥 Cloning woocommerce/google-listings-and-ads...  ✅ Completed: 10up/ads-txt (Score: 26.0)

    ✅ Successfully cloned woocommerce/google-listings-and-ads
    🔍 Running Semgrep on woocommerce/google-listings-and-ads...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/woocommerce_google-listings-and-ads_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 3 potential vulnerabilities
    🧹 Cleaned up woocommerce/google-listings-and-ads
    ✅ woocommerce/google-listings-and-ads - Final Score: 48.5 (Risk: 41, Semgrep: 15)

🎯 Processing: daun/processwire-dashboard
    🔗 Clone URL: https://github.com/daun/processwire-dashboard.git
  ✅ Completed: woocommerce/google-listings-and-ads (Score: 48.5)    📥 Cloning daun/processwire-dashboard...

    ✅ Successfully cloned daun/processwire-dashboard
    🔍 Running Semgrep on daun/processwire-dashboard...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/daun_processwire-dashboard_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 6 potential vulnerabilities
    🧹 Cleaned up daun/processwire-dashboard
    ✅ daun/processwire-dashboard - Final Score: 41.0 (Risk: 26, Semgrep: 30)

🎯 Processing: shish/shimmie2  ✅ Completed: daun/processwire-dashboard (Score: 41.0)
    🔗 Clone URL: https://github.com/shish/shimmie2.git
    📥 Cloning shish/shimmie2...

    ✅ Successfully cloned shish/shimmie2
    🔍 Running Semgrep on shish/shimmie2...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/shish_shimmie2_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 11 potential vulnerabilities
    🧹 Cleaned up shish/shimmie2
    ✅ shish/shimmie2 - Final Score: 88.0 (Risk: 38, Semgrep: 100)

🎯 Processing: wordpress-premium/advanced-custom-fields-pro
    🔗 Clone URL: https://github.com/wordpress-premium/advanced-custom-fields-pro.git
    📥 Cloning wordpress-premium/advanced-custom-fields-pro...
  ✅ Completed: shish/shimmie2 (Score: 88.0)
    ✅ Successfully cloned wordpress-premium/advanced-custom-fields-pro
    🔍 Running Semgrep on wordpress-premium/advanced-custom-fields-pro...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/wordpress-premium_advanced-custom-fields-pro_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 15 potential vulnerabilities
    🧹 Cleaned up wordpress-premium/advanced-custom-fields-pro
    ✅ wordpress-premium/advanced-custom-fields-pro - Final Score: 78.5 (Risk: 36, Semgrep: 85)

🎯 Processing: fooplugins/foogallery
    🔗 Clone URL: https://github.com/fooplugins/foogallery.git
    📥 Cloning fooplugins/foogallery...
  ✅ Completed: wordpress-premium/advanced-custom-fields-pro (Score: 78.5)
    ✅ Successfully cloned fooplugins/foogallery
    🔍 Running Semgrep on fooplugins/foogallery...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/fooplugins_foogallery_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 8 potential vulnerabilities
    🧹 Cleaned up fooplugins/foogallery
    ✅ fooplugins/foogallery - Final Score: 57.0 (Risk: 36, Semgrep: 42)

🎯 Processing: novafacile/novagallery  ✅ Completed: fooplugins/foogallery (Score: 57.0)

    🔗 Clone URL: https://github.com/novafacile/novagallery.git
    📥 Cloning novafacile/novagallery...
    ✅ Successfully cloned novafacile/novagallery
    🔍 Running Semgrep on novafacile/novagallery...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/novafacile_novagallery_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up novafacile/novagallery
    ✅ novafacile/novagallery - Final Score: 26.0 (Risk: 26, Semgrep: 0)

🎯 Processing: skaut/skaut-google-drive-gallery
    🔗 Clone URL: https://github.com/skaut/skaut-google-drive-gallery.git
    📥 Cloning skaut/skaut-google-drive-gallery...
  ✅ Completed: novafacile/novagallery (Score: 26.0)
    ✅ Successfully cloned skaut/skaut-google-drive-gallery
    🔍 Running Semgrep on skaut/skaut-google-drive-gallery...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/skaut_skaut-google-drive-gallery_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up skaut/skaut-google-drive-gallery
    ✅ skaut/skaut-google-drive-gallery - Final Score: 35.0 (Risk: 35, Semgrep: 0)

🎯 Processing: vish4395/laravel-file-viewer
    🔗 Clone URL: https://github.com/vish4395/laravel-file-viewer.git
    📥 Cloning vish4395/laravel-file-viewer...
  ✅ Completed: skaut/skaut-google-drive-gallery (Score: 35.0)
    ✅ Successfully cloned vish4395/laravel-file-viewer
    🔍 Running Semgrep on vish4395/laravel-file-viewer...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/vish4395_laravel-file-viewer_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 9 potential vulnerabilities
    🧹 Cleaned up vish4395/laravel-file-viewer
    ✅ vish4395/laravel-file-viewer - Final Score: 72.0 (Risk: 46, Semgrep: 52)

🎯 Processing: BeaconCMS/beacon_live_admin
    🔗 Clone URL: https://github.com/BeaconCMS/beacon_live_admin.git
    📥 Cloning BeaconCMS/beacon_live_admin...
  ✅ Completed: vish4395/laravel-file-viewer (Score: 72.0)
    ✅ Successfully cloned BeaconCMS/beacon_live_admin
    🔍 Running Semgrep on BeaconCMS/beacon_live_admin...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/BeaconCMS_beacon_live_admin_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up BeaconCMS/beacon_live_admin
    ✅ BeaconCMS/beacon_live_admin - Final Score: 46.5 (Risk: 44, Semgrep: 5)

🎯 Processing: canopas/canopas-blog-admin
    🔗 Clone URL: https://github.com/canopas/canopas-blog-admin.git
    📥 Cloning canopas/canopas-blog-admin...
  ✅ Completed: BeaconCMS/beacon_live_admin (Score: 46.5)
    ✅ Successfully cloned canopas/canopas-blog-admin
    🔍 Running Semgrep on canopas/canopas-blog-admin...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/canopas_canopas-blog-admin_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 7 potential vulnerabilities
    🧹 Cleaned up canopas/canopas-blog-admin
    ✅ canopas/canopas-blog-admin - Final Score: 61.5 (Risk: 39, Semgrep: 45)

🎯 Processing: richardgirges/express-fileupload  ✅ Completed: canopas/canopas-blog-admin (Score: 61.5)

    🔗 Clone URL: https://github.com/richardgirges/express-fileupload.git
    📥 Cloning richardgirges/express-fileupload...
    ✅ Successfully cloned richardgirges/express-fileupload
    🔍 Running Semgrep on richardgirges/express-fileupload...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/richardgirges_express-fileupload_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 4 potential vulnerabilities
    🧹 Cleaned up richardgirges/express-fileupload
    ✅ richardgirges/express-fileupload - Final Score: 31.0 (Risk: 23, Semgrep: 16)

🎯 Processing: veliovgroup/Meteor-Files
    🔗 Clone URL: https://github.com/veliovgroup/Meteor-Files.git
    📥 Cloning veliovgroup/Meteor-Files...  ✅ Completed: richardgirges/express-fileupload (Score: 31.0)

    ✅ Successfully cloned veliovgroup/Meteor-Files
    🔍 Running Semgrep on veliovgroup/Meteor-Files...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/veliovgroup_Meteor-Files_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 3 potential vulnerabilities
    🧹 Cleaned up veliovgroup/Meteor-Files
    ✅ veliovgroup/Meteor-Files - Final Score: 36.5 (Risk: 29, Semgrep: 15)

🎯 Processing: error311/FileRise
    🔗 Clone URL: https://github.com/error311/FileRise.git
    📥 Cloning error311/FileRise...
  ✅ Completed: veliovgroup/Meteor-Files (Score: 36.5)
    ✅ Successfully cloned error311/FileRise
    🔍 Running Semgrep on error311/FileRise...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/error311_FileRise_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 58 potential vulnerabilities
    🧹 Cleaned up error311/FileRise
    ✅ error311/FileRise - Final Score: 91.0 (Risk: 41, Semgrep: 100)

🎯 Processing: mbraak/django-file-form
    🔗 Clone URL: https://github.com/mbraak/django-file-form.git  ✅ Completed: error311/FileRise (Score: 91.0)

    📥 Cloning mbraak/django-file-form...
    ✅ Successfully cloned mbraak/django-file-form
    🔍 Running Semgrep on mbraak/django-file-form...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/mbraak_django-file-form_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 6 potential vulnerabilities
    🧹 Cleaned up mbraak/django-file-form
    ✅ mbraak/django-file-form - Final Score: 49.0 (Risk: 29, Semgrep: 40)

🎯 Processing: node-modules/formstream  ✅ Completed: mbraak/django-file-form (Score: 49.0)
    🔗 Clone URL: https://github.com/node-modules/formstream.git
    📥 Cloning node-modules/formstream...

    ✅ Successfully cloned node-modules/formstream
    🔍 Running Semgrep on node-modules/formstream...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/node-modules_formstream_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up node-modules/formstream
    ✅ node-modules/formstream - Final Score: 35.5 (Risk: 33, Semgrep: 5)

🎯 Processing: flowjs/ngx-flow  ✅ Completed: node-modules/formstream (Score: 35.5)
    🔗 Clone URL: https://github.com/flowjs/ngx-flow.git

    📥 Cloning flowjs/ngx-flow...
    ✅ Successfully cloned flowjs/ngx-flow
    🔍 Running Semgrep on flowjs/ngx-flow...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/flowjs_ngx-flow_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 3 potential vulnerabilities
    🧹 Cleaned up flowjs/ngx-flow
    ✅ flowjs/ngx-flow - Final Score: 28.5 (Risk: 23, Semgrep: 11)

🎯 Processing: typemill/typemill
    🔗 Clone URL: https://github.com/typemill/typemill.git
    📥 Cloning typemill/typemill...
  ✅ Completed: flowjs/ngx-flow (Score: 28.5)
    ✅ Successfully cloned typemill/typemill
    🔍 Running Semgrep on typemill/typemill...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/typemill_typemill_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 18 potential vulnerabilities
    🧹 Cleaned up typemill/typemill
    ✅ typemill/typemill - Final Score: 76.0 (Risk: 26, Semgrep: 100)

🎯 Processing: pluginpal/strapi-plugin-sitemap
    🔗 Clone URL: https://github.com/pluginpal/strapi-plugin-sitemap.git
    📥 Cloning pluginpal/strapi-plugin-sitemap...  ✅ Completed: typemill/typemill (Score: 76.0)

    ✅ Successfully cloned pluginpal/strapi-plugin-sitemap
    🔍 Running Semgrep on pluginpal/strapi-plugin-sitemap...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/pluginpal_strapi-plugin-sitemap_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 3 potential vulnerabilities
    🧹 Cleaned up pluginpal/strapi-plugin-sitemap
    ✅ pluginpal/strapi-plugin-sitemap - Final Score: 44.0 (Risk: 34, Semgrep: 20)

🎯 Processing: neos/neos-ui
    🔗 Clone URL: https://github.com/neos/neos-ui.git
    📥 Cloning neos/neos-ui...  ✅ Completed: pluginpal/strapi-plugin-sitemap (Score: 44.0)

    ✅ Successfully cloned neos/neos-ui
    🔍 Running Semgrep on neos/neos-ui...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/neos_neos-ui_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 37 potential vulnerabilities
    🧹 Cleaned up neos/neos-ui
    ✅ neos/neos-ui - Final Score: 86.0 (Risk: 36, Semgrep: 100)

🎯 Processing: solisoft/fasty  ✅ Completed: neos/neos-ui (Score: 86.0)

    🔗 Clone URL: https://github.com/solisoft/fasty.git
    📥 Cloning solisoft/fasty...
    ✅ Successfully cloned solisoft/fasty
    🔍 Running Semgrep on solisoft/fasty...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/solisoft_fasty_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 62 potential vulnerabilities
    🧹 Cleaned up solisoft/fasty
    ✅ solisoft/fasty - Final Score: 84.0 (Risk: 34, Semgrep: 100)

🎯 Processing: quiqr/quiqr-desktop
    🔗 Clone URL: https://github.com/quiqr/quiqr-desktop.git
    📥 Cloning quiqr/quiqr-desktop...
  ✅ Completed: solisoft/fasty (Score: 84.0)
    ✅ Successfully cloned quiqr/quiqr-desktop
    🔍 Running Semgrep on quiqr/quiqr-desktop...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/quiqr_quiqr-desktop_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 176 potential vulnerabilities
    🧹 Cleaned up quiqr/quiqr-desktop
    ✅ quiqr/quiqr-desktop - Final Score: 91.0 (Risk: 41, Semgrep: 100)

🎯 Processing: buttasam/cms-boot  ✅ Completed: quiqr/quiqr-desktop (Score: 91.0)

    🔗 Clone URL: https://github.com/buttasam/cms-boot.git
    📥 Cloning buttasam/cms-boot...
    ✅ Successfully cloned buttasam/cms-boot
    🔍 Running Semgrep on buttasam/cms-boot...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/buttasam_cms-boot_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 22 potential vulnerabilities
    🧹 Cleaned up buttasam/cms-boot
    ✅ buttasam/cms-boot - Final Score: 76.0 (Risk: 26, Semgrep: 100)

🎯 Processing: ecomplus/storefront  ✅ Completed: buttasam/cms-boot (Score: 76.0)

    🔗 Clone URL: https://github.com/ecomplus/storefront.git
    📥 Cloning ecomplus/storefront...
    ✅ Successfully cloned ecomplus/storefront
    🔍 Running Semgrep on ecomplus/storefront...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/ecomplus_storefront_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 159 potential vulnerabilities
    🧹 Cleaned up ecomplus/storefront
    ✅ ecomplus/storefront - Final Score: 84.0 (Risk: 34, Semgrep: 100)

🎯 Processing: sveltia/sveltia-cms-auth  ✅ Completed: ecomplus/storefront (Score: 84.0)

    🔗 Clone URL: https://github.com/sveltia/sveltia-cms-auth.git
    📥 Cloning sveltia/sveltia-cms-auth...
    ✅ Successfully cloned sveltia/sveltia-cms-auth
    🔍 Running Semgrep on sveltia/sveltia-cms-auth...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/sveltia_sveltia-cms-auth_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up sveltia/sveltia-cms-auth
    ✅ sveltia/sveltia-cms-auth - Final Score: 26.5 (Risk: 24, Semgrep: 5)

🎯 Processing: Scrivito/scrivito_example_app_js  ✅ Completed: sveltia/sveltia-cms-auth (Score: 26.5)

    🔗 Clone URL: https://github.com/Scrivito/scrivito_example_app_js.git
    📥 Cloning Scrivito/scrivito_example_app_js...
    ✅ Successfully cloned Scrivito/scrivito_example_app_js
    🔍 Running Semgrep on Scrivito/scrivito_example_app_js...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/Scrivito_scrivito_example_app_js_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 3 potential vulnerabilities
    🧹 Cleaned up Scrivito/scrivito_example_app_js
    ✅ Scrivito/scrivito_example_app_js - Final Score: 21.5 (Risk: 16, Semgrep: 11)

🎯 Processing: railwayapp-templates/strapi
    🔗 Clone URL: https://github.com/railwayapp-templates/strapi.git
  ✅ Completed: Scrivito/scrivito_example_app_js (Score: 21.5)    📥 Cloning railwayapp-templates/strapi...

    ✅ Successfully cloned railwayapp-templates/strapi
    🔍 Running Semgrep on railwayapp-templates/strapi...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/railwayapp-templates_strapi_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up railwayapp-templates/strapi
    ✅ railwayapp-templates/strapi - Final Score: 24.0 (Risk: 24, Semgrep: 0)

🎯 Processing: rpub-clj/rpub  ✅ Completed: railwayapp-templates/strapi (Score: 24.0)

    🔗 Clone URL: https://github.com/rpub-clj/rpub.git
    📥 Cloning rpub-clj/rpub...
    ✅ Successfully cloned rpub-clj/rpub
    🔍 Running Semgrep on rpub-clj/rpub...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/rpub-clj_rpub_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 2 potential vulnerabilities
    🧹 Cleaned up rpub-clj/rpub
    ✅ rpub-clj/rpub - Final Score: 23.5 (Risk: 16, Semgrep: 15)

🎯 Processing: breatheco-de/content  ✅ Completed: rpub-clj/rpub (Score: 23.5)

    🔗 Clone URL: https://github.com/breatheco-de/content.git
    📥 Cloning breatheco-de/content...
    ✅ Successfully cloned breatheco-de/content
    🔍 Running Semgrep on breatheco-de/content...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/breatheco-de_content_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 15 potential vulnerabilities
    🧹 Cleaned up breatheco-de/content
    ✅ breatheco-de/content - Final Score: 58.5 (Risk: 26, Semgrep: 65)

🎯 Processing: ttimot24/HorizontCMS
  ✅ Completed: breatheco-de/content (Score: 58.5)
    🔗 Clone URL: https://github.com/ttimot24/HorizontCMS.git
    📥 Cloning ttimot24/HorizontCMS...
    ✅ Successfully cloned ttimot24/HorizontCMS
    🔍 Running Semgrep on ttimot24/HorizontCMS...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/ttimot24_HorizontCMS_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 12 potential vulnerabilities
    🧹 Cleaned up ttimot24/HorizontCMS
    ✅ ttimot24/HorizontCMS - Final Score: 46.0 (Risk: 16, Semgrep: 60)

🎯 Processing: headwirecom/peregrine-cms
    🔗 Clone URL: https://github.com/headwirecom/peregrine-cms.git
    📥 Cloning headwirecom/peregrine-cms...  ✅ Completed: ttimot24/HorizontCMS (Score: 46.0)

    ✅ Successfully cloned headwirecom/peregrine-cms
    🔍 Running Semgrep on headwirecom/peregrine-cms...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/headwirecom_peregrine-cms_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 97 potential vulnerabilities
    🧹 Cleaned up headwirecom/peregrine-cms
    ✅ headwirecom/peregrine-cms - Final Score: 78.0 (Risk: 28, Semgrep: 100)

🎯 Processing: TianLangStudio/rust_cms  ✅ Completed: headwirecom/peregrine-cms (Score: 78.0)

    🔗 Clone URL: https://github.com/TianLangStudio/rust_cms.git
    📥 Cloning TianLangStudio/rust_cms...
    ✅ Successfully cloned TianLangStudio/rust_cms
    🔍 Running Semgrep on TianLangStudio/rust_cms...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/TianLangStudio_rust_cms_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 18 potential vulnerabilities
    🧹 Cleaned up TianLangStudio/rust_cms
    ✅ TianLangStudio/rust_cms - Final Score: 71.0 (Risk: 21, Semgrep: 100)

🎯 Processing: passkeydeveloper/passkey-authenticator-aaguids
    🔗 Clone URL: https://github.com/passkeydeveloper/passkey-authenticator-aaguids.git
    📥 Cloning passkeydeveloper/passkey-authenticator-aaguids...
  ✅ Completed: TianLangStudio/rust_cms (Score: 71.0)
    ✅ Successfully cloned passkeydeveloper/passkey-authenticator-aaguids
    🔍 Running Semgrep on passkeydeveloper/passkey-authenticator-aaguids...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/passkeydeveloper_passkey-authenticator-aaguids_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up passkeydeveloper/passkey-authenticator-aaguids
    ✅ passkeydeveloper/passkey-authenticator-aaguids - Final Score: 35.0 (Risk: 30, Semgrep: 10)

🎯 Processing: eduardogsilva/wireguard_webadmin  ✅ Completed: passkeydeveloper/passkey-authenticator-aaguids (Score: 35.0)

    🔗 Clone URL: https://github.com/eduardogsilva/wireguard_webadmin.git
    📥 Cloning eduardogsilva/wireguard_webadmin...
    ✅ Successfully cloned eduardogsilva/wireguard_webadmin
    🔍 Running Semgrep on eduardogsilva/wireguard_webadmin...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/eduardogsilva_wireguard_webadmin_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 159 potential vulnerabilities
    🧹 Cleaned up eduardogsilva/wireguard_webadmin
    ✅ eduardogsilva/wireguard_webadmin - Final Score: 71.0 (Risk: 21, Semgrep: 100)

🎯 Processing: teddy-vltn/vinted-discord-bot  ✅ Completed: eduardogsilva/wireguard_webadmin (Score: 71.0)

    🔗 Clone URL: https://github.com/teddy-vltn/vinted-discord-bot.git
    📥 Cloning teddy-vltn/vinted-discord-bot...
    ✅ Successfully cloned teddy-vltn/vinted-discord-bot
    🔍 Running Semgrep on teddy-vltn/vinted-discord-bot...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/teddy-vltn_vinted-discord-bot_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 7 potential vulnerabilities
    🧹 Cleaned up teddy-vltn/vinted-discord-bot
    ✅ teddy-vltn/vinted-discord-bot - Final Score: 39.5 (Risk: 22, Semgrep: 35)

🎯 Processing: authcompanion/authcompanion2  ✅ Completed: teddy-vltn/vinted-discord-bot (Score: 39.5)

    🔗 Clone URL: https://github.com/authcompanion/authcompanion2.git
    📥 Cloning authcompanion/authcompanion2...
    ✅ Successfully cloned authcompanion/authcompanion2
    🔍 Running Semgrep on authcompanion/authcompanion2...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/authcompanion_authcompanion2_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 5 potential vulnerabilities
    🧹 Cleaned up authcompanion/authcompanion2
    ✅ authcompanion/authcompanion2 - Final Score: 55.5 (Risk: 38, Semgrep: 35)

🎯 Processing: RunaVault/RunaVault  ✅ Completed: authcompanion/authcompanion2 (Score: 55.5)
    🔗 Clone URL: https://github.com/RunaVault/RunaVault.git
    📥 Cloning RunaVault/RunaVault...

    ✅ Successfully cloned RunaVault/RunaVault
    🔍 Running Semgrep on RunaVault/RunaVault...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/RunaVault_RunaVault_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 11 potential vulnerabilities
    🧹 Cleaned up RunaVault/RunaVault
    ✅ RunaVault/RunaVault - Final Score: 43.0 (Risk: 18, Semgrep: 50)

🎯 Processing: ayoubhayda/react-admin-dashboard
    🔗 Clone URL: https://github.com/ayoubhayda/react-admin-dashboard.git
    📥 Cloning ayoubhayda/react-admin-dashboard...
  ✅ Completed: RunaVault/RunaVault (Score: 43.0)
    ✅ Successfully cloned ayoubhayda/react-admin-dashboard
    🔍 Running Semgrep on ayoubhayda/react-admin-dashboard...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/ayoubhayda_react-admin-dashboard_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up ayoubhayda/react-admin-dashboard
    ✅ ayoubhayda/react-admin-dashboard - Final Score: 19.0 (Risk: 19, Semgrep: 0)

🎯 Processing: PrismarineJS/node-minecraft-protocol  ✅ Completed: ayoubhayda/react-admin-dashboard (Score: 19.0)

    🔗 Clone URL: https://github.com/PrismarineJS/node-minecraft-protocol.git
    📥 Cloning PrismarineJS/node-minecraft-protocol...
    ✅ Successfully cloned PrismarineJS/node-minecraft-protocol
    🔍 Running Semgrep on PrismarineJS/node-minecraft-protocol...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/PrismarineJS_node-minecraft-protocol_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 6 potential vulnerabilities
    🧹 Cleaned up PrismarineJS/node-minecraft-protocol
    ✅ PrismarineJS/node-minecraft-protocol - Final Score: 48.5 (Risk: 35, Semgrep: 27)

🎯 Processing: apify/proxy-chain  ✅ Completed: PrismarineJS/node-minecraft-protocol (Score: 48.5)

    🔗 Clone URL: https://github.com/apify/proxy-chain.git
    📥 Cloning apify/proxy-chain...
    ✅ Successfully cloned apify/proxy-chain
    🔍 Running Semgrep on apify/proxy-chain...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/apify_proxy-chain_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up apify/proxy-chain
    ✅ apify/proxy-chain - Final Score: 25.0 (Risk: 25, Semgrep: 0)

🎯 Processing: greymass/anchor  ✅ Completed: apify/proxy-chain (Score: 25.0)

    🔗 Clone URL: https://github.com/greymass/anchor.git
    📥 Cloning greymass/anchor...
    ✅ Successfully cloned greymass/anchor
    🔍 Running Semgrep on greymass/anchor...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/greymass_anchor_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 13 potential vulnerabilities
    🧹 Cleaned up greymass/anchor
    ✅ greymass/anchor - Final Score: 64.0 (Risk: 31, Semgrep: 66)

🎯 Processing: GongRzhe/Gmail-MCP-Server  ✅ Completed: greymass/anchor (Score: 64.0)

    🔗 Clone URL: https://github.com/GongRzhe/Gmail-MCP-Server.git
    📥 Cloning GongRzhe/Gmail-MCP-Server...
    ✅ Successfully cloned GongRzhe/Gmail-MCP-Server
    🔍 Running Semgrep on GongRzhe/Gmail-MCP-Server...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/GongRzhe_Gmail-MCP-Server_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 4 potential vulnerabilities
    🧹 Cleaned up GongRzhe/Gmail-MCP-Server
    ✅ GongRzhe/Gmail-MCP-Server - Final Score: 35.5 (Risk: 25, Semgrep: 21)

🎯 Processing: krissrex/google-authenticator-exporter
    🔗 Clone URL: https://github.com/krissrex/google-authenticator-exporter.git
    📥 Cloning krissrex/google-authenticator-exporter...
  ✅ Completed: GongRzhe/Gmail-MCP-Server (Score: 35.5)
    ✅ Successfully cloned krissrex/google-authenticator-exporter
    🔍 Running Semgrep on krissrex/google-authenticator-exporter...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/krissrex_google-authenticator-exporter_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 2 potential vulnerabilities
    🧹 Cleaned up krissrex/google-authenticator-exporter
    ✅ krissrex/google-authenticator-exporter - Final Score: 35.5 (Risk: 30, Semgrep: 11)

🎯 Processing: iGeeky/wolf  ✅ Completed: krissrex/google-authenticator-exporter (Score: 35.5)

    🔗 Clone URL: https://github.com/iGeeky/wolf.git
    📥 Cloning iGeeky/wolf...
    ✅ Successfully cloned iGeeky/wolf
    🔍 Running Semgrep on iGeeky/wolf...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/iGeeky_wolf_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 36 potential vulnerabilities
    🧹 Cleaned up iGeeky/wolf
    ✅ iGeeky/wolf - Final Score: 83.0 (Risk: 33, Semgrep: 100)

🎯 Processing: okta/okta-signin-widget  ✅ Completed: iGeeky/wolf (Score: 83.0)
    🔗 Clone URL: https://github.com/okta/okta-signin-widget.git
    📥 Cloning okta/okta-signin-widget...

    ✅ Successfully cloned okta/okta-signin-widget
    🔍 Running Semgrep on okta/okta-signin-widget...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/okta_okta-signin-widget_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 80 potential vulnerabilities
    🧹 Cleaned up okta/okta-signin-widget
    ✅ okta/okta-signin-widget - Final Score: 85.0 (Risk: 35, Semgrep: 100)

🎯 Processing: awslabs/sandbox-accounts-for-events
  ✅ Completed: okta/okta-signin-widget (Score: 85.0)
    🔗 Clone URL: https://github.com/awslabs/sandbox-accounts-for-events.git
    📥 Cloning awslabs/sandbox-accounts-for-events...
    ✅ Successfully cloned awslabs/sandbox-accounts-for-events
    🔍 Running Semgrep on awslabs/sandbox-accounts-for-events...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/awslabs_sandbox-accounts-for-events_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 12 potential vulnerabilities
    🧹 Cleaned up awslabs/sandbox-accounts-for-events
    ✅ awslabs/sandbox-accounts-for-events - Final Score: 40.0 (Risk: 26, Semgrep: 28)

🎯 Processing: filipedeschamps/parse-google-docs-json
    🔗 Clone URL: https://github.com/filipedeschamps/parse-google-docs-json.git
    📥 Cloning filipedeschamps/parse-google-docs-json...
  ✅ Completed: awslabs/sandbox-accounts-for-events (Score: 40.0)
    ✅ Successfully cloned filipedeschamps/parse-google-docs-json
    🔍 Running Semgrep on filipedeschamps/parse-google-docs-json...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/filipedeschamps_parse-google-docs-json_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up filipedeschamps/parse-google-docs-json
    ✅ filipedeschamps/parse-google-docs-json - Final Score: 29.0 (Risk: 29, Semgrep: 0)

🎯 Processing: hyeonsangjeon/youtube-dl-nas
    🔗 Clone URL: https://github.com/hyeonsangjeon/youtube-dl-nas.git
    📥 Cloning hyeonsangjeon/youtube-dl-nas...
  ✅ Completed: filipedeschamps/parse-google-docs-json (Score: 29.0)
    ✅ Successfully cloned hyeonsangjeon/youtube-dl-nas
    🔍 Running Semgrep on hyeonsangjeon/youtube-dl-nas...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/hyeonsangjeon_youtube-dl-nas_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up hyeonsangjeon/youtube-dl-nas
    ✅ hyeonsangjeon/youtube-dl-nas - Final Score: 35.0 (Risk: 30, Semgrep: 10)

🎯 Processing: lauriskuznecovs/next-js-jwt  ✅ Completed: hyeonsangjeon/youtube-dl-nas (Score: 35.0)

    🔗 Clone URL: https://github.com/lauriskuznecovs/next-js-jwt.git
    📥 Cloning lauriskuznecovs/next-js-jwt...
    ✅ Successfully cloned lauriskuznecovs/next-js-jwt
    🔍 Running Semgrep on lauriskuznecovs/next-js-jwt...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/lauriskuznecovs_next-js-jwt_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up lauriskuznecovs/next-js-jwt
    ✅ lauriskuznecovs/next-js-jwt - Final Score: 33.5 (Risk: 33, Semgrep: 1)

🎯 Processing: Zeeshu911/MERN-Stack-Hospital-Management-System-Web-Application
    🔗 Clone URL: https://github.com/Zeeshu911/MERN-Stack-Hospital-Management-System-Web-Application.git
  ✅ Completed: lauriskuznecovs/next-js-jwt (Score: 33.5)
    📥 Cloning Zeeshu911/MERN-Stack-Hospital-Management-System-Web-Application...
    ✅ Successfully cloned Zeeshu911/MERN-Stack-Hospital-Management-System-Web-Application
    🔍 Running Semgrep on Zeeshu911/MERN-Stack-Hospital-Management-System-Web-Application...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/Zeeshu911_MERN-Stack-Hospital-Management-System-Web-Application_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up Zeeshu911/MERN-Stack-Hospital-Management-System-Web-Application
    ✅ Zeeshu911/MERN-Stack-Hospital-Management-System-Web-Application - Final Score: 31.0 (Risk: 31, Semgrep: 0)

🎯 Processing: qoomon/otp-authenticator-webapp
    🔗 Clone URL: https://github.com/qoomon/otp-authenticator-webapp.git
    📥 Cloning qoomon/otp-authenticator-webapp...
  ✅ Completed: Zeeshu911/MERN-Stack-Hospital-Management-System-Web-Application (Score: 31.0)
    ✅ Successfully cloned qoomon/otp-authenticator-webapp
    🔍 Running Semgrep on qoomon/otp-authenticator-webapp...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/qoomon_otp-authenticator-webapp_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 5 potential vulnerabilities
    🧹 Cleaned up qoomon/otp-authenticator-webapp
    ✅ qoomon/otp-authenticator-webapp - Final Score: 43.5 (Risk: 21, Semgrep: 45)

🎯 Processing: narayan954/dummygram
    🔗 Clone URL: https://github.com/narayan954/dummygram.git
    📥 Cloning narayan954/dummygram...
  ✅ Completed: qoomon/otp-authenticator-webapp (Score: 43.5)
    ✅ Successfully cloned narayan954/dummygram
    🔍 Running Semgrep on narayan954/dummygram...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/narayan954_dummygram_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up narayan954/dummygram
    ✅ narayan954/dummygram - Final Score: 36.0 (Risk: 36, Semgrep: 0)

🎯 Processing: santhoshravindran7/Fabric-Analytics-MCP  ✅ Completed: narayan954/dummygram (Score: 36.0)

    🔗 Clone URL: https://github.com/santhoshravindran7/Fabric-Analytics-MCP.git
    📥 Cloning santhoshravindran7/Fabric-Analytics-MCP...
    ✅ Successfully cloned santhoshravindran7/Fabric-Analytics-MCP
    🔍 Running Semgrep on santhoshravindran7/Fabric-Analytics-MCP...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/santhoshravindran7_Fabric-Analytics-MCP_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 31 potential vulnerabilities
    🧹 Cleaned up santhoshravindran7/Fabric-Analytics-MCP
    ✅ santhoshravindran7/Fabric-Analytics-MCP - Final Score: 64.0 (Risk: 14, Semgrep: 100)

🎯 Processing: osmlab/osm-auth
    🔗 Clone URL: https://github.com/osmlab/osm-auth.git
    📥 Cloning osmlab/osm-auth...  ✅ Completed: santhoshravindran7/Fabric-Analytics-MCP (Score: 64.0)

    ✅ Successfully cloned osmlab/osm-auth
    🔍 Running Semgrep on osmlab/osm-auth...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/osmlab_osm-auth_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 2 potential vulnerabilities
    🧹 Cleaned up osmlab/osm-auth
    ✅ osmlab/osm-auth - Final Score: 24.0 (Risk: 19, Semgrep: 10)

🎯 Processing: serenity-kit/opaque
    🔗 Clone URL: https://github.com/serenity-kit/opaque.git
  ✅ Completed: osmlab/osm-auth (Score: 24.0)    📥 Cloning serenity-kit/opaque...

    ✅ Successfully cloned serenity-kit/opaque
    🔍 Running Semgrep on serenity-kit/opaque...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/serenity-kit_opaque_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 13 potential vulnerabilities
    🧹 Cleaned up serenity-kit/opaque
    ✅ serenity-kit/opaque - Final Score: 44.0 (Risk: 23, Semgrep: 42)

🎯 Processing: PrismarineJS/prismarine-auth  ✅ Completed: serenity-kit/opaque (Score: 44.0)

    🔗 Clone URL: https://github.com/PrismarineJS/prismarine-auth.git
    📥 Cloning PrismarineJS/prismarine-auth...
    ✅ Successfully cloned PrismarineJS/prismarine-auth
    🔍 Running Semgrep on PrismarineJS/prismarine-auth...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/PrismarineJS_prismarine-auth_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 2 potential vulnerabilities
    🧹 Cleaned up PrismarineJS/prismarine-auth
    ✅ PrismarineJS/prismarine-auth - Final Score: 25.0 (Risk: 20, Semgrep: 10)

🎯 Processing: amitkumardemo/EdgeCareer  ✅ Completed: PrismarineJS/prismarine-auth (Score: 25.0)
    🔗 Clone URL: https://github.com/amitkumardemo/EdgeCareer.git
    📥 Cloning amitkumardemo/EdgeCareer...

    ✅ Successfully cloned amitkumardemo/EdgeCareer
    🔍 Running Semgrep on amitkumardemo/EdgeCareer...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/amitkumardemo_EdgeCareer_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 2 potential vulnerabilities
    🧹 Cleaned up amitkumardemo/EdgeCareer
    ✅ amitkumardemo/EdgeCareer - Final Score: 33.0 (Risk: 23, Semgrep: 20)

🎯 Processing: jsumners/node-activedirectory  ✅ Completed: amitkumardemo/EdgeCareer (Score: 33.0)

    🔗 Clone URL: https://github.com/jsumners/node-activedirectory.git
    📥 Cloning jsumners/node-activedirectory...
    ✅ Successfully cloned jsumners/node-activedirectory
    🔍 Running Semgrep on jsumners/node-activedirectory...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/jsumners_node-activedirectory_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up jsumners/node-activedirectory
    ✅ jsumners/node-activedirectory - Final Score: 22.0 (Risk: 22, Semgrep: 0)

🎯 Processing: auth0/webauthn.me
    🔗 Clone URL: https://github.com/auth0/webauthn.me.git  ✅ Completed: jsumners/node-activedirectory (Score: 22.0)

    📥 Cloning auth0/webauthn.me...
    ✅ Successfully cloned auth0/webauthn.me
    🔍 Running Semgrep on auth0/webauthn.me...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/auth0_webauthn.me_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 19 potential vulnerabilities
    🧹 Cleaned up auth0/webauthn.me
    ✅ auth0/webauthn.me - Final Score: 68.0 (Risk: 18, Semgrep: 100)

🎯 Processing: inbasic/open-two-factor-authenticator  ✅ Completed: auth0/webauthn.me (Score: 68.0)

    🔗 Clone URL: https://github.com/inbasic/open-two-factor-authenticator.git
    📥 Cloning inbasic/open-two-factor-authenticator...
    ✅ Successfully cloned inbasic/open-two-factor-authenticator
    🔍 Running Semgrep on inbasic/open-two-factor-authenticator...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/inbasic_open-two-factor-authenticator_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 16 potential vulnerabilities
    🧹 Cleaned up inbasic/open-two-factor-authenticator
    ✅ inbasic/open-two-factor-authenticator - Final Score: 53.5 (Risk: 11, Semgrep: 85)

🎯 Processing: dwainscheeren/dwains-lovelace-dashboard
    🔗 Clone URL: https://github.com/dwainscheeren/dwains-lovelace-dashboard.git
  ✅ Completed: inbasic/open-two-factor-authenticator (Score: 53.5)
    📥 Cloning dwainscheeren/dwains-lovelace-dashboard...
    ✅ Successfully cloned dwainscheeren/dwains-lovelace-dashboard
    🔍 Running Semgrep on dwainscheeren/dwains-lovelace-dashboard...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/dwainscheeren_dwains-lovelace-dashboard_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up dwainscheeren/dwains-lovelace-dashboard
    ✅ dwainscheeren/dwains-lovelace-dashboard - Final Score: 33.5 (Risk: 31, Semgrep: 5)

🎯 Processing: j-a-n/lovelace-wallpanel
    🔗 Clone URL: https://github.com/j-a-n/lovelace-wallpanel.git
    📥 Cloning j-a-n/lovelace-wallpanel...
  ✅ Completed: dwainscheeren/dwains-lovelace-dashboard (Score: 33.5)
    ✅ Successfully cloned j-a-n/lovelace-wallpanel
    🔍 Running Semgrep on j-a-n/lovelace-wallpanel...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/j-a-n_lovelace-wallpanel_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 11 potential vulnerabilities
    🧹 Cleaned up j-a-n/lovelace-wallpanel
    ✅ j-a-n/lovelace-wallpanel - Final Score: 87.0 (Risk: 39, Semgrep: 96)

🎯 Processing: Sanjeet990/Astroluma  ✅ Completed: j-a-n/lovelace-wallpanel (Score: 87.0)
    🔗 Clone URL: https://github.com/Sanjeet990/Astroluma.git
    📥 Cloning Sanjeet990/Astroluma...

    ✅ Successfully cloned Sanjeet990/Astroluma
    🔍 Running Semgrep on Sanjeet990/Astroluma...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/Sanjeet990_Astroluma_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 64 potential vulnerabilities
    🧹 Cleaned up Sanjeet990/Astroluma
    ✅ Sanjeet990/Astroluma - Final Score: 76.0 (Risk: 26, Semgrep: 100)

🎯 Processing: getprimate/primate  ✅ Completed: Sanjeet990/Astroluma (Score: 76.0)

    🔗 Clone URL: https://github.com/getprimate/primate.git
    📥 Cloning getprimate/primate...
    ✅ Successfully cloned getprimate/primate
    🔍 Running Semgrep on getprimate/primate...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/getprimate_primate_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 12 potential vulnerabilities
    🧹 Cleaned up getprimate/primate
    ✅ getprimate/primate - Final Score: 76.5 (Risk: 29, Semgrep: 95)

🎯 Processing: geosolutions-it/MapStore2
  ✅ Completed: getprimate/primate (Score: 76.5)    🔗 Clone URL: https://github.com/geosolutions-it/MapStore2.git
    📥 Cloning geosolutions-it/MapStore2...

    ✅ Successfully cloned geosolutions-it/MapStore2
    🔍 Running Semgrep on geosolutions-it/MapStore2...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/geosolutions-it_MapStore2_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 123 potential vulnerabilities
    🧹 Cleaned up geosolutions-it/MapStore2
    ✅ geosolutions-it/MapStore2 - Final Score: 86.0 (Risk: 36, Semgrep: 100)

🎯 Processing: meceware/wapy.dev  ✅ Completed: geosolutions-it/MapStore2 (Score: 86.0)

    🔗 Clone URL: https://github.com/meceware/wapy.dev.git
    📥 Cloning meceware/wapy.dev...
    ✅ Successfully cloned meceware/wapy.dev
    🔍 Running Semgrep on meceware/wapy.dev...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/meceware_wapy.dev_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up meceware/wapy.dev
    ✅ meceware/wapy.dev - Final Score: 31.0 (Risk: 26, Semgrep: 10)

🎯 Processing: WJDDesigns/Ultra-Vehicle-Card
    🔗 Clone URL: https://github.com/WJDDesigns/Ultra-Vehicle-Card.git
    📥 Cloning WJDDesigns/Ultra-Vehicle-Card...
  ✅ Completed: meceware/wapy.dev (Score: 31.0)
    ✅ Successfully cloned WJDDesigns/Ultra-Vehicle-Card
    🔍 Running Semgrep on WJDDesigns/Ultra-Vehicle-Card...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/WJDDesigns_Ultra-Vehicle-Card_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up WJDDesigns/Ultra-Vehicle-Card
    ✅ WJDDesigns/Ultra-Vehicle-Card - Final Score: 26.0 (Risk: 26, Semgrep: 0)

🎯 Processing: riemann/riemann-dash  ✅ Completed: WJDDesigns/Ultra-Vehicle-Card (Score: 26.0)
    🔗 Clone URL: https://github.com/riemann/riemann-dash.git
    📥 Cloning riemann/riemann-dash...

    ✅ Successfully cloned riemann/riemann-dash
    🔍 Running Semgrep on riemann/riemann-dash...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/riemann_riemann-dash_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up riemann/riemann-dash
    ✅ riemann/riemann-dash - Final Score: 31.0 (Risk: 26, Semgrep: 10)

🎯 Processing: LachlanDev/Discord-BOT-Dashboard-V2
    🔗 Clone URL: https://github.com/LachlanDev/Discord-BOT-Dashboard-V2.git  ✅ Completed: riemann/riemann-dash (Score: 31.0)
    📥 Cloning LachlanDev/Discord-BOT-Dashboard-V2...

    ✅ Successfully cloned LachlanDev/Discord-BOT-Dashboard-V2
    🔍 Running Semgrep on LachlanDev/Discord-BOT-Dashboard-V2...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/LachlanDev_Discord-BOT-Dashboard-V2_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 20 potential vulnerabilities
    🧹 Cleaned up LachlanDev/Discord-BOT-Dashboard-V2
    ✅ LachlanDev/Discord-BOT-Dashboard-V2 - Final Score: 81.0 (Risk: 31, Semgrep: 100)

🎯 Processing: erohtar/Dasherr
    🔗 Clone URL: https://github.com/erohtar/Dasherr.git
    📥 Cloning erohtar/Dasherr...
  ✅ Completed: LachlanDev/Discord-BOT-Dashboard-V2 (Score: 81.0)
    ✅ Successfully cloned erohtar/Dasherr
    🔍 Running Semgrep on erohtar/Dasherr...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/erohtar_Dasherr_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 16 potential vulnerabilities
    🧹 Cleaned up erohtar/Dasherr
    ✅ erohtar/Dasherr - Final Score: 81.0 (Risk: 31, Semgrep: 100)

🎯 Processing: apache/shenyu-dashboard
    🔗 Clone URL: https://github.com/apache/shenyu-dashboard.git
    📥 Cloning apache/shenyu-dashboard...  ✅ Completed: erohtar/Dasherr (Score: 81.0)

    ✅ Successfully cloned apache/shenyu-dashboard
    🔍 Running Semgrep on apache/shenyu-dashboard...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/apache_shenyu-dashboard_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 11 potential vulnerabilities
    🧹 Cleaned up apache/shenyu-dashboard
    ✅ apache/shenyu-dashboard - Final Score: 53.5 (Risk: 26, Semgrep: 55)

🎯 Processing: TarsCloud/TarsWeb
    🔗 Clone URL: https://github.com/TarsCloud/TarsWeb.git
    📥 Cloning TarsCloud/TarsWeb...
  ✅ Completed: apache/shenyu-dashboard (Score: 53.5)
    ✅ Successfully cloned TarsCloud/TarsWeb
    🔍 Running Semgrep on TarsCloud/TarsWeb...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/TarsCloud_TarsWeb_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 45 potential vulnerabilities
    🧹 Cleaned up TarsCloud/TarsWeb
    ✅ TarsCloud/TarsWeb - Final Score: 81.0 (Risk: 31, Semgrep: 100)

🎯 Processing: node-red/node-red-ui-nodes
    🔗 Clone URL: https://github.com/node-red/node-red-ui-nodes.git
    📥 Cloning node-red/node-red-ui-nodes...
  ✅ Completed: TarsCloud/TarsWeb (Score: 81.0)
    ✅ Successfully cloned node-red/node-red-ui-nodes
    🔍 Running Semgrep on node-red/node-red-ui-nodes...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/node-red_node-red-ui-nodes_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 39 potential vulnerabilities
    🧹 Cleaned up node-red/node-red-ui-nodes
    ✅ node-red/node-red-ui-nodes - Final Score: 81.0 (Risk: 31, Semgrep: 100)

🎯 Processing: MixinNetwork/developers.mixin.one
    🔗 Clone URL: https://github.com/MixinNetwork/developers.mixin.one.git
    📥 Cloning MixinNetwork/developers.mixin.one...
  ✅ Completed: node-red/node-red-ui-nodes (Score: 81.0)
    ✅ Successfully cloned MixinNetwork/developers.mixin.one
    🔍 Running Semgrep on MixinNetwork/developers.mixin.one...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/MixinNetwork_developers.mixin.one_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 12 potential vulnerabilities
    🧹 Cleaned up MixinNetwork/developers.mixin.one
    ✅ MixinNetwork/developers.mixin.one - Final Score: 68.5 (Risk: 26, Semgrep: 85)

🎯 Processing: iamtherufus/Homio
    🔗 Clone URL: https://github.com/iamtherufus/Homio.git
    📥 Cloning iamtherufus/Homio...
  ✅ Completed: MixinNetwork/developers.mixin.one (Score: 68.5)
    ✅ Successfully cloned iamtherufus/Homio
    🔍 Running Semgrep on iamtherufus/Homio...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/iamtherufus_Homio_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 2 potential vulnerabilities
    🧹 Cleaned up iamtherufus/Homio
    ✅ iamtherufus/Homio - Final Score: 33.5 (Risk: 26, Semgrep: 15)

🎯 Processing: nextcloud/serverinfo
    🔗 Clone URL: https://github.com/nextcloud/serverinfo.git
    📥 Cloning nextcloud/serverinfo...
  ✅ Completed: iamtherufus/Homio (Score: 33.5)
    ✅ Successfully cloned nextcloud/serverinfo
    🔍 Running Semgrep on nextcloud/serverinfo...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/nextcloud_serverinfo_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 6 potential vulnerabilities
    🧹 Cleaned up nextcloud/serverinfo
    ✅ nextcloud/serverinfo - Final Score: 59.0 (Risk: 29, Semgrep: 60)

🎯 Processing: netreconlab/parse-hipaa
    🔗 Clone URL: https://github.com/netreconlab/parse-hipaa.git
    📥 Cloning netreconlab/parse-hipaa...
  ✅ Completed: nextcloud/serverinfo (Score: 59.0)
    ✅ Successfully cloned netreconlab/parse-hipaa
    🔍 Running Semgrep on netreconlab/parse-hipaa...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/netreconlab_parse-hipaa_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 36 potential vulnerabilities
    🧹 Cleaned up netreconlab/parse-hipaa
    ✅ netreconlab/parse-hipaa - Final Score: 81.0 (Risk: 31, Semgrep: 100)

🎯 Processing: telerik/kendoui-northwind-dashboard
    🔗 Clone URL: https://github.com/telerik/kendoui-northwind-dashboard.git  ✅ Completed: netreconlab/parse-hipaa (Score: 81.0)

    📥 Cloning telerik/kendoui-northwind-dashboard...
    ✅ Successfully cloned telerik/kendoui-northwind-dashboard
    🔍 Running Semgrep on telerik/kendoui-northwind-dashboard...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/telerik_kendoui-northwind-dashboard_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 82 potential vulnerabilities
    🧹 Cleaned up telerik/kendoui-northwind-dashboard
    ✅ telerik/kendoui-northwind-dashboard - Final Score: 66.0 (Risk: 16, Semgrep: 100)

🎯 Processing: athombv/homey.ink  ✅ Completed: telerik/kendoui-northwind-dashboard (Score: 66.0)

    🔗 Clone URL: https://github.com/athombv/homey.ink.git
    📥 Cloning athombv/homey.ink...
    ✅ Successfully cloned athombv/homey.ink
    🔍 Running Semgrep on athombv/homey.ink...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/athombv_homey.ink_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 13 potential vulnerabilities
    🧹 Cleaned up athombv/homey.ink
    ✅ athombv/homey.ink - Final Score: 71.0 (Risk: 21, Semgrep: 100)

🎯 Processing: Persian-Caesar/Ticker-Boy  ✅ Completed: athombv/homey.ink (Score: 71.0)

    🔗 Clone URL: https://github.com/Persian-Caesar/Ticker-Boy.git
    📥 Cloning Persian-Caesar/Ticker-Boy...
    ✅ Successfully cloned Persian-Caesar/Ticker-Boy
    🔍 Running Semgrep on Persian-Caesar/Ticker-Boy...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/Persian-Caesar_Ticker-Boy_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 5 potential vulnerabilities
    🧹 Cleaned up Persian-Caesar/Ticker-Boy
    ✅ Persian-Caesar/Ticker-Boy - Final Score: 23.5 (Risk: 21, Semgrep: 5)

🎯 Processing: openstack/skyline-console
    🔗 Clone URL: https://github.com/openstack/skyline-console.git
    📥 Cloning openstack/skyline-console...
  ✅ Completed: Persian-Caesar/Ticker-Boy (Score: 23.5)
    ✅ Successfully cloned openstack/skyline-console
    🔍 Running Semgrep on openstack/skyline-console...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/openstack_skyline-console_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 11 potential vulnerabilities
    🧹 Cleaned up openstack/skyline-console
    ✅ openstack/skyline-console - Final Score: 51.0 (Risk: 21, Semgrep: 60)

🎯 Processing: stagehacks/Cue-View  ✅ Completed: openstack/skyline-console (Score: 51.0)

    🔗 Clone URL: https://github.com/stagehacks/Cue-View.git
    📥 Cloning stagehacks/Cue-View...
    ✅ Successfully cloned stagehacks/Cue-View
    🔍 Running Semgrep on stagehacks/Cue-View...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/stagehacks_Cue-View_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 21 potential vulnerabilities
    🧹 Cleaned up stagehacks/Cue-View
    ✅ stagehacks/Cue-View - Final Score: 71.0 (Risk: 21, Semgrep: 100)

🎯 Processing: phoenixcoded/saasable-ui  ✅ Completed: stagehacks/Cue-View (Score: 71.0)

    🔗 Clone URL: https://github.com/phoenixcoded/saasable-ui.git
    📥 Cloning phoenixcoded/saasable-ui...
    ✅ Successfully cloned phoenixcoded/saasable-ui
    🔍 Running Semgrep on phoenixcoded/saasable-ui...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/phoenixcoded_saasable-ui_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 2 potential vulnerabilities
    🧹 Cleaned up phoenixcoded/saasable-ui
    ✅ phoenixcoded/saasable-ui - Final Score: 26.5 (Risk: 19, Semgrep: 15)

🎯 Processing: GoogleCloudPlatform/marketing-analytics-jumpstart-dataform  ✅ Completed: phoenixcoded/saasable-ui (Score: 26.5)

    🔗 Clone URL: https://github.com/GoogleCloudPlatform/marketing-analytics-jumpstart-dataform.git
    📥 Cloning GoogleCloudPlatform/marketing-analytics-jumpstart-dataform...
    ✅ Successfully cloned GoogleCloudPlatform/marketing-analytics-jumpstart-dataform
    🔍 Running Semgrep on GoogleCloudPlatform/marketing-analytics-jumpstart-dataform...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/GoogleCloudPlatform_marketing-analytics-jumpstart-dataform_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up GoogleCloudPlatform/marketing-analytics-jumpstart-dataform
    ✅ GoogleCloudPlatform/marketing-analytics-jumpstart-dataform - Final Score: 21.0 (Risk: 21, Semgrep: 0)

🎯 Processing: Dashticz/dashticz  ✅ Completed: GoogleCloudPlatform/marketing-analytics-jumpstart-dataform (Score: 21.0)

    🔗 Clone URL: https://github.com/Dashticz/dashticz.git
    📥 Cloning Dashticz/dashticz...
    ✅ Successfully cloned Dashticz/dashticz
    🔍 Running Semgrep on Dashticz/dashticz...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/Dashticz_dashticz_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 18 potential vulnerabilities
    🧹 Cleaned up Dashticz/dashticz
    ✅ Dashticz/dashticz - Final Score: 79.0 (Risk: 29, Semgrep: 100)

🎯 Processing: mozilla-frontend-infra/firefox-performance-dashboards
    🔗 Clone URL: https://github.com/mozilla-frontend-infra/firefox-performance-dashboards.git  ✅ Completed: Dashticz/dashticz (Score: 79.0)

    📥 Cloning mozilla-frontend-infra/firefox-performance-dashboards...
    ✅ Successfully cloned mozilla-frontend-infra/firefox-performance-dashboards
    🔍 Running Semgrep on mozilla-frontend-infra/firefox-performance-dashboards...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/mozilla-frontend-infra_firefox-performance-dashboards_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up mozilla-frontend-infra/firefox-performance-dashboards
    ✅ mozilla-frontend-infra/firefox-performance-dashboards - Final Score: 21.0 (Risk: 21, Semgrep: 0)

🎯 Processing: abhishekkr/ansible-dashr
    🔗 Clone URL: https://github.com/abhishekkr/ansible-dashr.git
    📥 Cloning abhishekkr/ansible-dashr...
  ✅ Completed: mozilla-frontend-infra/firefox-performance-dashboards (Score: 21.0)
    ✅ Successfully cloned abhishekkr/ansible-dashr
    🔍 Running Semgrep on abhishekkr/ansible-dashr...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/abhishekkr_ansible-dashr_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 21 potential vulnerabilities
    🧹 Cleaned up abhishekkr/ansible-dashr
    ✅ abhishekkr/ansible-dashr - Final Score: 66.0 (Risk: 16, Semgrep: 100)

🎯 Processing: bjornstar/Tumblr-Savior
    🔗 Clone URL: https://github.com/bjornstar/Tumblr-Savior.git
    📥 Cloning bjornstar/Tumblr-Savior...
  ✅ Completed: abhishekkr/ansible-dashr (Score: 66.0)
    ✅ Successfully cloned bjornstar/Tumblr-Savior
    🔍 Running Semgrep on bjornstar/Tumblr-Savior...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/bjornstar_Tumblr-Savior_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up bjornstar/Tumblr-Savior
    ✅ bjornstar/Tumblr-Savior - Final Score: 18.5 (Risk: 16, Semgrep: 5)

🎯 Processing: webdetails/cde  ✅ Completed: bjornstar/Tumblr-Savior (Score: 18.5)

    🔗 Clone URL: https://github.com/webdetails/cde.git
    📥 Cloning webdetails/cde...
    ✅ Successfully cloned webdetails/cde
    🔍 Running Semgrep on webdetails/cde...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/webdetails_cde_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 116 potential vulnerabilities
    🧹 Cleaned up webdetails/cde
    ✅ webdetails/cde - Final Score: 66.0 (Risk: 16, Semgrep: 100)

🎯 Processing: frc-web-components/frc-web-components
    🔗 Clone URL: https://github.com/frc-web-components/frc-web-components.git
    📥 Cloning frc-web-components/frc-web-components...
  ✅ Completed: webdetails/cde (Score: 66.0)
    ✅ Successfully cloned frc-web-components/frc-web-components
    🔍 Running Semgrep on frc-web-components/frc-web-components...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/frc-web-components_frc-web-components_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 22 potential vulnerabilities
    🧹 Cleaned up frc-web-components/frc-web-components
    ✅ frc-web-components/frc-web-components - Final Score: 71.0 (Risk: 21, Semgrep: 100)

🎯 Processing: t0nyz0/BambuBoard
  ✅ Completed: frc-web-components/frc-web-components (Score: 71.0)    🔗 Clone URL: https://github.com/t0nyz0/BambuBoard.git

    📥 Cloning t0nyz0/BambuBoard...
    ✅ Successfully cloned t0nyz0/BambuBoard
    🔍 Running Semgrep on t0nyz0/BambuBoard...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/t0nyz0_BambuBoard_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 32 potential vulnerabilities
    🧹 Cleaned up t0nyz0/BambuBoard
    ✅ t0nyz0/BambuBoard - Final Score: 66.0 (Risk: 16, Semgrep: 100)

🎯 Processing: bernikr/lovelace-notify-card
  ✅ Completed: t0nyz0/BambuBoard (Score: 66.0)
    🔗 Clone URL: https://github.com/bernikr/lovelace-notify-card.git
    📥 Cloning bernikr/lovelace-notify-card...
    ✅ Successfully cloned bernikr/lovelace-notify-card
    🔍 Running Semgrep on bernikr/lovelace-notify-card...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/bernikr_lovelace-notify-card_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 2 potential vulnerabilities
    🧹 Cleaned up bernikr/lovelace-notify-card
    ✅ bernikr/lovelace-notify-card - Final Score: 31.0 (Risk: 21, Semgrep: 20)

🎯 Processing: boogheta/coronavirus-countries
    🔗 Clone URL: https://github.com/boogheta/coronavirus-countries.git
    📥 Cloning boogheta/coronavirus-countries...
  ✅ Completed: bernikr/lovelace-notify-card (Score: 31.0)
    ✅ Successfully cloned boogheta/coronavirus-countries
    🔍 Running Semgrep on boogheta/coronavirus-countries...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/boogheta_coronavirus-countries_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up boogheta/coronavirus-countries
    ✅ boogheta/coronavirus-countries - Final Score: 23.5 (Risk: 21, Semgrep: 5)

🎯 Processing: TutulDevs/mui-practice
    🔗 Clone URL: https://github.com/TutulDevs/mui-practice.git
    📥 Cloning TutulDevs/mui-practice...
  ✅ Completed: boogheta/coronavirus-countries (Score: 23.5)
    ✅ Successfully cloned TutulDevs/mui-practice
    🔍 Running Semgrep on TutulDevs/mui-practice...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/TutulDevs_mui-practice_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up TutulDevs/mui-practice
    ✅ TutulDevs/mui-practice - Final Score: 21.0 (Risk: 21, Semgrep: 0)

🎯 Processing: NationalSecurityAgency/skills-docs
    🔗 Clone URL: https://github.com/NationalSecurityAgency/skills-docs.git  ✅ Completed: TutulDevs/mui-practice (Score: 21.0)

    📥 Cloning NationalSecurityAgency/skills-docs...
    ✅ Successfully cloned NationalSecurityAgency/skills-docs
    🔍 Running Semgrep on NationalSecurityAgency/skills-docs...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/NationalSecurityAgency_skills-docs_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 2 potential vulnerabilities
    🧹 Cleaned up NationalSecurityAgency/skills-docs
    ✅ NationalSecurityAgency/skills-docs - Final Score: 29.0 (Risk: 24, Semgrep: 10)

🎯 Processing: michbeck100/pv-monitoring  ✅ Completed: NationalSecurityAgency/skills-docs (Score: 29.0)

    🔗 Clone URL: https://github.com/michbeck100/pv-monitoring.git
    📥 Cloning michbeck100/pv-monitoring...
    ✅ Successfully cloned michbeck100/pv-monitoring
    🔍 Running Semgrep on michbeck100/pv-monitoring...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/michbeck100_pv-monitoring_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up michbeck100/pv-monitoring
    ✅ michbeck100/pv-monitoring - Final Score: 21.0 (Risk: 21, Semgrep: 0)

🎯 Processing: Purfview/IMDb-Scout-Mod  ✅ Completed: michbeck100/pv-monitoring (Score: 21.0)
    🔗 Clone URL: https://github.com/Purfview/IMDb-Scout-Mod.git
    📥 Cloning Purfview/IMDb-Scout-Mod...

    ✅ Successfully cloned Purfview/IMDb-Scout-Mod
    🔍 Running Semgrep on Purfview/IMDb-Scout-Mod...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/Purfview_IMDb-Scout-Mod_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up Purfview/IMDb-Scout-Mod
    ✅ Purfview/IMDb-Scout-Mod - Final Score: 35.0 (Risk: 35, Semgrep: 0)

🎯 Processing: BleedingXiko/GhostHub  ✅ Completed: Purfview/IMDb-Scout-Mod (Score: 35.0)
    🔗 Clone URL: https://github.com/BleedingXiko/GhostHub.git
    📥 Cloning BleedingXiko/GhostHub...

    ✅ Successfully cloned BleedingXiko/GhostHub
    🔍 Running Semgrep on BleedingXiko/GhostHub...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/BleedingXiko_GhostHub_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 28 potential vulnerabilities
    🧹 Cleaned up BleedingXiko/GhostHub
    ✅ BleedingXiko/GhostHub - Final Score: 84.0 (Risk: 34, Semgrep: 100)

🎯 Processing: robinp7720/Oblecto
    🔗 Clone URL: https://github.com/robinp7720/Oblecto.git
  ✅ Completed: BleedingXiko/GhostHub (Score: 84.0)
    📥 Cloning robinp7720/Oblecto...
    ✅ Successfully cloned robinp7720/Oblecto
    🔍 Running Semgrep on robinp7720/Oblecto...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/robinp7720_Oblecto_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 25 potential vulnerabilities
    🧹 Cleaned up robinp7720/Oblecto
    ✅ robinp7720/Oblecto - Final Score: 76.0 (Risk: 26, Semgrep: 100)

🎯 Processing: Drewpeifer/medialytics  ✅ Completed: robinp7720/Oblecto (Score: 76.0)

    🔗 Clone URL: https://github.com/Drewpeifer/medialytics.git
    📥 Cloning Drewpeifer/medialytics...
    ✅ Successfully cloned Drewpeifer/medialytics
    🔍 Running Semgrep on Drewpeifer/medialytics...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/Drewpeifer_medialytics_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 6 potential vulnerabilities
    🧹 Cleaned up Drewpeifer/medialytics
    ✅ Drewpeifer/medialytics - Final Score: 46.0 (Risk: 31, Semgrep: 30)

🎯 Processing: ant-media/StreamApp
    🔗 Clone URL: https://github.com/ant-media/StreamApp.git  ✅ Completed: Drewpeifer/medialytics (Score: 46.0)

    📥 Cloning ant-media/StreamApp...
    ✅ Successfully cloned ant-media/StreamApp
    🔍 Running Semgrep on ant-media/StreamApp...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/ant-media_StreamApp_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 70 potential vulnerabilities
    🧹 Cleaned up ant-media/StreamApp
    ✅ ant-media/StreamApp - Final Score: 71.0 (Risk: 21, Semgrep: 100)

🎯 Processing: self-evolving-runtimes/revolve
    🔗 Clone URL: https://github.com/self-evolving-runtimes/revolve.git
    📥 Cloning self-evolving-runtimes/revolve...
  ✅ Completed: ant-media/StreamApp (Score: 71.0)
    ✅ Successfully cloned self-evolving-runtimes/revolve
    🔍 Running Semgrep on self-evolving-runtimes/revolve...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/self-evolving-runtimes_revolve_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 17 potential vulnerabilities
    🧹 Cleaned up self-evolving-runtimes/revolve
    ✅ self-evolving-runtimes/revolve - Final Score: 84.0 (Risk: 34, Semgrep: 100)

🎯 Processing: musevarg/Transportation-Management-System
    🔗 Clone URL: https://github.com/musevarg/Transportation-Management-System.git
    📥 Cloning musevarg/Transportation-Management-System...  ✅ Completed: self-evolving-runtimes/revolve (Score: 84.0)

    ✅ Successfully cloned musevarg/Transportation-Management-System
    🔍 Running Semgrep on musevarg/Transportation-Management-System...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/musevarg_Transportation-Management-System_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 211 potential vulnerabilities
    🧹 Cleaned up musevarg/Transportation-Management-System
    ✅ musevarg/Transportation-Management-System - Final Score: 79.0 (Risk: 29, Semgrep: 100)

🎯 Processing: SmartBase-SK/django-smartbase-admin  ✅ Completed: musevarg/Transportation-Management-System (Score: 79.0)
    🔗 Clone URL: https://github.com/SmartBase-SK/django-smartbase-admin.git

    📥 Cloning SmartBase-SK/django-smartbase-admin...
    ✅ Successfully cloned SmartBase-SK/django-smartbase-admin
    🔍 Running Semgrep on SmartBase-SK/django-smartbase-admin...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/SmartBase-SK_django-smartbase-admin_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 89 potential vulnerabilities
    🧹 Cleaned up SmartBase-SK/django-smartbase-admin
    ✅ SmartBase-SK/django-smartbase-admin - Final Score: 71.0 (Risk: 21, Semgrep: 100)

🎯 Processing: pallets-eco/flask-wtf  ✅ Completed: SmartBase-SK/django-smartbase-admin (Score: 71.0)
    🔗 Clone URL: https://github.com/pallets-eco/flask-wtf.git
    📥 Cloning pallets-eco/flask-wtf...

    ✅ Successfully cloned pallets-eco/flask-wtf
    🔍 Running Semgrep on pallets-eco/flask-wtf...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/pallets-eco_flask-wtf_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 11 potential vulnerabilities
    🧹 Cleaned up pallets-eco/flask-wtf
    ✅ pallets-eco/flask-wtf - Final Score: 58.5 (Risk: 31, Semgrep: 55)

🎯 Processing: SilentDemonSD/WZML-X
    🔗 Clone URL: https://github.com/SilentDemonSD/WZML-X.git
    📥 Cloning SilentDemonSD/WZML-X...
  ✅ Completed: pallets-eco/flask-wtf (Score: 58.5)
    ✅ Successfully cloned SilentDemonSD/WZML-X
    🔍 Running Semgrep on SilentDemonSD/WZML-X...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/SilentDemonSD_WZML-X_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 26 potential vulnerabilities
    🧹 Cleaned up SilentDemonSD/WZML-X
    ✅ SilentDemonSD/WZML-X - Final Score: 82.0 (Risk: 32, Semgrep: 100)

🎯 Processing: kalanakt/All-Url-Uploader
    🔗 Clone URL: https://github.com/kalanakt/All-Url-Uploader.git
    📥 Cloning kalanakt/All-Url-Uploader...
  ✅ Completed: SilentDemonSD/WZML-X (Score: 82.0)
    ✅ Successfully cloned kalanakt/All-Url-Uploader
    🔍 Running Semgrep on kalanakt/All-Url-Uploader...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/kalanakt_All-Url-Uploader_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up kalanakt/All-Url-Uploader
    ✅ kalanakt/All-Url-Uploader - Final Score: 37.0 (Risk: 32, Semgrep: 10)

🎯 Processing: devgaganin/Save-Restricted-Content-Bot-v2
  ✅ Completed: kalanakt/All-Url-Uploader (Score: 37.0)    🔗 Clone URL: https://github.com/devgaganin/Save-Restricted-Content-Bot-v2.git
    📥 Cloning devgaganin/Save-Restricted-Content-Bot-v2...

    ✅ Successfully cloned devgaganin/Save-Restricted-Content-Bot-v2
    🔍 Running Semgrep on devgaganin/Save-Restricted-Content-Bot-v2...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/devgaganin_Save-Restricted-Content-Bot-v2_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 4 potential vulnerabilities
    🧹 Cleaned up devgaganin/Save-Restricted-Content-Bot-v2
    ✅ devgaganin/Save-Restricted-Content-Bot-v2 - Final Score: 39.5 (Risk: 27, Semgrep: 25)

🎯 Processing: AtticusZeller/fastapi_supabase_template  ✅ Completed: devgaganin/Save-Restricted-Content-Bot-v2 (Score: 39.5)

    🔗 Clone URL: https://github.com/AtticusZeller/fastapi_supabase_template.git
    📥 Cloning AtticusZeller/fastapi_supabase_template...
    ✅ Successfully cloned AtticusZeller/fastapi_supabase_template
    🔍 Running Semgrep on AtticusZeller/fastapi_supabase_template...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/AtticusZeller_fastapi_supabase_template_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 2 potential vulnerabilities
    🧹 Cleaned up AtticusZeller/fastapi_supabase_template
    ✅ AtticusZeller/fastapi_supabase_template - Final Score: 50.0 (Risk: 40, Semgrep: 20)

🎯 Processing: ryo-ma/gpt-assistants-api-ui
    🔗 Clone URL: https://github.com/ryo-ma/gpt-assistants-api-ui.git
    📥 Cloning ryo-ma/gpt-assistants-api-ui...
  ✅ Completed: AtticusZeller/fastapi_supabase_template (Score: 50.0)
    ✅ Successfully cloned ryo-ma/gpt-assistants-api-ui
    🔍 Running Semgrep on ryo-ma/gpt-assistants-api-ui...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/ryo-ma_gpt-assistants-api-ui_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 2 potential vulnerabilities
    🧹 Cleaned up ryo-ma/gpt-assistants-api-ui
    ✅ ryo-ma/gpt-assistants-api-ui - Final Score: 52.0 (Risk: 42, Semgrep: 20)

🎯 Processing: AeonOrg/Aeon-MLTB
    🔗 Clone URL: https://github.com/AeonOrg/Aeon-MLTB.git
    📥 Cloning AeonOrg/Aeon-MLTB...  ✅ Completed: ryo-ma/gpt-assistants-api-ui (Score: 52.0)

    ✅ Successfully cloned AeonOrg/Aeon-MLTB
    🔍 Running Semgrep on AeonOrg/Aeon-MLTB...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/AeonOrg_Aeon-MLTB_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 28 potential vulnerabilities
    🧹 Cleaned up AeonOrg/Aeon-MLTB
    ✅ AeonOrg/Aeon-MLTB - Final Score: 100.0 (Risk: 51, Semgrep: 100)

🎯 Processing: GeorgiKeranov/facebook-marketplace-bot  ✅ Completed: AeonOrg/Aeon-MLTB (Score: 100.0)

    🔗 Clone URL: https://github.com/GeorgiKeranov/facebook-marketplace-bot.git
    📥 Cloning GeorgiKeranov/facebook-marketplace-bot...
    ✅ Successfully cloned GeorgiKeranov/facebook-marketplace-bot
    🔍 Running Semgrep on GeorgiKeranov/facebook-marketplace-bot...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/GeorgiKeranov_facebook-marketplace-bot_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 3 potential vulnerabilities
    🧹 Cleaned up GeorgiKeranov/facebook-marketplace-bot
    ✅ GeorgiKeranov/facebook-marketplace-bot - Final Score: 39.5 (Risk: 32, Semgrep: 15)

🎯 Processing: iamleot/transferwee
    🔗 Clone URL: https://github.com/iamleot/transferwee.git
    📥 Cloning iamleot/transferwee...
  ✅ Completed: GeorgiKeranov/facebook-marketplace-bot (Score: 39.5)
    ✅ Successfully cloned iamleot/transferwee
    🔍 Running Semgrep on iamleot/transferwee...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/iamleot_transferwee_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up iamleot/transferwee
    ✅ iamleot/transferwee - Final Score: 27.0 (Risk: 27, Semgrep: 0)

🎯 Processing: kennell/curldrop
  ✅ Completed: iamleot/transferwee (Score: 27.0)
    🔗 Clone URL: https://github.com/kennell/curldrop.git
    📥 Cloning kennell/curldrop...
    ✅ Successfully cloned kennell/curldrop
    🔍 Running Semgrep on kennell/curldrop...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/kennell_curldrop_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up kennell/curldrop
    ✅ kennell/curldrop - Final Score: 33.5 (Risk: 31, Semgrep: 5)

🎯 Processing: zerodha/frappe-attachments-s3
    🔗 Clone URL: https://github.com/zerodha/frappe-attachments-s3.git  ✅ Completed: kennell/curldrop (Score: 33.5)

    📥 Cloning zerodha/frappe-attachments-s3...
    ✅ Successfully cloned zerodha/frappe-attachments-s3
    🔍 Running Semgrep on zerodha/frappe-attachments-s3...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/zerodha_frappe-attachments-s3_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up zerodha/frappe-attachments-s3
    ✅ zerodha/frappe-attachments-s3 - Final Score: 27.0 (Risk: 27, Semgrep: 0)

🎯 Processing: uploadcare/pyuploadcare  ✅ Completed: zerodha/frappe-attachments-s3 (Score: 27.0)
    🔗 Clone URL: https://github.com/uploadcare/pyuploadcare.git
    📥 Cloning uploadcare/pyuploadcare...

    ✅ Successfully cloned uploadcare/pyuploadcare
    🔍 Running Semgrep on uploadcare/pyuploadcare...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/uploadcare_pyuploadcare_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 3 potential vulnerabilities
    🧹 Cleaned up uploadcare/pyuploadcare
    ✅ uploadcare/pyuploadcare - Final Score: 34.5 (Risk: 27, Semgrep: 15)

🎯 Processing: devgaganin/Save-Restricted-Content-Bot-v3
    🔗 Clone URL: https://github.com/devgaganin/Save-Restricted-Content-Bot-v3.git
    📥 Cloning devgaganin/Save-Restricted-Content-Bot-v3...  ✅ Completed: uploadcare/pyuploadcare (Score: 34.5)

    ✅ Successfully cloned devgaganin/Save-Restricted-Content-Bot-v3
    🔍 Running Semgrep on devgaganin/Save-Restricted-Content-Bot-v3...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/devgaganin_Save-Restricted-Content-Bot-v3_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 3 potential vulnerabilities
    🧹 Cleaned up devgaganin/Save-Restricted-Content-Bot-v3
    ✅ devgaganin/Save-Restricted-Content-Bot-v3 - Final Score: 37.0 (Risk: 27, Semgrep: 20)

🎯 Processing: cdfxscrq/GDrive-Uploader-TG-Bot  ✅ Completed: devgaganin/Save-Restricted-Content-Bot-v3 (Score: 37.0)

    🔗 Clone URL: https://github.com/cdfxscrq/GDrive-Uploader-TG-Bot.git
    📥 Cloning cdfxscrq/GDrive-Uploader-TG-Bot...
    ✅ Successfully cloned cdfxscrq/GDrive-Uploader-TG-Bot
    🔍 Running Semgrep on cdfxscrq/GDrive-Uploader-TG-Bot...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/cdfxscrq_GDrive-Uploader-TG-Bot_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 2 potential vulnerabilities
    🧹 Cleaned up cdfxscrq/GDrive-Uploader-TG-Bot
    ✅ cdfxscrq/GDrive-Uploader-TG-Bot - Final Score: 27.0 (Risk: 22, Semgrep: 10)

🎯 Processing: codingjoe/django-s3file  ✅ Completed: cdfxscrq/GDrive-Uploader-TG-Bot (Score: 27.0)

    🔗 Clone URL: https://github.com/codingjoe/django-s3file.git
    📥 Cloning codingjoe/django-s3file...
    ✅ Successfully cloned codingjoe/django-s3file
    🔍 Running Semgrep on codingjoe/django-s3file...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/codingjoe_django-s3file_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 6 potential vulnerabilities
    🧹 Cleaned up codingjoe/django-s3file
    ✅ codingjoe/django-s3file - Final Score: 36.0 (Risk: 21, Semgrep: 30)

🎯 Processing: MrBlinky/Arduboy-Python-Utilities  ✅ Completed: codingjoe/django-s3file (Score: 36.0)
    🔗 Clone URL: https://github.com/MrBlinky/Arduboy-Python-Utilities.git
    📥 Cloning MrBlinky/Arduboy-Python-Utilities...

    ✅ Successfully cloned MrBlinky/Arduboy-Python-Utilities
    🔍 Running Semgrep on MrBlinky/Arduboy-Python-Utilities...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/MrBlinky_Arduboy-Python-Utilities_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up MrBlinky/Arduboy-Python-Utilities
    ✅ MrBlinky/Arduboy-Python-Utilities - Final Score: 17.0 (Risk: 17, Semgrep: 0)

🎯 Processing: RaSan147/pyrobox
    🔗 Clone URL: https://github.com/RaSan147/pyrobox.git
    📥 Cloning RaSan147/pyrobox...
  ✅ Completed: MrBlinky/Arduboy-Python-Utilities (Score: 17.0)
    ✅ Successfully cloned RaSan147/pyrobox
    🔍 Running Semgrep on RaSan147/pyrobox...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/RaSan147_pyrobox_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 42 potential vulnerabilities
    🧹 Cleaned up RaSan147/pyrobox
    ✅ RaSan147/pyrobox - Final Score: 72.0 (Risk: 22, Semgrep: 100)

🎯 Processing: feincms/feincms  ✅ Completed: RaSan147/pyrobox (Score: 72.0)
    🔗 Clone URL: https://github.com/feincms/feincms.git

    📥 Cloning feincms/feincms...
    ✅ Successfully cloned feincms/feincms
    🔍 Running Semgrep on feincms/feincms...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/feincms_feincms_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 33 potential vulnerabilities
    🧹 Cleaned up feincms/feincms
    ✅ feincms/feincms - Final Score: 74.0 (Risk: 24, Semgrep: 100)

🎯 Processing: TaleLin/lin-cms-flask  ✅ Completed: feincms/feincms (Score: 74.0)
    🔗 Clone URL: https://github.com/TaleLin/lin-cms-flask.git

    📥 Cloning TaleLin/lin-cms-flask...
    ✅ Successfully cloned TaleLin/lin-cms-flask
    🔍 Running Semgrep on TaleLin/lin-cms-flask...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/TaleLin_lin-cms-flask_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 11 potential vulnerabilities
    🧹 Cleaned up TaleLin/lin-cms-flask
    ✅ TaleLin/lin-cms-flask - Final Score: 59.0 (Risk: 29, Semgrep: 60)

🎯 Processing: nephila/djangocms-blog  ✅ Completed: TaleLin/lin-cms-flask (Score: 59.0)

    🔗 Clone URL: https://github.com/nephila/djangocms-blog.git
    📥 Cloning nephila/djangocms-blog...
    ✅ Successfully cloned nephila/djangocms-blog
    🔍 Running Semgrep on nephila/djangocms-blog...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/nephila_djangocms-blog_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 11 potential vulnerabilities
    🧹 Cleaned up nephila/djangocms-blog
    ✅ nephila/djangocms-blog - Final Score: 53.5 (Risk: 29, Semgrep: 49)

🎯 Processing: vsys-host/shkeeper.io
    🔗 Clone URL: https://github.com/vsys-host/shkeeper.io.git
    📥 Cloning vsys-host/shkeeper.io...
  ✅ Completed: nephila/djangocms-blog (Score: 53.5)
    ✅ Successfully cloned vsys-host/shkeeper.io
    🔍 Running Semgrep on vsys-host/shkeeper.io...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/vsys-host_shkeeper.io_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 76 potential vulnerabilities
    🧹 Cleaned up vsys-host/shkeeper.io
    ✅ vsys-host/shkeeper.io - Final Score: 79.0 (Risk: 29, Semgrep: 100)

🎯 Processing: openfun/richie
    🔗 Clone URL: https://github.com/openfun/richie.git
    📥 Cloning openfun/richie...
  ✅ Completed: vsys-host/shkeeper.io (Score: 79.0)
    ✅ Successfully cloned openfun/richie
    🔍 Running Semgrep on openfun/richie...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/openfun_richie_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 73 potential vulnerabilities
    🧹 Cleaned up openfun/richie
    ✅ openfun/richie - Final Score: 84.0 (Risk: 34, Semgrep: 100)

🎯 Processing: batiste/django-page-cms  ✅ Completed: openfun/richie (Score: 84.0)

    🔗 Clone URL: https://github.com/batiste/django-page-cms.git
    📥 Cloning batiste/django-page-cms...
    ✅ Successfully cloned batiste/django-page-cms
    🔍 Running Semgrep on batiste/django-page-cms...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/batiste_django-page-cms_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 47 potential vulnerabilities
    🧹 Cleaned up batiste/django-page-cms
    ✅ batiste/django-page-cms - Final Score: 74.0 (Risk: 24, Semgrep: 100)

🎯 Processing: mavenium/PyEditorial  ✅ Completed: batiste/django-page-cms (Score: 74.0)
    🔗 Clone URL: https://github.com/mavenium/PyEditorial.git
    📥 Cloning mavenium/PyEditorial...

    ✅ Successfully cloned mavenium/PyEditorial
    🔍 Running Semgrep on mavenium/PyEditorial...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/mavenium_PyEditorial_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 3 potential vulnerabilities
    🧹 Cleaned up mavenium/PyEditorial
    ✅ mavenium/PyEditorial - Final Score: 36.5 (Risk: 29, Semgrep: 15)

🎯 Processing: infoportugal/wagtail-modeltranslation
    🔗 Clone URL: https://github.com/infoportugal/wagtail-modeltranslation.git  ✅ Completed: mavenium/PyEditorial (Score: 36.5)

    📥 Cloning infoportugal/wagtail-modeltranslation...
    ✅ Successfully cloned infoportugal/wagtail-modeltranslation
    🔍 Running Semgrep on infoportugal/wagtail-modeltranslation...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/infoportugal_wagtail-modeltranslation_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 4 potential vulnerabilities
    🧹 Cleaned up infoportugal/wagtail-modeltranslation
    ✅ infoportugal/wagtail-modeltranslation - Final Score: 34.5 (Risk: 24, Semgrep: 21)

🎯 Processing: raagin/django-streamfield  ✅ Completed: infoportugal/wagtail-modeltranslation (Score: 34.5)

    🔗 Clone URL: https://github.com/raagin/django-streamfield.git
    📥 Cloning raagin/django-streamfield...
    ✅ Successfully cloned raagin/django-streamfield
    🔍 Running Semgrep on raagin/django-streamfield...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/raagin_django-streamfield_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 10 potential vulnerabilities
    🧹 Cleaned up raagin/django-streamfield
    ✅ raagin/django-streamfield - Final Score: 55.0 (Risk: 32, Semgrep: 46)

🎯 Processing: django-cms/django-cms-quickstart
    🔗 Clone URL: https://github.com/django-cms/django-cms-quickstart.git
    📥 Cloning django-cms/django-cms-quickstart...
  ✅ Completed: raagin/django-streamfield (Score: 55.0)
    ✅ Successfully cloned django-cms/django-cms-quickstart
    🔍 Running Semgrep on django-cms/django-cms-quickstart...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/django-cms_django-cms-quickstart_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up django-cms/django-cms-quickstart
    ✅ django-cms/django-cms-quickstart - Final Score: 37.0 (Risk: 32, Semgrep: 10)

🎯 Processing: openstax/openstax-cms
    🔗 Clone URL: https://github.com/openstax/openstax-cms.git
    📥 Cloning openstax/openstax-cms...
  ✅ Completed: django-cms/django-cms-quickstart (Score: 37.0)
    ✅ Successfully cloned openstax/openstax-cms
    🔍 Running Semgrep on openstax/openstax-cms...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/openstax_openstax-cms_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 27 potential vulnerabilities
    🧹 Cleaned up openstax/openstax-cms
    ✅ openstax/openstax-cms - Final Score: 74.0 (Risk: 24, Semgrep: 100)

🎯 Processing: fecgov/fec-cms  ✅ Completed: openstax/openstax-cms (Score: 74.0)
    🔗 Clone URL: https://github.com/fecgov/fec-cms.git
    📥 Cloning fecgov/fec-cms...

    ✅ Successfully cloned fecgov/fec-cms
    🔍 Running Semgrep on fecgov/fec-cms...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/fecgov_fec-cms_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 119 potential vulnerabilities
    🧹 Cleaned up fecgov/fec-cms
    ✅ fecgov/fec-cms - Final Score: 84.0 (Risk: 34, Semgrep: 100)

🎯 Processing: feincms/feincms3
    🔗 Clone URL: https://github.com/feincms/feincms3.git
    📥 Cloning feincms/feincms3...
  ✅ Completed: fecgov/fec-cms (Score: 84.0)
    ✅ Successfully cloned feincms/feincms3
    🔍 Running Semgrep on feincms/feincms3...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/feincms_feincms3_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 4 potential vulnerabilities
    🧹 Cleaned up feincms/feincms3
    ✅ feincms/feincms3 - Final Score: 24.0 (Risk: 14, Semgrep: 20)

🎯 Processing: springload/madewithwagtail
    🔗 Clone URL: https://github.com/springload/madewithwagtail.git
    📥 Cloning springload/madewithwagtail...
  ✅ Completed: feincms/feincms3 (Score: 24.0)
    ✅ Successfully cloned springload/madewithwagtail
    🔍 Running Semgrep on springload/madewithwagtail...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/springload_madewithwagtail_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 13 potential vulnerabilities
    🧹 Cleaned up springload/madewithwagtail
    ✅ springload/madewithwagtail - Final Score: 59.5 (Risk: 14, Semgrep: 91)

🎯 Processing: neon-jungle/wagtail-videos  ✅ Completed: springload/madewithwagtail (Score: 59.5)
    🔗 Clone URL: https://github.com/neon-jungle/wagtail-videos.git

    📥 Cloning neon-jungle/wagtail-videos...
    ✅ Successfully cloned neon-jungle/wagtail-videos
    🔍 Running Semgrep on neon-jungle/wagtail-videos...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/neon-jungle_wagtail-videos_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 15 potential vulnerabilities
    🧹 Cleaned up neon-jungle/wagtail-videos
    ✅ neon-jungle/wagtail-videos - Final Score: 31.5 (Risk: 14, Semgrep: 35)

🎯 Processing: mangadventure/MangAdventure
    🔗 Clone URL: https://github.com/mangadventure/MangAdventure.git
    📥 Cloning mangadventure/MangAdventure...
  ✅ Completed: neon-jungle/wagtail-videos (Score: 31.5)
    ✅ Successfully cloned mangadventure/MangAdventure
    🔍 Running Semgrep on mangadventure/MangAdventure...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/mangadventure_MangAdventure_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 24 potential vulnerabilities
    🧹 Cleaned up mangadventure/MangAdventure
    ✅ mangadventure/MangAdventure - Final Score: 69.0 (Risk: 19, Semgrep: 100)

🎯 Processing: nephila/djangocms-page-meta
    🔗 Clone URL: https://github.com/nephila/djangocms-page-meta.git
    📥 Cloning nephila/djangocms-page-meta...
  ✅ Completed: mangadventure/MangAdventure (Score: 69.0)
    ✅ Successfully cloned nephila/djangocms-page-meta
    🔍 Running Semgrep on nephila/djangocms-page-meta...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/nephila_djangocms-page-meta_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 2 potential vulnerabilities
    🧹 Cleaned up nephila/djangocms-page-meta
    ✅ nephila/djangocms-page-meta - Final Score: 19.0 (Risk: 14, Semgrep: 10)

🎯 Processing: ckan/ckanext-pages
    🔗 Clone URL: https://github.com/ckan/ckanext-pages.git
    📥 Cloning ckan/ckanext-pages...
  ✅ Completed: nephila/djangocms-page-meta (Score: 19.0)
    ✅ Successfully cloned ckan/ckanext-pages
    🔍 Running Semgrep on ckan/ckanext-pages...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/ckan_ckanext-pages_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up ckan/ckanext-pages
    ✅ ckan/ckanext-pages - Final Score: 16.5 (Risk: 14, Semgrep: 5)

🎯 Processing: jazzband/django-two-factor-auth  ✅ Completed: ckan/ckanext-pages (Score: 16.5)
    🔗 Clone URL: https://github.com/jazzband/django-two-factor-auth.git
    📥 Cloning jazzband/django-two-factor-auth...

    ✅ Successfully cloned jazzband/django-two-factor-auth
    🔍 Running Semgrep on jazzband/django-two-factor-auth...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/jazzband_django-two-factor-auth_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 52 potential vulnerabilities
    🧹 Cleaned up jazzband/django-two-factor-auth
    ✅ jazzband/django-two-factor-auth - Final Score: 71.0 (Risk: 23, Semgrep: 96)

🎯 Processing: privacyidea/privacyidea
    🔗 Clone URL: https://github.com/privacyidea/privacyidea.git
    📥 Cloning privacyidea/privacyidea...  ✅ Completed: jazzband/django-two-factor-auth (Score: 71.0)

    ✅ Successfully cloned privacyidea/privacyidea
    🔍 Running Semgrep on privacyidea/privacyidea...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/privacyidea_privacyidea_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 57 potential vulnerabilities
    🧹 Cleaned up privacyidea/privacyidea
    ✅ privacyidea/privacyidea - Final Score: 73.0 (Risk: 23, Semgrep: 100)

🎯 Processing: scito/extract_otp_secrets  ✅ Completed: privacyidea/privacyidea (Score: 73.0)

    🔗 Clone URL: https://github.com/scito/extract_otp_secrets.git
    📥 Cloning scito/extract_otp_secrets...
    ✅ Successfully cloned scito/extract_otp_secrets
    🔍 Running Semgrep on scito/extract_otp_secrets...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/scito_extract_otp_secrets_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up scito/extract_otp_secrets
    ✅ scito/extract_otp_secrets - Final Score: 40.0 (Risk: 35, Semgrep: 10)

🎯 Processing: simonrob/email-oauth2-proxy
    🔗 Clone URL: https://github.com/simonrob/email-oauth2-proxy.git
    📥 Cloning simonrob/email-oauth2-proxy...
  ✅ Completed: scito/extract_otp_secrets (Score: 40.0)
    ✅ Successfully cloned simonrob/email-oauth2-proxy
    🔍 Running Semgrep on simonrob/email-oauth2-proxy...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/simonrob_email-oauth2-proxy_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 15 potential vulnerabilities
    🧹 Cleaned up simonrob/email-oauth2-proxy
    ✅ simonrob/email-oauth2-proxy - Final Score: 74.5 (Risk: 32, Semgrep: 85)

🎯 Processing: dirkjanm/adidnsdump
  ✅ Completed: simonrob/email-oauth2-proxy (Score: 74.5)    🔗 Clone URL: https://github.com/dirkjanm/adidnsdump.git
    📥 Cloning dirkjanm/adidnsdump...

    ✅ Successfully cloned dirkjanm/adidnsdump
    🔍 Running Semgrep on dirkjanm/adidnsdump...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/dirkjanm_adidnsdump_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up dirkjanm/adidnsdump
    ✅ dirkjanm/adidnsdump - Final Score: 24.0 (Risk: 24, Semgrep: 0)

🎯 Processing: genotrance/px
    🔗 Clone URL: https://github.com/genotrance/px.git
    📥 Cloning genotrance/px...  ✅ Completed: dirkjanm/adidnsdump (Score: 24.0)

    ✅ Successfully cloned genotrance/px
    🔍 Running Semgrep on genotrance/px...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/genotrance_px_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 15 potential vulnerabilities
    🧹 Cleaned up genotrance/px
    ✅ genotrance/px - Final Score: 66.5 (Risk: 19, Semgrep: 95)

🎯 Processing: aaugustin/django-sesame  ✅ Completed: genotrance/px (Score: 66.5)

    🔗 Clone URL: https://github.com/aaugustin/django-sesame.git
    📥 Cloning aaugustin/django-sesame...
    ✅ Successfully cloned aaugustin/django-sesame
    🔍 Running Semgrep on aaugustin/django-sesame...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/aaugustin_django-sesame_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 8 potential vulnerabilities
    🧹 Cleaned up aaugustin/django-sesame
    ✅ aaugustin/django-sesame - Final Score: 49.0 (Risk: 29, Semgrep: 40)

🎯 Processing: yezz123/authx  ✅ Completed: aaugustin/django-sesame (Score: 49.0)

    🔗 Clone URL: https://github.com/yezz123/authx.git
    📥 Cloning yezz123/authx...
    ✅ Successfully cloned yezz123/authx
    🔍 Running Semgrep on yezz123/authx...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/yezz123_authx_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up yezz123/authx
    ✅ yezz123/authx - Final Score: 40.0 (Risk: 35, Semgrep: 10)

🎯 Processing: cisagov/untitledgoosetool  ✅ Completed: yezz123/authx (Score: 40.0)

    🔗 Clone URL: https://github.com/cisagov/untitledgoosetool.git
    📥 Cloning cisagov/untitledgoosetool...
    ✅ Successfully cloned cisagov/untitledgoosetool
    🔍 Running Semgrep on cisagov/untitledgoosetool...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/cisagov_untitledgoosetool_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 5 potential vulnerabilities
    🧹 Cleaned up cisagov/untitledgoosetool
    ✅ cisagov/untitledgoosetool - Final Score: 48.0 (Risk: 28, Semgrep: 40)

🎯 Processing: n8henrie/pycookiecheat  ✅ Completed: cisagov/untitledgoosetool (Score: 48.0)

    🔗 Clone URL: https://github.com/n8henrie/pycookiecheat.git
    📥 Cloning n8henrie/pycookiecheat...
    ✅ Successfully cloned n8henrie/pycookiecheat
    🔍 Running Semgrep on n8henrie/pycookiecheat...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/n8henrie_pycookiecheat_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up n8henrie/pycookiecheat
    ✅ n8henrie/pycookiecheat - Final Score: 21.5 (Risk: 19, Semgrep: 5)

🎯 Processing: Voyz/ibeam  ✅ Completed: n8henrie/pycookiecheat (Score: 21.5)

    🔗 Clone URL: https://github.com/Voyz/ibeam.git
    📥 Cloning Voyz/ibeam...
    ✅ Successfully cloned Voyz/ibeam
    🔍 Running Semgrep on Voyz/ibeam...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/Voyz_ibeam_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 4 potential vulnerabilities
    🧹 Cleaned up Voyz/ibeam
    ✅ Voyz/ibeam - Final Score: 41.5 (Risk: 31, Semgrep: 21)

🎯 Processing: fief-dev/fief
    🔗 Clone URL: https://github.com/fief-dev/fief.git
    📥 Cloning fief-dev/fief...
  ✅ Completed: Voyz/ibeam (Score: 41.5)
    ✅ Successfully cloned fief-dev/fief
    🔍 Running Semgrep on fief-dev/fief...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/fief-dev_fief_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 21 potential vulnerabilities
    🧹 Cleaned up fief-dev/fief
    ✅ fief-dev/fief - Final Score: 78.0 (Risk: 28, Semgrep: 100)

🎯 Processing: LinOTP/LinOTP
    🔗 Clone URL: https://github.com/LinOTP/LinOTP.git
  ✅ Completed: fief-dev/fief (Score: 78.0)    📥 Cloning LinOTP/LinOTP...

    ✅ Successfully cloned LinOTP/LinOTP
    🔍 Running Semgrep on LinOTP/LinOTP...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/LinOTP_LinOTP_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 131 potential vulnerabilities
    🧹 Cleaned up LinOTP/LinOTP
    ✅ LinOTP/LinOTP - Final Score: 83.0 (Risk: 33, Semgrep: 100)

🎯 Processing: christiaangoossens/hass-oidc-auth
    🔗 Clone URL: https://github.com/christiaangoossens/hass-oidc-auth.git
  ✅ Completed: LinOTP/LinOTP (Score: 83.0)
    📥 Cloning christiaangoossens/hass-oidc-auth...
    ✅ Successfully cloned christiaangoossens/hass-oidc-auth
    🔍 Running Semgrep on christiaangoossens/hass-oidc-auth...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/christiaangoossens_hass-oidc-auth_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 8 potential vulnerabilities
    🧹 Cleaned up christiaangoossens/hass-oidc-auth
    ✅ christiaangoossens/hass-oidc-auth - Final Score: 50.5 (Risk: 28, Semgrep: 45)

🎯 Processing: jupyterhub/oauthenticator  ✅ Completed: christiaangoossens/hass-oidc-auth (Score: 50.5)

    🔗 Clone URL: https://github.com/jupyterhub/oauthenticator.git
    📥 Cloning jupyterhub/oauthenticator...
    ✅ Successfully cloned jupyterhub/oauthenticator
    🔍 Running Semgrep on jupyterhub/oauthenticator...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/jupyterhub_oauthenticator_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 3 potential vulnerabilities
    🧹 Cleaned up jupyterhub/oauthenticator
    ✅ jupyterhub/oauthenticator - Final Score: 38.0 (Risk: 28, Semgrep: 20)

🎯 Processing: etianen/django-python3-ldap  ✅ Completed: jupyterhub/oauthenticator (Score: 38.0)

    🔗 Clone URL: https://github.com/etianen/django-python3-ldap.git
    📥 Cloning etianen/django-python3-ldap...
    ✅ Successfully cloned etianen/django-python3-ldap
    🔍 Running Semgrep on etianen/django-python3-ldap...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/etianen_django-python3-ldap_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up etianen/django-python3-ldap
    ✅ etianen/django-python3-ldap - Final Score: 27.0 (Risk: 27, Semgrep: 0)

🎯 Processing: Corvia/django-tenant-users
    🔗 Clone URL: https://github.com/Corvia/django-tenant-users.git
    📥 Cloning Corvia/django-tenant-users...
  ✅ Completed: etianen/django-python3-ldap (Score: 27.0)
    ✅ Successfully cloned Corvia/django-tenant-users
    🔍 Running Semgrep on Corvia/django-tenant-users...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/Corvia_django-tenant-users_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 2 potential vulnerabilities
    🧹 Cleaned up Corvia/django-tenant-users
    ✅ Corvia/django-tenant-users - Final Score: 28.0 (Risk: 23, Semgrep: 10)

🎯 Processing: openwisp/openwisp-radius
    🔗 Clone URL: https://github.com/openwisp/openwisp-radius.git
    📥 Cloning openwisp/openwisp-radius...  ✅ Completed: Corvia/django-tenant-users (Score: 28.0)

    ✅ Successfully cloned openwisp/openwisp-radius
    🔍 Running Semgrep on openwisp/openwisp-radius...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/openwisp_openwisp-radius_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 22 potential vulnerabilities
    🧹 Cleaned up openwisp/openwisp-radius
    ✅ openwisp/openwisp-radius - Final Score: 76.0 (Risk: 41, Semgrep: 70)

🎯 Processing: django-auth-ldap/django-auth-ldap
    🔗 Clone URL: https://github.com/django-auth-ldap/django-auth-ldap.git  ✅ Completed: openwisp/openwisp-radius (Score: 76.0)

    📥 Cloning django-auth-ldap/django-auth-ldap...
    ✅ Successfully cloned django-auth-ldap/django-auth-ldap
    🔍 Running Semgrep on django-auth-ldap/django-auth-ldap...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/django-auth-ldap_django-auth-ldap_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up django-auth-ldap/django-auth-ldap
    ✅ django-auth-ldap/django-auth-ldap - Final Score: 39.5 (Risk: 37, Semgrep: 5)

🎯 Processing: benavlabs/crudadmin
    🔗 Clone URL: https://github.com/benavlabs/crudadmin.git
    📥 Cloning benavlabs/crudadmin...  ✅ Completed: django-auth-ldap/django-auth-ldap (Score: 39.5)

    ✅ Successfully cloned benavlabs/crudadmin
    🔍 Running Semgrep on benavlabs/crudadmin...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/benavlabs_crudadmin_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 2 potential vulnerabilities
    🧹 Cleaned up benavlabs/crudadmin
    ✅ benavlabs/crudadmin - Final Score: 44.5 (Risk: 37, Semgrep: 15)

🎯 Processing: BeryJu/hass-auth-header  ✅ Completed: benavlabs/crudadmin (Score: 44.5)

    🔗 Clone URL: https://github.com/BeryJu/hass-auth-header.git
    📥 Cloning BeryJu/hass-auth-header...
    ✅ Successfully cloned BeryJu/hass-auth-header
    🔍 Running Semgrep on BeryJu/hass-auth-header...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/BeryJu_hass-auth-header_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 3 potential vulnerabilities
    🧹 Cleaned up BeryJu/hass-auth-header
    ✅ BeryJu/hass-auth-header - Final Score: 35.5 (Risk: 28, Semgrep: 15)

🎯 Processing: snok/django-auth-adfs
    🔗 Clone URL: https://github.com/snok/django-auth-adfs.git
    📥 Cloning snok/django-auth-adfs...
  ✅ Completed: BeryJu/hass-auth-header (Score: 35.5)
    ✅ Successfully cloned snok/django-auth-adfs
    🔍 Running Semgrep on snok/django-auth-adfs...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/snok_django-auth-adfs_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 8 potential vulnerabilities
    🧹 Cleaned up snok/django-auth-adfs
    ✅ snok/django-auth-adfs - Final Score: 43.0 (Risk: 23, Semgrep: 40)

🎯 Processing: venth/aws-adfs
    🔗 Clone URL: https://github.com/venth/aws-adfs.git
    📥 Cloning venth/aws-adfs...
  ✅ Completed: snok/django-auth-adfs (Score: 43.0)
    ✅ Successfully cloned venth/aws-adfs
    🔍 Running Semgrep on venth/aws-adfs...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/venth_aws-adfs_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up venth/aws-adfs
    ✅ venth/aws-adfs - Final Score: 28.0 (Risk: 23, Semgrep: 10)

🎯 Processing: wagnerdelima/drf-social-oauth2  ✅ Completed: venth/aws-adfs (Score: 28.0)

    🔗 Clone URL: https://github.com/wagnerdelima/drf-social-oauth2.git
    📥 Cloning wagnerdelima/drf-social-oauth2...
    ✅ Successfully cloned wagnerdelima/drf-social-oauth2
    🔍 Running Semgrep on wagnerdelima/drf-social-oauth2...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/wagnerdelima_drf-social-oauth2_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 2 potential vulnerabilities
    🧹 Cleaned up wagnerdelima/drf-social-oauth2
    ✅ wagnerdelima/drf-social-oauth2 - Final Score: 42.0 (Risk: 32, Semgrep: 20)

🎯 Processing: sud0Ru/NauthNRPC  ✅ Completed: wagnerdelima/drf-social-oauth2 (Score: 42.0)

    🔗 Clone URL: https://github.com/sud0Ru/NauthNRPC.git
    📥 Cloning sud0Ru/NauthNRPC...
    ✅ Successfully cloned sud0Ru/NauthNRPC
    🔍 Running Semgrep on sud0Ru/NauthNRPC...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/sud0Ru_NauthNRPC_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up sud0Ru/NauthNRPC
    ✅ sud0Ru/NauthNRPC - Final Score: 23.0 (Risk: 23, Semgrep: 0)

🎯 Processing: mkalioby/django-passkeys
    🔗 Clone URL: https://github.com/mkalioby/django-passkeys.git  ✅ Completed: sud0Ru/NauthNRPC (Score: 23.0)

    📥 Cloning mkalioby/django-passkeys...
    ✅ Successfully cloned mkalioby/django-passkeys
    🔍 Running Semgrep on mkalioby/django-passkeys...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/mkalioby_django-passkeys_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 3 potential vulnerabilities
    🧹 Cleaned up mkalioby/django-passkeys
    ✅ mkalioby/django-passkeys - Final Score: 35.5 (Risk: 28, Semgrep: 15)

🎯 Processing: grafana/django-saml2-auth
    🔗 Clone URL: https://github.com/grafana/django-saml2-auth.git
    📥 Cloning grafana/django-saml2-auth...  ✅ Completed: mkalioby/django-passkeys (Score: 35.5)

    ✅ Successfully cloned grafana/django-saml2-auth
    🔍 Running Semgrep on grafana/django-saml2-auth...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/grafana_django-saml2-auth_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 3 potential vulnerabilities
    🧹 Cleaned up grafana/django-saml2-auth
    ✅ grafana/django-saml2-auth - Final Score: 35.5 (Risk: 28, Semgrep: 15)

🎯 Processing: 3lp4tr0n/RemoteMonologue
  ✅ Completed: grafana/django-saml2-auth (Score: 35.5)    🔗 Clone URL: https://github.com/3lp4tr0n/RemoteMonologue.git
    📥 Cloning 3lp4tr0n/RemoteMonologue...

    ✅ Successfully cloned 3lp4tr0n/RemoteMonologue
    🔍 Running Semgrep on 3lp4tr0n/RemoteMonologue...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/3lp4tr0n_RemoteMonologue_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up 3lp4tr0n/RemoteMonologue
    ✅ 3lp4tr0n/RemoteMonologue - Final Score: 25.5 (Risk: 23, Semgrep: 5)

🎯 Processing: jambonrose/django-improved-user  ✅ Completed: 3lp4tr0n/RemoteMonologue (Score: 25.5)

    🔗 Clone URL: https://github.com/jambonrose/django-improved-user.git
    📥 Cloning jambonrose/django-improved-user...
    ✅ Successfully cloned jambonrose/django-improved-user
    🔍 Running Semgrep on jambonrose/django-improved-user...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/jambonrose_django-improved-user_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 6 potential vulnerabilities
    🧹 Cleaned up jambonrose/django-improved-user
    ✅ jambonrose/django-improved-user - Final Score: 38.0 (Risk: 23, Semgrep: 30)

🎯 Processing: MachineKillin/MSMC
    🔗 Clone URL: https://github.com/MachineKillin/MSMC.git
    📥 Cloning MachineKillin/MSMC...
  ✅ Completed: jambonrose/django-improved-user (Score: 38.0)
    ✅ Successfully cloned MachineKillin/MSMC
    🔍 Running Semgrep on MachineKillin/MSMC...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/MachineKillin_MSMC_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 6 potential vulnerabilities
    🧹 Cleaned up MachineKillin/MSMC
    ✅ MachineKillin/MSMC - Final Score: 49.5 (Risk: 24, Semgrep: 51)

🎯 Processing: piccolo-orm/piccolo_api  ✅ Completed: MachineKillin/MSMC (Score: 49.5)

    🔗 Clone URL: https://github.com/piccolo-orm/piccolo_api.git
    📥 Cloning piccolo-orm/piccolo_api...
    ✅ Successfully cloned piccolo-orm/piccolo_api
    🔍 Running Semgrep on piccolo-orm/piccolo_api...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/piccolo-orm_piccolo_api_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 24 potential vulnerabilities
    🧹 Cleaned up piccolo-orm/piccolo_api
    ✅ piccolo-orm/piccolo_api - Final Score: 78.0 (Risk: 28, Semgrep: 100)

🎯 Processing: SiddhantSadangi/st_login_form  ✅ Completed: piccolo-orm/piccolo_api (Score: 78.0)

    🔗 Clone URL: https://github.com/SiddhantSadangi/st_login_form.git
    📥 Cloning SiddhantSadangi/st_login_form...
    ✅ Successfully cloned SiddhantSadangi/st_login_form
    🔍 Running Semgrep on SiddhantSadangi/st_login_form...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/SiddhantSadangi_st_login_form_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up SiddhantSadangi/st_login_form
    ✅ SiddhantSadangi/st_login_form - Final Score: 30.5 (Risk: 28, Semgrep: 5)

🎯 Processing: nitmir/django-cas-server  ✅ Completed: SiddhantSadangi/st_login_form (Score: 30.5)

    🔗 Clone URL: https://github.com/nitmir/django-cas-server.git
    📥 Cloning nitmir/django-cas-server...
    ✅ Successfully cloned nitmir/django-cas-server
    🔍 Running Semgrep on nitmir/django-cas-server...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/nitmir_django-cas-server_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 17 potential vulnerabilities
    🧹 Cleaned up nitmir/django-cas-server
    ✅ nitmir/django-cas-server - Final Score: 57.5 (Risk: 23, Semgrep: 69)

🎯 Processing: temp43487580/EntraPassTheCert  ✅ Completed: nitmir/django-cas-server (Score: 57.5)

    🔗 Clone URL: https://github.com/temp43487580/EntraPassTheCert.git
    📥 Cloning temp43487580/EntraPassTheCert...
    ✅ Successfully cloned temp43487580/EntraPassTheCert
    🔍 Running Semgrep on temp43487580/EntraPassTheCert...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/temp43487580_EntraPassTheCert_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 11 potential vulnerabilities
    🧹 Cleaned up temp43487580/EntraPassTheCert
    ✅ temp43487580/EntraPassTheCert - Final Score: 49.0 (Risk: 19, Semgrep: 60)

🎯 Processing: OleFredrik1/remoteKrbRelayx
    🔗 Clone URL: https://github.com/OleFredrik1/remoteKrbRelayx.git
  ✅ Completed: temp43487580/EntraPassTheCert (Score: 49.0)
    📥 Cloning OleFredrik1/remoteKrbRelayx...
    ✅ Successfully cloned OleFredrik1/remoteKrbRelayx
    🔍 Running Semgrep on OleFredrik1/remoteKrbRelayx...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/OleFredrik1_remoteKrbRelayx_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 2 potential vulnerabilities
    🧹 Cleaned up OleFredrik1/remoteKrbRelayx
    ✅ OleFredrik1/remoteKrbRelayx - Final Score: 28.0 (Risk: 23, Semgrep: 10)

🎯 Processing: dan246/VisionFlow
    🔗 Clone URL: https://github.com/dan246/VisionFlow.git
  ✅ Completed: OleFredrik1/remoteKrbRelayx (Score: 28.0)    📥 Cloning dan246/VisionFlow...

    ✅ Successfully cloned dan246/VisionFlow
    🔍 Running Semgrep on dan246/VisionFlow...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/dan246_VisionFlow_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 42 potential vulnerabilities
    🧹 Cleaned up dan246/VisionFlow
    ✅ dan246/VisionFlow - Final Score: 69.0 (Risk: 19, Semgrep: 100)

🎯 Processing: app-generator/react-flask-authentication  ✅ Completed: dan246/VisionFlow (Score: 69.0)

    🔗 Clone URL: https://github.com/app-generator/react-flask-authentication.git
    📥 Cloning app-generator/react-flask-authentication...
    ✅ Successfully cloned app-generator/react-flask-authentication
    🔍 Running Semgrep on app-generator/react-flask-authentication...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/app-generator_react-flask-authentication_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 6 potential vulnerabilities
    🧹 Cleaned up app-generator/react-flask-authentication
    ✅ app-generator/react-flask-authentication - Final Score: 38.0 (Risk: 18, Semgrep: 40)

🎯 Processing: labd/wagtail-2fa
    🔗 Clone URL: https://github.com/labd/wagtail-2fa.git
    📥 Cloning labd/wagtail-2fa...  ✅ Completed: app-generator/react-flask-authentication (Score: 38.0)

    ✅ Successfully cloned labd/wagtail-2fa
    🔍 Running Semgrep on labd/wagtail-2fa...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/labd_wagtail-2fa_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 10 potential vulnerabilities
    🧹 Cleaned up labd/wagtail-2fa
    ✅ labd/wagtail-2fa - Final Score: 28.0 (Risk: 13, Semgrep: 30)

🎯 Processing: waza-ari/fastapi-keycloak-middleware  ✅ Completed: labd/wagtail-2fa (Score: 28.0)

    🔗 Clone URL: https://github.com/waza-ari/fastapi-keycloak-middleware.git
    📥 Cloning waza-ari/fastapi-keycloak-middleware...
    ✅ Successfully cloned waza-ari/fastapi-keycloak-middleware
    🔍 Running Semgrep on waza-ari/fastapi-keycloak-middleware...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/waza-ari_fastapi-keycloak-middleware_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up waza-ari/fastapi-keycloak-middleware
    ✅ waza-ari/fastapi-keycloak-middleware - Final Score: 21.0 (Risk: 21, Semgrep: 0)

🎯 Processing: IS2AI/SpeakingFaces
  ✅ Completed: waza-ari/fastapi-keycloak-middleware (Score: 21.0)
    🔗 Clone URL: https://github.com/IS2AI/SpeakingFaces.git
    📥 Cloning IS2AI/SpeakingFaces...
    ✅ Successfully cloned IS2AI/SpeakingFaces
    🔍 Running Semgrep on IS2AI/SpeakingFaces...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/IS2AI_SpeakingFaces_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 4 potential vulnerabilities
    🧹 Cleaned up IS2AI/SpeakingFaces
    ✅ IS2AI/SpeakingFaces - Final Score: 34.5 (Risk: 22, Semgrep: 25)

🎯 Processing: Abdullah4345/BioCrypt  ✅ Completed: IS2AI/SpeakingFaces (Score: 34.5)

    🔗 Clone URL: https://github.com/Abdullah4345/BioCrypt.git
    📥 Cloning Abdullah4345/BioCrypt...
    ✅ Successfully cloned Abdullah4345/BioCrypt
    🔍 Running Semgrep on Abdullah4345/BioCrypt...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/Abdullah4345_BioCrypt_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up Abdullah4345/BioCrypt
    ✅ Abdullah4345/BioCrypt - Final Score: 18.0 (Risk: 13, Semgrep: 10)

🎯 Processing: HynekPetrak/sshame  ✅ Completed: Abdullah4345/BioCrypt (Score: 18.0)

    🔗 Clone URL: https://github.com/HynekPetrak/sshame.git
    📥 Cloning HynekPetrak/sshame...
    ✅ Successfully cloned HynekPetrak/sshame
    🔍 Running Semgrep on HynekPetrak/sshame...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/HynekPetrak_sshame_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up HynekPetrak/sshame
    ✅ HynekPetrak/sshame - Final Score: 18.0 (Risk: 18, Semgrep: 0)

🎯 Processing: corbado/passkeys-python-django
    🔗 Clone URL: https://github.com/corbado/passkeys-python-django.git
    📥 Cloning corbado/passkeys-python-django...  ✅ Completed: HynekPetrak/sshame (Score: 18.0)

    ✅ Successfully cloned corbado/passkeys-python-django
    🔍 Running Semgrep on corbado/passkeys-python-django...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/corbado_passkeys-python-django_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 3 potential vulnerabilities
    🧹 Cleaned up corbado/passkeys-python-django
    ✅ corbado/passkeys-python-django - Final Score: 20.5 (Risk: 13, Semgrep: 15)

🎯 Processing: jupyterhub/nativeauthenticator
    🔗 Clone URL: https://github.com/jupyterhub/nativeauthenticator.git
    📥 Cloning jupyterhub/nativeauthenticator...
  ✅ Completed: corbado/passkeys-python-django (Score: 20.5)
    ✅ Successfully cloned jupyterhub/nativeauthenticator
    🔍 Running Semgrep on jupyterhub/nativeauthenticator...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/jupyterhub_nativeauthenticator_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up jupyterhub/nativeauthenticator
    ✅ jupyterhub/nativeauthenticator - Final Score: 16.5 (Risk: 14, Semgrep: 5)

🎯 Processing: ruvnet/FACT  ✅ Completed: jupyterhub/nativeauthenticator (Score: 16.5)
    🔗 Clone URL: https://github.com/ruvnet/FACT.git

    📥 Cloning ruvnet/FACT...
    ✅ Successfully cloned ruvnet/FACT
    🔍 Running Semgrep on ruvnet/FACT...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/ruvnet_FACT_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 31 potential vulnerabilities
    🧹 Cleaned up ruvnet/FACT
    ✅ ruvnet/FACT - Final Score: 69.0 (Risk: 19, Semgrep: 100)

🎯 Processing: nrbnlulu/strawberry-django-auth
    🔗 Clone URL: https://github.com/nrbnlulu/strawberry-django-auth.git  ✅ Completed: ruvnet/FACT (Score: 69.0)
    📥 Cloning nrbnlulu/strawberry-django-auth...

    ✅ Successfully cloned nrbnlulu/strawberry-django-auth
    🔍 Running Semgrep on nrbnlulu/strawberry-django-auth...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/nrbnlulu_strawberry-django-auth_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 4 potential vulnerabilities
    🧹 Cleaned up nrbnlulu/strawberry-django-auth
    ✅ nrbnlulu/strawberry-django-auth - Final Score: 28.5 (Risk: 18, Semgrep: 21)

🎯 Processing: jupyterhub/ltiauthenticator
    🔗 Clone URL: https://github.com/jupyterhub/ltiauthenticator.git
    📥 Cloning jupyterhub/ltiauthenticator...
  ✅ Completed: nrbnlulu/strawberry-django-auth (Score: 28.5)
    ✅ Successfully cloned jupyterhub/ltiauthenticator
    🔍 Running Semgrep on jupyterhub/ltiauthenticator...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/jupyterhub_ltiauthenticator_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    ✅ No vulnerabilities found
    🧹 Cleaned up jupyterhub/ltiauthenticator
    ✅ jupyterhub/ltiauthenticator - Final Score: 9.0 (Risk: 9, Semgrep: 0)

🎯 Processing: jmfederico/django-use-email-as-username
    🔗 Clone URL: https://github.com/jmfederico/django-use-email-as-username.git
    📥 Cloning jmfederico/django-use-email-as-username...  ✅ Completed: jupyterhub/ltiauthenticator (Score: 9.0)

    ✅ Successfully cloned jmfederico/django-use-email-as-username
    🔍 Running Semgrep on jmfederico/django-use-email-as-username...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/jmfederico_django-use-email-as-username_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 1 potential vulnerabilities
    🧹 Cleaned up jmfederico/django-use-email-as-username
    ✅ jmfederico/django-use-email-as-username - Final Score: 15.5 (Risk: 13, Semgrep: 5)

🎯 Processing: matthiask/django-authlib
    🔗 Clone URL: https://github.com/matthiask/django-authlib.git
    📥 Cloning matthiask/django-authlib...
  ✅ Completed: jmfederico/django-use-email-as-username (Score: 15.5)
    ✅ Successfully cloned matthiask/django-authlib
    🔍 Running Semgrep on matthiask/django-authlib...
    📋 Running command: semgrep --config=auto --json --output semgrep_results/matthiask_django-authlib_semgrep.json...
    📊 Semgrep exit code: 0
    📝 Semgrep stderr: running 1062 rules from 1 config remote-registry_0
Rules:
<SKIPPED DATA (too many entries; use --max-log-list-entries)>
               
               
┌─────────────┐
│ Scan Status │
└─────────────┘
...
    🚨 Found 2 potential vulnerabilities
    🧹 Cleaned up matthiask/django-authlib
    ✅ matthiask/django-authlib - Final Score: 25.0 (Risk: 20, Semgrep: 10)
  ✅ Completed: matthiask/django-authlib (Score: 25.0)

📊 Saving results...
  🎯 Keeping top 10 repositories out of 300 analyzed
  ✅ CSV saved successfully: unified_vulnerability_results.csv
  ✅ Top 10 results: unified_vulnerability_results.csv
  📋 Detailed log: unified_scan_log.json

🏆 TOP 10 REPOSITORIES:
   1. uasoft-indonesia/badaso - Score: 100.0 (88 vulns)
   2. moonshine-software/moonshine - Score: 100.0 (16 vulns)
   3. Automattic/woocommerce-payments - Score: 100.0 (40 vulns)
   4. AeonOrg/Aeon-MLTB - Score: 100.0 (28 vulns)
   5. e107inc/e107 - Score: 94.0 (289 vulns)
   6. ClassicPress/ClassicPress - Score: 91.0 (340 vulns)
   7. error311/FileRise - Score: 91.0 (58 vulns)
   8. quiqr/quiqr-desktop - Score: 91.0 (176 vulns)
   9. et-nik/gameap - Score: 90.0 (24 vulns)
  10. processwire/processwire - Score: 88.0 (101 vulns)

📊 SCAN SUMMARY
========================================
Repositories found: 439
Repositories cloned: 300
Semgrep scans completed: 300
Total vulnerabilities found: 8284
High-risk repositories: 10
Average final score: 49.4

Top vulnerability categories:
  Other: 224
  Command Injection: 55
  XSS: 48
  Hardcoded Secrets: 44
  Path Traversal: 30

🎯 NEXT STEPS:
1. Review high_priority_unified_results.csv for immediate targets
2. Check Semgrep results in semgrep_results/ directory
3. Focus on repositories with both high risk scores and Semgrep findings
4. Follow responsible disclosure practices
