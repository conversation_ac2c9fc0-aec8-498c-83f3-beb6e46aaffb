{"version": "1.130.0", "results": [{"check_id": "python.lang.security.deserialization.pickle.avoid-pickle", "path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/cache.py", "start": {"line": 30, "col": 24, "offset": 831}, "end": {"line": 30, "col": 49, "offset": 856}, "extra": {"message": "Avoid using `pickle`, which is known to lead to code execution vulnerabilities. When unpickling, the serialized data could be manipulated to run arbitrary code. Instead, consider serializing the relevant data as JSON or a similar text-based serialization format.", "metadata": {"owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "references": ["https://docs.python.org/3/library/pickle.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/python.lang.security.deserialization.pickle.avoid-pickle", "shortlink": "https://sg.run/OPwB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.deserialization.pickle.avoid-pickle", "path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/cache.py", "start": {"line": 39, "col": 32, "offset": 1189}, "end": {"line": 39, "col": 42, "offset": 1199}, "extra": {"message": "Avoid using `pickle`, which is known to lead to code execution vulnerabilities. When unpickling, the serialized data could be manipulated to run arbitrary code. Instead, consider serializing the relevant data as JSON or a similar text-based serialization format.", "metadata": {"owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "references": ["https://docs.python.org/3/library/pickle.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/python.lang.security.deserialization.pickle.avoid-pickle", "shortlink": "https://sg.run/OPwB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.deserialization.pickle.avoid-pickle", "path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/cache.py", "start": {"line": 70, "col": 32, "offset": 2318}, "end": {"line": 70, "col": 42, "offset": 2328}, "extra": {"message": "Avoid using `pickle`, which is known to lead to code execution vulnerabilities. When unpickling, the serialized data could be manipulated to run arbitrary code. Instead, consider serializing the relevant data as JSON or a similar text-based serialization format.", "metadata": {"owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "references": ["https://docs.python.org/3/library/pickle.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/python.lang.security.deserialization.pickle.avoid-pickle", "shortlink": "https://sg.run/OPwB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/email_confirm.html", "start": {"line": 23, "col": 7, "offset": 902}, "end": {"line": 25, "col": 14, "offset": 1024}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/login.html", "start": {"line": 64, "col": 11, "offset": 2799}, "end": {"line": 68, "col": 18, "offset": 3100}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/password_reset.html", "start": {"line": 15, "col": 5, "offset": 584}, "end": {"line": 33, "col": 12, "offset": 1466}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/password_reset_from_key.html", "start": {"line": 15, "col": 11, "offset": 523}, "end": {"line": 30, "col": 18, "offset": 1318}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/signup.html", "start": {"line": 14, "col": 5, "offset": 446}, "end": {"line": 33, "col": 12, "offset": 1469}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/signup.html", "start": {"line": 38, "col": 9, "offset": 1679}, "end": {"line": 42, "col": 16, "offset": 1973}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/layout.html", "start": {"line": 139, "col": 17, "offset": 6366}, "end": {"line": 144, "col": 24, "offset": 6700}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.filter-with-is-safe.filter-with-is-safe", "path": "downloaded_repos/mangadventure_MangAdventure/config/templatetags/custom_tags.py", "start": {"line": 29, "col": 1, "offset": 785}, "end": {"line": 48, "col": 6, "offset": 1504}, "extra": {"message": "Detected Django filters flagged with 'is_safe'. 'is_safe' tells <PERSON><PERSON><PERSON> not to apply escaping on the value returned by this filter (although the input is escaped). Used improperly, 'is_safe' could expose your application to cross-site scripting (XSS) vulnerabilities. Ensure this filter does not 1) add HTML characters, 2) remove characters, or 3) use external data in any way. Consider instead removing 'is_safe' and explicitly marking safe content with 'mark_safe()'.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.1/topics/security/#cross-site-scripting-xss-protection", "https://docs.djangoproject.com/en/3.1/howto/custom-template-tags/#filters-and-auto-escaping", "https://stackoverflow.com/questions/7665512/why-use-is-safe"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.filter-with-is-safe.filter-with-is-safe", "shortlink": "https://sg.run/7o12"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.dynamic-urllib-use-detected.dynamic-urllib-use-detected", "path": "downloaded_repos/mangadventure_MangAdventure/config/templatetags/custom_tags.py", "start": {"line": 80, "col": 14, "offset": 2305}, "end": {"line": 80, "col": 30, "offset": 2321}, "extra": {"message": "Detected a dynamic value being used with urllib. urllib supports 'file://' schemes, so a dynamic value controlled by a malicious actor may allow them to read arbitrary files. Audit uses of urllib calls to ensure user data cannot control the URLs, or consider using the 'requests' library instead.", "metadata": {"cwe": ["CWE-939: Improper Authorization in Handler for Custom URL Scheme"], "owasp": "A01:2017 - Injection", "source-rule-url": "https://github.com/PyCQA/bandit/blob/b1411bfb43795d3ffd268bef17a839dee954c2b1/bandit/blacklists/calls.py#L163", "bandit-code": "B310", "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://cwe.mitre.org/data/definitions/939.html"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.dynamic-urllib-use-detected.dynamic-urllib-use-detected", "shortlink": "https://sg.run/dKZZ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/mangadventure_MangAdventure/docker/Dockerfile", "start": {"line": 34, "col": 1, "offset": 707}, "end": {"line": 34, "col": 68, "offset": 774}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD [\"uwsgi\", \"--ini=/etc/uwsgi.ini\", \"--uid=uwsgi\", \"--gid=uwsgi\"]", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/mangadventure_MangAdventure/reader/admin.py", "start": {"line": 243, "col": 52, "offset": 8430}, "end": {"line": 251, "col": 16, "offset": 8913}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/mangadventure_MangAdventure/static/scripts/search.js", "start": {"line": 10, "col": 5, "offset": 347}, "end": {"line": 10, "col": 27, "offset": 369}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/mangadventure_MangAdventure/static/scripts/search.js", "start": {"line": 112, "col": 11, "offset": 3992}, "end": {"line": 112, "col": 44, "offset": 4025}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/mangadventure_MangAdventure/static/scripts/search.js", "start": {"line": 118, "col": 9, "offset": 4187}, "end": {"line": 119, "col": 50, "offset": 4282}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/mangadventure_MangAdventure/static/scripts/search.js", "start": {"line": 120, "col": 9, "offset": 4291}, "end": {"line": 121, "col": 73, "offset": 4432}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/mangadventure_MangAdventure/static/scripts/search.js", "start": {"line": 122, "col": 9, "offset": 4441}, "end": {"line": 123, "col": 64, "offset": 4564}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/mangadventure_MangAdventure/static/scripts/search.js", "start": {"line": 124, "col": 9, "offset": 4573}, "end": {"line": 125, "col": 71, "offset": 4710}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/mangadventure_MangAdventure/static/scripts/tinymce-init.js", "start": {"line": 16, "col": 15, "offset": 686}, "end": {"line": 17, "col": 48, "offset": 768}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.unvalidated-password.unvalidated-password", "path": "downloaded_repos/mangadventure_MangAdventure/users/forms.py", "start": {"line": 217, "col": 13, "offset": 7533}, "end": {"line": 217, "col": 80, "offset": 7600}, "extra": {"message": "The password on 'self.instance.user' is being set without validating the password. Call django.contrib.auth.password_validation.validate_password() with validation functions before setting the password. See https://docs.djangoproject.com/en/3.0/topics/auth/passwords/ for more information.", "fix": "if django.contrib.auth.password_validation.validate_password(self.cleaned_data['new_password2'], user=self.instance.user):\n                self.instance.user.set_password(self.cleaned_data['new_password2'])", "metadata": {"cwe": ["CWE-521: Weak Password Requirements"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://docs.djangoproject.com/en/3.0/topics/auth/passwords/#module-django.contrib.auth.password_validation"], "category": "security", "technology": ["django"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/python.django.security.audit.unvalidated-password.unvalidated-password", "shortlink": "https://sg.run/OPBL"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/mangadventure_MangAdventure/users/templates/delete.html", "start": {"line": 18, "col": 5, "offset": 620}, "end": {"line": 20, "col": 12, "offset": 743}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/mangadventure_MangAdventure/users/templates/edit_user.html", "start": {"line": 20, "col": 5, "offset": 659}, "end": {"line": 34, "col": 12, "offset": 1276}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/email_confirm.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 18, "offset": 64}}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/email_confirm.html", "start": {"line": 7, "col": 1, "offset": 0}, "end": {"line": 8, "col": 19, "offset": 33}}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/email_confirm.html", "start": {"line": 34, "col": 1, "offset": 0}, "end": {"line": 34, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/email_confirm.html:1:\n `{% extends 'layout.html' %}\n{% load account %}\n{% block title %}` was unexpected", "path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/email_confirm.html", "spans": [{"file": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/email_confirm.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 18, "offset": 64}}, {"file": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/email_confirm.html", "start": {"line": 7, "col": 1, "offset": 0}, "end": {"line": 8, "col": 19, "offset": 33}}, {"file": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/email_confirm.html", "start": {"line": 34, "col": 1, "offset": 0}, "end": {"line": 34, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/login.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 41, "offset": 121}}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/login.html", "start": {"line": 12, "col": 1, "offset": 0}, "end": {"line": 13, "col": 19, "offset": 33}}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/login.html", "start": {"line": 73, "col": 1, "offset": 0}, "end": {"line": 73, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/login.html:1:\n `{% extends 'layout.html' %}\n{% load account user_tags %}\n{% block head_extras %}\n  {% if request.user.is_authenticated %}` was unexpected", "path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/login.html", "spans": [{"file": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/login.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 41, "offset": 121}}, {"file": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/login.html", "start": {"line": 12, "col": 1, "offset": 0}, "end": {"line": 13, "col": 19, "offset": 33}}, {"file": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/login.html", "start": {"line": 73, "col": 1, "offset": 0}, "end": {"line": 73, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/password_reset_from_key.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 18, "offset": 45}}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/password_reset_from_key.html", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 7, "col": 19, "offset": 33}}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/password_reset_from_key.html", "start": {"line": 12, "col": 5, "offset": 0}, "end": {"line": 13, "col": 20, "offset": 43}}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/password_reset_from_key.html", "start": {"line": 44, "col": 1, "offset": 0}, "end": {"line": 44, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/password_reset_from_key.html:1:\n `{% extends 'layout.html' %}\n{% block title %}` was unexpected", "path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/password_reset_from_key.html", "spans": [{"file": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/password_reset_from_key.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 18, "offset": 45}}, {"file": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/password_reset_from_key.html", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 7, "col": 19, "offset": 33}}, {"file": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/password_reset_from_key.html", "start": {"line": 12, "col": 5, "offset": 0}, "end": {"line": 13, "col": 20, "offset": 43}}, {"file": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/password_reset_from_key.html", "start": {"line": 44, "col": 1, "offset": 0}, "end": {"line": 44, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/password_reset_from_key_done.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 18, "offset": 45}}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/password_reset_from_key_done.html", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 7, "col": 19, "offset": 33}}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/password_reset_from_key_done.html", "start": {"line": 16, "col": 1, "offset": 0}, "end": {"line": 16, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/password_reset_from_key_done.html:1:\n `{% extends 'layout.html' %}\n{% block title %}` was unexpected", "path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/password_reset_from_key_done.html", "spans": [{"file": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/password_reset_from_key_done.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 18, "offset": 45}}, {"file": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/password_reset_from_key_done.html", "start": {"line": 6, "col": 1, "offset": 0}, "end": {"line": 7, "col": 19, "offset": 33}}, {"file": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/password_reset_from_key_done.html", "start": {"line": 16, "col": 1, "offset": 0}, "end": {"line": 16, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/footer.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 60, "offset": 59}}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/footer.html", "start": {"line": 39, "col": 1, "offset": 0}, "end": {"line": 39, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/footer.html:1:\n `{% load cache %}{% cache 604800 footer rss_url arg token %}` was unexpected", "path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/footer.html", "spans": [{"file": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/footer.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 60, "offset": 59}}, {"file": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/footer.html", "start": {"line": 39, "col": 1, "offset": 0}, "end": {"line": 39, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/index.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 24, "offset": 89}}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/index.html", "start": {"line": 11, "col": 1, "offset": 0}, "end": {"line": 13, "col": 43, "offset": 77}}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/index.html", "start": {"line": 48, "col": 3, "offset": 0}, "end": {"line": 52, "col": 15, "offset": 121}}]], "message": "Syntax error at line downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/index.html:1:\n `{% extends 'layout.html' %}\n{% load cache humanize custom_tags %}\n{% block head_extras %}` was unexpected", "path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/index.html", "spans": [{"file": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/index.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 24, "offset": 89}}, {"file": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/index.html", "start": {"line": 11, "col": 1, "offset": 0}, "end": {"line": 13, "col": 43, "offset": 77}}, {"file": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/index.html", "start": {"line": 48, "col": 3, "offset": 0}, "end": {"line": 52, "col": 15, "offset": 121}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/layout.html", "start": {"line": 1, "col": 16, "offset": 0}, "end": {"line": 1, "col": 51, "offset": 35}}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/layout.html", "start": {"line": 170, "col": 8, "offset": 0}, "end": {"line": 170, "col": 26, "offset": 18}}]], "message": "Syntax error at line downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/layout.html:1:\n `{% load cache static custom_tags %}` was unexpected", "path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/layout.html", "spans": [{"file": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/layout.html", "start": {"line": 1, "col": 16, "offset": 0}, "end": {"line": 1, "col": 51, "offset": 35}}, {"file": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/layout.html", "start": {"line": 170, "col": 8, "offset": 0}, "end": {"line": 170, "col": 26, "offset": 18}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/rapidoc.html", "start": {"line": 1, "col": 16, "offset": 0}, "end": {"line": 1, "col": 51, "offset": 35}}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/rapidoc.html", "start": {"line": 50, "col": 8, "offset": 0}, "end": {"line": 50, "col": 26, "offset": 18}}]], "message": "Syntax error at line downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/rapidoc.html:1:\n `{% load cache static custom_tags %}` was unexpected", "path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/rapidoc.html", "spans": [{"file": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/rapidoc.html", "start": {"line": 1, "col": 16, "offset": 0}, "end": {"line": 1, "col": 51, "offset": 35}}, {"file": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/rapidoc.html", "start": {"line": 50, "col": 8, "offset": 0}, "end": {"line": 50, "col": 26, "offset": 18}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/search.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 19, "offset": 121}}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/search.html", "start": {"line": 13, "col": 1, "offset": 0}, "end": {"line": 14, "col": 18, "offset": 32}}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/search.html", "start": {"line": 18, "col": 1, "offset": 0}, "end": {"line": 19, "col": 20, "offset": 34}}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/search.html", "start": {"line": 37, "col": 35, "offset": 0}, "end": {"line": 37, "col": 36, "offset": 1}}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/search.html", "start": {"line": 103, "col": 48, "offset": 0}, "end": {"line": 103, "col": 68, "offset": 20}}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/search.html", "start": {"line": 106, "col": 48, "offset": 0}, "end": {"line": 106, "col": 68, "offset": 20}}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/search.html", "start": {"line": 127, "col": 40, "offset": 0}, "end": {"line": 127, "col": 55, "offset": 15}}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/search.html", "start": {"line": 135, "col": 1, "offset": 0}, "end": {"line": 135, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/search.html:1:\n `{% extends 'layout.html' %}\n{% load static humanize custom_tags %}\n{% block canonical %}{% endblock %}\n{% block robots %}` was unexpected", "path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/search.html", "spans": [{"file": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/search.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 19, "offset": 121}}, {"file": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/search.html", "start": {"line": 13, "col": 1, "offset": 0}, "end": {"line": 14, "col": 18, "offset": 32}}, {"file": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/search.html", "start": {"line": 18, "col": 1, "offset": 0}, "end": {"line": 19, "col": 20, "offset": 34}}, {"file": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/search.html", "start": {"line": 37, "col": 35, "offset": 0}, "end": {"line": 37, "col": 36, "offset": 1}}, {"file": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/search.html", "start": {"line": 103, "col": 48, "offset": 0}, "end": {"line": 103, "col": 68, "offset": 20}}, {"file": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/search.html", "start": {"line": 106, "col": 48, "offset": 0}, "end": {"line": 106, "col": 68, "offset": 20}}, {"file": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/search.html", "start": {"line": 127, "col": 40, "offset": 0}, "end": {"line": 127, "col": 55, "offset": 15}}, {"file": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/search.html", "start": {"line": 135, "col": 1, "offset": 0}, "end": {"line": 135, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mangadventure_MangAdventure/groups/templates/all_groups.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 24, "offset": 75}}, {"path": "downloaded_repos/mangadventure_MangAdventure/groups/templates/all_groups.html", "start": {"line": 11, "col": 1, "offset": 0}, "end": {"line": 13, "col": 38, "offset": 72}}, {"path": "downloaded_repos/mangadventure_MangAdventure/groups/templates/all_groups.html", "start": {"line": 34, "col": 3, "offset": 0}, "end": {"line": 35, "col": 15, "offset": 29}}]], "message": "Syntax error at line downloaded_repos/mangadventure_MangAdventure/groups/templates/all_groups.html:1:\n `{% extends 'layout.html' %}\n{% load cache static %}\n{% block head_extras %}` was unexpected", "path": "downloaded_repos/mangadventure_MangAdventure/groups/templates/all_groups.html", "spans": [{"file": "downloaded_repos/mangadventure_MangAdventure/groups/templates/all_groups.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 24, "offset": 75}}, {"file": "downloaded_repos/mangadventure_MangAdventure/groups/templates/all_groups.html", "start": {"line": 11, "col": 1, "offset": 0}, "end": {"line": 13, "col": 38, "offset": 72}}, {"file": "downloaded_repos/mangadventure_MangAdventure/groups/templates/all_groups.html", "start": {"line": 34, "col": 3, "offset": 0}, "end": {"line": 35, "col": 15, "offset": 29}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mangadventure_MangAdventure/groups/templates/group.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 25, "offset": 123}}, {"path": "downloaded_repos/mangadventure_MangAdventure/groups/templates/group.html", "start": {"line": 28, "col": 1, "offset": 0}, "end": {"line": 30, "col": 44, "offset": 78}}, {"path": "downloaded_repos/mangadventure_MangAdventure/groups/templates/group.html", "start": {"line": 138, "col": 3, "offset": 0}, "end": {"line": 142, "col": 15, "offset": 138}}]], "message": "Syntax error at line downloaded_repos/mangadventure_MangAdventure/groups/templates/group.html:1:\n `{% extends 'layout.html' %}\n{% load cache static group_tags custom_tags %}\n{% block description %}\n  {% if group.twitter %}` was unexpected", "path": "downloaded_repos/mangadventure_MangAdventure/groups/templates/group.html", "spans": [{"file": "downloaded_repos/mangadventure_MangAdventure/groups/templates/group.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 25, "offset": 123}}, {"file": "downloaded_repos/mangadventure_MangAdventure/groups/templates/group.html", "start": {"line": 28, "col": 1, "offset": 0}, "end": {"line": 30, "col": 44, "offset": 78}}, {"file": "downloaded_repos/mangadventure_MangAdventure/groups/templates/group.html", "start": {"line": 138, "col": 3, "offset": 0}, "end": {"line": 142, "col": 15, "offset": 138}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mangadventure_MangAdventure/reader/templates/chapter.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 19, "offset": 76}}, {"path": "downloaded_repos/mangadventure_MangAdventure/reader/templates/chapter.html", "start": {"line": 38, "col": 1, "offset": 0}, "end": {"line": 40, "col": 61, "offset": 95}}, {"path": "downloaded_repos/mangadventure_MangAdventure/reader/templates/chapter.html", "start": {"line": 46, "col": 3, "offset": 0}, "end": {"line": 46, "col": 16, "offset": 13}}, {"path": "downloaded_repos/mangadventure_MangAdventure/reader/templates/chapter.html", "start": {"line": 60, "col": 29, "offset": 0}, "end": {"line": 60, "col": 30, "offset": 1}}, {"path": "downloaded_repos/mangadventure_MangAdventure/reader/templates/chapter.html", "start": {"line": 83, "col": 33, "offset": 0}, "end": {"line": 83, "col": 34, "offset": 1}}, {"path": "downloaded_repos/mangadventure_MangAdventure/reader/templates/chapter.html", "start": {"line": 116, "col": 24, "offset": 0}, "end": {"line": 117, "col": 36, "offset": 44}}, {"path": "downloaded_repos/mangadventure_MangAdventure/reader/templates/chapter.html", "start": {"line": 136, "col": 1, "offset": 0}, "end": {"line": 136, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/mangadventure_MangAdventure/reader/templates/chapter.html:1:\n `{% extends 'layout.html' %}\n{% load static custom_tags %}\n{% block robots %}` was unexpected", "path": "downloaded_repos/mangadventure_MangAdventure/reader/templates/chapter.html", "spans": [{"file": "downloaded_repos/mangadventure_MangAdventure/reader/templates/chapter.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 19, "offset": 76}}, {"file": "downloaded_repos/mangadventure_MangAdventure/reader/templates/chapter.html", "start": {"line": 38, "col": 1, "offset": 0}, "end": {"line": 40, "col": 61, "offset": 95}}, {"file": "downloaded_repos/mangadventure_MangAdventure/reader/templates/chapter.html", "start": {"line": 46, "col": 3, "offset": 0}, "end": {"line": 46, "col": 16, "offset": 13}}, {"file": "downloaded_repos/mangadventure_MangAdventure/reader/templates/chapter.html", "start": {"line": 60, "col": 29, "offset": 0}, "end": {"line": 60, "col": 30, "offset": 1}}, {"file": "downloaded_repos/mangadventure_MangAdventure/reader/templates/chapter.html", "start": {"line": 83, "col": 33, "offset": 0}, "end": {"line": 83, "col": 34, "offset": 1}}, {"file": "downloaded_repos/mangadventure_MangAdventure/reader/templates/chapter.html", "start": {"line": 116, "col": 24, "offset": 0}, "end": {"line": 117, "col": 36, "offset": 44}}, {"file": "downloaded_repos/mangadventure_MangAdventure/reader/templates/chapter.html", "start": {"line": 136, "col": 1, "offset": 0}, "end": {"line": 136, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mangadventure_MangAdventure/reader/templates/directory.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 33, "offset": 122}}, {"path": "downloaded_repos/mangadventure_MangAdventure/reader/templates/directory.html", "start": {"line": 16, "col": 1, "offset": 0}, "end": {"line": 18, "col": 41, "offset": 75}}, {"path": "downloaded_repos/mangadventure_MangAdventure/reader/templates/directory.html", "start": {"line": 60, "col": 3, "offset": 0}, "end": {"line": 64, "col": 15, "offset": 120}}]], "message": "Syntax error at line downloaded_repos/mangadventure_MangAdventure/reader/templates/directory.html:1:\n `{% extends 'layout.html' %}\n{% load cache custom_tags humanize %}\n{% block head_extras %}\n  {{ library|jsonld:'library' }}` was unexpected", "path": "downloaded_repos/mangadventure_MangAdventure/reader/templates/directory.html", "spans": [{"file": "downloaded_repos/mangadventure_MangAdventure/reader/templates/directory.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 33, "offset": 122}}, {"file": "downloaded_repos/mangadventure_MangAdventure/reader/templates/directory.html", "start": {"line": 16, "col": 1, "offset": 0}, "end": {"line": 18, "col": 41, "offset": 75}}, {"file": "downloaded_repos/mangadventure_MangAdventure/reader/templates/directory.html", "start": {"line": 60, "col": 3, "offset": 0}, "end": {"line": 64, "col": 15, "offset": 120}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mangadventure_MangAdventure/reader/templates/series.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 41, "offset": 131}}, {"path": "downloaded_repos/mangadventure_MangAdventure/reader/templates/series.html", "start": {"line": 7, "col": 3, "offset": 0}, "end": {"line": 8, "col": 33, "offset": 44}}, {"path": "downloaded_repos/mangadventure_MangAdventure/reader/templates/series.html", "start": {"line": 32, "col": 1, "offset": 0}, "end": {"line": 33, "col": 20, "offset": 34}}, {"path": "downloaded_repos/mangadventure_MangAdventure/reader/templates/series.html", "start": {"line": 110, "col": 1, "offset": 0}, "end": {"line": 113, "col": 15, "offset": 127}}]], "message": "Syntax error at line downloaded_repos/mangadventure_MangAdventure/reader/templates/series.html:1:\n `{% extends 'layout.html' %}\n{% load static humanize custom_tags %}\n{% block head_extras %}\n  {% if request.user.is_authenticated %}` was unexpected", "path": "downloaded_repos/mangadventure_MangAdventure/reader/templates/series.html", "spans": [{"file": "downloaded_repos/mangadventure_MangAdventure/reader/templates/series.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 41, "offset": 131}}, {"file": "downloaded_repos/mangadventure_MangAdventure/reader/templates/series.html", "start": {"line": 7, "col": 3, "offset": 0}, "end": {"line": 8, "col": 33, "offset": 44}}, {"file": "downloaded_repos/mangadventure_MangAdventure/reader/templates/series.html", "start": {"line": 32, "col": 1, "offset": 0}, "end": {"line": 33, "col": 20, "offset": 34}}, {"file": "downloaded_repos/mangadventure_MangAdventure/reader/templates/series.html", "start": {"line": 110, "col": 1, "offset": 0}, "end": {"line": 113, "col": 15, "offset": 127}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mangadventure_MangAdventure/scripts/deploy.sh", "start": {"line": 7, "col": 20, "offset": 0}, "end": {"line": 13, "col": 5, "offset": 185}}]], "message": "Syntax error at line downloaded_repos/mangadventure_MangAdventure/scripts/deploy.sh:7:\n `.env \\\n  -e '/ADMIN=/d' \\\n  -e 's/none/weserv/' \\\n  -e 's/favicon.ico/logo.png/' \\\n  -e 's/EMAIL_URL=\".*\"/EMAIL_URL=\"console:\"/' \\\n  -e 's/${DOMAIN},www.${DOMAIN}/.onrender.com/' \\\n  -e` was unexpected", "path": "downloaded_repos/mangadventure_MangAdventure/scripts/deploy.sh", "spans": [{"file": "downloaded_repos/mangadventure_MangAdventure/scripts/deploy.sh", "start": {"line": 7, "col": 20, "offset": 0}, "end": {"line": 13, "col": 5, "offset": 185}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mangadventure_MangAdventure/users/templates/bookmarks.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 19, "offset": 66}}, {"path": "downloaded_repos/mangadventure_MangAdventure/users/templates/bookmarks.html", "start": {"line": 10, "col": 1, "offset": 0}, "end": {"line": 11, "col": 20, "offset": 34}}, {"path": "downloaded_repos/mangadventure_MangAdventure/users/templates/bookmarks.html", "start": {"line": 35, "col": 1, "offset": 0}, "end": {"line": 38, "col": 15, "offset": 124}}]], "message": "Syntax error at line downloaded_repos/mangadventure_MangAdventure/users/templates/bookmarks.html:1:\n `{% extends 'layout.html' %}\n{% load humanize %}\n{% block robots %}` was unexpected", "path": "downloaded_repos/mangadventure_MangAdventure/users/templates/bookmarks.html", "spans": [{"file": "downloaded_repos/mangadventure_MangAdventure/users/templates/bookmarks.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 19, "offset": 66}}, {"file": "downloaded_repos/mangadventure_MangAdventure/users/templates/bookmarks.html", "start": {"line": 10, "col": 1, "offset": 0}, "end": {"line": 11, "col": 20, "offset": 34}}, {"file": "downloaded_repos/mangadventure_MangAdventure/users/templates/bookmarks.html", "start": {"line": 35, "col": 1, "offset": 0}, "end": {"line": 38, "col": 15, "offset": 124}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mangadventure_MangAdventure/users/templates/profile.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 19, "offset": 131}}, {"path": "downloaded_repos/mangadventure_MangAdventure/users/templates/profile.html", "start": {"line": 29, "col": 3, "offset": 0}, "end": {"line": 32, "col": 40, "offset": 88}}, {"path": "downloaded_repos/mangadventure_MangAdventure/users/templates/profile.html", "start": {"line": 67, "col": 3, "offset": 0}, "end": {"line": 68, "col": 15, "offset": 29}}]], "message": "Syntax error at line downloaded_repos/mangadventure_MangAdventure/users/templates/profile.html:1:\n `{% extends 'layout.html' %}\n{% load cache account custom_tags %}\n{% user_display request.user as user_display %}\n{% block robots %}` was unexpected", "path": "downloaded_repos/mangadventure_MangAdventure/users/templates/profile.html", "spans": [{"file": "downloaded_repos/mangadventure_MangAdventure/users/templates/profile.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 19, "offset": 131}}, {"file": "downloaded_repos/mangadventure_MangAdventure/users/templates/profile.html", "start": {"line": 29, "col": 3, "offset": 0}, "end": {"line": 32, "col": 40, "offset": 88}}, {"file": "downloaded_repos/mangadventure_MangAdventure/users/templates/profile.html", "start": {"line": 67, "col": 3, "offset": 0}, "end": {"line": 68, "col": 15, "offset": 29}}]}], "paths": {"scanned": ["downloaded_repos/mangadventure_MangAdventure/.dockerignore", "downloaded_repos/mangadventure_MangAdventure/.editorconfig", "downloaded_repos/mangadventure_MangAdventure/.env.example", "downloaded_repos/mangadventure_MangAdventure/.gitattributes", "downloaded_repos/mangadventure_MangAdventure/.github/CODE_OF_CONDUCT.md", "downloaded_repos/mangadventure_MangAdventure/.github/CONTRIBUTING.md", "downloaded_repos/mangadventure_MangAdventure/.github/ISSUE_TEMPLATE/bug-report.yml", "downloaded_repos/mangadventure_MangAdventure/.github/ISSUE_TEMPLATE/config.yml", "downloaded_repos/mangadventure_MangAdventure/.github/ISSUE_TEMPLATE/feature-request.yml", "downloaded_repos/mangadventure_MangAdventure/.github/ISSUE_TEMPLATE/question.yml", "downloaded_repos/mangadventure_MangAdventure/.github/PULL_REQUEST_TEMPLATE.md", "downloaded_repos/mangadventure_MangAdventure/.github/SECURITY.md", "downloaded_repos/mangadventure_MangAdventure/.github/workflows/deploy.yml", "downloaded_repos/mangadventure_MangAdventure/.github/workflows/release.yml", "downloaded_repos/mangadventure_MangAdventure/.github/workflows/scans.yml", "downloaded_repos/mangadventure_MangAdventure/.github/workflows/tests.yml", "downloaded_repos/mangadventure_MangAdventure/.gitignore", "downloaded_repos/mangadventure_MangAdventure/LICENSE", "downloaded_repos/mangadventure_MangAdventure/MANIFEST.in", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/__init__.py", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/__main__.py", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/bad_bots.py", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/cache.py", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/converters.py", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/fields.py", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/filters.py", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/forms.py", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/jsonld.py", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/middleware.py", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/search.py", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/settings.py", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/sitemaps.py", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/storage.py", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/account_inactive.html", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/email/email_confirmation_message.txt", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/email/email_confirmation_signup_message.txt", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/email/email_confirmation_signup_subject.txt", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/email/email_confirmation_subject.txt", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/email/password_reset_key_message.txt", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/email/password_reset_key_subject.txt", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/email_confirm.html", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/login.html", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/password_reset.html", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/password_reset_done.html", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/password_reset_from_key.html", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/password_reset_from_key_done.html", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/signup.html", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/verification_sent.html", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/contribute.json", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/error.html", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/flatpages/default.html", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/footer.html", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/image-sitemap.xml", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/index.html", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/layout.html", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/manifest.webmanifest", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/opensearch.xml", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/rapidoc.html", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/search.html", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/socialaccount/authentication_error.html", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/urls.py", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/utils.py", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/validators.py", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/views.py", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/widgets.py", "downloaded_repos/mangadventure_MangAdventure/MangAdventure/wsgi.py", "downloaded_repos/mangadventure_MangAdventure/README.md", "downloaded_repos/mangadventure_MangAdventure/api/__init__.py", "downloaded_repos/mangadventure_MangAdventure/api/urls.py", "downloaded_repos/mangadventure_MangAdventure/api/v1/__init__.py", "downloaded_repos/mangadventure_MangAdventure/api/v1/apps.py", "downloaded_repos/mangadventure_MangAdventure/api/v1/response.py", "downloaded_repos/mangadventure_MangAdventure/api/v1/urls.py", "downloaded_repos/mangadventure_MangAdventure/api/v1/views.py", "downloaded_repos/mangadventure_MangAdventure/api/v2/__init__.py", "downloaded_repos/mangadventure_MangAdventure/api/v2/apps.py", "downloaded_repos/mangadventure_MangAdventure/api/v2/auth.py", "downloaded_repos/mangadventure_MangAdventure/api/v2/mixins.py", "downloaded_repos/mangadventure_MangAdventure/api/v2/negotiation.py", "downloaded_repos/mangadventure_MangAdventure/api/v2/pagination.py", "downloaded_repos/mangadventure_MangAdventure/api/v2/schema.py", "downloaded_repos/mangadventure_MangAdventure/api/v2/urls.py", "downloaded_repos/mangadventure_MangAdventure/api/v2/views.py", "downloaded_repos/mangadventure_MangAdventure/config/__init__.py", "downloaded_repos/mangadventure_MangAdventure/config/admin.py", "downloaded_repos/mangadventure_MangAdventure/config/apps.py", "downloaded_repos/mangadventure_MangAdventure/config/context_processors.py", "downloaded_repos/mangadventure_MangAdventure/config/management/__init__.py", "downloaded_repos/mangadventure_MangAdventure/config/management/commands/__init__.py", "downloaded_repos/mangadventure_MangAdventure/config/management/commands/clearcache.py", "downloaded_repos/mangadventure_MangAdventure/config/management/commands/createsuperuser.py", "downloaded_repos/mangadventure_MangAdventure/config/management/commands/fs2import.py", "downloaded_repos/mangadventure_MangAdventure/config/management/commands/logs.py", "downloaded_repos/mangadventure_MangAdventure/config/migrations/0001_initial.py", "downloaded_repos/mangadventure_MangAdventure/config/migrations/0002_scanlator_permissions.py", "downloaded_repos/mangadventure_MangAdventure/config/migrations/__init__.py", "downloaded_repos/mangadventure_MangAdventure/config/templatetags/__init__.py", "downloaded_repos/mangadventure_MangAdventure/config/templatetags/custom_tags.py", "downloaded_repos/mangadventure_MangAdventure/config/templatetags/flatpage_tags.py", "downloaded_repos/mangadventure_MangAdventure/config/urls.py", "downloaded_repos/mangadventure_MangAdventure/docker/Dockerfile", "downloaded_repos/mangadventure_MangAdventure/docker/uwsgi.ini", "downloaded_repos/mangadventure_MangAdventure/docs/.readthedocs.yaml", "downloaded_repos/mangadventure_MangAdventure/docs/Makefile", "downloaded_repos/mangadventure_MangAdventure/docs/_ext/mangadventure_patches.py", "downloaded_repos/mangadventure_MangAdventure/docs/_static/css/style.css", "downloaded_repos/mangadventure_MangAdventure/docs/_static/logo.png", "downloaded_repos/mangadventure_MangAdventure/docs/api/artists/all.rst", "downloaded_repos/mangadventure_MangAdventure/docs/api/artists/index.rst", "downloaded_repos/mangadventure_MangAdventure/docs/api/artists/single.rst", "downloaded_repos/mangadventure_MangAdventure/docs/api/authors/all.rst", "downloaded_repos/mangadventure_MangAdventure/docs/api/authors/index.rst", "downloaded_repos/mangadventure_MangAdventure/docs/api/authors/single.rst", "downloaded_repos/mangadventure_MangAdventure/docs/api/categories.rst", "downloaded_repos/mangadventure_MangAdventure/docs/api/groups/all.rst", "downloaded_repos/mangadventure_MangAdventure/docs/api/groups/index.rst", "downloaded_repos/mangadventure_MangAdventure/docs/api/groups/single.rst", "downloaded_repos/mangadventure_MangAdventure/docs/api/includes/chapter.rst", "downloaded_repos/mangadventure_MangAdventure/docs/api/includes/group.rst", "downloaded_repos/mangadventure_MangAdventure/docs/api/includes/headers-etag.rst", "downloaded_repos/mangadventure_MangAdventure/docs/api/includes/headers-modified.rst", "downloaded_repos/mangadventure_MangAdventure/docs/api/includes/series.rst", "downloaded_repos/mangadventure_MangAdventure/docs/api/includes/status.rst", "downloaded_repos/mangadventure_MangAdventure/docs/api/index.rst", "downloaded_repos/mangadventure_MangAdventure/docs/api/releases.rst", "downloaded_repos/mangadventure_MangAdventure/docs/api/series/all.rst", "downloaded_repos/mangadventure_MangAdventure/docs/api/series/chapter.rst", "downloaded_repos/mangadventure_MangAdventure/docs/api/series/index.rst", "downloaded_repos/mangadventure_MangAdventure/docs/api/series/single.rst", "downloaded_repos/mangadventure_MangAdventure/docs/api/series/volume.rst", "downloaded_repos/mangadventure_MangAdventure/docs/changelog.rst", "downloaded_repos/mangadventure_MangAdventure/docs/compatibility.rst", "downloaded_repos/mangadventure_MangAdventure/docs/conf.py", "downloaded_repos/mangadventure_MangAdventure/docs/examples/apache.conf", "downloaded_repos/mangadventure_MangAdventure/docs/examples/nginx.conf", "downloaded_repos/mangadventure_MangAdventure/docs/index.rst", "downloaded_repos/mangadventure_MangAdventure/docs/install.rst", "downloaded_repos/mangadventure_MangAdventure/docs/modules/MangAdventure.rst", "downloaded_repos/mangadventure_MangAdventure/docs/modules/api.rst", "downloaded_repos/mangadventure_MangAdventure/docs/modules/api.v1.rst", "downloaded_repos/mangadventure_MangAdventure/docs/modules/api.v2.rst", "downloaded_repos/mangadventure_MangAdventure/docs/modules/config.management.commands.rst", "downloaded_repos/mangadventure_MangAdventure/docs/modules/config.management.rst", "downloaded_repos/mangadventure_MangAdventure/docs/modules/config.rst", "downloaded_repos/mangadventure_MangAdventure/docs/modules/config.templatetags.rst", "downloaded_repos/mangadventure_MangAdventure/docs/modules/groups.rst", "downloaded_repos/mangadventure_MangAdventure/docs/modules/groups.templatetags.rst", "downloaded_repos/mangadventure_MangAdventure/docs/modules/index.rst", "downloaded_repos/mangadventure_MangAdventure/docs/modules/reader.rst", "downloaded_repos/mangadventure_MangAdventure/docs/modules/users.rst", "downloaded_repos/mangadventure_MangAdventure/docs/modules/users.templatetags.rst", "downloaded_repos/mangadventure_MangAdventure/docs/roadmap.rst", "downloaded_repos/mangadventure_MangAdventure/groups/__init__.py", "downloaded_repos/mangadventure_MangAdventure/groups/admin.py", "downloaded_repos/mangadventure_MangAdventure/groups/api.py", "downloaded_repos/mangadventure_MangAdventure/groups/apps.py", "downloaded_repos/mangadventure_MangAdventure/groups/feeds.py", "downloaded_repos/mangadventure_MangAdventure/groups/migrations/0001_squashed.py", "downloaded_repos/mangadventure_MangAdventure/groups/migrations/0002_managers.py", "downloaded_repos/mangadventure_MangAdventure/groups/migrations/0003_alter_group_id.py", "downloaded_repos/mangadventure_MangAdventure/groups/migrations/0004_constraints.py", "downloaded_repos/mangadventure_MangAdventure/groups/migrations/__init__.py", "downloaded_repos/mangadventure_MangAdventure/groups/models.py", "downloaded_repos/mangadventure_MangAdventure/groups/serializers.py", "downloaded_repos/mangadventure_MangAdventure/groups/sitemaps.py", "downloaded_repos/mangadventure_MangAdventure/groups/templates/all_groups.html", "downloaded_repos/mangadventure_MangAdventure/groups/templates/group.html", "downloaded_repos/mangadventure_MangAdventure/groups/templatetags/__init__.py", "downloaded_repos/mangadventure_MangAdventure/groups/templatetags/group_tags.py", "downloaded_repos/mangadventure_MangAdventure/groups/urls.py", "downloaded_repos/mangadventure_MangAdventure/groups/views.py", "downloaded_repos/mangadventure_MangAdventure/pyproject.toml", "downloaded_repos/mangadventure_MangAdventure/reader/__init__.py", "downloaded_repos/mangadventure_MangAdventure/reader/admin.py", "downloaded_repos/mangadventure_MangAdventure/reader/api.py", "downloaded_repos/mangadventure_MangAdventure/reader/apps.py", "downloaded_repos/mangadventure_MangAdventure/reader/feeds.py", "downloaded_repos/mangadventure_MangAdventure/reader/filters.py", "downloaded_repos/mangadventure_MangAdventure/reader/fixtures/categories.xml", "downloaded_repos/mangadventure_MangAdventure/reader/migrations/0001_squashed.py", "downloaded_repos/mangadventure_MangAdventure/reader/migrations/0002_series_created.py", "downloaded_repos/mangadventure_MangAdventure/reader/migrations/0003_chapter_published.py", "downloaded_repos/mangadventure_MangAdventure/reader/migrations/0004_aliases.py", "downloaded_repos/mangadventure_MangAdventure/reader/migrations/0005_managers.py", "downloaded_repos/mangadventure_MangAdventure/reader/migrations/0006_file_limits.py", "downloaded_repos/mangadventure_MangAdventure/reader/migrations/0007_series_licensed.py", "downloaded_repos/mangadventure_MangAdventure/reader/migrations/0008_chapter_views.py", "downloaded_repos/mangadventure_MangAdventure/reader/migrations/0009_constraints.py", "downloaded_repos/mangadventure_MangAdventure/reader/migrations/0010_null_volumes.py", "downloaded_repos/mangadventure_MangAdventure/reader/migrations/0011_series_status.py", "downloaded_repos/mangadventure_MangAdventure/reader/migrations/__init__.py", "downloaded_repos/mangadventure_MangAdventure/reader/models.py", "downloaded_repos/mangadventure_MangAdventure/reader/receivers.py", "downloaded_repos/mangadventure_MangAdventure/reader/serializers.py", "downloaded_repos/mangadventure_MangAdventure/reader/sitemaps.py", "downloaded_repos/mangadventure_MangAdventure/reader/templates/chapter.html", "downloaded_repos/mangadventure_MangAdventure/reader/templates/directory.html", "downloaded_repos/mangadventure_MangAdventure/reader/templates/series.html", "downloaded_repos/mangadventure_MangAdventure/reader/urls.py", "downloaded_repos/mangadventure_MangAdventure/reader/views.py", "downloaded_repos/mangadventure_MangAdventure/render.yaml", "downloaded_repos/mangadventure_MangAdventure/scripts/deploy.sh", "downloaded_repos/mangadventure_MangAdventure/scripts/lint.sh", "downloaded_repos/mangadventure_MangAdventure/setup.py", "downloaded_repos/mangadventure_MangAdventure/static/scripts/bookmark.js", "downloaded_repos/mangadventure_MangAdventure/static/scripts/chapter.js", "downloaded_repos/mangadventure_MangAdventure/static/scripts/search.js", "downloaded_repos/mangadventure_MangAdventure/static/scripts/tinymce-init.js", "downloaded_repos/mangadventure_MangAdventure/static/styles/_mixins.scss", "downloaded_repos/mangadventure_MangAdventure/static/styles/chapter.scss", "downloaded_repos/mangadventure_MangAdventure/static/styles/noscript.css", "downloaded_repos/mangadventure_MangAdventure/static/styles/style.scss", "downloaded_repos/mangadventure_MangAdventure/static/styles/tinymce.scss", "downloaded_repos/mangadventure_MangAdventure/users/__init__.py", "downloaded_repos/mangadventure_MangAdventure/users/adapters.py", "downloaded_repos/mangadventure_MangAdventure/users/admin.py", "downloaded_repos/mangadventure_MangAdventure/users/api.py", "downloaded_repos/mangadventure_MangAdventure/users/apps.py", "downloaded_repos/mangadventure_MangAdventure/users/backends.py", "downloaded_repos/mangadventure_MangAdventure/users/feeds.py", "downloaded_repos/mangadventure_MangAdventure/users/forms.py", "downloaded_repos/mangadventure_MangAdventure/users/migrations/0001_squashed.py", "downloaded_repos/mangadventure_MangAdventure/users/migrations/0002_userprofile_token.py", "downloaded_repos/mangadventure_MangAdventure/users/migrations/0003_apikey.py", "downloaded_repos/mangadventure_MangAdventure/users/migrations/0004_constraints.py", "downloaded_repos/mangadventure_MangAdventure/users/migrations/__init__.py", "downloaded_repos/mangadventure_MangAdventure/users/models.py", "downloaded_repos/mangadventure_MangAdventure/users/serializers.py", "downloaded_repos/mangadventure_MangAdventure/users/templates/bookmarks.html", "downloaded_repos/mangadventure_MangAdventure/users/templates/delete.html", "downloaded_repos/mangadventure_MangAdventure/users/templates/edit_user.html", "downloaded_repos/mangadventure_MangAdventure/users/templates/profile.html", "downloaded_repos/mangadventure_MangAdventure/users/templatetags/__init__.py", "downloaded_repos/mangadventure_MangAdventure/users/templatetags/user_tags.py", "downloaded_repos/mangadventure_MangAdventure/users/urls.py", "downloaded_repos/mangadventure_MangAdventure/users/views.py"], "skipped": [{"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/email_confirm.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/login.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/password_reset_from_key.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/account/password_reset_from_key_done.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/footer.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/index.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/layout.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/rapidoc.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/templates/search.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/tests/base.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/tests/settings.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/tests/test_cache.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/tests/test_forms.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/tests/test_middleware.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/tests/test_sitemaps.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/tests/test_storage.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/tests/test_validators.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/tests/test_views.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/tests/utils.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/api/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/api/tests/conftest.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/api/tests/fixtures/authors_artists.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/api/tests/fixtures/chapters.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/api/tests/fixtures/groups.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/api/tests/fixtures/series.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/api/tests/test_v1.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/api/tests/test_v2.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/config/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/config/tests/conftest.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/config/tests/fixtures/foolslide2/foolslide.xml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/config/tests/fixtures/info_pages.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/config/tests/fixtures/users.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/config/tests/test_admin.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/config/tests/test_fs2import.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/config/tests/test_tags.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/config/tests/test_urls.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/groups/templates/all_groups.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mangadventure_MangAdventure/groups/templates/group.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mangadventure_MangAdventure/groups/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/groups/tests/conftest.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/groups/tests/fixtures/users.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/groups/tests/test_admin.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/groups/tests/test_feeds.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/groups/tests/test_models.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/groups/tests/test_sitemaps.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/groups/tests/test_tags.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/groups/tests/test_views.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/reader/templates/chapter.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mangadventure_MangAdventure/reader/templates/directory.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mangadventure_MangAdventure/reader/templates/series.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mangadventure_MangAdventure/reader/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/reader/tests/conftest.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/reader/tests/fixtures/users.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/reader/tests/test_admin.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/reader/tests/test_feeds.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/reader/tests/test_models.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/reader/tests/test_receivers.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/reader/tests/test_sitemaps.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/reader/tests/test_views.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/scripts/deploy.sh", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mangadventure_MangAdventure/static/vendor/rapidoc-min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/static/vendor/tablesort.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/static/vendor/umami.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/users/templates/bookmarks.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mangadventure_MangAdventure/users/templates/profile.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mangadventure_MangAdventure/users/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/users/tests/conftest.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/users/tests/fixtures/series.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/users/tests/fixtures/users.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/users/tests/test_adapters.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/users/tests/test_admin.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/users/tests/test_backends.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/users/tests/test_feeds.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/users/tests/test_forms.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/users/tests/test_models.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/users/tests/test_tags.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/users/tests/test_utils.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mangadventure_MangAdventure/users/tests/test_views.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.938711166381836, "profiling_times": {"config_time": 6.7726194858551025, "core_time": 4.866072416305542, "ignores_time": 0.0019643306732177734, "total_time": 11.641997575759888}, "parsing_time": {"total_time": 1.3030672073364258, "per_file_time": {"mean": 0.00868711471557618, "std_dev": 0.00019150595466953748}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 15.081745147705078, "per_file_time": {"mean": 0.023788241557894448, "std_dev": 0.00993309042444613}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 7.009240388870239, "per_file_and_rule_time": {"mean": 0.005540901493178053, "std_dev": 0.00042343047793062013}, "very_slow_stats": {"time_ratio": 0.2413038793818201, "count_ratio": 0.006324110671936759}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/mangadventure_MangAdventure/reader/migrations/0001_squashed.py", "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.10495305061340332}, {"fpath": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/views.py", "rule_id": "python.django.security.injection.path-traversal.path-traversal-open.path-traversal-open", "time": 0.10589194297790527}, {"fpath": "downloaded_repos/mangadventure_MangAdventure/reader/models.py", "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.11835289001464844}, {"fpath": "downloaded_repos/mangadventure_MangAdventure/reader/admin.py", "rule_id": "python.lang.security.dangerous-system-call.dangerous-system-call", "time": 0.15403509140014648}, {"fpath": "downloaded_repos/mangadventure_MangAdventure/users/views.py", "rule_id": "python.django.security.injection.open-redirect.open-redirect", "time": 0.18694686889648438}, {"fpath": "downloaded_repos/mangadventure_MangAdventure/reader/views.py", "rule_id": "python.django.security.injection.open-redirect.open-redirect", "time": 0.29877591133117676}, {"fpath": "downloaded_repos/mangadventure_MangAdventure/users/views.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.32346105575561523}, {"fpath": "downloaded_repos/mangadventure_MangAdventure/MangAdventure/settings.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.3989400863647461}]}, "tainting_time": {"total_time": 2.117579936981201, "per_def_and_rule_time": {"mean": 0.0008018098966229465, "std_dev": 1.5863654084824498e-05}, "very_slow_stats": {"time_ratio": 0.08217919077877911, "count_ratio": 0.0003786444528587656}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/mangadventure_MangAdventure/config/management/commands/fs2import.py", "fline": 46, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.17402100563049316}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}