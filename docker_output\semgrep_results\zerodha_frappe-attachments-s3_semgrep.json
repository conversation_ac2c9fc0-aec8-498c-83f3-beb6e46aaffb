{"version": "1.130.0", "results": [], "errors": [], "paths": {"scanned": ["downloaded_repos/zerodha_frappe-attachments-s3/.editorconfig", "downloaded_repos/zerodha_frappe-attachments-s3/.github/issue_template.md", "downloaded_repos/zerodha_frappe-attachments-s3/.gitignore", "downloaded_repos/zerodha_frappe-attachments-s3/MANIFEST.in", "downloaded_repos/zerodha_frappe-attachments-s3/README.md", "downloaded_repos/zerodha_frappe-attachments-s3/frappe_s3_attachment/__init__.py", "downloaded_repos/zerodha_frappe-attachments-s3/frappe_s3_attachment/config/__init__.py", "downloaded_repos/zerodha_frappe-attachments-s3/frappe_s3_attachment/config/desktop.py", "downloaded_repos/zerodha_frappe-attachments-s3/frappe_s3_attachment/config/docs.py", "downloaded_repos/zerodha_frappe-attachments-s3/frappe_s3_attachment/controller.py", "downloaded_repos/zerodha_frappe-attachments-s3/frappe_s3_attachment/frappe_s3_attachment/__init__.py", "downloaded_repos/zerodha_frappe-attachments-s3/frappe_s3_attachment/frappe_s3_attachment/doctype/__init__.py", "downloaded_repos/zerodha_frappe-attachments-s3/frappe_s3_attachment/frappe_s3_attachment/doctype/s3_file_attachment/__init__.py", "downloaded_repos/zerodha_frappe-attachments-s3/frappe_s3_attachment/frappe_s3_attachment/doctype/s3_file_attachment/s3_file_attachment.js", "downloaded_repos/zerodha_frappe-attachments-s3/frappe_s3_attachment/frappe_s3_attachment/doctype/s3_file_attachment/s3_file_attachment.json", "downloaded_repos/zerodha_frappe-attachments-s3/frappe_s3_attachment/frappe_s3_attachment/doctype/s3_file_attachment/s3_file_attachment.py", "downloaded_repos/zerodha_frappe-attachments-s3/frappe_s3_attachment/frappe_s3_attachment/doctype/s3_file_attachment/test_s3_file_attachment.js", "downloaded_repos/zerodha_frappe-attachments-s3/frappe_s3_attachment/frappe_s3_attachment/doctype/s3_file_attachment/test_s3_file_attachment.py", "downloaded_repos/zerodha_frappe-attachments-s3/frappe_s3_attachment/hooks.py", "downloaded_repos/zerodha_frappe-attachments-s3/frappe_s3_attachment/modules.txt", "downloaded_repos/zerodha_frappe-attachments-s3/frappe_s3_attachment/patches.txt", "downloaded_repos/zerodha_frappe-attachments-s3/frappe_s3_attachment/templates/__init__.py", "downloaded_repos/zerodha_frappe-attachments-s3/frappe_s3_attachment/templates/pages/__init__.py", "downloaded_repos/zerodha_frappe-attachments-s3/license.txt", "downloaded_repos/zerodha_frappe-attachments-s3/requirements.txt", "downloaded_repos/zerodha_frappe-attachments-s3/setup.py", "downloaded_repos/zerodha_frappe-attachments-s3/tox.ini"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.7166380882263184, "profiling_times": {"config_time": 6.043984413146973, "core_time": 2.649722099304199, "ignores_time": 0.0018968582153320312, "total_time": 8.696433305740356}, "parsing_time": {"total_time": 0.3011970520019531, "per_file_time": {"mean": 0.017717473647173715, "std_dev": 7.843302429510038e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.47348952293396, "per_file_time": {"mean": 0.020753373562450137, "std_dev": 0.0013059908092424826}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.14838051795959473, "per_file_and_rule_time": {"mean": 0.0021504422892694892, "std_dev": 1.1808305272534906e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.0125274658203125, "per_def_and_rule_time": {"mean": 0.0003796201763731061, "std_dev": 1.390092373567831e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}