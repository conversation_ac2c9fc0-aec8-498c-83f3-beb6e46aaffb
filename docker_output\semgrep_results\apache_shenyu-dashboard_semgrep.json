{"version": "1.130.0", "results": [{"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/Common/CommonRuleHandle.js", "start": {"line": 105, "col": 36, "offset": 3780}, "end": {"line": 105, "col": 51, "offset": 3795}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/Common/Selector.js", "start": {"line": 785, "col": 40, "offset": 24890}, "end": {"line": 785, "col": 55, "offset": 24905}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/Common/Selector.js", "start": {"line": 1556, "col": 40, "offset": 50891}, "end": {"line": 1556, "col": 55, "offset": 50906}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/Discovery/ProxySelectorModal.js", "start": {"line": 394, "col": 44, "offset": 12384}, "end": {"line": 394, "col": 59, "offset": 12399}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/Discovery/ProxySelectorModal.js", "start": {"line": 627, "col": 44, "offset": 23078}, "end": {"line": 627, "col": 59, "offset": 23093}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.insecure-object-assign.insecure-object-assign", "path": "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/PluginRuleHandle/ParamPluginRuleHandle.js", "start": {"line": 60, "col": 9, "offset": 1835}, "end": {"line": 60, "col": 47, "offset": 1873}, "extra": {"message": "Depending on the context, user control data in `Object.assign` can cause web response to include data that it should not have or can lead to a mass assignment vulnerability.", "metadata": {"cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "owasp": ["A01:2021 - Broken Access Control"], "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Mass_Assignment_Cheat_Sheet.html", "https://en.wikipedia.org/wiki/Mass_assignment_vulnerability"], "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/javascript.lang.security.insecure-object-assign.insecure-object-assign", "shortlink": "https://sg.run/2R0D"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.insecure-object-assign.insecure-object-assign", "path": "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/PluginRuleHandle/ParamPluginRuleHandle.js", "start": {"line": 102, "col": 9, "offset": 3076}, "end": {"line": 102, "col": 47, "offset": 3114}, "extra": {"message": "Depending on the context, user control data in `Object.assign` can cause web response to include data that it should not have or can lead to a mass assignment vulnerability.", "metadata": {"cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "owasp": ["A01:2021 - Broken Access Control"], "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Mass_Assignment_Cheat_Sheet.html", "https://en.wikipedia.org/wiki/Mass_assignment_vulnerability"], "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/javascript.lang.security.insecure-object-assign.insecure-object-assign", "shortlink": "https://sg.run/2R0D"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.insecure-object-assign.insecure-object-assign", "path": "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/PluginRuleHandle/ResponseRuleHandle.js", "start": {"line": 80, "col": 9, "offset": 2181}, "end": {"line": 80, "col": 47, "offset": 2219}, "extra": {"message": "Depending on the context, user control data in `Object.assign` can cause web response to include data that it should not have or can lead to a mass assignment vulnerability.", "metadata": {"cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "owasp": ["A01:2021 - Broken Access Control"], "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Mass_Assignment_Cheat_Sheet.html", "https://en.wikipedia.org/wiki/Mass_assignment_vulnerability"], "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/javascript.lang.security.insecure-object-assign.insecure-object-assign", "shortlink": "https://sg.run/2R0D"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.insecure-object-assign.insecure-object-assign", "path": "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/PluginRuleHandle/ResponseRuleHandle.js", "start": {"line": 137, "col": 9, "offset": 3895}, "end": {"line": 137, "col": 47, "offset": 3933}, "extra": {"message": "Depending on the context, user control data in `Object.assign` can cause web response to include data that it should not have or can lead to a mass assignment vulnerability.", "metadata": {"cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "owasp": ["A01:2021 - Broken Access Control"], "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Mass_Assignment_Cheat_Sheet.html", "https://en.wikipedia.org/wiki/Mass_assignment_vulnerability"], "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/javascript.lang.security.insecure-object-assign.insecure-object-assign", "shortlink": "https://sg.run/2R0D"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/apache_shenyu-dashboard/src/routes/System/NamespacePlugin/AddModal.js", "start": {"line": 183, "col": 30, "offset": 5771}, "end": {"line": 183, "col": 45, "offset": 5786}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/apache_shenyu-dashboard/src/routes/System/Plugin/AddModal.js", "start": {"line": 176, "col": 30, "offset": 5220}, "end": {"line": 176, "col": 45, "offset": 5235}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/apache_shenyu-dashboard/.gitpod.Dockerfile", "start": {"line": 6, "col": 5, "offset": 0}, "end": {"line": 7, "col": 32, "offset": 64}}]], "message": "Syntax error at line downloaded_repos/apache_shenyu-dashboard/.gitpod.Dockerfile:6:\n `sdk install java 17.0.3-ms && \\\r\n    sdk default java 17.0.3-ms\"` was unexpected", "path": "downloaded_repos/apache_shenyu-dashboard/.gitpod.Dockerfile", "spans": [{"file": "downloaded_repos/apache_shenyu-dashboard/.gitpod.Dockerfile", "start": {"line": 6, "col": 5, "offset": 0}, "end": {"line": 7, "col": 32, "offset": 64}}]}], "paths": {"scanned": ["downloaded_repos/apache_shenyu-dashboard/.asf.yaml", "downloaded_repos/apache_shenyu-dashboard/.babelrc.js", "downloaded_repos/apache_shenyu-dashboard/.devcontainer/devcontainer.json", "downloaded_repos/apache_shenyu-dashboard/.editorconfig", "downloaded_repos/apache_shenyu-dashboard/.eslintrc.js", "downloaded_repos/apache_shenyu-dashboard/.github/release-drafter-config.yml", "downloaded_repos/apache_shenyu-dashboard/.github/workflows/build.yml", "downloaded_repos/apache_shenyu-dashboard/.github/workflows/deploy.yml", "downloaded_repos/apache_shenyu-dashboard/.github/workflows/release-drafter.yml", "downloaded_repos/apache_shenyu-dashboard/.gitignore", "downloaded_repos/apache_shenyu-dashboard/.gitpod.Dockerfile", "downloaded_repos/apache_shenyu-dashboard/.gitpod.yml", "downloaded_repos/apache_shenyu-dashboard/.roadhogrc.mock.js", "downloaded_repos/apache_shenyu-dashboard/.stylelintrc.json", "downloaded_repos/apache_shenyu-dashboard/.webpackrc.js", "downloaded_repos/apache_shenyu-dashboard/DISCLAIMER", "downloaded_repos/apache_shenyu-dashboard/LICENSE", "downloaded_repos/apache_shenyu-dashboard/NOTICE", "downloaded_repos/apache_shenyu-dashboard/README.md", "downloaded_repos/apache_shenyu-dashboard/jsconfig.json", "downloaded_repos/apache_shenyu-dashboard/package.json", "downloaded_repos/apache_shenyu-dashboard/public/favicon.ico", "downloaded_repos/apache_shenyu-dashboard/src/assets/TitleLogo.svg", "downloaded_repos/apache_shenyu-dashboard/src/assets/asf_logo.svg", "downloaded_repos/apache_shenyu-dashboard/src/assets/bg.jpg", "downloaded_repos/apache_shenyu-dashboard/src/assets/logo.png", "downloaded_repos/apache_shenyu-dashboard/src/assets/logo.svg", "downloaded_repos/apache_shenyu-dashboard/src/assets/support-apache.png", "downloaded_repos/apache_shenyu-dashboard/src/assets/welcome.jpg", "downloaded_repos/apache_shenyu-dashboard/src/common/menu.js", "downloaded_repos/apache_shenyu-dashboard/src/common/router.js", "downloaded_repos/apache_shenyu-dashboard/src/components/Authorized/Authorized.js", "downloaded_repos/apache_shenyu-dashboard/src/components/Authorized/AuthorizedRoute.js", "downloaded_repos/apache_shenyu-dashboard/src/components/Authorized/CheckPermissions.js", "downloaded_repos/apache_shenyu-dashboard/src/components/Authorized/CheckPermissions.test.js", "downloaded_repos/apache_shenyu-dashboard/src/components/Authorized/PromiseRender.js", "downloaded_repos/apache_shenyu-dashboard/src/components/Authorized/Secured.js", "downloaded_repos/apache_shenyu-dashboard/src/components/Authorized/demo/AuthorizedArray.md", "downloaded_repos/apache_shenyu-dashboard/src/components/Authorized/demo/AuthorizedFunction.md", "downloaded_repos/apache_shenyu-dashboard/src/components/Authorized/demo/basic.md", "downloaded_repos/apache_shenyu-dashboard/src/components/Authorized/demo/secured.md", "downloaded_repos/apache_shenyu-dashboard/src/components/Authorized/index.d.ts", "downloaded_repos/apache_shenyu-dashboard/src/components/Authorized/index.js", "downloaded_repos/apache_shenyu-dashboard/src/components/Authorized/index.md", "downloaded_repos/apache_shenyu-dashboard/src/components/Authorized/renderAuthorize.js", "downloaded_repos/apache_shenyu-dashboard/src/components/CountDown/demo/simple.md", "downloaded_repos/apache_shenyu-dashboard/src/components/CountDown/index.d.ts", "downloaded_repos/apache_shenyu-dashboard/src/components/CountDown/index.en-US.md", "downloaded_repos/apache_shenyu-dashboard/src/components/CountDown/index.js", "downloaded_repos/apache_shenyu-dashboard/src/components/CountDown/index.zh-CN.md", "downloaded_repos/apache_shenyu-dashboard/src/components/Exception/demo/403.md", "downloaded_repos/apache_shenyu-dashboard/src/components/Exception/demo/404.md", "downloaded_repos/apache_shenyu-dashboard/src/components/Exception/demo/500.md", "downloaded_repos/apache_shenyu-dashboard/src/components/Exception/index.d.ts", "downloaded_repos/apache_shenyu-dashboard/src/components/Exception/index.en-US.md", "downloaded_repos/apache_shenyu-dashboard/src/components/Exception/index.js", "downloaded_repos/apache_shenyu-dashboard/src/components/Exception/index.less", "downloaded_repos/apache_shenyu-dashboard/src/components/Exception/index.zh-CN.md", "downloaded_repos/apache_shenyu-dashboard/src/components/Exception/typeConfig.js", "downloaded_repos/apache_shenyu-dashboard/src/components/GlobalFooter/demo/basic.md", "downloaded_repos/apache_shenyu-dashboard/src/components/GlobalFooter/index.d.ts", "downloaded_repos/apache_shenyu-dashboard/src/components/GlobalFooter/index.js", "downloaded_repos/apache_shenyu-dashboard/src/components/GlobalFooter/index.less", "downloaded_repos/apache_shenyu-dashboard/src/components/GlobalFooter/index.md", "downloaded_repos/apache_shenyu-dashboard/src/components/GlobalHeader/ExportModal.js", "downloaded_repos/apache_shenyu-dashboard/src/components/GlobalHeader/ImportModal.js", "downloaded_repos/apache_shenyu-dashboard/src/components/GlobalHeader/ImportResultModal.js", "downloaded_repos/apache_shenyu-dashboard/src/components/GlobalHeader/index.js", "downloaded_repos/apache_shenyu-dashboard/src/components/GlobalHeader/index.less", "downloaded_repos/apache_shenyu-dashboard/src/components/Login/LoginCode.tsx", "downloaded_repos/apache_shenyu-dashboard/src/components/Login/LoginItem.js", "downloaded_repos/apache_shenyu-dashboard/src/components/Login/LoginSubmit.js", "downloaded_repos/apache_shenyu-dashboard/src/components/Login/LoginTab.js", "downloaded_repos/apache_shenyu-dashboard/src/components/Login/demo/basic.md", "downloaded_repos/apache_shenyu-dashboard/src/components/Login/index.d.ts", "downloaded_repos/apache_shenyu-dashboard/src/components/Login/index.en-US.md", "downloaded_repos/apache_shenyu-dashboard/src/components/Login/index.js", "downloaded_repos/apache_shenyu-dashboard/src/components/Login/index.less", "downloaded_repos/apache_shenyu-dashboard/src/components/Login/index.zh-CN.md", "downloaded_repos/apache_shenyu-dashboard/src/components/Login/map.js", "downloaded_repos/apache_shenyu-dashboard/src/components/PageHeader/demo/image.md", "downloaded_repos/apache_shenyu-dashboard/src/components/PageHeader/demo/simple.md", "downloaded_repos/apache_shenyu-dashboard/src/components/PageHeader/demo/standard.md", "downloaded_repos/apache_shenyu-dashboard/src/components/PageHeader/demo/structure.md", "downloaded_repos/apache_shenyu-dashboard/src/components/PageHeader/index.d.ts", "downloaded_repos/apache_shenyu-dashboard/src/components/PageHeader/index.js", "downloaded_repos/apache_shenyu-dashboard/src/components/PageHeader/index.less", "downloaded_repos/apache_shenyu-dashboard/src/components/PageHeader/index.md", "downloaded_repos/apache_shenyu-dashboard/src/components/PageHeader/index.test.js", "downloaded_repos/apache_shenyu-dashboard/src/components/Result/demo/classic.md", "downloaded_repos/apache_shenyu-dashboard/src/components/Result/demo/error.md", "downloaded_repos/apache_shenyu-dashboard/src/components/Result/demo/structure.md", "downloaded_repos/apache_shenyu-dashboard/src/components/Result/index.d.ts", "downloaded_repos/apache_shenyu-dashboard/src/components/Result/index.js", "downloaded_repos/apache_shenyu-dashboard/src/components/Result/index.less", "downloaded_repos/apache_shenyu-dashboard/src/components/Result/index.md", "downloaded_repos/apache_shenyu-dashboard/src/components/SiderMenu/SiderMenu.js", "downloaded_repos/apache_shenyu-dashboard/src/components/SiderMenu/SilderMenu.test.js", "downloaded_repos/apache_shenyu-dashboard/src/components/SiderMenu/index.js", "downloaded_repos/apache_shenyu-dashboard/src/components/SiderMenu/index.less", "downloaded_repos/apache_shenyu-dashboard/src/components/_utils/pathTools.js", "downloaded_repos/apache_shenyu-dashboard/src/components/_utils/pathTools.test.js", "downloaded_repos/apache_shenyu-dashboard/src/components/_utils/utils.ts", "downloaded_repos/apache_shenyu-dashboard/src/components/tsconfig.json", "downloaded_repos/apache_shenyu-dashboard/src/e2e/home.e2e.js", "downloaded_repos/apache_shenyu-dashboard/src/e2e/login.e2e.js", "downloaded_repos/apache_shenyu-dashboard/src/index.ejs", "downloaded_repos/apache_shenyu-dashboard/src/index.js", "downloaded_repos/apache_shenyu-dashboard/src/index.less", "downloaded_repos/apache_shenyu-dashboard/src/layouts/BasicLayout.js", "downloaded_repos/apache_shenyu-dashboard/src/layouts/BlankLayout.js", "downloaded_repos/apache_shenyu-dashboard/src/layouts/PageHeaderLayout.js", "downloaded_repos/apache_shenyu-dashboard/src/layouts/PageHeaderLayout.less", "downloaded_repos/apache_shenyu-dashboard/src/layouts/UserLayout.js", "downloaded_repos/apache_shenyu-dashboard/src/layouts/UserLayout.less", "downloaded_repos/apache_shenyu-dashboard/src/locales/en-US.json", "downloaded_repos/apache_shenyu-dashboard/src/locales/zh-CN.json", "downloaded_repos/apache_shenyu-dashboard/src/models/alert.js", "downloaded_repos/apache_shenyu-dashboard/src/models/auth.js", "downloaded_repos/apache_shenyu-dashboard/src/models/common.js", "downloaded_repos/apache_shenyu-dashboard/src/models/dataPermission.js", "downloaded_repos/apache_shenyu-dashboard/src/models/discovery.js", "downloaded_repos/apache_shenyu-dashboard/src/models/error.js", "downloaded_repos/apache_shenyu-dashboard/src/models/global.js", "downloaded_repos/apache_shenyu-dashboard/src/models/index.js", "downloaded_repos/apache_shenyu-dashboard/src/models/instance.js", "downloaded_repos/apache_shenyu-dashboard/src/models/login.js", "downloaded_repos/apache_shenyu-dashboard/src/models/manage.js", "downloaded_repos/apache_shenyu-dashboard/src/models/mcpServer.js", "downloaded_repos/apache_shenyu-dashboard/src/models/metadata.js", "downloaded_repos/apache_shenyu-dashboard/src/models/namespace.js", "downloaded_repos/apache_shenyu-dashboard/src/models/namespacePlugin.js", "downloaded_repos/apache_shenyu-dashboard/src/models/plugin.js", "downloaded_repos/apache_shenyu-dashboard/src/models/pluginHandle.js", "downloaded_repos/apache_shenyu-dashboard/src/models/resource.js", "downloaded_repos/apache_shenyu-dashboard/src/models/rewrite.js", "downloaded_repos/apache_shenyu-dashboard/src/models/role.js", "downloaded_repos/apache_shenyu-dashboard/src/models/scale.js", "downloaded_repos/apache_shenyu-dashboard/src/models/shenyuDict.js", "downloaded_repos/apache_shenyu-dashboard/src/models/spring.js", "downloaded_repos/apache_shenyu-dashboard/src/models/user.js", "downloaded_repos/apache_shenyu-dashboard/src/polyfill.js", "downloaded_repos/apache_shenyu-dashboard/src/rollbar.js", "downloaded_repos/apache_shenyu-dashboard/src/router.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Document/ApiDoc.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Document/components/AddAndUpdateApiDoc.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Document/components/AddAndUpdateTag.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Document/components/ApiContext.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Document/components/ApiDebug.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Document/components/ApiInfo.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Document/components/HeadersEditor.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Document/components/ImportSwaggerModal.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Document/components/SearchApi.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Document/components/TagInfo.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Document/components/globalData.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Exception/403.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Exception/404.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Exception/500.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Exception/style.less", "downloaded_repos/apache_shenyu-dashboard/src/routes/Exception/triggerException.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Home/home.less", "downloaded_repos/apache_shenyu-dashboard/src/routes/Home/index.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/Common/CommonRuleHandle.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/Common/ComposeRuleHandle.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/Common/Rule.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/Common/RuleCopy.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/Common/Selector.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/Common/SelectorCopy.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/Common/index.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/Discovery/DiscoveryCard.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/Discovery/DiscoveryConfigModal.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/Discovery/DiscoveryIcon.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/Discovery/DiscoveryImportModal.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/Discovery/DiscoveryUpstreamTable.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/Discovery/ProxySelectorCopy.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/Discovery/ProxySelectorModal.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/Discovery/discovery.less", "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/Discovery/index.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/McpServer/JsonEditModal.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/McpServer/McpConfigModal.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/McpServer/ToolsModal.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/McpServer/index.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/PluginRuleHandle/GeneralContextRuleHandle.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/PluginRuleHandle/HystrixRuleHandle.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/PluginRuleHandle/ParamPluginRuleHandle.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/PluginRuleHandle/RequestRuleHandle.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/PluginRuleHandle/ResponseRuleHandle.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/PluginRuleHandle/index.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/index.less", "downloaded_repos/apache_shenyu-dashboard/src/routes/Result/Error.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Result/Success.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/Result/Success.test.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/Alert/AddModal.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/Alert/globalData.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/Alert/index.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/AppAuth/AddModal.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/AppAuth/AddTable.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/AppAuth/RelateMetadata.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/AppAuth/SearchContent.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/AppAuth/TableTransfer.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/AppAuth/index.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/AppAuth/index.less", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/Dict/AddModal.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/Dict/index.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/Instance/index.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/Metadata/AddModal.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/Metadata/index.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/Namespace/AddModal.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/Namespace/index.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/NamespacePlugin/AddModal.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/NamespacePlugin/index.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/Plugin/AddModal.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/Plugin/index.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/PluginHandle/AddModal.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/PluginHandle/index.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/Resource/AddModal.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/Resource/IconModal.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/Resource/IconModal.less", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/Resource/index.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/Resource/index.less", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/Role/AddModal.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/Role/index.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/Scale/PolicyModal.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/Scale/RuleModal.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/Scale/globalData.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/Scale/index.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/User/AddModal.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/User/DataPermModal.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/System/User/index.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/User/Login.js", "downloaded_repos/apache_shenyu-dashboard/src/routes/User/Login.less", "downloaded_repos/apache_shenyu-dashboard/src/services/api.js", "downloaded_repos/apache_shenyu-dashboard/src/services/error.js", "downloaded_repos/apache_shenyu-dashboard/src/services/user.js", "downloaded_repos/apache_shenyu-dashboard/src/theme.js", "downloaded_repos/apache_shenyu-dashboard/src/utils/AuthButton.js", "downloaded_repos/apache_shenyu-dashboard/src/utils/AuthRoute.js", "downloaded_repos/apache_shenyu-dashboard/src/utils/IntlUtils.js", "downloaded_repos/apache_shenyu-dashboard/src/utils/download.js", "downloaded_repos/apache_shenyu-dashboard/src/utils/emit.js", "downloaded_repos/apache_shenyu-dashboard/src/utils/locales.js", "downloaded_repos/apache_shenyu-dashboard/src/utils/namespacePlugin.js", "downloaded_repos/apache_shenyu-dashboard/src/utils/plugin.js", "downloaded_repos/apache_shenyu-dashboard/src/utils/request.js", "downloaded_repos/apache_shenyu-dashboard/src/utils/resizable.js", "downloaded_repos/apache_shenyu-dashboard/src/utils/utils.js", "downloaded_repos/apache_shenyu-dashboard/src/utils/utils.less", "downloaded_repos/apache_shenyu-dashboard/tsconfig.json"], "skipped": [{"path": "downloaded_repos/apache_shenyu-dashboard/.gitpod.Dockerfile", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/apache_shenyu-dashboard/tests/fix_puppeteer.sh", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/apache_shenyu-dashboard/tests/run-tests.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.7545619010925293, "profiling_times": {"config_time": 5.976848602294922, "core_time": 8.928152322769165, "ignores_time": 0.0017910003662109375, "total_time": 14.907641172409058}, "parsing_time": {"total_time": 8.600291967391968, "per_file_time": {"mean": 0.045990866135785936, "std_dev": 0.0035120578376610675}, "very_slow_stats": {"time_ratio": 0.05827012844974774, "count_ratio": 0.0053475935828877}, "very_slow_files": [{"fpath": "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/Common/Selector.js", "ftime": 0.5011401176452637}]}, "scanning_time": {"total_time": 56.15360736846924, "per_file_time": {"mean": 0.08221611620566505, "std_dev": 0.09732252198054947}, "very_slow_stats": {"time_ratio": 0.3013859699998259, "count_ratio": 0.010248901903367497}, "very_slow_files": [{"fpath": "downloaded_repos/apache_shenyu-dashboard/src/services/api.js", "ftime": 1.5120630264282227}, {"fpath": "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/McpServer/ToolsModal.js", "ftime": 1.7192068099975586}, {"fpath": "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/Common/index.js", "ftime": 1.8637847900390625}, {"fpath": "downloaded_repos/apache_shenyu-dashboard/src/routes/Document/components/SearchApi.js", "ftime": 2.0261659622192383}, {"fpath": "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/Common/Selector.js", "ftime": 2.3499019145965576}, {"fpath": "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/McpServer/index.js", "ftime": 2.407320976257324}, {"fpath": "downloaded_repos/apache_shenyu-dashboard/src/routes/Document/components/ApiDebug.js", "ftime": 5.04546594619751}]}, "matching_time": {"total_time": 26.62812638282776, "per_file_and_rule_time": {"mean": 0.01683193829508708, "std_dev": 0.0012604707677552481}, "very_slow_stats": {"time_ratio": 0.28222839050217163, "count_ratio": 0.025284450063211124}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/Common/Selector.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.27120089530944824}, {"fpath": "downloaded_repos/apache_shenyu-dashboard/src/routes/Document/components/SearchApi.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.2724640369415283}, {"fpath": "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/Common/Selector.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.27832603454589844}, {"fpath": "downloaded_repos/apache_shenyu-dashboard/src/routes/Document/components/ApiDebug.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.28013014793395996}, {"fpath": "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/McpServer/index.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.2821221351623535}, {"fpath": "downloaded_repos/apache_shenyu-dashboard/src/utils/utils.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.3024880886077881}, {"fpath": "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/McpServer/JsonEditModal.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.3038060665130615}, {"fpath": "downloaded_repos/apache_shenyu-dashboard/src/routes/Document/components/SearchApi.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.3252270221710205}, {"fpath": "downloaded_repos/apache_shenyu-dashboard/src/models/rewrite.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.4109499454498291}, {"fpath": "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/McpServer/index.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.4450230598449707}]}, "tainting_time": {"total_time": 9.638403415679932, "per_def_and_rule_time": {"mean": 0.0021253370266107892, "std_dev": 0.000324130634894043}, "very_slow_stats": {"time_ratio": 0.252997187926284, "count_ratio": 0.004189636163175303}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/apache_shenyu-dashboard/src/components/GlobalHeader/index.js", "fline": 262, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.07012200355529785}, {"fpath": "downloaded_repos/apache_shenyu-dashboard/src/routes/Document/components/SearchApi.js", "fline": 1, "rule_id": "javascript.express.security.cors-misconfiguration.cors-misconfiguration", "time": 0.07013797760009766}, {"fpath": "downloaded_repos/apache_shenyu-dashboard/src/routes/Document/components/SearchApi.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.0729830265045166}, {"fpath": "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/McpServer/index.js", "fline": 1008, "rule_id": "javascript.express.security.require-request.require-request", "time": 0.07536911964416504}, {"fpath": "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/Common/index.js", "fline": 919, "rule_id": "javascript.express.security.require-request.require-request", "time": 0.07880783081054688}, {"fpath": "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/McpServer/ToolsModal.js", "fline": 431, "rule_id": "javascript.express.security.audit.res-render-injection.res-render-injection", "time": 0.08000898361206055}, {"fpath": "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/McpServer/ToolsModal.js", "fline": 431, "rule_id": "javascript.express.security.require-request.require-request", "time": 0.08051300048828125}, {"fpath": "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/Common/index.js", "fline": 919, "rule_id": "javascript.express.security.audit.res-render-injection.res-render-injection", "time": 0.09403491020202637}, {"fpath": "downloaded_repos/apache_shenyu-dashboard/src/routes/Plugin/Discovery/DiscoveryUpstreamTable.js", "fline": 279, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.16204214096069336}, {"fpath": "downloaded_repos/apache_shenyu-dashboard/src/routes/Document/components/ApiDebug.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 1.1357390880584717}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089476480}, "engine_requested": "OSS", "skipped_rules": []}