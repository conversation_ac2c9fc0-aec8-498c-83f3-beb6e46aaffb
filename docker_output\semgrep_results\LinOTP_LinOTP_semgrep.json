{"version": "1.130.0", "results": [{"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/LinOTP_LinOTP/linotp/app.py", "start": {"line": 696, "col": 15, "offset": 25604}, "end": {"line": 696, "col": 77, "offset": 25666}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.insecure-hash-algorithms.insecure-hash-algorithm-sha1", "path": "downloaded_repos/LinOTP_LinOTP/linotp/cli/init_cmd.py", "start": {"line": 206, "col": 9, "offset": 6747}, "end": {"line": 206, "col": 23, "offset": 6761}, "extra": {"message": "Detected SHA1 hash algorithm which is considered insecure. SHA1 is not collision resistant and is therefore not suitable as a cryptographic signature. Use SHA256 or SHA3 instead.", "fix": "hashlib.sha256()", "metadata": {"source-rule-url": "https://github.com/PyCQA/bandit/blob/d5f8fa0d89d7b11442fc6ec80ca42953974354c8/bandit/blacklists/calls.py#L59", "cwe": ["CWE-327: Use of a Broken or Risky Cryptographic Algorithm"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "bandit-code": "B303", "asvs": {"control_id": "6.2.2 Insecure Custom Algorithm", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x14-V6-Cryptography.md#v62-algorithms", "section": "V6 Stored Cryptography Verification Requirements", "version": "4"}, "references": ["https://www.schneier.com/blog/archives/2012/10/when_will_we_se.html", "https://www.trendmicro.com/vinfo/us/security/news/vulnerabilities-and-exploits/sha-1-collision-signals-the-end-of-the-algorithm-s-viability", "http://2012.sharcs.org/slides/stevens.pdf", "https://pycryptodome.readthedocs.io/en/latest/src/hash/sha3_256.html"], "category": "security", "technology": ["python"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/python.lang.security.insecure-hash-algorithms.insecure-hash-algorithm-sha1", "shortlink": "https://sg.run/ydYx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/admin.py", "start": {"line": 284, "col": 17, "offset": 9717}, "end": {"line": 284, "col": 74, "offset": 9774}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"Only tokens in realm %s will be shown\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/admin.py", "start": {"line": 469, "col": 13, "offset": 15343}, "end": {"line": 474, "col": 14, "offset": 15518}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[enable] enable token with serial %s for user %s@%s.\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/admin.py", "start": {"line": 626, "col": 13, "offset": 20789}, "end": {"line": 631, "col": 14, "offset": 20966}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[disable] disable token with serial %s for user %s@%s.\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/admin.py", "start": {"line": 923, "col": 13, "offset": 34544}, "end": {"line": 927, "col": 14, "offset": 34684}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[init] initialize token. user: %s, serial: %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/admin.py", "start": {"line": 1189, "col": 13, "offset": 43504}, "end": {"line": 1189, "col": 82, "offset": 43573}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[setPin] setting userPin for token with serial %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/admin.py", "start": {"line": 1968, "col": 13, "offset": 73036}, "end": {"line": 1972, "col": 14, "offset": 73173}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[tokenrealm] setting realms for token %s to %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/admin.py", "start": {"line": 2026, "col": 13, "offset": 74839}, "end": {"line": 2029, "col": 14, "offset": 74964}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[reset] resetting the FailCounter for token with serial %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/admin.py", "start": {"line": 2110, "col": 13, "offset": 77730}, "end": {"line": 2114, "col": 14, "offset": 77881}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[copyTokenPin] copying Pin from token %s to token %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/admin.py", "start": {"line": 2189, "col": 13, "offset": 80331}, "end": {"line": 2193, "col": 14, "offset": 80484}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[copyTokenUser] copying User from token %s to token %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/admin.py", "start": {"line": 2320, "col": 9, "offset": 84796}, "end": {"line": 2323, "col": 10, "offset": 84913}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[loadtokens] importing linotp.lib. Known import types: %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/admin.py", "start": {"line": 2368, "col": 13, "offset": 86381}, "end": {"line": 2372, "col": 14, "offset": 86542}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[loadtokens] loading token file to server Filetype: %s. File: %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/admin.py", "start": {"line": 2380, "col": 17, "offset": 86864}, "end": {"line": 2380, "col": 76, "offset": 86923}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[loadtokens] Field storage file: %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/admin.py", "start": {"line": 2386, "col": 13, "offset": 87144}, "end": {"line": 2386, "col": 65, "offset": 87196}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[loadtokens] fileString: %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/admin.py", "start": {"line": 2389, "col": 17, "offset": 87264}, "end": {"line": 2389, "col": 75, "offset": 87322}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[loadtokens] Field storage type: %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/admin.py", "start": {"line": 2393, "col": 13, "offset": 87436}, "end": {"line": 2393, "col": 69, "offset": 87492}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[loadtokens] typeString: <<%s>>\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/admin.py", "start": {"line": 2395, "col": 17, "offset": 87546}, "end": {"line": 2400, "col": 18, "offset": 87766}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[loadtokens] passing password: %s, key: %s, checkserial: %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/admin.py", "start": {"line": 2403, "col": 17, "offset": 87837}, "end": {"line": 2403, "col": 63, "offset": 87883}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[loadtokens] file: %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/admin.py", "start": {"line": 2404, "col": 17, "offset": 87900}, "end": {"line": 2404, "col": 63, "offset": 87946}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[loadtokens] type: %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/admin.py", "start": {"line": 2412, "col": 17, "offset": 88253}, "end": {"line": 2417, "col": 18, "offset": 88470}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[loadtokens] Unknown file type: >>%s<<. \"\n                    \"We only know the types: %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/admin.py", "start": {"line": 2527, "col": 17, "offset": 92369}, "end": {"line": 2527, "col": 68, "offset": 92420}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[loadtokens] importing token %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/gettoken.py", "start": {"line": 128, "col": 13, "offset": 4191}, "end": {"line": 128, "col": 81, "offset": 4259}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[getmultiot<PERSON>] retrieving OTP value for token %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/gettoken.py", "start": {"line": 188, "col": 17, "offset": 6259}, "end": {"line": 188, "col": 80, "offset": 6322}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[getotp] retrieving OTP value for token %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/gettoken.py", "start": {"line": 190, "col": 17, "offset": 6368}, "end": {"line": 194, "col": 18, "offset": 6538}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[getotp] retrieving OTP value for token for user %s@%s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/gettoken.py", "start": {"line": 208, "col": 21, "offset": 7039}, "end": {"line": 213, "col": 22, "offset": 7254}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[getotp] retrieving OTP for token %s for user %s@%s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/gettoken.py", "start": {"line": 215, "col": 21, "offset": 7297}, "end": {"line": 219, "col": 22, "offset": 7467}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[getotp] no token found for user %s@%s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/manage.py", "start": {"line": 355, "col": 13, "offset": 11723}, "end": {"line": 359, "col": 14, "offset": 11893}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[tokenview_flexi] admin >%s< may display the following realms: %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/manage.py", "start": {"line": 360, "col": 13, "offset": 11906}, "end": {"line": 366, "col": 14, "offset": 12104}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[tokenview_flexi] page: %s, filter: %s, sort: %s, dir: %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/manage.py", "start": {"line": 373, "col": 13, "offset": 12238}, "end": {"line": 380, "col": 14, "offset": 12495}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[tokenview_flexi] calling TokenIterator for user=%s@%s, \"\n                \"filter=%s, filterRealm=%s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/manage.py", "start": {"line": 611, "col": 13, "offset": 20046}, "end": {"line": 615, "col": 14, "offset": 20207}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[tokeninfo] admin >%s< may display the following realms: %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/manage.py", "start": {"line": 616, "col": 13, "offset": 20220}, "end": {"line": 616, "col": 74, "offset": 20281}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[tokeninfo] displaying tokens: serial: %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/userservice.py", "start": {"line": 1291, "col": 17, "offset": 41783}, "end": {"line": 1296, "col": 18, "offset": 41999}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[userenable] user %s@%s is enabling his token with serial %s.\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/userservice.py", "start": {"line": 1353, "col": 17, "offset": 43923}, "end": {"line": 1358, "col": 18, "offset": 44127}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"user %s@%s is disabling his token with serial %s.\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/userservice.py", "start": {"line": 1409, "col": 17, "offset": 45743}, "end": {"line": 1414, "col": 18, "offset": 45959}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[userdelete] user %s@%s is deleting his token with serial %s.\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/userservice.py", "start": {"line": 1431, "col": 13, "offset": 46509}, "end": {"line": 1435, "col": 14, "offset": 46650}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[userdelete] deleting token %s of user %s failed!\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/userservice.py", "start": {"line": 1487, "col": 13, "offset": 48230}, "end": {"line": 1487, "col": 77, "offset": 48294}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"error resetting token with serial %s: %r\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/userservice.py", "start": {"line": 1524, "col": 17, "offset": 49395}, "end": {"line": 1529, "col": 18, "offset": 49601}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"user %s@%s is unassigning his token with serial %s.\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/userservice.py", "start": {"line": 1550, "col": 13, "offset": 50223}, "end": {"line": 1550, "col": 87, "offset": 50297}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"unassigning token %s of user %s failed! %r\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/userservice.py", "start": {"line": 1585, "col": 17, "offset": 51362}, "end": {"line": 1590, "col": 18, "offset": 51575}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"user %s@%s is setting the OTP PIN for token with serial %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/userservice.py", "start": {"line": 1595, "col": 21, "offset": 51710}, "end": {"line": 1600, "col": 22, "offset": 51943}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"Setting of OTP PIN for Token %s by user %s failed: %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/userservice.py", "start": {"line": 1653, "col": 17, "offset": 53659}, "end": {"line": 1658, "col": 18, "offset": 53873}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"user %s@%s is setting the mOTP PIN for token with serial %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/userservice.py", "start": {"line": 1713, "col": 17, "offset": 55510}, "end": {"line": 1718, "col": 18, "offset": 55713}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"user %s@%s is resyncing his token with serial %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/userservice.py", "start": {"line": 1733, "col": 13, "offset": 56132}, "end": {"line": 1733, "col": 76, "offset": 56195}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"error resyncing token with serial %s:%r\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/userservice.py", "start": {"line": 1997, "col": 13, "offset": 65560}, "end": {"line": 1997, "col": 79, "offset": 65626}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"error verifying token with serial %s: %r\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/userservice.py", "start": {"line": 2050, "col": 13, "offset": 67436}, "end": {"line": 2055, "col": 14, "offset": 67628}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"user %s@%s is assign the token with serial %s to himself.\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/userservice.py", "start": {"line": 2064, "col": 17, "offset": 67901}, "end": {"line": 2064, "col": 64, "offset": 67948}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"set description of token %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/userservice.py", "start": {"line": 2213, "col": 21, "offset": 73370}, "end": {"line": 2217, "col": 22, "offset": 73555}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"Enrolling Token by user %s failed: %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/userservice.py", "start": {"line": 2290, "col": 13, "offset": 75995}, "end": {"line": 2297, "col": 14, "offset": 76245}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[userinit] initialize a token with serial %s \"\n                \"and type %s by user %s@%s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/userservice.py", "start": {"line": 2299, "col": 13, "offset": 76259}, "end": {"line": 2306, "col": 14, "offset": 76509}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[userinit] Initializing the token serial: %s,\"\n                \" desc: %s, for user %s @ %s.\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/userservice.py", "start": {"line": 2415, "col": 13, "offset": 80549}, "end": {"line": 2415, "col": 85, "offset": 80621}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[usergetmultiotp] retrieving OTP value for token %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/userservice.py", "start": {"line": 2724, "col": 13, "offset": 90487}, "end": {"line": 2729, "col": 14, "offset": 90681}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"user %s@%s is changing description of token with serial %s.\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-etc-shadow.detected-etc-shadow", "path": "downloaded_repos/LinOTP_LinOTP/linotp/def-passwd", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 11, "offset": 10}, "extra": {"message": "linux shadow file detected", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "category": "security", "technology": ["secrets"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-etc-shadow.detected-etc-shadow", "shortlink": "https://sg.run/4ylL"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "path": "downloaded_repos/LinOTP_LinOTP/linotp/flap.py", "start": {"line": 147, "col": 15, "offset": 4634}, "end": {"line": 147, "col": 74, "offset": 4693}, "extra": {"message": "Detected direct use of jinja2. If not done properly, this may bypass HTML escaping which opens up the application to cross-site scripting (XSS) vulnerabilities. Prefer using the Flask method 'render_template()' and templates with a '.html' extension in order to prevent XSS.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://jinja.palletsprojects.com/en/2.11.x/api/#basics"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "shortlink": "https://sg.run/RoKe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/lib/ImportOTP/PSKC.py", "start": {"line": 97, "col": 13, "offset": 2694}, "end": {"line": 102, "col": 14, "offset": 2945}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[aes_decrypt] the key of token %s is a multiple \"\n                \"of blocksize but is padded. This is not compliant \"\n                \"to the specification but we import it anyway.\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/lib/ImportOTP/PSKC.py", "start": {"line": 225, "col": 17, "offset": 7462}, "end": {"line": 232, "col": 18, "offset": 7761}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"calculation encryption key from password [%s], salt: [%s] and \"\n                    \"length: [%s], count: [%s]\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/lib/ImportOTP/eTokenDat.py", "start": {"line": 440, "col": 13, "offset": 12547}, "end": {"line": 444, "col": 14, "offset": 12698}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"Error during token submission. Response was: %r, Content: %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/lib/ImportOTP/safenet.py", "start": {"line": 66, "col": 21, "offset": 2038}, "end": {"line": 70, "col": 22, "offset": 2218}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"The Token with the serial %s has the productname %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/lib/ImportOTP/safenet.py", "start": {"line": 94, "col": 17, "offset": 3174}, "end": {"line": 94, "col": 77, "offset": 3234}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"Found token %s without a element 'Seed'\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/lib/error.py", "start": {"line": 147, "col": 9, "offset": 4380}, "end": {"line": 147, "col": 68, "offset": 4439}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"TokenStateError occurred. Message: %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/LinOTP_LinOTP/linotp/lib/module_loader.py", "start": {"line": 50, "col": 28, "offset": 1466}, "end": {"line": 50, "col": 78, "offset": 1516}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/__init__.py", "start": {"line": 228, "col": 17, "offset": 6523}, "end": {"line": 232, "col": 18, "offset": 6685}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"setting random pin for token with serial %s and user: %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/__init__.py", "start": {"line": 276, "col": 13, "offset": 8029}, "end": {"line": 279, "col": 14, "offset": 8151}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"the maximum tokens for the realm %s is exceeded.\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/__init__.py", "start": {"line": 299, "col": 13, "offset": 8822}, "end": {"line": 303, "col": 14, "offset": 8982}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"the admin >%r< is not allowed to get serial of token %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/__init__.py", "start": {"line": 402, "col": 13, "offset": 12106}, "end": {"line": 406, "col": 14, "offset": 12252}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"setting random pin for token with serial %s and user: %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/__init__.py", "start": {"line": 567, "col": 17, "offset": 17707}, "end": {"line": 567, "col": 83, "offset": 17773}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"the maximum tokens for the realm %s is exceeded.\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/__init__.py", "start": {"line": 581, "col": 13, "offset": 18236}, "end": {"line": 587, "col": 14, "offset": 18461}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"the admin >%s< is not allowed to disable token %s for user %s@%s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/__init__.py", "start": {"line": 746, "col": 17, "offset": 23979}, "end": {"line": 746, "col": 59, "offset": 24021}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"Unknown token type: %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/__init__.py", "start": {"line": 759, "col": 13, "offset": 24609}, "end": {"line": 767, "col": 14, "offset": 24885}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"the admin >%s< is not allowed to enroll token %s of \"\n                \"type %s to user %s@%s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/__init__.py", "start": {"line": 804, "col": 13, "offset": 26190}, "end": {"line": 809, "col": 14, "offset": 26378}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"the admin >%s< is not allowed to enroll token %s of type %s.\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/__init__.py", "start": {"line": 845, "col": 13, "offset": 27607}, "end": {"line": 851, "col": 14, "offset": 27833}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"the admin >%s< is not allowed to unassign token %s for user %s@%s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/__init__.py", "start": {"line": 867, "col": 13, "offset": 28345}, "end": {"line": 871, "col": 14, "offset": 28500}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"the admin >%s< is not allowed to assign token %s. \" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/__init__.py", "start": {"line": 890, "col": 13, "offset": 29160}, "end": {"line": 896, "col": 14, "offset": 29384}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"the admin >%s< is not allowed to assign token %s for user %s@%s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/__init__.py", "start": {"line": 915, "col": 17, "offset": 29976}, "end": {"line": 919, "col": 18, "offset": 30156}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"the admin >%s< is not allowed to set MOTP PIN for token %s.\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/__init__.py", "start": {"line": 986, "col": 13, "offset": 32439}, "end": {"line": 992, "col": 14, "offset": 32663}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"the admin >%s< is not allowed to resync token %s for user %s@%s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/__init__.py", "start": {"line": 1100, "col": 17, "offset": 36387}, "end": {"line": 1104, "col": 18, "offset": 36561}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"the admin >%r< is not allowed to manage tokens in realm %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/__init__.py", "start": {"line": 1205, "col": 13, "offset": 40000}, "end": {"line": 1208, "col": 14, "offset": 40122}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"the maximum tokens for the realm %s is exceeded.\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/__init__.py", "start": {"line": 3056, "col": 9, "offset": 93391}, "end": {"line": 3056, "col": 57, "offset": 93439}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"found matching token type %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/__init__.py", "start": {"line": 3168, "col": 5, "offset": 96249}, "end": {"line": 3168, "col": 58, "offset": 96302}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"found these tokentypes: <%s>\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/__init__.py", "start": {"line": 3172, "col": 9, "offset": 96387}, "end": {"line": 3172, "col": 84, "offset": 96462}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"multiple tokens with serial %s found - cannot get OTP!\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/__init__.py", "start": {"line": 3177, "col": 9, "offset": 96586}, "end": {"line": 3177, "col": 60, "offset": 96637}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"found one token with serial %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/__init__.py", "start": {"line": 3180, "col": 9, "offset": 96693}, "end": {"line": 3180, "col": 69, "offset": 96753}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"got the type %s for token %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/maxtoken.py", "start": {"line": 126, "col": 5, "offset": 3974}, "end": {"line": 130, "col": 6, "offset": 4095}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"getting the already assigned tokens for user %r, realms %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/maxtoken.py", "start": {"line": 186, "col": 5, "offset": 5908}, "end": {"line": 190, "col": 6, "offset": 6029}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"getting the already assigned tokens for user %r, realms %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/lib/token.py", "start": {"line": 144, "col": 17, "offset": 4864}, "end": {"line": 144, "col": 49, "offset": 4896}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[initToken] %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/lib/token.py", "start": {"line": 1931, "col": 5, "offset": 62331}, "end": {"line": 1931, "col": 64, "offset": 62390}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"The token type of serial %s is %r\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/lib/tools/migrate_resolver.py", "start": {"line": 118, "col": 17, "offset": 3762}, "end": {"line": 122, "col": 18, "offset": 3914}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"Faild to set new resolver data for token %s: %r\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/lib/userservice.py", "start": {"line": 69, "col": 5, "offset": 2256}, "end": {"line": 69, "col": 83, "offset": 2334}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[getTokenForUser] ...user %s in realm %s.\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/model/challange.py", "start": {"line": 198, "col": 13, "offset": 5857}, "end": {"line": 202, "col": 14, "offset": 6065}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[checkChallengeSignature] integrity violation for challenge %s, token %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.formatted-sql-query.formatted-sql-query", "path": "downloaded_repos/LinOTP_LinOTP/linotp/model/migrate.py", "start": {"line": 103, "col": 9, "offset": 3172}, "end": {"line": 105, "col": 10, "offset": 3286}, "extra": {"message": "Detected possible formatted SQL query. Use parameterized queries instead.", "metadata": {"owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "references": ["https://stackoverflow.com/questions/775296/mysql-parameterized-queries"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.formatted-sql-query.formatted-sql-query", "shortlink": "https://sg.run/EkWw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.sqlalchemy.security.sqlalchemy-execute-raw-query.sqlalchemy-execute-raw-query", "path": "downloaded_repos/LinOTP_LinOTP/linotp/model/migrate.py", "start": {"line": 103, "col": 9, "offset": 3172}, "end": {"line": 105, "col": 10, "offset": 3286}, "extra": {"message": "Avoiding SQL string concatenation: untrusted input concatenated with raw SQL query can result in SQL Injection. In order to execute raw query safely, prepared statement should be used. SQLAlchemy provides TextualSQL to easily used prepared statement with named parameters. For complex SQL composition, use SQL Expression Language or Schema Definition Language. In most cases, SQLAlchemy ORM will be a better option.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://docs.sqlalchemy.org/en/14/core/tutorial.html#using-textual-sql", "https://www.tutorialspoint.com/sqlalchemy/sqlalchemy_quick_guide.htm", "https://docs.sqlalchemy.org/en/14/core/tutorial.html#using-more-specific-text-with-table-expression-literal-column-and-expression-column"], "category": "security", "technology": ["sqlalchemy"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/python.sqlalchemy.security.sqlalchemy-execute-raw-query.sqlalchemy-execute-raw-query", "shortlink": "https://sg.run/2b1L"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.formatted-sql-query.formatted-sql-query", "path": "downloaded_repos/LinOTP_LinOTP/linotp/model/migrate.py", "start": {"line": 132, "col": 9, "offset": 4034}, "end": {"line": 134, "col": 10, "offset": 4144}, "extra": {"message": "Detected possible formatted SQL query. Use parameterized queries instead.", "metadata": {"owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "references": ["https://stackoverflow.com/questions/775296/mysql-parameterized-queries"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.formatted-sql-query.formatted-sql-query", "shortlink": "https://sg.run/EkWw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.sqlalchemy.security.sqlalchemy-execute-raw-query.sqlalchemy-execute-raw-query", "path": "downloaded_repos/LinOTP_LinOTP/linotp/model/migrate.py", "start": {"line": 132, "col": 9, "offset": 4034}, "end": {"line": 134, "col": 10, "offset": 4144}, "extra": {"message": "Avoiding SQL string concatenation: untrusted input concatenated with raw SQL query can result in SQL Injection. In order to execute raw query safely, prepared statement should be used. SQLAlchemy provides TextualSQL to easily used prepared statement with named parameters. For complex SQL composition, use SQL Expression Language or Schema Definition Language. In most cases, SQLAlchemy ORM will be a better option.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://docs.sqlalchemy.org/en/14/core/tutorial.html#using-textual-sql", "https://www.tutorialspoint.com/sqlalchemy/sqlalchemy_quick_guide.htm", "https://docs.sqlalchemy.org/en/14/core/tutorial.html#using-more-specific-text-with-table-expression-literal-column-and-expression-column"], "category": "security", "technology": ["sqlalchemy"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/python.sqlalchemy.security.sqlalchemy-execute-raw-query.sqlalchemy-execute-raw-query", "shortlink": "https://sg.run/2b1L"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.formatted-sql-query.formatted-sql-query", "path": "downloaded_repos/LinOTP_LinOTP/linotp/model/migrate.py", "start": {"line": 155, "col": 9, "offset": 4700}, "end": {"line": 155, "col": 87, "offset": 4778}, "extra": {"message": "Detected possible formatted SQL query. Use parameterized queries instead.", "metadata": {"owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "references": ["https://stackoverflow.com/questions/775296/mysql-parameterized-queries"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.formatted-sql-query.formatted-sql-query", "shortlink": "https://sg.run/EkWw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.sqlalchemy.security.sqlalchemy-execute-raw-query.sqlalchemy-execute-raw-query", "path": "downloaded_repos/LinOTP_LinOTP/linotp/model/migrate.py", "start": {"line": 155, "col": 9, "offset": 4700}, "end": {"line": 155, "col": 87, "offset": 4778}, "extra": {"message": "Avoiding SQL string concatenation: untrusted input concatenated with raw SQL query can result in SQL Injection. In order to execute raw query safely, prepared statement should be used. SQLAlchemy provides TextualSQL to easily used prepared statement with named parameters. For complex SQL composition, use SQL Expression Language or Schema Definition Language. In most cases, SQLAlchemy ORM will be a better option.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://docs.sqlalchemy.org/en/14/core/tutorial.html#using-textual-sql", "https://www.tutorialspoint.com/sqlalchemy/sqlalchemy_quick_guide.htm", "https://docs.sqlalchemy.org/en/14/core/tutorial.html#using-more-specific-text-with-table-expression-literal-column-and-expression-column"], "category": "security", "technology": ["sqlalchemy"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/python.sqlalchemy.security.sqlalchemy-execute-raw-query.sqlalchemy-execute-raw-query", "shortlink": "https://sg.run/2b1L"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.formatted-sql-query.formatted-sql-query", "path": "downloaded_repos/LinOTP_LinOTP/linotp/model/migrate.py", "start": {"line": 233, "col": 23, "offset": 7188}, "end": {"line": 233, "col": 72, "offset": 7237}, "extra": {"message": "Detected possible formatted SQL query. Use parameterized queries instead.", "metadata": {"owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "references": ["https://stackoverflow.com/questions/775296/mysql-parameterized-queries"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.formatted-sql-query.formatted-sql-query", "shortlink": "https://sg.run/EkWw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.sqlalchemy.security.sqlalchemy-execute-raw-query.sqlalchemy-execute-raw-query", "path": "downloaded_repos/LinOTP_LinOTP/linotp/model/migrate.py", "start": {"line": 233, "col": 23, "offset": 7188}, "end": {"line": 233, "col": 72, "offset": 7237}, "extra": {"message": "Avoiding SQL string concatenation: untrusted input concatenated with raw SQL query can result in SQL Injection. In order to execute raw query safely, prepared statement should be used. SQLAlchemy provides TextualSQL to easily used prepared statement with named parameters. For complex SQL composition, use SQL Expression Language or Schema Definition Language. In most cases, SQLAlchemy ORM will be a better option.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://docs.sqlalchemy.org/en/14/core/tutorial.html#using-textual-sql", "https://www.tutorialspoint.com/sqlalchemy/sqlalchemy_quick_guide.htm", "https://docs.sqlalchemy.org/en/14/core/tutorial.html#using-more-specific-text-with-table-expression-literal-column-and-expression-column"], "category": "security", "technology": ["sqlalchemy"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/python.sqlalchemy.security.sqlalchemy-execute-raw-query.sqlalchemy-execute-raw-query", "shortlink": "https://sg.run/2b1L"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.formatted-sql-query.formatted-sql-query", "path": "downloaded_repos/LinOTP_LinOTP/linotp/model/migrate.py", "start": {"line": 242, "col": 20, "offset": 7503}, "end": {"line": 244, "col": 14, "offset": 7609}, "extra": {"message": "Detected possible formatted SQL query. Use parameterized queries instead.", "metadata": {"owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "references": ["https://stackoverflow.com/questions/775296/mysql-parameterized-queries"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.formatted-sql-query.formatted-sql-query", "shortlink": "https://sg.run/EkWw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.sqlalchemy.security.sqlalchemy-execute-raw-query.sqlalchemy-execute-raw-query", "path": "downloaded_repos/LinOTP_LinOTP/linotp/model/migrate.py", "start": {"line": 242, "col": 20, "offset": 7503}, "end": {"line": 244, "col": 14, "offset": 7609}, "extra": {"message": "Avoiding SQL string concatenation: untrusted input concatenated with raw SQL query can result in SQL Injection. In order to execute raw query safely, prepared statement should be used. SQLAlchemy provides TextualSQL to easily used prepared statement with named parameters. For complex SQL composition, use SQL Expression Language or Schema Definition Language. In most cases, SQLAlchemy ORM will be a better option.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://docs.sqlalchemy.org/en/14/core/tutorial.html#using-textual-sql", "https://www.tutorialspoint.com/sqlalchemy/sqlalchemy_quick_guide.htm", "https://docs.sqlalchemy.org/en/14/core/tutorial.html#using-more-specific-text-with-table-expression-literal-column-and-expression-column"], "category": "security", "technology": ["sqlalchemy"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/python.sqlalchemy.security.sqlalchemy-execute-raw-query.sqlalchemy-execute-raw-query", "shortlink": "https://sg.run/2b1L"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/model/token.py", "start": {"line": 340, "col": 5, "offset": 11312}, "end": {"line": 340, "col": 41, "offset": 11348}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"createToken(%s)\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.mako-templates-detected.mako-templates-detected", "path": "downloaded_repos/LinOTP_LinOTP/linotp/provider/emailprovider/__init__.py", "start": {"line": 420, "col": 28, "offset": 14563}, "end": {"line": 420, "col": 74, "offset": 14609}, "extra": {"message": "Mako templates do not provide a global HTML escaping mechanism. This means you must escape all sensitive data in your templates using '| u' for URL escaping or '| h' for HTML escaping. If you are using Mako to serve web content, consider using a system such as Jinja2 which enables global escaping.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "source-rule-url": "https://github.com/PyCQA/bandit/blob/b1411bfb43795d3ffd268bef17a839dee954c2b1/bandit/plugins/mako_templates.py", "references": ["https://docs.makotemplates.org/en/latest/syntax.html#expression-escaping", "https://jinja.palletsprojects.com/en/2.11.x/intro/#"], "category": "security", "technology": ["mako"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.lang.security.audit.mako-templates-detected.mako-templates-detected", "shortlink": "https://sg.run/Q5v4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/flexigrid.js", "start": {"line": 233, "col": 25, "offset": 7677}, "end": {"line": 233, "col": 64, "offset": 7716}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/flexigrid.js", "start": {"line": 463, "col": 10, "offset": 14943}, "end": {"line": 463, "col": 61, "offset": 14994}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/flexigrid.js", "start": {"line": 472, "col": 37, "offset": 15552}, "end": {"line": 472, "col": 109, "offset": 15624}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/flexigrid.js", "start": {"line": 490, "col": 10, "offset": 16352}, "end": {"line": 490, "col": 80, "offset": 16422}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/flexigrid.js", "start": {"line": 492, "col": 10, "offset": 16479}, "end": {"line": 492, "col": 66, "offset": 16535}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/flexigrid.js", "start": {"line": 528, "col": 29, "offset": 17577}, "end": {"line": 528, "col": 100, "offset": 17648}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/flexigrid.js", "start": {"line": 536, "col": 9, "offset": 17965}, "end": {"line": 536, "col": 49, "offset": 18005}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/flexigrid.js", "start": {"line": 747, "col": 6, "offset": 22951}, "end": {"line": 747, "col": 39, "offset": 22984}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/flexigrid.js", "start": {"line": 905, "col": 7, "offset": 27789}, "end": {"line": 905, "col": 33, "offset": 27815}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/flexigrid.js", "start": {"line": 978, "col": 6, "offset": 30149}, "end": {"line": 978, "col": 100, "offset": 30243}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/flexigrid.js", "start": {"line": 1057, "col": 4, "offset": 32550}, "end": {"line": 1057, "col": 37, "offset": 32583}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/flexigrid.js", "start": {"line": 1308, "col": 4, "offset": 41574}, "end": {"line": 1308, "col": 67, "offset": 41637}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jed.js", "start": {"line": 387, "col": 15, "offset": 13850}, "end": {"line": 387, "col": 37, "offset": 13872}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery-migrate-3.3.2.js", "start": {"line": 732, "col": 3, "offset": 21666}, "end": {"line": 732, "col": 29, "offset": 21692}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery.datetimepicker.js", "start": {"line": 1067, "col": 43, "offset": 57531}, "end": {"line": 1067, "col": 75, "offset": 57563}, "extra": {"message": "RegExp() called with a `_options` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery.datetimepicker.js", "start": {"line": 2186, "col": 32, "offset": 112717}, "end": {"line": 2186, "col": 49, "offset": 112734}, "extra": {"message": "RegExp() called with a `mask` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery.validate.js", "start": {"line": 232, "col": 28, "offset": 6495}, "end": {"line": 232, "col": 64, "offset": 6531}, "extra": {"message": "RegExp() called with a `i` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery.validate.js", "start": {"line": 634, "col": 20, "offset": 18733}, "end": {"line": 634, "col": 122, "offset": 18835}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery.validate.js", "start": {"line": 789, "col": 38, "offset": 24026}, "end": {"line": 789, "col": 73, "offset": 24061}, "extra": {"message": "RegExp() called with a `$` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery.validate.js", "start": {"line": 789, "col": 38, "offset": 24026}, "end": {"line": 789, "col": 73, "offset": 24061}, "extra": {"message": "RegExp() called with a `element` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery.validate.js", "start": {"line": 789, "col": 38, "offset": 24026}, "end": {"line": 789, "col": 73, "offset": 24061}, "extra": {"message": "RegExp() called with a `message` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/tokens/__init__.py", "start": {"line": 104, "col": 9, "offset": 2863}, "end": {"line": 104, "col": 75, "offset": 2929}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"Token type not supported on this setup: %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/tokens/ocra2token/ocra2token.py", "start": {"line": 804, "col": 13, "offset": 26572}, "end": {"line": 808, "col": 14, "offset": 26719}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[Ocra2TokenClass] challenge verification failed: %s,%r: \" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/tokens/pushtoken/pushtoken.py", "start": {"line": 293, "col": 13, "offset": 9882}, "end": {"line": 297, "col": 14, "offset": 10056}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"pairing the 'not completed' token %s in state %s again.\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/tokens/radiustoken.py", "start": {"line": 212, "col": 9, "offset": 6387}, "end": {"line": 216, "col": 10, "offset": 6526}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[splitPinPass] [radiustoken] returning (len:%s) (len:%s)\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/tokens/radiustoken.py", "start": {"line": 312, "col": 17, "offset": 9745}, "end": {"line": 317, "col": 18, "offset": 9950}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[do_request] [RadiusToken] Radiusserver %s granted \"\n                    \"access to user %s.\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/tokens/radiustoken.py", "start": {"line": 321, "col": 17, "offset": 10042}, "end": {"line": 326, "col": 18, "offset": 10250}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[do_request] [RadiusToken] Radiusserver %s\"\n                    \"rejected access to user %s.\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/tokens/totptoken.py", "start": {"line": 690, "col": 9, "offset": 22024}, "end": {"line": 695, "col": 10, "offset": 22210}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[resync] checking time_window: %s, time_step: %s, current token time_shift: %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/useridresolver/PasswdIdResolver.py", "start": {"line": 247, "col": 9, "offset": 7317}, "end": {"line": 247, "col": 71, "offset": 7379}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[checkPass] checking password for user uid %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/LinOTP_LinOTP/linotp/useridresolver/SQLIdResolver.py", "start": {"line": 601, "col": 9, "offset": 17546}, "end": {"line": 601, "col": 67, "offset": 17604}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"[checkPass] checking password for user %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Timeout", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "message": "Timeout when running python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http on downloaded_repos/LinOTP_LinOTP/linotp/controllers/admin.py:\n ", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/admin.py"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "message": "Timeout when running python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http on downloaded_repos/LinOTP_LinOTP/linotp/controllers/userservice.py:\n ", "path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/userservice.py"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-response.tainted-html-response", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-response.tainted-html-response on downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery-3.6.0.js:\n ", "path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery-3.6.0.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery-3.6.0.js:\n ", "path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery-3.6.0.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery-3.6.0.js:\n ", "path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery-3.6.0.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery-ui.js:\n ", "path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery-ui.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery-ui.js:\n ", "path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery-ui.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "message": "Timeout when running javascript.express.security.injection.raw-html-format.raw-html-format on downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery-ui.js:\n ", "path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery-ui.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/LinOTP_LinOTP/linotp/public/js/manage.js:\n ", "path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/manage.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.lang.security.audit.sqli.node-mysql-sqli.node-mysql-sqli", "message": "Timeout when running javascript.lang.security.audit.sqli.node-mysql-sqli.node-mysql-sqli on downloaded_repos/LinOTP_LinOTP/linotp/public/js/manage.js:\n ", "path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/manage.js"}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/LinOTP_LinOTP/docker/linotp/entrypoint.sh", "start": {"line": 7, "col": 16, "offset": 0}, "end": {"line": 7, "col": 17, "offset": 1}}]], "message": "Syntax error at line downloaded_repos/LinOTP_LinOTP/docker/linotp/entrypoint.sh:7:\n `2` was unexpected", "path": "downloaded_repos/LinOTP_LinOTP/docker/linotp/entrypoint.sh", "spans": [{"file": "downloaded_repos/LinOTP_LinOTP/docker/linotp/entrypoint.sh", "start": {"line": 7, "col": 16, "offset": 0}, "end": {"line": 7, "col": 17, "offset": 1}}]}], "paths": {"scanned": ["downloaded_repos/LinOTP_LinOTP/.dockerignore", "downloaded_repos/LinOTP_LinOTP/.gitattributes", "downloaded_repos/LinOTP_LinOTP/.gitignore", "downloaded_repos/LinOTP_LinOTP/.gitlab-ci.yml", "downloaded_repos/LinOTP_LinOTP/.pre-commit-config.yaml", "downloaded_repos/LinOTP_LinOTP/.pylintrc", "downloaded_repos/LinOTP_LinOTP/.python-version", "downloaded_repos/LinOTP_LinOTP/CHANGELOG.md", "downloaded_repos/LinOTP_LinOTP/DEVELOP.md", "downloaded_repos/LinOTP_LinOTP/LICENSE", "downloaded_repos/LinOTP_LinOTP/MANIFEST.in", "downloaded_repos/LinOTP_LinOTP/Makefile", "downloaded_repos/LinOTP_LinOTP/Makefile.linotp", "downloaded_repos/LinOTP_LinOTP/README.md", "downloaded_repos/LinOTP_LinOTP/api-doc/Makefile", "downloaded_repos/LinOTP_LinOTP/api-doc/source/conf.py", "downloaded_repos/LinOTP_LinOTP/api-doc/source/themes/linotp-api/static/linotp-api.css", "downloaded_repos/LinOTP_LinOTP/api-doc/source/themes/linotp-api/theme.conf", "downloaded_repos/LinOTP_LinOTP/babel.cfg", "downloaded_repos/LinOTP_LinOTP/docker/Dockerfile.linotp", "downloaded_repos/LinOTP_LinOTP/docker/compose.e2etests.yml", "downloaded_repos/LinOTP_LinOTP/docker/compose.yaml", "downloaded_repos/LinOTP_LinOTP/docker/linotp/doas.conf", "downloaded_repos/LinOTP_LinOTP/docker/linotp/entrypoint.sh", "downloaded_repos/LinOTP_LinOTP/docker/linotp/install-ca-certificates.sh", "downloaded_repos/LinOTP_LinOTP/docker/linotp/symlink_custom_assets.py", "downloaded_repos/LinOTP_LinOTP/docker/linotp/wait_for_db.py", "downloaded_repos/LinOTP_LinOTP/examples/mailtemplate-authenticate.eml", "downloaded_repos/LinOTP_LinOTP/examples/mailtemplate-enroll.eml", "downloaded_repos/LinOTP_LinOTP/examples/mailtemplate-set-pin.eml", "downloaded_repos/LinOTP_LinOTP/linotp/__init__.py", "downloaded_repos/LinOTP_LinOTP/linotp/app.py", "downloaded_repos/LinOTP_LinOTP/linotp/cli/__init__.py", "downloaded_repos/LinOTP_LinOTP/linotp/cli/admin_cmd.py", "downloaded_repos/LinOTP_LinOTP/linotp/cli/audit_cmd.py", "downloaded_repos/LinOTP_LinOTP/linotp/cli/dbsnapshot_cmd.py", "downloaded_repos/LinOTP_LinOTP/linotp/cli/init_cmd.py", "downloaded_repos/LinOTP_LinOTP/linotp/cli/local_admins_cmd.py", "downloaded_repos/LinOTP_LinOTP/linotp/cli/mysql_cmd.py", "downloaded_repos/LinOTP_LinOTP/linotp/cli/support_cmd.py", "downloaded_repos/LinOTP_LinOTP/linotp/controllers/__init__.py", "downloaded_repos/LinOTP_LinOTP/linotp/controllers/admin.py", "downloaded_repos/LinOTP_LinOTP/linotp/controllers/audit.py", "downloaded_repos/LinOTP_LinOTP/linotp/controllers/auditlog.py", "downloaded_repos/LinOTP_LinOTP/linotp/controllers/auth.py", "downloaded_repos/LinOTP_LinOTP/linotp/controllers/base.py", "downloaded_repos/LinOTP_LinOTP/linotp/controllers/error.py", "downloaded_repos/LinOTP_LinOTP/linotp/controllers/gettoken.py", "downloaded_repos/LinOTP_LinOTP/linotp/controllers/maintenance.py", "downloaded_repos/LinOTP_LinOTP/linotp/controllers/manage.py", "downloaded_repos/LinOTP_LinOTP/linotp/controllers/migrate.py", "downloaded_repos/LinOTP_LinOTP/linotp/controllers/monitoring.py", "downloaded_repos/LinOTP_LinOTP/linotp/controllers/realms.py", "downloaded_repos/LinOTP_LinOTP/linotp/controllers/reporting.py", "downloaded_repos/LinOTP_LinOTP/linotp/controllers/resolvers.py", "downloaded_repos/LinOTP_LinOTP/linotp/controllers/selfservice.py", "downloaded_repos/LinOTP_LinOTP/linotp/controllers/system.py", "downloaded_repos/LinOTP_LinOTP/linotp/controllers/tokens.py", "downloaded_repos/LinOTP_LinOTP/linotp/controllers/tools.py", "downloaded_repos/LinOTP_LinOTP/linotp/controllers/u2f.py", "downloaded_repos/LinOTP_LinOTP/linotp/controllers/userservice.py", "downloaded_repos/LinOTP_LinOTP/linotp/controllers/validate.py", "downloaded_repos/LinOTP_LinOTP/linotp/def-passwd", "downloaded_repos/LinOTP_LinOTP/linotp/dictionary", "downloaded_repos/LinOTP_LinOTP/linotp/flap.py", "downloaded_repos/LinOTP_LinOTP/linotp/i18n/de/LC_MESSAGES/linotp.po", "downloaded_repos/LinOTP_LinOTP/linotp/lib/HMAC.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/ImportOTP/DPWplain.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/ImportOTP/PSKC.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/ImportOTP/__init__.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/ImportOTP/eTokenDat.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/ImportOTP/oath.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/ImportOTP/safenet.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/ImportOTP/yubico.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/__init__.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/apps.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/audit/SQLAudit.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/audit/__init__.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/audit/base.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/audit/iterator.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/auth/__init__.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/auth/finishtokens.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/auth/validate.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/black.gif", "downloaded_repos/LinOTP_LinOTP/linotp/lib/cache.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/cache_utils.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/challenges.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/config/__init__.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/config/config_class.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/config/db_api.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/config/global_api.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/config/parsing.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/config/type_definition.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/config/util.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/context.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/crypto/__init__.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/crypto/encrypted_data.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/crypto/pbkdf2.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/crypto/rsa.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/crypto/utils.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/error.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/ext/__init__.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/ext/pbkdf2.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/fs_utils.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/gray.gif", "downloaded_repos/LinOTP_LinOTP/linotp/lib/local.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/log.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/logs.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/migrate.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/module_loader.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/monitoring.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/pairing.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/__init__.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/action.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/definitions.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/evaluate.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/filter.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/forward.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/manage.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/maxtoken.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/permissions.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/processing.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/util.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/realm.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/registry.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/remote_service.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/reply.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/reporting.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/request.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/resolver.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/resources.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/rw_lock.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/secret_obj.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/security/__init__.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/security/default.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/security/fips.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/security/libfips/__init__.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/security/libfips/selfcheck.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/security/pkcs11.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/security/provider.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/selfservice.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/support.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/text_utils.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/token.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/tokeniterator.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/tools/__init__.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/tools/expiring_list.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/tools/import_user/ImportHandler.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/tools/import_user/SQLImportHandler.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/tools/import_user/__init__.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/tools/migrate_resolver.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/tools/set_password.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/type_utils.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/user.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/useriterator.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/userservice.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/util.py", "downloaded_repos/LinOTP_LinOTP/linotp/lib/white.gif", "downloaded_repos/LinOTP_LinOTP/linotp/linotp.cfg", "downloaded_repos/LinOTP_LinOTP/linotp/middlewares/__init__.py", "downloaded_repos/LinOTP_LinOTP/linotp/middlewares/trusted_proxy_handler.py", "downloaded_repos/LinOTP_LinOTP/linotp/model/__init__.py", "downloaded_repos/LinOTP_LinOTP/linotp/model/challange.py", "downloaded_repos/LinOTP_LinOTP/linotp/model/config.py", "downloaded_repos/LinOTP_LinOTP/linotp/model/db_logging.py", "downloaded_repos/LinOTP_LinOTP/linotp/model/imported_user.py", "downloaded_repos/LinOTP_LinOTP/linotp/model/local_admin_user.py", "downloaded_repos/LinOTP_LinOTP/linotp/model/migrate.py", "downloaded_repos/LinOTP_LinOTP/linotp/model/realm.py", "downloaded_repos/LinOTP_LinOTP/linotp/model/reporting.py", "downloaded_repos/LinOTP_LinOTP/linotp/model/resolver.py", "downloaded_repos/LinOTP_LinOTP/linotp/model/schema/__init__.py", "downloaded_repos/LinOTP_LinOTP/linotp/model/schema/challange_schema.py", "downloaded_repos/LinOTP_LinOTP/linotp/model/schema/config_schema.py", "downloaded_repos/LinOTP_LinOTP/linotp/model/schema/db_logging_schema.py", "downloaded_repos/LinOTP_LinOTP/linotp/model/schema/imported_user_schema.py", "downloaded_repos/LinOTP_LinOTP/linotp/model/schema/realm_schema.py", "downloaded_repos/LinOTP_LinOTP/linotp/model/schema/reporting_schema.py", "downloaded_repos/LinOTP_LinOTP/linotp/model/schema/token_realm_schema.py", "downloaded_repos/LinOTP_LinOTP/linotp/model/schema/token_schema.py", "downloaded_repos/LinOTP_LinOTP/linotp/model/token.py", "downloaded_repos/LinOTP_LinOTP/linotp/model/tokenRealm.py", "downloaded_repos/LinOTP_LinOTP/linotp/provider/__init__.py", "downloaded_repos/LinOTP_LinOTP/linotp/provider/config_parsing.py", "downloaded_repos/LinOTP_LinOTP/linotp/provider/create_provider_ini.py", "downloaded_repos/LinOTP_LinOTP/linotp/provider/emailprovider/__init__.py", "downloaded_repos/LinOTP_LinOTP/linotp/provider/notification.py", "downloaded_repos/LinOTP_LinOTP/linotp/provider/pushprovider/__init__.py", "downloaded_repos/LinOTP_LinOTP/linotp/provider/pushprovider/default_push_provider.py", "downloaded_repos/LinOTP_LinOTP/linotp/provider/smsprovider/DeviceSMSProvider.py", "downloaded_repos/LinOTP_LinOTP/linotp/provider/smsprovider/FileSMSProvider.py", "downloaded_repos/LinOTP_LinOTP/linotp/provider/smsprovider/HttpSMSProvider.py", "downloaded_repos/LinOTP_LinOTP/linotp/provider/smsprovider/RestSMSProvider.py", "downloaded_repos/LinOTP_LinOTP/linotp/provider/smsprovider/SMPPSMSProvider.py", "downloaded_repos/LinOTP_LinOTP/linotp/provider/smsprovider/SmtpSMSProvider.py", "downloaded_repos/LinOTP_LinOTP/linotp/provider/smsprovider/__init__.py", "downloaded_repos/LinOTP_LinOTP/linotp/provider/voiceprovider/__init__.py", "downloaded_repos/LinOTP_LinOTP/linotp/provider/voiceprovider/custom_voice_provider.py", "downloaded_repos/LinOTP_LinOTP/linotp/public/bg.png", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/datetimepicker/jquery.datetimepicker.css", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/flexigrid/flexigrid.css", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/flexigrid/images/bg.gif", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/flexigrid/images/btn-sprite.gif", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/flexigrid/images/ddn.png", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/flexigrid/images/dn.png", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/flexigrid/images/fhbg.gif", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/flexigrid/images/first.gif", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/flexigrid/images/hl.png", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/flexigrid/images/last.gif", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/flexigrid/images/line.gif", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/flexigrid/images/load.gif", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/flexigrid/images/load.png", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/flexigrid/images/magnifier.png", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/flexigrid/images/next.gif", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/flexigrid/images/prev.gif", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/flexigrid/images/up.png", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/flexigrid/images/uup.png", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/flexigrid/images/wbg.gif", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/images/add.png", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/images/close.png", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/jquery-ui/images/ui-bg_glass_55_fbf9ee_1x400.png", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/jquery-ui/images/ui-bg_glass_65_ffffff_1x400.png", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/jquery-ui/images/ui-bg_glass_75_dadada_1x400.png", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/jquery-ui/images/ui-bg_glass_75_e6e6e6_1x400.png", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/jquery-ui/images/ui-bg_glass_95_fef1ec_1x400.png", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/jquery-ui/images/ui-bg_highlight-soft_75_cccccc_1x100.png", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/jquery-ui/images/ui-icons_222222_256x240.png", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/jquery-ui/images/ui-icons_2e83ff_256x240.png", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/jquery-ui/images/ui-icons_454545_256x240.png", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/jquery-ui/images/ui-icons_888888_256x240.png", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/jquery-ui/images/ui-icons_cd0a0a_256x240.png", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/jquery-ui/images/ui-icons_ffffff_256x240.png", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/jquery-ui/jquery-ui.css", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/jquery-ui/jquery-ui.min.css", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/jquery-ui/jquery-ui.structure.css", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/jquery-ui/jquery-ui.structure.min.css", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/jquery-ui/jquery-ui.theme.css", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/jquery-ui/jquery-ui.theme.min.css", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/linotp.css", "downloaded_repos/LinOTP_LinOTP/linotp/public/css/superfish.css", "downloaded_repos/LinOTP_LinOTP/linotp/public/favicon.ico", "downloaded_repos/LinOTP_LinOTP/linotp/public/images/Readme", "downloaded_repos/LinOTP_LinOTP/linotp/public/images/ajax-loader.gif", "downloaded_repos/LinOTP_LinOTP/linotp/public/images/arrows-ffffff.png", "downloaded_repos/LinOTP_LinOTP/linotp/public/images/linotp_logo_103x35.png", "downloaded_repos/LinOTP_LinOTP/linotp/public/images/linotp_logo_200x68_72dpi.png", "downloaded_repos/LinOTP_LinOTP/linotp/public/images/shadow.png", "downloaded_repos/LinOTP_LinOTP/linotp/public/images/ui-bg_highlight-soft_75_cccccc_1x100.png", "downloaded_repos/LinOTP_LinOTP/linotp/public/js/aladdin.js", "downloaded_repos/LinOTP_LinOTP/linotp/public/js/auth.js", "downloaded_repos/LinOTP_LinOTP/linotp/public/js/dat.js", "downloaded_repos/LinOTP_LinOTP/linotp/public/js/dpw.js", "downloaded_repos/LinOTP_LinOTP/linotp/public/js/feitian.js", "downloaded_repos/LinOTP_LinOTP/linotp/public/js/flexigrid.js", "downloaded_repos/LinOTP_LinOTP/linotp/public/js/hoverIntent.js", "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jed.js", "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery-3.6.0.js", "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery-3.6.0.min.map", "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery-migrate-3.3.2.js", "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery-ui.js", "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery.cookie.js", "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery.cookie.min.js.map", "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery.datetimepicker.js", "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery.form.js", "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery.form.min.js.map", "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery.validate.js", "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery.validate.linotp.js", "downloaded_repos/LinOTP_LinOTP/linotp/public/js/libmanage.js", "downloaded_repos/LinOTP_LinOTP/linotp/public/js/linotp_utils.js", "downloaded_repos/LinOTP_LinOTP/linotp/public/js/manage/login.js", "downloaded_repos/LinOTP_LinOTP/linotp/public/js/manage.js", "downloaded_repos/LinOTP_LinOTP/linotp/public/js/oathcsv.js", "downloaded_repos/LinOTP_LinOTP/linotp/public/js/pskc.js", "downloaded_repos/LinOTP_LinOTP/linotp/public/js/selfservice/login.js", "downloaded_repos/LinOTP_LinOTP/linotp/public/js/selfservice.js", "downloaded_repos/LinOTP_LinOTP/linotp/public/js/superfish.js", "downloaded_repos/LinOTP_LinOTP/linotp/public/js/tools.js", "downloaded_repos/LinOTP_LinOTP/linotp/public/js/u2f-api.js", "downloaded_repos/LinOTP_LinOTP/linotp/public/js/yubikeycsv.js", "downloaded_repos/LinOTP_LinOTP/linotp/public/manage/login.css", "downloaded_repos/LinOTP_LinOTP/linotp/public/manage/style.css", "downloaded_repos/LinOTP_LinOTP/linotp/public/openid/style.css", "downloaded_repos/LinOTP_LinOTP/linotp/public/pylons-logo.gif", "downloaded_repos/LinOTP_LinOTP/linotp/public/selfservice/auth.css", "downloaded_repos/LinOTP_LinOTP/linotp/public/selfservice/style.css", "downloaded_repos/LinOTP_LinOTP/linotp/settings.py", "downloaded_repos/LinOTP_LinOTP/linotp/templates/auth-base.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/auth-challenge-response.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/auth-ocra2.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/auth-push.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/auth-qrtoken.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/auth.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/auth3.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/manage/audit.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/manage/login.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/manage/manage-base.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/manage/policies.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/manage/tokeninfo.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/manage/tokenview.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/manage/userview.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/openid/base.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/openid/check_setup.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/openid/login.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/openid/status.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/selfservice/assign.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/selfservice/base.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/selfservice/delete.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/selfservice/disable.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/selfservice/enable.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/selfservice/getotp.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/selfservice/history.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/selfservice/landing.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/selfservice/login.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/selfservice/multiotp_view.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/selfservice/register-sms.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/selfservice/reset.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/selfservice/resync.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/selfservice/selfservice.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/selfservice/setmpin.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/selfservice/setpin.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/selfservice/tokenlist.mako", "downloaded_repos/LinOTP_LinOTP/linotp/templates/selfservice/unassign.mako", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/__init__.py", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/base/__init__.py", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/base/stateful_mixin.py", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/base/tokeninfo_mixin.py", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/base/tokenproperty_mixin.py", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/base/validity_mixin.py", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/emailtoken.mako", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/emailtoken.py", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/forwardtoken.mako", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/forwardtoken.py", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/hmactoken.mako", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/hmactoken.py", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/motp/__init__.py", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/motp/motptoken.mako", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/motp/motptoken.py", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/ocra2token/__init__.py", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/ocra2token/ocra2token.mako", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/ocra2token/ocra2token.py", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/passwordtoken.mako", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/passwordtoken.py", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/pushtoken/__init__.py", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/pushtoken/pushtoken.mako", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/pushtoken/pushtoken.py", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/qrtoken/__init__.py", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/qrtoken/qrtoken.mako", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/qrtoken/qrtoken.py", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/radiustoken.mako", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/radiustoken.py", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/remotetoken.mako", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/remotetoken.py", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/smstoken.mako", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/smstoken.py", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/spasstoken.mako", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/spasstoken.py", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/tagespassworttoken.mako", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/tagespassworttoken.py", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/totptoken.mako", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/totptoken.py", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/u2ftoken/__init__.py", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/u2ftoken/u2ftoken.mako", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/u2ftoken/u2ftoken.py", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/voicetoken/__init__.py", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/voicetoken/voicetoken.mako", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/voicetoken/voicetoken.py", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/yubicotoken.mako", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/yubicotoken.py", "downloaded_repos/LinOTP_LinOTP/linotp/tokens/yubikeytoken.py", "downloaded_repos/LinOTP_LinOTP/linotp/useridresolver/LDAPIdResolver.py", "downloaded_repos/LinOTP_LinOTP/linotp/useridresolver/PasswdIdResolver.py", "downloaded_repos/LinOTP_LinOTP/linotp/useridresolver/SQLIdResolver.py", "downloaded_repos/LinOTP_LinOTP/linotp/useridresolver/UserIdResolver.py", "downloaded_repos/LinOTP_LinOTP/linotp/useridresolver/__init__.py", "downloaded_repos/LinOTP_LinOTP/linotpapp.py", "downloaded_repos/LinOTP_LinOTP/preprocessor.ini", "downloaded_repos/LinOTP_LinOTP/pyproject.toml", "downloaded_repos/LinOTP_LinOTP/rules.pl", "downloaded_repos/LinOTP_LinOTP/uv.lock"], "skipped": [{"path": "downloaded_repos/LinOTP_LinOTP/docker/linotp/entrypoint.sh", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/admin.py", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/userservice.py", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery-3.6.0.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery-3.6.0.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery-migrate-3.3.2.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery-ui.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery-ui.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery.cookie.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery.datetimepicker.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery.form.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery.validate.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/manage.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/superfish.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/common-vars.mk", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/conftest.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/Makefile", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/admin_authentication/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/admin_authentication/test_delete_realm_resolver.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/admin_authentication/test_qol_realm_resolver.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/authorization/test_audit.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/authorization/test_monitoring.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/authorization/test_reporting.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/authorization/test_system.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/authorization/test_tools.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/challenge_response/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/challenge_response/test_challenge_response.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/challenge_response/test_httpsms.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/challenge_response/test_radiustoken.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/challenge_response/test_remote_token.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/challenge_response/test_remote_token2.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/challenge_response/test_remotetoken.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/challenge_response/testing_controller.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/fixtures/4users.csv", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/fixtures/admin-passwd", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/fixtures/cert.pem", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/fixtures/def-passwd", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/fixtures/def-passwd-changed", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/fixtures/def-passwd-plain-changed.csv", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/fixtures/def-passwd-plain.csv", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/fixtures/def-passwd.csv", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/fixtures/demo-lic.pem", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/fixtures/email.eml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/fixtures/enrollment_email.eml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/fixtures/etc-passwd", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/fixtures/expired-lic.pem", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/fixtures/linotp2.token_user.pem", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/fixtures/my-pass2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/fixtures/my-passwd", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/fixtures/myDom-passwd", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/fixtures/oath_tokens.csv", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/fixtures/oath_tokens_bad_seed.csv", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/fixtures/oath_tokens_sha256.csv", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/fixtures/oath_tokens_sha512.csv", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/fixtures/ocra_pskc_tokens.xml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/fixtures/policy_realm.cfg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/fixtures/pskc_tokens.xml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/fixtures/safenet_tokens.dat", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/fixtures/safenet_tokens.xml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/fixtures/safework_tokens.dat", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/fixtures/setpin_email.eml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/fixtures/yubi_chall_tokens.csv", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/fixtures/yubi_hmac.csv", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/fixtures/yubi_tokens.csv", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_admin.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_admin_auth.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_admin_privilege.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_authorize.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_challenge_prompt.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_challenge_totp.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_check_status.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_config.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_controllers/test_realms.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_controllers/test_resolver.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_controllers/test_tokens.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_docker/test_entrypoint.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_emailtoken.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_fixes.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_forward_token.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_getotp.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_getserial.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_healthcheck.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_httperror_response.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_import_user.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_importotp.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_jwt_auth.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_ldapresolver.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_manage.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_monitoring.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_motp.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_ocra2token/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_ocra2token/test_ocra2.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_ocra2token/test_userservice.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_orphaned.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_passwdidresolver.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_policy/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_policy/test_autosms.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_policy/test_permissions.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_policy/test_policy.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_policy/test_realm_policies.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_policy/test_upload_max.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_policy2/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_policy2/test_admin_show.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_policy2/test_autoassignment.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_policy2/test_autoenroll.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_policy2/test_autoenroll_sms.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_policy2/test_forward_server.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_policy2/test_inactive_policy.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_policy2/test_maxtoken.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_policy2/test_new_engine.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_policy2/test_passthrough.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_policy2/test_policy_hierarchy.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_policy2/test_randompin.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_policy2/test_realm_context.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_policy2/test_setrealm.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_policy2/test_tokencount.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_provider/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_provider/test_provider.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_provider/test_push_provider.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_provider/test_rest_sms_provider.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_provider/test_smppsmsprovider.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_provider/test_voice_provider.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_pushtoken.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_qrtoken.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_radius_token.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_replication_sync.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_reporting.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_resolver.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_resolvers/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_resolvers/sql_test_controller.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_resolvers/sql_user.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_resolvers/test_passwords_of_sql_resolver.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_resolvers/test_resolver_cache.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_resolvers/test_special_char_passwords_with_sqlresolver.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_resolvers/test_sql_resolver.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_selfservice.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_selfservice_auth.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_set_password.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_support.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_system.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_testconnection.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_token_enrollment/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_token_enrollment/test_motptoken.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_token_enrollment/test_rollout_token.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_token_enrollment_info.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_tokenlabel.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_tokenlist.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_tokens/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_tokens/test_duplicate_failcounter_increment.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_tokens/test_emailtoken.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_tokens/test_hmac.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_tokens/test_hotp.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_tokens/test_last_access.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_tokens/test_totp.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_tokens/test_totp_lookup.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_tokens/test_transaction_id.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_tokensearch.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_tools/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_tools/test_migrate_resolver.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_totp.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_u2f.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_userprincipal.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_userservice/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_userservice/push_token_validation.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_userservice/qr_token_validation.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_userservice/test_enroll.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_userservice/test_otplogin_userselfservice.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_userservice/test_reporting.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_userservice/test_setdescription_token.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_userservice/test_userservice_login.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_userservice/test_verify_token.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_validate.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_voicetoken.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/functional/test_yubikey.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/Makefile", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/certificates/blackdog-ldap.crt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/certificates/root_ca.corp.linotp.de.crt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/certificates/step-ca.crt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/conftest.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/docker_cfg.ini.tmpl", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/integration_data.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/linotp_selenium_helper/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/linotp_selenium_helper/auth_ui.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/linotp_selenium_helper/helper.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/linotp_selenium_helper/license_import.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/linotp_selenium_helper/manage_elements.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/linotp_selenium_helper/manage_ui.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/linotp_selenium_helper/policy.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/linotp_selenium_helper/realm.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/linotp_selenium_helper/set_config.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/linotp_selenium_helper/smtp_server.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/linotp_selenium_helper/system_config.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/linotp_selenium_helper/test_case.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/linotp_selenium_helper/token_enroll.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/linotp_selenium_helper/token_import.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/linotp_selenium_helper/token_view.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/linotp_selenium_helper/user_id_resolver.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/linotp_selenium_helper/user_view.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/linotp_selenium_helper/validate.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/server_cfg.ini", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/test_auth.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/test_create_useridresolvers.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/test_emailtoken.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/test_import_tokens.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/test_ldap_conn.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/test_license.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/test_manage.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/test_policies.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/test_realm_dialog.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/test_scenario01.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/test_smstoken.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/test_system_config.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/test_token_view.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/test_user_view.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/test_yubikey.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/testdata/aladdin.xml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/testdata/oath_tokens.csv", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/testdata/oath_tokens_bad_seed.csv", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/testdata/se_mypasswd", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/integration/testdata/wrong_token.xml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/load/test_ocra_p.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/test_additionals.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/test_sql_connect_str.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/tools/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/tools/dummy_radius_server.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/tools/dummy_smpp_server.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/Makefile", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/cli/test_audit_cmd.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/cli/test_cli.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/cli/test_cli_local_admins_cmd.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/cli/test_cli_support_cmd.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/cli/test_config_cmd.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/cli/test_dbsnapshot_cmd.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/cli/test_init_cmd.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/controllers/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/controllers/test_admin.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/controllers/test_audit.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/controllers/test_auth.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/controllers/test_base.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/controllers/test_maintenance.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/controllers/test_manage.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/controllers/test_system.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/controllers/test_tokens.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/controllers/test_userservice.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/controllers/test_validate.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/js/spec/javascripts/linotp_libmanage_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/js/spec/javascripts/linotp_utils_spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/js/spec/javascripts/support/jasmine.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/js/spec/javascripts/support/jasmine2-custom-message-0.8.2.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/audit/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/audit/test_base.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/audit/test_iterator.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/auth/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/auth/test_validate_check_with_serial.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/crypto/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/crypto/test_check_password.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/crypto/test_encrypted_data.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/crypto/test_get_hashalgo.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/crypto/test_pdkdf2.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/crypto/test_rsa.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/enrollment/test_autoassign_wo_pass.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/ext/pbkdf2.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/importOTP/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/importOTP/test_importotp.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/policy/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/policy/test_check_admin_auth.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/policy/test_client_policy.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/policy/test_condition_comparison.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/policy/test_evaluate.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/policy/test_get_client_policy.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/policy/test_import.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/policy/test_is_authorized.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/policy/test_otppin.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/policy/test_parse_actionvalue.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/policy/test_policy_selection.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/policy/test_policy_with_actions.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/policy/test_selfservice_actions.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/security/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/security/test_create_hsm_pool.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/test_audit_entry.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/test_cache_utils.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/test_challenges.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/test_chunk_config.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/test_fs_utils.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/test_get_client.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/test_logging_decorators.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/test_losttoken_validity.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/test_maxtoken.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/test_migrate.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/test_pairing.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/test_reply.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/test_resolver.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/test_resources.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/test_support.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/test_tokenlabel.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/test_tokensearch.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/test_user_compare.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/test_user_from_param.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/token/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/token/test_token_last_accessed.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/token/test_token_owner.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/tokenclass/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/tokenclass/test_validity_mixin.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/tokeniterator/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/tokeniterator/test_wildcard_user_match.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/tools/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/tools/test_resolver.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/tools/test_set_password.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/tools/test_sql_import_handler.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/type_util/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/type_util/test_duration.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/type_util/test_ipdq.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/type_utils/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/type_utils/test_get_timeout.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/user/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/user/test_cache_activation.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/user/test_functions.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/user/test_getUserInfo.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/user/test_get_user_from_request.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/user/test_logging_in_user_lookup.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/user/test_user_class.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/user/test_user_from_options.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/userservice/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/userservice/test_cookie.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/userservice/test_precontext.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/util/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/util/test_modhex_conversion.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/lib/util/test_reencode.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/middlewares/test_trusted_proxy_handler.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/migration/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/migration/test_rename_webprovision_policies.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/provider/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/provider/gnokiirc", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/provider/test_devicesms.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/provider/test_email_provider/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/provider/test_email_provider/test_email_template.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/provider/test_filesmsprovider.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/provider/test_provider_from_policy.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/provider/test_provider_ini.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/provider/test_server_certificate.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/provider/test_sms.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/provider/test_sms_provider/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/provider/test_sms_provider/test_phone_template.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/provider/test_voice_provider.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/resolver/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/resolver/imported/data/addindex-64bit", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/resolver/imported/data/data", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/resolver/imported/data/exp.ldif", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/resolver/imported/data/linotp-users.sql", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/resolver/imported/data/parse-64bit", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/resolver/imported/data/tinyldap-64bit", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/resolver/imported/test_resolve_ldap.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/resolver/imported/test_resolve_pwd.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/resolver/imported/test_resolve_sql.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/resolver/ldap_resolver/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/resolver/ldap_resolver/test_attribute_encoding.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/resolver/ldap_resolver/test_failover.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/resolver/ldap_resolver/test_multiple_search_attributes.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/resolver/sqlresolver/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/resolver/sqlresolver/test_localadminresolver.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/resolver/sqlresolver/test_passwords.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/resolver/test_sql_sensitive_data.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/resolver/test_static_methods.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/test_config.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/test_config_logging.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/test_jwt_base.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/test_remote_service.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/test_setting_controllers.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/test_site_root.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/testdata/provider.ini", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/tokens/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/tokens/challenge_token/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/tokens/challenge_token/test_challenge_prompt.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/tokens/test_dpw.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/tokens/test_hotp.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/tokens/test_hotp_testvectors.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/tokens/test_mako/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/tokens/test_mako/test_qrtoken_activate.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/tokens/test_ocra2token/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/tokens/test_ocra2token/test_ocra2token.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/tokens/test_ocra2token/test_ocrasuite.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/tokens/test_pushtoken.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/tokens/test_qrtoken.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/tokens/test_smstoken/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/tokens/test_smstoken/test_dynamic_mobile_number.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/tokens/test_totptoken/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/tokens/test_totptoken/test_totp.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/tokens/test_totptoken/test_totp_testvectors.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/tokens/test_u2ftoken.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/tokens/test_yubicotoken.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/tokens/test_yubikeytoken.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/tools/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/LinOTP_LinOTP/linotp/tests/unit/tools/test_expiring_list.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.854550838470459, "profiling_times": {"config_time": 7.150131464004517, "core_time": 34.86012125015259, "ignores_time": 0.002716541290283203, "total_time": 42.014044761657715}, "parsing_time": {"total_time": 12.66090440750122, "per_file_time": {"mean": 0.05457286382543628, "std_dev": 0.023301164631954342}, "very_slow_stats": {"time_ratio": 0.42529371127222404, "count_ratio": 0.03017241379310345}, "very_slow_files": [{"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/__init__.py", "ftime": 0.43317604064941406}, {"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery-migrate-3.3.2.js", "ftime": 0.4753451347351074}, {"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery.form.js", "ftime": 0.4922349452972412}, {"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery.validate.js", "ftime": 0.526757001876831}, {"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jed.js", "ftime": 0.6162331104278564}, {"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/flexigrid.js", "ftime": 1.3979458808898926}, {"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery.datetimepicker.js", "ftime": 1.44291090965271}]}, "scanning_time": {"total_time": 335.32291173934937, "per_file_time": {"mean": 0.33700795149683327, "std_dev": 3.6563745469027835}, "very_slow_stats": {"time_ratio": 0.8169535859184606, "count_ratio": 0.04120603015075377}, "very_slow_files": [{"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/system.py", "ftime": 7.973475933074951}, {"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/settings.py", "ftime": 8.384690046310425}, {"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/tokens/ocra2token/ocra2token.py", "ftime": 9.300437927246094}, {"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/flexigrid.js", "ftime": 12.679340839385986}, {"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/admin.py", "ftime": 15.380882024765015}, {"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/__init__.py", "ftime": 16.47040605545044}, {"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery-3.6.0.js", "ftime": 17.991619110107422}, {"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/userservice.py", "ftime": 18.34748101234436}, {"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/manage.js", "ftime": 27.779383897781372}, {"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery.datetimepicker.js", "ftime": 30.059523105621338}]}, "matching_time": {"total_time": 164.76411724090576, "per_file_and_rule_time": {"mean": 0.0417124347445331, "std_dev": 0.030978636196713847}, "very_slow_stats": {"time_ratio": 0.7305152756013726, "count_ratio": 0.08531645569620253}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/selfservice.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 1.7385339736938477}, {"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/manage.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 1.9758398532867432}, {"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/lib/policy/__init__.py", "rule_id": "python.django.security.injection.path-traversal.path-traversal-open.path-traversal-open", "time": 2.2308149337768555}, {"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/tokens/pushtoken/pushtoken.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 2.3188061714172363}, {"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/controllers/userservice.py", "rule_id": "python.django.security.injection.path-traversal.path-traversal-open.path-traversal-open", "time": 2.363269090652466}, {"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/app.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 2.4629220962524414}, {"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/lib/reply.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 2.5603039264678955}, {"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/manage.js", "rule_id": "javascript.express.security.audit.express-open-redirect.express-open-redirect", "time": 3.6322901248931885}, {"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/settings.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 3.774696111679077}, {"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/tokens/ocra2token/ocra2token.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 3.808340072631836}]}, "tainting_time": {"total_time": 70.43798184394836, "per_def_and_rule_time": {"mean": 0.002882903525721291, "std_dev": 0.0006924157329687089}, "very_slow_stats": {"time_ratio": 0.36046845231570834, "count_ratio": 0.004379323046699137}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/flexigrid.js", "fline": 1, "rule_id": "javascript.express.security.cors-misconfiguration.cors-misconfiguration", "time": 0.6248049736022949}, {"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery.datetimepicker.js", "fline": 1, "rule_id": "javascript.express.security.cors-misconfiguration.cors-misconfiguration", "time": 0.7312169075012207}, {"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery.datetimepicker.js", "fline": 1, "rule_id": "javascript.express.security.x-frame-options-misconfiguration.x-frame-options-misconfiguration", "time": 0.8761401176452637}, {"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery.datetimepicker.js", "fline": 1, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 0.9370529651641846}, {"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery.datetimepicker.js", "fline": 1, "rule_id": "javascript.aws-lambda.security.tainted-eval.tainted-eval", "time": 0.9436330795288086}, {"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery.datetimepicker.js", "fline": 1, "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "time": 1.086846113204956}, {"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery.datetimepicker.js", "fline": 1, "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 1.1129209995269775}, {"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/flexigrid.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 1.1318860054016113}, {"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery.datetimepicker.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 1.1852450370788574}, {"fpath": "downloaded_repos/LinOTP_LinOTP/linotp/public/js/jquery.datetimepicker.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 2.1987850666046143}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}