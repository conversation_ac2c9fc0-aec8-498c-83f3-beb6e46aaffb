{"version": "1.130.0", "results": [{"check_id": "python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "path": "downloaded_repos/dwain<PERSON>eren_dwains-lovelace-dashboard/custom_components/dwains_dashboard/process_yaml.py", "start": {"line": 29, "col": 9, "offset": 640}, "end": {"line": 29, "col": 64, "offset": 695}, "extra": {"message": "Detected direct use of jinja2. If not done properly, this may bypass HTML escaping which opens up the application to cross-site scripting (XSS) vulnerabilities. Prefer using the Flask method 'render_template()' and templates with a '.html' extension in order to prevent XSS.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://jinja.palletsprojects.com/en/2.11.x/api/#basics"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.xss.audit.direct-use-of-jinja2.direct-use-of-jinja2", "shortlink": "https://sg.run/RoKe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/dwain<PERSON>eren_dwains-lovelace-dashboard/custom_components/dwains_dashboard/lovelace/views/04.more_page_view.yaml:1:\n (approximate error location; error nearby after) error calling parser: found character that cannot start any token character 0 position 0 returned: 0", "path": "downloaded_repos/dwain<PERSON><PERSON><PERSON>_dwains-lovelace-dashboard/custom_components/dwains_dashboard/lovelace/views/04.more_page_view.yaml"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/dwainscheeren_dwains-lovelace-dashboard/custom_components/dwains_dashboard/js/dwains-dashboard.js:\n ", "path": "downloaded_repos/dwain<PERSON>eren_dwains-lovelace-dashboard/custom_components/dwains_dashboard/js/dwains-dashboard.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/dwainscheeren_dwains-lovelace-dashboard/custom_components/dwains_dashboard/js/dwains-dashboard.js:\n ", "path": "downloaded_repos/dwain<PERSON>eren_dwains-lovelace-dashboard/custom_components/dwains_dashboard/js/dwains-dashboard.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "message": "Timeout when running javascript.express.security.injection.raw-html-format.raw-html-format on downloaded_repos/dwainscheeren_dwains-lovelace-dashboard/custom_components/dwains_dashboard/js/dwains-dashboard.js:\n ", "path": "downloaded_repos/dwain<PERSON>eren_dwains-lovelace-dashboard/custom_components/dwains_dashboard/js/dwains-dashboard.js"}], "paths": {"scanned": ["downloaded_repos/dwain<PERSON><PERSON><PERSON>_dwains-lovelace-dashboard/.github/FUNDING.yml", "downloaded_repos/dwain<PERSON>eren_dwains-lovelace-dashboard/.gitignore", "downloaded_repos/dwain<PERSON><PERSON><PERSON>_dwains-lovelace-dashboard/.vscode/extensions.json", "downloaded_repos/dwain<PERSON><PERSON><PERSON>_dwains-lovelace-dashboard/LICENSE.md", "downloaded_repos/dwain<PERSON>eren_dwains-lovelace-dashboard/README.md", "downloaded_repos/dwain<PERSON><PERSON><PERSON>_dwains-lovelace-dashboard/custom_components/dwains_dashboard/__init__.py", "downloaded_repos/dwain<PERSON><PERSON><PERSON>_dwains-lovelace-dashboard/custom_components/dwains_dashboard/config_flow.py", "downloaded_repos/dwain<PERSON>eren_dwains-lovelace-dashboard/custom_components/dwains_dashboard/const.py", "downloaded_repos/dwain<PERSON>eren_dwains-lovelace-dashboard/custom_components/dwains_dashboard/js/.gitignore", "downloaded_repos/dwain<PERSON>eren_dwains-lovelace-dashboard/custom_components/dwains_dashboard/js/dwains-dashboard.js", "downloaded_repos/dwain<PERSON><PERSON>n_dwains-lovelace-dashboard/custom_components/dwains_dashboard/js/dwains-dashboard.js.LICENSE.txt", "downloaded_repos/dwain<PERSON>eren_dwains-lovelace-dashboard/custom_components/dwains_dashboard/js/package-lock.json", "downloaded_repos/dwain<PERSON><PERSON><PERSON>_dwains-lovelace-dashboard/custom_components/dwains_dashboard/js/package.json", "downloaded_repos/dwain<PERSON><PERSON><PERSON>_dwains-lovelace-dashboard/custom_components/dwains_dashboard/js/src/translations.js", "downloaded_repos/dwain<PERSON><PERSON><PERSON>_dwains-lovelace-dashboard/custom_components/dwains_dashboard/js/webpack.config.js", "downloaded_repos/dwain<PERSON>eren_dwains-lovelace-dashboard/custom_components/dwains_dashboard/js/webpack.config.js.gz", "downloaded_repos/dwain<PERSON>eren_dwains-lovelace-dashboard/custom_components/dwains_dashboard/load_dashboard.py", "downloaded_repos/dwain<PERSON>ere<PERSON>_dwains-lovelace-dashboard/custom_components/dwains_dashboard/load_plugins.py", "downloaded_repos/dwain<PERSON><PERSON>n_dwains-lovelace-dashboard/custom_components/dwains_dashboard/lovelace/ui-lovelace.yaml", "downloaded_repos/dwain<PERSON><PERSON>n_dwains-lovelace-dashboard/custom_components/dwains_dashboard/lovelace/views/01.homepage.yaml", "downloaded_repos/dwain<PERSON><PERSON>n_dwains-lovelace-dashboard/custom_components/dwains_dashboard/lovelace/views/02.devices.yaml", "downloaded_repos/dwain<PERSON><PERSON><PERSON>_dwains-lovelace-dashboard/custom_components/dwains_dashboard/lovelace/views/04.more_page_view.yaml", "downloaded_repos/dwain<PERSON><PERSON>n_dwains-lovelace-dashboard/custom_components/dwains_dashboard/lovelace/views/05.more_pages.yaml", "downloaded_repos/dwain<PERSON><PERSON><PERSON>_dwains-lovelace-dashboard/custom_components/dwains_dashboard/manifest.json", "downloaded_repos/dwain<PERSON><PERSON>n_dwains-lovelace-dashboard/custom_components/dwains_dashboard/notifications.py", "downloaded_repos/dwain<PERSON>eren_dwains-lovelace-dashboard/custom_components/dwains_dashboard/process_yaml.py", "downloaded_repos/dwain<PERSON><PERSON><PERSON>_dwains-lovelace-dashboard/custom_components/dwains_dashboard/sensor.py", "downloaded_repos/dwain<PERSON>eren_dwains-lovelace-dashboard/custom_components/dwains_dashboard/services.yaml", "downloaded_repos/dwain<PERSON><PERSON><PERSON>_dwains-lovelace-dashboard/custom_components/dwains_dashboard/translations/bg.json", "downloaded_repos/dwain<PERSON><PERSON>n_dwains-lovelace-dashboard/custom_components/dwains_dashboard/translations/en.json", "downloaded_repos/dwain<PERSON><PERSON><PERSON>_dwains-lovelace-dashboard/custom_components/dwains_dashboard/translations/pl.json", "downloaded_repos/dwain<PERSON><PERSON>n_dwains-lovelace-dashboard/custom_components/dwains_dashboard/translations/zh.json", "downloaded_repos/dwain<PERSON><PERSON>n_dwains-lovelace-dashboard/hacs.json"], "skipped": [{"path": "downloaded_repos/dwain<PERSON>eren_dwains-lovelace-dashboard/custom_components/dwains_dashboard/js/dwains-dashboard.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/dwain<PERSON>eren_dwains-lovelace-dashboard/custom_components/dwains_dashboard/js/dwains-dashboard.js.map", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/dwain<PERSON><PERSON><PERSON>_dwains-lovelace-dashboard/custom_components/dwains_dashboard/lovelace/views/04.more_page_view.yaml", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.719088077545166, "profiling_times": {"config_time": 5.867539167404175, "core_time": 7.692412853240967, "ignores_time": 0.0020143985748291016, "total_time": 13.562841415405273}, "parsing_time": {"total_time": 0.4964792728424072, "per_file_time": {"mean": 0.019859170913696295, "std_dev": 0.0011033524720187413}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 7.436342000961304, "per_file_time": {"mean": 0.07996066667700327, "std_dev": 0.29138481848583486}, "very_slow_stats": {"time_ratio": 0.7020931746073833, "count_ratio": 0.010752688172043012}, "very_slow_files": [{"fpath": "downloaded_repos/dwain<PERSON>eren_dwains-lovelace-dashboard/custom_components/dwains_dashboard/js/dwains-dashboard.js", "ftime": 5.221004962921143}]}, "matching_time": {"total_time": 0.44840383529663086, "per_file_and_rule_time": {"mean": 0.001540906650503886, "std_dev": 2.6583433200311265e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.1703641414642334, "per_def_and_rule_time": {"mean": 0.002077611481271139, "std_dev": 2.548380689232827e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}