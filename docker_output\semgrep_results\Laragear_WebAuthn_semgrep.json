{"version": "1.130.0", "results": [], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/Laragear_WebAuthn/.github/workflows/php.yml", "start": {"line": 19, "col": 72, "offset": 327}, "end": {"line": 19, "col": 74, "offset": 329}}]], "message": "Syntax error at line downloaded_repos/Laragear_WebAuthn/.github/workflows/php.yml:19:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `==` was unexpected", "path": "downloaded_repos/Laragear_WebAuthn/.github/workflows/php.yml", "spans": [{"file": "downloaded_repos/Laragear_WebAuthn/.github/workflows/php.yml", "start": {"line": 19, "col": 72, "offset": 327}, "end": {"line": 19, "col": 74, "offset": 329}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/Laragear_WebAuthn/src/ByteBuffer.php", "start": {"line": 236, "col": 34, "offset": 0}, "end": {"line": 236, "col": 35, "offset": 1}}]], "message": "Syntax error at line downloaded_repos/Laragear_WebAuthn/src/ByteBuffer.php:236:\n `-` was unexpected", "path": "downloaded_repos/Laragear_WebAuthn/src/ByteBuffer.php", "spans": [{"file": "downloaded_repos/Laragear_WebAuthn/src/ByteBuffer.php", "start": {"line": 236, "col": 34, "offset": 0}, "end": {"line": 236, "col": 35, "offset": 1}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/Laragear_WebAuthn/src/SharedPipes/CheckRelyingPartyIdContained.php", "start": {"line": 119, "col": 27, "offset": 0}, "end": {"line": 119, "col": 30, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/Laragear_WebAuthn/src/SharedPipes/CheckRelyingPartyIdContained.php:119:\n `...` was unexpected", "path": "downloaded_repos/Laragear_WebAuthn/src/SharedPipes/CheckRelyingPartyIdContained.php", "spans": [{"file": "downloaded_repos/Laragear_WebAuthn/src/SharedPipes/CheckRelyingPartyIdContained.php", "start": {"line": 119, "col": 27, "offset": 0}, "end": {"line": 119, "col": 30, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/Laragear_WebAuthn/src/WebAuthnData.php", "start": {"line": 13, "col": 33, "offset": 0}, "end": {"line": 13, "col": 41, "offset": 8}}, {"path": "downloaded_repos/Laragear_WebAuthn/src/WebAuthnData.php", "start": {"line": 13, "col": 66, "offset": 0}, "end": {"line": 13, "col": 74, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/Laragear_WebAuthn/src/WebAuthnData.php:13:\n `readonly` was unexpected", "path": "downloaded_repos/Laragear_WebAuthn/src/WebAuthnData.php", "spans": [{"file": "downloaded_repos/Laragear_WebAuthn/src/WebAuthnData.php", "start": {"line": 13, "col": 33, "offset": 0}, "end": {"line": 13, "col": 41, "offset": 8}}, {"file": "downloaded_repos/Laragear_WebAuthn/src/WebAuthnData.php", "start": {"line": 13, "col": 66, "offset": 0}, "end": {"line": 13, "col": 74, "offset": 8}}]}], "paths": {"scanned": ["downloaded_repos/Laragear_WebAuthn/.editorconfig", "downloaded_repos/Laragear_WebAuthn/.gitattributes", "downloaded_repos/Laragear_WebAuthn/.github/FUNDING.yml", "downloaded_repos/Laragear_WebAuthn/.github/ISSUE_TEMPLATE/bug_report.yml", "downloaded_repos/Laragear_WebAuthn/.github/ISSUE_TEMPLATE/config.yml", "downloaded_repos/Laragear_WebAuthn/.github/ISSUE_TEMPLATE/feature_request.yml", "downloaded_repos/Laragear_WebAuthn/.github/PULL_REQUEST_TEMPLATE.md", "downloaded_repos/Laragear_WebAuthn/.github/assets/support.png", "downloaded_repos/Laragear_WebAuthn/.github/dependabot.yml", "downloaded_repos/Laragear_WebAuthn/.github/workflows/php.yml", "downloaded_repos/Laragear_WebAuthn/.gitignore", "downloaded_repos/Laragear_WebAuthn/.styleci.yml", "downloaded_repos/Laragear_WebAuthn/DATABASE.md", "downloaded_repos/Laragear_WebAuthn/LICENSE.md", "downloaded_repos/Laragear_WebAuthn/README.md", "downloaded_repos/Laragear_WebAuthn/composer.json", "downloaded_repos/Laragear_WebAuthn/config/webauthn.php", "downloaded_repos/Laragear_WebAuthn/database/migrations/0000_00_00_000000_create_webauthn_credentials.php", "downloaded_repos/Laragear_WebAuthn/phpstan.neon", "downloaded_repos/Laragear_WebAuthn/phpunit.xml", "downloaded_repos/Laragear_WebAuthn/resources/js/webauthn.js", "downloaded_repos/Laragear_WebAuthn/resources/views/livewire/attest.blade.php", "downloaded_repos/Laragear_WebAuthn/src/Assertion/Creator/AssertionCreation.php", "downloaded_repos/Laragear_WebAuthn/src/Assertion/Creator/AssertionCreator.php", "downloaded_repos/Laragear_WebAuthn/src/Assertion/Creator/Pipes/AddConfiguration.php", "downloaded_repos/Laragear_WebAuthn/src/Assertion/Creator/Pipes/CreateAssertionChallenge.php", "downloaded_repos/Laragear_WebAuthn/src/Assertion/Creator/Pipes/MayRequireUserVerification.php", "downloaded_repos/Laragear_WebAuthn/src/Assertion/Creator/Pipes/MayRetrieveCredentialsIdForUser.php", "downloaded_repos/Laragear_WebAuthn/src/Assertion/Validator/AssertionValidation.php", "downloaded_repos/Laragear_WebAuthn/src/Assertion/Validator/AssertionValidator.php", "downloaded_repos/Laragear_WebAuthn/src/Assertion/Validator/Pipes/CheckChallengeSame.php", "downloaded_repos/Laragear_WebAuthn/src/Assertion/Validator/Pipes/CheckCredentialIsForUser.php", "downloaded_repos/Laragear_WebAuthn/src/Assertion/Validator/Pipes/CheckCredentialIsWebAuthnGet.php", "downloaded_repos/Laragear_WebAuthn/src/Assertion/Validator/Pipes/CheckPublicKeyCounterCorrect.php", "downloaded_repos/Laragear_WebAuthn/src/Assertion/Validator/Pipes/CheckPublicKeySignature.php", "downloaded_repos/Laragear_WebAuthn/src/Assertion/Validator/Pipes/CheckRelyingPartyHashSame.php", "downloaded_repos/Laragear_WebAuthn/src/Assertion/Validator/Pipes/CheckRelyingPartyIdContained.php", "downloaded_repos/Laragear_WebAuthn/src/Assertion/Validator/Pipes/CheckTypeIsPublicKey.php", "downloaded_repos/Laragear_WebAuthn/src/Assertion/Validator/Pipes/CheckUserInteraction.php", "downloaded_repos/Laragear_WebAuthn/src/Assertion/Validator/Pipes/CompileAuthenticatorData.php", "downloaded_repos/Laragear_WebAuthn/src/Assertion/Validator/Pipes/CompileClientDataJson.php", "downloaded_repos/Laragear_WebAuthn/src/Assertion/Validator/Pipes/FireCredentialAssertedEvent.php", "downloaded_repos/Laragear_WebAuthn/src/Assertion/Validator/Pipes/IncrementCredentialCounter.php", "downloaded_repos/Laragear_WebAuthn/src/Assertion/Validator/Pipes/RetrieveChallenge.php", "downloaded_repos/Laragear_WebAuthn/src/Assertion/Validator/Pipes/RetrievesCredentialId.php", "downloaded_repos/Laragear_WebAuthn/src/Attestation/AttestationObject.php", "downloaded_repos/Laragear_WebAuthn/src/Attestation/AuthenticatorData.php", "downloaded_repos/Laragear_WebAuthn/src/Attestation/Creator/AttestationCreation.php", "downloaded_repos/Laragear_WebAuthn/src/Attestation/Creator/AttestationCreator.php", "downloaded_repos/Laragear_WebAuthn/src/Attestation/Creator/Pipes/AddAcceptedAlgorithms.php", "downloaded_repos/Laragear_WebAuthn/src/Attestation/Creator/Pipes/AddRelyingParty.php", "downloaded_repos/Laragear_WebAuthn/src/Attestation/Creator/Pipes/AddUserDescriptor.php", "downloaded_repos/Laragear_WebAuthn/src/Attestation/Creator/Pipes/CreateAttestationChallenge.php", "downloaded_repos/Laragear_WebAuthn/src/Attestation/Creator/Pipes/MayPreventDuplicateCredentials.php", "downloaded_repos/Laragear_WebAuthn/src/Attestation/Creator/Pipes/MayRequireUserVerification.php", "downloaded_repos/Laragear_WebAuthn/src/Attestation/Creator/Pipes/SetResidentKeyConfiguration.php", "downloaded_repos/Laragear_WebAuthn/src/Attestation/Formats/Format.php", "downloaded_repos/Laragear_WebAuthn/src/Attestation/Formats/None.php", "downloaded_repos/Laragear_WebAuthn/src/Attestation/Validator/AttestationValidation.php", "downloaded_repos/Laragear_WebAuthn/src/Attestation/Validator/AttestationValidator.php", "downloaded_repos/Laragear_WebAuthn/src/Attestation/Validator/Pipes/AttestationIsForCreation.php", "downloaded_repos/Laragear_WebAuthn/src/Attestation/Validator/Pipes/CheckChallengeSame.php", "downloaded_repos/Laragear_WebAuthn/src/Attestation/Validator/Pipes/CheckRelyingPartyHashSame.php", "downloaded_repos/Laragear_WebAuthn/src/Attestation/Validator/Pipes/CheckRelyingPartyIdContained.php", "downloaded_repos/Laragear_WebAuthn/src/Attestation/Validator/Pipes/CheckUserInteraction.php", "downloaded_repos/Laragear_WebAuthn/src/Attestation/Validator/Pipes/CompileAttestationObject.php", "downloaded_repos/Laragear_WebAuthn/src/Attestation/Validator/Pipes/CompileClientDataJson.php", "downloaded_repos/Laragear_WebAuthn/src/Attestation/Validator/Pipes/CredentialIdShouldNotBeDuplicated.php", "downloaded_repos/Laragear_WebAuthn/src/Attestation/Validator/Pipes/FireCredentialAttestedEvent.php", "downloaded_repos/Laragear_WebAuthn/src/Attestation/Validator/Pipes/MakeWebAuthnCredential.php", "downloaded_repos/Laragear_WebAuthn/src/Attestation/Validator/Pipes/RetrieveChallenge.php", "downloaded_repos/Laragear_WebAuthn/src/Auth/WebAuthnUserProvider.php", "downloaded_repos/Laragear_WebAuthn/src/ByteBuffer.php", "downloaded_repos/Laragear_WebAuthn/src/CborDecoder.php", "downloaded_repos/Laragear_WebAuthn/src/Challenge/Challenge.php", "downloaded_repos/Laragear_WebAuthn/src/Challenge/SessionChallengeRepository.php", "downloaded_repos/Laragear_WebAuthn/src/ClientDataJson.php", "downloaded_repos/Laragear_WebAuthn/src/Console/WebAuthnInstallCommand.php", "downloaded_repos/Laragear_WebAuthn/src/Contracts/WebAuthnAuthenticatable.php", "downloaded_repos/Laragear_WebAuthn/src/Contracts/WebAuthnChallengeRepository.php", "downloaded_repos/Laragear_WebAuthn/src/Contracts/WebAuthnException.php", "downloaded_repos/Laragear_WebAuthn/src/Enums/Extension.php", "downloaded_repos/Laragear_WebAuthn/src/Enums/Formats.php", "downloaded_repos/Laragear_WebAuthn/src/Enums/Platform.php", "downloaded_repos/Laragear_WebAuthn/src/Enums/ResidentKey.php", "downloaded_repos/Laragear_WebAuthn/src/Enums/Transport.php", "downloaded_repos/Laragear_WebAuthn/src/Enums/UserVerification.php", "downloaded_repos/Laragear_WebAuthn/src/Events/CredentialAsserted.php", "downloaded_repos/Laragear_WebAuthn/src/Events/CredentialAttested.php", "downloaded_repos/Laragear_WebAuthn/src/Events/CredentialCloned.php", "downloaded_repos/Laragear_WebAuthn/src/Events/CredentialCreated.php", "downloaded_repos/Laragear_WebAuthn/src/Events/CredentialDisabled.php", "downloaded_repos/Laragear_WebAuthn/src/Events/CredentialEnabled.php", "downloaded_repos/Laragear_WebAuthn/src/Exceptions/AssertionException.php", "downloaded_repos/Laragear_WebAuthn/src/Exceptions/AttestationException.php", "downloaded_repos/Laragear_WebAuthn/src/Exceptions/DataException.php", "downloaded_repos/Laragear_WebAuthn/src/Http/Requests/AssertedRequest.php", "downloaded_repos/Laragear_WebAuthn/src/Http/Requests/AssertionRequest.php", "downloaded_repos/Laragear_WebAuthn/src/Http/Requests/AttestationRequest.php", "downloaded_repos/Laragear_WebAuthn/src/Http/Requests/AttestedRequest.php", "downloaded_repos/Laragear_WebAuthn/src/Http/Routes.php", "downloaded_repos/Laragear_WebAuthn/src/JsonTransport.php", "downloaded_repos/Laragear_WebAuthn/src/Models/WebAuthnCredential.php", "downloaded_repos/Laragear_WebAuthn/src/SharedPipes/CheckChallengeSame.php", "downloaded_repos/Laragear_WebAuthn/src/SharedPipes/CheckRelyingPartyHashSame.php", "downloaded_repos/Laragear_WebAuthn/src/SharedPipes/CheckRelyingPartyIdContained.php", "downloaded_repos/Laragear_WebAuthn/src/SharedPipes/CheckUserInteraction.php", "downloaded_repos/Laragear_WebAuthn/src/SharedPipes/CompileClientDataJson.php", "downloaded_repos/Laragear_WebAuthn/src/SharedPipes/RequireWebAuthnUser.php", "downloaded_repos/Laragear_WebAuthn/src/SharedPipes/RetrieveChallenge.php", "downloaded_repos/Laragear_WebAuthn/src/SharedPipes/ThrowsCeremonyException.php", "downloaded_repos/Laragear_WebAuthn/src/WebAuthnAuthentication.php", "downloaded_repos/Laragear_WebAuthn/src/WebAuthnData.php", "downloaded_repos/Laragear_WebAuthn/src/WebAuthnServiceProvider.php", "downloaded_repos/Laragear_WebAuthn/stubs/controllers/WebAuthnLoginController.php", "downloaded_repos/Laragear_WebAuthn/stubs/controllers/WebAuthnRegisterController.php"], "skipped": [{"path": "downloaded_repos/Laragear_WebAuthn/.github/workflows/php.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/Laragear_WebAuthn/src/ByteBuffer.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/Laragear_WebAuthn/src/SharedPipes/CheckRelyingPartyIdContained.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/Laragear_WebAuthn/src/WebAuthnData.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/Laragear_WebAuthn/tests/Assertion/CreatorTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Laragear_WebAuthn/tests/Assertion/ValidationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Laragear_WebAuthn/tests/Attestation/CreatorTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Laragear_WebAuthn/tests/Attestation/ValidationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Laragear_WebAuthn/tests/Auth/EloquentWebAuthnProviderTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Laragear_WebAuthn/tests/ChallengeRepositoryTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Laragear_WebAuthn/tests/Console/WebAuthnInstallCommandTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Laragear_WebAuthn/tests/DatabaseTestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Laragear_WebAuthn/tests/FakeAuthenticator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Laragear_WebAuthn/tests/Http/Controllers/StubControllersTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Laragear_WebAuthn/tests/Http/Requests/AssertedRequestTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Laragear_WebAuthn/tests/Http/Requests/AssertionRequestTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Laragear_WebAuthn/tests/Http/Requests/AttestationRequestTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Laragear_WebAuthn/tests/Http/Requests/AttestedRequestTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Laragear_WebAuthn/tests/Models/WebAuthnCredentialTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Laragear_WebAuthn/tests/ServiceProviderTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Laragear_WebAuthn/tests/Stubs/WebAuthnAuthenticatableUser.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Laragear_WebAuthn/tests/TestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Laragear_WebAuthn/tests/WebAuthnAuthenticationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Laragear_WebAuthn/tests/WebAuthnTest.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 2.1374340057373047, "profiling_times": {"config_time": 6.218827247619629, "core_time": 3.4687106609344482, "ignores_time": 0.0016682147979736328, "total_time": 9.690233945846558}, "parsing_time": {"total_time": 0.8747859001159668, "per_file_time": {"mean": 0.008252697170905349, "std_dev": 0.000297134886234061}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 2.3734922409057617, "per_file_time": {"mean": 0.007022166393212307, "std_dev": 0.0005618722281480444}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.519512414932251, "per_file_and_rule_time": {"mean": 0.001492851767046698, "std_dev": 2.8192935194989076e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.07444453239440918, "per_def_and_rule_time": {"mean": 0.00022976707529138638, "std_dev": 1.201653762600506e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}