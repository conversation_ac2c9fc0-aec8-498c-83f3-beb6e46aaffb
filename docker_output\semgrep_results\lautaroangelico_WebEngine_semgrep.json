{"version": "1.130.0", "results": [{"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/modules_manager.php", "start": {"line": 78, "col": 17, "offset": 2071}, "end": {"line": 78, "col": 26, "offset": 2080}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/website_settings.php", "start": {"line": 66, "col": 19, "offset": 1878}, "end": {"line": 66, "col": 77, "offset": 1936}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/website_settings.php", "start": {"line": 107, "col": 19, "offset": 4178}, "end": {"line": 107, "col": 80, "offset": 4239}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "path": "downloaded_repos/lautaroangelico_WebEngine/includes/classes/class.account.php", "start": {"line": 401, "col": 6, "offset": 16069}, "end": {"line": 401, "col": 27, "offset": 16090}, "extra": {"message": "Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues", "metadata": {"cwe": ["CWE-697: Incorrect Comparison"], "references": ["https://www.php.net/manual/en/types.comparisons.php", "https://web.archive.org/web/**************/https://www.whitehatsec.com/blog/magic-hashes/"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.md5-loose-equality.md5-loose-equality", "shortlink": "https://sg.run/Do4G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/lautaroangelico_WebEngine/includes/classes/class.cache.php", "start": {"line": 85, "col": 4, "offset": 2165}, "end": {"line": 85, "col": 48, "offset": 2209}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/lautaroangelico_WebEngine/includes/classes/class.cache.php", "start": {"line": 93, "col": 4, "offset": 2383}, "end": {"line": 93, "col": 49, "offset": 2428}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/lautaroangelico_WebEngine/includes/classes/class.news.php", "start": {"line": 197, "col": 5, "offset": 4902}, "end": {"line": 197, "col": 18, "offset": 4915}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/lautaroangelico_WebEngine/includes/classes/class.news.php", "start": {"line": 406, "col": 4, "offset": 12735}, "end": {"line": 406, "col": 32, "offset": 12763}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/lautaroangelico_WebEngine/includes/classes/class.news.php", "start": {"line": 410, "col": 4, "offset": 12822}, "end": {"line": 410, "col": 37, "offset": 12855}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/lautaroangelico_WebEngine/includes/classes/phpmailer/PHPMailer.php", "start": {"line": 1769, "col": 17, "offset": 56726}, "end": {"line": 1769, "col": 48, "offset": 56757}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/lautaroangelico_WebEngine/includes/classes/phpmailer/PHPMailer.php", "start": {"line": 1795, "col": 13, "offset": 57836}, "end": {"line": 1795, "col": 44, "offset": 57867}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/lautaroangelico_WebEngine/includes/classes/phpmailer/PHPMailer.php", "start": {"line": 3111, "col": 18, "offset": 109753}, "end": {"line": 3111, "col": 31, "offset": 109766}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/lautaroangelico_WebEngine/includes/classes/phpmailer/PHPMailer.php", "start": {"line": 3114, "col": 22, "offset": 109874}, "end": {"line": 3114, "col": 37, "offset": 109889}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/lautaroangelico_WebEngine/includes/classes/phpmailer/PHPMailer.php", "start": {"line": 3120, "col": 22, "offset": 110223}, "end": {"line": 3120, "col": 37, "offset": 110238}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/lautaroangelico_WebEngine/templates/default/js/events.js", "start": {"line": 10, "col": 5, "offset": 276}, "end": {"line": 10, "col": 66, "offset": 337}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/lautaroangelico_WebEngine/templates/default/js/events.js", "start": {"line": 11, "col": 5, "offset": 342}, "end": {"line": 11, "col": 66, "offset": 403}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/lautaroangelico_WebEngine/templates/default/js/events.js", "start": {"line": 34, "col": 4, "offset": 853}, "end": {"line": 34, "col": 70, "offset": 919}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/lautaroangelico_WebEngine/templates/default/js/events.js", "start": {"line": 35, "col": 4, "offset": 923}, "end": {"line": 35, "col": 70, "offset": 989}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/lautaroangelico_WebEngine/templates/default/js/events.js", "start": {"line": 78, "col": 4, "offset": 2038}, "end": {"line": 78, "col": 67, "offset": 2101}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/lautaroangelico_WebEngine/templates/default/js/events.js", "start": {"line": 81, "col": 5, "offset": 2136}, "end": {"line": 81, "col": 127, "offset": 2258}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/lautaroangelico_WebEngine/templates/default/js/events.js", "start": {"line": 83, "col": 5, "offset": 2275}, "end": {"line": 83, "col": 108, "offset": 2378}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/lautaroangelico_WebEngine/templates/default/js/main.js", "start": {"line": 33, "col": 6, "offset": 1034}, "end": {"line": 33, "col": 59, "offset": 1087}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/lautaroangelico_WebEngine/templates/default/js/main.js", "start": {"line": 98, "col": 5, "offset": 2724}, "end": {"line": 98, "col": 66, "offset": 2785}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/lautaroangelico_WebEngine/templates/default/js/main.js", "start": {"line": 101, "col": 5, "offset": 2828}, "end": {"line": 101, "col": 65, "offset": 2888}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/lautaroangelico_WebEngine/templates/default/js/main.js", "start": {"line": 128, "col": 4, "offset": 3541}, "end": {"line": 128, "col": 84, "offset": 3621}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/lautaroangelico_WebEngine/templates/default/js/main.js", "start": {"line": 129, "col": 4, "offset": 3625}, "end": {"line": 129, "col": 82, "offset": 3703}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/lautaroangelico_WebEngine/templates/default/js/main.js", "start": {"line": 130, "col": 4, "offset": 3707}, "end": {"line": 130, "col": 84, "offset": 3787}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/lautaroangelico_WebEngine/templates/default/js/main.js", "start": {"line": 131, "col": 4, "offset": 3791}, "end": {"line": 131, "col": 82, "offset": 3869}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/lautaroangelico_WebEngine/templates/default/js/main.js", "start": {"line": 141, "col": 3, "offset": 4004}, "end": {"line": 141, "col": 79, "offset": 4080}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/lautaroangelico_WebEngine/templates/default/js/main.js", "start": {"line": 143, "col": 3, "offset": 4140}, "end": {"line": 143, "col": 80, "offset": 4217}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": "Syntax error", "message": "Syntax error at line downloaded_repos/lautaroangelico_WebEngine/includes/config/webengine.json:1:\n missing element", "path": "downloaded_repos/lautaroangelico_WebEngine/includes/config/webengine.json"}], "paths": {"scanned": ["downloaded_repos/lautaroangelico_WebEngine/.htaccess", "downloaded_repos/lautaroangelico_WebEngine/LICENSE", "downloaded_repos/lautaroangelico_WebEngine/README.md", "downloaded_repos/lautaroangelico_WebEngine/admincp/.htaccess", "downloaded_repos/lautaroangelico_WebEngine/admincp/css/index.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/css/metisMenu.min.css", "downloaded_repos/lautaroangelico_WebEngine/admincp/css/webengine.css", "downloaded_repos/lautaroangelico_WebEngine/admincp/favicon.ico", "downloaded_repos/lautaroangelico_WebEngine/admincp/img/index.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/img/logo.jpg", "downloaded_repos/lautaroangelico_WebEngine/admincp/inc/.htaccess", "downloaded_repos/lautaroangelico_WebEngine/admincp/inc/check.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/inc/functions.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/index.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/js/index.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/.htaccess", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/accountinfo.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/accountsfromip.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/addnews.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/addnewstranslation.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/admincp_access.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/banaccount.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/blockedips.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/cachemanager.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/connection_settings.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/creditsconfigs.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/creditsmanager.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/cronmanager.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/editcharacter.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/editnews.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/editnewstranslation.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/home.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/latestbans.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/latestpaypal.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/managenews.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/mconfig/addstats.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/mconfig/buyzen.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/mconfig/castlesiege.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/mconfig/clearpk.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/mconfig/clearskilltree.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/mconfig/contact.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/mconfig/donation.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/mconfig/downloads.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/mconfig/email.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/mconfig/forgotpassword.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/mconfig/login.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/mconfig/myaccount.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/mconfig/myemail.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/mconfig/mypassword.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/mconfig/news.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/mconfig/paypal.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/mconfig/profiles.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/mconfig/rankings.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/mconfig/register.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/mconfig/reset.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/mconfig/resetstats.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/mconfig/unstick.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/mconfig/vote.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/modules_manager.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/navbar.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/newregistrations.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/onlineaccounts.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/phrases.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/plugin_install.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/plugins.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/searchaccount.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/searchban.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/searchcharacter.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/topvotes.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/usercp.php", "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/website_settings.php", "downloaded_repos/lautaroangelico_WebEngine/api/.htaccess", "downloaded_repos/lautaroangelico_WebEngine/api/castlesiege.php", "downloaded_repos/lautaroangelico_WebEngine/api/cron.php", "downloaded_repos/lautaroangelico_WebEngine/api/events.php", "downloaded_repos/lautaroangelico_WebEngine/api/guildmark.php", "downloaded_repos/lautaroangelico_WebEngine/api/index.php", "downloaded_repos/lautaroangelico_WebEngine/api/paypal.php", "downloaded_repos/lautaroangelico_WebEngine/api/servertime.php", "downloaded_repos/lautaroangelico_WebEngine/api/version.php", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ad.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ae.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/af.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ag.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ai.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/al.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/am.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/an.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ao.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ar.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/as.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/at.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/au.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/aw.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ax.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/az.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ba.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/bb.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/bd.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/be.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/bf.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/bg.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/bh.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/bi.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/bj.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/bm.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/bn.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/bo.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/br.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/bs.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/bt.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/bv.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/bw.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/by.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/bz.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ca.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/catalonia.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/cc.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/cd.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/cf.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/cg.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ch.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ci.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ck.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/cl.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/cm.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/cn.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/co.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/cr.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/cs.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/cu.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/cv.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/cx.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/cy.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/cz.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/de.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/default.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/dj.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/dk.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/dm.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/do.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/dz.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ec.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ee.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/eg.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/eh.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/england.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/er.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/es.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/et.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/europeanunion.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/fam.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/fi.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/fj.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/fk.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/fm.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/fo.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/fr.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ga.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/gb.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/gd.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ge.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/gf.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/gh.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/gi.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/gl.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/gm.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/gn.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/gp.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/gq.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/gr.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/gs.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/gt.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/gu.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/gw.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/gy.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/hk.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/hm.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/hn.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/hr.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ht.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/hu.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/id.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ie.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/il.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/in.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/index.html", "downloaded_repos/lautaroangelico_WebEngine/img/flags/io.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/iq.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ir.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/is.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/it.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/jm.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/jo.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/jp.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ke.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/kg.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/kh.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ki.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/km.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/kn.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/kp.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/kr.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/kw.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ky.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/kz.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/la.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/lb.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/lc.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/li.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/lk.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/lr.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ls.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/lt.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/lu.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/lv.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ly.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ma.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/mc.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/md.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/me.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/mg.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/mh.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/mk.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ml.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/mm.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/mn.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/mo.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/mp.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/mq.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/mr.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ms.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/mt.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/mu.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/mv.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/mw.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/mx.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/my.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/mz.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/na.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/nc.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ne.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/nf.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ng.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ni.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/nl.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/no.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/np.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/nr.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/nu.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/nz.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/om.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/pa.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/pe.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/pf.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/pg.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ph.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/pk.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/pl.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/pm.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/pn.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/pr.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ps.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/pt.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/pw.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/py.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/qa.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/re.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ro.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/rs.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ru.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/rw.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/sa.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/sb.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/sc.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/scotland.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/sd.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/se.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/sg.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/sh.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/si.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/sj.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/sk.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/sl.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/sm.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/sn.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/so.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/sr.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/st.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/sv.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/sy.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/sz.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/tc.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/td.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/tf.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/tg.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/th.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/tj.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/tk.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/tl.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/tm.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/tn.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/to.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/tr.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/tt.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/tv.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/tw.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/tz.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ua.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ug.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/um.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/us.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/uy.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/uz.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/va.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/vc.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ve.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/vg.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/vi.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/vn.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/vu.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/wales.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/wf.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ws.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/ye.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/yt.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/za.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/zm.gif", "downloaded_repos/lautaroangelico_WebEngine/img/flags/zw.gif", "downloaded_repos/lautaroangelico_WebEngine/img/index.html", "downloaded_repos/lautaroangelico_WebEngine/img/offline.png", "downloaded_repos/lautaroangelico_WebEngine/img/online.png", "downloaded_repos/lautaroangelico_WebEngine/img/webengine.jpg", "downloaded_repos/lautaroangelico_WebEngine/includes/.htaccess", "downloaded_repos/lautaroangelico_WebEngine/includes/cache/blocked_ip.cache", "downloaded_repos/lautaroangelico_WebEngine/includes/cache/castle_siege.cache", "downloaded_repos/lautaroangelico_WebEngine/includes/cache/character_country.cache", "downloaded_repos/lautaroangelico_WebEngine/includes/cache/downloads.cache", "downloaded_repos/lautaroangelico_WebEngine/includes/cache/news/translations/.gitignore", "downloaded_repos/lautaroangelico_WebEngine/includes/cache/news.cache", "downloaded_repos/lautaroangelico_WebEngine/includes/cache/online_characters.cache", "downloaded_repos/lautaroangelico_WebEngine/includes/cache/plugins.cache", "downloaded_repos/lautaroangelico_WebEngine/includes/cache/profiles/guilds/.gitignore", "downloaded_repos/lautaroangelico_WebEngine/includes/cache/profiles/players/.gitignore", "downloaded_repos/lautaroangelico_WebEngine/includes/cache/rankings_gens.cache", "downloaded_repos/lautaroangelico_WebEngine/includes/cache/rankings_gr.cache", "downloaded_repos/lautaroangelico_WebEngine/includes/cache/rankings_guilds.cache", "downloaded_repos/lautaroangelico_WebEngine/includes/cache/rankings_level.cache", "downloaded_repos/lautaroangelico_WebEngine/includes/cache/rankings_master.cache", "downloaded_repos/lautaroangelico_WebEngine/includes/cache/rankings_online.cache", "downloaded_repos/lautaroangelico_WebEngine/includes/cache/rankings_pk.cache", "downloaded_repos/lautaroangelico_WebEngine/includes/cache/rankings_resets.cache", "downloaded_repos/lautaroangelico_WebEngine/includes/cache/rankings_votes.cache", "downloaded_repos/lautaroangelico_WebEngine/includes/cache/server_info.cache", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/class.account.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/class.cache.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/class.castlesiege.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/class.character.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/class.common.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/class.connection.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/class.credits.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/class.cron.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/class.database.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/class.email.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/class.handler.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/class.login.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/class.news.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/class.plugins.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/class.profiles.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/class.rankings.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/class.validator.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/class.vote.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/paypal/PaypalIPN.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/paypal/cert/cacert.pem", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/phpmailer/DSNConfigurator.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/phpmailer/Exception.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/phpmailer/OAuth.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/phpmailer/OAuthTokenProvider.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/phpmailer/PHPMailer.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/phpmailer/POP3.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/phpmailer/SMTP.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/phpmailer/autoload.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/recaptcha/ReCaptcha/ReCaptcha.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/recaptcha/ReCaptcha/RequestMethod/Curl.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/recaptcha/ReCaptcha/RequestMethod/CurlPost.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/recaptcha/ReCaptcha/RequestMethod/Post.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/recaptcha/ReCaptcha/RequestMethod/Socket.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/recaptcha/ReCaptcha/RequestMethod/SocketPost.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/recaptcha/ReCaptcha/RequestMethod.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/recaptcha/ReCaptcha/RequestParameters.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/recaptcha/ReCaptcha/Response.php", "downloaded_repos/lautaroangelico_WebEngine/includes/classes/recaptcha/autoload.php", "downloaded_repos/lautaroangelico_WebEngine/includes/config/castlesiege.json", "downloaded_repos/lautaroangelico_WebEngine/includes/config/compatibility.php", "downloaded_repos/lautaroangelico_WebEngine/includes/config/custom.tables.php", "downloaded_repos/lautaroangelico_WebEngine/includes/config/email.xml", "downloaded_repos/lautaroangelico_WebEngine/includes/config/igcn.tables.php", "downloaded_repos/lautaroangelico_WebEngine/includes/config/modules/contact.xml", "downloaded_repos/lautaroangelico_WebEngine/includes/config/modules/donation.paypal.xml", "downloaded_repos/lautaroangelico_WebEngine/includes/config/modules/donation.xml", "downloaded_repos/lautaroangelico_WebEngine/includes/config/modules/downloads.xml", "downloaded_repos/lautaroangelico_WebEngine/includes/config/modules/forgotpassword.xml", "downloaded_repos/lautaroangelico_WebEngine/includes/config/modules/login.xml", "downloaded_repos/lautaroangelico_WebEngine/includes/config/modules/news.xml", "downloaded_repos/lautaroangelico_WebEngine/includes/config/modules/profiles.xml", "downloaded_repos/lautaroangelico_WebEngine/includes/config/modules/rankings.xml", "downloaded_repos/lautaroangelico_WebEngine/includes/config/modules/register.xml", "downloaded_repos/lautaroangelico_WebEngine/includes/config/modules/usercp.addstats.xml", "downloaded_repos/lautaroangelico_WebEngine/includes/config/modules/usercp.buyzen.xml", "downloaded_repos/lautaroangelico_WebEngine/includes/config/modules/usercp.clearpk.xml", "downloaded_repos/lautaroangelico_WebEngine/includes/config/modules/usercp.clearskilltree.xml", "downloaded_repos/lautaroangelico_WebEngine/includes/config/modules/usercp.myaccount.xml", "downloaded_repos/lautaroangelico_WebEngine/includes/config/modules/usercp.myemail.xml", "downloaded_repos/lautaroangelico_WebEngine/includes/config/modules/usercp.mypassword.xml", "downloaded_repos/lautaroangelico_WebEngine/includes/config/modules/usercp.reset.xml", "downloaded_repos/lautaroangelico_WebEngine/includes/config/modules/usercp.resetstats.xml", "downloaded_repos/lautaroangelico_WebEngine/includes/config/modules/usercp.unstick.xml", "downloaded_repos/lautaroangelico_WebEngine/includes/config/modules/usercp.vote.xml", "downloaded_repos/lautaroangelico_WebEngine/includes/config/navbar.json", "downloaded_repos/lautaroangelico_WebEngine/includes/config/timezone.php", "downloaded_repos/lautaroangelico_WebEngine/includes/config/usercp.json", "downloaded_repos/lautaroangelico_WebEngine/includes/config/webengine.json", "downloaded_repos/lautaroangelico_WebEngine/includes/config/webengine.json.default", "downloaded_repos/lautaroangelico_WebEngine/includes/config/webengine.tables.php", "downloaded_repos/lautaroangelico_WebEngine/includes/config/writable.paths.json", "downloaded_repos/lautaroangelico_WebEngine/includes/config/xteam.tables.php", "downloaded_repos/lautaroangelico_WebEngine/includes/cron/account_country.php", "downloaded_repos/lautaroangelico_WebEngine/includes/cron/castle_siege.php", "downloaded_repos/lautaroangelico_WebEngine/includes/cron/character_country.php", "downloaded_repos/lautaroangelico_WebEngine/includes/cron/cron.php", "downloaded_repos/lautaroangelico_WebEngine/includes/cron/gens_ranking.php", "downloaded_repos/lautaroangelico_WebEngine/includes/cron/grandresets_ranking.php", "downloaded_repos/lautaroangelico_WebEngine/includes/cron/guilds_ranking.php", "downloaded_repos/lautaroangelico_WebEngine/includes/cron/killers_ranking.php", "downloaded_repos/lautaroangelico_WebEngine/includes/cron/levels_ranking.php", "downloaded_repos/lautaroangelico_WebEngine/includes/cron/masterlevel_ranking.php", "downloaded_repos/lautaroangelico_WebEngine/includes/cron/online_characters.php", "downloaded_repos/lautaroangelico_WebEngine/includes/cron/online_ranking.php", "downloaded_repos/lautaroangelico_WebEngine/includes/cron/resets_ranking.php", "downloaded_repos/lautaroangelico_WebEngine/includes/cron/server_info.php", "downloaded_repos/lautaroangelico_WebEngine/includes/cron/temporal_bans.php", "downloaded_repos/lautaroangelico_WebEngine/includes/cron/votes_ranking.php", "downloaded_repos/lautaroangelico_WebEngine/includes/emails/ADMIN_CHANGE_EMAIL.txt", "downloaded_repos/lautaroangelico_WebEngine/includes/emails/ADMIN_CHANGE_PASSWORD.txt", "downloaded_repos/lautaroangelico_WebEngine/includes/emails/CHANGE_EMAIL_VERIFICATION.txt", "downloaded_repos/lautaroangelico_WebEngine/includes/emails/CHANGE_PASSWORD.txt", "downloaded_repos/lautaroangelico_WebEngine/includes/emails/CHANGE_PASSWORD_EMAIL_VERIFICATION.txt", "downloaded_repos/lautaroangelico_WebEngine/includes/emails/PASSWORD_RECOVERY_COMPLETED.txt", "downloaded_repos/lautaroangelico_WebEngine/includes/emails/PASSWORD_RECOVERY_REQUEST.txt", "downloaded_repos/lautaroangelico_WebEngine/includes/emails/WELCOME_EMAIL.txt", "downloaded_repos/lautaroangelico_WebEngine/includes/emails/WELCOME_EMAIL_VERIFICATION.txt", "downloaded_repos/lautaroangelico_WebEngine/includes/error.html", "downloaded_repos/lautaroangelico_WebEngine/includes/functions.php", "downloaded_repos/lautaroangelico_WebEngine/includes/languages/cn/language.php", "downloaded_repos/lautaroangelico_WebEngine/includes/languages/en/language.php", "downloaded_repos/lautaroangelico_WebEngine/includes/languages/es/language.php", "downloaded_repos/lautaroangelico_WebEngine/includes/languages/lt/language.php", "downloaded_repos/lautaroangelico_WebEngine/includes/languages/ph/language.php", "downloaded_repos/lautaroangelico_WebEngine/includes/languages/pt/language.php", "downloaded_repos/lautaroangelico_WebEngine/includes/languages/ro/language.php", "downloaded_repos/lautaroangelico_WebEngine/includes/languages/ru/language.php", "downloaded_repos/lautaroangelico_WebEngine/includes/logs/database_errors.log", "downloaded_repos/lautaroangelico_WebEngine/includes/logs/php_errors.log", "downloaded_repos/lautaroangelico_WebEngine/includes/plugins/.gitignore", "downloaded_repos/lautaroangelico_WebEngine/includes/webengine.php", "downloaded_repos/lautaroangelico_WebEngine/index.php", "downloaded_repos/lautaroangelico_WebEngine/install/.htaccess", "downloaded_repos/lautaroangelico_WebEngine/install/definitions.php", "downloaded_repos/lautaroangelico_WebEngine/install/index.php", "downloaded_repos/lautaroangelico_WebEngine/install/install.php", "downloaded_repos/lautaroangelico_WebEngine/install/install_intro.php", "downloaded_repos/lautaroangelico_WebEngine/install/install_step_1.php", "downloaded_repos/lautaroangelico_WebEngine/install/install_step_2.php", "downloaded_repos/lautaroangelico_WebEngine/install/install_step_3.php", "downloaded_repos/lautaroangelico_WebEngine/install/install_step_4.php", "downloaded_repos/lautaroangelico_WebEngine/install/install_step_5.php", "downloaded_repos/lautaroangelico_WebEngine/install/loader.php", "downloaded_repos/lautaroangelico_WebEngine/install/sql/.htaccess", "downloaded_repos/lautaroangelico_WebEngine/install/sql/WEBENGINE_ACCOUNT_COUNTRY.txt", "downloaded_repos/lautaroangelico_WebEngine/install/sql/WEBENGINE_BANS.txt", "downloaded_repos/lautaroangelico_WebEngine/install/sql/WEBENGINE_BAN_LOG.txt", "downloaded_repos/lautaroangelico_WebEngine/install/sql/WEBENGINE_BLOCKED_IP.txt", "downloaded_repos/lautaroangelico_WebEngine/install/sql/WEBENGINE_CREDITS_CONFIG.txt", "downloaded_repos/lautaroangelico_WebEngine/install/sql/WEBENGINE_CREDITS_LOGS.txt", "downloaded_repos/lautaroangelico_WebEngine/install/sql/WEBENGINE_CRON.txt", "downloaded_repos/lautaroangelico_WebEngine/install/sql/WEBENGINE_DOWNLOADS.txt", "downloaded_repos/lautaroangelico_WebEngine/install/sql/WEBENGINE_FLA.txt", "downloaded_repos/lautaroangelico_WebEngine/install/sql/WEBENGINE_NEWS.txt", "downloaded_repos/lautaroangelico_WebEngine/install/sql/WEBENGINE_NEWS_TRANSLATIONS.txt", "downloaded_repos/lautaroangelico_WebEngine/install/sql/WEBENGINE_PASSCHANGE_REQUEST.txt", "downloaded_repos/lautaroangelico_WebEngine/install/sql/WEBENGINE_PAYPAL_TRANSACTIONS.txt", "downloaded_repos/lautaroangelico_WebEngine/install/sql/WEBENGINE_PLUGINS.txt", "downloaded_repos/lautaroangelico_WebEngine/install/sql/WEBENGINE_REGISTER_ACCOUNT.txt", "downloaded_repos/lautaroangelico_WebEngine/install/sql/WEBENGINE_VOTES.txt", "downloaded_repos/lautaroangelico_WebEngine/install/sql/WEBENGINE_VOTE_LOGS.txt", "downloaded_repos/lautaroangelico_WebEngine/install/sql/WEBENGINE_VOTE_SITES.txt", "downloaded_repos/lautaroangelico_WebEngine/modules/.htaccess", "downloaded_repos/lautaroangelico_WebEngine/modules/castlesiege.php", "downloaded_repos/lautaroangelico_WebEngine/modules/contact.php", "downloaded_repos/lautaroangelico_WebEngine/modules/donation/paypal.php", "downloaded_repos/lautaroangelico_WebEngine/modules/donation.php", "downloaded_repos/lautaroangelico_WebEngine/modules/downloads.php", "downloaded_repos/lautaroangelico_WebEngine/modules/forgotpassword.php", "downloaded_repos/lautaroangelico_WebEngine/modules/home.php", "downloaded_repos/lautaroangelico_WebEngine/modules/info.php", "downloaded_repos/lautaroangelico_WebEngine/modules/language/switch.php", "downloaded_repos/lautaroangelico_WebEngine/modules/login.php", "downloaded_repos/lautaroangelico_WebEngine/modules/logout.php", "downloaded_repos/lautaroangelico_WebEngine/modules/news.php", "downloaded_repos/lautaroangelico_WebEngine/modules/privacy.php", "downloaded_repos/lautaroangelico_WebEngine/modules/profile/guild.php", "downloaded_repos/lautaroangelico_WebEngine/modules/profile/player.php", "downloaded_repos/lautaroangelico_WebEngine/modules/rankings/gens.php", "downloaded_repos/lautaroangelico_WebEngine/modules/rankings/grandresets.php", "downloaded_repos/lautaroangelico_WebEngine/modules/rankings/guilds.php", "downloaded_repos/lautaroangelico_WebEngine/modules/rankings/killers.php", "downloaded_repos/lautaroangelico_WebEngine/modules/rankings/level.php", "downloaded_repos/lautaroangelico_WebEngine/modules/rankings/master.php", "downloaded_repos/lautaroangelico_WebEngine/modules/rankings/online.php", "downloaded_repos/lautaroangelico_WebEngine/modules/rankings/resets.php", "downloaded_repos/lautaroangelico_WebEngine/modules/rankings/votes.php", "downloaded_repos/lautaroangelico_WebEngine/modules/rankings.php", "downloaded_repos/lautaroangelico_WebEngine/modules/refunds.php", "downloaded_repos/lautaroangelico_WebEngine/modules/register.php", "downloaded_repos/lautaroangelico_WebEngine/modules/tos.php", "downloaded_repos/lautaroangelico_WebEngine/modules/usercp/addstats.php", "downloaded_repos/lautaroangelico_WebEngine/modules/usercp/buyzen.php", "downloaded_repos/lautaroangelico_WebEngine/modules/usercp/clearpk.php", "downloaded_repos/lautaroangelico_WebEngine/modules/usercp/clearskilltree.php", "downloaded_repos/lautaroangelico_WebEngine/modules/usercp/myaccount.php", "downloaded_repos/lautaroangelico_WebEngine/modules/usercp/myemail.php", "downloaded_repos/lautaroangelico_WebEngine/modules/usercp/mypassword.php", "downloaded_repos/lautaroangelico_WebEngine/modules/usercp/reset.php", "downloaded_repos/lautaroangelico_WebEngine/modules/usercp/resetstats.php", "downloaded_repos/lautaroangelico_WebEngine/modules/usercp/unstick.php", "downloaded_repos/lautaroangelico_WebEngine/modules/usercp/vote.php", "downloaded_repos/lautaroangelico_WebEngine/modules/usercp.php", "downloaded_repos/lautaroangelico_WebEngine/modules/verifyemail.php", "downloaded_repos/lautaroangelico_WebEngine/robots.txt", "downloaded_repos/lautaroangelico_WebEngine/templates/default/css/castle-siege.css", "downloaded_repos/lautaroangelico_WebEngine/templates/default/css/index.html", "downloaded_repos/lautaroangelico_WebEngine/templates/default/css/override.css", "downloaded_repos/lautaroangelico_WebEngine/templates/default/css/profiles.css", "downloaded_repos/lautaroangelico_WebEngine/templates/default/css/style.css", "downloaded_repos/lautaroangelico_WebEngine/templates/default/favicon.ico", "downloaded_repos/lautaroangelico_WebEngine/templates/default/fonts/index.html", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/background.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/castle_owner_bg.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/character-avatars/alc.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/character-avatars/avatar.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/character-avatars/dk.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/character-avatars/dl.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/character-avatars/dw.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/character-avatars/elf.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/character-avatars/gc.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/character-avatars/gl.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/character-avatars/ik.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/character-avatars/index.html", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/character-avatars/lem.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/character-avatars/liw.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/character-avatars/mg.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/character-avatars/rf.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/character-avatars/rw.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/character-avatars/sl.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/character-avatars/sum.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/donation/index.html", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/donation/paypal.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/gens_1.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/gens_2.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/account.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/addstats.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/bullet.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/clearpk.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/clearst.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/comment.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/donate.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/extra_icon_01.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/extra_icon_02.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/extra_icon_03.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/extra_icon_04.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/extra_icon_05.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/extra_icon_06.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/extra_icon_07.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/extra_icon_08.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/extra_icon_09.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/extra_icon_10.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/extra_icon_11.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/extra_icon_12.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/extra_icon_13.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/extra_icon_14.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/extra_icon_15.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/extra_icon_16.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/extra_icon_17.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/extra_icon_18.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/extra_icon_19.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/extra_icon_20.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/facebook.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/fixstats.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/index.html", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/reset.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/unstick.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/usercp_default.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/vip.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/vote.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/icons/zen.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/index.html", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/logo.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/online_progress_bar.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/paypal-logo-200-68.png", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/paypal-submit.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/profiles/alc.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/profiles/dl.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/profiles/elf.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/profiles/gc.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/profiles/gl.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/profiles/guild.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/profiles/ik.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/profiles/index.html", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/profiles/knight.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/profiles/lem.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/profiles/liw.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/profiles/mg.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/profiles/rf.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/profiles/rw.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/profiles/sl.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/profiles/sum.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/profiles/wiz.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/sidebar_banner_download.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/sidebar_banner_join.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/social/discord.svg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/social/facebook.svg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/social/index.html", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/social/instagram.svg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/usercp_bg.jpg", "downloaded_repos/lautaroangelico_WebEngine/templates/default/inc/.htaccess", "downloaded_repos/lautaroangelico_WebEngine/templates/default/inc/modules/footer.php", "downloaded_repos/lautaroangelico_WebEngine/templates/default/inc/modules/sidebar.php", "downloaded_repos/lautaroangelico_WebEngine/templates/default/inc/template.functions.php", "downloaded_repos/lautaroangelico_WebEngine/templates/default/index.php", "downloaded_repos/lautaroangelico_WebEngine/templates/default/js/events.js", "downloaded_repos/lautaroangelico_WebEngine/templates/default/js/index.html", "downloaded_repos/lautaroangelico_WebEngine/templates/default/js/main.js", "downloaded_repos/lautaroangelico_WebEngine/templates/index.php"], "skipped": [{"path": "downloaded_repos/lautaroangelico_WebEngine/admincp/js/metisMenu.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/lautaroangelico_WebEngine/includes/config/webengine.json", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/lautaroangelico_WebEngine/templates/default/img/background-2600.jpg", "reason": "exceeded_size_limit"}]}, "time": {"rules": [], "rules_parse_time": 1.9610750675201416, "profiling_times": {"config_time": 5.881244659423828, "core_time": 8.316109418869019, "ignores_time": 0.0028171539306640625, "total_time": 14.201173067092896}, "parsing_time": {"total_time": 3.689845323562622, "per_file_time": {"mean": 0.018357439420709564, "std_dev": 0.0009437035602985792}, "very_slow_stats": {"time_ratio": 0.08657787345361463, "count_ratio": 0.004975124378109453}, "very_slow_files": [{"fpath": "downloaded_repos/lautaroangelico_WebEngine/includes/classes/phpmailer/PHPMailer.php", "ftime": 0.3194589614868164}]}, "scanning_time": {"total_time": 46.105283975601196, "per_file_time": {"mean": 0.030736855983734084, "std_dev": 0.032395347177615365}, "very_slow_stats": {"time_ratio": 0.29750646315665485, "count_ratio": 0.004}, "very_slow_files": [{"fpath": "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/navbar.php", "ftime": 1.****************}, {"fpath": "downloaded_repos/lautaroangelico_WebEngine/includes/classes/class.rankings.php", "ftime": 1.****************}, {"fpath": "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/usercp.php", "ftime": 2.***************}, {"fpath": "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/website_settings.php", "ftime": 2.***************}, {"fpath": "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/accountinfo.php", "ftime": 2.***************}, {"fpath": "downloaded_repos/lautaroangelico_WebEngine/includes/classes/phpmailer/PHPMailer.php", "ftime": 3.****************}]}, "matching_time": {"total_time": 19.**************, "per_file_and_rule_time": {"mean": 0.****************, "std_dev": 0.004058949636789735}, "very_slow_stats": {"time_ratio": 0.*****************, "count_ratio": 0.047619047619047616}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/usercp.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.*****************}, {"fpath": "downloaded_repos/lautaroangelico_WebEngine/includes/webengine.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.****************}, {"fpath": "downloaded_repos/lautaroangelico_WebEngine/includes/classes/phpmailer/PHPMailer.php", "rule_id": "php.lang.security.injection.tainted-object-instantiation.tainted-object-instantiation", "time": 0.***************}, {"fpath": "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/connection_settings.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.*****************}, {"fpath": "downloaded_repos/lautaroangelico_WebEngine/includes/classes/class.account.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.****************}, {"fpath": "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/creditsconfigs.php", "rule_id": "php.laravel.security.laravel-sql-injection.laravel-sql-injection", "time": 0.****************}, {"fpath": "downloaded_repos/lautaroangelico_WebEngine/includes/classes/class.rankings.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.*****************}, {"fpath": "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/website_settings.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.****************}, {"fpath": "downloaded_repos/lautaroangelico_WebEngine/includes/classes/phpmailer/PHPMailer.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.****************}, {"fpath": "downloaded_repos/lautaroangelico_WebEngine/includes/classes/phpmailer/PHPMailer.php", "rule_id": "php.lang.security.injection.tainted-callable.tainted-callable", "time": 1.0326039791107178}]}, "tainting_time": {"total_time": 12.693252801895142, "per_def_and_rule_time": {"mean": 0.00485216085699356, "std_dev": 0.0004446775750154627}, "very_slow_stats": {"time_ratio": 0.4706002200139104, "count_ratio": 0.013761467889908258}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/usercp.php", "fline": 1, "rule_id": "php.lang.security.injection.tainted-callable.tainted-callable", "time": 0.****************}, {"fpath": "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/editcharacter.php", "fline": 1, "rule_id": "php.lang.security.injection.printed-request.printed-request", "time": 0.*****************}, {"fpath": "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/accountinfo.php", "fline": 1, "rule_id": "php.laravel.security.laravel-sql-injection.laravel-sql-injection", "time": 0.*****************}, {"fpath": "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/navbar.php", "fline": 1, "rule_id": "php.lang.security.injection.tainted-object-instantiation.tainted-object-instantiation", "time": 0.*****************}, {"fpath": "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/accountinfo.php", "fline": 1, "rule_id": "php.lang.security.injection.tainted-object-instantiation.tainted-object-instantiation", "time": 0.*****************}, {"fpath": "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/accountinfo.php", "fline": 1, "rule_id": "php.lang.security.injection.tainted-exec.tainted-exec", "time": 0.*****************}, {"fpath": "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/editcharacter.php", "fline": 1, "rule_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "time": 0.*****************}, {"fpath": "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/accountinfo.php", "fline": 1, "rule_id": "php.lang.security.injection.printed-request.printed-request", "time": 0.*****************}, {"fpath": "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/usercp.php", "fline": 1, "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.*****************}, {"fpath": "downloaded_repos/lautaroangelico_WebEngine/admincp/modules/usercp.php", "fline": 1, "rule_id": "php.lang.security.injection.tainted-exec.tainted-exec", "time": 0.*****************}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": **********}, "engine_requested": "OSS", "skipped_rules": []}