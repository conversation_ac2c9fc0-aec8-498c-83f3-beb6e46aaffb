{"version": "1.130.0", "results": [], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/plan2net_webp/Classes/Converter/AbstractConverter.php", "start": {"line": 9, "col": 52, "offset": 0}, "end": {"line": 9, "col": 58, "offset": 6}}]], "message": "Syntax error at line downloaded_repos/plan2net_webp/Classes/Converter/AbstractConverter.php:9:\n `string` was unexpected", "path": "downloaded_repos/plan2net_webp/Classes/Converter/AbstractConverter.php", "spans": [{"file": "downloaded_repos/plan2net_webp/Classes/Converter/AbstractConverter.php", "start": {"line": 9, "col": 52, "offset": 0}, "end": {"line": 9, "col": 58, "offset": 6}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/plan2net_webp/Classes/Converter/MagickConverter.php", "start": {"line": 60, "col": 53, "offset": 0}, "end": {"line": 60, "col": 56, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/plan2net_webp/Classes/Converter/MagickConverter.php:60:\n `...` was unexpected", "path": "downloaded_repos/plan2net_webp/Classes/Converter/MagickConverter.php", "spans": [{"file": "downloaded_repos/plan2net_webp/Classes/Converter/MagickConverter.php", "start": {"line": 60, "col": 53, "offset": 0}, "end": {"line": 60, "col": 56, "offset": 3}}]}], "paths": {"scanned": ["downloaded_repos/plan2net_webp/.gitignore", "downloaded_repos/plan2net_webp/Classes/Converter/AbstractConverter.php", "downloaded_repos/plan2net_webp/Classes/Converter/Converter.php", "downloaded_repos/plan2net_webp/Classes/Converter/Exception/ConvertedFileLargerThanOriginalException.php", "downloaded_repos/plan2net_webp/Classes/Converter/Exception/WillNotRetryWithConfigurationException.php", "downloaded_repos/plan2net_webp/Classes/Converter/ExternalConverter.php", "downloaded_repos/plan2net_webp/Classes/Converter/MagickConverter.php", "downloaded_repos/plan2net_webp/Classes/Converter/PhpGdConverter.php", "downloaded_repos/plan2net_webp/Classes/Core/Filter/FileNameFilter.php", "downloaded_repos/plan2net_webp/Classes/EventListener/AfterFileProcessing.php", "downloaded_repos/plan2net_webp/Classes/Service/Configuration.php", "downloaded_repos/plan2net_webp/Classes/Service/Webp.php", "downloaded_repos/plan2net_webp/Configuration/Services.yaml", "downloaded_repos/plan2net_webp/Documentation/Administrator/Index.rst", "downloaded_repos/plan2net_webp/Documentation/Index.rst", "downloaded_repos/plan2net_webp/Documentation/Introduction/Index.rst", "downloaded_repos/plan2net_webp/Documentation/KnownProblems/Index.rst", "downloaded_repos/plan2net_webp/Documentation/Links.rst", "downloaded_repos/plan2net_webp/Documentation/guides.xml", "downloaded_repos/plan2net_webp/LICENSE", "downloaded_repos/plan2net_webp/README.md", "downloaded_repos/plan2net_webp/Resources/Public/Documentation/extension_settings.png", "downloaded_repos/plan2net_webp/Resources/Public/Documentation/headers.png", "downloaded_repos/plan2net_webp/Resources/Public/Icons/Extension.png", "downloaded_repos/plan2net_webp/composer.json", "downloaded_repos/plan2net_webp/ext_conf_template.txt", "downloaded_repos/plan2net_webp/ext_emconf.php", "downloaded_repos/plan2net_webp/ext_localconf.php", "downloaded_repos/plan2net_webp/ext_tables.sql", "downloaded_repos/plan2net_webp/php-cs-fixer.config.php"], "skipped": [{"path": "downloaded_repos/plan2net_webp/Classes/Converter/AbstractConverter.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/plan2net_webp/Classes/Converter/MagickConverter.php", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 2.1937880516052246, "profiling_times": {"config_time": 6.**********94922, "core_time": 3.0024025440216064, "ignores_time": 0.0021152496337890625, "total_time": 9.442818880081177}, "parsing_time": {"total_time": 0.30646538734436035, "per_file_time": {"mean": 0.019154086709022522, "std_dev": 5.4022785856977507e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.6651372909545898, "per_file_time": {"mean": 0.008751806459928815, "std_dev": 0.0002039317798077341}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.11410093307495117, "per_file_and_rule_time": {"mean": 0.002152847793867003, "std_dev": 1.0557566056859683e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.00549006462097168, "per_def_and_rule_time": {"mean": 0.00039214747292654853, "std_dev": 3.3467305211949996e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}