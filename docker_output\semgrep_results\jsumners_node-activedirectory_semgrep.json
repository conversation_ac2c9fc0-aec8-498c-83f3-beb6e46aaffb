{"version": "1.130.0", "results": [], "errors": [], "paths": {"scanned": ["downloaded_repos/jsumners_node-activedirectory/.eslintrc", "downloaded_repos/jsumners_node-activedirectory/.github/workflows/ci.yml", "downloaded_repos/jsumners_node-activedirectory/.gitignore", "downloaded_repos/jsumners_node-activedirectory/.npmrc", "downloaded_repos/jsumners_node-activedirectory/.prettierignore", "downloaded_repos/jsumners_node-activedirectory/.taprc", "downloaded_repos/jsumners_node-activedirectory/Changelog.md", "downloaded_repos/jsumners_node-activedirectory/LICENSE", "downloaded_repos/jsumners_node-activedirectory/README.md", "downloaded_repos/jsumners_node-activedirectory/index.js", "downloaded_repos/jsumners_node-activedirectory/jsdoc.json", "downloaded_repos/jsumners_node-activedirectory/lib/activedirectory.js", "downloaded_repos/jsumners_node-activedirectory/lib/adpromise.js", "downloaded_repos/jsumners_node-activedirectory/lib/client/RangeAttribute.js", "downloaded_repos/jsumners_node-activedirectory/lib/components/find.js", "downloaded_repos/jsumners_node-activedirectory/lib/components/findGroups.js", "downloaded_repos/jsumners_node-activedirectory/lib/components/findUsers.js", "downloaded_repos/jsumners_node-activedirectory/lib/components/getGroupMembershipForDN.js", "downloaded_repos/jsumners_node-activedirectory/lib/components/getUsersForGroup.js", "downloaded_repos/jsumners_node-activedirectory/lib/components/parseRangeAttributes.js", "downloaded_repos/jsumners_node-activedirectory/lib/components/search.js", "downloaded_repos/jsumners_node-activedirectory/lib/components/utilities.js", "downloaded_repos/jsumners_node-activedirectory/lib/models/group.js", "downloaded_repos/jsumners_node-activedirectory/lib/models/user.js", "downloaded_repos/jsumners_node-activedirectory/package.json"], "skipped": [{"path": "downloaded_repos/jsumners_node-activedirectory/test/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/attributes.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/authenticate.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/authenticatePromised.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/config.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/ctor.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/entryParser.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/find.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/findPromised.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/findgroup.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/findgroupPromised.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/findgroups.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/findgroupsPromised.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/finduser.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/finduserPromised.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/findusers.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/findusersPromised.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/getgroupmembershipforgroup.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/getgroupmembershipforgroupPromised.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/getgroupmembershipforuser.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/getgroupmembershipforuserPromised.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/getusersforgroup.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/getusersforgroupPromised.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/groupexists.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/groupexistsPromised.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/isusermemberof.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/isusermemberofPromised.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/mockServer/FakeDN.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/mockServer/FakeRDN.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/mockServer/authentication.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/mockServer/connectionHandler.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/mockServer/getResponse.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/mockServer/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/mockServer/schema.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/mockServer/search.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/mockServer/setupConnection.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/mockServer/utilities.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/network.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/range.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/rangeAttribute.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/rootDSE.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/rootDSEPromised.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/settings.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/userexists.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/userexistsPromised.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jsumners_node-activedirectory/test/utilities.test.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6598591804504395, "profiling_times": {"config_time": 6.096042156219482, "core_time": 2.872288703918457, "ignores_time": 0.0018467903137207031, "total_time": 8.971102476119995}, "parsing_time": {"total_time": 0.911778450012207, "per_file_time": {"mean": 0.053634026471306294, "std_dev": 0.001691511546409447}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 3.281432628631592, "per_file_time": {"mean": 0.048976606397486425, "std_dev": 0.010920996975857276}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 1.1301615238189697, "per_file_and_rule_time": {"mean": 0.00837156684310348, "std_dev": 0.0002609545518548101}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.36972880363464355, "per_def_and_rule_time": {"mean": 0.0012793384208811198, "std_dev": 5.166066133929845e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}