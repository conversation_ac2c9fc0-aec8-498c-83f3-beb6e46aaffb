{"version": "1.130.0", "results": [{"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/spicywebau_craft-neo/src/web/assets/configurator/src/scripts/BlockTypeSettings.js", "start": {"line": 616, "col": 9, "offset": 20788}, "end": {"line": 616, "col": 56, "offset": 20835}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/spicywebau_craft-neo/src/web/assets/input/src/scripts/Block.js", "start": {"line": 340, "col": 9, "offset": 11177}, "end": {"line": 340, "col": 37, "offset": 11205}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/spicywebau_craft-neo/.ddev/config.yaml", "downloaded_repos/spicywebau_craft-neo/.gitattributes", "downloaded_repos/spicywebau_craft-neo/.github/ISSUE_TEMPLATE/BUG-REPORT.yml", "downloaded_repos/spicywebau_craft-neo/.github/ISSUE_TEMPLATE/FEATURE-REQUEST.yml", "downloaded_repos/spicywebau_craft-neo/.github/ISSUE_TEMPLATE/QUESTION.yml", "downloaded_repos/spicywebau_craft-neo/.github/ISSUE_TEMPLATE/config.yml", "downloaded_repos/spicywebau_craft-neo/.gitignore", "downloaded_repos/spicywebau_craft-neo/CHANGELOG.md", "downloaded_repos/spicywebau_craft-neo/LICENSE", "downloaded_repos/spicywebau_craft-neo/README.md", "downloaded_repos/spicywebau_craft-neo/codeception.yml", "downloaded_repos/spicywebau_craft-neo/composer.json", "downloaded_repos/spicywebau_craft-neo/composer.lock", "downloaded_repos/spicywebau_craft-neo/docs/api.md", "downloaded_repos/spicywebau_craft-neo/docs/assets/feature1-1.png", "downloaded_repos/spicywebau_craft-neo/docs/assets/feature3-1.png", "downloaded_repos/spicywebau_craft-neo/docs/assets/feature3-2.png", "downloaded_repos/spicywebau_craft-neo/docs/assets/feature4-1.png", "downloaded_repos/spicywebau_craft-neo/docs/assets/feature4-2.png", "downloaded_repos/spicywebau_craft-neo/docs/assets/feature5-1.png", "downloaded_repos/spicywebau_craft-neo/docs/assets/feature5-2.png", "downloaded_repos/spicywebau_craft-neo/docs/assets/feature6-1.png", "downloaded_repos/spicywebau_craft-neo/docs/assets/feature6-2.png", "downloaded_repos/spicywebau_craft-neo/docs/assets/feature6-3.png", "downloaded_repos/spicywebau_craft-neo/docs/assets/feed-me-block-level.png", "downloaded_repos/spicywebau_craft-neo/docs/assets/icon.png", "downloaded_repos/spicywebau_craft-neo/docs/assets/neo-block-type-settings.png", "downloaded_repos/spicywebau_craft-neo/docs/console-commands.md", "downloaded_repos/spicywebau_craft-neo/docs/content-migration-guides/populating-neo-fields.md", "downloaded_repos/spicywebau_craft-neo/docs/content-migration-guides/updating-duplicating-creating-block-types.md", "downloaded_repos/spicywebau_craft-neo/docs/creating-neo-fields.md", "downloaded_repos/spicywebau_craft-neo/docs/eager-loading.md", "downloaded_repos/spicywebau_craft-neo/docs/events.md", "downloaded_repos/spicywebau_craft-neo/docs/faq.md", "downloaded_repos/spicywebau_craft-neo/docs/feed-me.md", "downloaded_repos/spicywebau_craft-neo/docs/graphql.md", "downloaded_repos/spicywebau_craft-neo/docs/installation.md", "downloaded_repos/spicywebau_craft-neo/docs/plugin-compatibility.md", "downloaded_repos/spicywebau_craft-neo/docs/resources.md", "downloaded_repos/spicywebau_craft-neo/docs/settings.md", "downloaded_repos/spicywebau_craft-neo/docs/templating.md", "downloaded_repos/spicywebau_craft-neo/docs/upgrade-guides/neo-2.7-craft-3.4.md", "downloaded_repos/spicywebau_craft-neo/docs/upgrade-guides/neo-4.md", "downloaded_repos/spicywebau_craft-neo/docs/upgrade-guides/neo-5.md", "downloaded_repos/spicywebau_craft-neo/ecs.php", "downloaded_repos/spicywebau_craft-neo/package-lock.json", "downloaded_repos/spicywebau_craft-neo/package.json", "downloaded_repos/spicywebau_craft-neo/src/Field.php", "downloaded_repos/spicywebau_craft-neo/src/Plugin.php", "downloaded_repos/spicywebau_craft-neo/src/console/controllers/BlockTypeGroupsController.php", "downloaded_repos/spicywebau_craft-neo/src/console/controllers/BlockTypesController.php", "downloaded_repos/spicywebau_craft-neo/src/console/controllers/FieldsController.php", "downloaded_repos/spicywebau_craft-neo/src/controllers/Configurator.php", "downloaded_repos/spicywebau_craft-neo/src/controllers/Conversion.php", "downloaded_repos/spicywebau_craft-neo/src/controllers/Input.php", "downloaded_repos/spicywebau_craft-neo/src/elements/Block.php", "downloaded_repos/spicywebau_craft-neo/src/elements/conditions/BlockCondition.php", "downloaded_repos/spicywebau_craft-neo/src/elements/conditions/OwnerCategoryGroupConditionRule.php", "downloaded_repos/spicywebau_craft-neo/src/elements/conditions/OwnerConditionRuleTrait.php", "downloaded_repos/spicywebau_craft-neo/src/elements/conditions/OwnerDateCreatedConditionRule.php", "downloaded_repos/spicywebau_craft-neo/src/elements/conditions/OwnerDateUpdatedConditionRule.php", "downloaded_repos/spicywebau_craft-neo/src/elements/conditions/OwnerEntryTypeConditionRule.php", "downloaded_repos/spicywebau_craft-neo/src/elements/conditions/OwnerHasUrlConditionRule.php", "downloaded_repos/spicywebau_craft-neo/src/elements/conditions/OwnerLevelConditionRule.php", "downloaded_repos/spicywebau_craft-neo/src/elements/conditions/OwnerSectionConditionRule.php", "downloaded_repos/spicywebau_craft-neo/src/elements/conditions/OwnerSlugConditionRule.php", "downloaded_repos/spicywebau_craft-neo/src/elements/conditions/OwnerTagGroupConditionRule.php", "downloaded_repos/spicywebau_craft-neo/src/elements/conditions/OwnerTitleConditionRule.php", "downloaded_repos/spicywebau_craft-neo/src/elements/conditions/OwnerUriConditionRule.php", "downloaded_repos/spicywebau_craft-neo/src/elements/conditions/OwnerUserGroupConditionRule.php", "downloaded_repos/spicywebau_craft-neo/src/elements/conditions/OwnerVolumeConditionRule.php", "downloaded_repos/spicywebau_craft-neo/src/elements/conditions/fields/ParentDateFieldConditionRule.php", "downloaded_repos/spicywebau_craft-neo/src/elements/conditions/fields/ParentFieldConditionRuleTrait.php", "downloaded_repos/spicywebau_craft-neo/src/elements/conditions/fields/ParentLightswitchFieldConditionRule.php", "downloaded_repos/spicywebau_craft-neo/src/elements/conditions/fields/ParentNumberFieldConditionRule.php", "downloaded_repos/spicywebau_craft-neo/src/elements/conditions/fields/ParentOptionsFieldConditionRule.php", "downloaded_repos/spicywebau_craft-neo/src/elements/conditions/fields/ParentRelationalFieldConditionRule.php", "downloaded_repos/spicywebau_craft-neo/src/elements/conditions/fields/ParentTextFieldConditionRule.php", "downloaded_repos/spicywebau_craft-neo/src/elements/db/BlockQuery.php", "downloaded_repos/spicywebau_craft-neo/src/enums/BlockTypeGroupDropdown.php", "downloaded_repos/spicywebau_craft-neo/src/enums/BlockTypeIconSelectMode.php", "downloaded_repos/spicywebau_craft-neo/src/enums/NewBlockMenuStyle.php", "downloaded_repos/spicywebau_craft-neo/src/errors/BlockTypeNotFoundException.php", "downloaded_repos/spicywebau_craft-neo/src/events/BlockTypeEvent.php", "downloaded_repos/spicywebau_craft-neo/src/events/FilterBlockTypesEvent.php", "downloaded_repos/spicywebau_craft-neo/src/events/SetConditionElementTypesEvent.php", "downloaded_repos/spicywebau_craft-neo/src/fieldlayoutelements/ChildBlocksUiElement.php", "downloaded_repos/spicywebau_craft-neo/src/gql/arguments/elements/Block.php", "downloaded_repos/spicywebau_craft-neo/src/gql/interfaces/elements/Block.php", "downloaded_repos/spicywebau_craft-neo/src/gql/resolvers/elements/Block.php", "downloaded_repos/spicywebau_craft-neo/src/gql/types/elements/Block.php", "downloaded_repos/spicywebau_craft-neo/src/gql/types/generators/BlockType.php", "downloaded_repos/spicywebau_craft-neo/src/gql/types/input/Block.php", "downloaded_repos/spicywebau_craft-neo/src/helpers/Memoize.php", "downloaded_repos/spicywebau_craft-neo/src/icon.svg", "downloaded_repos/spicywebau_craft-neo/src/integrations/feedme/Field.php", "downloaded_repos/spicywebau_craft-neo/src/jobs/ApplyNeoPropagationMethod.php", "downloaded_repos/spicywebau_craft-neo/src/jobs/DeleteBlock.php", "downloaded_repos/spicywebau_craft-neo/src/jobs/DeleteBlocks.php", "downloaded_repos/spicywebau_craft-neo/src/jobs/ResaveFieldBlockStructures.php", "downloaded_repos/spicywebau_craft-neo/src/jobs/SaveBlockStructures.php", "downloaded_repos/spicywebau_craft-neo/src/migrations/Install.php", "downloaded_repos/spicywebau_craft-neo/src/migrations/m231005_132818_add_block_type_icon_filename_property.php", "downloaded_repos/spicywebau_craft-neo/src/migrations/m231027_012155_project_config_sort_orders.php", "downloaded_repos/spicywebau_craft-neo/src/migrations/m240212_040753_move_project_config_data.php", "downloaded_repos/spicywebau_craft-neo/src/migrations/m240224_024030_migrate_owners_table.php", "downloaded_repos/spicywebau_craft-neo/src/migrations/m240226_032156_migrate_deleted_with_owner.php", "downloaded_repos/spicywebau_craft-neo/src/migrations/m240311_044007_add_block_type_color.php", "downloaded_repos/spicywebau_craft-neo/src/migrations/m240403_061537_content_refactor.php", "downloaded_repos/spicywebau_craft-neo/src/migrations/m240711_024245_block_type_entry_type.php", "downloaded_repos/spicywebau_craft-neo/src/migrations/m240722_092833_revert_index_change.php", "downloaded_repos/spicywebau_craft-neo/src/models/BlockStructure.php", "downloaded_repos/spicywebau_craft-neo/src/models/BlockType.php", "downloaded_repos/spicywebau_craft-neo/src/models/BlockTypeGroup.php", "downloaded_repos/spicywebau_craft-neo/src/models/Settings.php", "downloaded_repos/spicywebau_craft-neo/src/records/Block.php", "downloaded_repos/spicywebau_craft-neo/src/records/BlockStructure.php", "downloaded_repos/spicywebau_craft-neo/src/records/BlockType.php", "downloaded_repos/spicywebau_craft-neo/src/records/BlockTypeGroup.php", "downloaded_repos/spicywebau_craft-neo/src/services/BlockTypes.php", "downloaded_repos/spicywebau_craft-neo/src/services/Blocks.php", "downloaded_repos/spicywebau_craft-neo/src/services/Conversion.php", "downloaded_repos/spicywebau_craft-neo/src/services/Fields.php", "downloaded_repos/spicywebau_craft-neo/src/templates/_block-type-icon-input.twig", "downloaded_repos/spicywebau_craft-neo/src/templates/_tabs.twig", "downloaded_repos/spicywebau_craft-neo/src/templates/block-type-group-settings.twig", "downloaded_repos/spicywebau_craft-neo/src/templates/block-type-settings.twig", "downloaded_repos/spicywebau_craft-neo/src/templates/block.twig", "downloaded_repos/spicywebau_craft-neo/src/templates/child-blocks.twig", "downloaded_repos/spicywebau_craft-neo/src/templates/feed-me.twig", "downloaded_repos/spicywebau_craft-neo/src/templates/input.twig", "downloaded_repos/spicywebau_craft-neo/src/templates/macros.twig", "downloaded_repos/spicywebau_craft-neo/src/templates/plugin-settings.twig", "downloaded_repos/spicywebau_craft-neo/src/templates/settings.twig", "downloaded_repos/spicywebau_craft-neo/src/translations/de/neo.php", "downloaded_repos/spicywebau_craft-neo/src/translations/en/neo.php", "downloaded_repos/spicywebau_craft-neo/src/translations/fr/neo.php", "downloaded_repos/spicywebau_craft-neo/src/translations/nl/neo.php", "downloaded_repos/spicywebau_craft-neo/src/validators/FieldValidator.php", "downloaded_repos/spicywebau_craft-neo/src/web/assets/configurator/ConfiguratorAsset.php", "downloaded_repos/spicywebau_craft-neo/src/web/assets/configurator/src/scripts/BlockType.js", "downloaded_repos/spicywebau_craft-neo/src/web/assets/configurator/src/scripts/BlockTypeFieldLayout.js", "downloaded_repos/spicywebau_craft-neo/src/web/assets/configurator/src/scripts/BlockTypeIconSelect.js", "downloaded_repos/spicywebau_craft-neo/src/web/assets/configurator/src/scripts/BlockTypeSettings.js", "downloaded_repos/spicywebau_craft-neo/src/web/assets/configurator/src/scripts/Configurator.js", "downloaded_repos/spicywebau_craft-neo/src/web/assets/configurator/src/scripts/Group.js", "downloaded_repos/spicywebau_craft-neo/src/web/assets/configurator/src/scripts/GroupSettings.js", "downloaded_repos/spicywebau_craft-neo/src/web/assets/configurator/src/scripts/Item.js", "downloaded_repos/spicywebau_craft-neo/src/web/assets/configurator/src/scripts/Settings.js", "downloaded_repos/spicywebau_craft-neo/src/web/assets/configurator/src/scripts/jquery-extensions.js", "downloaded_repos/spicywebau_craft-neo/src/web/assets/configurator/src/scripts/main.js", "downloaded_repos/spicywebau_craft-neo/src/web/assets/configurator/src/scripts/namespace.js", "downloaded_repos/spicywebau_craft-neo/src/web/assets/configurator/src/styles/main.scss", "downloaded_repos/spicywebau_craft-neo/src/web/assets/converter/ConverterAsset.php", "downloaded_repos/spicywebau_craft-neo/src/web/assets/converter/src/scripts/main.js", "downloaded_repos/spicywebau_craft-neo/src/web/assets/input/InputAsset.php", "downloaded_repos/spicywebau_craft-neo/src/web/assets/input/src/scripts/Block.js", "downloaded_repos/spicywebau_craft-neo/src/web/assets/input/src/scripts/BlockSort.js", "downloaded_repos/spicywebau_craft-neo/src/web/assets/input/src/scripts/BlockType.js", "downloaded_repos/spicywebau_craft-neo/src/web/assets/input/src/scripts/Buttons.js", "downloaded_repos/spicywebau_craft-neo/src/web/assets/input/src/scripts/ButtonsGrid.js", "downloaded_repos/spicywebau_craft-neo/src/web/assets/input/src/scripts/ButtonsList.js", "downloaded_repos/spicywebau_craft-neo/src/web/assets/input/src/scripts/Group.js", "downloaded_repos/spicywebau_craft-neo/src/web/assets/input/src/scripts/Input.js", "downloaded_repos/spicywebau_craft-neo/src/web/assets/input/src/scripts/NewBlockMenu.js", "downloaded_repos/spicywebau_craft-neo/src/web/assets/input/src/scripts/main.js", "downloaded_repos/spicywebau_craft-neo/src/web/assets/input/src/scripts/namespace.js", "downloaded_repos/spicywebau_craft-neo/src/web/assets/input/src/scripts/plugins/cpfieldinspect/main.js", "downloaded_repos/spicywebau_craft-neo/src/web/assets/input/src/styles/main.scss", "downloaded_repos/spicywebau_craft-neo/src/web/twig/Extension.php", "downloaded_repos/spicywebau_craft-neo/src/web/twig/Variable.php", "downloaded_repos/spicywebau_craft-neo/webpack.config.js"], "skipped": [{"path": "downloaded_repos/spicywebau_craft-neo/src/web/assets/configurator/dist/scripts/configurator.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/src/web/assets/configurator/dist/scripts/configurator.js.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/src/web/assets/configurator/dist/styles/configurator.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/src/web/assets/configurator/dist/styles/configurator.css.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/src/web/assets/configurator/dist/styles/configurator.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/src/web/assets/converter/dist/scripts/converter.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/src/web/assets/converter/dist/scripts/converter.js.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/src/web/assets/input/dist/scripts/input.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/src/web/assets/input/dist/scripts/input.js.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/src/web/assets/input/dist/styles/input.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/src/web/assets/input/dist/styles/input.css.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/src/web/assets/input/dist/styles/input.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/.env", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/_bootstrap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/_craft/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/_craft/config/test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/_data/.gitkeep", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/_data/project/entryTypes/entrytype-00000000000000000000000001.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/_data/project/entryTypes/entrytype-00000000000000000000000002.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/_data/project/fieldGroups/fieldgroup-0000000000000000000000001.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/_data/project/fieldGroups/fieldgroup-0000000000000000000000002.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/_data/project/fields/field-000000000000000000000000000001.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/_data/project/fields/field-000000000000000000000000000002.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/_data/project/fields/field-000000000000000000000000000003.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/_data/project/neo/blockTypeGroups/blocktypegroup-000000000000000000001.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/_data/project/neo/blockTypes/neoblocktype-00000000000000000000001.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/_data/project/neo/blockTypes/neoblocktype-00000000000000000000002.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/_data/project/neo/orders/field-000000000000000000000000000001.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/_data/project/neo/orders/field-000000000000000000000000000003.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/_data/project/project.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/_data/project/sections/section-0000000000000000000000000001.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/_data/project/siteGroups/sitegroup-00000000000000000000000001.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/_data/project/sites/site-0000000000000000000000000000001.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/_output/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/_support/AcceptanceTester.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/_support/FunctionalTester.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/_support/Helper/Acceptance.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/_support/Helper/Functional.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/_support/Helper/Unit.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/_support/UnitTester.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/_support/_generated/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/acceptance/_bootstrap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/acceptance.suite.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/fixtures/EntriesFixture.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/fixtures/data/entries.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/functional/_bootstrap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/functional.suite.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/unit/BlockChildrenUnitTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/unit/BlockQueryUnitTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/unit/BlockStructureUnitTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/unit/ConversionUnitTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/unit/FieldUnitTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/unit/_bootstrap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-neo/tests/unit.suite.yml", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6673309803009033, "profiling_times": {"config_time": 5.685556411743164, "core_time": 6.958503723144531, "ignores_time": 0.0021371841430664062, "total_time": 12.647093296051025}, "parsing_time": {"total_time": 3.4931483268737793, "per_file_time": {"mean": 0.028868994436973393, "std_dev": 0.0040515208083330345}, "very_slow_stats": {"time_ratio": 0.28378298319210155, "count_ratio": 0.024793388429752067}, "very_slow_files": [{"fpath": "downloaded_repos/spicywebau_craft-neo/src/web/assets/input/src/scripts/Input.js", "ftime": 0.31719112396240234}, {"fpath": "downloaded_repos/spicywebau_craft-neo/src/web/assets/input/src/scripts/Block.js", "ftime": 0.32347893714904785}, {"fpath": "downloaded_repos/spicywebau_craft-neo/src/Field.php", "ftime": 0.35062599182128906}]}, "scanning_time": {"total_time": 19.872401475906372, "per_file_time": {"mean": 0.0427363472600137, "std_dev": 0.07577636750523956}, "very_slow_stats": {"time_ratio": 0.4612353343352902, "count_ratio": 0.0064516129032258064}, "very_slow_files": [{"fpath": "downloaded_repos/spicywebau_craft-neo/src/web/assets/configurator/src/scripts/BlockTypeSettings.js", "ftime": 1.797257900238037}, {"fpath": "downloaded_repos/spicywebau_craft-neo/src/web/assets/input/src/scripts/Block.js", "ftime": 3.2136409282684326}, {"fpath": "downloaded_repos/spicywebau_craft-neo/src/web/assets/input/src/scripts/Input.js", "ftime": 4.15495491027832}]}, "matching_time": {"total_time": 6.8911590576171875, "per_file_and_rule_time": {"mean": 0.01403494716418979, "std_dev": 0.003034332032026103}, "very_slow_stats": {"time_ratio": 0.5768182084898996, "count_ratio": 0.032586558044806514}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/spicywebau_craft-neo/src/web/assets/configurator/src/scripts/Configurator.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.18138504028320312}, {"fpath": "downloaded_repos/spicywebau_craft-neo/src/web/assets/input/src/scripts/Input.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.19095611572265625}, {"fpath": "downloaded_repos/spicywebau_craft-neo/src/web/assets/configurator/src/scripts/Configurator.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.20799803733825684}, {"fpath": "downloaded_repos/spicywebau_craft-neo/src/web/assets/configurator/src/scripts/BlockTypeSettings.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.21678805351257324}, {"fpath": "downloaded_repos/spicywebau_craft-neo/src/web/assets/configurator/src/scripts/BlockTypeSettings.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.2211768627166748}, {"fpath": "downloaded_repos/spicywebau_craft-neo/src/web/assets/input/src/scripts/Input.js", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 0.2513089179992676}, {"fpath": "downloaded_repos/spicywebau_craft-neo/src/web/assets/input/src/scripts/Block.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.2627840042114258}, {"fpath": "downloaded_repos/spicywebau_craft-neo/src/web/assets/input/src/scripts/Block.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.30646395683288574}, {"fpath": "downloaded_repos/spicywebau_craft-neo/src/web/assets/input/src/scripts/Block.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.6325209140777588}, {"fpath": "downloaded_repos/spicywebau_craft-neo/src/web/assets/input/src/scripts/Input.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.7404890060424805}]}, "tainting_time": {"total_time": 3.4234633445739746, "per_def_and_rule_time": {"mean": 0.0011601027938237805, "std_dev": 1.6984970402308082e-05}, "very_slow_stats": {"time_ratio": 0.0487167462239483, "count_ratio": 0.0010166045408336157}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/spicywebau_craft-neo/src/web/assets/input/src/scripts/Input.js", "fline": 1, "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 0.05282783508300781}, {"fpath": "downloaded_repos/spicywebau_craft-neo/src/web/assets/input/src/scripts/Input.js", "fline": 1, "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.05298805236816406}, {"fpath": "downloaded_repos/spicywebau_craft-neo/src/web/assets/input/src/scripts/Input.js", "fline": 1, "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 0.060964107513427734}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}