{"version": "1.130.0", "results": [{"check_id": "javascript.shelljs.security.shelljs-exec-injection.shelljs-exec-injection", "path": "downloaded_repos/serenity-kit_opaque/bin/build.js", "start": {"line": 114, "col": 3, "offset": 3328}, "end": {"line": 116, "col": 4, "offset": 3456}, "extra": {"message": "If unverified user data can reach the `exec` method it can result in Remote Code Execution", "metadata": {"owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["<PERSON><PERSON>s"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/javascript.shelljs.security.shelljs-exec-injection.shelljs-exec-injection", "shortlink": "https://sg.run/AvEB"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/serenity-kit_opaque/examples/client-simple-vite/index.html", "start": {"line": 6, "col": 5, "offset": 120}, "end": {"line": 6, "col": 56, "offset": 171}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/serenity-kit_opaque/examples/client-simple-vite/main.js", "start": {"line": 38, "col": 15, "offset": 956}, "end": {"line": 38, "col": 39, "offset": 980}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/serenity-kit_opaque/examples/client-simple-webpack/index.html", "start": {"line": 6, "col": 5, "offset": 123}, "end": {"line": 6, "col": 56, "offset": 174}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/serenity-kit_opaque/examples/client-simple-webpack/index.js", "start": {"line": 38, "col": 15, "offset": 956}, "end": {"line": 38, "col": 39, "offset": 980}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/serenity-kit_opaque/examples/client-with-password-reset/index.html", "start": {"line": 6, "col": 5, "offset": 128}, "end": {"line": 6, "col": 56, "offset": 179}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/serenity-kit_opaque/examples/client-with-password-reset/index.js", "start": {"line": 59, "col": 15, "offset": 1914}, "end": {"line": 59, "col": 39, "offset": 1938}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/api/FileStore.ts", "start": {"line": 29, "col": 34, "offset": 861}, "end": {"line": 29, "col": 42, "offset": 869}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/utils/client.ts", "start": {"line": 9, "col": 15, "offset": 236}, "end": {"line": 9, "col": 32, "offset": 253}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/serenity-kit_opaque/examples/fullstack-simple-nextjs/src/app/api/FileStore.ts", "start": {"line": 21, "col": 34, "offset": 499}, "end": {"line": 21, "col": 42, "offset": 507}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/serenity-kit_opaque/examples/fullstack-simple-nextjs/src/app/page.tsx", "start": {"line": 7, "col": 15, "offset": 190}, "end": {"line": 7, "col": 32, "offset": 207}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/serenity-kit_opaque/examples/server-simple/src/server.js", "start": {"line": 69, "col": 9, "offset": 1816}, "end": {"line": 69, "col": 81, "offset": 1888}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/serenity-kit_opaque/examples/server-with-password-reset/src/server.js", "start": {"line": 72, "col": 9, "offset": 1918}, "end": {"line": 72, "col": 81, "offset": 1990}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/serenity-kit_opaque/.changeset/README.md", "downloaded_repos/serenity-kit_opaque/.changeset/config.json", "downloaded_repos/serenity-kit_opaque/.github/workflows/bump-version.yml", "downloaded_repos/serenity-kit_opaque/.github/workflows/example-e2e-tests.yml", "downloaded_repos/serenity-kit_opaque/.github/workflows/lint-and-format.yml", "downloaded_repos/serenity-kit_opaque/.github/workflows/release.yml", "downloaded_repos/serenity-kit_opaque/.github/workflows/tests.yml", "downloaded_repos/serenity-kit_opaque/.gitignore", "downloaded_repos/serenity-kit_opaque/.prettierignore", "downloaded_repos/serenity-kit_opaque/.prettierrc.json", "downloaded_repos/serenity-kit_opaque/CONTRIBUTING.md", "downloaded_repos/serenity-kit_opaque/Cargo.toml", "downloaded_repos/serenity-kit_opaque/LICENSE", "downloaded_repos/serenity-kit_opaque/README.md", "downloaded_repos/serenity-kit_opaque/SECURITY.md", "downloaded_repos/serenity-kit_opaque/babel.config.js", "downloaded_repos/serenity-kit_opaque/bin/build.js", "downloaded_repos/serenity-kit_opaque/bin/generate-dotenv.js", "downloaded_repos/serenity-kit_opaque/bin/templates/client.ts", "downloaded_repos/serenity-kit_opaque/bin/templates/index.ts", "downloaded_repos/serenity-kit_opaque/bin/templates/server.ts", "downloaded_repos/serenity-kit_opaque/examples/client-simple-vite/.gitignore", "downloaded_repos/serenity-kit_opaque/examples/client-simple-vite/index.html", "downloaded_repos/serenity-kit_opaque/examples/client-simple-vite/main.js", "downloaded_repos/serenity-kit_opaque/examples/client-simple-vite/package.json", "downloaded_repos/serenity-kit_opaque/examples/client-simple-vite/vite.config.js", "downloaded_repos/serenity-kit_opaque/examples/client-simple-webpack/.gitignore", "downloaded_repos/serenity-kit_opaque/examples/client-simple-webpack/index.html", "downloaded_repos/serenity-kit_opaque/examples/client-simple-webpack/index.js", "downloaded_repos/serenity-kit_opaque/examples/client-simple-webpack/package.json", "downloaded_repos/serenity-kit_opaque/examples/client-simple-webpack/playwright.config.ts", "downloaded_repos/serenity-kit_opaque/examples/client-simple-webpack/webpack.config.js", "downloaded_repos/serenity-kit_opaque/examples/client-with-password-reset/.gitignore", "downloaded_repos/serenity-kit_opaque/examples/client-with-password-reset/index.html", "downloaded_repos/serenity-kit_opaque/examples/client-with-password-reset/index.js", "downloaded_repos/serenity-kit_opaque/examples/client-with-password-reset/package.json", "downloaded_repos/serenity-kit_opaque/examples/client-with-password-reset/playwright.config.ts", "downloaded_repos/serenity-kit_opaque/examples/client-with-password-reset/webpack.config.js", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/.gitignore", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/README.md", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/eslint.config.mjs", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/jest.config.js", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/next.config.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/package.json", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/playwright.config.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/postcss.config.mjs", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/Button.tsx", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/CredentialsForm.tsx", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/Demoflow.tsx", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/LoginForm.tsx", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/api/Datastore.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/api/FileStore.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/api/RedisStore.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/api/db.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/api/env.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/api/locker/route.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/api/login/finish/route.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/api/login/start/route.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/api/logout/route.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/api/rateLimiter.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/api/recovery/login/finish/route.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/api/recovery/login/start/route.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/api/recovery/register/finish/route.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/api/recovery/register/start/route.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/api/recovery/route.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/api/register/finish/route.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/api/register/start/route.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/api/schema.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/api/withUserSession.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/globals.css", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/layout.tsx", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/page.tsx", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/private/CreateRecoveryKeyButton.tsx", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/private/FetchRequest.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/private/Locker.tsx", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/private/LogoutButton.tsx", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/private/RemoveRecoveryKeyButton.tsx", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/private/page.tsx", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/private/useLockerRequest.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/recovery/RecoveryForm.tsx", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/recovery/page.tsx", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/register/RegistrationForm.tsx", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/register/page.tsx", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/utils/auth.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/utils/client.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/utils/demoflow.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/utils/isLockerObject.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/utils/locker/client/createLocker.test.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/utils/locker/client/createLocker.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/utils/locker/client/createLockerSecretKey.test.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/utils/locker/client/createLockerSecretKey.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/utils/locker/client/createRecoveryLockbox.test.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/utils/locker/client/createRecoveryLockbox.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/utils/locker/client/decryptLocker.test.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/utils/locker/client/decryptLocker.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/utils/locker/client/decryptLockerFromRecoveryLockbox.test.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/utils/locker/client/decryptLockerFromRecoveryLockbox.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/utils/locker/index.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/utils/locker/server/isValidLocker.test.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/utils/locker/server/isValidLocker.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/utils/locker/types.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/utils/session/createAuthorizationToken.test.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/utils/session/createAuthorizationToken.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/utils/session/index.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/utils/session/types.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/tsconfig.json", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/verifpal/locker_client-server.vp", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/verifpal/locker_client-to-client.vp", "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/verifpal/locker_server-client.vp", "downloaded_repos/serenity-kit_opaque/examples/fullstack-simple-nextjs/.gitignore", "downloaded_repos/serenity-kit_opaque/examples/fullstack-simple-nextjs/README.md", "downloaded_repos/serenity-kit_opaque/examples/fullstack-simple-nextjs/eslint.config.mjs", "downloaded_repos/serenity-kit_opaque/examples/fullstack-simple-nextjs/next.config.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-simple-nextjs/package.json", "downloaded_repos/serenity-kit_opaque/examples/fullstack-simple-nextjs/postcss.config.mjs", "downloaded_repos/serenity-kit_opaque/examples/fullstack-simple-nextjs/src/app/api/Datastore.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-simple-nextjs/src/app/api/FileStore.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-simple-nextjs/src/app/api/RedisStore.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-simple-nextjs/src/app/api/db.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-simple-nextjs/src/app/api/env.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-simple-nextjs/src/app/api/login/finish/route.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-simple-nextjs/src/app/api/login/start/route.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-simple-nextjs/src/app/api/rateLimiter.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-simple-nextjs/src/app/api/register/finish/route.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-simple-nextjs/src/app/api/register/start/route.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-simple-nextjs/src/app/api/schema.ts", "downloaded_repos/serenity-kit_opaque/examples/fullstack-simple-nextjs/src/app/globals.css", "downloaded_repos/serenity-kit_opaque/examples/fullstack-simple-nextjs/src/app/layout.tsx", "downloaded_repos/serenity-kit_opaque/examples/fullstack-simple-nextjs/src/app/page.tsx", "downloaded_repos/serenity-kit_opaque/examples/fullstack-simple-nextjs/tsconfig.json", "downloaded_repos/serenity-kit_opaque/examples/server-simple/.gitignore", "downloaded_repos/serenity-kit_opaque/examples/server-simple/package.json", "downloaded_repos/serenity-kit_opaque/examples/server-simple/src/InMemoryStore.js", "downloaded_repos/serenity-kit_opaque/examples/server-simple/src/RedisStore.js", "downloaded_repos/serenity-kit_opaque/examples/server-simple/src/schema.js", "downloaded_repos/serenity-kit_opaque/examples/server-simple/src/server.js", "downloaded_repos/serenity-kit_opaque/examples/server-simple/src/types.d.ts", "downloaded_repos/serenity-kit_opaque/examples/server-with-password-reset/.gitignore", "downloaded_repos/serenity-kit_opaque/examples/server-with-password-reset/package.json", "downloaded_repos/serenity-kit_opaque/examples/server-with-password-reset/src/InMemoryStore.js", "downloaded_repos/serenity-kit_opaque/examples/server-with-password-reset/src/RedisStore.js", "downloaded_repos/serenity-kit_opaque/examples/server-with-password-reset/src/schema.js", "downloaded_repos/serenity-kit_opaque/examples/server-with-password-reset/src/server.js", "downloaded_repos/serenity-kit_opaque/examples/server-with-password-reset/src/types.d.ts", "downloaded_repos/serenity-kit_opaque/package.json", "downloaded_repos/serenity-kit_opaque/pnpm-lock.yaml", "downloaded_repos/serenity-kit_opaque/pnpm-workspace.yaml", "downloaded_repos/serenity-kit_opaque/rollup.config.js", "downloaded_repos/serenity-kit_opaque/src/lib.rs", "downloaded_repos/serenity-kit_opaque/tsconfig.json"], "skipped": [{"path": "downloaded_repos/serenity-kit_opaque/build/p256/package.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/serenity-kit_opaque/build/ristretto/package.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/serenity-kit_opaque/examples/client-simple-webpack/tests/full-flow.e2e.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/serenity-kit_opaque/examples/client-with-password-reset/tests/full-flow.e2e.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/tests/full-flow.e2e.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/serenity-kit_opaque/tests/opaque.test.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.7083110809326172, "profiling_times": {"config_time": 6.005875587463379, "core_time": 4.795365333557129, "ignores_time": 0.0016491413116455078, "total_time": 10.80367660522461}, "parsing_time": {"total_time": 1.7337634563446045, "per_file_time": {"mean": 0.013981963357617783, "std_dev": 0.0011120795304979509}, "very_slow_stats": {"time_ratio": 0.18453269152306312, "count_ratio": 0.008064516129032258}, "very_slow_files": [{"fpath": "downloaded_repos/serenity-kit_opaque/pnpm-lock.yaml", "ftime": 0.31993603706359863}]}, "scanning_time": {"total_time": 10.012179136276245, "per_file_time": {"mean": 0.023392941907187503, "std_dev": 0.012692985363351318}, "very_slow_stats": {"time_ratio": 0.1847503756656616, "count_ratio": 0.002336448598130841}, "very_slow_files": [{"fpath": "downloaded_repos/serenity-kit_opaque/pnpm-lock.yaml", "ftime": 1.8497538566589355}]}, "matching_time": {"total_time": 4.801546812057495, "per_file_and_rule_time": {"mean": 0.006419180230023388, "std_dev": 0.00034755517233161864}, "very_slow_stats": {"time_ratio": 0.2089865573750467, "count_ratio": 0.009358288770053475}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/serenity-kit_opaque/examples/server-simple/src/server.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.1008598804473877}, {"fpath": "downloaded_repos/serenity-kit_opaque/examples/server-simple/src/server.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.10366606712341309}, {"fpath": "downloaded_repos/serenity-kit_opaque/examples/fullstack-simple-nextjs/src/app/page.tsx", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.11611080169677734}, {"fpath": "downloaded_repos/serenity-kit_opaque/pnpm-lock.yaml", "rule_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "time": 0.12786006927490234}, {"fpath": "downloaded_repos/serenity-kit_opaque/examples/client-with-password-reset/index.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.12967801094055176}, {"fpath": "downloaded_repos/serenity-kit_opaque/examples/server-with-password-reset/src/server.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.16803693771362305}, {"fpath": "downloaded_repos/serenity-kit_opaque/pnpm-lock.yaml", "rule_id": "yaml.kubernetes.security.allow-privilege-escalation-no-securitycontext.allow-privilege-escalation-no-securitycontext", "time": 0.2572469711303711}]}, "tainting_time": {"total_time": 1.079310655593872, "per_def_and_rule_time": {"mean": 0.0010230432754444285, "std_dev": 7.83207249887538e-06}, "very_slow_stats": {"time_ratio": 0.06061466897078987, "count_ratio": 0.0009478672985781991}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/serenity-kit_opaque/examples/fullstack-e2e-encrypted-locker-nextjs/src/app/utils/locker/server/isValidLocker.test.ts", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.06542205810546875}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}