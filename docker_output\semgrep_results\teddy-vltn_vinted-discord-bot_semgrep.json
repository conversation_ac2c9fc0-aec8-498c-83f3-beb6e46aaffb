{"version": "1.130.0", "results": [{"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/teddy-vltn_vinted-discord-bot/docker-compose.mongo-express.yml", "start": {"line": 4, "col": 3, "offset": 28}, "end": {"line": 4, "col": 16, "offset": 41}, "extra": {"message": "Service 'mongo-express' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/teddy-vltn_vinted-discord-bot/docker-compose.mongo-express.yml", "start": {"line": 4, "col": 3, "offset": 28}, "end": {"line": 4, "col": 16, "offset": 41}, "extra": {"message": "Service 'mongo-express' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/teddy-vltn_vinted-discord-bot/docker-compose.yml", "start": {"line": 4, "col": 3, "offset": 28}, "end": {"line": 4, "col": 6, "offset": 31}, "extra": {"message": "Service 'app' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/teddy-vltn_vinted-discord-bot/docker-compose.yml", "start": {"line": 4, "col": 3, "offset": 28}, "end": {"line": 4, "col": 6, "offset": 31}, "extra": {"message": "Service 'app' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/teddy-vltn_vinted-discord-bot/docker-compose.yml", "start": {"line": 20, "col": 3, "offset": 356}, "end": {"line": 20, "col": 10, "offset": 363}, "extra": {"message": "Service 'mongodb' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/teddy-vltn_vinted-discord-bot/docker-compose.yml", "start": {"line": 20, "col": 3, "offset": 356}, "end": {"line": 20, "col": 10, "offset": 363}, "extra": {"message": "Service 'mongodb' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/teddy-vltn_vinted-discord-bot/src/utils/language.js", "start": {"line": 18, "col": 57, "offset": 550}, "end": {"line": 18, "col": 61, "offset": 554}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/teddy-vltn_vinted-discord-bot/.env", "downloaded_repos/teddy-vltn_vinted-discord-bot/.gitignore", "downloaded_repos/teddy-vltn_vinted-discord-bot/LICENSE", "downloaded_repos/teddy-vltn_vinted-discord-bot/doc/AnimationCreatePrivate.gif", "downloaded_repos/teddy-vltn_vinted-discord-bot/doc/relations.png", "downloaded_repos/teddy-vltn_vinted-discord-bot/docker-compose.mongo-express.yml", "downloaded_repos/teddy-vltn_vinted-discord-bot/docker-compose.yml", "downloaded_repos/teddy-vltn_vinted-discord-bot/locales/de.json", "downloaded_repos/teddy-vltn_vinted-discord-bot/locales/en-GB.json", "downloaded_repos/teddy-vltn_vinted-discord-bot/locales/es-ES.json", "downloaded_repos/teddy-vltn_vinted-discord-bot/locales/fr.json", "downloaded_repos/teddy-vltn_vinted-discord-bot/locales/it.json", "downloaded_repos/teddy-vltn_vinted-discord-bot/locales/pl.json", "downloaded_repos/teddy-vltn_vinted-discord-bot/main.js", "downloaded_repos/teddy-vltn_vinted-discord-bot/package.json", "downloaded_repos/teddy-vltn_vinted-discord-bot/readme.md", "downloaded_repos/teddy-vltn_vinted-discord-bot/src/api/fetchCatalogInitializers.js", "downloaded_repos/teddy-vltn_vinted-discord-bot/src/api/fetchCatalogItems.js", "downloaded_repos/teddy-vltn_vinted-discord-bot/src/api/fetchCookie.js", "downloaded_repos/teddy-vltn_vinted-discord-bot/src/api/fetchItem.js", "downloaded_repos/teddy-vltn_vinted-discord-bot/src/bot/commands/create_private_channel.js", "downloaded_repos/teddy-vltn_vinted-discord-bot/src/bot/commands/delete_all_private_channels.js", "downloaded_repos/teddy-vltn_vinted-discord-bot/src/bot/commands/delete_private_channel.js", "downloaded_repos/teddy-vltn_vinted-discord-bot/src/bot/commands/info.js", "downloaded_repos/teddy-vltn_vinted-discord-bot/src/bot/commands/link_public_channel.js", "downloaded_repos/teddy-vltn_vinted-discord-bot/src/bot/commands/set_max_channels.js", "downloaded_repos/teddy-vltn_vinted-discord-bot/src/bot/commands/set_mentions.js", "downloaded_repos/teddy-vltn_vinted-discord-bot/src/bot/commands/start_monitoring.js", "downloaded_repos/teddy-vltn_vinted-discord-bot/src/bot/commands/stop_monitoring.js", "downloaded_repos/teddy-vltn_vinted-discord-bot/src/bot/commands/unlink_public_channel.js", "downloaded_repos/teddy-vltn_vinted-discord-bot/src/bot/commands_handler.js", "downloaded_repos/teddy-vltn_vinted-discord-bot/src/bot/components/base_embeds.js", "downloaded_repos/teddy-vltn_vinted-discord-bot/src/bot/components/item_embed.js", "downloaded_repos/teddy-vltn_vinted-discord-bot/src/client.js", "downloaded_repos/teddy-vltn_vinted-discord-bot/src/crud.js", "downloaded_repos/teddy-vltn_vinted-discord-bot/src/database.js", "downloaded_repos/teddy-vltn_vinted-discord-bot/src/entities/vinted_item.js", "downloaded_repos/teddy-vltn_vinted-discord-bot/src/helpers/execute_helper.js", "downloaded_repos/teddy-vltn_vinted-discord-bot/src/services/catalog_service.js", "downloaded_repos/teddy-vltn_vinted-discord-bot/src/services/discord_service.js", "downloaded_repos/teddy-vltn_vinted-discord-bot/src/services/url_service.js", "downloaded_repos/teddy-vltn_vinted-discord-bot/src/t.js", "downloaded_repos/teddy-vltn_vinted-discord-bot/src/utils/config_manager.js", "downloaded_repos/teddy-vltn_vinted-discord-bot/src/utils/event_emitter.js", "downloaded_repos/teddy-vltn_vinted-discord-bot/src/utils/language.js", "downloaded_repos/teddy-vltn_vinted-discord-bot/src/utils/logger.js", "downloaded_repos/teddy-vltn_vinted-discord-bot/src/utils/proxies.js", "downloaded_repos/teddy-vltn_vinted-discord-bot/src/utils/proxy_manager.js", "downloaded_repos/teddy-vltn_vinted-discord-bot/src/utils/request_builder.js", "downloaded_repos/teddy-vltn_vinted-discord-bot/start.sh", "downloaded_repos/teddy-vltn_vinted-discord-bot/stop.sh"], "skipped": [{"path": "downloaded_repos/teddy-vltn_vinted-discord-bot/doc/AnimationMentions.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/teddy-vltn_vinted-discord-bot/doc/AnimationPublic.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/teddy-vltn_vinted-discord-bot/doc/AnimationStartMonitoring.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/teddy-vltn_vinted-discord-bot/doc/bot.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/teddy-vltn_vinted-discord-bot/doc/premium.png", "reason": "exceeded_size_limit"}]}, "time": {"rules": [], "rules_parse_time": 1.7121520042419434, "profiling_times": {"config_time": 6.122730731964111, "core_time": 2.716224193572998, "ignores_time": 0.0019478797912597656, "total_time": 8.841713905334473}, "parsing_time": {"total_time": 0.818528413772583, "per_file_time": {"mean": 0.018189520306057406, "std_dev": 0.0003182190273019156}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 3.1851985454559326, "per_file_time": {"mean": 0.021668017316026758, "std_dev": 0.0024139925587696866}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 1.2089323997497559, "per_file_and_rule_time": {"mean": 0.004168732412930191, "std_dev": 5.660681039503378e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.36244869232177734, "per_def_and_rule_time": {"mean": 0.0005104911159461653, "std_dev": 1.8725643166117524e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}