#!/usr/bin/env python3

"""
Comprehensive Vulnerability Scanner Suite
=========================================

Runs all CVE trend-based scanners in sequence to maximize vulnerability discovery
while minimizing time wasted on false positives.
"""

import os
import sys
import subprocess
import time
from datetime import datetime

def print_banner():
    print("🎯" + "=" * 60 + "🎯")
    print("    COMPREHENSIVE VULNERABILITY SCANNER SUITE")
    print("    CVE Trend-Based GitHub Repository Analysis")
    print("🎯" + "=" * 60 + "🎯")
    print()

def check_requirements():
    """Check if required dependencies are installed"""
    print("🔍 Checking requirements...")
    
    required_packages = ['requests', 'langdetect']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"  ❌ {package}")
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print("Install with: pip install " + " ".join(missing_packages))
        return False
    
    # Check GitHub token
    github_token = os.getenv("GITHUB_TOKEN")
    if not github_token or github_token == "your_github_token_here":
        print("  ❌ GITHUB_TOKEN not set")
        print("  Set with: export GITHUB_TOKEN='your_token_here'")
        return False
    else:
        print("  ✅ GITHUB_TOKEN configured")
    
    print("✅ All requirements satisfied\n")
    return True

def run_scanner(script_name, description, args=None):
    """Run a scanner script and return success status"""
    print(f"🚀 Running {description}...")
    print(f"   Script: {script_name}")
    
    if args is None:
        args = []
    
    start_time = time.time()
    
    try:
        # Check if script exists
        if not os.path.exists(script_name):
            print(f"   ❌ Script not found: {script_name}")
            return False
        
        # Run the script
        cmd = [sys.executable, script_name] + args
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)  # 30 min timeout
        
        end_time = time.time()
        duration = end_time - start_time
        
        if result.returncode == 0:
            print(f"   ✅ Completed successfully in {duration:.1f}s")
            return True
        else:
            print(f"   ❌ Failed with return code {result.returncode}")
            if result.stderr:
                print(f"   Error: {result.stderr[:200]}...")
            return False
    
    except subprocess.TimeoutExpired:
        print(f"   ⏰ Timeout after 30 minutes")
        return False
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        return False

def consolidate_results():
    """Consolidate results from all scanners"""
    print("📊 Consolidating results...")
    
    output_files = [
        "vulnerable_projects.csv",
        "high_priority_vulnerable_projects.csv",
        "cve_trend_targets.csv",
        "vulnerability_patterns_found.csv"
    ]
    
    found_files = []
    total_repos = 0
    
    for file in output_files:
        if os.path.exists(file):
            found_files.append(file)
            try:
                with open(file, 'r', encoding='utf-8') as f:
                    lines = sum(1 for line in f) - 1  # Subtract header
                    total_repos += lines
                    print(f"  📄 {file}: {lines} repositories")
            except:
                print(f"  📄 {file}: Found but couldn't count lines")
    
    print(f"\n📈 Total repositories identified: {total_repos}")
    print(f"📁 Output files created: {len(found_files)}")
    
    return found_files

def generate_summary_report():
    """Generate a summary report of all findings"""
    print("\n📋 Generating summary report...")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    summary_file = f"vulnerability_scan_summary_{timestamp}.txt"
    
    try:
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("VULNERABILITY SCAN SUMMARY REPORT\n")
            f.write("=" * 50 + "\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # List all output files
            f.write("OUTPUT FILES:\n")
            f.write("-" * 20 + "\n")
            
            output_files = [
                ("vulnerable_projects.csv", "All repositories with CVE risk analysis"),
                ("high_priority_vulnerable_projects.csv", "High-priority targets only"),
                ("cve_trend_targets.csv", "CVE probability analysis"),
                ("vulnerability_patterns_found.csv", "Specific vulnerability patterns"),
                ("cve_risk_analysis.txt", "Detailed risk factor analysis"),
                ("cve_trend_analysis.json", "Detailed CVE trend analysis"),
                ("pattern_analysis.json", "Detailed pattern analysis")
            ]
            
            for filename, description in output_files:
                if os.path.exists(filename):
                    f.write(f"✅ {filename} - {description}\n")
                else:
                    f.write(f"❌ {filename} - {description}\n")
            
            f.write("\nRECOMMENDED NEXT STEPS:\n")
            f.write("-" * 25 + "\n")
            f.write("1. Review high_priority_vulnerable_projects.csv for immediate targets\n")
            f.write("2. Check vulnerability_patterns_found.csv for specific vulnerability types\n")
            f.write("3. Cross-reference findings across multiple output files\n")
            f.write("4. Prioritize repositories with multiple risk indicators\n")
            f.write("5. Follow responsible disclosure practices\n")
        
        print(f"  ✅ Summary saved to: {summary_file}")
        return summary_file
    
    except Exception as e:
        print(f"  ❌ Failed to generate summary: {e}")
        return None

def main():
    print_banner()
    
    # Check requirements
    if not check_requirements():
        print("❌ Requirements not met. Please fix the issues above.")
        sys.exit(1)
    
    print("🎯 Starting comprehensive vulnerability scan...")
    print("   This may take 15-30 minutes depending on API rate limits\n")
    
    # Define scanners to run
    scanners = [
        {
            "script": "advanced_scanner.py",
            "description": "Enhanced Advanced Scanner (CVE Risk Analysis)",
            "args": []
        },
        {
            "script": "cve_trend_scanner.py", 
            "description": "CVE Trend Scanner (Probability Analysis)",
            "args": ["--pages", "3", "--min-stars", "50", "--max-stars", "3000"]
        },
        {
            "script": "vulnerability_pattern_scanner.py",
            "description": "Vulnerability Pattern Scanner (Code Analysis)",
            "args": []
        }
    ]
    
    # Run each scanner
    successful_scans = 0
    total_scans = len(scanners)
    
    for i, scanner in enumerate(scanners, 1):
        print(f"\n[{i}/{total_scans}] " + "=" * 50)
        success = run_scanner(
            scanner["script"], 
            scanner["description"], 
            scanner["args"]
        )
        if success:
            successful_scans += 1
        print("=" * 60)
    
    # Consolidate results
    print(f"\n🎯 SCAN COMPLETE")
    print("=" * 40)
    print(f"Successful scans: {successful_scans}/{total_scans}")
    
    if successful_scans > 0:
        found_files = consolidate_results()
        summary_file = generate_summary_report()
        
        print(f"\n✅ RESULTS READY")
        print("=" * 30)
        print("📁 Key output files:")
        
        priority_files = [
            "high_priority_vulnerable_projects.csv",
            "vulnerability_patterns_found.csv", 
            "cve_trend_targets.csv"
        ]
        
        for file in priority_files:
            if os.path.exists(file):
                print(f"  🎯 {file}")
        
        if summary_file:
            print(f"  📋 {summary_file}")
        
        print(f"\n🚀 NEXT STEPS:")
        print("1. Review high-priority targets first")
        print("2. Cross-reference findings across scanners")
        print("3. Focus on repositories with multiple risk indicators")
        print("4. Follow responsible disclosure practices")
        
    else:
        print("❌ No scans completed successfully")
        print("Check error messages above and ensure:")
        print("  - GitHub token is valid")
        print("  - Internet connection is stable")
        print("  - API rate limits are not exceeded")
    
    print(f"\n🎯 Scan completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
