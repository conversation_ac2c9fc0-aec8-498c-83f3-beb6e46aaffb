{"version": "1.130.0", "results": [{"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/renoki-co_php-k8s/src/Traits/Cluster/LoadsFromKubeConfig.php", "start": {"line": 60, "col": 32, "offset": 1773}, "end": {"line": 60, "col": 37, "offset": 1778}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/renoki-co_php-k8s/src/Traits/Cluster/LoadsFromKubeConfig.php", "start": {"line": 60, "col": 67, "offset": 1808}, "end": {"line": 60, "col": 72, "offset": 1813}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "path": "downloaded_repos/renoki-co_php-k8s/src/Traits/Cluster/MakesWebsocketCalls.php", "start": {"line": 153, "col": 27, "offset": 4085}, "end": {"line": 153, "col": 32, "offset": 4090}, "extra": {"message": "Insecure WebSocket Detected. WebSocket Secure (wss) should be used for all WebSocket connections.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "13.5.1 Insecure WebSocket", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x21-V13-API.md#v135-websocket-security-requirements", "section": "V13: API and Web Service Verification Requirements", "version": "4"}, "category": "security", "technology": ["regex"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "shortlink": "https://sg.run/GWyz"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/renoki-co_php-k8s/.github/workflows/ci.yml", "start": {"line": 69, "col": 53, "offset": 1803}, "end": {"line": 69, "col": 69, "offset": 1819}}, {"path": "downloaded_repos/renoki-co_php-k8s/.github/workflows/ci.yml", "start": {"line": 69, "col": 97, "offset": 1803}, "end": {"line": 69, "col": 115, "offset": 1821}}, {"path": "downloaded_repos/renoki-co_php-k8s/.github/workflows/ci.yml", "start": {"line": 70, "col": 19, "offset": 1803}, "end": {"line": 70, "col": 22, "offset": 1806}}]], "message": "Syntax error at line downloaded_repos/renoki-co_php-k8s/.github/workflows/ci.yml:69:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ matrix.laravel` was unexpected", "path": "downloaded_repos/renoki-co_php-k8s/.github/workflows/ci.yml", "spans": [{"file": "downloaded_repos/renoki-co_php-k8s/.github/workflows/ci.yml", "start": {"line": 69, "col": 53, "offset": 1803}, "end": {"line": 69, "col": 69, "offset": 1819}}, {"file": "downloaded_repos/renoki-co_php-k8s/.github/workflows/ci.yml", "start": {"line": 69, "col": 97, "offset": 1803}, "end": {"line": 69, "col": 115, "offset": 1821}}, {"file": "downloaded_repos/renoki-co_php-k8s/.github/workflows/ci.yml", "start": {"line": 70, "col": 19, "offset": 1803}, "end": {"line": 70, "col": 22, "offset": 1806}}]}], "paths": {"scanned": ["downloaded_repos/renoki-co_php-k8s/.codecov.yml", "downloaded_repos/renoki-co_php-k8s/.editorconfig", "downloaded_repos/renoki-co_php-k8s/.gitattributes", "downloaded_repos/renoki-co_php-k8s/.github/FUNDING.yml", "downloaded_repos/renoki-co_php-k8s/.github/dependabot.yml", "downloaded_repos/renoki-co_php-k8s/.github/stale.yml", "downloaded_repos/renoki-co_php-k8s/.github/workflows/ci.yml", "downloaded_repos/renoki-co_php-k8s/.gitignore", "downloaded_repos/renoki-co_php-k8s/.styleci.yml", "downloaded_repos/renoki-co_php-k8s/CONTRIBUTING.md", "downloaded_repos/renoki-co_php-k8s/LICENSE", "downloaded_repos/renoki-co_php-k8s/README.md", "downloaded_repos/renoki-co_php-k8s/composer.json", "downloaded_repos/renoki-co_php-k8s/phpunit.xml", "downloaded_repos/renoki-co_php-k8s/psalm.xml", "downloaded_repos/renoki-co_php-k8s/src/Contracts/Attachable.php", "downloaded_repos/renoki-co_php-k8s/src/Contracts/Dnsable.php", "downloaded_repos/renoki-co_php-k8s/src/Contracts/Executable.php", "downloaded_repos/renoki-co_php-k8s/src/Contracts/InteractsWithK8sCluster.php", "downloaded_repos/renoki-co_php-k8s/src/Contracts/Loggable.php", "downloaded_repos/renoki-co_php-k8s/src/Contracts/Podable.php", "downloaded_repos/renoki-co_php-k8s/src/Contracts/Scalable.php", "downloaded_repos/renoki-co_php-k8s/src/Contracts/Watchable.php", "downloaded_repos/renoki-co_php-k8s/src/Exceptions/KubeConfigBaseEncodedDataInvalid.php", "downloaded_repos/renoki-co_php-k8s/src/Exceptions/KubeConfigClusterNotFound.php", "downloaded_repos/renoki-co_php-k8s/src/Exceptions/KubeConfigContextNotFound.php", "downloaded_repos/renoki-co_php-k8s/src/Exceptions/KubeConfigUserNotFound.php", "downloaded_repos/renoki-co_php-k8s/src/Exceptions/KubernetesAPIException.php", "downloaded_repos/renoki-co_php-k8s/src/Exceptions/KubernetesAttachException.php", "downloaded_repos/renoki-co_php-k8s/src/Exceptions/KubernetesExecException.php", "downloaded_repos/renoki-co_php-k8s/src/Exceptions/KubernetesLogsException.php", "downloaded_repos/renoki-co_php-k8s/src/Exceptions/KubernetesScalingException.php", "downloaded_repos/renoki-co_php-k8s/src/Exceptions/KubernetesWatchException.php", "downloaded_repos/renoki-co_php-k8s/src/Exceptions/PhpK8sException.php", "downloaded_repos/renoki-co_php-k8s/src/Instances/Affinity.php", "downloaded_repos/renoki-co_php-k8s/src/Instances/Container.php", "downloaded_repos/renoki-co_php-k8s/src/Instances/Expression.php", "downloaded_repos/renoki-co_php-k8s/src/Instances/Instance.php", "downloaded_repos/renoki-co_php-k8s/src/Instances/MountedVolume.php", "downloaded_repos/renoki-co_php-k8s/src/Instances/Probe.php", "downloaded_repos/renoki-co_php-k8s/src/Instances/ResourceMetric.php", "downloaded_repos/renoki-co_php-k8s/src/Instances/ResourceObject.php", "downloaded_repos/renoki-co_php-k8s/src/Instances/Rule.php", "downloaded_repos/renoki-co_php-k8s/src/Instances/Subject.php", "downloaded_repos/renoki-co_php-k8s/src/Instances/Volume.php", "downloaded_repos/renoki-co_php-k8s/src/Instances/Webhook.php", "downloaded_repos/renoki-co_php-k8s/src/K8s.php", "downloaded_repos/renoki-co_php-k8s/src/Kinds/K8sClusterRole.php", "downloaded_repos/renoki-co_php-k8s/src/Kinds/K8sClusterRoleBinding.php", "downloaded_repos/renoki-co_php-k8s/src/Kinds/K8sConfigMap.php", "downloaded_repos/renoki-co_php-k8s/src/Kinds/K8sCronJob.php", "downloaded_repos/renoki-co_php-k8s/src/Kinds/K8sDaemonSet.php", "downloaded_repos/renoki-co_php-k8s/src/Kinds/K8sDeployment.php", "downloaded_repos/renoki-co_php-k8s/src/Kinds/K8sEvent.php", "downloaded_repos/renoki-co_php-k8s/src/Kinds/K8sHorizontalPodAutoscaler.php", "downloaded_repos/renoki-co_php-k8s/src/Kinds/K8sIngress.php", "downloaded_repos/renoki-co_php-k8s/src/Kinds/K8sJob.php", "downloaded_repos/renoki-co_php-k8s/src/Kinds/K8sMutatingWebhookConfiguration.php", "downloaded_repos/renoki-co_php-k8s/src/Kinds/K8sNamespace.php", "downloaded_repos/renoki-co_php-k8s/src/Kinds/K8sNode.php", "downloaded_repos/renoki-co_php-k8s/src/Kinds/K8sPersistentVolume.php", "downloaded_repos/renoki-co_php-k8s/src/Kinds/K8sPersistentVolumeClaim.php", "downloaded_repos/renoki-co_php-k8s/src/Kinds/K8sPod.php", "downloaded_repos/renoki-co_php-k8s/src/Kinds/K8sPodDisruptionBudget.php", "downloaded_repos/renoki-co_php-k8s/src/Kinds/K8sResource.php", "downloaded_repos/renoki-co_php-k8s/src/Kinds/K8sRole.php", "downloaded_repos/renoki-co_php-k8s/src/Kinds/K8sRoleBinding.php", "downloaded_repos/renoki-co_php-k8s/src/Kinds/K8sScale.php", "downloaded_repos/renoki-co_php-k8s/src/Kinds/K8sSecret.php", "downloaded_repos/renoki-co_php-k8s/src/Kinds/K8sService.php", "downloaded_repos/renoki-co_php-k8s/src/Kinds/K8sServiceAccount.php", "downloaded_repos/renoki-co_php-k8s/src/Kinds/K8sStatefulSet.php", "downloaded_repos/renoki-co_php-k8s/src/Kinds/K8sStorageClass.php", "downloaded_repos/renoki-co_php-k8s/src/Kinds/K8sValidatingWebhookConfiguration.php", "downloaded_repos/renoki-co_php-k8s/src/KubernetesCluster.php", "downloaded_repos/renoki-co_php-k8s/src/PhpK8sServiceProvider.php", "downloaded_repos/renoki-co_php-k8s/src/ResourcesList.php", "downloaded_repos/renoki-co_php-k8s/src/Traits/Cluster/AuthenticatesCluster.php", "downloaded_repos/renoki-co_php-k8s/src/Traits/Cluster/ChecksClusterVersion.php", "downloaded_repos/renoki-co_php-k8s/src/Traits/Cluster/LoadsFromKubeConfig.php", "downloaded_repos/renoki-co_php-k8s/src/Traits/Cluster/MakesHttpCalls.php", "downloaded_repos/renoki-co_php-k8s/src/Traits/Cluster/MakesWebsocketCalls.php", "downloaded_repos/renoki-co_php-k8s/src/Traits/InitializesInstances.php", "downloaded_repos/renoki-co_php-k8s/src/Traits/InitializesResources.php", "downloaded_repos/renoki-co_php-k8s/src/Traits/Resource/CanScale.php", "downloaded_repos/renoki-co_php-k8s/src/Traits/Resource/HasAccessModes.php", "downloaded_repos/renoki-co_php-k8s/src/Traits/Resource/HasAnnotations.php", "downloaded_repos/renoki-co_php-k8s/src/Traits/Resource/HasAttributes.php", "downloaded_repos/renoki-co_php-k8s/src/Traits/Resource/HasEvents.php", "downloaded_repos/renoki-co_php-k8s/src/Traits/Resource/HasKind.php", "downloaded_repos/renoki-co_php-k8s/src/Traits/Resource/HasLabels.php", "downloaded_repos/renoki-co_php-k8s/src/Traits/Resource/HasMinimumSurge.php", "downloaded_repos/renoki-co_php-k8s/src/Traits/Resource/HasMountOptions.php", "downloaded_repos/renoki-co_php-k8s/src/Traits/Resource/HasName.php", "downloaded_repos/renoki-co_php-k8s/src/Traits/Resource/HasNamespace.php", "downloaded_repos/renoki-co_php-k8s/src/Traits/Resource/HasPods.php", "downloaded_repos/renoki-co_php-k8s/src/Traits/Resource/HasReplicas.php", "downloaded_repos/renoki-co_php-k8s/src/Traits/Resource/HasRules.php", "downloaded_repos/renoki-co_php-k8s/src/Traits/Resource/HasSelector.php", "downloaded_repos/renoki-co_php-k8s/src/Traits/Resource/HasSpec.php", "downloaded_repos/renoki-co_php-k8s/src/Traits/Resource/HasStatus.php", "downloaded_repos/renoki-co_php-k8s/src/Traits/Resource/HasStatusConditions.php", "downloaded_repos/renoki-co_php-k8s/src/Traits/Resource/HasStatusPhase.php", "downloaded_repos/renoki-co_php-k8s/src/Traits/Resource/HasStorageClass.php", "downloaded_repos/renoki-co_php-k8s/src/Traits/Resource/HasSubjects.php", "downloaded_repos/renoki-co_php-k8s/src/Traits/Resource/HasTemplate.php", "downloaded_repos/renoki-co_php-k8s/src/Traits/Resource/HasVersion.php", "downloaded_repos/renoki-co_php-k8s/src/Traits/Resource/HasWebhooks.php", "downloaded_repos/renoki-co_php-k8s/src/Traits/Resource/IsImmutable.php", "downloaded_repos/renoki-co_php-k8s/src/Traits/RunsClusterOperations.php"], "skipped": [{"path": "downloaded_repos/renoki-co_php-k8s/.github/workflows/ci.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/AffinityTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/ClusterRoleBindingTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/ClusterRoleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/ConfigMapTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/ContainerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/CronJobTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/DaemonSetTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/DeploymentTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/EventTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/ExpressionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/HorizontalPodAutoscalerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/IngressTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/JobTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/Kinds/IstioGateway.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/Kinds/IstioGatewayNoNamespacedVersion.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/Kinds/NewResource.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/Kinds/SealedSecret.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/KubeConfigTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/MacroTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/MutatingWebhookConfigurationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/NamespaceTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/NodeTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/PersistentVolumeClaimTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/PersistentVolumeTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/PodDisruptionBudgetTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/PodTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/ProbeTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/ResourceMetricTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/ResourceObjectTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/RoleBindingTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/RoleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/RuleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/SecretTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/ServiceAccountTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/ServiceTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/StatefulSetTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/StorageClassTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/SubjectTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/TestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/ValidatingWebhookConfigurationTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/VolumeTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/YamlTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/cluster/kubeconfig-2.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/cluster/kubeconfig-command.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/cluster/kubeconfig.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/cluster/token.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/cluster/token.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/temp/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/yaml/clusterrole.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/yaml/clusterrolebinding.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/yaml/configmap.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/yaml/configmap_and_secret.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/yaml/configmap_with_placeholder.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/yaml/cronjob.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/yaml/daemonset.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/yaml/deployment.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/yaml/hpa.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/yaml/ingress.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/yaml/job.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/yaml/mutatingwebhookconfiguration.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/yaml/namespace.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/yaml/pdb.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/yaml/persistentvolume.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/yaml/persistentvolumeclaim.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/yaml/pod.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/yaml/role.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/yaml/rolebinding.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/yaml/sealedsecret.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/yaml/secret.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/yaml/service.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/yaml/serviceaccount.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/yaml/statefulset.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/yaml/storageclass.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/renoki-co_php-k8s/tests/yaml/validatingwebhookconfiguration.yaml", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 2.****************, "profiling_times": {"config_time": 6.***************, "core_time": 3.***************, "ignores_time": 0.*****************, "total_time": 9.***************}, "parsing_time": {"total_time": 0.****************, "per_file_time": {"mean": 0.006367919491786587, "std_dev": 0.0001637853101499277}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.****************, "per_file_time": {"mean": 0.004893591685324719, "std_dev": 0.****************327}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.22331881523132324, "per_file_and_rule_time": {"mean": 0.0007835747902853447, "std_dev": 7.841871422345362e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.02759099006652832, "per_def_and_rule_time": {"mean": 0.0004926962511880057, "std_dev": 8.979821594702365e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}