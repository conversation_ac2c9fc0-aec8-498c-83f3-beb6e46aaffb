{"version": "1.130.0", "results": [{"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/typemill_typemill/Dockerfile", "start": {"line": 44, "col": 1, "offset": 1516}, "end": {"line": 44, "col": 47, "offset": 1562}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD [\"/var/www/html/docker-utils/init-server\"]", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-bcrypt-hash.detected-bcrypt-hash", "path": "downloaded_repos/typemill_typemill/cypress/fixtures/01_setup/prepulate_settings_seed/settings/users/trendschau.yaml", "start": {"line": 4, "col": 11, "offset": 83}, "end": {"line": 4, "col": 71, "offset": 143}, "extra": {"message": "bcrypt hash detected", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "category": "security", "technology": ["secrets", "bcrypt"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-bcrypt-hash.detected-bcrypt-hash", "shortlink": "https://sg.run/3A8G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/typemill_typemill/system/typemill/Controllers/ControllerApiGlobals.php", "start": {"line": 374, "col": 9, "offset": 11096}, "end": {"line": 374, "col": 37, "offset": 11124}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "path": "downloaded_repos/typemill_typemill/system/typemill/Models/License.php", "start": {"line": 293, "col": 7, "offset": 7602}, "end": {"line": 293, "col": 35, "offset": 7630}, "extra": {"message": "Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues", "metadata": {"cwe": ["CWE-697: Incorrect Comparison"], "references": ["https://www.php.net/manual/en/types.comparisons.php", "https://web.archive.org/web/20210430183236/https://www.whitehatsec.com/blog/magic-hashes/"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.md5-loose-equality.md5-loose-equality", "shortlink": "https://sg.run/Do4G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/typemill_typemill/system/typemill/Models/Media.php", "start": {"line": 66, "col": 11, "offset": 1395}, "end": {"line": 66, "col": 28, "offset": 1412}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/typemill_typemill/system/typemill/Models/Storage.php", "start": {"line": 229, "col": 10, "offset": 5888}, "end": {"line": 229, "col": 31, "offset": 5909}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/typemill_typemill/system/typemill/Models/Storage.php", "start": {"line": 391, "col": 7, "offset": 9649}, "end": {"line": 391, "col": 24, "offset": 9666}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/typemill_typemill/system/typemill/Models/Storage.php", "start": {"line": 806, "col": 50, "offset": 20745}, "end": {"line": 806, "col": 83, "offset": 20778}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/typemill_typemill/system/typemill/Models/Storage.php", "start": {"line": 811, "col": 52, "offset": 20934}, "end": {"line": 811, "col": 87, "offset": 20969}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/typemill_typemill/system/typemill/Models/Storage.php", "start": {"line": 822, "col": 8, "offset": 21351}, "end": {"line": 822, "col": 22, "offset": 21365}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/typemill_typemill/system/typemill/Models/Storage.php", "start": {"line": 831, "col": 8, "offset": 21677}, "end": {"line": 831, "col": 22, "offset": 21691}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/typemill_typemill/system/typemill/Models/Storage.php", "start": {"line": 933, "col": 48, "offset": 23951}, "end": {"line": 933, "col": 81, "offset": 23984}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/typemill_typemill/system/typemill/Models/Storage.php", "start": {"line": 951, "col": 5, "offset": 24277}, "end": {"line": 951, "col": 18, "offset": 24290}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/typemill_typemill/system/typemill/Models/User.php", "start": {"line": 374, "col": 21, "offset": 8830}, "end": {"line": 374, "col": 114, "offset": 8923}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/typemill_typemill/system/typemill/Models/User.php", "start": {"line": 415, "col": 21, "offset": 9715}, "end": {"line": 415, "col": 114, "offset": 9808}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/typemill_typemill/system/typemill/Models/User.php", "start": {"line": 444, "col": 4, "offset": 10377}, "end": {"line": 444, "col": 73, "offset": 10446}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/typemill_typemill/system/typemill/Models/User.php", "start": {"line": 449, "col": 4, "offset": 10541}, "end": {"line": 449, "col": 73, "offset": 10610}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.incomplete-sanitization.incomplete-sanitization", "path": "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-blox-components.js", "start": {"line": 1099, "col": 24, "offset": 29351}, "end": {"line": 1099, "col": 58, "offset": 29385}, "extra": {"message": "`definitionList.replace` method will only replace the first occurrence when used with a string argument (\"\\r\"). If this method is used for escaping of dangerous data then there is a possibility for a bypass. Try to use sanitization library instead or use a Regex with a global flag.", "metadata": {"cwe": ["CWE-116: Improper Encoding or Escaping of Output"], "category": "security", "technology": ["javascript"], "owasp": ["A03:2021 - Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Encoding"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.incomplete-sanitization.incomplete-sanitization", "shortlink": "https://sg.run/1GbQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/typemill_typemill/themes/cyanine/languages/admin/fr.yaml:14:\n (approximate error location; error nearby after) error calling parser: found character that cannot start any token character 0 position 0 returned: 0", "path": "downloaded_repos/typemill_typemill/themes/cyanine/languages/admin/fr.yaml"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/typemill_typemill/system/typemill/author/translations/fr.yaml:128:\n (approximate error location; error nearby after) error calling parser: found character that cannot start any token character 0 position 0 returned: 0", "path": "downloaded_repos/typemill_typemill/system/typemill/author/translations/fr.yaml"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/typemill_typemill/system/typemill/author/translations/de.yaml:171:\n (approximate error location; error nearby after) error calling parser: found character that cannot start any token character 0 position 0 returned: 0", "path": "downloaded_repos/typemill_typemill/system/typemill/author/translations/de.yaml"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/typemill_typemill/system/typemill/author/js/vue.global.prod.js:\n ", "path": "downloaded_repos/typemill_typemill/system/typemill/author/js/vue.global.prod.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/typemill_typemill/system/typemill/author/js/vue.global.prod.js:\n ", "path": "downloaded_repos/typemill_typemill/system/typemill/author/js/vue.global.prod.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "message": "Timeout when running javascript.express.security.injection.raw-html-format.raw-html-format on downloaded_repos/typemill_typemill/system/typemill/author/js/vue.global.prod.js:\n ", "path": "downloaded_repos/typemill_typemill/system/typemill/author/js/vue.global.prod.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/typemill_typemill/system/typemill/author/js/vue.js:\n ", "path": "downloaded_repos/typemill_typemill/system/typemill/author/js/vue.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/typemill_typemill/system/typemill/author/js/vue.js:\n ", "path": "downloaded_repos/typemill_typemill/system/typemill/author/js/vue.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/typemill_typemill/system/typemill/author/js/vue.js:\n ", "path": "downloaded_repos/typemill_typemill/system/typemill/author/js/vue.js"}], "paths": {"scanned": ["downloaded_repos/typemill_typemill/.dockerignore", "downloaded_repos/typemill_typemill/.github/workflows/docker-image.yml", "downloaded_repos/typemill_typemill/.gitignore", "downloaded_repos/typemill_typemill/.htaccess", "downloaded_repos/typemill_typemill/Dockerfile", "downloaded_repos/typemill_typemill/cache/cyanine-custom.css", "downloaded_repos/typemill_typemill/cache/timer.yaml", "downloaded_repos/typemill_typemill/composer.json", "downloaded_repos/typemill_typemill/composer.lock", "downloaded_repos/typemill_typemill/content/00-getting-started/00-create-your-first-page.md", "downloaded_repos/typemill_typemill/content/00-getting-started/00-create-your-first-page.yaml", "downloaded_repos/typemill_typemill/content/00-getting-started/01-edit-your-page.md", "downloaded_repos/typemill_typemill/content/00-getting-started/01-edit-your-page.yaml", "downloaded_repos/typemill_typemill/content/00-getting-started/02-edit-the-page-meta.md", "downloaded_repos/typemill_typemill/content/00-getting-started/02-edit-the-page-meta.yaml", "downloaded_repos/typemill_typemill/content/00-getting-started/03-publish-your-page.md", "downloaded_repos/typemill_typemill/content/00-getting-started/03-publish-your-page.yaml", "downloaded_repos/typemill_typemill/content/00-getting-started/04-new.yaml", "downloaded_repos/typemill_typemill/content/00-getting-started/index.md", "downloaded_repos/typemill_typemill/content/00-getting-started/index.yaml", "downloaded_repos/typemill_typemill/content/01-publish-status/00-unpublished.txt", "downloaded_repos/typemill_typemill/content/01-publish-status/00-unpublished.yaml", "downloaded_repos/typemill_typemill/content/01-publish-status/01-modified.md", "downloaded_repos/typemill_typemill/content/01-publish-status/01-modified.txt", "downloaded_repos/typemill_typemill/content/01-publish-status/01-modified.yaml", "downloaded_repos/typemill_typemill/content/01-publish-status/02-published.md", "downloaded_repos/typemill_typemill/content/01-publish-status/02-published.yaml", "downloaded_repos/typemill_typemill/content/01-publish-status/03-hidden.md", "downloaded_repos/typemill_typemill/content/01-publish-status/03-hidden.yaml", "downloaded_repos/typemill_typemill/content/01-publish-status/04-noindex.md", "downloaded_repos/typemill_typemill/content/01-publish-status/04-noindex.yaml", "downloaded_repos/typemill_typemill/content/01-publish-status/05-restricted.md", "downloaded_repos/typemill_typemill/content/01-publish-status/05-restricted.yaml", "downloaded_repos/typemill_typemill/content/01-publish-status/06-redirect-301.md", "downloaded_repos/typemill_typemill/content/01-publish-status/06-redirect-301.yaml", "downloaded_repos/typemill_typemill/content/01-publish-status/07-redirect-302.md", "downloaded_repos/typemill_typemill/content/01-publish-status/07-redirect-302.yaml", "downloaded_repos/typemill_typemill/content/01-publish-status/08-copy.md", "downloaded_repos/typemill_typemill/content/01-publish-status/08-copy.yaml", "downloaded_repos/typemill_typemill/content/01-publish-status/09-link.md", "downloaded_repos/typemill_typemill/content/01-publish-status/09-link.yaml", "downloaded_repos/typemill_typemill/content/01-publish-status/index.md", "downloaded_repos/typemill_typemill/content/01-publish-status/index.yaml", "downloaded_repos/typemill_typemill/content/02-news/202405171834-fast-websites.md", "downloaded_repos/typemill_typemill/content/02-news/202405171834-fast-websites.yaml", "downloaded_repos/typemill_typemill/content/02-news/202405171843-reports-and-handbooks.md", "downloaded_repos/typemill_typemill/content/02-news/202405171843-reports-and-handbooks.yaml", "downloaded_repos/typemill_typemill/content/02-news/202405171855-documentations-and-manuals.md", "downloaded_repos/typemill_typemill/content/02-news/202405171855-documentations-and-manuals.yaml", "downloaded_repos/typemill_typemill/content/02-news/index.md", "downloaded_repos/typemill_typemill/content/02-news/index.yaml", "downloaded_repos/typemill_typemill/content/index.md", "downloaded_repos/typemill_typemill/content/index.yaml", "downloaded_repos/typemill_typemill/cypress/fixtures/01_setup/default-settings.yaml", "downloaded_repos/typemill_typemill/cypress/fixtures/01_setup/prepulate_settings_seed/settings/settings.yaml", "downloaded_repos/typemill_typemill/cypress/fixtures/01_setup/prepulate_settings_seed/settings/users/trendschau.yaml", "downloaded_repos/typemill_typemill/cypress/integration/01_setup/01-system-setup-signup.spec.js", "downloaded_repos/typemill_typemill/cypress/integration/01_setup/02-initial-frontend.spec.js", "downloaded_repos/typemill_typemill/cypress/integration/03-system-settings.spec.js", "downloaded_repos/typemill_typemill/cypress/integration/04-theme-settings.spec.js", "downloaded_repos/typemill_typemill/cypress/integration/05_visual-editor/05-blox-editor.spec.js", "downloaded_repos/typemill_typemill/cypress/integration/99-login.spec.js", "downloaded_repos/typemill_typemill/cypress/plugins/index.js", "downloaded_repos/typemill_typemill/cypress/support/commands.js", "downloaded_repos/typemill_typemill/cypress/support/index.js", "downloaded_repos/typemill_typemill/cypress.json", "downloaded_repos/typemill_typemill/data/security/securitylog.txt", "downloaded_repos/typemill_typemill/docker-utils/init-server", "downloaded_repos/typemill_typemill/docker-utils/install-composer", "downloaded_repos/typemill_typemill/index.php", "downloaded_repos/typemill_typemill/licence.md", "downloaded_repos/typemill_typemill/media/custom/.gitkeep", "downloaded_repos/typemill_typemill/media/files/filerestrictions.yaml", "downloaded_repos/typemill_typemill/media/files/markdown.png", "downloaded_repos/typemill_typemill/media/live/chatgpt-typemill-dummy-wide.webp", "downloaded_repos/typemill_typemill/media/original/chatgpt-typemill-dummy-wide.webp", "downloaded_repos/typemill_typemill/media/thumbs/chatgpt-typemill-dummy-wide.webp", "downloaded_repos/typemill_typemill/media/tmp/.gitkeep", "downloaded_repos/typemill_typemill/package-lock.json", "downloaded_repos/typemill_typemill/package.json", "downloaded_repos/typemill_typemill/plugins/demo/DemoController.php", "downloaded_repos/typemill_typemill/plugins/demo/Text.php", "downloaded_repos/typemill_typemill/plugins/demo/css/demo.css", "downloaded_repos/typemill_typemill/plugins/demo/demo.php", "downloaded_repos/typemill_typemill/plugins/demo/demo.twig", "downloaded_repos/typemill_typemill/plugins/demo/demo.yaml", "downloaded_repos/typemill_typemill/plugins/demo/js/editordemo.js", "downloaded_repos/typemill_typemill/plugins/demo/js/systemdemo.js", "downloaded_repos/typemill_typemill/plugins/demo/templates/demo.twig", "downloaded_repos/typemill_typemill/readme.md", "downloaded_repos/typemill_typemill/settings/public_key.pem", "downloaded_repos/typemill_typemill/system/autoload.php", "downloaded_repos/typemill_typemill/system/typemill/Assets.php", "downloaded_repos/typemill_typemill/system/typemill/Controllers/Controller.php", "downloaded_repos/typemill_typemill/system/typemill/Controllers/ControllerApiAuthorArticle.php", "downloaded_repos/typemill_typemill/system/typemill/Controllers/ControllerApiAuthorBlock.php", "downloaded_repos/typemill_typemill/system/typemill/Controllers/ControllerApiAuthorMeta.php", "downloaded_repos/typemill_typemill/system/typemill/Controllers/ControllerApiAuthorShortcode.php", "downloaded_repos/typemill_typemill/system/typemill/Controllers/ControllerApiFile.php", "downloaded_repos/typemill_typemill/system/typemill/Controllers/ControllerApiGlobals.php", "downloaded_repos/typemill_typemill/system/typemill/Controllers/ControllerApiImage.php", "downloaded_repos/typemill_typemill/system/typemill/Controllers/ControllerApiKixote.php", "downloaded_repos/typemill_typemill/system/typemill/Controllers/ControllerApiSystemExtensions.php", "downloaded_repos/typemill_typemill/system/typemill/Controllers/ControllerApiSystemLicense.php", "downloaded_repos/typemill_typemill/system/typemill/Controllers/ControllerApiSystemPlugins.php", "downloaded_repos/typemill_typemill/system/typemill/Controllers/ControllerApiSystemSettings.php", "downloaded_repos/typemill_typemill/system/typemill/Controllers/ControllerApiSystemThemes.php", "downloaded_repos/typemill_typemill/system/typemill/Controllers/ControllerApiSystemUsers.php", "downloaded_repos/typemill_typemill/system/typemill/Controllers/ControllerApiSystemVersions.php", "downloaded_repos/typemill_typemill/system/typemill/Controllers/ControllerApiTestmail.php", "downloaded_repos/typemill_typemill/system/typemill/Controllers/ControllerWebAuth.php", "downloaded_repos/typemill_typemill/system/typemill/Controllers/ControllerWebAuthor.php", "downloaded_repos/typemill_typemill/system/typemill/Controllers/ControllerWebDownload.php", "downloaded_repos/typemill_typemill/system/typemill/Controllers/ControllerWebFrontend.php", "downloaded_repos/typemill_typemill/system/typemill/Controllers/ControllerWebRecover.php", "downloaded_repos/typemill_typemill/system/typemill/Controllers/ControllerWebSetup.php", "downloaded_repos/typemill_typemill/system/typemill/Controllers/ControllerWebSystem.php", "downloaded_repos/typemill_typemill/system/typemill/Events/BaseEvent.php", "downloaded_repos/typemill_typemill/system/typemill/Events/OnBreadcrumbLoaded.php", "downloaded_repos/typemill_typemill/system/typemill/Events/OnCacheUpdated.php", "downloaded_repos/typemill_typemill/system/typemill/Events/OnContentArrayLoaded.php", "downloaded_repos/typemill_typemill/system/typemill/Events/OnCspLoaded.php", "downloaded_repos/typemill_typemill/system/typemill/Events/OnHtmlLoaded.php", "downloaded_repos/typemill_typemill/system/typemill/Events/OnItemLoaded.php", "downloaded_repos/typemill_typemill/system/typemill/Events/OnMarkdownLoaded.php", "downloaded_repos/typemill_typemill/system/typemill/Events/OnMetaDefinitionsLoaded.php", "downloaded_repos/typemill_typemill/system/typemill/Events/OnMetaLoaded.php", "downloaded_repos/typemill_typemill/system/typemill/Events/OnOriginalLoaded.php", "downloaded_repos/typemill_typemill/system/typemill/Events/OnPageCreated.php", "downloaded_repos/typemill_typemill/system/typemill/Events/OnPageDeleted.php", "downloaded_repos/typemill_typemill/system/typemill/Events/OnPageDiscard.php", "downloaded_repos/typemill_typemill/system/typemill/Events/OnPagePublished.php", "downloaded_repos/typemill_typemill/system/typemill/Events/OnPageReady.php", "downloaded_repos/typemill_typemill/system/typemill/Events/OnPageRenamed.php", "downloaded_repos/typemill_typemill/system/typemill/Events/OnPageSorted.php", "downloaded_repos/typemill_typemill/system/typemill/Events/OnPageUnpublished.php", "downloaded_repos/typemill_typemill/system/typemill/Events/OnPageUpdated.php", "downloaded_repos/typemill_typemill/system/typemill/Events/OnPagetreeLoaded.php", "downloaded_repos/typemill_typemill/system/typemill/Events/OnPluginsLoaded.php", "downloaded_repos/typemill_typemill/system/typemill/Events/OnResourcesLoaded.php", "downloaded_repos/typemill_typemill/system/typemill/Events/OnRestrictionsLoaded.php", "downloaded_repos/typemill_typemill/system/typemill/Events/OnRolesPermissionsLoaded.php", "downloaded_repos/typemill_typemill/system/typemill/Events/OnSessionSegmentsLoaded.php", "downloaded_repos/typemill_typemill/system/typemill/Events/OnSettingsLoaded.php", "downloaded_repos/typemill_typemill/system/typemill/Events/OnShortcodeFound.php", "downloaded_repos/typemill_typemill/system/typemill/Events/OnSystemnaviLoaded.php", "downloaded_repos/typemill_typemill/system/typemill/Events/OnTwigGlobalsLoaded.php", "downloaded_repos/typemill_typemill/system/typemill/Events/OnTwigLoaded.php", "downloaded_repos/typemill_typemill/system/typemill/Events/OnUserConfirmed.php", "downloaded_repos/typemill_typemill/system/typemill/Events/OnUserDeleted.php", "downloaded_repos/typemill_typemill/system/typemill/Events/OnUserfieldsLoaded.php", "downloaded_repos/typemill_typemill/system/typemill/Extensions/MediaExtension.php", "downloaded_repos/typemill_typemill/system/typemill/Extensions/ParsedownExtension.php", "downloaded_repos/typemill_typemill/system/typemill/Extensions/TwigCaptchaExtension.php", "downloaded_repos/typemill_typemill/system/typemill/Extensions/TwigCsrfExtension.php", "downloaded_repos/typemill_typemill/system/typemill/Extensions/TwigLanguageExtension.php", "downloaded_repos/typemill_typemill/system/typemill/Extensions/TwigMarkdownExtension.php", "downloaded_repos/typemill_typemill/system/typemill/Extensions/TwigMetaExtension.php", "downloaded_repos/typemill_typemill/system/typemill/Extensions/TwigPagelistExtension.php", "downloaded_repos/typemill_typemill/system/typemill/Extensions/TwigUrlExtension.php", "downloaded_repos/typemill_typemill/system/typemill/Extensions/TwigUserExtension.php", "downloaded_repos/typemill_typemill/system/typemill/Middleware/ApiAuthentication.php", "downloaded_repos/typemill_typemill/system/typemill/Middleware/ApiAuthorization.php", "downloaded_repos/typemill_typemill/system/typemill/Middleware/AssetMiddleware.php", "downloaded_repos/typemill_typemill/system/typemill/Middleware/CorsHeadersMiddleware.php", "downloaded_repos/typemill_typemill/system/typemill/Middleware/CspHeadersMiddleware.php", "downloaded_repos/typemill_typemill/system/typemill/Middleware/CustomHeadersMiddleware.php", "downloaded_repos/typemill_typemill/system/typemill/Middleware/FlashMessages.php", "downloaded_repos/typemill_typemill/system/typemill/Middleware/JsonBodyParser.php", "downloaded_repos/typemill_typemill/system/typemill/Middleware/OldInputMiddleware.php", "downloaded_repos/typemill_typemill/system/typemill/Middleware/RemoveCredentialsMiddleware.php", "downloaded_repos/typemill_typemill/system/typemill/Middleware/SecurityMiddleware.php", "downloaded_repos/typemill_typemill/system/typemill/Middleware/SessionMiddleware.php", "downloaded_repos/typemill_typemill/system/typemill/Middleware/ValidationErrorsMiddleware.php", "downloaded_repos/typemill_typemill/system/typemill/Middleware/WebAuthorization.php", "downloaded_repos/typemill_typemill/system/typemill/Middleware/WebRedirectIfAuthenticated.php", "downloaded_repos/typemill_typemill/system/typemill/Middleware/WebRedirectIfUnauthenticated.php", "downloaded_repos/typemill_typemill/system/typemill/Models/ApiCalls.php", "downloaded_repos/typemill_typemill/system/typemill/Models/Content.php", "downloaded_repos/typemill_typemill/system/typemill/Models/Extension.php", "downloaded_repos/typemill_typemill/system/typemill/Models/Field.php", "downloaded_repos/typemill_typemill/system/typemill/Models/Fields.php", "downloaded_repos/typemill_typemill/system/typemill/Models/Folder.php", "downloaded_repos/typemill_typemill/system/typemill/Models/License.php", "downloaded_repos/typemill_typemill/system/typemill/Models/Media.php", "downloaded_repos/typemill_typemill/system/typemill/Models/Meta.php", "downloaded_repos/typemill_typemill/system/typemill/Models/Navigation.php", "downloaded_repos/typemill_typemill/system/typemill/Models/Settings.php", "downloaded_repos/typemill_typemill/system/typemill/Models/SimpleMail.php", "downloaded_repos/typemill_typemill/system/typemill/Models/Sitemap.php", "downloaded_repos/typemill_typemill/system/typemill/Models/Storage.php", "downloaded_repos/typemill_typemill/system/typemill/Models/StorageWrapper.php", "downloaded_repos/typemill_typemill/system/typemill/Models/SvgSanitizer.php", "downloaded_repos/typemill_typemill/system/typemill/Models/User.php", "downloaded_repos/typemill_typemill/system/typemill/Models/Validation.php", "downloaded_repos/typemill_typemill/system/typemill/Plugin.php", "downloaded_repos/typemill_typemill/system/typemill/Static/Helpers.php", "downloaded_repos/typemill_typemill/system/typemill/Static/Permissions.php", "downloaded_repos/typemill_typemill/system/typemill/Static/Plugins.php", "downloaded_repos/typemill_typemill/system/typemill/Static/Session.php", "downloaded_repos/typemill_typemill/system/typemill/Static/Slug.php", "downloaded_repos/typemill_typemill/system/typemill/Static/Translations.php", "downloaded_repos/typemill_typemill/system/typemill/Static/Urlinfo.php", "downloaded_repos/typemill_typemill/system/typemill/author/404.twig", "downloaded_repos/typemill_typemill/system/typemill/author/auth/authcode.twig", "downloaded_repos/typemill_typemill/system/typemill/author/auth/login.twig", "downloaded_repos/typemill_typemill/system/typemill/author/auth/recover.twig", "downloaded_repos/typemill_typemill/system/typemill/author/auth/recoverconf.twig", "downloaded_repos/typemill_typemill/system/typemill/author/auth/reset.twig", "downloaded_repos/typemill_typemill/system/typemill/author/auth/setup.twig", "downloaded_repos/typemill_typemill/system/typemill/author/content/blox-editor.twig", "downloaded_repos/typemill_typemill/system/typemill/author/content/raw-editor.twig", "downloaded_repos/typemill_typemill/system/typemill/author/css/a11y-dark.min.css", "downloaded_repos/typemill_typemill/system/typemill/author/css/custom.css", "downloaded_repos/typemill_typemill/system/typemill/author/css/input.css", "downloaded_repos/typemill_typemill/system/typemill/author/css/output.css", "downloaded_repos/typemill_typemill/system/typemill/author/js/typemillutils.js", "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-blox-components.js", "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-blox-config.js", "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-blox.js", "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-contentnavi.js", "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-eventbus.js", "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-forms.js", "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-kixote.js", "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-license.js", "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-medialib.js", "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-meta.js", "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-plugins.js", "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-posts.js", "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-publisher.js", "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-raw.js", "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-shared.js", "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-system.js", "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-systemnavi.js", "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-themes.js", "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-user.js", "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-usernew.js", "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-users.js", "downloaded_repos/typemill_typemill/system/typemill/author/js/vue.global.prod.js", "downloaded_repos/typemill_typemill/system/typemill/author/js/vue.js", "downloaded_repos/typemill_typemill/system/typemill/author/layouts/layoutAuth.twig", "downloaded_repos/typemill_typemill/system/typemill/author/layouts/layoutContent.twig", "downloaded_repos/typemill_typemill/system/typemill/author/layouts/layoutSystem.twig", "downloaded_repos/typemill_typemill/system/typemill/author/layouts/layoutSystemBlank.twig", "downloaded_repos/typemill_typemill/system/typemill/author/partials/contentNavi.twig", "downloaded_repos/typemill_typemill/system/typemill/author/partials/fields.twig", "downloaded_repos/typemill_typemill/system/typemill/author/partials/flash.twig", "downloaded_repos/typemill_typemill/system/typemill/author/partials/form.twig", "downloaded_repos/typemill_typemill/system/typemill/author/partials/mainNavi.twig", "downloaded_repos/typemill_typemill/system/typemill/author/partials/symbols.twig", "downloaded_repos/typemill_typemill/system/typemill/author/partials/systemNavi.twig", "downloaded_repos/typemill_typemill/system/typemill/author/system/account.twig", "downloaded_repos/typemill_typemill/system/typemill/author/system/license.twig", "downloaded_repos/typemill_typemill/system/typemill/author/system/plugins.twig", "downloaded_repos/typemill_typemill/system/typemill/author/system/system.twig", "downloaded_repos/typemill_typemill/system/typemill/author/system/themes.twig", "downloaded_repos/typemill_typemill/system/typemill/author/system/user.twig", "downloaded_repos/typemill_typemill/system/typemill/author/system/usernew.twig", "downloaded_repos/typemill_typemill/system/typemill/author/system/users.twig", "downloaded_repos/typemill_typemill/system/typemill/author/translations/de.yaml", "downloaded_repos/typemill_typemill/system/typemill/author/translations/en.yaml", "downloaded_repos/typemill_typemill/system/typemill/author/translations/fr.yaml", "downloaded_repos/typemill_typemill/system/typemill/author/translations/it.yaml", "downloaded_repos/typemill_typemill/system/typemill/author/translations/nl.yaml", "downloaded_repos/typemill_typemill/system/typemill/author/translations/ru.yaml", "downloaded_repos/typemill_typemill/system/typemill/routes/api.php", "downloaded_repos/typemill_typemill/system/typemill/routes/setup.php", "downloaded_repos/typemill_typemill/system/typemill/routes/web.php", "downloaded_repos/typemill_typemill/system/typemill/settings/defaults.yaml", "downloaded_repos/typemill_typemill/system/typemill/settings/kixote.yaml", "downloaded_repos/typemill_typemill/system/typemill/settings/license.yaml", "downloaded_repos/typemill_typemill/system/typemill/settings/mainnavi.yaml", "downloaded_repos/typemill_typemill/system/typemill/settings/metatabs.yaml", "downloaded_repos/typemill_typemill/system/typemill/settings/permissions.yaml", "downloaded_repos/typemill_typemill/system/typemill/settings/resources.yaml", "downloaded_repos/typemill_typemill/system/typemill/settings/system.yaml", "downloaded_repos/typemill_typemill/system/typemill/settings/systemnavi.yaml", "downloaded_repos/typemill_typemill/system/typemill/settings/user.yaml", "downloaded_repos/typemill_typemill/system/typemill/system.php", "downloaded_repos/typemill_typemill/tailwind.config.js", "downloaded_repos/typemill_typemill/themes/cyanine/404.twig", "downloaded_repos/typemill_typemill/themes/cyanine/blog.twig", "downloaded_repos/typemill_typemill/themes/cyanine/css/style.css", "downloaded_repos/typemill_typemill/themes/cyanine/css/tachyons.min.css", "downloaded_repos/typemill_typemill/themes/cyanine/cyanine-thumb.png", "downloaded_repos/typemill_typemill/themes/cyanine/cyanine.png", "downloaded_repos/typemill_typemill/themes/cyanine/cyanine.yaml", "downloaded_repos/typemill_typemill/themes/cyanine/home/<USER>", "downloaded_repos/typemill_typemill/themes/cyanine/home/<USER>", "downloaded_repos/typemill_typemill/themes/cyanine/home/<USER>", "downloaded_repos/typemill_typemill/themes/cyanine/home/<USER>", "downloaded_repos/typemill_typemill/themes/cyanine/home/<USER>", "downloaded_repos/typemill_typemill/themes/cyanine/home/<USER>", "downloaded_repos/typemill_typemill/themes/cyanine/home.twig", "downloaded_repos/typemill_typemill/themes/cyanine/index.twig", "downloaded_repos/typemill_typemill/themes/cyanine/js/script.js", "downloaded_repos/typemill_typemill/themes/cyanine/landingpage.twig", "downloaded_repos/typemill_typemill/themes/cyanine/languages/admin/en.yaml", "downloaded_repos/typemill_typemill/themes/cyanine/languages/admin/fr.yaml", "downloaded_repos/typemill_typemill/themes/cyanine/languages/admin/it.yaml", "downloaded_repos/typemill_typemill/themes/cyanine/languages/user/en.yaml", "downloaded_repos/typemill_typemill/themes/cyanine/languages/user/it.yaml", "downloaded_repos/typemill_typemill/themes/cyanine/layout.twig", "downloaded_repos/typemill_typemill/themes/cyanine/page.twig", "downloaded_repos/typemill_typemill/themes/cyanine/partials/breadcrumb.twig", "downloaded_repos/typemill_typemill/themes/cyanine/partials/footer.twig", "downloaded_repos/typemill_typemill/themes/cyanine/partials/navigation.twig", "downloaded_repos/typemill_typemill/themes/cyanine/partials/navigationFlat.twig", "downloaded_repos/typemill_typemill/themes/cyanine/partials/navigationGlossary.twig", "downloaded_repos/typemill_typemill/themes/cyanine/partials/posts.twig", "downloaded_repos/typemill_typemill/typemill-editor.webp"], "skipped": [{"path": "downloaded_repos/typemill_typemill/system/typemill/author/js/autosize.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/typemill_typemill/system/typemill/author/js/axios.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/typemill_typemill/system/typemill/author/js/highlight.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/typemill_typemill/system/typemill/author/js/sortable.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/typemill_typemill/system/typemill/author/js/vue.global.prod.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/typemill_typemill/system/typemill/author/js/vue.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/typemill_typemill/system/typemill/author/js/vuedraggable.umd.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/typemill_typemill/system/typemill/author/translations/de.yaml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/typemill_typemill/system/typemill/author/translations/fr.yaml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/typemill_typemill/themes/cyanine/languages/admin/fr.yaml", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.8260002136230469, "profiling_times": {"config_time": 6.120802402496338, "core_time": 11.532411336898804, "ignores_time": 0.004445075988769531, "total_time": 17.659000158309937}, "parsing_time": {"total_time": 9.421181678771973, "per_file_time": {"mean": 0.0452941426864037, "std_dev": 0.008019434571516772}, "very_slow_stats": {"time_ratio": 0.19746617133110467, "count_ratio": 0.019230769230769232}, "very_slow_files": [{"fpath": "downloaded_repos/typemill_typemill/system/typemill/Controllers/ControllerApiAuthorMeta.php", "ftime": 0.30977797508239746}, {"fpath": "downloaded_repos/typemill_typemill/system/typemill/Extensions/ParsedownExtension.php", "ftime": 0.3105168342590332}, {"fpath": "downloaded_repos/typemill_typemill/system/typemill/Controllers/ControllerApiAuthorArticle.php", "ftime": 0.35620784759521484}, {"fpath": "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-blox-components.js", "ftime": 0.8838620185852051}]}, "scanning_time": {"total_time": 54.91718244552612, "per_file_time": {"mean": 0.06576908077308515, "std_dev": 0.16730382181068504}, "very_slow_stats": {"time_ratio": 0.44717137745951346, "count_ratio": 0.00718562874251497}, "very_slow_files": [{"fpath": "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-medialib.js", "ftime": 1.6242320537567139}, {"fpath": "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-forms.js", "ftime": 1.6485960483551025}, {"fpath": "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-kixote.js", "ftime": 2.983981132507324}, {"fpath": "downloaded_repos/typemill_typemill/system/typemill/author/js/vue.global.prod.js", "ftime": 5.154989957809448}, {"fpath": "downloaded_repos/typemill_typemill/system/typemill/author/js/vue.js", "ftime": 5.566759824752808}, {"fpath": "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-blox-components.js", "ftime": 7.578833103179932}]}, "matching_time": {"total_time": 15.46672534942627, "per_file_and_rule_time": {"mean": 0.008170483544335059, "std_dev": 0.0011390163604578917}, "very_slow_stats": {"time_ratio": 0.4012991677106175, "count_ratio": 0.01531959852086635}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-contentnavi.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.20668888092041016}, {"fpath": "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-kixote.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.21191787719726562}, {"fpath": "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-blox-components.js", "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.21696114540100098}, {"fpath": "downloaded_repos/typemill_typemill/cypress/integration/05_visual-editor/05-blox-editor.spec.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.2666809558868408}, {"fpath": "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-blox-components.js", "rule_id": "problem-based-packs.insecure-transport.js-node.using-http-server.using-http-server", "time": 0.2758920192718506}, {"fpath": "downloaded_repos/typemill_typemill/system/typemill/Models/Storage.php", "rule_id": "php.lang.security.injection.tainted-callable.tainted-callable", "time": 0.34065699577331543}, {"fpath": "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-kixote.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.3892989158630371}, {"fpath": "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-blox-components.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.4168128967285156}, {"fpath": "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-blox-components.js", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 0.45004916191101074}, {"fpath": "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-blox-components.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.7852590084075928}]}, "tainting_time": {"total_time": 8.038879632949829, "per_def_and_rule_time": {"mean": 0.00250744841951024, "std_dev": 0.00017738389145510833}, "very_slow_stats": {"time_ratio": 0.4184381970136877, "count_ratio": 0.009357454772301934}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-blox-components.js", "fline": 1, "rule_id": "javascript.express.security.require-request.require-request", "time": 0.125993013381958}, {"fpath": "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-blox-components.js", "fline": 1, "rule_id": "javascript.express.security.x-frame-options-misconfiguration.x-frame-options-misconfiguration", "time": 0.1300959587097168}, {"fpath": "downloaded_repos/typemill_typemill/cypress/integration/05_visual-editor/05-blox-editor.spec.js", "fline": 1, "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.13494110107421875}, {"fpath": "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-blox-components.js", "fline": 1, "rule_id": "javascript.express.security.express-wkhtml-injection.express-wkhtmltoimage-injection", "time": 0.13523507118225098}, {"fpath": "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-blox-components.js", "fline": 1, "rule_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "time": 0.14964699745178223}, {"fpath": "downloaded_repos/typemill_typemill/cypress/integration/05_visual-editor/05-blox-editor.spec.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.16969704627990723}, {"fpath": "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-blox-components.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.19919705390930176}, {"fpath": "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-blox-components.js", "fline": 1, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.20558404922485352}, {"fpath": "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-blox-components.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.3001530170440674}, {"fpath": "downloaded_repos/typemill_typemill/system/typemill/author/js/vue-blox-components.js", "fline": 1, "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 0.33873510360717773}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}