{"version": "1.130.0", "results": [{"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/cisagov_untitledgoosetool/Dockerfile", "start": {"line": 17, "col": 1, "offset": 220}, "end": {"line": 17, "col": 15, "offset": 234}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD [\"goosey\"]", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.requests.security.disabled-cert-validation.disabled-cert-validation", "path": "downloaded_repos/cisagov_untitledgoosetool/goosey/auth.py", "start": {"line": 168, "col": 24, "offset": 7182}, "end": {"line": 168, "col": 83, "offset": 7241}, "extra": {"message": "Certificate verification has been explicitly disabled. This permits insecure connections to insecure servers. Re-enable certification validation.", "fix": "requests.request(\"GET\", url, headers=headers, verify=True)", "metadata": {"cwe": ["CWE-295: Improper Certificate Validation"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A07:2021 - Identification and Authentication Failures"], "references": ["https://stackoverflow.com/questions/41740361/is-it-safe-to-disable-ssl-certificate-verification-in-pythonss-requests-lib"], "category": "security", "technology": ["requests"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/python.requests.security.disabled-cert-validation.disabled-cert-validation", "shortlink": "https://sg.run/AlYp"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.requests.security.disabled-cert-validation.disabled-cert-validation", "path": "downloaded_repos/cisagov_untitledgoosetool/goosey/auth.py", "start": {"line": 186, "col": 25, "offset": 7738}, "end": {"line": 186, "col": 90, "offset": 7803}, "extra": {"message": "Certificate verification has been explicitly disabled. This permits insecure connections to insecure servers. Re-enable certification validation.", "fix": "requests.post(url2, headers=headers2, json=payload, verify=True)", "metadata": {"cwe": ["CWE-295: Improper Certificate Validation"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A07:2021 - Identification and Authentication Failures"], "references": ["https://stackoverflow.com/questions/41740361/is-it-safe-to-disable-ssl-certificate-verification-in-pythonss-requests-lib"], "category": "security", "technology": ["requests"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/python.requests.security.disabled-cert-validation.disabled-cert-validation", "shortlink": "https://sg.run/AlYp"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.dangerous-globals-use.dangerous-globals-use", "path": "downloaded_repos/cisagov_untitledgoosetool/goosey/conf.py", "start": {"line": 176, "col": 36, "offset": 9318}, "end": {"line": 176, "col": 54, "offset": 9336}, "extra": {"message": "Found non static data as an index to 'globals()'. This is extremely dangerous because it allows an attacker to execute arbitrary code on the system. Refactor your code not to use 'globals()'.", "metadata": {"cwe": ["CWE-96: Improper Neutralization of Directives in Statically Saved Code ('Static Code Injection')"], "owasp": ["A03:2021 - Injection"], "references": ["https://github.com/mpirnat/lets-be-bad-guys/blob/d92768fb3ade32956abd53bd6bb06e19d634a084/badguys/vulnerable/views.py#L181-L186"], "category": "security", "technology": ["python"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.dangerous-globals-use.dangerous-globals-use", "shortlink": "https://sg.run/jNzn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.dangerous-globals-use.dangerous-globals-use", "path": "downloaded_repos/cisagov_untitledgoosetool/goosey/conf.py", "start": {"line": 208, "col": 36, "offset": 10752}, "end": {"line": 208, "col": 54, "offset": 10770}, "extra": {"message": "Found non static data as an index to 'globals()'. This is extremely dangerous because it allows an attacker to execute arbitrary code on the system. Refactor your code not to use 'globals()'.", "metadata": {"cwe": ["CWE-96: Improper Neutralization of Directives in Statically Saved Code ('Static Code Injection')"], "owasp": ["A03:2021 - Injection"], "references": ["https://github.com/mpirnat/lets-be-bad-guys/blob/d92768fb3ade32956abd53bd6bb06e19d634a084/badguys/vulnerable/views.py#L181-L186"], "category": "security", "technology": ["python"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.dangerous-globals-use.dangerous-globals-use", "shortlink": "https://sg.run/jNzn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/cisagov_untitledgoosetool/.gitignore", "downloaded_repos/cisagov_untitledgoosetool/CHANGELOG.md", "downloaded_repos/cisagov_untitledgoosetool/CONTRIBUTING.md", "downloaded_repos/cisagov_untitledgoosetool/Dockerfile", "downloaded_repos/cisagov_untitledgoosetool/LICENSE", "downloaded_repos/cisagov_untitledgoosetool/README.md", "downloaded_repos/cisagov_untitledgoosetool/assets/goosey_gui.png", "downloaded_repos/cisagov_untitledgoosetool/cyclonedx_output.json", "downloaded_repos/cisagov_untitledgoosetool/gooey_LICENSE.txt", "downloaded_repos/cisagov_untitledgoosetool/goosey/__init__.py", "downloaded_repos/cisagov_untitledgoosetool/goosey/auth.py", "downloaded_repos/cisagov_untitledgoosetool/goosey/azure_dumper.py", "downloaded_repos/cisagov_untitledgoosetool/goosey/conf.py", "downloaded_repos/cisagov_untitledgoosetool/goosey/csv.py", "downloaded_repos/cisagov_untitledgoosetool/goosey/d4iot.py", "downloaded_repos/cisagov_untitledgoosetool/goosey/d4iot_dumper.py", "downloaded_repos/cisagov_untitledgoosetool/goosey/datadumper.py", "downloaded_repos/cisagov_untitledgoosetool/goosey/entra_id_datadumper.py", "downloaded_repos/cisagov_untitledgoosetool/goosey/honk.py", "downloaded_repos/cisagov_untitledgoosetool/goosey/m365_datadumper.py", "downloaded_repos/cisagov_untitledgoosetool/goosey/main.py", "downloaded_repos/cisagov_untitledgoosetool/goosey/mde_datadumper.py", "downloaded_repos/cisagov_untitledgoosetool/goosey/utils.py", "downloaded_repos/cisagov_untitledgoosetool/requirements.txt", "downloaded_repos/cisagov_untitledgoosetool/scripts/Create_SP.ps1", "downloaded_repos/cisagov_untitledgoosetool/setup.py", "downloaded_repos/cisagov_untitledgoosetool/spdx_output.json"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.8538529872894287, "profiling_times": {"config_time": 6.390734910964966, "core_time": 5.723454713821411, "ignores_time": 0.0024585723876953125, "total_time": 12.11799693107605}, "parsing_time": {"total_time": 1.2508320808410645, "per_file_time": {"mean": 0.06949067115783691, "std_dev": 0.004873569352986982}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 13.072509765625, "per_file_time": {"mean": 0.18156263563368052, "std_dev": 0.26162847364731334}, "very_slow_stats": {"time_ratio": 0.4295807155488374, "count_ratio": 0.027777777777777776}, "very_slow_files": [{"fpath": "downloaded_repos/cisagov_untitledgoosetool/goosey/m365_datadumper.py", "ftime": 2.519667148590088}, {"fpath": "downloaded_repos/cisagov_untitledgoosetool/goosey/azure_dumper.py", "ftime": 3.0960309505462646}]}, "matching_time": {"total_time": 6.119233846664429, "per_file_and_rule_time": {"mean": 0.021776632906279104, "std_dev": 0.0014708843457958431}, "very_slow_stats": {"time_ratio": 0.37486275091486077, "count_ratio": 0.05338078291814947}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/cisagov_untitledgoosetool/goosey/utils.py", "rule_id": "python.django.security.injection.path-traversal.path-traversal-open.path-traversal-open", "time": 0.12520194053649902}, {"fpath": "downloaded_repos/cisagov_untitledgoosetool/goosey/m365_datadumper.py", "rule_id": "python.django.security.injection.path-traversal.path-traversal-open.path-traversal-open", "time": 0.12927699089050293}, {"fpath": "downloaded_repos/cisagov_untitledgoosetool/goosey/mde_datadumper.py", "rule_id": "python.django.security.injection.path-traversal.path-traversal-open.path-traversal-open", "time": 0.13192391395568848}, {"fpath": "downloaded_repos/cisagov_untitledgoosetool/goosey/m365_datadumper.py", "rule_id": "python.lang.security.dangerous-subprocess-use.dangerous-subprocess-use", "time": 0.13342905044555664}, {"fpath": "downloaded_repos/cisagov_untitledgoosetool/goosey/azure_dumper.py", "rule_id": "python.flask.security.injection.raw-html-concat.raw-html-format", "time": 0.1679689884185791}, {"fpath": "downloaded_repos/cisagov_untitledgoosetool/goosey/azure_dumper.py", "rule_id": "python.lang.security.dangerous-system-call.dangerous-system-call", "time": 0.177170991897583}, {"fpath": "downloaded_repos/cisagov_untitledgoosetool/goosey/azure_dumper.py", "rule_id": "python.django.security.injection.command.command-injection-os-system.command-injection-os-system", "time": 0.17794299125671387}, {"fpath": "downloaded_repos/cisagov_untitledgoosetool/goosey/azure_dumper.py", "rule_id": "python.django.security.injection.path-traversal.path-traversal-open.path-traversal-open", "time": 0.18752098083496094}, {"fpath": "downloaded_repos/cisagov_untitledgoosetool/goosey/auth.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.24476885795593262}, {"fpath": "downloaded_repos/cisagov_untitledgoosetool/goosey/m365_datadumper.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.26032185554504395}]}, "tainting_time": {"total_time": 3.601116180419922, "per_def_and_rule_time": {"mean": 0.0036783617777527288, "std_dev": 0.00039966044850270154}, "very_slow_stats": {"time_ratio": 0.5731761202994456, "count_ratio": 0.012257405515832482}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/cisagov_untitledgoosetool/goosey/azure_dumper.py", "fline": 414, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.14481306076049805}, {"fpath": "downloaded_repos/cisagov_untitledgoosetool/goosey/azure_dumper.py", "fline": 287, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.1612839698791504}, {"fpath": "downloaded_repos/cisagov_untitledgoosetool/goosey/utils.py", "fline": 270, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.16617202758789062}, {"fpath": "downloaded_repos/cisagov_untitledgoosetool/goosey/azure_dumper.py", "fline": 996, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.20594000816345215}, {"fpath": "downloaded_repos/cisagov_untitledgoosetool/goosey/m365_datadumper.py", "fline": 294, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.20687198638916016}, {"fpath": "downloaded_repos/cisagov_untitledgoosetool/goosey/entra_id_datadumper.py", "fline": 65, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.20926284790039062}, {"fpath": "downloaded_repos/cisagov_untitledgoosetool/goosey/m365_datadumper.py", "fline": 520, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.20964503288269043}, {"fpath": "downloaded_repos/cisagov_untitledgoosetool/goosey/entra_id_datadumper.py", "fline": 346, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.20975899696350098}, {"fpath": "downloaded_repos/cisagov_untitledgoosetool/goosey/azure_dumper.py", "fline": 134, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.21292996406555176}, {"fpath": "downloaded_repos/cisagov_untitledgoosetool/goosey/entra_id_datadumper.py", "fline": 175, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.219865083694458}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}