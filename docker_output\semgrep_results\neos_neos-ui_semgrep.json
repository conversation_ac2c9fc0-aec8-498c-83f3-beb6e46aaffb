{"version": "1.130.0", "results": [{"check_id": "typescript.react.security.react-insecure-request.react-insecure-request", "path": "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/Fixtures/1Dimension/nodeTreePresets.e2e.js", "start": {"line": 11, "col": 32, "offset": 315}, "end": {"line": 17, "col": 11, "offset": 584}, "extra": {"message": "Unencrypted request over HTTP detected.", "metadata": {"vulnerability": "Insecure Transport", "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.npmjs.com/package/axios"], "category": "security", "technology": ["react"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/typescript.react.security.react-insecure-request.react-insecure-request", "shortlink": "https://sg.run/1n0b"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "typescript.react.security.react-insecure-request.react-insecure-request", "path": "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/Fixtures/1Dimension/nodeTreePresets.e2e.js", "start": {"line": 24, "col": 32, "offset": 830}, "end": {"line": 26, "col": 11, "offset": 932}, "extra": {"message": "Unencrypted request over HTTP detected.", "metadata": {"vulnerability": "Insecure Transport", "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.npmjs.com/package/axios"], "category": "security", "technology": ["react"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/typescript.react.security.react-insecure-request.react-insecure-request", "shortlink": "https://sg.run/1n0b"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/Classes/Application/RemoveAdditionalSettings/RemoveAdditionalSettingsCommandHandler.php", "start": {"line": 36, "col": 13, "offset": 950}, "end": {"line": 36, "col": 48, "offset": 985}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/docker-compose.neos-dev-instance.yaml", "start": {"line": 4, "col": 3, "offset": 28}, "end": {"line": 4, "col": 6, "offset": 31}, "extra": {"message": "Service 'php' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/docker-compose.neos-dev-instance.yaml", "start": {"line": 4, "col": 3, "offset": 28}, "end": {"line": 4, "col": 6, "offset": 31}, "extra": {"message": "Service 'php' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/docker-compose.neos-dev-instance.yaml", "start": {"line": 19, "col": 3, "offset": 505}, "end": {"line": 19, "col": 5, "offset": 507}, "extra": {"message": "Service 'db' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/docker-compose.neos-dev-instance.yaml", "start": {"line": 19, "col": 3, "offset": 505}, "end": {"line": 19, "col": 5, "offset": 507}, "extra": {"message": "Service 'db' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/docker-compose.yaml", "start": {"line": 4, "col": 3, "offset": 28}, "end": {"line": 4, "col": 6, "offset": 31}, "extra": {"message": "Service 'php' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/docker-compose.yaml", "start": {"line": 4, "col": 3, "offset": 28}, "end": {"line": 4, "col": 6, "offset": 31}, "extra": {"message": "Service 'php' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/docker-compose.yaml", "start": {"line": 20, "col": 3, "offset": 530}, "end": {"line": 20, "col": 5, "offset": 532}, "extra": {"message": "Service 'db' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/docker-compose.yaml", "start": {"line": 20, "col": 3, "offset": 530}, "end": {"line": 20, "col": 5, "offset": 532}, "extra": {"message": "Service 'db' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/neos_neos-ui/cssModules.js", "start": {"line": 60, "col": 39, "offset": 2363}, "end": {"line": 60, "col": 84, "offset": 2408}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/neos_neos-ui/cssModules.js", "start": {"line": 60, "col": 86, "offset": 2410}, "end": {"line": 60, "col": 107, "offset": 2431}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/neos_neos-ui/packages/debug-reason-for-rendering/src/index.js", "start": {"line": 89, "col": 23, "offset": 3598}, "end": {"line": 89, "col": 138, "offset": 3713}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/neos_neos-ui/packages/debug-reason-for-rendering/src/index.js", "start": {"line": 114, "col": 22, "offset": 4745}, "end": {"line": 114, "col": 187, "offset": 4910}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/Endpoints/index.ts", "start": {"line": 297, "col": 13, "offset": 12010}, "end": {"line": 297, "col": 48, "offset": 12045}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/Endpoints/index.ts", "start": {"line": 319, "col": 13, "offset": 13112}, "end": {"line": 319, "col": 48, "offset": 13147}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/Endpoints/index.ts", "start": {"line": 350, "col": 13, "offset": 14947}, "end": {"line": 350, "col": 48, "offset": 14982}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/Endpoints/index.ts", "start": {"line": 382, "col": 13, "offset": 16479}, "end": {"line": 382, "col": 34, "offset": 16500}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/Endpoints/index.ts", "start": {"line": 423, "col": 13, "offset": 18314}, "end": {"line": 423, "col": 34, "offset": 18335}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/Endpoints/index.ts", "start": {"line": 470, "col": 13, "offset": 20273}, "end": {"line": 470, "col": 34, "offset": 20294}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/Endpoints/index.ts", "start": {"line": 509, "col": 17, "offset": 22252}, "end": {"line": 509, "col": 44, "offset": 22279}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/FetchWithErrorHandling/index.ts", "start": {"line": 201, "col": 17, "offset": 8529}, "end": {"line": 201, "col": 42, "offset": 8554}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/neos_neos-ui/packages/neos-ui-error/src/container/ErrorBoundary/terminateDueToFatalInitializationError.ts", "start": {"line": 10, "col": 5, "offset": 337}, "end": {"line": 20, "col": 7, "offset": 690}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility-webpack-adapter/scripts/helpers/webpack.config.js", "start": {"line": 144, "col": 47, "offset": 6750}, "end": {"line": 144, "col": 83, "offset": 6786}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/Changes/saveHooks.ts", "start": {"line": 36, "col": 35, "offset": 1167}, "end": {"line": 36, "col": 85, "offset": 1217}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/neos_neos-ui/packages/neos-ui-validators/src/RegularExpression/index.tsx", "start": {"line": 21, "col": 31, "offset": 848}, "end": {"line": 21, "col": 61, "offset": 878}, "extra": {"message": "RegExp() called with a `validatorOptions` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/neos_neos-ui/packages/neos-ui/src/index.js", "start": {"line": 121, "col": 13, "offset": 4480}, "end": {"line": 121, "col": 47, "offset": 4514}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/neos_neos-ui/packages/neos-ui/src/index.js", "start": {"line": 141, "col": 13, "offset": 5686}, "end": {"line": 141, "col": 47, "offset": 5720}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/neos_neos-ui/packages/neos-ui/src/manifest.js", "start": {"line": 427, "col": 9, "offset": 19191}, "end": {"line": 427, "col": 53, "offset": 19235}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/neos_neos-ui/packages/neos-ui/src/manifest.js", "start": {"line": 532, "col": 9, "offset": 23702}, "end": {"line": 532, "col": 53, "offset": 23746}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/neos_neos-ui/packages/neos-ui/src/preprocessNodeConfiguration.ts", "start": {"line": 38, "col": 30, "offset": 1546}, "end": {"line": 38, "col": 100, "offset": 1616}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/neos_neos-ui/packages/react-ui-components/esbuild.js", "start": {"line": 19, "col": 43, "offset": 541}, "end": {"line": 19, "col": 46, "offset": 544}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/neos_neos-ui/packages/react-ui-components/esbuild.js", "start": {"line": 19, "col": 48, "offset": 546}, "end": {"line": 19, "col": 57, "offset": 555}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/neos_neos-ui/packages/react-ui-components/esbuild.js", "start": {"line": 21, "col": 33, "offset": 608}, "end": {"line": 21, "col": 36, "offset": 611}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/neos_neos-ui/packages/react-ui-components/esbuild.js", "start": {"line": 21, "col": 38, "offset": 613}, "end": {"line": 21, "col": 47, "offset": 622}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "typescript.react.security.audit.react-dangerouslysetinnerhtml.react-dangerouslysetinnerhtml", "path": "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/ResourceIcon/index.tsx", "start": {"line": 25, "col": 52, "offset": 537}, "end": {"line": 25, "col": 58, "offset": 543}, "extra": {"message": "Detection of dangerouslySetInnerHTML from non-constant definition. This can inadvertently expose users to cross-site scripting (XSS) attacks if this comes from user-provided input. If you have to use dangerouslySetInnerHTML, consider using a sanitization library such as DOMPurify to sanitize your HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://react.dev/reference/react-dom/components/common#dangerously-setting-the-inner-html"], "category": "security", "confidence": "MEDIUM", "technology": ["react"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/typescript.react.security.audit.react-dangerouslysetinnerhtml.react-dangerouslysetinnerhtml", "shortlink": "https://sg.run/rAx6"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neos_neos-ui/Classes/Application/ChangeTargetWorkspace.php", "start": {"line": 28, "col": 7, "offset": 0}, "end": {"line": 28, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/neos_neos-ui/Classes/Application/ChangeTargetWorkspace.php:28:\n `readonly` was unexpected", "path": "downloaded_repos/neos_neos-ui/Classes/Application/ChangeTargetWorkspace.php", "spans": [{"file": "downloaded_repos/neos_neos-ui/Classes/Application/ChangeTargetWorkspace.php", "start": {"line": 28, "col": 7, "offset": 0}, "end": {"line": 28, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neos_neos-ui/Classes/Application/DiscardAllChanges.php", "start": {"line": 27, "col": 7, "offset": 0}, "end": {"line": 27, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/neos_neos-ui/Classes/Application/DiscardAllChanges.php:27:\n `readonly` was unexpected", "path": "downloaded_repos/neos_neos-ui/Classes/Application/DiscardAllChanges.php", "spans": [{"file": "downloaded_repos/neos_neos-ui/Classes/Application/DiscardAllChanges.php", "start": {"line": 27, "col": 7, "offset": 0}, "end": {"line": 27, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neos_neos-ui/Classes/Application/DiscardChangesInDocument.php", "start": {"line": 28, "col": 7, "offset": 0}, "end": {"line": 28, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/neos_neos-ui/Classes/Application/DiscardChangesInDocument.php:28:\n `readonly` was unexpected", "path": "downloaded_repos/neos_neos-ui/Classes/Application/DiscardChangesInDocument.php", "spans": [{"file": "downloaded_repos/neos_neos-ui/Classes/Application/DiscardChangesInDocument.php", "start": {"line": 28, "col": 7, "offset": 0}, "end": {"line": 28, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neos_neos-ui/Classes/Application/DiscardChangesInSite.php", "start": {"line": 28, "col": 7, "offset": 0}, "end": {"line": 28, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/neos_neos-ui/Classes/Application/DiscardChangesInSite.php:28:\n `readonly` was unexpected", "path": "downloaded_repos/neos_neos-ui/Classes/Application/DiscardChangesInSite.php", "spans": [{"file": "downloaded_repos/neos_neos-ui/Classes/Application/DiscardChangesInSite.php", "start": {"line": 28, "col": 7, "offset": 0}, "end": {"line": 28, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neos_neos-ui/Classes/Application/PublishChangesInDocument/PublishChangesInDocumentCommand.php", "start": {"line": 30, "col": 7, "offset": 0}, "end": {"line": 30, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/neos_neos-ui/Classes/Application/PublishChangesInDocument/PublishChangesInDocumentCommand.php:30:\n `readonly` was unexpected", "path": "downloaded_repos/neos_neos-ui/Classes/Application/PublishChangesInDocument/PublishChangesInDocumentCommand.php", "spans": [{"file": "downloaded_repos/neos_neos-ui/Classes/Application/PublishChangesInDocument/PublishChangesInDocumentCommand.php", "start": {"line": 30, "col": 7, "offset": 0}, "end": {"line": 30, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neos_neos-ui/Classes/Application/PublishChangesInSite/PublishChangesInSiteCommand.php", "start": {"line": 30, "col": 7, "offset": 0}, "end": {"line": 30, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/neos_neos-ui/Classes/Application/PublishChangesInSite/PublishChangesInSiteCommand.php:30:\n `readonly` was unexpected", "path": "downloaded_repos/neos_neos-ui/Classes/Application/PublishChangesInSite/PublishChangesInSiteCommand.php", "spans": [{"file": "downloaded_repos/neos_neos-ui/Classes/Application/PublishChangesInSite/PublishChangesInSiteCommand.php", "start": {"line": 30, "col": 7, "offset": 0}, "end": {"line": 30, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/MinimalNodeForTree.php", "start": {"line": 29, "col": 7, "offset": 0}, "end": {"line": 29, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/MinimalNodeForTree.php:29:\n `readonly` was unexpected", "path": "downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/MinimalNodeForTree.php", "spans": [{"file": "downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/MinimalNodeForTree.php", "start": {"line": 29, "col": 7, "offset": 0}, "end": {"line": 29, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/NodeMap.php", "start": {"line": 27, "col": 7, "offset": 0}, "end": {"line": 27, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/NodeMap.php:27:\n `readonly` was unexpected", "path": "downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/NodeMap.php", "spans": [{"file": "downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/NodeMap.php", "start": {"line": 27, "col": 7, "offset": 0}, "end": {"line": 27, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/NodeMapBuilder.php", "start": {"line": 37, "col": 26, "offset": 0}, "end": {"line": 37, "col": 32, "offset": 6}}, {"path": "downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/NodeMapBuilder.php", "start": {"line": 38, "col": 17, "offset": 0}, "end": {"line": 38, "col": 25, "offset": 8}}, {"path": "downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/NodeMapBuilder.php", "start": {"line": 39, "col": 17, "offset": 0}, "end": {"line": 39, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/NodeMapBuilder.php:37:\n `string` was unexpected", "path": "downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/NodeMapBuilder.php", "spans": [{"file": "downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/NodeMapBuilder.php", "start": {"line": 37, "col": 26, "offset": 0}, "end": {"line": 37, "col": 32, "offset": 6}}, {"file": "downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/NodeMapBuilder.php", "start": {"line": 38, "col": 17, "offset": 0}, "end": {"line": 38, "col": 25, "offset": 8}}, {"file": "downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/NodeMapBuilder.php", "start": {"line": 39, "col": 17, "offset": 0}, "end": {"line": 39, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/ReloadNodesQuery.php", "start": {"line": 31, "col": 7, "offset": 0}, "end": {"line": 31, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/ReloadNodesQuery.php:31:\n `readonly` was unexpected", "path": "downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/ReloadNodesQuery.php", "spans": [{"file": "downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/ReloadNodesQuery.php", "start": {"line": 31, "col": 7, "offset": 0}, "end": {"line": 31, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/ReloadNodesQueryResult.php", "start": {"line": 27, "col": 7, "offset": 0}, "end": {"line": 27, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/ReloadNodesQueryResult.php:27:\n `readonly` was unexpected", "path": "downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/ReloadNodesQueryResult.php", "spans": [{"file": "downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/ReloadNodesQueryResult.php", "start": {"line": 27, "col": 7, "offset": 0}, "end": {"line": 27, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neos_neos-ui/Classes/Application/Shared/Conflict.php", "start": {"line": 26, "col": 7, "offset": 0}, "end": {"line": 26, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/neos_neos-ui/Classes/Application/Shared/Conflict.php:26:\n `readonly` was unexpected", "path": "downloaded_repos/neos_neos-ui/Classes/Application/Shared/Conflict.php", "spans": [{"file": "downloaded_repos/neos_neos-ui/Classes/Application/Shared/Conflict.php", "start": {"line": 26, "col": 7, "offset": 0}, "end": {"line": 26, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neos_neos-ui/Classes/Application/Shared/Conflicts.php", "start": {"line": 23, "col": 7, "offset": 0}, "end": {"line": 23, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/neos_neos-ui/Classes/Application/Shared/Conflicts.php:23:\n `readonly` was unexpected", "path": "downloaded_repos/neos_neos-ui/Classes/Application/Shared/Conflicts.php", "spans": [{"file": "downloaded_repos/neos_neos-ui/Classes/Application/Shared/Conflicts.php", "start": {"line": 23, "col": 7, "offset": 0}, "end": {"line": 23, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neos_neos-ui/Classes/Application/Shared/ConflictsOccurred.php", "start": {"line": 23, "col": 7, "offset": 0}, "end": {"line": 23, "col": 15, "offset": 8}}, {"path": "downloaded_repos/neos_neos-ui/Classes/Application/Shared/ConflictsOccurred.php", "start": {"line": 26, "col": 16, "offset": 0}, "end": {"line": 26, "col": 24, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/neos_neos-ui/Classes/Application/Shared/ConflictsOccurred.php:23:\n `readonly` was unexpected", "path": "downloaded_repos/neos_neos-ui/Classes/Application/Shared/ConflictsOccurred.php", "spans": [{"file": "downloaded_repos/neos_neos-ui/Classes/Application/Shared/ConflictsOccurred.php", "start": {"line": 23, "col": 7, "offset": 0}, "end": {"line": 23, "col": 15, "offset": 8}}, {"file": "downloaded_repos/neos_neos-ui/Classes/Application/Shared/ConflictsOccurred.php", "start": {"line": 26, "col": 16, "offset": 0}, "end": {"line": 26, "col": 24, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neos_neos-ui/Classes/Application/Shared/IconLabel.php", "start": {"line": 23, "col": 7, "offset": 0}, "end": {"line": 23, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/neos_neos-ui/Classes/Application/Shared/IconLabel.php:23:\n `readonly` was unexpected", "path": "downloaded_repos/neos_neos-ui/Classes/Application/Shared/IconLabel.php", "spans": [{"file": "downloaded_repos/neos_neos-ui/Classes/Application/Shared/IconLabel.php", "start": {"line": 23, "col": 7, "offset": 0}, "end": {"line": 23, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neos_neos-ui/Classes/Application/Shared/PartialConflictsOccurred.php", "start": {"line": 23, "col": 7, "offset": 0}, "end": {"line": 23, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/neos_neos-ui/Classes/Application/Shared/PartialConflictsOccurred.php:23:\n `readonly` was unexpected", "path": "downloaded_repos/neos_neos-ui/Classes/Application/Shared/PartialConflictsOccurred.php", "spans": [{"file": "downloaded_repos/neos_neos-ui/Classes/Application/Shared/PartialConflictsOccurred.php", "start": {"line": 23, "col": 7, "offset": 0}, "end": {"line": 23, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neos_neos-ui/Classes/Application/Shared/PublishSucceeded.php", "start": {"line": 23, "col": 7, "offset": 0}, "end": {"line": 23, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/neos_neos-ui/Classes/Application/Shared/PublishSucceeded.php:23:\n `readonly` was unexpected", "path": "downloaded_repos/neos_neos-ui/Classes/Application/Shared/PublishSucceeded.php", "spans": [{"file": "downloaded_repos/neos_neos-ui/Classes/Application/Shared/PublishSucceeded.php", "start": {"line": 23, "col": 7, "offset": 0}, "end": {"line": 23, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neos_neos-ui/Classes/Application/SyncWorkspace/SyncWorkspaceCommand.php", "start": {"line": 30, "col": 7, "offset": 0}, "end": {"line": 30, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/neos_neos-ui/Classes/Application/SyncWorkspace/SyncWorkspaceCommand.php:30:\n `readonly` was unexpected", "path": "downloaded_repos/neos_neos-ui/Classes/Application/SyncWorkspace/SyncWorkspaceCommand.php", "spans": [{"file": "downloaded_repos/neos_neos-ui/Classes/Application/SyncWorkspace/SyncWorkspaceCommand.php", "start": {"line": 30, "col": 7, "offset": 0}, "end": {"line": 30, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neos_neos-ui/Classes/Application/SyncWorkspace/SyncingSucceeded.php", "start": {"line": 26, "col": 7, "offset": 0}, "end": {"line": 26, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/neos_neos-ui/Classes/Application/SyncWorkspace/SyncingSucceeded.php:26:\n `readonly` was unexpected", "path": "downloaded_repos/neos_neos-ui/Classes/Application/SyncWorkspace/SyncingSucceeded.php", "spans": [{"file": "downloaded_repos/neos_neos-ui/Classes/Application/SyncWorkspace/SyncingSucceeded.php", "start": {"line": 26, "col": 7, "offset": 0}, "end": {"line": 26, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neos_neos-ui/Classes/Controller/BackendServiceController.php", "start": {"line": 526, "col": 41, "offset": 0}, "end": {"line": 526, "col": 44, "offset": 3}}, {"path": "downloaded_repos/neos_neos-ui/Classes/Controller/BackendServiceController.php", "start": {"line": 553, "col": 41, "offset": 0}, "end": {"line": 553, "col": 44, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/neos_neos-ui/Classes/Controller/BackendServiceController.php:526:\n `...` was unexpected", "path": "downloaded_repos/neos_neos-ui/Classes/Controller/BackendServiceController.php", "spans": [{"file": "downloaded_repos/neos_neos-ui/Classes/Controller/BackendServiceController.php", "start": {"line": 526, "col": 41, "offset": 0}, "end": {"line": 526, "col": 44, "offset": 3}}, {"file": "downloaded_repos/neos_neos-ui/Classes/Controller/BackendServiceController.php", "start": {"line": 553, "col": 41, "offset": 0}, "end": {"line": 553, "col": 44, "offset": 3}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neos_neos-ui/Classes/Domain/Model/Feedback/Operations/UpdateWorkspaceInfo.php", "start": {"line": 45, "col": 17, "offset": 0}, "end": {"line": 45, "col": 25, "offset": 8}}, {"path": "downloaded_repos/neos_neos-ui/Classes/Domain/Model/Feedback/Operations/UpdateWorkspaceInfo.php", "start": {"line": 46, "col": 17, "offset": 0}, "end": {"line": 46, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/neos_neos-ui/Classes/Domain/Model/Feedback/Operations/UpdateWorkspaceInfo.php:45:\n `readonly` was unexpected", "path": "downloaded_repos/neos_neos-ui/Classes/Domain/Model/Feedback/Operations/UpdateWorkspaceInfo.php", "spans": [{"file": "downloaded_repos/neos_neos-ui/Classes/Domain/Model/Feedback/Operations/UpdateWorkspaceInfo.php", "start": {"line": 45, "col": 17, "offset": 0}, "end": {"line": 45, "col": 25, "offset": 8}}, {"file": "downloaded_repos/neos_neos-ui/Classes/Domain/Model/Feedback/Operations/UpdateWorkspaceInfo.php", "start": {"line": 46, "col": 17, "offset": 0}, "end": {"line": 46, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neos_neos-ui/Classes/Domain/NodeCreation/NodeCreationCommands.php", "start": {"line": 48, "col": 7, "offset": 0}, "end": {"line": 48, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/neos_neos-ui/Classes/Domain/NodeCreation/NodeCreationCommands.php:48:\n `readonly` was unexpected", "path": "downloaded_repos/neos_neos-ui/Classes/Domain/NodeCreation/NodeCreationCommands.php", "spans": [{"file": "downloaded_repos/neos_neos-ui/Classes/Domain/NodeCreation/NodeCreationCommands.php", "start": {"line": 48, "col": 7, "offset": 0}, "end": {"line": 48, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neos_neos-ui/Classes/Domain/NodeCreation/NodeCreationElements.php", "start": {"line": 59, "col": 7, "offset": 0}, "end": {"line": 59, "col": 15, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/neos_neos-ui/Classes/Domain/NodeCreation/NodeCreationElements.php:59:\n `readonly` was unexpected", "path": "downloaded_repos/neos_neos-ui/Classes/Domain/NodeCreation/NodeCreationElements.php", "spans": [{"file": "downloaded_repos/neos_neos-ui/Classes/Domain/NodeCreation/NodeCreationElements.php", "start": {"line": 59, "col": 7, "offset": 0}, "end": {"line": 59, "col": 15, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neos_neos-ui/Classes/Infrastructure/ContentRepository/ConflictsFactory.php", "start": {"line": 64, "col": 17, "offset": 0}, "end": {"line": 64, "col": 25, "offset": 8}}, {"path": "downloaded_repos/neos_neos-ui/Classes/Infrastructure/ContentRepository/ConflictsFactory.php", "start": {"line": 65, "col": 17, "offset": 0}, "end": {"line": 65, "col": 25, "offset": 8}}, {"path": "downloaded_repos/neos_neos-ui/Classes/Infrastructure/ContentRepository/ConflictsFactory.php", "start": {"line": 67, "col": 17, "offset": 0}, "end": {"line": 67, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/neos_neos-ui/Classes/Infrastructure/ContentRepository/ConflictsFactory.php:64:\n `readonly` was unexpected", "path": "downloaded_repos/neos_neos-ui/Classes/Infrastructure/ContentRepository/ConflictsFactory.php", "spans": [{"file": "downloaded_repos/neos_neos-ui/Classes/Infrastructure/ContentRepository/ConflictsFactory.php", "start": {"line": 64, "col": 17, "offset": 0}, "end": {"line": 64, "col": 25, "offset": 8}}, {"file": "downloaded_repos/neos_neos-ui/Classes/Infrastructure/ContentRepository/ConflictsFactory.php", "start": {"line": 65, "col": 17, "offset": 0}, "end": {"line": 65, "col": 25, "offset": 8}}, {"file": "downloaded_repos/neos_neos-ui/Classes/Infrastructure/ContentRepository/ConflictsFactory.php", "start": {"line": 67, "col": 17, "offset": 0}, "end": {"line": 67, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neos_neos-ui/Classes/Infrastructure/ContentRepository/CreationDialog/PromotedElementsCreationHandlerFactory.php", "start": {"line": 30, "col": 25, "offset": 0}, "end": {"line": 30, "col": 33, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/neos_neos-ui/Classes/Infrastructure/ContentRepository/CreationDialog/PromotedElementsCreationHandlerFactory.php:30:\n `readonly` was unexpected", "path": "downloaded_repos/neos_neos-ui/Classes/Infrastructure/ContentRepository/CreationDialog/PromotedElementsCreationHandlerFactory.php", "spans": [{"file": "downloaded_repos/neos_neos-ui/Classes/Infrastructure/ContentRepository/CreationDialog/PromotedElementsCreationHandlerFactory.php", "start": {"line": 30, "col": 25, "offset": 0}, "end": {"line": 30, "col": 33, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neos_neos-ui/Classes/Infrastructure/MVC/RoutesProviderHelper.php", "start": {"line": 27, "col": 17, "offset": 0}, "end": {"line": 27, "col": 25, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/neos_neos-ui/Classes/Infrastructure/MVC/RoutesProviderHelper.php:27:\n `readonly` was unexpected", "path": "downloaded_repos/neos_neos-ui/Classes/Infrastructure/MVC/RoutesProviderHelper.php", "spans": [{"file": "downloaded_repos/neos_neos-ui/Classes/Infrastructure/MVC/RoutesProviderHelper.php", "start": {"line": 27, "col": 17, "offset": 0}, "end": {"line": 27, "col": 25, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neos_neos-ui/Classes/Infrastructure/Neos/UriPathSegmentNodeCreationHandlerFactory.php", "start": {"line": 41, "col": 25, "offset": 0}, "end": {"line": 41, "col": 33, "offset": 8}}, {"path": "downloaded_repos/neos_neos-ui/Classes/Infrastructure/Neos/UriPathSegmentNodeCreationHandlerFactory.php", "start": {"line": 42, "col": 25, "offset": 0}, "end": {"line": 42, "col": 33, "offset": 8}}]], "message": "Syntax error at line downloaded_repos/neos_neos-ui/Classes/Infrastructure/Neos/UriPathSegmentNodeCreationHandlerFactory.php:41:\n `readonly` was unexpected", "path": "downloaded_repos/neos_neos-ui/Classes/Infrastructure/Neos/UriPathSegmentNodeCreationHandlerFactory.php", "spans": [{"file": "downloaded_repos/neos_neos-ui/Classes/Infrastructure/Neos/UriPathSegmentNodeCreationHandlerFactory.php", "start": {"line": 41, "col": 25, "offset": 0}, "end": {"line": 41, "col": 33, "offset": 8}}, {"file": "downloaded_repos/neos_neos-ui/Classes/Infrastructure/Neos/UriPathSegmentNodeCreationHandlerFactory.php", "start": {"line": 42, "col": 25, "offset": 0}, "end": {"line": 42, "col": 33, "offset": 8}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neos_neos-ui/Classes/View/OutOfBandRenderingViewFactory.php", "start": {"line": 31, "col": 36, "offset": 0}, "end": {"line": 31, "col": 49, "offset": 13}}]], "message": "Syntax error at line downloaded_repos/neos_neos-ui/Classes/View/OutOfBandRenderingViewFactory.php:31:\n `AbstractView&` was unexpected", "path": "downloaded_repos/neos_neos-ui/Classes/View/OutOfBandRenderingViewFactory.php", "spans": [{"file": "downloaded_repos/neos_neos-ui/Classes/View/OutOfBandRenderingViewFactory.php", "start": {"line": 31, "col": 36, "offset": 0}, "end": {"line": 31, "col": 49, "offset": 13}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neos_neos-ui/Resources/Private/Templates/Error/ErrorMessage.html", "start": {"line": 7, "col": 31, "offset": 0}, "end": {"line": 7, "col": 48, "offset": 17}}, {"path": "downloaded_repos/neos_neos-ui/Resources/Private/Templates/Error/ErrorMessage.html", "start": {"line": 10, "col": 74, "offset": 0}, "end": {"line": 10, "col": 91, "offset": 17}}]], "message": "Syntax error at line downloaded_repos/neos_neos-ui/Resources/Private/Templates/Error/ErrorMessage.html:7:\n `> f:format.raw()}` was unexpected", "path": "downloaded_repos/neos_neos-ui/Resources/Private/Templates/Error/ErrorMessage.html", "spans": [{"file": "downloaded_repos/neos_neos-ui/Resources/Private/Templates/Error/ErrorMessage.html", "start": {"line": 7, "col": 31, "offset": 0}, "end": {"line": 7, "col": 48, "offset": 17}}, {"file": "downloaded_repos/neos_neos-ui/Resources/Private/Templates/Error/ErrorMessage.html", "start": {"line": 10, "col": 74, "offset": 0}, "end": {"line": 10, "col": 91, "offset": 17}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/Classes/Application/WriteAdditionalSettings/WriteAdditionalSettingsCommand.php", "start": {"line": 26, "col": 25, "offset": 0}, "end": {"line": 26, "col": 30, "offset": 5}}]], "message": "Syntax error at line downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/Classes/Application/WriteAdditionalSettings/WriteAdditionalSettingsCommand.php:26:\n `array` was unexpected", "path": "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/Classes/Application/WriteAdditionalSettings/WriteAdditionalSettingsCommand.php", "spans": [{"file": "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/Classes/Application/WriteAdditionalSettings/WriteAdditionalSettingsCommand.php", "start": {"line": 26, "col": 25, "offset": 0}, "end": {"line": 26, "col": 30, "offset": 5}}]}], "paths": {"scanned": ["downloaded_repos/neos_neos-ui/.circleci/config.yml", "downloaded_repos/neos_neos-ui/.ecrc.json", "downloaded_repos/neos_neos-ui/.editorconfig", "downloaded_repos/neos_neos-ui/.eslintrc.js", "downloaded_repos/neos_neos-ui/.gitattributes", "downloaded_repos/neos_neos-ui/.github/ISSUE_TEMPLATE.md", "downloaded_repos/neos_neos-ui/.github/PULL_REQUEST_TEMPLATE.md", "downloaded_repos/neos_neos-ui/.github/dependabot.yml", "downloaded_repos/neos_neos-ui/.github/release.yml", "downloaded_repos/neos_neos-ui/.github/stale.yml", "downloaded_repos/neos_neos-ui/.github/workflows/add-pr-labels.yml", "downloaded_repos/neos_neos-ui/.github/workflows/build_and_test.yml", "downloaded_repos/neos_neos-ui/.gitignore", "downloaded_repos/neos_neos-ui/.nvmrc", "downloaded_repos/neos_neos-ui/.sauce/config.yml", "downloaded_repos/neos_neos-ui/.sauceignore", "downloaded_repos/neos_neos-ui/.styleci.yml", "downloaded_repos/neos_neos-ui/.stylelintrc", "downloaded_repos/neos_neos-ui/.yarnrc.yml", "downloaded_repos/neos_neos-ui/Build/Jenkins/release-neos-ui.sh", "downloaded_repos/neos_neos-ui/Build/Jenkins/update-neos-ui-compiled.sh", "downloaded_repos/neos_neos-ui/Classes/Application/ChangeTargetWorkspace.php", "downloaded_repos/neos_neos-ui/Classes/Application/DiscardAllChanges.php", "downloaded_repos/neos_neos-ui/Classes/Application/DiscardChangesInDocument.php", "downloaded_repos/neos_neos-ui/Classes/Application/DiscardChangesInSite.php", "downloaded_repos/neos_neos-ui/Classes/Application/PublishChangesInDocument/PublishChangesInDocumentCommand.php", "downloaded_repos/neos_neos-ui/Classes/Application/PublishChangesInDocument/PublishChangesInDocumentCommandHandler.php", "downloaded_repos/neos_neos-ui/Classes/Application/PublishChangesInSite/PublishChangesInSiteCommand.php", "downloaded_repos/neos_neos-ui/Classes/Application/PublishChangesInSite/PublishChangesInSiteCommandHandler.php", "downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/MinimalNodeForTree.php", "downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/NoDocumentNodeWasFound.php", "downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/NoSiteNodeWasFound.php", "downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/NodeMap.php", "downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/NodeMapBuilder.php", "downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/ReloadNodesQuery.php", "downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/ReloadNodesQueryHandler.php", "downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/ReloadNodesQueryResult.php", "downloaded_repos/neos_neos-ui/Classes/Application/Shared/Conflict.php", "downloaded_repos/neos_neos-ui/Classes/Application/Shared/Conflicts.php", "downloaded_repos/neos_neos-ui/Classes/Application/Shared/ConflictsOccurred.php", "downloaded_repos/neos_neos-ui/Classes/Application/Shared/IconLabel.php", "downloaded_repos/neos_neos-ui/Classes/Application/Shared/PartialConflictsOccurred.php", "downloaded_repos/neos_neos-ui/Classes/Application/Shared/PublishSucceeded.php", "downloaded_repos/neos_neos-ui/Classes/Application/Shared/ReasonForConflict.php", "downloaded_repos/neos_neos-ui/Classes/Application/Shared/TypeOfChange.php", "downloaded_repos/neos_neos-ui/Classes/Application/SyncWorkspace/SyncWorkspaceCommand.php", "downloaded_repos/neos_neos-ui/Classes/Application/SyncWorkspace/SyncWorkspaceCommandHandler.php", "downloaded_repos/neos_neos-ui/Classes/Application/SyncWorkspace/SyncingSucceeded.php", "downloaded_repos/neos_neos-ui/Classes/ContentRepository/Service/NeosUiNodeService.php", "downloaded_repos/neos_neos-ui/Classes/ContentRepository/Service/WorkspaceService.php", "downloaded_repos/neos_neos-ui/Classes/Controller/BackendController.php", "downloaded_repos/neos_neos-ui/Classes/Controller/BackendServiceController.php", "downloaded_repos/neos_neos-ui/Classes/Controller/TranslationTrait.php", "downloaded_repos/neos_neos-ui/Classes/Domain/InitialData/CacheConfigurationVersionProviderInterface.php", "downloaded_repos/neos_neos-ui/Classes/Domain/InitialData/ConfigurationProviderInterface.php", "downloaded_repos/neos_neos-ui/Classes/Domain/InitialData/FrontendConfigurationProviderInterface.php", "downloaded_repos/neos_neos-ui/Classes/Domain/InitialData/InitialStateProviderInterface.php", "downloaded_repos/neos_neos-ui/Classes/Domain/InitialData/MenuProviderInterface.php", "downloaded_repos/neos_neos-ui/Classes/Domain/InitialData/NodeTypeGroupsAndRolesProviderInterface.php", "downloaded_repos/neos_neos-ui/Classes/Domain/InitialData/RoutesProviderInterface.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/AbstractChange.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/AbstractFeedback.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/ChangeCollection.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/ChangeInterface.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/Changes/AbstractCreate.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/Changes/AbstractStructuralChange.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/Changes/CopyAfter.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/Changes/CopyBefore.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/Changes/CopyInto.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/Changes/Create.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/Changes/CreateAfter.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/Changes/CreateBefore.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/Changes/MoveAfter.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/Changes/MoveBefore.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/Changes/MoveInto.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/Changes/Property.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/Changes/Remove.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/Feedback/AbstractMessageFeedback.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/Feedback/Messages/Error.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/Feedback/Messages/Info.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/Feedback/Messages/Success.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/Feedback/Messages/Warning.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/Feedback/Operations/NodeCreated.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/Feedback/Operations/Redirect.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/Feedback/Operations/ReloadContentOutOfBand.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/Feedback/Operations/ReloadDocument.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/Feedback/Operations/RemoveNode.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/Feedback/Operations/RenderContentOutOfBand.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/Feedback/Operations/UpdateNodeInfo.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/Feedback/Operations/UpdateNodePath.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/Feedback/Operations/UpdateNodePreviewUrl.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/Feedback/Operations/UpdateWorkspaceInfo.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/FeedbackCollection.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/FeedbackInterface.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/ReferencingChangeInterface.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Model/RenderedNodeDomAddress.php", "downloaded_repos/neos_neos-ui/Classes/Domain/NodeCreation/NodeCreationCommands.php", "downloaded_repos/neos_neos-ui/Classes/Domain/NodeCreation/NodeCreationElements.php", "downloaded_repos/neos_neos-ui/Classes/Domain/NodeCreation/NodeCreationHandlerFactoryInterface.php", "downloaded_repos/neos_neos-ui/Classes/Domain/NodeCreation/NodeCreationHandlerInterface.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Service/ConfigurationRenderingService.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Service/NodePropertyConversionService.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Service/NodePropertyConverterService.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Service/StyleAndJavascriptInclusionService.php", "downloaded_repos/neos_neos-ui/Classes/Domain/Service/UserLocaleService.php", "downloaded_repos/neos_neos-ui/Classes/Exception/InvalidNodeCreationHandlerException.php", "downloaded_repos/neos_neos-ui/Classes/FlowQueryOperations/NeosUiDefaultNodesOperation.php", "downloaded_repos/neos_neos-ui/Classes/FlowQueryOperations/NeosUiFilteredChildrenOperation.php", "downloaded_repos/neos_neos-ui/Classes/FlowQueryOperations/SearchOperation.php", "downloaded_repos/neos_neos-ui/Classes/Fusion/ExceptionHandler/PageExceptionHandler.php", "downloaded_repos/neos_neos-ui/Classes/Fusion/Helper/ContentDimensionsHelper.php", "downloaded_repos/neos_neos-ui/Classes/Fusion/Helper/NodeInfoHelper.php", "downloaded_repos/neos_neos-ui/Classes/Fusion/Helper/RenderingModeHelper.php", "downloaded_repos/neos_neos-ui/Classes/Fusion/Helper/StaticResourcesHelper.php", "downloaded_repos/neos_neos-ui/Classes/Fusion/Helper/WorkspaceHelper.php", "downloaded_repos/neos_neos-ui/Classes/Fusion/RenderConfigurationImplementation.php", "downloaded_repos/neos_neos-ui/Classes/Infrastructure/Cache/CacheConfigurationVersionProvider.php", "downloaded_repos/neos_neos-ui/Classes/Infrastructure/Configuration/ConfigurationProvider.php", "downloaded_repos/neos_neos-ui/Classes/Infrastructure/Configuration/FrontendConfigurationProvider.php", "downloaded_repos/neos_neos-ui/Classes/Infrastructure/Configuration/InitialStateProvider.php", "downloaded_repos/neos_neos-ui/Classes/Infrastructure/ContentRepository/ConflictsFactory.php", "downloaded_repos/neos_neos-ui/Classes/Infrastructure/ContentRepository/CreationDialog/CreationDialogNodeTypePostprocessor.php", "downloaded_repos/neos_neos-ui/Classes/Infrastructure/ContentRepository/CreationDialog/PromotedElementsCreationHandlerFactory.php", "downloaded_repos/neos_neos-ui/Classes/Infrastructure/ContentRepository/NodeTypeGroupsAndRolesProvider.php", "downloaded_repos/neos_neos-ui/Classes/Infrastructure/MVC/RoutesProvider.php", "downloaded_repos/neos_neos-ui/Classes/Infrastructure/MVC/RoutesProviderHelper.php", "downloaded_repos/neos_neos-ui/Classes/Infrastructure/Neos/MenuProvider.php", "downloaded_repos/neos_neos-ui/Classes/Infrastructure/Neos/UriPathSegmentNodeCreationHandlerFactory.php", "downloaded_repos/neos_neos-ui/Classes/Presentation/ApplicationView.php", "downloaded_repos/neos_neos-ui/Classes/Service/NodeClipboard.php", "downloaded_repos/neos_neos-ui/Classes/Service/NodePropertyValidationService.php", "downloaded_repos/neos_neos-ui/Classes/TypeConverter/ChangeCollectionConverter.php", "downloaded_repos/neos_neos-ui/Classes/View/OutOfBandRenderingCapable.php", "downloaded_repos/neos_neos-ui/Classes/View/OutOfBandRenderingFusionView.php", "downloaded_repos/neos_neos-ui/Classes/View/OutOfBandRenderingViewFactory.php", "downloaded_repos/neos_neos-ui/Configuration/NodeTypes.yaml", "downloaded_repos/neos_neos-ui/Configuration/Objects.yaml", "downloaded_repos/neos_neos-ui/Configuration/Policy.yaml", "downloaded_repos/neos_neos-ui/Configuration/Routes.Backend.yaml", "downloaded_repos/neos_neos-ui/Configuration/Routes.Service.yaml", "downloaded_repos/neos_neos-ui/Configuration/Routes.yaml", "downloaded_repos/neos_neos-ui/Configuration/Settings.yaml", "downloaded_repos/neos_neos-ui/LICENSE", "downloaded_repos/neos_neos-ui/Makefile", "downloaded_repos/neos_neos-ui/Migrations/Code/Version20180907103800.php", "downloaded_repos/neos_neos-ui/Migrations/Code/Version20190319094900.php", "downloaded_repos/neos_neos-ui/README.md", "downloaded_repos/neos_neos-ui/Resources/Private/Fusion/Prototypes/Page.fusion", "downloaded_repos/neos_neos-ui/Resources/Private/Fusion/Prototypes/RenderConfiguration.fusion", "downloaded_repos/neos_neos-ui/Resources/Private/Fusion/Root.fusion", "downloaded_repos/neos_neos-ui/Resources/Private/Templates/Backend/ConfigurationVersion.html", "downloaded_repos/neos_neos-ui/Resources/Private/Templates/Backend/Guest.html", "downloaded_repos/neos_neos-ui/Resources/Private/Templates/Backend/GuestNotificationScript.html", "downloaded_repos/neos_neos-ui/Resources/Private/Templates/Error/ErrorMessage.html", "downloaded_repos/neos_neos-ui/Resources/Private/Translations/ar/Main.xlf", "downloaded_repos/neos_neos-ui/Resources/Private/Translations/cs/Main.xlf", "downloaded_repos/neos_neos-ui/Resources/Private/Translations/da/Main.xlf", "downloaded_repos/neos_neos-ui/Resources/Private/Translations/de/Main.xlf", "downloaded_repos/neos_neos-ui/Resources/Private/Translations/el/Main.xlf", "downloaded_repos/neos_neos-ui/Resources/Private/Translations/en/Error.xlf", "downloaded_repos/neos_neos-ui/Resources/Private/Translations/en/Main.xlf", "downloaded_repos/neos_neos-ui/Resources/Private/Translations/en/PublishingDialog.xlf", "downloaded_repos/neos_neos-ui/Resources/Private/Translations/en/SyncWorkspaceDialog.xlf", "downloaded_repos/neos_neos-ui/Resources/Private/Translations/es/Main.xlf", "downloaded_repos/neos_neos-ui/Resources/Private/Translations/fi/Main.xlf", "downloaded_repos/neos_neos-ui/Resources/Private/Translations/fr/Main.xlf", "downloaded_repos/neos_neos-ui/Resources/Private/Translations/hu/Main.xlf", "downloaded_repos/neos_neos-ui/Resources/Private/Translations/id/Main.xlf", "downloaded_repos/neos_neos-ui/Resources/Private/Translations/it/Main.xlf", "downloaded_repos/neos_neos-ui/Resources/Private/Translations/ja/Main.xlf", "downloaded_repos/neos_neos-ui/Resources/Private/Translations/km/Main.xlf", "downloaded_repos/neos_neos-ui/Resources/Private/Translations/lv/Main.xlf", "downloaded_repos/neos_neos-ui/Resources/Private/Translations/nl/Main.xlf", "downloaded_repos/neos_neos-ui/Resources/Private/Translations/no/Main.xlf", "downloaded_repos/neos_neos-ui/Resources/Private/Translations/pl/Main.xlf", "downloaded_repos/neos_neos-ui/Resources/Private/Translations/pt/Main.xlf", "downloaded_repos/neos_neos-ui/Resources/Private/Translations/pt_PT/Main.xlf", "downloaded_repos/neos_neos-ui/Resources/Private/Translations/ru/Main.xlf", "downloaded_repos/neos_neos-ui/Resources/Private/Translations/sr/Main.xlf", "downloaded_repos/neos_neos-ui/Resources/Private/Translations/sv/Main.xlf", "downloaded_repos/neos_neos-ui/Resources/Private/Translations/tl/Main.xlf", "downloaded_repos/neos_neos-ui/Resources/Private/Translations/tr/Main.xlf", "downloaded_repos/neos_neos-ui/Resources/Private/Translations/uk/Main.xlf", "downloaded_repos/neos_neos-ui/Resources/Private/Translations/vi/Main.xlf", "downloaded_repos/neos_neos-ui/Resources/Private/Translations/zh/Main.xlf", "downloaded_repos/neos_neos-ui/Resources/Private/Translations/zh_TW/Main.xlf", "downloaded_repos/neos_neos-ui/Resources/Public/Build/.gitkeep", "downloaded_repos/neos_neos-ui/Resources/Public/Images/apple-touch-icon.png", "downloaded_repos/neos_neos-ui/Resources/Public/Images/favicon-16x16.png", "downloaded_repos/neos_neos-ui/Resources/Public/Images/favicon-32x32.png", "downloaded_repos/neos_neos-ui/Resources/Public/Images/safari-pinned-tab.svg", "downloaded_repos/neos_neos-ui/Tests/Functional/Service/NodePropertyValidationServiceTest.php", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/Fixtures/1Dimension/createNewNodeInContainer.e2e.js", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/Fixtures/1Dimension/createNewNodes.e2e.js", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/Fixtures/1Dimension/discarding.e2e.js", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/Fixtures/1Dimension/imageEditor.e2e.js", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/Fixtures/1Dimension/inspector.e2e.js", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/Fixtures/1Dimension/inspectorValidation.e2e.js", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/Fixtures/1Dimension/issue-3184.e2e.js", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/Fixtures/1Dimension/nodeTreePresets.e2e.js", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/Fixtures/1Dimension/refreshTree.e2e.js", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/Fixtures/1Dimension/richTextEditor.e2e.js", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/Fixtures/1Dimension/selectBoxes.e2e.js", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/Fixtures/1Dimension/sidebarToggle.e2e.js", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/Fixtures/1Dimension/switchingDimensions.e2e.js", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/Fixtures/1Dimension/syncing.e2e.js", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/Fixtures/1Dimension/treeMultiselect.e2e.js", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/Fixtures/1Dimension/treeSearch.e2e.js", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/Fixtures/2Dimension/switchingDimensions.e2e.js", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/Fixtures/2Dimension/switchingSites.e2e.js", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/.gitignore", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/Configuration/Settings.yaml", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.Test.OneDimension/Configuration/Settings.yaml", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.Test.OneDimension/Resources/Private/Content/Assets/ee3d239e-48b0-4f99-90be-054301b91792.json", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.Test.OneDimension/Resources/Private/Content/ImageVariants/50cd4a3e-1cc3-4bbb-b2ab-919abb4011f1.json", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.Test.OneDimension/Resources/Private/Content/Resources/aac28f51e5ca842e2646e88e7d242ac3c27e1f25", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.Test.OneDimension/Resources/Private/Content/events.jsonl", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.Test.OneDimension/Resources/Private/Content/sites.json", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.Test.OneDimension/composer.json", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.Test.TwoDimensions/Configuration/Settings.yaml", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.Test.TwoDimensions/Resources/Private/Content/Assets/ee3d239e-48b0-4f99-90be-054301b91792.json", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.Test.TwoDimensions/Resources/Private/Content/ImageVariants/50cd4a3e-1cc3-4bbb-b2ab-919abb4011f1.json", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.Test.TwoDimensions/Resources/Private/Content/Resources/aac28f51e5ca842e2646e88e7d242ac3c27e1f25", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.Test.TwoDimensions/Resources/Private/Content/events.jsonl", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.Test.TwoDimensions/Resources/Private/Content/sites.json", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.Test.TwoDimensions/Resources/Private/Fusion/Root.fusion", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.Test.TwoDimensions/composer.json", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/Classes/Application/RemoveAdditionalSettings/Controller/RemoveAdditionalSettingsController.php", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/Classes/Application/RemoveAdditionalSettings/RemoveAdditionalSettingsCommand.php", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/Classes/Application/RemoveAdditionalSettings/RemoveAdditionalSettingsCommandHandler.php", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/Classes/Application/WriteAdditionalSettings/Controller/WriteAdditionalSettingsController.php", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/Classes/Application/WriteAdditionalSettings/WriteAdditionalSettingsCommand.php", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/Classes/Application/WriteAdditionalSettings/WriteAdditionalSettingsCommandHandler.php", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/Classes/DataSources/NodeWithDependingPropertiesDataSource.php", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/Configuration/Policy.yaml", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/Configuration/Routes.yaml", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/Configuration/Settings.Neos.Flow.yaml", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/Configuration/Settings.yaml", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/NodeTypes/Content/Container.yaml", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/NodeTypes/Content/Headline.yaml", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/NodeTypes/Content/Image.yaml", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/NodeTypes/Content/InlineHeadline.yaml", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/NodeTypes/Content/NodeWithDependingProperties.yaml", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/NodeTypes/Content/Text.yaml", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/NodeTypes/Document/Blog.yaml", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/NodeTypes/Document/BlogArticle.yaml", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/NodeTypes/Document/HomePage.yaml", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/NodeTypes/Document/Page.yaml", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/NodeTypes/Document/PageWithImage.yaml", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/NodeTypes/Document/SelectBoxTestPage/OpensAbove.yaml", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/NodeTypes/Document/SelectBoxTestPage/OpensAboveInInspector.yaml", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/NodeTypes/Document/SelectBoxTestPage/OpensBelowAndBreaksOut.yaml", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/Resources/Private/Fusion/Component/Navigation/Navigation.fusion", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/Resources/Private/Fusion/Content/Container/Container.fusion", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/Resources/Private/Fusion/Content/Headline/Headline.fusion", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/Resources/Private/Fusion/Content/Image/Image.fusion", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/Resources/Private/Fusion/Content/InlineHeadline/InlineHeadline.fusion", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/Resources/Private/Fusion/Content/NodeWithDependingProperties/NodeWithDependingProperties.fusion", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/Resources/Private/Fusion/Content/Text/Text.fusion", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/Resources/Private/Fusion/Document/Page/HomePage.fusion", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/Resources/Private/Fusion/Document/Page/Page.fusion", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/Resources/Private/Fusion/Document/PageWithImage/PageWithImage.fusion", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/Resources/Private/Fusion/Document/SelectBoxTestPage/OpensAboveIn.fusion", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/Resources/Private/Fusion/Document/SelectBoxTestPage/OpensAboveInInspector.fusion", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/Resources/Private/Fusion/Root.fusion", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/composer.json", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/composer.json", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/docker-compose.neos-dev-instance.yaml", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/docker-compose.yaml", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/e2e-docker.sh", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/e2e.sh", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/pageModel.js", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/start-neos-dev-instance.sh", "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/utils.js", "downloaded_repos/neos_neos-ui/Tests/Unit/CreationDialogNodeTypePostprocessorTest.php", "downloaded_repos/neos_neos-ui/Tests/Unit/Domain/Model/RenderedNodeDomAddressTest.php", "downloaded_repos/neos_neos-ui/composer.json", "downloaded_repos/neos_neos-ui/cssModules.js", "downloaded_repos/neos_neos-ui/cssVariables.css", "downloaded_repos/neos_neos-ui/cssVariables.js", "downloaded_repos/neos_neos-ui/esbuild.js", "downloaded_repos/neos_neos-ui/legacyDependencies.js", "downloaded_repos/neos_neos-ui/package.json", "downloaded_repos/neos_neos-ui/packages/debug-reason-for-rendering/README.md", "downloaded_repos/neos_neos-ui/packages/debug-reason-for-rendering/docs/screenshot.jpg", "downloaded_repos/neos_neos-ui/packages/debug-reason-for-rendering/package.json", "downloaded_repos/neos_neos-ui/packages/debug-reason-for-rendering/src/index.js", "downloaded_repos/neos_neos-ui/packages/framework-observable/README.md", "downloaded_repos/neos_neos-ui/packages/framework-observable/package.json", "downloaded_repos/neos_neos-ui/packages/framework-observable/src/Observable.spec.ts", "downloaded_repos/neos_neos-ui/packages/framework-observable/src/Observable.ts", "downloaded_repos/neos_neos-ui/packages/framework-observable/src/Observer.ts", "downloaded_repos/neos_neos-ui/packages/framework-observable/src/State.spec.ts", "downloaded_repos/neos_neos-ui/packages/framework-observable/src/State.ts", "downloaded_repos/neos_neos-ui/packages/framework-observable/src/Subscriber.ts", "downloaded_repos/neos_neos-ui/packages/framework-observable/src/Subscription.ts", "downloaded_repos/neos_neos-ui/packages/framework-observable/src/index.ts", "downloaded_repos/neos_neos-ui/packages/framework-observable-react/README.md", "downloaded_repos/neos_neos-ui/packages/framework-observable-react/package.json", "downloaded_repos/neos_neos-ui/packages/framework-observable-react/src/index.ts", "downloaded_repos/neos_neos-ui/packages/framework-observable-react/src/useLatestState.ts", "downloaded_repos/neos_neos-ui/packages/framework-observable-react/src/useLatestValueFrom.ts", "downloaded_repos/neos_neos-ui/packages/jest-preset-neos-ui/jest-preset.json", "downloaded_repos/neos_neos-ui/packages/jest-preset-neos-ui/package.json", "downloaded_repos/neos_neos-ui/packages/jest-preset-neos-ui/src/enzymeConfiguration.js", "downloaded_repos/neos_neos-ui/packages/jest-preset-neos-ui/src/esbuildTransformer.js", "downloaded_repos/neos_neos-ui/packages/jest-preset-neos-ui/src/moduleFieldResolver.js", "downloaded_repos/neos_neos-ui/packages/jest-preset-neos-ui/src/setupBrowserEnv.js", "downloaded_repos/neos_neos-ui/packages/jest-preset-neos-ui/src/setupNeosUiHostEnv.js", "downloaded_repos/neos_neos-ui/packages/neos-ts-interfaces/package.json", "downloaded_repos/neos_neos-ui/packages/neos-ts-interfaces/src/dummy.js", "downloaded_repos/neos_neos-ui/packages/neos-ts-interfaces/src/index.spec.ts", "downloaded_repos/neos_neos-ui/packages/neos-ts-interfaces/src/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui/package.json", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/App.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/ContentCanvas/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/ContentCanvas/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Drawer/MenuItem/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Drawer/MenuItemGroup/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Drawer/UserDropDown/RestoreButtonItem.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Drawer/UserDropDown/UserImage.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Drawer/UserDropDown/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Drawer/UserDropDown/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Drawer/VersionPanel/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Drawer/constants.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Drawer/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Drawer/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/LeftSideBar/NodeTree/Node/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/LeftSideBar/NodeTree/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/LeftSideBar/NodeTree/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/LeftSideBar/NodeTreeSearchBar/NodeTreeFilter/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/LeftSideBar/NodeTreeSearchBar/NodeTreeFilter/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/LeftSideBar/NodeTreeSearchBar/NodeTreeSearchInput/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/LeftSideBar/NodeTreeSearchBar/NodeTreeSearchInput/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/LeftSideBar/NodeTreeSearchBar/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/LeftSideBar/NodeTreeSearchBar/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/LeftSideBar/NodeTreeToolBar/Buttons/AddNode/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/LeftSideBar/NodeTreeToolBar/Buttons/CopySelectedNode/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/LeftSideBar/NodeTreeToolBar/Buttons/CutSelectedNode/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/LeftSideBar/NodeTreeToolBar/Buttons/DeleteSelectedNode/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/LeftSideBar/NodeTreeToolBar/Buttons/HideSelectedNode/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/LeftSideBar/NodeTreeToolBar/Buttons/PasteClipBoardNode/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/LeftSideBar/NodeTreeToolBar/Buttons/RefreshPageTree/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/LeftSideBar/NodeTreeToolBar/Buttons/RefreshPageTree/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/LeftSideBar/NodeTreeToolBar/Buttons/ToggleContentTree/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/LeftSideBar/NodeTreeToolBar/Buttons/ToggleContentTree/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/LeftSideBar/NodeTreeToolBar/Buttons/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/LeftSideBar/NodeTreeToolBar/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/LeftSideBar/NodeTreeToolBar/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/LeftSideBar/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/LeftSideBar/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/DeleteNode/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/DeleteNode/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/InsertMode/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/InsertMode/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/KeyboardShortcutModal/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/KeyboardShortcutModal/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/NodeCreationDialog/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/NodeCreationDialog/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/NodeVariantCreationDialog/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/NodeVariantCreationDialog/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/PublishingDialog/ConfirmationDialog.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/PublishingDialog/Diagram.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/PublishingDialog/PartialConflictDialog.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/PublishingDialog/ProcessIndicator.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/PublishingDialog/PublishingDialog.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/PublishingDialog/ResultDialog.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/PublishingDialog/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/PublishingDialog/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/ReloginDialog/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/ReloginDialog/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/SelectNodeType/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/SelectNodeType/nodeTypeFilter.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/SelectNodeType/nodeTypeGroupPanel.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/SelectNodeType/nodeTypeItem.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/SelectNodeType/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/SyncWorkspaceDialog/ConfirmationDialog.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/SyncWorkspaceDialog/ConflictList.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/SyncWorkspaceDialog/Diagram.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/SyncWorkspaceDialog/ProcessIndicator.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/SyncWorkspaceDialog/ResolutionStrategyConfirmationDialog.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/SyncWorkspaceDialog/ResolutionStrategySelectionDialog.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/SyncWorkspaceDialog/ResultDialog.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/SyncWorkspaceDialog/SyncWorkspaceDialog.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/SyncWorkspaceDialog/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/SyncWorkspaceDialog/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/UnappliedChangesDialog/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/UnappliedChangesDialog/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Modals/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Neos/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/PrimaryToolbar/Brand/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/PrimaryToolbar/Brand/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/PrimaryToolbar/DimensionSwitcher/DimensionSelector.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/PrimaryToolbar/DimensionSwitcher/DimensionSelectorOption.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/PrimaryToolbar/DimensionSwitcher/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/PrimaryToolbar/DimensionSwitcher/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/PrimaryToolbar/EditPreviewDropDown/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/PrimaryToolbar/EditPreviewDropDown/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/PrimaryToolbar/MenuToggler/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/PrimaryToolbar/MenuToggler/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/PrimaryToolbar/PublishDropDown/AbstractButton/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/PrimaryToolbar/PublishDropDown/AbstractButton/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/PrimaryToolbar/PublishDropDown/WorkspaceSelector/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/PrimaryToolbar/PublishDropDown/WorkspaceSelector/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/PrimaryToolbar/PublishDropDown/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/PrimaryToolbar/PublishDropDown/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/PrimaryToolbar/WorkspaceSync/WorkspaceSync.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/PrimaryToolbar/WorkspaceSync/WorkspaceSyncIcon.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/PrimaryToolbar/WorkspaceSync/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/PrimaryToolbar/WorkspaceSync/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/PrimaryToolbar/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/PrimaryToolbar/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/RightSideBar/Inspector/InspectorEditorEnvelope/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/RightSideBar/Inspector/InspectorEditorEnvelope/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/RightSideBar/Inspector/InspectorViewEnvelope/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/RightSideBar/Inspector/InspectorViewEnvelope/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/RightSideBar/Inspector/PropertyGroup/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/RightSideBar/Inspector/PropertyGroup/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/RightSideBar/Inspector/PropertyGroup/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/RightSideBar/Inspector/SelectedElement/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/RightSideBar/Inspector/SelectedElement/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/RightSideBar/Inspector/TabPanel/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/RightSideBar/Inspector/TabPanel/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/RightSideBar/Inspector/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/RightSideBar/Inspector/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/RightSideBar/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/RightSideBar/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/Root.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/SecondaryToolbar/FullScreenButton/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/SecondaryToolbar/FullScreenButton/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/SecondaryToolbar/KeyboardShortcutButton/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/SecondaryToolbar/LoadingIndicator/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/SecondaryToolbar/LoadingIndicator/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/SecondaryToolbar/PreviewButton/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/SecondaryToolbar/PreviewButton/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/SecondaryToolbar/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/SecondaryToolbar/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/DelegatingReducer.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/README.md", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Registry/HotkeyRegistry.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Registry/RichTextToolbarRegistry.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Registry/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/System/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/apiExposureMap.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/clipboardMiddleware.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/localStorageMiddleware.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/manifest.containers.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/manifest.dataloaders.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/manifest.hotkeys.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/manifest.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/manifest.reducer.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/preprocessNodeConfiguration.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/preprocessNodeConfiguration.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui/src/styleHostOnly.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/package.json", "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/Endpoints/Helpers.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/Endpoints/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/FetchWithErrorHandling/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/FlowQuery/Operations/Children/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/FlowQuery/Operations/Context/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/FlowQuery/Operations/Filter/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/FlowQuery/Operations/Find/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/FlowQuery/Operations/Get/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/FlowQuery/Operations/GetForTree/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/FlowQuery/Operations/GetForTreeWithParents/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/FlowQuery/Operations/NeosUiDefaultNodes/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/FlowQuery/Operations/NeosUiFilteredChildren/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/FlowQuery/Operations/Search/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/FlowQuery/Operations/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/FlowQuery/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/FlowQuery/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/Use/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/Use/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/package.json", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/EditorToolbar/EditorToolbar.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/EditorToolbar/Helpers/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/EditorToolbar/Helpers/isToolbarItemActive.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/EditorToolbar/Helpers/isToolbarItemVisible.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/EditorToolbar/Helpers/renderToolbarComponents.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/EditorToolbar/LinkButton.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/EditorToolbar/LinkButton.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/EditorToolbar/StyleSelect.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/EditorToolbar/TableButton.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/EditorToolbar/TableButton.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/EditorToolbar/TableDropDown.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/EditorToolbar/TableDropDown.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/EditorToolbar/TableStyles.vanilla-css", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/EditorToolbar/icons/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/EditorToolbar/icons/table-column.dataurl.svg", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/EditorToolbar/icons/table-merge-cell.dataurl.svg", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/EditorToolbar/icons/table-row.dataurl.svg", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/EditorToolbar/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/EditorToolbar/index.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/ckEditorApi.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/cke-overwrites.vanilla-css", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/cleanupContentBeforeCommit.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/cleanupContentBeforeCommit.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/manifest.config.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/manifest.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/manifest.richtextToolbar.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/placeholder.vanilla-css", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/plugins/disabledAutoparagraphMode.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/plugins/insideTable.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/plugins/italicWithEm.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/plugins/italicWithEmEditing.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/plugins/linkAttributeCommand.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/plugins/linkDownload.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/plugins/linkRelNofollow.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/plugins/linkTargetBlank.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/plugins/linkTitle.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/plugins/sub.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/plugins/sup.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/registry/CkEditorConfigRegistry.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/src/registry/RichTextToolbarRegistry.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-constants/package.json", "downloaded_repos/neos_neos-ui/packages/neos-ui-constants/src/dndTypes.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-constants/src/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-containers/package.json", "downloaded_repos/neos_neos-ui/packages/neos-ui-containers/src/InsertModeSelector/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-containers/src/InsertModeSelector/resources/create-above.svg", "downloaded_repos/neos_neos-ui/packages/neos-ui-containers/src/InsertModeSelector/resources/create-below.svg", "downloaded_repos/neos_neos-ui/packages/neos-ui-containers/src/InsertModeSelector/resources/create-inside.svg", "downloaded_repos/neos_neos-ui/packages/neos-ui-containers/src/InsertModeSelector/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-containers/src/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-contentrepository/package.json", "downloaded_repos/neos_neos-ui/packages/neos-ui-contentrepository/src/manifest.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-contentrepository/src/registry/NodeTypesRegistry.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-contentrepository/src/registry/NodeTypesRegistry.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-contentrepository/src/registry/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-decorators/.yarnclean", "downloaded_repos/neos_neos-ui/packages/neos-ui-decorators/package.json", "downloaded_repos/neos_neos-ui/packages/neos-ui-decorators/src/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-decorators/src/neos.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/package.json", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/EditorEnvelope/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/EditorEnvelope/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/AssetEditor/Components/Controls/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/AssetEditor/Components/Controls/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/AssetEditor/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/Boolean/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/Boolean/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/CKEditor/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/CodeMirror/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/CodeMirror/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/DateTime/helpers.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/DateTime/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/Image/Components/Controls/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/Image/Components/Controls/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/Image/Components/PreviewScreen/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/Image/Components/PreviewScreen/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/Image/Components/ResizeControls/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/Image/Components/ResizeControls/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/Image/Components/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/Image/Utils/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/Image/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/Image/resource/dummy-image.dataurl.svg", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/Image/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/Link/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/NodeType/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/NodeType/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/Range/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/Range/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/Reference/createNew.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/Reference/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/Reference/referenceDataLoader.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/References/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/SelectBox/DataSourceBasedSelectBoxEditor.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/SelectBox/SimpleSelectBoxEditor.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/SelectBox/createSelectBoxValueStringFromPossiblyStrangeNodePropertyValue.spec.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/SelectBox/createSelectBoxValueStringFromPossiblyStrangeNodePropertyValue.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/SelectBox/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/SelectBox/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/SelectBox/selectBoxHelpers.spec.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/SelectBox/selectBoxHelpers.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/TextArea/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/TextField/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/UriPathSegment/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/UriPathSegment/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Editors/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Library/AssetOption.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Library/AssetUpload.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Library/LinkInput.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Library/LinkInput.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Library/LinkInputOptions.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Library/LinkOption.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Library/NodeOption.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Library/PreviewOption.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Library/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Library/sanitizeOptions.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/Library/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/SecondaryEditors/CKEditorWrap/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/SecondaryEditors/CKEditorWrap/index.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/SecondaryEditors/CodeMirrorWrap/codemirror-twilight.vanilla-css", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/SecondaryEditors/CodeMirrorWrap/codemirror.vanilla-css", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/SecondaryEditors/CodeMirrorWrap/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/SecondaryEditors/ImageCropper/AspectRatioDropDown/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/SecondaryEditors/ImageCropper/AspectRatioDropDown/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/SecondaryEditors/ImageCropper/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/SecondaryEditors/ImageCropper/model.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/SecondaryEditors/ImageCropper/react_crop.vanilla-css", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/SecondaryEditors/ImageCropper/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/SecondaryEditors/MediaDetailsScreen/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/SecondaryEditors/MediaDetailsScreen/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/SecondaryEditors/MediaSelectionScreen/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/SecondaryEditors/MediaSelectionScreen/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/SecondaryEditors/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/_lib/testUtils.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-editors/src/manifest.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-error/README.md", "downloaded_repos/neos_neos-ui/packages/neos-ui-error/package.json", "downloaded_repos/neos_neos-ui/packages/neos-ui-error/src/container/ErrorBoundary/ErrorBoundary.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui-error/src/container/ErrorBoundary/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-error/src/container/ErrorBoundary/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-error/src/container/ErrorBoundary/terminateDueToFatalInitializationError.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-error/src/container/ErrorView/ErrorView.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui-error/src/container/ErrorView/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-error/src/container/ErrorView/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-error/src/container/FlashMessages/FlashMessage.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui-error/src/container/FlashMessages/FlashMessages.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui-error/src/container/FlashMessages/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-error/src/container/FlashMessages/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-error/src/container/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-error/src/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-error/src/types.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/README.md", "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/extensibilityMap.json", "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/package.json", "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/src/createConsumerApi.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/src/createConsumerApi.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/src/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/src/manifest.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/src/readFromConsumerApi.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/src/readFromConsumerApi.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/src/registry/AbstractRegistry.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/src/registry/SynchronousMetaRegistry.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/src/registry/SynchronousRegistry.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/src/registry/SynchronousRegistry.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/src/registry/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/src/shims/neosProjectPackages/neos-ui-backend-connector/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/src/shims/neosProjectPackages/neos-ui-ckeditor5-bindings/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/src/shims/neosProjectPackages/neos-ui-decorators/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/src/shims/neosProjectPackages/neos-ui-editors/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/src/shims/neosProjectPackages/neos-ui-guest-frame/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/src/shims/neosProjectPackages/neos-ui-i18n/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/src/shims/neosProjectPackages/neos-ui-redux-store/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/src/shims/neosProjectPackages/neos-ui-views/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/src/shims/neosProjectPackages/react-ui-components/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/src/shims/neosProjectPackages/utils-redux/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/tsconfig.json", "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility-webpack-adapter/README.md", "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility-webpack-adapter/bin/neos-react-scripts.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility-webpack-adapter/package.json", "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility-webpack-adapter/scripts/build.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility-webpack-adapter/scripts/helpers/banner.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility-webpack-adapter/scripts/helpers/postcss.config.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility-webpack-adapter/scripts/helpers/webpack.config.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility-webpack-adapter/scripts/watch.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-guest-frame/package.json", "downloaded_repos/neos_neos-ui/packages/neos-ui-guest-frame/src/InlineUI/InlineValidationErrors/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-guest-frame/src/InlineUI/InlineValidationErrors/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-guest-frame/src/InlineUI/NodeToolbar/Buttons/AddNode/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-guest-frame/src/InlineUI/NodeToolbar/Buttons/CopySelectedNode/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-guest-frame/src/InlineUI/NodeToolbar/Buttons/CutSelectedNode/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-guest-frame/src/InlineUI/NodeToolbar/Buttons/DeleteSelectedNode/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-guest-frame/src/InlineUI/NodeToolbar/Buttons/HideSelectedNode/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-guest-frame/src/InlineUI/NodeToolbar/Buttons/PasteClipBoardNode/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-guest-frame/src/InlineUI/NodeToolbar/Buttons/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-guest-frame/src/InlineUI/NodeToolbar/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-guest-frame/src/InlineUI/NodeToolbar/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-guest-frame/src/InlineUI/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-guest-frame/src/InlineUI/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-guest-frame/src/dom.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-guest-frame/src/dom.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-guest-frame/src/initializeContentDomNode.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-guest-frame/src/initializeGuestFrame.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-guest-frame/src/initializePropertyDomNode.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-guest-frame/src/manifest.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-guest-frame/src/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/README.md", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/package.json", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/component/I18n.spec.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/component/I18n.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/component/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/global/globals.spec.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/global/globals.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/global/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/global/initializeI18n.spec.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/global/initializeI18n.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/global/setupI18n.spec.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/global/setupI18n.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/global/teardownI18n.spec.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/global/teardownI18n.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/manifest.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/model/Locale.spec.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/model/Locale.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/model/Parameters.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/model/PluralRule.spec.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/model/PluralRule.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/model/PluralRules.spec.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/model/PluralRules.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/model/Translation.spec.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/model/Translation.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/model/TranslationAddress.spec.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/model/TranslationAddress.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/model/TranslationRepository.spec.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/model/TranslationRepository.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/model/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/registry/I18nRegistry.spec.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/registry/I18nRegistry.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/registry/LegacyParameters.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/registry/getTranslationAddress.spec.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/registry/getTranslationAddress.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/registry/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/registry/substitutePlaceholders.spec.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/registry/substitutePlaceholders.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/translate.spec.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/translate.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-inspector/package.json", "downloaded_repos/neos_neos-ui/packages/neos-ui-inspector/src/SecondaryInspector/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-inspector/src/SecondaryInspector/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-inspector/src/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/package.json", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/CR/ContentDimensions/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/CR/ContentDimensions/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/CR/Nodes/helpers.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/CR/Nodes/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/CR/Nodes/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/CR/Nodes/selectors.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/CR/Publishing/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/CR/Syncing/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/CR/Workspaces/__snapshots__/index.spec.js.snap", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/CR/Workspaces/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/CR/Workspaces/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/CR/Workspaces/selectors.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/CR/Workspaces/selectors.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/CR/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/Changes/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/ServerFeedback/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/System/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/System/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/AddNodeModal/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/AddNodeModal/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/ContentCanvas/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/ContentCanvas/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/ContentCanvas/selectors.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/ContentTree/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/ContentTree/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/ContentTree/selectors.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/Drawer/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/Drawer/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/EditPreviewMode/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/EditPreviewMode/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/FlashMessages/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/FlashMessages/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/FullScreen/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/FullScreen/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/InsertionModeModal/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/Inspector/__snapshots__/index.spec.js.snap", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/Inspector/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/Inspector/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/Inspector/selectors.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/Inspector/selectors.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/KeyboardShortcutModal/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/LeftSideBar/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/LeftSideBar/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/NodeCreationDialog/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/NodeLinking/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/NodeVariantCreationDialog/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/PageTree/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/PageTree/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/PageTree/selectors.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/PageTree/selectors.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/Remote/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/Remote/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/RightSideBar/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/RightSideBar/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/SelectNodeTypeModal/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/SyncWorkspaceModal/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/UI/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/User/Impersonate/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/User/Name/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/User/Name/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/User/Preferences/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/User/Preferences/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/User/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/combineReducers.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/package.json", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/Browser/__snapshots__/index.spec.js.snap", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/Browser/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/Browser/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/CR/ContentDimensions/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/CR/NodeOperations/addNode.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/CR/NodeOperations/determineInsertMode.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/CR/NodeOperations/helpers.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/CR/NodeOperations/hideNode.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/CR/NodeOperations/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/CR/NodeOperations/moveDroppedNode.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/CR/NodeOperations/moveDroppedNodes.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/CR/NodeOperations/pasteNode.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/CR/NodeOperations/reloadNodes.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/CR/NodeOperations/reloadState.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/CR/NodeOperations/removeNodeIfConfirmed.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/CR/NodeOperations/showNode.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/CR/Policies/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/CR/Workspaces/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/Changes/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/Changes/saveHooks.spec.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/Changes/saveHooks.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/Publish/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/ServerFeedback/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/Sync/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/UI/ContentCanvas/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/UI/ContentTree/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/UI/EditPreviewMode/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/UI/FlashMessages/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/UI/Hotkeys/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/UI/Impersonate/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/UI/Inspector/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/UI/PageTree/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/manifest.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-validators/package.json", "downloaded_repos/neos_neos-ui/packages/neos-ui-validators/src/Alphanumeric/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-validators/src/Alphanumeric/index.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui-validators/src/Count/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-validators/src/Count/index.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui-validators/src/DateTime/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-validators/src/DateTime/index.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui-validators/src/EmailAddress/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-validators/src/EmailAddress/index.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui-validators/src/Float/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-validators/src/Float/index.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui-validators/src/Integer/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-validators/src/Integer/index.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui-validators/src/Label/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-validators/src/Label/index.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui-validators/src/NotEmpty/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-validators/src/NotEmpty/index.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui-validators/src/NumberRange/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-validators/src/NumberRange/index.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui-validators/src/RegularExpression/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-validators/src/RegularExpression/index.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui-validators/src/String/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-validators/src/String/index.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui-validators/src/StringLength/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-validators/src/StringLength/index.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui-validators/src/Text/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-validators/src/Text/index.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui-validators/src/Uuid/index.spec.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-validators/src/Uuid/index.tsx", "downloaded_repos/neos_neos-ui/packages/neos-ui-validators/src/index.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-validators/src/manifest.ts", "downloaded_repos/neos_neos-ui/packages/neos-ui-views/package.json", "downloaded_repos/neos_neos-ui/packages/neos-ui-views/src/Data/ColumnView/column.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-views/src/Data/ColumnView/column.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-views/src/Data/ColumnView/hero.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-views/src/Data/ColumnView/hero.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-views/src/Data/ColumnView/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-views/src/Data/ColumnView/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-views/src/Data/DataLoader/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-views/src/Data/DataLoader/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-views/src/Data/TableView/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-views/src/Data/TableView/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-views/src/Data/TimeSeriesView/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-views/src/NodeInfoView/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-views/src/NodeInfoView/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-views/src/ViewEnvelope/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-views/src/Widget/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-views/src/Widget/style.module.css", "downloaded_repos/neos_neos-ui/packages/neos-ui-views/src/index.js", "downloaded_repos/neos_neos-ui/packages/neos-ui-views/src/manifest.js", "downloaded_repos/neos_neos-ui/packages/positional-array-sorter/README.md", "downloaded_repos/neos_neos-ui/packages/positional-array-sorter/package.json", "downloaded_repos/neos_neos-ui/packages/positional-array-sorter/src/positionalArraySorter.spec.js", "downloaded_repos/neos_neos-ui/packages/positional-array-sorter/src/positionalArraySorter.ts", "downloaded_repos/neos_neos-ui/packages/positional-array-sorter/tsconfig.json", "downloaded_repos/neos_neos-ui/packages/react-proptypes/package.json", "downloaded_repos/neos_neos-ui/packages/react-proptypes/src/documentNode.js", "downloaded_repos/neos_neos-ui/packages/react-proptypes/src/documentNode.spec.js", "downloaded_repos/neos_neos-ui/packages/react-proptypes/src/index.js", "downloaded_repos/neos_neos-ui/packages/react-proptypes/src/internal/nodeShape.js", "downloaded_repos/neos_neos-ui/packages/react-proptypes/src/node.js", "downloaded_repos/neos_neos-ui/packages/react-proptypes/src/node.spec.js", "downloaded_repos/neos_neos-ui/packages/react-proptypes/src/nodeType.js", "downloaded_repos/neos_neos-ui/packages/react-proptypes/src/nodeType.spec.js", "downloaded_repos/neos_neos-ui/packages/react-proptypes/src/propertyDefinition.js", "downloaded_repos/neos_neos-ui/packages/react-proptypes/src/propertyDefinition.spec.js", "downloaded_repos/neos_neos-ui/packages/react-proptypes/src/renderedNodeDomAddress.js", "downloaded_repos/neos_neos-ui/packages/react-proptypes/src/renderedNodeDomAddress.spec.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/.gitignore", "downloaded_repos/neos_neos-ui/packages/react-ui-components/LICENSE", "downloaded_repos/neos_neos-ui/packages/react-ui-components/README.md", "downloaded_repos/neos_neos-ui/packages/react-ui-components/esbuild.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/example/font.css", "downloaded_repos/neos_neos-ui/packages/react-ui-components/example/test.html", "downloaded_repos/neos_neos-ui/packages/react-ui-components/example/test.jsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/package.json", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Badge/__snapshots__/badge.spec.tsx.snap", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Badge/badge.spec.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Badge/badge.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Badge/index.spec.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Badge/index.story.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Badge/index.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Badge/style.module.css", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Bar/__snapshots__/bar.spec.tsx.snap", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Bar/bar.spec.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Bar/bar.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Bar/index.spec.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Bar/index.story.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Bar/index.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Bar/style.module.css", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Button/__snapshots__/button.spec.tsx.snap", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Button/button.spec.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Button/button.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Button/index.spec.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Button/index.story.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Button/index.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Button/style.module.css", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/ButtonGroup/__snapshots__/buttonGroup.spec.tsx.snap", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/ButtonGroup/__snapshots__/buttonGroupItem.spec.tsx.snap", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/ButtonGroup/buttonGroup.spec.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/ButtonGroup/buttonGroup.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/ButtonGroup/buttonGroupItem.spec.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/ButtonGroup/buttonGroupItem.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/ButtonGroup/index.spec.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/ButtonGroup/index.story.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/ButtonGroup/index.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/ButtonGroup/style.module.css", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/CheckBox/__snapshots__/checkBox.spec.tsx.snap", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/CheckBox/checkBox.spec.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/CheckBox/checkBox.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/CheckBox/index.spec.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/CheckBox/index.story.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/CheckBox/index.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/CheckBox/style.module.css", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/DateInput/__snapshots__/dateInput.spec.tsx.snap", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/DateInput/dateInput.spec.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/DateInput/dateInput.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/DateInput/index.spec.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/DateInput/index.story.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/DateInput/index.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/DateInput/style.module.css", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Dialog/DialogManager.spec.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Dialog/DialogManager.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Dialog/__snapshots__/dialog.spec.tsx.snap", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Dialog/dialog.spec.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Dialog/dialog.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Dialog/index.spec.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Dialog/index.story.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Dialog/index.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Dialog/style.module.css", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/DropDown/__snapshots__/header.spec.tsx.snap", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/DropDown/__snapshots__/wrapper.spec.tsx.snap", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/DropDown/contents.spec.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/DropDown/contents.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/DropDown/header.spec.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/DropDown/header.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/DropDown/index.spec.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/DropDown/index.story.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/DropDown/index.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/DropDown/style.module.css", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/DropDown/wrapper.spec.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/DropDown/wrapper.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Frame/__snapshots__/frame.spec.tsx.snap", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Frame/frame.spec.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Frame/frame.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Frame/index.spec.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Frame/index.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Headline/__snapshots__/headline.spec.tsx.snap", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Headline/headline.spec.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Headline/headline.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Headline/index.spec.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Headline/index.story.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Headline/index.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Headline/style.module.css", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Icon/__snapshots__/fontAwesomeIcon.spec.tsx.snap", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Icon/__snapshots__/icon.spec.tsx.snap", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Icon/fontAwesomeIcon.spec.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Icon/fontAwesomeIcon.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Icon/icon.spec.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Icon/icon.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Icon/iconDefaultProps.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Icon/index.spec.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Icon/index.story.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Icon/index.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Icon/mapper.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Icon/resourceIcon.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Icon/style.module.css", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/IconButton/__snapshots__/iconButton.spec.tsx.snap", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/IconButton/iconButton.spec.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/IconButton/iconButton.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/IconButton/index.spec.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/IconButton/index.story.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/IconButton/index.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/IconButton/style.module.css", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/IconButtonDropDown/__snapshots__/dropDownItem.spec.tsx.snap", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/IconButtonDropDown/__snapshots__/iconButtonDropDown.spec.tsx.snap", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/IconButtonDropDown/dropDownItem.spec.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/IconButtonDropDown/dropDownItem.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/IconButtonDropDown/iconButtonDropDown.spec.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/IconButtonDropDown/iconButtonDropDown.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/IconButtonDropDown/index.spec.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/IconButtonDropDown/index.story.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/IconButtonDropDown/index.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/IconButtonDropDown/style.module.css", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Label/__snapshots__/label.spec.tsx.snap", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Label/index.spec.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Label/index.story.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Label/index.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Label/label.spec.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Label/label.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Label/style.module.css", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/ListPreviewElement/index.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/ListPreviewElement/listPreviewElement.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/ListPreviewElement/style.module.css", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Logo/index.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Logo/resource/logo.svg", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Logo/style.module.css", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/MultiSelectBox/__snapshots__/multiSelectBox.spec.js.snap", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/MultiSelectBox/index.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/MultiSelectBox/index.spec.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/MultiSelectBox/index.story.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/MultiSelectBox/multiSelectBox.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/MultiSelectBox/multiSelectBox.spec.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/MultiSelectBox/style.module.css", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/MultiSelectBox_ListPreviewSortable/index.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/MultiSelectBox_ListPreviewSortable/multiSelectBox_ListPreviewSortable.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/MultiSelectBox_ListPreviewSortable_DraggableListPreviewElement/index.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/MultiSelectBox_ListPreviewSortable_DraggableListPreviewElement/multiSelectBox_ListPreviewSortable_DraggableListPreviewElement.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/MultiSelectBox_ListPreviewSortable_DraggableListPreviewElement/style.module.css", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/ResourceIcon/index.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/ResourceIcon/style.module.css", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SelectBox/__snapshots__/selectBox.spec.js.snap", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SelectBox/config.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SelectBox/index.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SelectBox/index.spec.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SelectBox/index.story.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SelectBox/selectBox.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SelectBox/selectBox.spec.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SelectBox/style.module.css", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SelectBox_CreateNew/index.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SelectBox_CreateNew/selectBox_CreateNew.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SelectBox_Header/index.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SelectBox_Header/selectBox_Header.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SelectBox_Header/style.module.css", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SelectBox_HeaderWithSearchInput/index.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SelectBox_HeaderWithSearchInput/selectBox_HeaderWithSearchInput.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SelectBox_HeaderWithSearchInput/style.module.css", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SelectBox_ListPreview/index.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SelectBox_ListPreview/selectBox_ListPreview.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SelectBox_ListPreviewFlat/index.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SelectBox_ListPreviewFlat/selectBox_ListPreviewFlat.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SelectBox_ListPreviewGrouped/index.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SelectBox_ListPreviewGrouped/selectBox_ListPreviewGrouped.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SelectBox_Option_MultiLineWithThumbnail/index.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SelectBox_Option_MultiLineWithThumbnail/selectBox_Option_MultiLineWithThumbnail.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SelectBox_Option_MultiLineWithThumbnail/style.module.css", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SelectBox_Option_SingleLine/__snapshots__/selectBox_Option_SingleLine.spec.js.snap", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SelectBox_Option_SingleLine/index.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SelectBox_Option_SingleLine/selectBox_Option_SingleLine.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SelectBox_Option_SingleLine/selectBox_Option_SingleLine.spec.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SideBar/__snapshots__/sideBar.spec.tsx.snap", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SideBar/index.spec.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SideBar/index.story.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SideBar/index.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SideBar/sideBar.spec.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SideBar/sideBar.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/SideBar/style.module.css", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Tabs/__snapshots__/panel.spec.tsx.snap", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Tabs/__snapshots__/tabs.spec.tsx.snap", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Tabs/index.spec.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Tabs/index.story.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Tabs/index.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Tabs/panel.index.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Tabs/panel.spec.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Tabs/panel.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Tabs/style.module.css", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Tabs/tabs.spec.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Tabs/tabs.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/TextArea/__snapshots__/textArea.spec.tsx.snap", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/TextArea/index.spec.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/TextArea/index.story.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/TextArea/index.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/TextArea/style.module.css", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/TextArea/textArea.spec.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/TextArea/textArea.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/TextInput/__snapshots__/textInput.spec.tsx.snap", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/TextInput/index.spec.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/TextInput/index.story.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/TextInput/index.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/TextInput/style.module.css", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/TextInput/textInput.spec.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/TextInput/textInput.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/ToggablePanel/__snapshots__/toggablePanel.spec.js.snap", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/ToggablePanel/index.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/ToggablePanel/index.spec.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/ToggablePanel/index.story.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/ToggablePanel/style.module.css", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/ToggablePanel/toggablePanel.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/ToggablePanel/toggablePanel.spec.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Tooltip/__snapshots__/tooltip.spec.tsx.snap", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Tooltip/index.spec.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Tooltip/index.story.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Tooltip/index.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Tooltip/style.module.css", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Tooltip/tooltip.spec.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Tooltip/tooltip.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Tree/__snapshots__/node.spec.js.snap", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Tree/__snapshots__/tree.spec.js.snap", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Tree/dragLayer.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Tree/dragLayer.module.css", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Tree/index.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Tree/index.spec.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Tree/index.story.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Tree/node.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Tree/node.module.css", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Tree/node.spec.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Tree/reactDndScrollzoneFork.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Tree/tree.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Tree/tree.module.css", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/Tree/tree.spec.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/_lib/focusNode.spec.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/_lib/focusNode.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/_lib/injectProps.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/_lib/injectProps.spec.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/_lib/storyUtils.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/_lib/storyUtils.spec.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/_lib/testUtils.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/_lib/testUtils.spec.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/enhanceWithClickOutside/index.tsx", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/global.d.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/identifiers.spec.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/identifiers.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/index.spec.js", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/index.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/reset.module.css", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/unstyled.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/src/utils-typescript.d.ts", "downloaded_repos/neos_neos-ui/packages/react-ui-components/tsconfig.json", "downloaded_repos/neos_neos-ui/packages/utils-helpers/package.json", "downloaded_repos/neos_neos-ui/packages/utils-helpers/src/cancelIdleCallback.spec.js", "downloaded_repos/neos_neos-ui/packages/utils-helpers/src/cancelIdleCallback.ts", "downloaded_repos/neos_neos-ui/packages/utils-helpers/src/decodeHtml.spec.js", "downloaded_repos/neos_neos-ui/packages/utils-helpers/src/decodeHtml.ts", "downloaded_repos/neos_neos-ui/packages/utils-helpers/src/getVersion.ts", "downloaded_repos/neos_neos-ui/packages/utils-helpers/src/index.spec.js", "downloaded_repos/neos_neos-ui/packages/utils-helpers/src/index.ts", "downloaded_repos/neos_neos-ui/packages/utils-helpers/src/isEmail.spec.js", "downloaded_repos/neos_neos-ui/packages/utils-helpers/src/isEmail.ts", "downloaded_repos/neos_neos-ui/packages/utils-helpers/src/isEqualSet.ts", "downloaded_repos/neos_neos-ui/packages/utils-helpers/src/isNil.spec.js", "downloaded_repos/neos_neos-ui/packages/utils-helpers/src/isNil.ts", "downloaded_repos/neos_neos-ui/packages/utils-helpers/src/isThenable.spec.js", "downloaded_repos/neos_neos-ui/packages/utils-helpers/src/isThenable.ts", "downloaded_repos/neos_neos-ui/packages/utils-helpers/src/isUri.spec.js", "downloaded_repos/neos_neos-ui/packages/utils-helpers/src/isUri.ts", "downloaded_repos/neos_neos-ui/packages/utils-helpers/src/requestIdleCallback.spec.js", "downloaded_repos/neos_neos-ui/packages/utils-helpers/src/requestIdleCallback.ts", "downloaded_repos/neos_neos-ui/packages/utils-helpers/src/stripTags.spec.js", "downloaded_repos/neos_neos-ui/packages/utils-helpers/src/stripTags.ts", "downloaded_repos/neos_neos-ui/packages/utils-helpers/src/svgToDataUri.ts", "downloaded_repos/neos_neos-ui/packages/utils-helpers/src/urlWithParams.spec.js", "downloaded_repos/neos_neos-ui/packages/utils-helpers/src/urlWithParams.ts", "downloaded_repos/neos_neos-ui/packages/utils-logger/package.json", "downloaded_repos/neos_neos-ui/packages/utils-logger/src/index.spec.js", "downloaded_repos/neos_neos-ui/packages/utils-logger/src/index.ts", "downloaded_repos/neos_neos-ui/packages/utils-redux/package.json", "downloaded_repos/neos_neos-ui/packages/utils-redux/src/__snapshots__/handleActions.spec.js.snap", "downloaded_repos/neos_neos-ui/packages/utils-redux/src/handleActions.spec.js", "downloaded_repos/neos_neos-ui/packages/utils-redux/src/handleActions.ts", "downloaded_repos/neos_neos-ui/packages/utils-redux/src/index.spec.js", "downloaded_repos/neos_neos-ui/packages/utils-redux/src/index.ts", "downloaded_repos/neos_neos-ui/patches/isemail-npm-3.2.0-browserified.patch", "downloaded_repos/neos_neos-ui/patches/react-codemirror2-npm-7.2.1-browserified.patch", "downloaded_repos/neos_neos-ui/phpstan.neon.dist", "downloaded_repos/neos_neos-ui/tsconfig.json", "downloaded_repos/neos_neos-ui/yarn.lock"], "skipped": [{"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@adobe-css-tools-npm-4.4.0-3e89ecd033-1f08fb49bf.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ampproject-remapping-npm-2.1.2-d1536e36d6-e023f92cdd.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ampproject-remapping-npm-2.3.0-559c14eee4-d3ad7b89d9.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-code-frame-npm-7.18.6-25229a7e34-195e2be317.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-code-frame-npm-7.24.7-315a600a58-830e62cd38.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-compat-data-npm-7.20.1-4eea2d1c20-989b9b7a6f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-compat-data-npm-7.25.4-213b9c835f-b12a91d27c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-core-npm-7.20.2-7fb00344fc-98faaaef26.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-core-npm-7.25.2-341930f809-9a1ef604a7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-eslint-parser-npm-7.19.1-8f22f07e44-6d5360f62f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-generator-npm-7.20.4-97edf6df16-967b59f18e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-generator-npm-7.25.6-3bdca6c59f-b55975cd66.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-annotate-as-pure-npm-7.18.6-36e25293d8-88ccd15ced.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-annotate-as-pure-npm-7.24.7-537c5e8bf3-6178566099.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-builder-binary-assignment-operator-visitor-npm-7.24.7-1653e5773a-71a6158a9f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-compilation-targets-npm-7.20.0-f842a8700a-bc183f2109.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-compilation-targets-npm-7.25.2-27e0232144-aed33c5496.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-create-class-features-plugin-npm-7.25.4-125644448f-4544ebda45.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-create-regexp-features-plugin-npm-7.25.2-35b05e1e79-df55fdc6a1.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-define-polyfill-provider-npm-0.3.3-8c896ae707-8e3fe75513.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-define-polyfill-provider-npm-0.4.4-7dfaee32d2-2453cdd79f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-define-polyfill-provider-npm-0.5.0-f9749c8844-d24626b819.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-define-polyfill-provider-npm-0.6.2-554cbf22ae-2bba965ea9.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-environment-visitor-npm-7.18.9-9f5b3635a1-b25101f616.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-function-name-npm-7.19.0-5bcf55236f-eac1f5db42.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-hoist-variables-npm-7.18.6-6eb061f405-fd9c35bb43.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-member-expression-to-functions-npm-7.24.8-6042e98e38-bf923d05d8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-module-imports-npm-7.18.6-1031faa864-f393f8a3b3.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-module-imports-npm-7.24.7-f60e66adbf-8ac15d96d2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-module-transforms-npm-7.20.2-9a4905d6a2-33a60ca115.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-module-transforms-npm-7.25.2-2c8d511580-282d4e3308.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-optimise-call-expression-npm-7.24.7-59b5fb050d-280654eaf9.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-plugin-utils-npm-7.20.2-63f605bb73-f6cae53b7f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-plugin-utils-npm-7.24.8-a288f101a7-73b1a83ba8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-remap-async-to-generator-npm-7.25.0-0f64f09501-47f3065e43.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-replace-supers-npm-7.25.0-7aaa2ff595-f669fc2487.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-simple-access-npm-7.20.2-842ec98fbb-ad1e96ee2e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-simple-access-npm-7.24.7-beddd00b0e-ddbf55f9de.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-skip-transparent-expression-wrappers-npm-7.24.7-f573fe40ee-11b28fe534.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-split-export-declaration-npm-7.18.6-53ebf8ad4c-c6d3dede53.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-string-parser-npm-7.19.4-0db110dc3a-b2f8a3920b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-string-parser-npm-7.24.8-133b2e71e1-39b03c5119.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-validator-identifier-npm-7.19.1-d84f19e1dc-0eca5e86a7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-validator-identifier-npm-7.24.7-748889c8d2-6799ab117c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-validator-option-npm-7.18.6-cc7d1a3315-f9cc6eb7cc.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-validator-option-npm-7.24.8-e093ef5016-a52442dfa7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helper-wrap-function-npm-7.25.0-c85147a474-0095b47417.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helpers-npm-7.20.1-3ab5411f3a-be35f78666.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-helpers-npm-7.25.6-6722375514-5a548999db.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-highlight-npm-7.18.6-9d35ad2e27-92d8ee6154.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-highlight-npm-7.24.7-d792bd8d9f-5cd3a89f14.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-parser-npm-7.20.3-dba5e30700-33bcdb45de.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-parser-npm-7.25.6-3cb198940b-85b237ded0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-bugfix-firefox-class-in-computed-class-key-npm-7.25.3-d2e8ec6012-d3dba60f36.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-bugfix-safari-class-field-initializer-scope-npm-7.25.0-96be020ed4-fd56d1e643.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-bugfix-safari-id-destructuring-collision-in-function-expression-npm-7.25.0-cd338f5f19-13ed301b10.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-bugfix-v8-spread-parameters-in-optional-chaining-npm-7.24.7-a96e8cc868-07b92878ac.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-bugfix-v8-static-class-fields-redefine-readonly-npm-7.25.0-0974fd41ef-c8d08b8d6c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-proposal-async-generator-functions-npm-7.20.7-14b8ab2230-111109ee11.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-proposal-class-properties-npm-7.18.6-5f5c2d730f-49a78a2773.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-proposal-decorators-npm-7.24.7-b145211162-75aa5ff553.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-proposal-object-rest-spread-npm-7.20.7-0bc581aa09-1329db1700.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-proposal-private-methods-npm-7.18.6-55729207b7-22d8502ee9.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-proposal-private-property-in-object-npm-7.21.0-placeholder-for-preset-env.2-eb70026c88-d97745d098.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-syntax-async-generators-npm-7.8.4-d10cf993c9-7ed1c1d9b9.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-syntax-bigint-npm-7.8.3-b05d971e6c-3a10849d83.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-syntax-class-properties-npm-7.12.13-002ee9d930-24f34b196d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-syntax-class-static-block-npm-7.14.5-7bdd0ff1b3-3e80814b5b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-syntax-decorators-npm-7.24.7-fcb66bfb39-dc303bcc1f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-syntax-dynamic-import-npm-7.8.3-fb9ff5634a-ce307af83c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-syntax-export-namespace-from-npm-7.8.3-1747201aa9-85740478be.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-syntax-flow-npm-7.24.7-8f0fd978b1-43b78b5fcd.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-syntax-import-assertions-npm-7.25.6-f61334bd30-b3b251ace9.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-syntax-import-attributes-npm-7.25.6-21fbaebb12-3b0928e73e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-syntax-import-meta-npm-7.10.4-4a0a0158bc-166ac1125d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-syntax-json-strings-npm-7.8.3-6dc7848179-bf5aea1f31.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-syntax-jsx-npm-7.18.6-3e378d5f11-6d37ea9729.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-syntax-jsx-npm-7.24.7-8f9596c5ff-7a5ca629d8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-syntax-logical-assignment-operators-npm-7.10.4-72ae00fdf6-aff3357703.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-syntax-nullish-coalescing-operator-npm-7.8.3-8a723173b5-87aca49189.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-syntax-numeric-separator-npm-7.10.4-81444be605-01ec5547bd.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-syntax-object-rest-spread-npm-7.8.3-60bd05b6ae-fddcf581a5.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-syntax-optional-catch-binding-npm-7.8.3-ce337427d8-910d90e72b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-syntax-optional-chaining-npm-7.8.3-f3f3c79579-eef94d53a1.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-syntax-private-property-in-object-npm-7.14.5-ee837fdbb2-b317174783.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-syntax-top-level-await-npm-7.14.5-60a0a2e83b-bbd1a56b09.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-syntax-typescript-npm-7.20.0-21fa6329fe-6189c0b5c3.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-syntax-unicode-sets-regex-npm-7.18.6-b618a36bfd-a651d700fe.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-arrow-functions-npm-7.24.7-dc9654ba4f-707c209b53.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-async-generator-functions-npm-7.25.4-5ba1962e15-4235444735.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-async-to-generator-npm-7.24.7-335cbe94e0-13704fb3b8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-block-scoped-functions-npm-7.24.7-3bde68de42-249cdcbff4.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-block-scoping-npm-7.25.0-3cb78e0e8f-b1a8f932f6.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-class-properties-npm-7.25.4-d9eb8b633d-b73f7d9686.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-class-static-block-npm-7.24.7-2ab97b4caf-3240492635.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-classes-npm-7.25.4-4c8c2f428f-0bf20e46ee.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-computed-properties-npm-7.24.7-707065a998-0cf8c1b1e4.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-destructuring-npm-7.24.8-d05798f0dc-0b4bd3d608.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-dotall-regex-npm-7.24.7-1e4ebcbb6c-67b10fc6ab.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-duplicate-keys-npm-7.24.7-c999e46d25-d1da2ff85e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-duplicate-named-capturing-groups-regex-npm-7.25.0-4d6aab7c02-608d6b0e77.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-dynamic-import-npm-7.24.7-e15a724c6c-776509ff62.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-exponentiation-operator-npm-7.24.7-1bd197e640-23c84a23eb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-export-namespace-from-npm-7.24.7-2e43f5c58e-3bd3a10038.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-flow-strip-types-npm-7.25.2-a3e93a84d0-9f7b96cbd3.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-for-of-npm-7.24.7-e85b1239ae-a53b42dc93.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-function-name-npm-7.25.1-abd6b587c7-743f3ea03b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-json-strings-npm-7.24.7-17858f14f3-88874d0b7a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-literals-npm-7.25.2-7f2fb8bbc9-70c9bb40e3.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-logical-assignment-operators-npm-7.24.7-8d08c296f2-3367ce0be2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-member-expression-literals-npm-7.24.7-566bef1c80-2720c57aa3.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-modules-amd-npm-7.24.7-7b9b7c2d4b-f1dd0fb2f4.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-modules-commonjs-npm-7.24.8-4d32ab4533-a4cf95b163.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-modules-systemjs-npm-7.25.0-838c85ab7f-fe673bec08.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-modules-umd-npm-7.24.7-46b61a2a8a-9ff1c46489.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-named-capturing-groups-regex-npm-7.24.7-68b2f48b40-f1c6c7b5d6.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-new-target-npm-7.24.7-cb95d780d4-3cb94cd107.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-nullish-coalescing-operator-npm-7.24.7-91cfb40042-4a92213564.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-numeric-separator-npm-7.24.7-70370e84d8-561b5f1d08.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-object-rest-spread-npm-7.24.7-c5bcb6d635-169d257b98.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-object-super-npm-7.24.7-8b4ef26bc1-f71e607a83.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-optional-catch-binding-npm-7.24.7-4dc481e34f-7229f3a5a4.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-optional-chaining-npm-7.24.8-3f07208b22-45e55e3a2f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-parameters-npm-7.24.7-d18b1cfc71-ab534b03ac.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-private-methods-npm-7.25.4-0bfe911738-cb1dabfc03.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-private-property-in-object-npm-7.24.7-96a62af9e6-8cee947309.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-property-literals-npm-7.24.7-d2b997a7b0-9aeefc3aab.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-react-display-name-npm-7.18.6-dad446a24b-51c087ab9e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-react-display-name-npm-7.24.7-879a53458d-a05bf83bf5.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-react-jsx-development-npm-7.18.6-1b4b00c77d-ec9fa65db6.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-react-jsx-development-npm-7.24.7-74c0352290-653d32ea5a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-react-jsx-npm-7.19.0-a26bc7e3f9-d7d6f0b8f2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-react-jsx-npm-7.25.2-1cc057ac5e-44fbde0463.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-react-pure-annotations-npm-7.18.6-ed47d931a2-97c4873d40.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-react-pure-annotations-npm-7.24.7-ce37002788-d859ada3cb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-regenerator-npm-7.24.7-7ba719f821-20c6c3fb6f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-reserved-words-npm-7.24.7-b2adfbf85a-3d5876954d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-runtime-npm-7.19.6-03c4f8c199-ef93efbcbb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-runtime-npm-7.23.3-c52746b76a-e8f8a49d69.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-shorthand-properties-npm-7.24.7-bc90ee60de-7b52424581.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-spread-npm-7.24.7-0009f44d3c-4c4254c8b9.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-sticky-regex-npm-7.24.7-98cd1af8a9-118fc7a7eb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-template-literals-npm-7.24.7-0ecb9f0cf5-ad44e5826f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-typeof-symbol-npm-7.24.8-105494a49d-8663a8e734.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-unicode-escapes-npm-7.24.7-39ca160006-4af0a193e1.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-unicode-property-regex-npm-7.24.7-6d5a35d7ce-aae13350c5.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-unicode-regex-npm-7.24.7-c5a44da0ea-1cb4e70678.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-plugin-transform-unicode-sets-regex-npm-7.25.4-ce2960540d-6d1a7e9fdd.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-preset-env-npm-7.25.4-ec56d62db2-752be43f0b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-preset-flow-npm-7.24.7-faecfd1682-4caca02a6e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-preset-modules-npm-0.1.6-no-external-plugins-0ae0b52ff3-4855e799bc.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-preset-react-npm-7.18.6-d1cea43c32-540d9cf0a0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-preset-react-npm-7.24.7-eaa2600adf-76d0365b6b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-regjsgen-npm-0.8.0-b0fbdbf644-89c338fee7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-runtime-corejs3-npm-7.20.1-41d80197fc-bac1463304.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-runtime-npm-7.20.1-8f9256f2ed-00567a333d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-runtime-npm-7.25.6-6725f0979a-ee1a69d3ac.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-template-npm-7.18.10-b6d6fdbaf8-93a6aa094a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-template-npm-7.25.0-2c6ddcb43a-3f2db56871.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-traverse-npm-7.20.1-569a25513a-6696176d57.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-traverse-npm-7.25.6-1b9e2a314c-11ee47269a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-types-npm-7.20.2-0e6581f12d-57e76e5f21.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@babel-types-npm-7.25.6-98df73a2ca-9b2f84ff3f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@bcoe-v8-coverage-npm-0.2.3-9e27b3c57e-850f930553.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-adapter-ckfinder-npm-44.0.0-06e6bdb665-a7152c7f11.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-alignment-npm-44.0.0-1466eb6b78-d8ae68918c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-autoformat-npm-44.0.0-af7fbce98a-9b531d4d20.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-autosave-npm-44.0.0-018978c1c2-aef6b9b483.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-basic-styles-npm-44.0.0-5fa1ba8b7f-5d8de19262.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-block-quote-npm-44.0.0-e923f8a2bd-5359e78317.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-bookmark-npm-44.0.0-0cbb5555b0-8946df8297.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-ckbox-npm-44.0.0-398eb95df8-6dcfe9936a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-ckfinder-npm-44.0.0-0ca7a6bf52-098a117ced.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-clipboard-npm-44.0.0-bc736f7695-519da043c8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-cloud-services-npm-44.0.0-92be699ee0-95554228d0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-code-block-npm-44.0.0-3ab798922b-b294468a98.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-core-npm-44.0.0-a277165cd5-f995ef7713.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-easy-image-npm-44.0.0-cd84bf737e-3ca8ec0871.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-editor-balloon-npm-44.0.0-e1b8642ccd-6031e44b01.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-editor-classic-npm-44.0.0-288eefc34e-3b9fc1b6eb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-editor-decoupled-npm-44.0.0-cfae0546fd-43eb4565eb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-editor-inline-npm-44.0.0-646553a1ee-de225a0170.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-editor-multi-root-npm-44.0.0-ccf88420cb-29acd69963.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-engine-npm-44.0.0-b1d8052981-dc390f3bb2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-enter-npm-44.0.0-0092e6b801-b1d74d438d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-essentials-npm-44.0.0-fefced513a-5474a35743.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-find-and-replace-npm-44.0.0-0ebd3fe9ca-00695b6884.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-font-npm-44.0.0-eade3ec6dc-012071bf3c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-heading-npm-44.0.0-f0f3fe8235-df5b30520d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-highlight-npm-44.0.0-57d1e7a6c2-837acbf7ac.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-horizontal-line-npm-44.0.0-f622256307-579b119bb7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-html-embed-npm-44.0.0-8fdc5a4cf7-2df621b0ab.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-html-support-npm-44.0.0-dcff546e9f-ed4a5cade2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-image-npm-44.0.0-b2fbf99cd5-b6bd38314e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-indent-npm-44.0.0-66802edb3b-8509afec86.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-language-npm-44.0.0-a324fec302-c38d5003b2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-link-npm-44.0.0-f5931795e4-7442bb38b2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-list-npm-44.0.0-74cefda3c5-8884017ef4.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-markdown-gfm-npm-44.0.0-50d395eb14-a9942258b8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-media-embed-npm-44.0.0-f83276917d-1caaf1a482.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-mention-npm-44.0.0-300e461066-83b7563002.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-minimap-npm-44.0.0-d49243d9e8-36a328dc55.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-page-break-npm-44.0.0-6de4476717-1e6ccdaf2a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-paragraph-npm-44.0.0-95e655df0b-7ba641e214.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-paste-from-office-npm-44.0.0-cf9c16e8c6-2b7e59cd86.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-remove-format-npm-44.0.0-6f3038ce15-e1e13a5812.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-restricted-editing-npm-44.0.0-d7763f8176-9113642f36.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-select-all-npm-44.0.0-954ae702fb-74785ddd6c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-show-blocks-npm-44.0.0-03c59fd46e-7f9554fa34.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-source-editing-npm-44.0.0-e8706fd42a-d9706147e7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-special-characters-npm-44.0.0-8fc2af7927-8c6da91d2a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-style-npm-44.0.0-482c093e94-f0e48b9ed6.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-table-npm-44.0.0-7ee559783d-c9fcfc361b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-theme-lark-npm-44.0.0-a19a8fe4ef-881efb5572.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-typing-npm-44.0.0-b7c12d84ce-9bc93cadb1.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-ui-npm-44.0.0-c25f3d8204-08dac36ecb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-undo-npm-44.0.0-8ba315bf1c-85701d2f56.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-upload-npm-44.0.0-636a121e5f-a55fa3dd27.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-utils-npm-44.0.0-f570b179e7-65282690fb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-watchdog-npm-44.0.0-02a6a95e5c-d9ef810fcd.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-widget-npm-44.0.0-88f2e97636-8851dc0612.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@ckeditor-ckeditor5-word-count-npm-44.0.0-2f1f41c158-12f01f06ea.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@devexpress-bin-v8-flags-filter-npm-1.3.0-81ef659541-7787f9ca8d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@devexpress-callsite-record-npm-4.1.7-5b508aea95-91c7b0f078.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@electron-asar-npm-3.2.13-f1c5eb226a-1af6a1aac1.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@esbuild-darwin-arm64-npm-0.17.8-a4f4f1b6d9-8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@esbuild-linux-x64-npm-0.17.8-56ca2f1293-8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@eslint-eslintrc-npm-1.3.3-9e3a462140-f03e9d6727.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@fortawesome-fontawesome-common-types-npm-6.5.2-3062ecb730-8164f3e166.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@fortawesome-fontawesome-svg-core-npm-6.5.2-43d83ee9e6-f0c2a08000.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@fortawesome-free-brands-svg-icons-npm-6.5.2-c0e6a51bfc-f037c15978.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@fortawesome-free-regular-svg-icons-npm-6.5.2-05536928ce-e5a6cf019e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@fortawesome-free-solid-svg-icons-npm-6.5.2-24587ff7e2-f23964434c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@fortawesome-react-fontawesome-npm-0.2.2-e1863961b2-e4bed35bfb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@friendsofreactjs-react-css-themr-npm-4.2.1-3bac33f833-df2a7b913b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@gar-promisify-npm-1.1.3-ac1a325862-4059f790e2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@humanwhocodes-config-array-npm-0.11.7-fa83ecd27f-cf506dc45d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@humanwhocodes-module-importer-npm-1.0.1-9d07ed2e4a-0fd22007db.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@humanwhocodes-object-schema-npm-1.2.1-eb622b5d0e-a824a1ec31.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@istanbuljs-load-nyc-config-npm-1.1.0-42d17c9cb1-d578da5e2e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@istanbuljs-schema-npm-0.1.3-466bd3eaaa-5282759d96.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@jest-console-npm-27.5.1-d2bbc2b25a-7cb20f06a3.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@jest-core-npm-27.5.1-b2d79816b3-904a94ad8f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@jest-environment-npm-27.5.1-375c740ca0-2a9e18c35a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@jest-fake-timers-npm-27.5.1-d5ae31aa49-02a0561ed2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@jest-globals-npm-27.5.1-b4ce1a8d04-087f97047e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@jest-reporters-npm-27.5.1-a792fda73f-faba5eafb8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@jest-source-map-npm-27.5.1-82cd2ed5c0-4fb1e743b6.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@jest-test-result-npm-27.5.1-76df324af3-338f7c509d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@jest-test-sequencer-npm-27.5.1-b9bc39f9fc-f21f9c8bb7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@jest-transform-npm-27.5.1-2c1cc049e5-a22079121a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@jest-types-npm-27.5.1-c589ce1890-d1f43cc946.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@jridgewell-gen-mapping-npm-0.3.2-c64eeb4a4e-1832707a1c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@jridgewell-gen-mapping-npm-0.3.5-d8b85ebeaf-ff7a1764eb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@jridgewell-resolve-uri-npm-3.0.5-bb57d127db-1ee652b693.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@jridgewell-resolve-uri-npm-3.1.0-6ff2351e61-b5ceaaf9a1.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@jridgewell-resolve-uri-npm-3.1.2-5bc4245992-83b85f72c5.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@jridgewell-set-array-npm-1.1.2-45b82d7fb6-69a84d5980.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@jridgewell-set-array-npm-1.2.1-2312928209-832e513a85.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@jridgewell-sourcemap-codec-npm-1.4.11-68fac44d63-3b2afaf840.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@jridgewell-sourcemap-codec-npm-1.4.14-f5f0630788-61100637b6.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@jridgewell-sourcemap-codec-npm-1.5.0-dfd9126d71-05df4f2538.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@jridgewell-trace-mapping-npm-0.3.17-57578fd48c-9d703b859c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@jridgewell-trace-mapping-npm-0.3.25-c076fd2279-9d3c40d225.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@jridgewell-trace-mapping-npm-0.3.4-d0db906c7f-ab8bce84bb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@mixmark-io-domino-npm-2.2.0-4e1d58551b-547af4a8f7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@neos-project-brand-npm-1.3.0-d5d0ed8aed-c8345aa903.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@neos-project-eslint-config-neos-npm-2.6.1-a79f090f1c-3a81138089.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@nicolo-ribaudo-eslint-scope-5-internals-npm-5.1.1-v1-87df86be4b-f2e3b2d6a6.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@nodelib-fs.scandir-npm-2.1.3-92e0b8f0d5-0054efbba1.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@nodelib-fs.scandir-npm-2.1.5-89c67370dd-a970d595bd.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@nodelib-fs.stat-npm-2.0.3-be9eaa5ef7-d3612efcee.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@nodelib-fs.stat-npm-2.0.5-01f4dd3030-012480b5ca.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@nodelib-fs.walk-npm-1.2.4-1ae6c97d00-a971d1dcc1.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@nodelib-fs.walk-npm-1.2.8-b4a89da548-190c643f15.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@npmcli-fs-npm-2.1.0-3b106d08bc-6ec6d678af.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@npmcli-move-file-npm-2.0.0-d8bd1d35d2-1388777b50.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@react-dnd-asap-npm-4.0.0-f6b65c5b5b-4c37ef5c4e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@react-dnd-invariant-npm-2.0.0-e055f1025b-ef1e989920.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@react-dnd-shallowequal-npm-2.0.0-2a10dca275-b5bbdc795d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@sindresorhus-is-npm-4.6.0-7cad05c55e-83839f13da.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@sinonjs-commons-npm-1.8.5-d88c5da4b6-74cb49e2f2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@sinonjs-fake-timers-npm-8.1.0-95c51c96db-09b5a158ce.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@stylelint-postcss-css-in-js-npm-0.37.2-8a19c1b9be-cc9b5d1bd9.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@stylelint-postcss-markdown-npm-0.36.1-b68ba6782d-5e6b802b12.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@szmarczak-http-timer-npm-4.0.6-6ace00d82d-c29df3bcec.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@tanem-svg-injector-npm-8.2.5-a72c826d71-03387c5b2c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@tootallnate-once-npm-1.1.2-0517220057-e1fb1bbbc1.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@tootallnate-once-npm-2.0.0-e36cf4f140-ad87447820.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-babel__core-npm-7.1.20-90b6a59ca7-a09c4f0456.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-babel__generator-npm-7.6.4-03e776f956-20effbbb5f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-babel__template-npm-7.4.1-fe1db49e53-649fe8b42c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-babel__traverse-npm-7.18.2-17d99e224e-05972775e2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-cacheable-request-npm-6.0.2-894b6992d5-667d25808d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-cheerio-npm-0.22.31-63e46da0e3-8d73d22fdd.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-classnames-npm-2.3.0-9634c349d8-e1bd6de60f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-d3-array-npm-3.0.4-156dd0b52b-b0e398365f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-d3-color-npm-3.1.0-e4a43fff88-b1856f17d6.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-d3-ease-npm-3.0.0-b342ad1e9f-1be7c99364.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-d3-interpolate-npm-3.0.1-6fb3cc4131-29ce472968.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-d3-path-npm-3.0.0-73db3fee36-af7f45ea91.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-d3-scale-npm-4.0.3-f929b25483-76684da851.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-d3-shape-npm-3.1.1-542f46cb78-8f1762ecde.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-d3-time-npm-3.0.0-9b0c5b0218-e76adb056d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-d3-timer-npm-3.0.0-b8c95909c6-1ec86b3808.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-debug-npm-4.1.7-f54c81f8db-0a7b89d8ed.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-enzyme-npm-3.10.12-eae4c96ec1-356e914256.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-estree-npm-0.0.46-173c5af455-620f7549c8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-events-npm-3.0.0-2eee0ea973-9a424c2da2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-glob-npm-7.1.1-10bfc8b49b-9fb96d004c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-glob-npm-7.2.0-772334bf9a-6ae717fedf.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-glob-npm-8.1.0-bdb9d0520c-9101f3a906.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-graceful-fs-npm-4.1.5-91d62e1050-d076bb61f4.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-hast-npm-2.3.4-7249cc0ece-fff47998f4.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-he-npm-1.1.2-93648696f3-71e9786c35.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-hoist-non-react-statics-npm-3.3.1-c0081332b2-2c0778570d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-http-cache-semantics-npm-4.0.1-90863c7a3e-1048aacf62.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-istanbul-lib-coverage-npm-2.0.4-734954bb56-a25d7589ee.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-istanbul-lib-report-npm-3.0.0-50de3e6b3b-656398b62d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-istanbul-reports-npm-3.0.1-770e825002-f1ad54bc68.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-jest-npm-23.3.14-d793744be4-0f8ec7c7ef.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-json-buffer-npm-3.0.0-9f2fe89eaa-6b0a371dd6.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-json-schema-npm-7.0.11-79462ae5ca-527bddfe62.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-json5-npm-0.0.29-f63a7916bd-e60b153664.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-keyv-npm-3.1.4-a8082ea56b-e009a2bfb5.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-lodash-npm-4.14.182-1073aac722-7dd137aa9d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-lodash.assignin-npm-4.2.7-a862269f30-feea417f71.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-lodash.defaultsdeep-npm-4.6.7-e562446ba1-74e8cdab19.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-lodash.isequal-npm-4.5.6-d5cbcdc0e2-0f06598940.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-lodash.mapvalues-npm-4.6.7-0a3913b26a-f28db2c062.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-lodash.merge-npm-4.6.7-5289996784-94cd6fb8e6.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-lodash.omit-npm-4.5.7-420295089b-e30600de51.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-mdast-npm-3.0.3-3eaccff045-5318624af8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-minimatch-npm-3.0.3-c8b0625f84-b80259d55b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-minimatch-npm-5.1.2-aab9c394d3-0391a28286.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-minimist-npm-1.2.1-dc316d808d-02631cdd79.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-ms-npm-0.7.31-ea3b89342b-daadd354ae.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-node-npm-13.1.0-1161fa2d98-8c63b8816a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-node-npm-18.19.50-1d7a03097e-73bdd2b46f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-node-npm-20.16.5-fd6c27be3b-f38b7bd8c4.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-normalize-package-data-npm-2.4.0-ed928aaaa8-fd22ba86a1.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-parse-json-npm-4.0.0-298522afa6-fd6bce2b67.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-prettier-npm-2.7.1-d46ada27e6-5e3f58e229.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-prop-types-npm-15.7.5-2aa48aa177-5b43b8b154.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-react-dom-npm-16.9.15-d8d06a8f66-37e3518a46.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-react-fontawesome-npm-1.6.5-f89e6a919f-655d5eaa01.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-react-npm-16.14.25-356b2d635b-3a06cf26f2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-react-portal-npm-4.0.4-7e27a1ab22-29e025bdcd.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-responselike-npm-1.0.0-85dd08af42-e99fc7cc62.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-scheduler-npm-0.16.2-ba3a7d8c68-b6b4dcfeae.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-semver-npm-7.3.13-56212b60da-00c0724d54.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-stack-utils-npm-2.0.1-867718ab70-205fdbe332.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-unist-npm-2.0.3-4b26dedfde-4427306b09.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-yargs-npm-16.0.4-7aaef7d6c8-caa21d2c95.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@types-yargs-parser-npm-21.0.0-c8a3b32c52-b2f4c8d12a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@typescript-eslint-eslint-plugin-npm-5.44.0-0615085a30-88784e77e8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@typescript-eslint-parser-npm-5.44.0-027abf9212-2d09a1a154.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@typescript-eslint-scope-manager-npm-5.43.0-49d608d443-e594c7a32c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@typescript-eslint-scope-manager-npm-5.44.0-954881377d-4cfe4b55eb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@typescript-eslint-type-utils-npm-5.44.0-71227b7097-4c7b594f8a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@typescript-eslint-types-npm-5.43.0-a0ec9967c0-fc5e5431c3.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@typescript-eslint-types-npm-5.44.0-5f41ae3b77-ced7d32abe.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@typescript-eslint-typescript-estree-npm-5.43.0-6d17802054-3479f9413d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@typescript-eslint-typescript-estree-npm-5.44.0-efec736209-7587311084.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@typescript-eslint-utils-npm-5.43.0-594c84859f-4c6b383b51.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@typescript-eslint-utils-npm-5.44.0-d7345b74a3-bc5bb28e41.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@typescript-eslint-visitor-keys-npm-5.43.0-44f5ae1b0c-4820679e50.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@typescript-eslint-visitor-keys-npm-5.44.0-b3734ecb0a-a012c88820.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@wdio-config-npm-7.33.0-b120ca60c2-458a84543d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@wdio-logger-npm-7.26.0-aebae27d1a-aad60e5a17.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@wdio-protocols-npm-7.27.0-451488b629-4e6b738b42.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@wdio-types-npm-7.33.0-33476364bf-6fdc6272bb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/@wdio-utils-npm-7.33.0-8305266b0a-3b81a41429.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/abab-npm-2.0.3-278ae51229-d3e4e4ff69.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/abab-npm-2.0.6-2662fba7f0-6ffc1af4ff.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/abbrev-npm-1.1.1-3659247eab-a4a97ec07d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/acorn-globals-npm-4.3.4-0dae130248-c31bfde102.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/acorn-globals-npm-6.0.0-acbec28ad5-72d95e5b5e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/acorn-hammerhead-npm-0.5.0-a9d38254b5-6bb2564fcd.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/acorn-hammerhead-npm-0.6.2-273516ed8d-de60dcdcb8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/acorn-jsx-npm-5.1.0-681001b4ff-619cf42528.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/acorn-jsx-npm-5.3.2-d7594599ea-c3d3b2a89c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/acorn-npm-6.4.0-1f1a150ab0-eca6eed23a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/acorn-npm-7.4.1-f450b4646c-1860f23c21.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/acorn-npm-8.8.1-20e4aea981-4079b67283.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/acorn-walk-npm-6.2.0-8b629285e9-ea241a5d96.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/acorn-walk-npm-7.2.0-5f8b515308-9252158a79.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/address-npm-2.0.3-cde39e6a0a-f2b11e6f94.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/adm-zip-npm-0.4.16-8a2fd28feb-5ea46664d8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/agent-base-npm-6.0.2-428f325a93-f52b6872cc.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/agentkeepalive-npm-4.2.1-b86a9fb343-39cb49ed8c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/aggregate-error-npm-3.0.1-46f220b212-1f922d00cc.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/airbnb-prop-types-npm-2.15.0-806e0e3df5-c7defd4ed3.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/ajv-npm-6.12.6-4b5105e2b2-874972efe5.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/amator-npm-1.1.0-5968db4593-221bbe02be.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/amdefine-npm-1.0.1-40b219807a-9d4e15b946.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/ansi-escapes-npm-3.2.0-a9d573100e-0f94695b67.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/ansi-escapes-npm-4.3.2-3ad173702f-93111c4218.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/ansi-regex-npm-3.0.0-be0b845911-2ad11c416f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/ansi-regex-npm-4.1.1-af0a582bb9-b1a6ee44cb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/ansi-regex-npm-5.0.1-c963a48615-2aa4bb54ca.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/ansi-styles-npm-3.2.1-8cb8107983-d85ade01c1.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/ansi-styles-npm-4.3.0-245c7d42c7-513b44c3b2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/ansi-styles-npm-5.2.0-72fc7003e3-d7f4e97ce0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/anymatch-npm-3.1.3-bc81d103b1-3e044fd6d1.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/aproba-npm-2.0.0-8716bcfde6-5615cadcfb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/are-we-there-yet-npm-3.0.0-1391430190-348edfdd93.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/argparse-npm-1.0.10-528934e59d-7ca6e45583.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/argparse-npm-2.0.1-faff7999e6-83644b5649.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/aria-query-npm-4.2.2-e0c4f1a309-38401a9a40.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/array-equal-npm-1.0.0-c41a42b83b-3f68045806.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/array-filter-npm-1.0.0-2d57caf5c8-467054291f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/array-find-npm-1.0.0-e6a7eed080-6588ebfd15.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/array-includes-npm-3.1.6-d0ff9d248b-f22f8cd8ba.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/array-map-npm-0.0.0-45992ed41e-30d73fdc99.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/array-reduce-npm-0.0.0-d774206485-d622632527.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/array-union-npm-1.0.2-cc61ee268f-82cec6421b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/array-union-npm-2.1.0-4e4852b221-5bee12395c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/array-uniq-npm-1.0.3-e7f5d6f3a1-1625f06b09.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/array.prototype.find-npm-2.1.0-e876373250-06129744cf.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/array.prototype.flat-npm-1.2.3-1da18d2561-ba4cf6e538.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/array.prototype.flat-npm-1.3.1-e9a9e389c0-5a8415949d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/array.prototype.flatmap-npm-1.3.1-c65186ca34-8c1c43a499.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/arrify-npm-1.0.1-affafba9fe-745075dd4a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/asap-npm-2.0.6-36714d439d-b296c92c4b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/asar-npm-2.0.3-0540c2ced3-0523e86cfd.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/asn1-npm-0.2.4-219dd49411-aa5d6f77b1.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/assert-plus-npm-1.0.0-cac95ef098-19b4340cb8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/assertion-error-npm-1.1.0-66b893015e-fd9429d3a3.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/ast-types-flow-npm-0.0.7-7d32a3abf5-a26dcc2182.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/astral-regex-npm-1.0.0-2df7c41332-93417fc087.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/astral-regex-npm-2.0.0-f30d866aab-876231688c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/async-exit-hook-npm-1.1.2-f74a635a86-a7b65da92d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/async-limiter-npm-1.0.1-7e6819bcdb-2b849695b4.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/async-npm-2.6.4-3155e80151-a52083fb32.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/async-npm-3.2.3-e9d6b79c88-c4bee57ab2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/async-npm-3.2.4-aba13508f9-43d07459a4.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/asynckit-npm-0.4.0-c718858525-7b78c451df.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/atob-npm-2.1.2-bcb583261e-dfeeeb7009.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/attr-accept-npm-1.1.3-a846f77cd3-836c0e8637.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/autoprefixer-npm-9.8.6-852792fe00-46987bc3de.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/aws-sign2-npm-0.7.0-656c6cb84d-b148b0bb07.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/aws4-npm-1.9.0-03690afb48-7e6a6bdb2e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/axe-core-npm-4.5.2-6c0c209d0c-4068f183b2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/axobject-query-npm-2.2.0-6553738f52-96b8c7d807.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/babel-jest-npm-27.5.1-f9f56b9874-4e93e6e9fb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/babel-plugin-istanbul-npm-6.1.1-df824055e4-cb4fd95738.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/babel-plugin-jest-hoist-npm-27.5.1-9fcb34fdf4-709c17727a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/babel-plugin-module-resolver-npm-5.0.0-67eb48a53b-d6880e49fc.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/babel-plugin-polyfill-corejs2-npm-0.3.3-374b04c5be-7db3044993.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/babel-plugin-polyfill-corejs2-npm-0.4.11-77e1239277-f098353ce7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/babel-plugin-polyfill-corejs3-npm-0.10.6-066bf0a146-f762f29f7a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/babel-plugin-polyfill-corejs3-npm-0.6.0-2d0edf85b8-470bb8c59f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/babel-plugin-polyfill-corejs3-npm-0.8.7-76de93c569-51bc215ab0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/babel-plugin-polyfill-regenerator-npm-0.4.1-f2ab3efe27-ab0355efba.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/babel-plugin-polyfill-regenerator-npm-0.5.5-4829ad3cd0-3a9b482867.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/babel-plugin-polyfill-regenerator-npm-0.6.2-c6dd64788f-1502335710.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/babel-plugin-syntax-trailing-function-commas-npm-6.22.0-1be1a2e94e-d8b9039ded.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/babel-preset-current-node-syntax-npm-1.0.1-849ec71e32-d118c27424.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/babel-preset-jest-npm-27.5.1-2c76f7f68c-251bcea11c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/babel-runtime-npm-6.26.0-d38e7946b4-8aeade9466.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/bail-npm-1.0.4-2beb43f28d-6166ff11b9.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/bail-npm-2.0.2-42130cb251-aab4e8ccdc.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/balanced-match-npm-1.0.0-951a2ad706-9b67bfe558.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/base64-js-npm-1.3.1-8625be908e-957b9ced0e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/base64-js-npm-1.5.1-b2f7275641-669632eb37.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/bcrypt-pbkdf-npm-1.0.2-80db8b16ed-4edfc9fe7d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/bezier-easing-npm-2.1.0-71cc003779-ec22666b53.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/bluebird-npm-3.7.2-6a54136ee3-869417503c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/blurhash-npm-2.0.5-7648719b71-aa4d6855bb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/boolbase-npm-1.0.0-965fe9af6d-3e25c80ef6.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/bowser-npm-1.6.0-5ce87005b6-e68ed8e849.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/bowser-npm-2.11.0-33664d9063-29c3f01f22.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/brace-expansion-npm-1.1.11-fb95eb05ad-faf34a7bb0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/brace-expansion-npm-2.0.1-17aa2616f9-a61e7cd2e8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/braces-npm-3.0.2-782240b28a-e2a8e769a8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/brotli-npm-1.3.2-12367afd63-d1b0ddcf67.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/browser-env-npm-3.3.0-86984fd465-8d73854d00.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/browser-process-hrtime-npm-0.1.3-12a293d493-e052e05933.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/browser-process-hrtime-npm-1.0.0-db700805c2-e30f868cdb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/browserslist-npm-4.19.3-580f50c44a-c28958313d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/browserslist-npm-4.21.4-7d64a96afc-4af3793704.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/browserslist-npm-4.23.3-4e727c7b5b-7906064f99.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/bser-npm-2.1.1-cc902055ce-9ba4dc58ce.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/buffer-from-npm-1.1.1-22917b8ed8-ccc53b6973.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/buffer-npm-5.7.1-513ef8259e-e2cf8429e1.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/cacache-npm-16.0.7-b9c035b8c8-2155b099b7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/cacheable-lookup-npm-5.0.4-8f13e8b44b-763e02cf91.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/cacheable-request-npm-7.0.2-e64cc641fc-6152813982.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/call-bind-npm-1.0.2-c957124861-f8e31de9d1.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/callsite-npm-1.0.0-897924017b-569686d622.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/callsites-npm-3.1.0-268f989910-072d17b6ab.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/camelcase-keys-npm-6.2.2-d13777ec12-43c9af1adf.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/camelcase-npm-5.3.1-5db8af62c5-e6effce26b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/camelcase-npm-6.3.0-e5e42a0d15-8c96818a90.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/caniuse-lite-npm-1.0.30001581-7909cc6e66-ca4e2cd9d0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/caniuse-lite-npm-1.0.30001660-6b60bb7533-8b2c5de2f5.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/caseless-npm-0.12.0-e83bc5df83-b43bd4c440.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/ccount-npm-1.0.4-d5765a2389-14e143d557.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/chai-npm-4.3.4-808f3b5355-772c522b3b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/chalk-npm-2.4.2-3ea16dd91e-ec3661d38f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/chalk-npm-4.1.0-c746e252ba-5561c7b4c0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/char-regex-npm-1.0.2-ecade5f97f-b563e4b603.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/character-entities-html4-npm-1.1.3-1cf5f6bd99-f0c58d9dbe.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/character-entities-legacy-npm-1.1.3-5b4b02fb0c-658867823b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/character-entities-npm-1.2.3-dec832362b-c5343e0c18.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/character-entities-npm-2.0.2-b5ef4d8fe2-cf16438140.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/character-reference-invalid-npm-1.1.3-33113bef4b-298a5ac194.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/chardet-npm-0.7.0-27933dd6c7-6fd5da1f5d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/check-error-npm-1.0.2-00c540c6e9-d9d1065044.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/cheerio-npm-1.0.0-rc.3-27acf9bb0c-90163e8f36.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/chownr-npm-2.0.0-638f1c9c61-c57cf9dd07.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/chrome-remote-interface-npm-0.32.2-e5d9302a0c-8a9fab2496.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/chromium-pickle-js-npm-0.2.0-ab332c66b8-5ccacc538b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/ci-info-npm-1.6.0-2d91706840-dfc058f60c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/ci-info-npm-3.6.1-c8a40d87d0-e463ed7152.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/cjs-module-lexer-npm-1.2.2-473ce063ea-977f3f042b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/ckeditor5-npm-44.0.0-6764714b6f-79db23a04e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/classnames-npm-2.3.1-f2ae0a8d3c-14db8889d5.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/clean-stack-npm-2.2.0-a8ce435a5c-2ac8cd2b2f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/cli-cursor-npm-2.1.0-3920629c9c-d88e97bfda.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/cli-width-npm-2.2.0-0e002b49d0-f4422e3b0f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/cliui-npm-7.0.4-d6b8a9edb6-ce2e8f578a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/clone-regexp-npm-2.2.0-189204de0f-3624905a98.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/clone-response-npm-1.0.3-f71cb6aff5-4e671cac39.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/co-npm-4.6.0-03f2d1feb6-5210d92230.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/codemirror-npm-5.65.2-7151f9e145-8e13511319.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/coffeescript-npm-2.4.1-a662dfa079-5e5f57e65a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/collapse-white-space-npm-1.0.5-94cbad9532-80b98d7d49.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/collect-v8-coverage-npm-1.0.1-39dec86bad-4efe0a1fcc.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/color-convert-npm-1.9.3-1fe690075e-fd7a64a17c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/color-convert-npm-2.0.1-79730e935b-79e6bdb9fd.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/color-name-npm-1.1.3-728b7b5d39-09c5d3e33d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/color-name-npm-1.1.4-025792b0ea-b044585952.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/color-parse-npm-1.4.2-bd4a4dff72-3ed5916f87.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/color-support-npm-1.1.3-3be5c53455-9b73568176.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/colorette-npm-1.2.1-1d4664fcf6-06e2fcdb9e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/combined-stream-npm-1.0.8-dc14d4a63a-49fa4aeb49.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/comma-separated-tokens-npm-2.0.2-9359322477-8fa68ff260.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/commander-npm-2.11.0-c1edb3e469-0d0c622d12.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/commander-npm-2.20.3-d8dcbaa39b-ab8c07884e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/commander-npm-5.1.0-7e939e7832-0b7fec1712.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/commander-npm-8.3.0-c0d18c66d5-0f82321821.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/compress-brotli-npm-1.3.8-9b6ceac281-de7589d692.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/concat-map-npm-0.0.1-85a921b7ee-902a9f5d89.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/console-control-strings-npm-1.1.0-e3160e5275-8755d76787.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/content-type-npm-1.0.4-3b1a5ca16b-3d93585fda.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/convert-source-map-npm-1.8.0-037f671dde-985d974a2d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/convert-source-map-npm-1.9.0-e294555f4b-dc55a1f28d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/convert-source-map-npm-2.0.0-7ab664dc4e-63ae9933be.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/core-js-compat-npm-3.26.1-8b3f5beb3f-f222bce000.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/core-js-compat-npm-3.38.1-4114633af1-a0a5673bcd.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/core-js-npm-1.2.7-88c984873f-0b76371bfa.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/core-js-npm-2.6.11-15178ded27-6944011e7a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/core-js-npm-3.6.1-5a02213fc6-63df2050e8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/core-js-pure-npm-3.26.1-d78c7431c6-d88c40e5e2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/core-util-is-npm-1.0.2-9fc2b94dc3-7a4c925b49.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/cosmiconfig-npm-7.0.0-b9d0d7d156-6801feaa02.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/create-react-class-npm-15.6.3-ae38dfa180-8ad0060381.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/cross-fetch-npm-4.0.0-9c67668db4-ecca4f37ff.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/cross-spawn-npm-6.0.5-2deab6c280-f893bb0d96.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/cross-spawn-npm-7.0.3-e4ff3e65b3-671cc7c728.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/crypto-md5-npm-1.0.0-56b18cf042-51f7f6bdf0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/css-npm-2.2.3-77c21fd5b5-7020de5171.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/css-select-npm-1.2.0-a7a03607e0-607cca60d2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/css-unit-converter-npm-1.1.2-277ce2166c-0788803334.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/css-what-npm-2.1.3-a9583898e8-a52d56c591.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/cssesc-npm-3.0.0-15ec56f86f-f8c4ababff.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/cssom-npm-0.3.8-a9291d36ff-24beb3087c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/cssom-npm-0.4.4-818f01a6e3-e3bc1076e7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/cssstyle-npm-1.4.0-ae32f18f2b-7efb9731d6.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/cssstyle-npm-2.3.0-b5d112c450-5f05e6fd2e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/csstype-npm-3.0.11-b49897178d-95e56abfe9.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/cuint-npm-0.2.2-3990651cc6-b8127a93a7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/d3-array-npm-3.1.1-2bf9c13a24-bb1a76d2da.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/d3-array-npm-3.2.0-c3a38fe288-e236f6670b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/d3-array-npm-3.2.2-f6016b3101-98af3db792.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/d3-color-npm-3.1.0-fc73fe3b15-4931fbfda5.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/d3-ease-npm-3.0.1-f8f3709dc7-06e2ee5326.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/d3-format-npm-3.1.0-dfc19924ca-f345ec3b8a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/d3-interpolate-npm-3.0.1-77ddca7977-a42ba314e2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/d3-path-npm-3.1.0-8d69e9e4e5-2306f1bd91.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/d3-scale-npm-4.0.2-d17a53447b-a9c770d283.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/d3-shape-npm-3.2.0-0beb7d8b02-de2af5fc9a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/d3-time-format-npm-4.1.0-7f352c4634-7342bce283.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/d3-time-npm-1.1.0-5df1aeb229-33fcfff94f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/d3-time-npm-3.0.0-a4963e64c8-01646568ef.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/d3-time-npm-3.1.0-fb068fd1c9-613b435352.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/d3-timer-npm-3.0.1-45083f465d-1cfddf86d7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/damerau-levenshtein-npm-1.0.8-bda7311c69-d240b77575.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/dashdash-npm-1.14.1-be8f10a286-3634c24957.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/data-urls-npm-1.1.0-b8123abe9f-dc4bd9621d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/data-urls-npm-2.0.0-2b80c32b82-97caf828aa.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/debug-npm-2.6.9-7d4cb597dc-d2f51589ca.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/debug-npm-3.2.7-754e818c7a-b3d8c59407.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/debug-npm-4.3.1-22e08d605e-2c3352e37d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/debug-npm-4.3.4-4513954577-3dbad3f94e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/decamelize-keys-npm-1.1.0-75168ffadd-8bc5d32e03.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/decamelize-npm-1.2.0-c5a2fdc622-ad8c51a7e7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/decimal.js-light-npm-2.5.0-50d6344c03-89d4d301f7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/decimal.js-npm-10.4.2-0119c97ba5-536cd6816a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/decode-named-character-reference-npm-1.0.2-db17a755fd-f4c71d3b93.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/decode-uri-component-npm-0.2.2-e22e2aa917-95476a7d28.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/decompress-response-npm-6.0.0-359de2878c-d377cf47e0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/dedent-npm-0.4.0-3981bb2373-dd99c0ea75.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/dedent-npm-0.6.0-caa47ac627-fe2c0f5adb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/dedent-npm-0.7.0-2dbb45a4c5-87de191050.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/deep-eql-npm-3.0.1-9a66c09c65-4f4c9fb79e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/deep-is-npm-0.1.3-0941784645-c15b04c384.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/deep-is-npm-0.1.4-88938b5a67-edb65dd0d7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/deepmerge-npm-4.2.2-112165ced2-a8c43a1ed8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/deepmerge-npm-4.3.1-4f751a0844-2024c6a980.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/defer-to-connect-npm-2.0.1-9005cc8c60-8a9b50d2f2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/define-properties-npm-1.1.4-85ee575655-ce0aef3f9e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/del-npm-3.0.0-e5f4cb556d-88192c1041.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/del-npm-5.1.0-eb4ac07f4c-d9e4ef2c12.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/delayed-stream-npm-1.0.0-c5a4c4cc02-46fe6e83e2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/delegates-npm-1.0.0-9b1942d75f-a51744d9b5.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/depd-npm-1.1.2-b0c8414da7-6b406620d2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/dequal-npm-2.0.3-53a630c60e-8679b850e1.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/des.js-npm-1.1.0-be97c341bf-0e9c1584b7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/desired-capabilities-npm-0.1.0-97c997553e-dda19679d3.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/detect-libc-npm-1.0.3-c30ac344d4-daaaed925f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/detect-newline-npm-3.1.0-6d33fa8d37-ae6cd429c4.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/device-specs-npm-1.0.0-75de2bfcbf-d50719f707.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/diff-npm-4.0.2-73133c7102-f2c09b0ce4.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/diff-npm-5.1.0-d24d222280-c7bf0df7c9.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/diff-sequences-npm-27.5.1-29338362fa-a00db5554c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/dir-glob-npm-3.0.1-1aea628b1b-fa05e18324.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/discontinuous-range-npm-1.0.0-572abfd975-8ee88d7082.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/dnd-core-npm-10.0.2-e645ff3319-58771aef10.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/dnd-core-npm-7.7.0-2d7d747a09-4b2340fe7b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/doctrine-npm-2.1.0-ac15d049b7-a45e277f7f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/doctrine-npm-3.0.0-c6f1615f04-fd7673ca77.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/dom-helpers-npm-3.4.0-5d3cdecaac-58d9f1c4a9.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/dom-serializer-npm-0.1.1-4c6e4ec242-4f6a3eff80.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/dom-serializer-npm-0.2.2-2e24969c0e-376344893e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/domelementtype-npm-1.3.1-87c4b5f9f4-7893da4021.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/domelementtype-npm-2.0.1-23794ee948-940c62d1c4.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/domexception-npm-1.0.1-89d1e4d3fe-f564a9c091.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/domexception-npm-2.0.1-81b20626ae-d638e9cb05.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/domhandler-npm-2.4.2-497ea9cea1-49bd70c9c7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/domutils-npm-1.5.1-6f8de414e8-800d1f9d1c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/domutils-npm-1.7.0-7a1529fcfc-f60a725b1f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/ecc-jsbn-npm-0.1.2-85b7a7be89-22fef4b620.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/editorconfig-checker-npm-4.0.2-e776df7723-fd34b0fbeb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/electron-to-chromium-npm-1.4.284-2fb881a7ac-be496e9dca.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/electron-to-chromium-npm-1.4.83-72f523d47b-fab4c992f1.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/electron-to-chromium-npm-1.5.22-0a24685643-3f8f21f013.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/elegant-spinner-npm-1.0.1-8b799f39a6-d6a773d950.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/email-validator-npm-2.0.4-ac07d6a6c7-e702fee24e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/emittery-npm-0.4.1-24c9b05f5e-dcd9f3dec5.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/emittery-npm-0.8.1-9771f0f260-2457e8c7b0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/emoji-regex-npm-7.0.3-cfe9479bb3-9159b2228b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/emoji-regex-npm-8.0.0-213764015c-d4c5c39d5a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/emoji-regex-npm-9.2.2-e6fac8d058-8487182da7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/encoding-npm-0.1.13-82a1837d30-bb98632f8f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/end-of-stream-npm-1.4.4-497fc6dee1-530a5a5a1e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/entities-npm-1.1.2-78e77a4b6d-d537b02799.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/entities-npm-2.0.0-90314ccb18-0d7e5323bb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/entities-npm-4.5.0-7cdb83b832-853f8ebd5b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/env-paths-npm-2.2.1-7c7577428c-65b5df55a8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/enzyme-adapter-react-16-npm-1.15.2-f9522ed334-a6e620b0b3.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/enzyme-adapter-utils-npm-1.13.0-5be5ecef8d-a76718f9c0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/enzyme-npm-3.11.0-74bfb15742-69ae80049c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/enzyme-shallow-equal-npm-1.0.1-352dbd5a14-bd2f0c4db5.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/enzyme-to-json-npm-3.4.3-d7ae7491ec-a81204e3bd.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/err-code-npm-2.0.3-082e0ff9a7-8b7b1be20d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/error-ex-npm-1.3.2-5654f80c0f-c1c2b8b65f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/error-stack-parser-npm-2.1.4-5b9f7fc0c2-3b916d2d14.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/es-abstract-npm-1.19.5-524a87d262-55199b0f17.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/es-abstract-npm-1.20.4-543318afab-89297cc785.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/es-shim-unscopables-npm-1.0.0-06186593f1-83e95cadbb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/es-to-primitive-npm-1.2.1-b7a7eac6c5-4ead6671a2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/esbuild-npm-0.17.8-dbf72a781c-d09776fcf3.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/escalade-npm-3.1.1-e02da076aa-a3e2a99f07.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/escalade-npm-3.2.0-19b50dd48f-47b029c83d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/escape-string-regexp-npm-1.0.5-3284de402f-6092fda75c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/escape-string-regexp-npm-2.0.0-aef69d2a25-9f8a2d5743.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/escape-string-regexp-npm-4.0.0-4b531d8d59-98b48897d9.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/escodegen-npm-1.14.3-a4dedc6eeb-381cdc4767.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/escodegen-npm-2.0.0-6450b02925-5aa6b2966f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/eslint-config-xo-npm-0.24.2-088ec46b7c-1a42a11c8a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/eslint-config-xo-react-npm-0.17.0-3f4f065489-2d91130182.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/eslint-config-xo-space-npm-0.20.0-5000415fb3-cc1398cf5a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/eslint-import-resolver-node-npm-0.3.6-d9426786c6-6266733af1.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/eslint-module-utils-npm-2.7.4-a1640084cb-5da13645da.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/eslint-npm-5.16.0-91d3d3fc21-53c6b94209.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/eslint-npm-8.27.0-2f252732ca-153b022d30.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/eslint-plugin-babel-npm-5.3.1-54ff661dc7-18b0bfda53.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/eslint-plugin-import-npm-2.26.0-959fe14a01-0bf77ad803.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/eslint-plugin-jest-npm-27.1.5-106a4feaa3-7c9de76579.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/eslint-plugin-jsx-a11y-npm-6.6.1-02552a3ea2-baae7377f0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/eslint-plugin-react-npm-7.31.10-d43fb4c6e9-f013669c29.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/eslint-rule-composer-npm-0.3.0-0188afafaa-c2f57cded8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/eslint-scope-npm-4.0.3-1492c6d263-c5f835f681.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/eslint-scope-npm-5.1.1-71fe59b18a-47e4b6a3f0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/eslint-scope-npm-7.1.1-23935eb377-9f6e974ab2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/eslint-utils-npm-1.4.3-b8f8bce3ac-a20630e686.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/eslint-utils-npm-3.0.0-630b3a4013-0668fe02f5.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/eslint-visitor-keys-npm-1.1.0-58aec922ec-1cb5616063.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/eslint-visitor-keys-npm-2.1.0-c31806b6b9-e3081d7dd2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/eslint-visitor-keys-npm-3.3.0-d329af7c8c-d59e68a7c5.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/esotope-hammerhead-npm-0.6.1-8eefe081bf-c20a2a8ab4.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/esotope-hammerhead-npm-0.6.8-57c7820c71-c434f6a27e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/espree-npm-5.0.1-abcab55b28-a091aac2bd.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/espree-npm-9.4.1-b88faf10bc-4d266b0cf8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/esprima-npm-4.0.1-1084e98778-b45bc805a6.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/esquery-npm-1.0.1-b909fe9deb-2d66ec49c3.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/esquery-npm-1.4.0-f39408b1a7-a0807e17ab.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/esrecurse-npm-4.2.1-9ebee4c3b1-3f05f9b650.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/esrecurse-npm-4.3.0-10b86a887a-ebc17b1a33.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/estraverse-npm-4.3.0-920a32f3c6-a6299491f9.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/estraverse-npm-5.3.0-03284f8f63-072780882d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/esutils-npm-2.0.3-f865beafd5-22b5b08f74.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/eventemitter3-npm-4.0.7-7afcdd74ae-1875311c42.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/execa-npm-3.4.0-ac88a31854-72832ff72f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/execa-npm-4.1.0-cc675b4189-e30d298934.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/execa-npm-5.1.1-191347acf5-fba9022c8c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/execall-npm-2.0.0-9ea589665a-d98ee3e33f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/exit-npm-0.1.2-ef3761a67d-abc407f07a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/expect-npm-27.5.1-4747b2cdc8-b2c66beb52.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/extend-npm-3.0.2-e1ca07ac54-a50a8309ca.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/external-editor-npm-3.1.0-878e7807af-1c2a616a73.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/extsprintf-npm-1.3.0-61a92b324c-cee7a4a1e3.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/extsprintf-npm-1.4.0-2b015bcaab-184dc8a413.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/fast-deep-equal-npm-3.1.1-cbd83be021-98bcc0eece.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/fast-deep-equal-npm-3.1.3-790edcfcf5-e21a9d8d84.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/fast-equals-npm-2.0.4-4807826768-1aac8a2e16.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/fast-glob-npm-3.2.11-bc01135fef-f473105324.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/fast-json-stable-stringify-npm-2.1.0-02e8905fda-b191531e36.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/fast-levenshtein-npm-2.0.6-fcd74b8df5-92cfec0a8d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/fastest-levenshtein-npm-1.0.12-a32b4ef51e-e1a013698d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/fastq-npm-1.6.0-b53b5275fa-e643b1c304.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/fb-watchman-npm-2.0.1-30005d50fe-8510230778.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/fbjs-npm-0.8.17-9a02cb2222-e969aeb175.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/figures-npm-2.0.0-f2db814eec-081beb16ea.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/file-entry-cache-npm-5.0.1-7212af17f3-9014b17766.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/file-entry-cache-npm-6.0.1-31965cf0af-f49701feaa.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/fill-range-npm-7.0.1-b8b1817caa-cc283f4e65.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/find-babel-config-npm-2.1.2-489c2d0cc1-268f29cb38.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/find-up-npm-3.0.0-a2d4b1b317-38eba3fe7a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/find-up-npm-4.1.0-c3ccf8d855-4c172680e8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/find-up-npm-5.0.0-e03e9b796d-07955e3573.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/flat-cache-npm-2.0.1-abf037b0b9-0f5e664676.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/flat-cache-npm-3.0.4-ee77e5911e-4fdd10ecbc.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/flatted-npm-2.0.2-ccb06e14ff-473c754db7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/flatted-npm-3.2.7-0da10b7c56-427633049d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/foreach-npm-2.0.4-0bb9a733da-a8241d8d97.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/forever-agent-npm-0.6.1-01dae53bf9-766ae6e220.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/form-data-npm-2.3.3-c016cc11c0-10c1780fa1.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/form-data-npm-3.0.1-d080d436e0-b019e8d35c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/fs-extra-npm-10.0.1-7c8ee14050-c1faaa5eb9.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/fs-minipass-npm-2.1.0-501ef87306-1b8d128dae.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/fs.realpath-npm-1.0.0-c8f05d8126-99ddea01a7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/fsevents-npm-2.3.2-a881d6ac9f-97ade64e75.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/fsevents-patch-3340e2eb10-8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/fsm-iterator-npm-1.1.0-30837569da-ca35f89a66.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/function-bind-npm-1.1.1-b56b322ae9-b32fbaebb3.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/function-bind-npm-1.1.2-7a55be9b03-2b0ff4ce70.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/function.prototype.name-npm-1.1.2-1c6a3f5dcd-68b4e1fc4b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/function.prototype.name-npm-1.1.5-e776a642bb-acd21d733a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/functional-red-black-tree-npm-1.0.1-ccfe924dcd-ca6c170f37.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/functions-have-names-npm-1.2.0-7ba7bfcca0-52e286a313.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/functions-have-names-npm-1.2.3-e5cf1e2208-c3f1f5ba20.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/gauge-npm-4.0.4-8f878385e9-788b6bfe52.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/gensync-npm-1.0.0-beta.2-224666d72f-a7437e58c6.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/get-caller-file-npm-2.0.5-80e8a86305-b9769a836d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/get-func-name-npm-2.0.0-afbf363765-8d82e69f3e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/get-intrinsic-npm-1.1.1-7e868745da-a9fe2ca8fa.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/get-intrinsic-npm-1.1.3-b6c7bd1010-152d79e872.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/get-os-info-npm-1.0.2-85ccd30eef-2b5a89b8ca.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/get-package-type-npm-0.1.0-6c70cdc8ab-bba0811116.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/get-stdin-npm-4.0.1-10c6ac0b43-4f73d3fe05.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/get-stdin-npm-8.0.0-920f876bc2-40128b6cd2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/get-stream-npm-5.2.0-2cfd3b452b-8bc1a23174.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/get-stream-npm-6.0.1-83e51a4642-e04ecece32.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/get-symbol-description-npm-1.0.0-9c95a4bc1f-9ceff8fe96.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/getos-npm-3.2.1-620c03aa34-42fd78a66d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/getpass-npm-0.1.7-519164a3be-ab18d55661.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/glob-npm-7.2.0-bb4644d239-78a8ea9423.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/glob-npm-8.0.1-3485e1ee02-7ac782f3ef.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/glob-npm-8.1.0-65f64af8b1-92fbea3221.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/glob-parent-npm-5.1.2-021ab32634-f4f2bfe242.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/glob-parent-npm-6.0.2-2cbef12738-c13ee97978.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/global-modules-npm-2.0.0-f71d340362-d6197f2585.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/global-prefix-npm-3.0.0-68cf01e67d-8a82fc1d6f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/globals-npm-11.12.0-1fa7f41a6c-67051a45ec.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/globals-npm-13.17.0-a6039e7d26-fbaf4112e5.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/globby-npm-10.0.1-35fa2ba87a-a16754b93e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/globby-npm-11.1.0-bdcdf20c71-b4be8885e0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/globby-npm-6.1.0-590bfb2681-18109d6b9d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/globjoin-npm-0.1.4-a1beb812bf-0a47d88d56.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/gonzales-pe-npm-4.3.0-82921c6976-49d60fc49a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/got-npm-11.8.5-787b5e3116-2de8a1bbda.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/got-npm-11.8.6-89e7cd5d67-bbc783578a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/graceful-fs-npm-4.2.10-79c70989ca-3f109d70ae.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/grapheme-splitter-npm-1.0.4-648f2bf509-0c22ec54de.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/graphlib-npm-2.1.8-9fab334669-1e0db4dea1.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/har-schema-npm-2.0.0-3a318c0ca5-d8946348f3.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/har-validator-npm-5.1.5-bd9ac162f5-b998a7269c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/hard-rejection-npm-2.1.0-a80f2a977d-7baaf80a0c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/harmony-reflect-npm-1.6.2-47a9f53361-2e5bae414c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/has-bigints-npm-1.0.2-52732e614d-390e31e7be.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/has-flag-npm-3.0.0-16ac11fe05-4a15638b45.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/has-flag-npm-4.0.0-32af9f0536-261a135703.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/has-npm-1.0.3-b7f00631c1-b9ad53d53b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/has-property-descriptors-npm-1.0.0-56289b918d-a6d3f0a266.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/has-symbols-npm-1.0.3-1986bff2c4-a054c40c63.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/has-tostringtag-npm-1.0.0-b1fcf3ab55-cc12eb28cb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/has-unicode-npm-2.0.1-893adb4747-1eab07a743.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/hash-sum-npm-2.0.0-2216318cf2-efeeacf09e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/hashlru-npm-2.3.0-8267692d49-38b3559e6f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/hasown-npm-2.0.2-80fe6c9901-e8516f776a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/hast-util-whitespace-npm-2.0.0-ca41487059-abeb538607.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/he-npm-1.2.0-3b73a2ff07-3d4d6babcc.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/highlight-es-npm-1.0.3-bc2bcc9d6b-c85c0060b2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/hoist-non-react-statics-npm-3.3.1-856203dd85-0333b04ef0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/hosted-git-info-npm-2.8.5-1e85fc7ff5-ce9d7884fb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/html-element-map-npm-1.2.0-d84ca33cc2-b5e7cad2da.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/html-encoding-sniffer-npm-1.0.2-ff426fe520-b874df6750.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/html-encoding-sniffer-npm-2.0.1-381bf15a76-bf30cce461.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/html-escaper-npm-2.0.2-38e51ef294-d2df2da3ad.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/html-tags-npm-3.1.0-2be8f49b1e-67587f2d40.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/htmlparser2-npm-3.10.1-1bc462e640-6875f7dd87.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/http-cache-semantics-npm-4.1.0-860520a31f-974de94a81.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/http-proxy-agent-npm-4.0.1-ce9ef61788-c6a5da5a19.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/http-proxy-agent-npm-5.0.0-7f1f121b83-e2ee1ff165.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/http-signature-npm-1.2.0-ee92426f34-3324598712.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/http-status-codes-npm-2.2.0-8d45a60399-31e1d73085.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/http2-wrapper-npm-1.0.3-5b58ade1df-74160b862e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/httpntlm-npm-1.8.13-ac5f1035f2-3107aee5b2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/httpreq-npm-1.1.1-efa0c14d6a-78df1fbd22.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/https-proxy-agent-npm-5.0.0-bb777903c3-165bfb090b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/human-signals-npm-1.1.1-616b2586c2-d587647c9e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/human-signals-npm-2.1.0-f75815481d-b87fd89fce.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/humanize-duration-npm-3.27.1-7dbdb13641-dad9f1a136.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/humanize-ms-npm-1.2.1-e942bd7329-9c7a74a282.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/iconv-lite-npm-0.4.24-c5c4ac6695-bd9f120f5a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/iconv-lite-npm-0.5.1-8f82a50a5f-db6f95423c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/iconv-lite-npm-0.6.3-24b8aae27e-3f60d47a5c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/identity-obj-proxy-npm-3.0.0-080ab9ac1e-97559f8ea2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/ieee754-npm-1.2.1-fb63b3caeb-5144c0c981.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/ignore-npm-4.0.6-66c0d6543e-248f82e50a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/ignore-npm-5.2.0-fc4b58a4f3-6b1f926792.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/immer-npm-9.0.12-e8cd7358fa-bcbec6d76d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/import-fresh-npm-3.2.2-509b4e1291-80bdc4c0ef.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/import-lazy-npm-3.1.0-d268fae3e2-50250b9591.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/import-lazy-npm-4.0.0-3215653869-22f5e51702.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/import-local-npm-3.1.0-8960af5e51-bfcdb63b5e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/imurmurhash-npm-0.1.4-610c5068a0-7cae75c8cd.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/indent-string-npm-1.2.2-73c84c6e36-b4d3b160ee.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/indent-string-npm-4.0.0-7b717435b2-824cfb9929.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/indexes-of-npm-1.0.1-5ce8500941-4f9799b173.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/indexof-npm-0.0.1-b35b810950-0fb04e8b14.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/infer-owner-npm-1.0.4-685ac3d2af-181e732764.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/inflight-npm-1.0.6-ccedb4b908-f4f76aa072.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/inherits-npm-2.0.4-c66b3957a0-4a48a73384.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/ini-npm-1.3.8-fb5040b4c0-dfd98b0ca3.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/inline-style-parser-npm-0.1.1-702eac409f-5d545056a3.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/inquirer-npm-6.5.2-4f6408c247-175ad4cd1e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/internal-slot-npm-1.0.3-9e05eea002-1944f92e98.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/internmap-npm-2.0.3-d74f5c9998-7ca41ec6ab.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/invariant-npm-2.2.4-717fbdb119-cc3182d793.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/ip-npm-1.1.5-af36318aa6-30133981f0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-alphabetical-npm-1.0.3-99341afaed-5427d289ce.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-alphanumeric-npm-1.0.0-d58b7d3fab-2f4f4f227f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-alphanumerical-npm-1.0.3-5233afb71b-149db8e369.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-arrayish-npm-0.2.1-23927dfb15-eef4417e3c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-bigint-npm-1.0.4-31c2eecbc9-c56edfe09b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-boolean-object-npm-1.1.2-ecbd575e6a-c03b23dbaa.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-buffer-npm-2.0.5-17e563f277-764c9ad8b5.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-callable-npm-1.2.4-03fc17459c-1a28d57dc4.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-callable-npm-1.2.7-808a303e61-61fd57d03b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-ci-npm-1.2.1-6a67118112-eca06c5626.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-core-module-npm-2.11.0-70061e141a-f96fd490c6.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-core-module-npm-2.15.1-34c73a6cbd-df134c1681.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-core-module-npm-2.8.1-ce21740d1b-418b7bc107.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-date-object-npm-1.0.2-461fbe93c0-ac859426e5.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-decimal-npm-1.0.3-e78f76a38e-52e4a9bd84.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-docker-npm-2.0.0-e998f9161a-9b0733c2b3.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-es2016-keyword-npm-1.0.0-926966d039-3574b3c61e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-extglob-npm-1.0.0-6d00ab629f-5eea8517fe.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-extglob-npm-2.1.1-0870ea68b5-df033653d0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-finite-npm-1.0.2-121ed1a9b2-4619b69013.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-fullwidth-code-point-npm-2.0.0-507f56ec71-eef9c6e15f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-fullwidth-code-point-npm-3.0.0-1ecf4ebee5-44a30c2945.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-generator-fn-npm-2.1.0-37895c2d2b-a6ad5492cf.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-glob-npm-2.0.1-f316041d6e-089f5f9364.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-glob-npm-4.0.1-341760116f-84627cad11.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-glob-npm-4.0.3-cb87bf1bdb-d381c1319f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-hexadecimal-npm-1.0.3-bbfe0f2bb5-1405688469.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-lambda-npm-1.0.1-7ab55bc8a8-93a32f0194.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-negative-zero-npm-2.0.2-0adac91f15-f3232194c4.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-number-npm-7.0.0-060086935c-456ac6f8e0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-number-object-npm-1.0.4-c8e38aaa89-d8e4525b5c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-path-cwd-npm-1.0.0-e4a6dc5f50-ade6d8d59b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-path-cwd-npm-2.2.0-e35e4aab5f-46a840921b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-path-in-cwd-npm-1.0.1-2cbe369511-bacfc67c0d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-path-inside-npm-1.0.1-cd0d417091-07e52c8116.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-path-inside-npm-3.0.2-e675e113cb-108fc2a60c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-path-inside-npm-3.0.3-2ea0ef44fd-abd50f0618.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-plain-obj-npm-1.1.0-1046f64c0b-0ee0480779.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-plain-obj-npm-2.1.0-8dffd7ae9c-cec9100678.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-plain-obj-npm-4.1.0-a4f2a92b44-6dc45da70d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-podman-npm-1.0.1-ee44473753-9a72f5fced.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-potential-custom-element-name-npm-1.0.1-f352f606f8-ced7bbbb64.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-promise-npm-2.1.0-ab46647421-ae31d22c2e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-regex-npm-1.1.4-cca193ef11-362399b335.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-regexp-npm-2.1.0-761c9ec2f7-502f8e09fa.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-shared-array-buffer-npm-1.0.2-32e4181fcd-9508929cf1.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-stream-npm-1.1.0-818ecbf6bb-063c6bec9d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-stream-npm-2.0.0-1401f82ad7-4dc47738e2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-string-npm-1.0.7-9f7066daed-323b3d0462.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-subset-npm-0.1.1-15dc569569-97b8d7852a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-symbol-npm-1.0.4-eb9baac703-92805812ef.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-typedarray-npm-1.0.0-bbd99de5b6-3508c6cd0a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-utf8-npm-0.2.1-46ab364e2f-167ccd2be8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-weakref-npm-1.0.2-ff80e8c314-95bd9a57cd.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-whitespace-character-npm-1.0.3-e641c3dd0b-e10b8f9111.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/is-word-character-npm-1.0.3-7db3267f64-53117ee2b7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/isarray-npm-0.0.1-92e37e0a70-49191f1425.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/isarray-npm-1.0.0-db4f547720-f032df8e02.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/isemail-npm-3.2.0-99fb9661e6-77adfbe8d6.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/isemail-patch-4a87058595-c8998b9188.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/isexe-npm-2.0.0-b58870bd2e-26bf6c5480.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/isomorphic-fetch-npm-2.2.1-46b4db5d7b-bb5daa7c37.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/isstream-npm-0.1.2-8581c75385-1eb2fe63a7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/istanbul-lib-coverage-npm-3.2.0-93f84b2c8c-a2a545033b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/istanbul-lib-instrument-npm-5.2.1-1b3ad719a9-bf16f1803b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/istanbul-lib-report-npm-3.0.0-660f97340a-3f29eb3f53.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/istanbul-lib-source-maps-npm-4.0.1-af0f859df7-21ad3df45d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/istanbul-reports-npm-3.1.5-fb11324e3e-7867228f83.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jest-changed-files-npm-27.5.1-e3b21b0242-95e9dc74c3.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jest-circus-npm-27.5.1-d8d9d88da5-6192dccbcc.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jest-cli-npm-27.5.1-e801369688-6c0a69fb48.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jest-config-npm-27.5.1-e70d159078-1188fd46c0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jest-diff-npm-27.5.1-818e549196-8be27c1e1e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jest-docblock-npm-27.5.1-7cec6a4999-c0fed6d55b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jest-each-npm-27.5.1-981b49b3a7-b5a6d8730f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jest-environment-jsdom-npm-27.5.1-de33b7f396-bc104aef7d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jest-environment-node-npm-27.5.1-2ecb71f8f5-0f988330c4.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jest-get-type-npm-27.5.1-980fbf7a43-63064ab701.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jest-haste-map-npm-27.5.1-2dfafa5d6b-e092a14128.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jest-jasmine2-npm-27.5.1-732ff8c674-b716adf253.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jest-leak-detector-npm-27.5.1-65940ce9fd-5c96890609.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jest-matcher-utils-npm-27.5.1-0c47b071fb-bb2135fc48.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jest-message-util-npm-27.5.1-6150700d58-eb6d637d14.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jest-mock-npm-27.5.1-22d1da854d-f5b5904bb1.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jest-npm-27.5.1-bacad4fe2a-96f1d69042.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jest-pnp-resolver-npm-1.2.3-70e06bf27c-db1a8ab2cb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jest-regex-util-npm-27.5.1-2fc9b32d99-d45ca7a954.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jest-resolve-dependencies-npm-27.5.1-0ae7a0aa18-c67af97afa.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jest-resolve-npm-27.5.1-a0a4a415f5-735830e726.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jest-runner-npm-27.5.1-2ed2c1cda8-5bbe6cf847.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jest-runtime-npm-27.5.1-c106eea3ba-929e3df0c5.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jest-serializer-npm-27.5.1-7cec732598-803e03a552.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jest-snapshot-npm-27.5.1-b26687beb2-a5cfadf0d2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jest-util-npm-27.5.1-26e68baa39-ac8d122f6d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jest-validate-npm-27.5.1-ee2a062ca8-82e870f8ee.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jest-watcher-npm-27.5.1-5993e06167-191c4e9c27.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jest-worker-npm-27.5.1-1c110b5894-98cd68b696.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/js-md4-npm-0.3.2-98750753d0-aa7b7cbd73.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/js-sdsl-npm-4.1.5-66fcf4f580-695f657ddc.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/js-tokens-npm-3.0.2-fe6fb334bd-ff24cf90e6.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/js-tokens-npm-4.0.0-0ac852e9e2-8a95213a5a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/js-yaml-npm-3.14.1-b968c6095e-bef146085f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/js-yaml-npm-4.1.0-3606f32312-c7830dfd45.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jsbn-npm-0.1.1-0eb7132404-e5ff29c1b8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jsdom-npm-13.2.0-8ff2aa99bd-b6bf6a838c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jsdom-npm-16.7.0-216c5c4bf9-454b833718.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jsesc-npm-0.5.0-6827074492-b8b44cbfc9.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jsesc-npm-2.5.2-c5acb78804-4dc1907711.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/json-buffer-npm-3.0.1-f8f6d20603-9026b03edc.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/json-parse-even-better-errors-npm-2.3.1-144d62256e-798ed4cf33.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/json-schema-npm-0.2.3-018ee3dfc9-bbc2070988.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/json-schema-traverse-npm-0.4.1-4759091693-7486074d3b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/json-stable-stringify-without-jsonify-npm-1.0.1-b65772b28b-cff44156dd.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/json-stringify-safe-npm-5.0.1-064ddd6ab4-48ec0adad5.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/json3-npm-3.3.0-9da24d9aeb-d2b52e7138.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/json5-npm-1.0.1-647fc8794b-e76ea23dbb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/json5-npm-2.2.1-44675c859c-74b8a23b10.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/json5-npm-2.2.3-9962c55073-2a7436a933.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jsonfile-npm-6.1.0-20a4796cee-7af3b8e1ac.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jsprim-npm-1.4.1-948d2c9ec3-6bcb20ec26.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/jsx-ast-utils-npm-3.3.3-3d3171e1e4-a2ed78cac4.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/keyv-npm-4.4.1-7b127d2e70-efce046d16.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/kind-of-npm-6.0.3-ab15f36220-3ab01e7b1d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/kleur-npm-3.0.3-f6f53649a4-df82cd1e17.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/kleur-npm-4.1.5-46b6135f41-1dc476e327.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/known-css-properties-npm-0.19.0-7137123707-a6be2d154c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/ky-npm-0.30.0-ead89c9778-d6ec6461e1.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/language-subtag-registry-npm-0.3.22-e9f79fe04e-8ab70a7e0e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/language-tags-npm-1.0.5-3a50e75c96-c81b5d8b9f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/leven-npm-3.1.0-b7697736a3-638401d534.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/levn-npm-0.3.0-48d774b1c2-0d084a5242.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/levn-npm-0.4.1-d183b2d7bb-12c5021c85.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/lightningcss-darwin-arm64-npm-1.18.0-f7a041f9b9-8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/lightningcss-linux-x64-gnu-npm-1.18.0-cd49846821-8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/lightningcss-npm-1.18.0-4c21c66fe7-e3673ca60a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/lines-and-columns-npm-1.1.6-23e74fab67-198a5436b1.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/linux-platform-info-npm-0.0.3-52ed848324-95d618467e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/locate-path-npm-3.0.0-991671ae9f-53db399667.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/locate-path-npm-5.0.0-46580c43e4-83e51725e6.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/locate-path-npm-6.0.0-06a1e4c528-72eb661788.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/lodash-es-npm-4.17.21-b45832dfce-05cbffad6e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/lodash-npm-4.17.21-6382451519-eb835a2e51.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/lodash.assign-npm-4.2.0-18b16126b9-75bbc6733c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/lodash.assignin-npm-4.2.0-f45fed9160-4b55bc1d65.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/lodash.debounce-npm-4.0.8-f1d6e09799-a3f527d22c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/lodash.defaultsdeep-npm-4.6.1-cf3ec4337e-1f346f1615.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/lodash.escape-npm-4.0.1-5ea709377f-fcb54f4574.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/lodash.escaperegexp-npm-4.1.2-c5b90e0e9c-6d99452b1c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/lodash.flattendeep-npm-4.4.0-26b2b4cbd7-8521c919ac.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/lodash.flowright-npm-3.5.0-fb486e3ce2-d7924839f8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/lodash.get-npm-4.4.2-7bda64ed87-e403047ddb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/lodash.isequal-npm-4.5.0-f8b0f64d63-da27515dc5.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/lodash.isfunction-npm-3.0.9-72aaa7f66c-99e54c34b1.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/lodash.ismatch-npm-4.4.0-e538fd6c3d-a393917578.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/lodash.mapvalues-npm-4.6.0-4664380119-0ff1b252fd.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/lodash.memoize-npm-4.1.2-0e6250041f-9ff3942fee.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/lodash.merge-npm-4.6.2-77cb4416bf-ad580b4bdb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/lodash.omit-npm-4.5.0-786639f9a0-434645e49f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/lodash.sortby-npm-4.7.0-fda8ab950d-db170c9396.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/lodash.throttle-npm-4.1.1-856641af92-129c0a28ce.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/lodash.unescape-npm-4.0.1-369781db1f-7a9c2133f5.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/log-symbols-npm-4.0.0-7291c4d053-a7c1fb5cc5.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/log-update-async-hook-npm-2.0.7-5eaaa2c8a9-ade8430b1e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/loglevel-npm-1.9.2-8bc06035f5-896c67b90a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/loglevel-plugin-prefix-npm-0.8.4-612472140b-5fe0632fa0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/longest-streak-npm-2.0.3-65d23aca91-c93ce22ee0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/loose-envify-npm-1.4.0-6307b72ccf-6517e24e0c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/lowercase-keys-npm-2.0.0-1876065a32-24d7ebd56c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/lru-cache-npm-2.6.3-b2c216d7c5-32602966ff.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/lru-cache-npm-5.1.1-f475882a51-c154ae1cbb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/lru-cache-npm-6.0.0-b4c8668fe1-f97f499f89.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/lru-cache-npm-7.9.0-d803108233-c91a293a10.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/macos-release-npm-3.1.0-07e3853185-e26c48c953.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/make-dir-npm-3.1.0-d1d7505142-484200020a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/make-fetch-happen-npm-10.1.2-e1f79fcb6c-42825d119a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/makeerror-npm-1.0.12-69abf085d7-b38a025a12.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/map-obj-npm-1.0.1-fa55100fac-9949e7baec.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/map-obj-npm-4.1.0-6460603295-c62b22f23e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/markdown-escapes-npm-1.0.3-f199ab1614-b5ed7e728f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/markdown-table-npm-2.0.0-a9c10c8e83-9bb634a930.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/marked-npm-4.0.12-1fc6e0ed31-7575117f85.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/match-url-wildcard-npm-0.0.4-c6f9992247-cf59c55b9c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/mathml-tag-names-npm-2.1.3-875bd2d6e7-1201a25a13.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/mdast-util-compact-npm-2.0.1-a807b39cc4-750cc76e46.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/mdast-util-definitions-npm-5.1.1-0d936c28d7-f8025e2c35.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/mdast-util-from-markdown-npm-1.2.0-0e8d4c3b86-fadc3521a3.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/mdast-util-to-hast-npm-12.2.4-b15d9e1783-c9a1c31527.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/mdast-util-to-string-npm-3.1.0-d888f00663-f42ddd4e22.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/meow-npm-7.1.1-8aa72d593e-87bba177ab.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/merge-stream-npm-1.0.1-8b7634c25d-3be7887dff.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/merge-stream-npm-2.0.0-2ac83efea5-6fa4dcc8d8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/merge2-npm-1.4.1-a2507bd06c-7268db63ed.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/micromark-core-commonmark-npm-1.0.6-fad8e6e27b-4b483c4607.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/micromark-factory-destination-npm-1.0.0-ead4af0386-8e733ae9c1.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/micromark-factory-label-npm-1.0.2-aa46281fdc-957e9366bd.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/micromark-factory-space-npm-1.0.0-e5fee5c60c-70d3aafde4.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/micromark-factory-title-npm-1.0.2-3739fc0d5d-9a9cf66bab.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/micromark-factory-whitespace-npm-1.0.0-ea665f7a7c-0888386e6e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/micromark-npm-3.1.0-2432407996-5fe5bc3bf9.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/micromark-util-character-npm-1.1.0-fe6b5d2ff8-504a4e3321.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/micromark-util-chunked-npm-1.0.0-456e2b513f-c1efd56e8c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/micromark-util-classify-character-npm-1.0.0-68787d9dc2-180446e6a1.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/micromark-util-combine-extensions-npm-1.0.0-f13e57de5b-5304a820ef.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/micromark-util-decode-numeric-character-reference-npm-1.0.0-68686444bf-f3ae2bb582.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/micromark-util-decode-string-npm-1.0.2-6aaf8c3c42-2dbb41c969.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/micromark-util-encode-npm-1.0.1-53c61ae1ae-9290583abf.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/micromark-util-html-tag-name-npm-1.1.0-a218ca069f-a9b783cec8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/micromark-util-normalize-identifier-npm-1.0.0-79f4e82268-d7c09d5e83.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/micromark-util-resolve-all-npm-1.0.0-cb1a6324ae-409667f2bd.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/micromark-util-sanitize-uri-npm-1.1.0-4389b68137-fe6093faa0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/micromark-util-subtokenize-npm-1.0.2-9d0d437340-c32ee58a7e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/micromark-util-symbol-npm-1.0.1-e674dcf8f4-c6a3023b3a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/micromark-util-types-npm-1.0.2-83e6ddb6df-08dc901b7c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/micromatch-npm-4.0.4-9fdcbb7a0e-ef3d1c88e7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/mime-db-npm-1.42.0-de454ab83e-b563c0f4af.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/mime-npm-1.4.1-d0e583698c-14c9de5c80.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/mime-types-npm-2.1.25-fab08685b4-5bcb035ef0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/mimic-fn-npm-1.2.0-960bf15ab7-69c0820515.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/mimic-fn-npm-2.1.0-4fbeb3abb4-d2421a3444.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/mimic-response-npm-1.0.1-f6f85dde84-034c78753b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/mimic-response-npm-3.1.0-a4a24b4e96-25739fee32.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/min-indent-npm-1.0.1-77031f50e1-bfc6dd03c5.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/minimalistic-assert-npm-1.0.1-dc8bb23d29-cc7974a926.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/minimatch-npm-3.1.2-9405269906-c154e56640.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/minimatch-npm-5.0.1-612724f6f0-b34b98463d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/minimist-npm-1.2.6-f4cee4b4af-d15428cd1e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/minimist-npm-1.2.7-51d33b1371-7346574a10.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/minimist-options-npm-4.1.0-64ca250fc1-8c040b3068.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/minipass-collect-npm-1.0.2-3b4676eab5-14df761028.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/minipass-fetch-npm-2.1.0-300ce55188-1334732859.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/minipass-flush-npm-1.0.5-efe79d9826-56269a0b22.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/minipass-npm-3.1.6-f032df1661-57a0404141.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/minipass-pipeline-npm-1.2.4-5924cb077f-b14240dac0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/minipass-sized-npm-1.0.3-306d86f432-79076749fc.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/minizlib-npm-2.1.2-ea89cd0cfb-f1fdeac0b0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/mkdirp-npm-0.5.5-6bc76534fc-3bce20ea52.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/mkdirp-npm-1.0.4-37f6ef56b9-a96865108c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/moment-duration-format-commonjs-npm-1.0.0-19fd822c82-ec59c658aa.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/moment-npm-2.24.0-d95decfdfe-9cd93a251a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/monet-npm-0.9.2-65551b373c-5d3ce0df3f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/moo-npm-0.4.3-17df15c625-f13bfb22ea.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/mousetrap-npm-1.6.3-d5a508751f-66f4e21c39.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/mri-npm-1.2.0-8ecee0357d-83f515abbc.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/ms-npm-2.0.0-9e1101a471-0e6a22b8b7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/ms-npm-2.1.2-ec0c1512ff-673cdb2c31.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/ms-npm-2.1.3-81ff3cfac1-aa92de6080.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/mustache-npm-2.3.2-c66c21dab1-e3073355fe.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/mute-stream-npm-0.0.7-22b59a65dd-a9d4772c1c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/nanoid-npm-3.3.1-bdd760bee0-4ef0969e1b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/natural-compare-lite-npm-1.4.0-12b6b308ed-5222ac3986.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/natural-compare-npm-1.4.0-97b75b362d-23ad088b08.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/nearley-npm-2.19.0-d1dc1eaa37-90c463503a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/negotiator-npm-0.6.3-9d50e36171-b8ffeb1e26.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/nice-try-npm-1.0.5-963856b16f-0b4af3b5bb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/node-fetch-npm-1.7.3-eb8372f991-3bb0528c05.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/node-fetch-npm-2.6.7-777aa2a6df-8d816ffd1e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/node-fetch-npm-2.7.0-587d57004e-d76d2f5edb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/node-gyp-npm-9.0.0-0eccfca4d1-4d8ef8860f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/node-int64-npm-0.4.0-0dc04ec3b2-d0b30b1ee6.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/node-releases-npm-2.0.18-51abc46668-ef55a3d853.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/node-releases-npm-2.0.2-a4dc82058a-da858bf86b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/node-releases-npm-2.0.6-8accb3fefb-e86a926dc9.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/nopt-npm-5.0.0-304b40fbfe-d35fdec187.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/normalize-package-data-npm-2.5.0-af0345deed-7999112efc.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/normalize-path-npm-3.0.0-658ba7d77f-88eeb4da89.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/normalize-range-npm-0.1.2-bec5e259e2-9b2f14f093.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/normalize-selector-npm-0.2.0-4a023ab014-6cc88334df.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/normalize-url-npm-6.1.0-b95bc12ece-4a49446311.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/normalize.css-npm-8.0.1-6124fb39b6-4698cae88e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/notosans-fontface-npm-1.3.0-86eff56be0-c552f8578d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/npm-run-path-npm-4.0.1-7aebd8bab3-5374c0cea4.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/npmlog-npm-6.0.2-e0e69455c7-ae238cd264.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/nth-check-npm-1.0.2-3f6d0d22eb-59e115fdd7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/num2fraction-npm-1.2.2-dc0a0a80ad-1da9c6797b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/number-is-nan-npm-1.0.1-845325a0fe-13656bc9aa.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/nwsapi-npm-2.2.0-8f05590043-5ef4a9bc0c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/nwsapi-npm-2.2.2-b77040326c-4376910629.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/oauth-sign-npm-0.9.0-7aa9422221-8f5497a127.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/object-assign-npm-3.0.0-8d79858461-56c66a7731.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/object-assign-npm-4.1.1-1004ad6dec-fcc6e4ea8c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/object-inspect-npm-1.12.0-d064fa559a-2b36d4001a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/object-inspect-npm-1.12.2-f125a822c0-a534fc1b85.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/object-is-npm-1.0.2-5e88138b4f-736cbe568e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/object-keys-npm-0.5.0-c29d5f81ed-9b2f8b6960.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/object-keys-npm-1.1.1-1bf2f1be93-b363c5e764.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/object.assign-npm-4.1.2-d52edada1c-d621d832ed.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/object.assign-npm-4.1.4-fb3deb1c3a-76cab513a5.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/object.entries-npm-1.1.1-611e0671fb-e3183f0a51.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/object.entries-npm-1.1.6-5f9ba14b46-0f8c47517e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/object.fromentries-npm-2.0.2-17b1a805ec-950ab9e906.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/object.fromentries-npm-2.0.6-424cf4cd3c-453c6d6941.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/object.hasown-npm-1.1.2-db9bbc7f97-b936572536.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/object.values-npm-1.1.1-f4f0df6a55-f1217c09fa.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/object.values-npm-1.1.6-ab9b67ccd3-f6fff9fd81.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/once-npm-1.4.0-ccf03ef07a-cd0a885013.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/onetime-npm-2.0.1-6c39ecc911-bb44015ac7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/onetime-npm-5.1.2-3ed148fa42-2478859ef8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/optionator-npm-0.8.3-bc555bc5b7-b8695ddf3d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/optionator-npm-0.9.1-577e397aae-dbc6fa0656.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/os-family-npm-1.1.0-1dbc219142-2bd105181d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/os-tmpdir-npm-1.0.2-e305b0689b-5666560f7b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/p-cancelable-npm-2.1.1-9388305f02-3dba12b4fb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/p-finally-npm-2.0.1-b59964aa17-6306a2851c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/p-iteration-npm-1.1.8-550aacc1f2-3eb8d8affc.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/p-limit-npm-2.3.0-94a0310039-84ff17f1a3.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/p-limit-npm-3.1.0-05d2ede37f-7c3690c4db.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/p-locate-npm-3.0.0-74de74f952-83991734a9.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/p-locate-npm-4.1.0-eec6872537-513bd14a45.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/p-locate-npm-5.0.0-92cc7c7a3e-1623088f36.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/p-map-npm-1.2.0-28313e49b6-1ac7267b1e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/p-map-npm-3.0.0-e4f17c4167-49b0fcbc66.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/p-map-npm-4.0.0-4677ae07c7-cb0ab21ec0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/p-try-npm-2.2.0-e0390dbaf8-f8a8e9a769.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/parent-module-npm-1.0.1-1fae11b095-6ba8b25514.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/parse-entities-npm-2.0.0-b7b4f46ff6-7addfd3e7d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/parse-json-npm-5.1.0-a83e3ab0f2-0c0c299347.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/parse-json-npm-5.2.0-00a63b1199-62085b17d6.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/parse5-npm-1.5.1-bf560a8338-688b8a665e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/parse5-npm-2.2.3-02a2b2d7ed-b1a13f5751.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/parse5-npm-3.0.3-fb7c9e4969-6a82d59d60.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/parse5-npm-5.1.0-b9c35ee7fa-13c44c6d47.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/parse5-npm-6.0.1-70a35a494a-7d569a176c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/parse5-npm-7.1.2-aa9a92c270-59465dd05e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/path-exists-npm-3.0.0-e80371aa68-96e92643aa.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/path-exists-npm-4.0.0-e9e4f63eb0-505807199d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/path-is-absolute-npm-1.0.1-31bc695ffd-060840f92c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/path-is-inside-npm-1.0.2-7dd0711668-0b5b6c92d3.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/path-key-npm-2.0.1-b1a971833d-f7ab0ad42f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/path-key-npm-3.1.1-0e66ea8321-55cd7a9dd4.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/path-parse-npm-1.0.7-09564527b7-49abf3d811.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/path-type-npm-4.0.0-10d47fc86a-5b1e2daa24.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/pathval-npm-1.1.1-ce0311d7e0-090e314771.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/performance-now-npm-0.2.0-938cf097a3-2020aecc39.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/performance-now-npm-2.1.0-45e3ce7e49-534e641aa8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/picocolors-npm-1.0.0-d81e0b1927-a2e8092dd8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/picocolors-npm-1.1.0-ea12a640bd-a64d653d3a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/picomatch-npm-2.3.1-c782cfd986-050c865ce8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/pify-npm-2.3.0-8b63310934-9503aaeaf4.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/pify-npm-3.0.0-679ee405c8-6cdcbc3567.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/pinkie-npm-1.0.0-1864baf29a-987523756f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/pinkie-npm-2.0.4-cffce4fb09-b12b10afea.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/pinkie-promise-npm-1.0.0-e0c57d0c18-9dc945fd77.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/pinkie-promise-npm-2.0.1-095439b8c5-b53a4a2e73.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/pirates-npm-4.0.5-22f8e827ce-c9994e61b8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/pkg-dir-npm-4.2.0-2b5d0a8d32-9863e3f351.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/pkg-up-npm-3.1.0-1eebe033b7-5bac346b7c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/pn-npm-1.1.0-442ee166b2-e4654186dc.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/pngjs-npm-3.4.0-4e495c1dad-8bd40bd698.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/postcss-html-npm-0.36.0-29df05b0a1-5f340df1d9.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/postcss-less-npm-3.1.4-9f62004ae4-f18d002e11.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/postcss-media-query-parser-npm-0.2.3-7c1ee973b6-8000d4d95b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/postcss-npm-7.0.35-d0709e0a47-6b19776905.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/postcss-resolve-nested-selector-npm-0.1.1-7067e0fef7-b08fb76ab0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/postcss-safe-parser-npm-4.0.2-d1aff2c5bb-b812832c06.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/postcss-sass-npm-0.4.4-ac0ddc0b6e-d361114e5a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/postcss-scss-npm-2.1.1-b2d9948208-61535f0465.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/postcss-selector-parser-npm-6.0.4-0a01b64fe5-2030e3439a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/postcss-syntax-npm-0.36.2-ef243fe3b7-812baee602.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/postcss-value-parser-npm-3.3.1-24ecbb1b05-62cd26e1cd.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/postcss-value-parser-npm-4.1.0-4620e3e849-68a9ea27c7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/prelude-ls-npm-1.1.2-a0daac0886-c4867c8748.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/prelude-ls-npm-1.2.1-3e4d272a55-cd192ec0d0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/pretty-format-npm-27.5.1-cd7d49696f-cf610cffcb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/pretty-hrtime-npm-1.0.3-32fd75fcbd-bae0e6832f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/process-nextick-args-npm-2.0.1-b8d7971609-1d38588e52.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/progress-npm-2.0.3-d1f87e2ac6-f67403fe7b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/promise-inflight-npm-1.0.1-5bb925afac-2274948309.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/promise-npm-7.3.1-5d81d474c0-475bb06913.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/promise-retry-npm-2.0.1-871f0b01b7-f96a3f6d90.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/promisify-event-npm-1.0.0-a5273093c5-16777615ee.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/prompts-npm-2.4.2-f5d25d5eea-d8fd1fe638.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/prop-types-exact-npm-1.2.0-4664209648-21676a16d5.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/prop-types-npm-15.8.1-17c71ee7ee-c056d3f1c0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/property-information-npm-6.1.1-70a68a8891-654b1e5c35.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/psl-npm-1.8.0-226099d70e-6150048ed2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/pump-npm-3.0.0-0080bf6a7a-e42e9229fb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/punycode-npm-2.1.1-26eb3e15cf-823bf443c6.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/qrcode-terminal-npm-0.10.0-fd8a08eb4f-2d5f53dd9a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/qs-npm-6.5.3-90b2635484-6f20bf08ca.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/querystringify-npm-2.2.0-4e77c9f606-5641ea231b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/quick-lru-npm-4.0.1-ef8aa17c9c-bea46e1abf.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/quick-lru-npm-5.1.1-e38e0edce3-a516faa255.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/raf-npm-3.4.1-c25d48d76e-50ba284e48.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/railroad-diagrams-npm-1.0.0-ab8798413c-9e312af352.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/randexp-npm-0.4.6-60140c8119-3c0d440a3f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/react-codemirror2-npm-7.2.1-dcadbb9383-5c524a0731.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/react-codemirror2-patch-e45ef6986c-2605999618.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/react-collapse-npm-5.1.1-3ae8f7fa6e-3c215cf387.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/react-datetime-npm-2.16.3-d6f981f191-802616a8de.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/react-dnd-html5-backend-npm-10.0.2-8c39650580-a3f5d41e71.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/react-dnd-npm-10.0.2-a8307febc2-4ce085a4b0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/react-dnd-test-backend-npm-7.7.0-51847d2016-4b325d2c93.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/react-dom-npm-16.13.1-b0abd8a83a-5009f3ee9b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/react-dropzone-npm-3.13.4-5984c9e1f4-6eb86f00a4.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/react-height-npm-3.0.2-47dedf8acd-7d81d9413f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/react-image-crop-npm-2.1.1-b18d247f4b-c8df746a17.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/react-is-npm-16.13.1-a9b9382b4f-f7a19ac349.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/react-is-npm-17.0.2-091bbb8db6-9d6d111d89.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/react-is-npm-18.2.0-0cc5edb910-e72d0ba81b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/react-keydown-npm-1.9.12-2c014be33b-28857d499d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/react-lifecycles-compat-npm-3.0.4-d5e285a39e-a904b0fc0a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/react-markdown-npm-8.0.3-7227aac18d-66c0b45889.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/react-motion-npm-0.5.2-f01f53dd58-b1b2ab15d0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/react-npm-16.13.1-56d408860d-4508f4207c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/react-onclickoutside-npm-6.9.0-8a676ccbab-111d46f155.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/react-portal-npm-4.2.0-81ab9086a7-c859f6dbc6.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/react-redux-npm-7.1.3-a44a68d19f-5917a9d489.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/react-resize-detector-npm-7.1.2-146e0b9a4f-55f4abad7f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/react-smooth-npm-2.0.1-9d723ee64c-65678491cb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/react-svg-npm-11.2.5-5b98773be4-bff8447519.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/react-test-renderer-npm-16.12.0-84c3d6b095-c4be5f7340.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/react-textarea-autosize-npm-8.3.3-ef925643e7-da3d019282.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/react-transition-group-npm-2.9.0-fe7767fa8d-d8c9e50aab.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/read-file-relative-npm-1.2.0-5afc91dbd8-95b8f99a31.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/read-pkg-npm-5.2.0-50426bd8dc-eb696e6052.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/read-pkg-up-npm-7.0.1-11895bed9a-e4e93ce70e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/readable-stream-npm-2.3.6-db500d5cd3-686bbf9e23.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/readable-stream-npm-3.6.0-23a4a5eb56-d4ea81502d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/recharts-npm-2.4.1-d63e8a828f-95c2fe46ae.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/recharts-scale-npm-0.4.5-30646fd0f5-e970377190.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/redent-npm-3.0.0-31892f4906-fa1ef20404.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/reduce-css-calc-npm-2.1.8-e2acd211e5-8fd27c06c4.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/reduce-reducers-npm-0.1.5-88416121a9-e0f2aa9cc7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/redux-actions-npm-0.12.0-9fbbb2aa8b-d0fa68ad4a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/redux-npm-4.0.5-aca53e621f-23689ba431.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/redux-saga-npm-0.15.6-dc1227459d-7b40f5473f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/redux-saga-test-plan-npm-3.7.0-924b1bb2a8-9ff85cc4bd.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/reflect.ownkeys-npm-0.2.0-e3d8f6e4f0-9530b16656.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/regenerate-npm-1.4.2-b296c5b63a-3317a09b2f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/regenerate-unicode-properties-npm-10.2.0-3d662e6e17-d5c5fc13f8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/regenerator-runtime-npm-0.11.1-a31e4f8dcd-3c97bd2c7b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/regenerator-runtime-npm-0.13.11-90bf536060-27481628d2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/regenerator-runtime-npm-0.13.9-6d02340eec-65ed455fe5.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/regenerator-runtime-npm-0.14.1-a6c97c609a-9f57c93277.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/regenerator-transform-npm-0.15.2-109e57a69f-20b6f9377d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/regexp.prototype.flags-npm-1.4.3-df1c08b65d-51228bae73.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/regexpp-npm-2.0.1-ac47f2bc1e-1f41cf80ac.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/regexpp-npm-3.2.0-2513f32cfc-a78dc5c715.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/regexpu-core-npm-5.3.2-89effc52a2-95bb970884.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/regjsparser-npm-0.9.1-47cd7c2ee2-5e1b76afe8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/remark-npm-12.0.1-afac00131a-1ce7a264c1.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/remark-parse-npm-10.0.1-e654d7df78-505088e564.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/remark-parse-npm-8.0.3-e459558b20-2dfea250e7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/remark-rehype-npm-10.1.0-bd8e6f7d8b-b9ac8acff3.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/remark-stringify-npm-8.1.1-19c0515ee9-9a556e5a0d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/repeat-string-npm-1.6.1-bc8e388655-1b809fc6db.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/repeating-npm-1.1.3-a1ca72906a-a10e45c779.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/replace-ext-npm-1.0.0-5d4ecb34ac-123e5c2804.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/replicator-npm-1.0.5-cbc6b8ce66-8dd62588a4.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/request-npm-2.88.2-f4a57c72c4-4e112c087f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/request-promise-core-npm-1.1.3-1b898b5c44-8572b5dd21.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/request-promise-native-npm-1.0.8-45c7cbe307-85522f86c9.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/require-directory-npm-2.1.1-8608aee50b-fb47e70bf0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/requires-port-npm-1.0.0-fd036b488a-eee0e303ad.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/reselect-npm-3.0.1-f1902bdb16-c7ce544bae.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/reselect-npm-4.1.8-cad5f0a3f3-a4ac87ceda.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/resolve-alpn-npm-1.2.1-af77edd28b-f558071fcb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/resolve-cwd-npm-1.0.0-c02922d9df-92ba282fbe.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/resolve-cwd-npm-3.0.0-e6f4e296bf-546e081601.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/resolve-from-npm-2.0.0-7d5a73a272-02db4c30fe.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/resolve-from-npm-4.0.0-f758ec21bf-f4ba0b8494.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/resolve-from-npm-5.0.0-15c9db4d33-4ceeb9113e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/resolve-npm-1.22.0-f641ddcc95-a2d14cc437.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/resolve-npm-1.22.1-3980488690-07af5fc1e8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/resolve-npm-1.22.8-098f379dfe-f8a26958aa.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/resolve-npm-2.0.0-next.4-3d0bd8621e-c438ac9a65.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/resolve-patch-46f9469d0d-5656f4d0be.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/resolve-patch-bad885c6ea-c79ecaea36.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/resolve-patch-bdbf6a2444-4bf9f4f8a4.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/resolve-patch-f6b5304cab-5479b7d431.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/resolve-url-npm-0.2.1-39edb8f908-7b7035b9ed.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/resolve.exports-npm-1.1.0-81756e03ba-52865af8ed.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/responselike-npm-2.0.1-7f64b6e122-b122535466.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/restore-cursor-npm-2.0.0-80278eb6b7-482e13d02d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/ret-npm-0.1.15-0d3c19de76-d76a9159eb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/retry-npm-0.12.0-72ac7fb4cc-623bd7d2e5.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/reusify-npm-1.0.4-95ac4aec11-c3076ebcc2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/rimraf-npm-2.6.3-f34c6c72ec-3ea587b981.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/rimraf-npm-2.7.1-9a71f3cc37-cdc7f6eacb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/rimraf-npm-3.0.2-2cb7dac69a-87f4164e39.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/rst-selector-parser-npm-2.2.3-94eed38823-fbfb2f6a7d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/run-async-npm-2.3.0-20b785ec02-9d713151c7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/run-parallel-npm-1.1.9-83c6b2d620-8bbeda89c2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/rxjs-npm-6.6.7-055046ea3c-bc334edef1.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/sade-npm-1.8.1-4759dc74c1-0756e5b04c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/safe-buffer-npm-5.1.2-c27fedf6c4-f2f1f7943c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/safe-buffer-npm-5.2.1-3481c8aa9b-b99c4b41fd.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/safe-regex-test-npm-1.0.0-e94a09b84e-bc566d8beb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/safer-buffer-npm-2.1.2-8d5c0b705e-cab8f25ae6.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/sanitize-filename-npm-1.6.3-cb83b2c2aa-aa733c012b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/sauce-connect-launcher-npm-1.3.2-f4bf7594fe-1c40be52aa.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/saucelabs-connector-npm-2.0.0-a8024ecc23-0ebe36a80e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/saxes-npm-3.1.11-a42cfd8cfa-3b69918c01.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/saxes-npm-5.0.1-57abf031ae-5636b55cf1.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/scheduler-npm-0.18.0-ee0cc1e577-b6e0b9e008.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/scheduler-npm-0.19.1-a4dd0ffd3a-73e185a59e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/semver-npm-5.5.0-891c3f008a-f7ae12b9d2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/semver-npm-5.7.1-40bcea106b-57fd0acfd0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/semver-npm-6.3.0-b3eace8bfd-1b26ecf6db.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/semver-npm-6.3.1-bcba31fdbe-ae47d06de2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/semver-npm-7.3.5-618cf5db6a-5eafe6102b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/semver-npm-7.3.8-25a996cb4f-ba9c7cbbf2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/semver-npm-7.5.3-275095dbf3-9d58db1652.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/semver-npm-7.6.3-57e82c14d5-4110ec5d01.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/set-blocking-npm-2.0.0-49e2cffa24-6e65a05f7c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/set-cookie-parser-npm-2.7.0-f8857d236c-1eed43d7b2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/setimmediate-npm-1.0.5-54587459b6-c9a6f2c5b5.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/shebang-command-npm-1.2.0-8990ba5d1d-9eed175030.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/shebang-command-npm-2.0.0-eb2b01921d-6b52fe8727.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/shebang-regex-npm-1.0.0-c3612b74e9-404c5a752c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/shebang-regex-npm-3.0.0-899a0cd65e-1a2bcae50d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/side-channel-npm-1.0.4-e1f38b9e06-351e41b947.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/signal-exit-npm-3.0.7-bd270458a3-a2f098f247.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/sisteransi-npm-1.0.5-af60cc0cfa-aba6438f46.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/slash-npm-3.0.0-b87de2279a-94a93fff61.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/slice-ansi-npm-2.1.0-02505ccc06-4e82995aa5.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/slice-ansi-npm-4.0.0-6eeca1d10e-4a82d7f085.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/smart-buffer-npm-4.2.0-5ac3f668bb-b5167a7142.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/socks-npm-2.6.2-94c1dcb8b8-dd91942930.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/socks-proxy-agent-npm-6.2.0-9c332b84bc-6723fd64fb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/source-map-npm-0.1.43-fb500e9a6d-0a230f8cae.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/source-map-npm-0.6.1-1a3621db16-59ce8640cf.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/source-map-npm-0.7.4-bc8d018ab6-01cc5a74b1.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/source-map-resolve-npm-0.5.3-6502ae65ba-c73fa44ac0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/source-map-support-npm-0.5.21-09ca99e250-43e98d700d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/source-map-url-npm-0.4.0-011efde48b-63ed54045f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/space-separated-tokens-npm-2.0.1-b5e2dbf218-66e30a6382.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/spdx-correct-npm-3.1.0-9ad640b3ef-fda9fc191e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/spdx-exceptions-npm-2.2.0-a19a6b5050-29189de3f6.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/spdx-expression-parse-npm-3.0.0-704f8535ae-308c8c4925.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/spdx-license-ids-npm-3.0.5-cb028e9441-b1ceea3f87.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/specificity-npm-0.4.1-042c3c2fd5-e558f1098f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/sprintf-js-npm-1.0.3-73f0a322fa-19d79aec21.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/sshpk-npm-1.16.1-feb759e7e0-5e76afd1ce.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/ssri-npm-9.0.0-5fe678a028-bf33174232.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/stack-utils-npm-2.0.6-2be1099696-052bf4d25b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/stackframe-npm-1.3.4-bf4b7cc8fd-bae1596873.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/state-toggle-npm-1.0.2-011fd812d1-8082196088.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/stealthy-require-npm-1.1.1-0105ec8207-6805b857a9.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/string-length-npm-4.0.2-675173c7a2-ce85533ef5.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/string-width-npm-2.1.1-0c2c6ae53f-d6173abe08.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/string-width-npm-3.1.0-e031bfa4e0-57f7ca73d2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/string-width-npm-4.2.3-2c27177bae-e52c10dc3f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/string.prototype.matchall-npm-4.0.8-1feb1531b6-952da3a818.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/string.prototype.trim-npm-1.2.1-d9e9f2fdd7-ff77c2b022.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/string.prototype.trimend-npm-1.0.5-2b980efa37-d44f543833.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/string.prototype.trimend-npm-1.0.6-304246ecc1-0fdc34645a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/string.prototype.trimstart-npm-1.0.5-9e62187810-a4857c5399.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/string.prototype.trimstart-npm-1.0.6-0926caea6c-89080feef4.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/string_decoder-npm-1.1.1-e46a6c1353-9ab7e56f9d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/string_decoder-npm-1.3.0-2422117fd0-8417646695.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/stringify-entities-npm-3.1.0-78dc656761-5b6212e298.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/strip-ansi-npm-4.0.0-d4de985014-d9186e6c0c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/strip-ansi-npm-5.2.0-275214c316-bdb5f76ade.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/strip-ansi-npm-6.0.1-caddc7cb40-f3cd25890a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/strip-bom-npm-2.0.0-5c4b64ed5a-08efb746bc.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/strip-bom-npm-3.0.0-71e8f81ff9-8d50ff27b7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/strip-bom-npm-4.0.0-97d367a64d-9dbcfbaf50.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/strip-final-newline-npm-2.0.0-340c4f7c66-69412b5e25.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/strip-indent-npm-3.0.0-519e75a28d-18f045d57d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/strip-json-comments-npm-2.0.1-e7883b2d04-1074ccb632.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/strip-json-comments-npm-3.1.1-dcb2324823-492f73e272.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/style-search-npm-0.1.0-e3177d3642-3cfefe3350.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/style-to-object-npm-0.3.0-612fa5e630-4d70840152.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/stylelint-npm-13.7.2-dd066448e6-63713c072f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/sugarss-npm-2.0.0-f6ef04c45b-777abf3167.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/supports-color-npm-5.5.0-183ac537bc-95f6f4ba5a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/supports-color-npm-6.1.0-7d19cd7f55-74358f9535.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/supports-color-npm-7.2.0-606bfcf7da-3dda818de0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/supports-color-npm-8.1.1-289e937149-c052193a7e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/supports-hyperlinks-npm-2.3.0-d19176eba2-9ee0de3c8c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/supports-preserve-symlinks-flag-npm-1.0.0-f17c4d0028-53b1e247e6.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/svg-tags-npm-1.0.0-68a35c11fa-407e5ef87c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/symbol-observable-npm-1.2.0-9e812a0a39-48ffbc22e3.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/symbol-tree-npm-3.2.4-fe70cdb75b-6e8fc7e148.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/table-npm-5.4.6-190b118384-9e35d3efa7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/table-npm-6.0.3-96d703a60a-7223650a38.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/tar-npm-6.1.11-e6ac3cba9c-a04c07bb9e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/terminal-link-npm-2.1.1-de80341758-ce3d2cd3a4.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/test-exclude-npm-6.0.0-3fb03d69df-3b34a3d771.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/testcafe-browser-provider-saucelabs-npm-3.0.0-de1e623975-17cbe5af99.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/testcafe-browser-tools-npm-2.0.26-77c1cdb7b2-764c31074e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/testcafe-hammerhead-npm-24.5.15-d07d214fb9-23fe387e66.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/testcafe-hammerhead-npm-31.7.2-89a598c45a-dc25f53f9f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/testcafe-legacy-api-npm-5.1.8-42fa733243-d99dfce398.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/testcafe-npm-3.6.2-a81c6699ec-1d815654a5.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/testcafe-react-selectors-npm-5.0.3-2be5275755-4f78a5d608.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/testcafe-reporter-json-npm-2.2.0-927dba3861-fbc893745a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/testcafe-reporter-list-npm-2.2.0-936d4104ed-ba473ca217.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/testcafe-reporter-minimal-npm-2.2.0-5e3a1739b5-1919a793c0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/testcafe-reporter-spec-npm-2.2.0-b4d8072498-a3992a8583.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/testcafe-reporter-xunit-npm-2.2.1-19bc2b9a82-ca096f2aff.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/testcafe-safe-storage-npm-1.1.2-74823ce34a-2deb1a159d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/testcafe-selector-generator-npm-0.1.0-e3fbe291b5-b5bbbe5773.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/text-table-npm-0.2.0-d92a778b59-b6937a38c8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/throat-npm-6.0.1-1308a37a10-782d4171ee.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/through-npm-2.3.8-df5f72a16e-a38c3e0598.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/time-limit-promise-npm-1.0.4-e2e455a305-39c0785950.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/tmp-npm-0.0.28-456e914f24-8167d2471b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/tmp-npm-0.0.33-bcbf65df2a-902d7aceb7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/tmp-npm-0.1.0-fa18ef19c4-6bab8431de.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/tmp-promise-npm-1.1.0-170b75b166-1f4a455986.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/tmpl-npm-1.0.5-d399ba37e2-cd922d9b85.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/to-fast-properties-npm-2.0.0-0dc60cc481-be2de62fe5.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/to-regex-range-npm-5.0.1-f1e8263b00-f76fa01b3d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/tough-cookie-npm-2.5.0-79a2fe43fe-16a8cd0902.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/tough-cookie-npm-4.0.0-7c5f3086af-0891b37eb7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/tough-cookie-npm-4.1.2-09068826e1-a7359e9a3e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/tough-cookie-npm-4.1.3-08285e2518-c9226afff3.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/tr46-npm-0.0.3-de53018915-726321c5ea.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/tr46-npm-1.0.1-9547f343a4-96d4ed46bc.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/tr46-npm-2.1.0-00af583f4f-ffe6049b9d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/tree-kill-npm-1.2.2-3da0e5a759-49117f5f41.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/trim-lines-npm-3.0.1-24471f7e84-e241da1046.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/trim-newlines-npm-3.0.0-ccf666d8fc-ad99b771e7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/trim-npm-0.0.1-d138075543-2b4646dff9.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/trim-trailing-lines-npm-1.1.2-69de7238c4-fdd7904d01.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/trough-npm-1.0.4-146509a4be-6a8f7febe9.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/trough-npm-2.1.0-20e92f46fc-a577bb561c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/truncate-utf8-bytes-npm-1.0.2-ed694c53b0-ad09731470.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/tsconfig-paths-npm-3.14.1-17a815b5c5-8afa01c673.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/tslib-npm-1.10.0-9261539b46-1d0450dc6f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/tslib-npm-2.4.0-9cb6dc5030-8c4aa6a3c5.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/tsutils-npm-3.21.0-347e6636c5-1843f4c1b2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/tunnel-agent-npm-0.6.0-64345ab7eb-05f6510358.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/turndown-npm-7.2.0-35c69df4d5-a0bdf2fcc8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/turndown-plugin-gfm-npm-1.0.2-73465b88af-18191dc18d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/tweetnacl-npm-0.14.5-a3f766c0d1-6061daba17.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/type-check-npm-0.3.2-a4a38bb0b6-dd3b149564.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/type-check-npm-0.4.0-60565800ce-ec688ebfc9.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/type-detect-npm-4.0.8-8d8127b901-62b5628bff.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/type-fest-npm-0.13.1-7f4486b973-e6bf2e3c44.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/type-fest-npm-0.20.2-b36432617f-4fb3272df2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/type-fest-npm-0.21.3-5ff2a9c6fd-e6b32a3b38.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/type-fest-npm-0.6.0-76b229965b-b2188e6e4b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/type-fest-npm-0.8.1-351ad028fe-d61c4b2eba.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/typedarray-to-buffer-npm-3.1.5-aadc11995e-99c11aaa8f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/typesafe-actions-npm-5.1.0-22e7ca5fa4-63f973ca93.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/typescript-npm-4.6.4-114dfa5f7e-e7bfcc39cd.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/typescript-npm-4.7.4-65aa6ffb42-5750181b1c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/typescript-patch-044c37f428-1cb434fbc6.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/typescript-patch-ae154b3216-96d3030cb0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/ua-parser-js-npm-0.7.31-aeb4c9aae9-e2f8324a83.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/unbox-primitive-npm-1.0.2-cb56a05066-b7a1cf5862.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/underscore-npm-1.12.1-f5ca0889f5-ec327603aa.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/undici-types-npm-5.26.5-de4f7c7bb9-3192ef6f3f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/undici-types-npm-6.19.8-9f12285b7a-de51f1b447.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/unherit-npm-1.1.2-b37f2c6f5e-b5d55cf2ea.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/unicode-canonical-property-names-ecmascript-npm-2.0.0-d2d8554a14-39be078afd.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/unicode-match-property-ecmascript-npm-2.0.0-97a00fd52c-1f34a7434a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/unicode-match-property-value-ecmascript-npm-2.2.0-011b10a684-9e3151e1d0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/unicode-property-aliases-ecmascript-npm-2.0.0-1636cb7768-dda4d39128.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/unified-npm-10.1.2-731093c9be-053e7c65ed.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/unified-npm-9.2.0-2edf64a14a-0cac4ae119.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/uniq-npm-1.0.1-5cab2dd0f3-8206535f83.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/unique-filename-npm-1.1.1-c885c5095b-cf4998c922.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/unique-slug-npm-2.0.2-f6ba1ddeb7-5b6876a645.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/unist-builder-npm-3.0.0-7a683c2dbd-80459ee3c2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/unist-util-find-all-after-npm-3.0.2-f898fe2b2b-74b1fe81e3.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/unist-util-generated-npm-2.0.0-9eb541f36f-3a806793fa.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/unist-util-is-npm-4.0.3-df0a79ea11-8febc0ef7d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/unist-util-is-npm-5.1.1-eeef3df35f-e8743a19a3.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/unist-util-position-npm-4.0.3-061bc576b3-0d89973628.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/unist-util-remove-position-npm-2.0.1-8d82f0286a-4149294969.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/unist-util-stringify-position-npm-2.0.3-abaa9bf961-f755cadc95.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/unist-util-stringify-position-npm-3.0.2-e0b49394fc-2dfd7a0fb2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/unist-util-visit-npm-2.0.3-e3d6dbea25-1fe19d500e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/unist-util-visit-npm-4.1.1-d4ebc5dd61-c4a63734b0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/unist-util-visit-parents-npm-3.1.1-a4bb258148-1170e397df.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/unist-util-visit-parents-npm-5.1.1-fe9eee5f6b-c699d18f5b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/universalify-npm-0.1.2-9b22d31d2d-40cdc60f6e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/universalify-npm-0.2.0-9984e61c10-e86134cb12.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/universalify-npm-2.0.0-03b8b418a8-2406a4edf4.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/unquote-npm-1.1.1-11903c1689-71745867d0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/update-browserslist-db-npm-1.0.10-676baf0b9f-12db73b4f6.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/update-browserslist-db-npm-1.1.0-3d2cb7d955-7b74694d96.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/uri-js-npm-4.2.2-e6ac2fca26-5a91c55d8a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/urix-npm-0.1.0-bd5e55a13a-4c076ecfbf.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/url-parse-npm-1.5.10-64fa2bcd6d-fbdba6b1d8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/url-to-options-npm-2.0.0-5c49006293-104741b13c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/use-composed-ref-npm-1.3.0-20ed37af5c-f771cbadfd.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/use-isomorphic-layout-effect-npm-1.1.2-65facd0a4b-a6532f7fc9.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/use-latest-npm-1.2.1-a2c18ad4ba-ed3f2ddddf.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/utf8-byte-length-npm-1.0.4-cf13f06124-f188ca076e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/util-deprecate-npm-1.0.2-e3fe1a219c-474acf1146.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/util-inspect-npm-0.1.8-fc128cb05e-d2ada82f51.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/uuid-npm-3.3.3-db057f4601-21133d0e8a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/uvu-npm-0.5.6-c8507ad49b-09460a3797.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/v8-compile-cache-npm-2.2.0-b4d8f03dca-b5916ac207.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/v8-to-istanbul-npm-8.1.1-15c031b361-54ce92bec2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/validate-npm-package-license-npm-3.0.4-7af8adc7a8-35703ac889.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/vanilla-colorful-npm-0.7.2-e7027ae79f-02bcdc1c8c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/verror-npm-1.10.0-c3f839c579-c431df0bed.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/vfile-location-npm-3.2.0-cd97ee24d4-9bb3df6d0b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/vfile-message-npm-2.0.2-c056770870-6b588a3435.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/vfile-npm-4.2.0-c3dcb6dc90-89d3410d8c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/vfile-npm-5.3.5-aa9b688b1f-14a9ea19d1.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/victory-vendor-npm-36.6.8-918f3cd0b4-6411f7c19a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/w3c-hr-time-npm-1.0.1-22f42e9e95-0d726afa5d.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/w3c-hr-time-npm-1.0.2-87f88e51d9-ec3c2dacbf.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/w3c-xmlserializer-npm-1.1.2-1ccc348fd8-1683e083d0.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/w3c-xmlserializer-npm-2.0.0-f8f7bc8b42-ae25c51cf7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/walker-npm-1.0.8-b0a05b9478-ad7a257ea1.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/webauth-npm-1.1.0-ff524c5768-fa7bbe872a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/webdriver-npm-7.33.0-a232b5e7a5-4e43df0df7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/webidl-conversions-npm-3.0.1-60310f6a2b-c92a0a6ab9.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/webidl-conversions-npm-4.0.2-1d159e6409-c93d8dfe90.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/webidl-conversions-npm-5.0.0-9649787484-ccf1ec2ca7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/webidl-conversions-npm-6.1.0-0594fd577c-1f526507aa.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/whatwg-encoding-npm-1.0.5-85e0fb7d7d-5be4efe111.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/whatwg-fetch-npm-3.6.2-4bdf324792-ee976b7249.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/whatwg-mimetype-npm-2.3.0-52eaa1d941-23eb885940.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/whatwg-url-npm-5.0.0-374fb45e60-b8daed4ad3.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/whatwg-url-npm-7.1.0-d6cae01571-fecb07c872.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/whatwg-url-npm-8.7.0-67af66db8f-a87abcc6ce.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/which-boxed-primitive-npm-1.0.2-e214f9ae5a-53ce774c73.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/which-npm-1.3.1-f0ebb8bdd8-f2e185c624.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/which-npm-2.0.2-320ddf72f7-1a5c563d3c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/which-promise-npm-1.0.0-a25bf5eda8-b5a9609116.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/wide-align-npm-1.1.5-889d77e592-d5fc37cd56.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/window-npm-4.2.6-236560e05c-bb4a635f56.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/windows-release-npm-5.0.1-fc0de1cc02-b6b403333b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/word-wrap-npm-1.2.3-7fb15ab002-30b48f91fc.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/wrap-ansi-npm-7.0.0-ad6e1a0554-a790b846fd.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/wrappy-npm-1.0.2-916de4d4b3-159da4805f.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/write-file-atomic-npm-3.0.3-d948a237da-c55b24617c.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/write-npm-1.0.3-1bac756049-6496197ceb.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/ws-npm-6.2.1-bbe0ef9859-82f7512bb7.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/ws-npm-7.5.7-6cc440864a-5c1f669a16.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/ws-npm-7.5.9-26f12a5ed6-c3c100a181.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/xml-name-validator-npm-3.0.0-10e74a38ea-b3ac459afe.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/xmlchars-npm-2.2.0-8b78f0f5e4-8c70ac9407.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/xtend-npm-4.0.2-7f2375736e-ac5dfa738b.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/y18n-npm-5.0.8-5f3a0a7e62-54f0fb9562.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/yallist-npm-3.1.1-a568a556b4-48f7bb00dc.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/yallist-npm-4.0.0-b493d9e907-343617202a.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/yaml-npm-1.10.0-3e2d763b45-ae81d29a82.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/yargs-npm-16.2.0-547873d425-b14afbb51e.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/yargs-parser-npm-18.1.3-0ba9c4f088-60e8c7d1b8.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/yargs-parser-npm-20.2.9-a1d19e598d-8bb69015f2.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/cache/yocto-queue-npm-0.1.0-c6c9a7db29-f77b3d8d00.zip", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/plugins/@yarnpkg/plugin-interactive-tools.cjs", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/plugins/@yarnpkg/plugin-typescript.cjs", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/plugins/@yarnpkg/plugin-version.cjs", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/plugins/@yarnpkg/plugin-workspace-tools.cjs", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/.yarn/releases/yarn-3.2.0.cjs", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/Classes/Application/ChangeTargetWorkspace.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neos_neos-ui/Classes/Application/DiscardAllChanges.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neos_neos-ui/Classes/Application/DiscardChangesInDocument.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neos_neos-ui/Classes/Application/DiscardChangesInSite.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neos_neos-ui/Classes/Application/PublishChangesInDocument/PublishChangesInDocumentCommand.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neos_neos-ui/Classes/Application/PublishChangesInSite/PublishChangesInSiteCommand.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/MinimalNodeForTree.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/NodeMap.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/NodeMapBuilder.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/ReloadNodesQuery.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neos_neos-ui/Classes/Application/ReloadNodes/ReloadNodesQueryResult.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neos_neos-ui/Classes/Application/Shared/Conflict.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neos_neos-ui/Classes/Application/Shared/Conflicts.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neos_neos-ui/Classes/Application/Shared/ConflictsOccurred.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neos_neos-ui/Classes/Application/Shared/IconLabel.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neos_neos-ui/Classes/Application/Shared/PartialConflictsOccurred.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neos_neos-ui/Classes/Application/Shared/PublishSucceeded.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neos_neos-ui/Classes/Application/SyncWorkspace/SyncWorkspaceCommand.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neos_neos-ui/Classes/Application/SyncWorkspace/SyncingSucceeded.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neos_neos-ui/Classes/Controller/BackendServiceController.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neos_neos-ui/Classes/Domain/Model/Feedback/Operations/UpdateWorkspaceInfo.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neos_neos-ui/Classes/Domain/NodeCreation/NodeCreationCommands.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neos_neos-ui/Classes/Domain/NodeCreation/NodeCreationElements.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neos_neos-ui/Classes/Infrastructure/ContentRepository/ConflictsFactory.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neos_neos-ui/Classes/Infrastructure/ContentRepository/CreationDialog/PromotedElementsCreationHandlerFactory.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neos_neos-ui/Classes/Infrastructure/MVC/RoutesProviderHelper.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neos_neos-ui/Classes/Infrastructure/Neos/UriPathSegmentNodeCreationHandlerFactory.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neos_neos-ui/Classes/View/OutOfBandRenderingViewFactory.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neos_neos-ui/Resources/Private/Templates/Error/ErrorMessage.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/TestDistribution/DistributionPackages/Neos.TestNodeTypes/Classes/Application/WriteAdditionalSettings/WriteAdditionalSettingsCommand.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/tests/manual/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/tests/manual/build.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/tests/manual/index.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/packages/neos-ui-ckeditor5-bindings/tests/manual/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/src/shims/vendor/ckeditor5-exports/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/src/shims/vendor/classnames/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/src/shims/vendor/prop-types/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/src/shims/vendor/react/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/src/shims/vendor/react-css-themr/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/src/shims/vendor/react-dnd/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/src/shims/vendor/react-dnd-html5-backend/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/src/shims/vendor/react-dom/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/src/shims/vendor/react-redux/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/src/shims/vendor/redux-actions/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/src/shims/vendor/redux-saga/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/src/shims/vendor/redux-saga-effects/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility/src/shims/vendor/reselect/index.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 4.509186029434204, "profiling_times": {"config_time": 6.486230850219727, "core_time": 17.520836353302002, "ignores_time": 0.0023450851440429688, "total_time": 24.010857820510864}, "parsing_time": {"total_time": 20.582051753997803, "per_file_time": {"mean": 0.022107466975293033, "std_dev": 0.0017135060009474347}, "very_slow_stats": {"time_ratio": 0.03503610245008936, "count_ratio": 0.0021482277121374865}, "very_slow_files": [{"fpath": "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/Endpoints/index.ts", "ftime": 0.3371570110321045}, {"fpath": "downloaded_repos/neos_neos-ui/Tests/IntegrationTests/pageModel.js", "ftime": 0.3839578628540039}]}, "scanning_time": {"total_time": 88.21512651443481, "per_file_time": {"mean": 0.02672375841091632, "std_dev": 0.027663368159295165}, "very_slow_stats": {"time_ratio": 0.2299371506121477, "count_ratio": 0.0021205695243865495}, "very_slow_files": [{"fpath": "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/CR/Nodes/index.ts", "ftime": 1.5333199501037598}, {"fpath": "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/LeftSideBar/NodeTree/Node/index.js", "ftime": 1.638826847076416}, {"fpath": "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/CR/Nodes/selectors.ts", "ftime": 2.049791097640991}, {"fpath": "downloaded_repos/neos_neos-ui/packages/neos-ui/src/manifest.dataloaders.js", "ftime": 2.080120086669922}, {"fpath": "downloaded_repos/neos_neos-ui/packages/neos-ui/src/manifest.js", "ftime": 3.1117160320281982}, {"fpath": "downloaded_repos/neos_neos-ui/.circleci/config.yml", "ftime": 3.7963638305664062}, {"fpath": "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/Endpoints/index.ts", "ftime": 6.073796987533569}]}, "matching_time": {"total_time": 30.61507797241211, "per_file_and_rule_time": {"mean": 0.007972676555315649, "std_dev": 0.0023097220330255894}, "very_slow_stats": {"time_ratio": 0.39641864043364455, "count_ratio": 0.011197916666666667}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/neos_neos-ui/packages/neos-ui/src/manifest.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.32404494285583496}, {"fpath": "downloaded_repos/neos_neos-ui/packages/neos-ui-sagas/src/UI/PageTree/index.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.32419705390930176}, {"fpath": "downloaded_repos/neos_neos-ui/packages/neos-ui-extensibility-webpack-adapter/scripts/watch.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.3326880931854248}, {"fpath": "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/CR/Nodes/selectors.ts", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.36324596405029297}, {"fpath": "downloaded_repos/neos_neos-ui/packages/neos-ui-i18n/src/global/initializeI18n.ts", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.38006091117858887}, {"fpath": "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/Endpoints/index.ts", "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.4500749111175537}, {"fpath": "downloaded_repos/neos_neos-ui/packages/neos-ui-redux-store/src/CR/Nodes/selectors.ts", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.541550874710083}, {"fpath": "downloaded_repos/neos_neos-ui/packages/neos-ui/src/Containers/LeftSideBar/NodeTree/Node/index.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 1.0658440589904785}, {"fpath": "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/Endpoints/index.ts", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 1.3124969005584717}, {"fpath": "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/Endpoints/index.ts", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 1.928251028060913}]}, "tainting_time": {"total_time": 8.44180679321289, "per_def_and_rule_time": {"mean": 0.003935574262570114, "std_dev": 0.0007219641152914038}, "very_slow_stats": {"time_ratio": 0.4671738228145085, "count_ratio": 0.016783216783216783}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/Endpoints/index.ts", "fline": 1, "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.09690093994140625}, {"fpath": "downloaded_repos/neos_neos-ui/packages/neos-ui/src/manifest.dataloaders.js", "fline": 1, "rule_id": "javascript.express.security.require-request.require-request", "time": 0.09695100784301758}, {"fpath": "downloaded_repos/neos_neos-ui/packages/neos-ui/src/manifest.dataloaders.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.0976710319519043}, {"fpath": "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/Endpoints/index.ts", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.10203194618225098}, {"fpath": "downloaded_repos/neos_neos-ui/packages/neos-ui/src/manifest.js", "fline": 1, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.1081230640411377}, {"fpath": "downloaded_repos/neos_neos-ui/packages/neos-ui/src/manifest.dataloaders.js", "fline": 1, "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.11364316940307617}, {"fpath": "downloaded_repos/neos_neos-ui/packages/neos-ui/src/manifest.dataloaders.js", "fline": 1, "rule_id": "javascript.express.security.express-data-exfiltration.express-data-exfiltration", "time": 0.11722707748413086}, {"fpath": "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/Endpoints/index.ts", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.14153599739074707}, {"fpath": "downloaded_repos/neos_neos-ui/packages/neos-ui-backend-connector/src/Endpoints/index.ts", "fline": 1, "rule_id": "javascript.express.security.cors-misconfiguration.cors-misconfiguration", "time": 0.19355201721191406}, {"fpath": "downloaded_repos/neos_neos-ui/packages/neos-ui-guest-frame/src/InlineUI/InlineValidationErrors/index.js", "fline": 68, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 1.12192702293396}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1125934528}, "engine_requested": "OSS", "skipped_rules": []}