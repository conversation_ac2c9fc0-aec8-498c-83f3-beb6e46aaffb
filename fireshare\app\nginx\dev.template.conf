#user  nginx;
worker_processes  1;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;

events {
    worker_connections  1024;
}

http {
    include       /usr/local/etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$http_x_forwarded_for - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$remote_addr"';

    server_tokens off;

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    keepalive_timeout  65;
    include /usr/local/etc/nginx/conf.d/*.conf;

    server {
        listen 80 default;
        server_name default;

        root /processed/;

        location /_content/ {
            rewrite ^/_content/(.*)$ /$1 break;
            root /processed/;
        }

        location /_content/video/ {
            rewrite ^/_content/video/(.*)$ /$1 break;
            root /processed/video_links/;
        }

        location / {
            root /app/build;
            index  index.html;
        }

        location ~ /api/.*$ {
            proxy_pass              http://localhost:5000;
            proxy_http_version      1.1;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header        Host $http_host;
            proxy_connect_timeout   60s;
            proxy_read_timeout      60s;
        }

        location ~ /w/.*$ {
            proxy_pass              http://localhost:5000;
            proxy_http_version      1.1;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header        Host $http_host;
            proxy_connect_timeout   60s;
            proxy_read_timeout      60s;
        }
    }

}
