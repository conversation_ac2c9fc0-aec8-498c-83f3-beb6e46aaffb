{"version": "1.130.0", "results": [{"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/daun_processwire-dashboard/DashboardPanelChart.js", "start": {"line": 1, "col": 7162, "offset": 7161}, "end": {"line": 1, "col": 7168, "offset": 7167}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/daun_processwire-dashboard/docs/index.html", "start": {"line": 9, "col": 3, "offset": 425}, "end": {"line": 9, "col": 110, "offset": 532}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/daun_processwire-dashboard/docs/index.html", "start": {"line": 86, "col": 3, "offset": 2818}, "end": {"line": 86, "col": 67, "offset": 2882}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/daun_processwire-dashboard/docs/index.html", "start": {"line": 87, "col": 3, "offset": 2886}, "end": {"line": 87, "col": 75, "offset": 2958}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/daun_processwire-dashboard/docs/index.html", "start": {"line": 88, "col": 3, "offset": 2962}, "end": {"line": 88, "col": 76, "offset": 3035}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/daun_processwire-dashboard/docs/index.html", "start": {"line": 89, "col": 3, "offset": 3039}, "end": {"line": 89, "col": 58, "offset": 3094}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/daun_processwire-dashboard/.browserslistrc", "downloaded_repos/daun_processwire-dashboard/.gitattributes", "downloaded_repos/daun_processwire-dashboard/.github/ISSUE_TEMPLATE/bug_report.md", "downloaded_repos/daun_processwire-dashboard/.github/ISSUE_TEMPLATE/documentation-improvement.md", "downloaded_repos/daun_processwire-dashboard/.github/ISSUE_TEMPLATE/feature_request.md", "downloaded_repos/daun_processwire-dashboard/.gitignore", "downloaded_repos/daun_processwire-dashboard/.postcssrc", "downloaded_repos/daun_processwire-dashboard/.styleci.yml", "downloaded_repos/daun_processwire-dashboard/.vscode/settings.json", "downloaded_repos/daun_processwire-dashboard/CHANGELOG.md", "downloaded_repos/daun_processwire-dashboard/CONTRIBUTING.md", "downloaded_repos/daun_processwire-dashboard/Dashboard.css", "downloaded_repos/daun_processwire-dashboard/Dashboard.js", "downloaded_repos/daun_processwire-dashboard/Dashboard.module", "downloaded_repos/daun_processwire-dashboard/DashboardPanel.class.php", "downloaded_repos/daun_processwire-dashboard/DashboardPanelAddNew.css", "downloaded_repos/daun_processwire-dashboard/DashboardPanelAddNew.module", "downloaded_repos/daun_processwire-dashboard/DashboardPanelChart.css", "downloaded_repos/daun_processwire-dashboard/DashboardPanelChart.js", "downloaded_repos/daun_processwire-dashboard/DashboardPanelChart.module", "downloaded_repos/daun_processwire-dashboard/DashboardPanelCollection.css", "downloaded_repos/daun_processwire-dashboard/DashboardPanelCollection.js", "downloaded_repos/daun_processwire-dashboard/DashboardPanelCollection.module", "downloaded_repos/daun_processwire-dashboard/DashboardPanelHelloWorld.css", "downloaded_repos/daun_processwire-dashboard/DashboardPanelHelloWorld.js", "downloaded_repos/daun_processwire-dashboard/DashboardPanelHelloWorld.module", "downloaded_repos/daun_processwire-dashboard/DashboardPanelInstance.class.php", "downloaded_repos/daun_processwire-dashboard/DashboardPanelNotice.css", "downloaded_repos/daun_processwire-dashboard/DashboardPanelNotice.module", "downloaded_repos/daun_processwire-dashboard/DashboardPanelNumber.css", "downloaded_repos/daun_processwire-dashboard/DashboardPanelNumber.module", "downloaded_repos/daun_processwire-dashboard/DashboardPanelPageList.css", "downloaded_repos/daun_processwire-dashboard/DashboardPanelPageList.js", "downloaded_repos/daun_processwire-dashboard/DashboardPanelPageList.module", "downloaded_repos/daun_processwire-dashboard/DashboardPanelShortcuts.css", "downloaded_repos/daun_processwire-dashboard/DashboardPanelShortcuts.module", "downloaded_repos/daun_processwire-dashboard/DashboardPanelTemplate.module", "downloaded_repos/daun_processwire-dashboard/LICENSE", "downloaded_repos/daun_processwire-dashboard/README.md", "downloaded_repos/daun_processwire-dashboard/VERSION", "downloaded_repos/daun_processwire-dashboard/composer.json", "downloaded_repos/daun_processwire-dashboard/composer.lock", "downloaded_repos/daun_processwire-dashboard/docs/.nojekyll", "downloaded_repos/daun_processwire-dashboard/docs/_sidebar.md", "downloaded_repos/daun_processwire-dashboard/docs/assets/prism-themes/prismjs-github.css", "downloaded_repos/daun_processwire-dashboard/docs/configuration.md", "downloaded_repos/daun_processwire-dashboard/docs/getting-started.md", "downloaded_repos/daun_processwire-dashboard/docs/images/add-new-dropdown.png", "downloaded_repos/daun_processwire-dashboard/docs/images/add-new.png", "downloaded_repos/daun_processwire-dashboard/docs/images/chart.png", "downloaded_repos/daun_processwire-dashboard/docs/images/collection.png", "downloaded_repos/daun_processwire-dashboard/docs/images/dashboard.png", "downloaded_repos/daun_processwire-dashboard/docs/images/groups.png", "downloaded_repos/daun_processwire-dashboard/docs/images/notice.png", "downloaded_repos/daun_processwire-dashboard/docs/images/number.png", "downloaded_repos/daun_processwire-dashboard/docs/images/page-list.png", "downloaded_repos/daun_processwire-dashboard/docs/images/shortcuts-comparison.png", "downloaded_repos/daun_processwire-dashboard/docs/images/shortcuts-grid.png", "downloaded_repos/daun_processwire-dashboard/docs/images/template.png", "downloaded_repos/daun_processwire-dashboard/docs/index.html", "downloaded_repos/daun_processwire-dashboard/docs/introduction.md", "downloaded_repos/daun_processwire-dashboard/docs/license.md", "downloaded_repos/daun_processwire-dashboard/docs/panels/add-new.md", "downloaded_repos/daun_processwire-dashboard/docs/panels/chart.md", "downloaded_repos/daun_processwire-dashboard/docs/panels/collection.md", "downloaded_repos/daun_processwire-dashboard/docs/panels/custom.md", "downloaded_repos/daun_processwire-dashboard/docs/panels/groups.md", "downloaded_repos/daun_processwire-dashboard/docs/panels/notice.md", "downloaded_repos/daun_processwire-dashboard/docs/panels/number.md", "downloaded_repos/daun_processwire-dashboard/docs/panels/page-list.md", "downloaded_repos/daun_processwire-dashboard/docs/panels/shortcuts.md", "downloaded_repos/daun_processwire-dashboard/docs/panels/tabs.md", "downloaded_repos/daun_processwire-dashboard/docs/panels/template.md", "downloaded_repos/daun_processwire-dashboard/docs/panels/third-party.md", "downloaded_repos/daun_processwire-dashboard/docs/panels.md", "downloaded_repos/daun_processwire-dashboard/docs/requirements.md", "downloaded_repos/daun_processwire-dashboard/package-lock.json", "downloaded_repos/daun_processwire-dashboard/package.json", "downloaded_repos/daun_processwire-dashboard/rector.php", "downloaded_repos/daun_processwire-dashboard/src/Dashboard.css", "downloaded_repos/daun_processwire-dashboard/src/Dashboard.js", "downloaded_repos/daun_processwire-dashboard/src/DashboardPanelAddNew.css", "downloaded_repos/daun_processwire-dashboard/src/DashboardPanelChart.css", "downloaded_repos/daun_processwire-dashboard/src/DashboardPanelChart.js", "downloaded_repos/daun_processwire-dashboard/src/DashboardPanelCollection.css", "downloaded_repos/daun_processwire-dashboard/src/DashboardPanelCollection.js", "downloaded_repos/daun_processwire-dashboard/src/DashboardPanelHelloWorld.css", "downloaded_repos/daun_processwire-dashboard/src/DashboardPanelHelloWorld.js", "downloaded_repos/daun_processwire-dashboard/src/DashboardPanelNotice.css", "downloaded_repos/daun_processwire-dashboard/src/DashboardPanelNumber.css", "downloaded_repos/daun_processwire-dashboard/src/DashboardPanelPageList.css", "downloaded_repos/daun_processwire-dashboard/src/DashboardPanelPageList.js", "downloaded_repos/daun_processwire-dashboard/src/DashboardPanelShortcuts.css", "downloaded_repos/daun_processwire-dashboard/src/charts/chartjs-defaults.js", "downloaded_repos/daun_processwire-dashboard/src/charts/color-themes.js", "downloaded_repos/daun_processwire-dashboard/src/lib/tooltips.js", "downloaded_repos/daun_processwire-dashboard/views/dashboard.php", "downloaded_repos/daun_processwire-dashboard/views/group.php", "downloaded_repos/daun_processwire-dashboard/views/panel.php", "downloaded_repos/daun_processwire-dashboard/views/panels/chart.php", "downloaded_repos/daun_processwire-dashboard/views/panels/number.php", "downloaded_repos/daun_processwire-dashboard/views/panels.php"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.9037230014801025, "profiling_times": {"config_time": 7.201409339904785, "core_time": 3.2304177284240723, "ignores_time": 0.0018584728240966797, "total_time": 10.43446946144104}, "parsing_time": {"total_time": 0.8061378002166748, "per_file_time": {"mean": 0.028790635722024106, "std_dev": 0.0014096916082453296}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 2.910133123397827, "per_file_time": {"mean": 0.012543677256025117, "std_dev": 0.0027758113417576656}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.7538256645202637, "per_file_and_rule_time": {"mean": 0.005935635153702865, "std_dev": 0.00010423627463995352}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.4036738872528076, "per_def_and_rule_time": {"mean": 0.0016476485193992149, "std_dev": 2.484945759600588e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}