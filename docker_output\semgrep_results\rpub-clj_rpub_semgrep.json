{"version": "1.130.0", "results": [{"check_id": "dockerfile.security.missing-user-entrypoint.missing-user-entrypoint", "path": "downloaded_repos/rpub-clj_rpub/Dockerfile", "start": {"line": 24, "col": 1, "offset": 910}, "end": {"line": 28, "col": 2, "offset": 1080}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nENTRYPOINT [ \\\n  \"bb\", \"-Sdeps\", \"{:deps {dev.rpub/rpub {:mvn/version \\\"0.2.0-SNAPSHOT\\\"}}}\", \\\n  \"-m\", \"rpub.tasks/supervisor\", \\\n  \"--mvn/version\", \"0.2.0-SNAPSHOT\" \\\n]", "metadata": {"cwe": ["CWE-269: Improper Privilege Management"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user-entrypoint.missing-user-entrypoint", "shortlink": "https://sg.run/k281"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/dev/rpub/dev/cljs/client.WOY7SUKL.js", "start": {"line": 3, "col": 13, "offset": 563}, "end": {"line": 3, "col": 24, "offset": 574}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-eval.tainted-eval", "message": "Timeout when running javascript.aws-lambda.security.tainted-eval.tainted-eval on downloaded_repos/rpub-clj_rpub/dev/public/js/rpub/dev/xyflow-react-bundle.js:\n ", "path": "downloaded_repos/rpub-clj_rpub/dev/public/js/rpub/dev/xyflow-react-bundle.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/rpub-clj_rpub/dev/public/js/rpub/dev/xyflow-react-bundle.js:\n ", "path": "downloaded_repos/rpub-clj_rpub/dev/public/js/rpub/dev/xyflow-react-bundle.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/rpub-clj_rpub/dev/public/js/rpub/dev/xyflow-react-bundle.js:\n ", "path": "downloaded_repos/rpub-clj_rpub/dev/public/js/rpub/dev/xyflow-react-bundle.js"}], "paths": {"scanned": ["downloaded_repos/rpub-clj_rpub/.github/workflows/ci.yml", "downloaded_repos/rpub-clj_rpub/.gitignore", "downloaded_repos/rpub-clj_rpub/.node-version", "downloaded_repos/rpub-clj_rpub/API.md", "downloaded_repos/rpub-clj_rpub/CHANGELOG.md", "downloaded_repos/rpub-clj_rpub/CREDITS.md", "downloaded_repos/rpub-clj_rpub/Dockerfile", "downloaded_repos/rpub-clj_rpub/Dockerfile.template", "downloaded_repos/rpub-clj_rpub/LICENSE", "downloaded_repos/rpub-clj_rpub/README.md", "downloaded_repos/rpub-clj_rpub/bb.edn", "downloaded_repos/rpub-clj_rpub/build.clj", "downloaded_repos/rpub-clj_rpub/build.cljs", "downloaded_repos/rpub-clj_rpub/cljfmt.edn", "downloaded_repos/rpub-clj_rpub/deps.edn", "downloaded_repos/rpub-clj_rpub/dev/public/js/rpub/dev/xyflow-react-bundle.js", "downloaded_repos/rpub-clj_rpub/dev/rpub/dev/cljs/client.cljs", "downloaded_repos/rpub-clj_rpub/dev/rpub/dev/cljs/repl.cljs", "downloaded_repos/rpub-clj_rpub/dev/rpub/dev/cljs/server.clj", "downloaded_repos/rpub-clj_rpub/dev/rpub/dev/dag/metadata/all-content-types-page.edn", "downloaded_repos/rpub-clj_rpub/dev/rpub/dev/dag/metadata/new-user-page.edn", "downloaded_repos/rpub-clj_rpub/dev/rpub/dev/dag/viz/aliases.cljs", "downloaded_repos/rpub-clj_rpub/dev/rpub/dev/dag/viz.cljs", "downloaded_repos/rpub-clj_rpub/dev/rpub/dev/repl.clj", "downloaded_repos/rpub-clj_rpub/dev/rpub/dev/tasks/deploy.clj", "downloaded_repos/rpub-clj_rpub/dev/rpub/dev/tasks.clj", "downloaded_repos/rpub-clj_rpub/dev/rpub/plugins/admin/dev.clj", "downloaded_repos/rpub-clj_rpub/docker-compose.template.yaml", "downloaded_repos/rpub-clj_rpub/docker-compose.yaml", "downloaded_repos/rpub-clj_rpub/package-lock.json", "downloaded_repos/rpub-clj_rpub/package.json", "downloaded_repos/rpub-clj_rpub/resources/css/admin/postcss.config.mjs", "downloaded_repos/rpub-clj_rpub/resources/css/admin/tailwind.css", "downloaded_repos/rpub-clj_rpub/resources/data/app.clj", "downloaded_repos/rpub-clj_rpub/resources/data/deps.edn", "downloaded_repos/rpub-clj_rpub/resources/public/css/admin/main.259d2e1f74223ab61d76.css", "downloaded_repos/rpub-clj_rpub/resources/public/css/admin/manifest.json", "downloaded_repos/rpub-clj_rpub/resources/public/js/manifest.json", "downloaded_repos/rpub-clj_rpub/resources/public/js/rads/dnd.mjs", "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/dev/rpub/dev/cljs/client.WOY7SUKL.js", "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/dev/rpub/dev/cljs/repl.7CR7ZDMF.js", "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/dev/rpub/dev/dag/viz/aliases.FYU7Y7TY.js", "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/dev/rpub/dev/dag/viz.Z3TANBRF.js", "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/lib/dag/react.HYSUNOXA.js", "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/lib/dag.KVY5FYQ4.js", "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/lib/forms.Y7LWWPIV.js", "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/lib/html.2MDLCRUR.js", "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/lib/http.6P6TLNCZ.js", "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/lib/permalinks.JPNB7HOC.js", "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/lib/reagent.N4JGNTND.js", "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/lib/tap.HEHYOO2E.js", "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/lib/transit.TN53Z6VC.js", "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/admin/dashboard/page.KJMINFVZ.js", "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/admin/helpers.67O7TOVG.js", "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/admin/plugins/page.L6J43TRM.js", "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/admin/roles.YUAZV7S7.js", "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/admin/settings/page.EGBMECCR.js", "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/admin/themes/all_themes_page.KP5WPRNR.js", "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/admin/themes/single_theme_page.ACR4EECU.js", "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/admin/users/all_users_page.XFJB5BHJ.js", "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/admin/users/single_user_page.UUZJYZVO.js", "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/admin.EFZERDRK.js", "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/content_types/admin/all_content_types_page.BLVR2JHI.js", "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/content_types/admin/single_content_item_page.WQFRUIGP.js", "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/content_types/admin/single_content_type_page.AFEKEACD.js", "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/content_types/admin.DQNRTA5G.js", "downloaded_repos/rpub-clj_rpub/src/data_readers.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/core.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/lib/dag/react.cljs", "downloaded_repos/rpub-clj_rpub/src/rpub/lib/dag.cljc", "downloaded_repos/rpub-clj_rpub/src/rpub/lib/db.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/lib/deps.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/lib/edn.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/lib/forms.cljs", "downloaded_repos/rpub-clj_rpub/src/rpub/lib/git.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/lib/html.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/lib/html.cljs", "downloaded_repos/rpub-clj_rpub/src/rpub/lib/http.cljs", "downloaded_repos/rpub-clj_rpub/src/rpub/lib/logs.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/lib/malli.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/lib/otel.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/lib/permalinks.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/lib/permalinks.cljs", "downloaded_repos/rpub-clj_rpub/src/rpub/lib/plugins.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/lib/reagent.cljs", "downloaded_repos/rpub-clj_rpub/src/rpub/lib/ring.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/lib/router.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/lib/secrets.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/lib/tap.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/lib/tap.cljs", "downloaded_repos/rpub-clj_rpub/src/rpub/lib/transit.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/lib/transit.cljs", "downloaded_repos/rpub-clj_rpub/src/rpub/main.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/model/apps/sqlite.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/model/apps.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/model/common.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/model/content_types.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/model/plugins/sqlite.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/model/plugins.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/model/settings/sqlite.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/model/settings.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/model/sqlite/migrations.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/model/sqlite.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/model/themes/sqlite.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/model/themes.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/model/unsaved_changes/sqlite.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/model/unsaved_changes.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/model/users/sqlite.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/model/users.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/model.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/plugins/admin/dashboard/page.cljs", "downloaded_repos/rpub-clj_rpub/src/rpub/plugins/admin/dashboard.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/plugins/admin/helpers.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/plugins/admin/helpers.cljs", "downloaded_repos/rpub-clj_rpub/src/rpub/plugins/admin/login.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/plugins/admin/plugins/page.cljs", "downloaded_repos/rpub-clj_rpub/src/rpub/plugins/admin/plugins.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/plugins/admin/roles.cljc", "downloaded_repos/rpub-clj_rpub/src/rpub/plugins/admin/settings/page.cljs", "downloaded_repos/rpub-clj_rpub/src/rpub/plugins/admin/settings.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/plugins/admin/setup.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/plugins/admin/themes/all_themes_page.cljs", "downloaded_repos/rpub-clj_rpub/src/rpub/plugins/admin/themes/single_theme_page.cljs", "downloaded_repos/rpub-clj_rpub/src/rpub/plugins/admin/themes.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/plugins/admin/unsaved_changes.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/plugins/admin/users/all_users_page.cljs", "downloaded_repos/rpub-clj_rpub/src/rpub/plugins/admin/users/single_user_page.cljs", "downloaded_repos/rpub-clj_rpub/src/rpub/plugins/admin/users.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/plugins/admin.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/plugins/admin.cljs", "downloaded_repos/rpub-clj_rpub/src/rpub/plugins/api.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/plugins/app/helpers.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/plugins/app.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/plugins/content_types/admin/all_content_types_page.cljs", "downloaded_repos/rpub-clj_rpub/src/rpub/plugins/content_types/admin/single_content_item_page.cljs", "downloaded_repos/rpub-clj_rpub/src/rpub/plugins/content_types/admin/single_content_type_page.cljs", "downloaded_repos/rpub-clj_rpub/src/rpub/plugins/content_types/admin.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/plugins/content_types/admin.cljs", "downloaded_repos/rpub-clj_rpub/src/rpub/plugins/content_types/sqlite/migrations.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/plugins/content_types/sqlite.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/plugins/content_types.clj", "downloaded_repos/rpub-clj_rpub/src/rpub/tasks.clj"], "skipped": [{"path": "downloaded_repos/rpub-clj_rpub/dev/public/js/rpub/dev/xyflow-react-bundle.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/rpub-clj_rpub/test/rpub/core_test.clj", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/rpub-clj_rpub/test/rpub/lib/router_test.clj", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/rpub-clj_rpub/test/rpub/main_test.clj", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/rpub-clj_rpub/test/rpub/plugins/admin_test.clj", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/rpub-clj_rpub/test/rpub/plugins/app_test.clj", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/rpub-clj_rpub/test/rpub/test_util.clj", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6114070415496826, "profiling_times": {"config_time": 5.821104049682617, "core_time": 19.191158533096313, "ignores_time": 0.0017886161804199219, "total_time": 25.015204668045044}, "parsing_time": {"total_time": 6.556986093521118, "per_file_time": {"mean": 0.050053328958176484, "std_dev": 0.011280910204452783}, "very_slow_stats": {"time_ratio": 0.32195244904614734, "count_ratio": 0.030534351145038167}, "very_slow_files": [{"fpath": "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/admin/themes/all_themes_page.KP5WPRNR.js", "ftime": 0.3015859127044678}, {"fpath": "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/lib/html.2MDLCRUR.js", "ftime": 0.38925790786743164}, {"fpath": "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/admin/helpers.67O7TOVG.js", "ftime": 0.4245278835296631}, {"fpath": "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/content_types/admin/all_content_types_page.BLVR2JHI.js", "ftime": 0.9956660270690918}]}, "scanning_time": {"total_time": 58.378029108047485, "per_file_time": {"mean": 0.14033180074049878, "std_dev": 0.9084485434953452}, "very_slow_stats": {"time_ratio": 0.7532020649216269, "count_ratio": 0.02403846153846154}, "very_slow_files": [{"fpath": "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/admin/users/all_users_page.XFJB5BHJ.js", "ftime": 1.763214111328125}, {"fpath": "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/admin/settings/page.EGBMECCR.js", "ftime": 1.7933449745178223}, {"fpath": "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/admin/plugins/page.L6J43TRM.js", "ftime": 2.166059970855713}, {"fpath": "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/dev/rpub/dev/dag/viz.Z3TANBRF.js", "ftime": 2.1784210205078125}, {"fpath": "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/content_types/admin/single_content_item_page.WQFRUIGP.js", "ftime": 2.626797914505005}, {"fpath": "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/admin/themes/all_themes_page.KP5WPRNR.js", "ftime": 2.966032028198242}, {"fpath": "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/admin/helpers.67O7TOVG.js", "ftime": 3.3794970512390137}, {"fpath": "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/lib/html.2MDLCRUR.js", "ftime": 3.582671880722046}, {"fpath": "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/content_types/admin/all_content_types_page.BLVR2JHI.js", "ftime": 6.932348966598511}, {"fpath": "downloaded_repos/rpub-clj_rpub/dev/public/js/rpub/dev/xyflow-react-bundle.js", "ftime": 16.582064151763916}]}, "matching_time": {"total_time": 24.32010006904602, "per_file_and_rule_time": {"mean": 0.04319733582423805, "std_dev": 0.020404429731295293}, "very_slow_stats": {"time_ratio": 0.7788191903775599, "count_ratio": 0.108348134991119}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/admin/helpers.67O7TOVG.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.49095678329467773}, {"fpath": "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/content_types/admin/all_content_types_page.BLVR2JHI.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.5020229816436768}, {"fpath": "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/admin/settings/page.EGBMECCR.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.5041899681091309}, {"fpath": "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/admin/dashboard/page.KJMINFVZ.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.5168681144714355}, {"fpath": "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/content_types/admin/single_content_item_page.WQFRUIGP.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.5413920879364014}, {"fpath": "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/admin/themes/single_theme_page.ACR4EECU.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.6012468338012695}, {"fpath": "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/admin/plugins/page.L6J43TRM.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.6488571166992188}, {"fpath": "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/lib/html.2MDLCRUR.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.7712240219116211}, {"fpath": "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/content_types/admin/all_content_types_page.BLVR2JHI.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.9535031318664551}, {"fpath": "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/content_types/admin/all_content_types_page.BLVR2JHI.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 2.420253038406372}]}, "tainting_time": {"total_time": 3.8319954872131348, "per_def_and_rule_time": {"mean": 0.020939866050344997, "std_dev": 0.0010851396109914748}, "very_slow_stats": {"time_ratio": 0.5359651614796255, "count_ratio": 0.12568306010928962}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/admin/helpers.67O7TOVG.js", "fline": 1, "rule_id": "javascript.express.security.cors-misconfiguration.cors-misconfiguration", "time": 0.08364200592041016}, {"fpath": "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/admin/themes/all_themes_page.KP5WPRNR.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.08505702018737793}, {"fpath": "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/lib/html.2MDLCRUR.js", "fline": 1, "rule_id": "javascript.express.security.cors-misconfiguration.cors-misconfiguration", "time": 0.08542203903198242}, {"fpath": "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/admin/themes/all_themes_page.KP5WPRNR.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.10311198234558105}, {"fpath": "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/lib/html.2MDLCRUR.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.10369110107421875}, {"fpath": "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/admin/helpers.67O7TOVG.js", "fline": 1, "rule_id": "javascript.express.security.x-frame-options-misconfiguration.x-frame-options-misconfiguration", "time": 0.1050410270690918}, {"fpath": "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/lib/html.2MDLCRUR.js", "fline": 1, "rule_id": "javascript.express.security.x-frame-options-misconfiguration.x-frame-options-misconfiguration", "time": 0.12060403823852539}, {"fpath": "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/content_types/admin/all_content_types_page.BLVR2JHI.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.1521310806274414}, {"fpath": "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/admin/helpers.67O7TOVG.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.15774297714233398}, {"fpath": "downloaded_repos/rpub-clj_rpub/resources/public/js/rpub/src/rpub/plugins/admin/helpers.67O7TOVG.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.21934103965759277}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}