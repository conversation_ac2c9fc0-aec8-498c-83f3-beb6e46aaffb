#!/usr/bin/env python3
"""
🛡️ Safe Test Environment Setup
==============================

This script helps you set up a safe, isolated environment for testing
command injection vulnerabilities. It creates a controlled environment
where you can safely learn about security vulnerabilities.

⚠️  IMPORTANT: Only test on systems you own or have explicit permission to test!
"""

import subprocess
import sys
import os
import time
from pathlib import Path

class SafeTestEnvironment:
    def __init__(self):
        self.project_dir = Path.cwd()
        print("🛡️ Safe Test Environment Setup")
        print("=" * 35)
        print(f"Working directory: {self.project_dir}")
        print()

    def check_prerequisites(self):
        """Check if required tools are installed"""
        print("🔍 Checking prerequisites...")
        
        required_tools = ['docker', 'docker-compose']
        missing_tools = []
        
        for tool in required_tools:
            try:
                result = subprocess.run([tool, '--version'], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    print(f"✅ {tool} is installed")
                else:
                    missing_tools.append(tool)
            except FileNotFoundError:
                missing_tools.append(tool)
                print(f"❌ {tool} is not installed")
        
        if missing_tools:
            print(f"\n❌ Missing tools: {', '.join(missing_tools)}")
            print("Please install Docker and Docker Compose before continuing.")
            return False
        
        print("✅ All prerequisites met!")
        return True

    def create_isolated_network(self):
        """Create an isolated Docker network for testing"""
        print("\n🌐 Creating isolated test network...")
        
        try:
            # Create isolated network
            subprocess.run([
                'docker', 'network', 'create', 
                '--driver', 'bridge',
                '--internal',  # No external access
                'fireshare-test-net'
            ], check=True, capture_output=True)
            print("✅ Isolated network created: fireshare-test-net")
            return True
        except subprocess.CalledProcessError:
            print("ℹ️  Network might already exist, continuing...")
            return True
        except Exception as e:
            print(f"❌ Failed to create network: {e}")
            return False

    def setup_vulnerable_instance(self):
        """Set up a vulnerable Fireshare instance for testing"""
        print("\n🎯 Setting up vulnerable test instance...")
        
        # Check if docker-compose.yml exists
        compose_file = self.project_dir / 'docker-compose.yml'
        if not compose_file.exists():
            print("❌ docker-compose.yml not found!")
            print("Make sure you're in the Fireshare project directory.")
            return False
        
        try:
            print("🚀 Starting Fireshare with Docker Compose...")
            
            # Start the services
            subprocess.run([
                'docker-compose', 'up', '-d'
            ], check=True, cwd=self.project_dir)
            
            print("✅ Fireshare instance started!")
            print("🔗 URL: http://localhost:8080")
            print("👤 Default credentials: admin / admin")
            
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to start Fireshare: {e}")
            return False

    def wait_for_service(self, url="http://localhost:8080", timeout=60):
        """Wait for the Fireshare service to be ready"""
        print(f"\n⏳ Waiting for service at {url} to be ready...")
        
        import requests
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                response = requests.get(url, timeout=5)
                if response.status_code == 200:
                    print("✅ Service is ready!")
                    return True
            except:
                pass
            
            print(".", end="", flush=True)
            time.sleep(2)
        
        print(f"\n❌ Service did not become ready within {timeout} seconds")
        return False

    def create_test_script(self):
        """Create a simple test script for the user"""
        test_script = """#!/bin/bash
# Quick test script for command injection

echo "🧪 Testing Command Injection Vulnerability"
echo "=========================================="

# Test 1: Basic whoami injection
echo "Test 1: Basic command injection (whoami)"
python3 command_injection_demo.py http://localhost:8080 --basic-only

echo ""
echo "Test 2: Full demonstration"
python3 command_injection_demo.py http://localhost:8080 -u admin -p admin

echo ""
echo "🎓 Learning complete! Check the output above."
"""
        
        script_path = self.project_dir / 'run_tests.sh'
        script_path.write_text(test_script)
        script_path.chmod(0o755)
        
        print(f"✅ Test script created: {script_path}")
        return script_path

    def show_learning_guide(self):
        """Show a guide for learning about command injection"""
        print("\n📚 COMMAND INJECTION LEARNING GUIDE")
        print("=" * 40)
        print()
        print("🎯 STEP 1: Understand the Vulnerability")
        print("   Run: python3 command_injection_demo.py http://localhost:8080 --explain-only")
        print()
        print("🧪 STEP 2: Test Basic Injection")
        print("   Run: python3 command_injection_demo.py http://localhost:8080 --basic-only")
        print()
        print("🔥 STEP 3: Advanced Testing")
        print("   Run: python3 command_injection_demo.py http://localhost:8080 -u admin -p admin")
        print()
        print("🔍 STEP 4: Examine the Code")
        print("   Look at: app/server/fireshare/api.py lines 269 and 303")
        print("   Find the vulnerable Popen() calls")
        print()
        print("🛡️ STEP 5: Learn Prevention")
        print("   Study the prevention methods shown in the demo")
        print("   Practice writing secure code")
        print()
        print("📖 ADDITIONAL LEARNING:")
        print("   - OWASP Command Injection Guide")
        print("   - CWE-78: OS Command Injection")
        print("   - Practice on other vulnerable apps (DVWA, WebGoat)")

    def cleanup_environment(self):
        """Clean up the test environment"""
        print("\n🧹 Cleaning up test environment...")
        
        try:
            # Stop docker-compose services
            subprocess.run([
                'docker-compose', 'down'
            ], check=True, cwd=self.project_dir, capture_output=True)
            print("✅ Stopped Fireshare services")
            
            # Remove test network
            subprocess.run([
                'docker', 'network', 'rm', 'fireshare-test-net'
            ], capture_output=True)
            print("✅ Removed test network")
            
        except Exception as e:
            print(f"⚠️  Cleanup warning: {e}")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Set up safe test environment for command injection learning")
    parser.add_argument("--cleanup", action="store_true", help="Clean up test environment")
    parser.add_argument("--guide-only", action="store_true", help="Show learning guide only")
    
    args = parser.parse_args()
    
    env = SafeTestEnvironment()
    
    if args.cleanup:
        env.cleanup_environment()
        return
    
    if args.guide_only:
        env.show_learning_guide()
        return
    
    # Full setup process
    print("🎓 Welcome to Command Injection Security Research Training!")
    print("This will set up a safe, isolated environment for learning.")
    print()
    
    # Check prerequisites
    if not env.check_prerequisites():
        sys.exit(1)
    
    # Create isolated network
    if not env.create_isolated_network():
        print("❌ Failed to create isolated network")
        sys.exit(1)
    
    # Set up vulnerable instance
    if not env.setup_vulnerable_instance():
        print("❌ Failed to set up test instance")
        sys.exit(1)
    
    # Wait for service
    if not env.wait_for_service():
        print("❌ Service failed to start properly")
        sys.exit(1)
    
    # Create test script
    test_script = env.create_test_script()
    
    # Show learning guide
    env.show_learning_guide()
    
    print("\n🎉 SETUP COMPLETE!")
    print("=" * 20)
    print("✅ Vulnerable Fireshare instance is running at http://localhost:8080")
    print("✅ Default credentials: admin / admin")
    print("✅ Test script created: run_tests.sh")
    print()
    print("🚀 Ready to start learning! Run:")
    print("   python3 command_injection_demo.py http://localhost:8080 --explain-only")
    print()
    print("🧹 When done, clean up with:")
    print("   python3 setup_safe_test_environment.py --cleanup")
    print()
    print("⚠️  Remember: This is for educational purposes only!")
    print("   Only test on systems you own or have permission to test.")

if __name__ == "__main__":
    main()
