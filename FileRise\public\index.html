<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title data-i18n-key="title">FileRise</title>
  <link rel="icon" type="image/png" href="/assets/logo.png">
  <link rel="icon" type="image/svg+xml" href="/assets/logo.svg">
  <meta name="csrf-token" content="">
  <meta name="share-url" content="">
  <style>
    /* hide the app shell until JS says otherwise */
    .main-wrapper {
      display: none;
    }

    /* full-screen white overlay while we check auth */
    #loadingOverlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: var(--bg-color, #fff);
      z-index: 9999;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  </style>
  <!-- Google Fonts and Material Icons -->
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500&display=swap" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" />
  <!-- Bootstrap CSS -->
  <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css"
    integrity="sha384-JcKb8q3iqJ61gNV9KGb8thSsNjpSL0n8PARn9HuZOnIxN0hoP+VmmDGMN5t9UJ0Z" crossorigin="anonymous">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.5/codemirror.min.css"
    integrity="sha384-zaeBlB/vwYsDRSlFajnDd7OydJ0cWk+c2OWybl3eSUf6hW2EbhlCsQPqKr3gkznT" crossorigin="anonymous">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.5/theme/material-darker.min.css"
    integrity="sha384-eZTPTN0EvJdn23s24UDYJmUM2T7C2ZFa3qFLypeBruJv8mZeTusKUAO/j5zPAQ6l" crossorigin="anonymous">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.5/codemirror.min.js"
    integrity="sha384-UXbkZAbZYZ/KCAslc6UO4d6UHNKsOxZ/sqROSQaPTZCuEIKhfbhmffQ64uXFOcma"
    crossorigin="anonymous"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.5/mode/xml/xml.min.js"
    integrity="sha384-xPpkMo5nDgD98fIcuRVYhxkZV6/9Y4L8s3p0J5c4MxgJkyKJ8BJr+xfRkq7kn6Tw"
    crossorigin="anonymous"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.5/mode/css/css.min.js"
    integrity="sha384-to8njsu2GAiXQnY/aLGzz0DIY/SFSeSDodtvSl869n2NmsBdHOTZNNqbEBPYh7Pa"
    crossorigin="anonymous"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.5/mode/javascript/javascript.min.js"
    integrity="sha384-kmQrbJf09Uo1WRLMDVGoVG3nM6F48frIhcj7f3FDUjeRzsiHwyBWDjMUIttnIeAf"
    crossorigin="anonymous"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/resumable.js/1.1.0/resumable.min.js"
    integrity="sha384-EXTg7rRfdTPZWoKVCslusAAev2TYw76fm+Wox718iEtFQ+gdAdAc5Z/ndLHSo4mq"
    crossorigin="anonymous"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/dompurify/2.4.0/purify.min.js"
    integrity="sha384-Tsl3d5pUAO7a13enIvSsL3O0/95nsthPJiPto5NtLuY8w3+LbZOpr3Fl2MNmrh1E"
    crossorigin="anonymous"></script>
  <script src="https://cdn.jsdelivr.net/npm/fuse.js@6.6.2/dist/fuse.min.js"
    integrity="sha384-zPE55eyESN+FxCWGEnlNxGyAPJud6IZ6TtJmXb56OFRGhxZPN4akj9rjA3gw5Qqa"
    crossorigin="anonymous"></script>
  <link rel="stylesheet" href="css/styles.css" />
</head>

<body>
  <header class="header-container">
    <div class="header-left">
      <a href="index.html">
        <div class="header-logo">
          <svg version="1.1" id="filingCabinetLogo" xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 64 64" xml:space="preserve">
            <defs>
              <!-- Gradient for the cabinet body -->
              <linearGradient id="cabinetGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" style="stop-color:#2196F3;stop-opacity:1" />
                <stop offset="100%" style="stop-color:#1976D2;stop-opacity:1" />
              </linearGradient>
              <!-- Drop shadow filter with animated attributes for a lifting effect -->
              <filter id="shadowFilter" x="-20%" y="-20%" width="140%" height="140%">
                <feDropShadow id="dropShadow" dx="0" dy="2" stdDeviation="2" flood-color="#000" flood-opacity="0.2">
                  <!-- Animate the vertical offset: from 2 to 1 (as it rises), hold, then back to 2 -->
                  <animate attributeName="dy" values="2;1;1;2" keyTimes="0;0.2;0.8;1" dur="5s" fill="freeze" />
                  <!-- Animate the blur similarly: from 2 to 1.5 then back to 2 -->
                  <animate attributeName="stdDeviation" values="2;1.5;1.5;2" keyTimes="0;0.2;0.8;1" dur="5s"
                    fill="freeze" />
                </feDropShadow>
              </filter>
            </defs>
            <style type="text/css">
              /* Cabinet with gradient, white outline, and drop shadow */
              .cabinet {
                fill: url(#cabinetGradient);
                stroke: white;
                stroke-width: 2;
              }

              .divider {
                stroke: #1565C0;
                stroke-width: 1.5;
              }

              .drawer {
                fill: #FFFFFF;
              }

              .handle {
                fill: #1565C0;
              }
            </style>
            <!-- Group that will animate upward and then back down once -->
            <g id="cabinetGroup">
              <!-- Cabinet Body with rounded corners, white outline, and drop shadow -->
              <rect x="4" y="4" width="56" height="56" rx="6" ry="6" class="cabinet" filter="url(#shadowFilter)" />
              <!-- Divider lines for drawers -->
              <line x1="5" y1="22" x2="59" y2="22" class="divider" />
              <line x1="5" y1="34" x2="59" y2="34" class="divider" />
              <!-- Drawers with Handles -->
              <rect x="8" y="24" width="48" height="6" rx="1" ry="1" class="drawer" />
              <circle cx="54" cy="27" r="1.5" class="handle" />
              <rect x="8" y="36" width="48" height="6" rx="1" ry="1" class="drawer" />
              <circle cx="54" cy="39" r="1.5" class="handle" />
              <rect x="8" y="48" width="48" height="6" rx="1" ry="1" class="drawer" />
              <circle cx="54" cy="51" r="1.5" class="handle" />
              <!-- Additional detail: a small top handle on the cabinet door -->
              <rect x="28" y="10" width="8" height="4" rx="1" ry="1" fill="#1565C0" />
              <!-- Animate transform: rises by 2 pixels over 1s, holds for 3s, then falls over 1s (total 5s) -->
              <animateTransform attributeName="transform" type="translate" values="0 0; 0 -2; 0 -2; 0 0"
                keyTimes="0;0.2;0.8;1" dur="5s" fill="freeze" />
            </g>
          </svg>
        </div>
      </a>
    </div>
    <div class="header-title">
      <h1 data-i18n-key="header_title">FileRise</h1>
    </div>
    <div class="header-right">
      <div class="header-buttons-wrapper" style="display: flex; align-items: center; gap: 10px;">
        <!-- Your header drop zone -->
        <div id="headerDropArea" class="header-drop-zone"></div>
        <div class="header-buttons">
          <button id="changePasswordBtn" data-i18n-title="change_password" style="display: none;">
            <i class="material-icons">vpn_key</i>
          </button>
          <div id="restoreFilesModal" class="modal centered-modal" style="display: none;">
            <div class="modal-content">
              <h4 class="custom-restore-header">
                <i class="material-icons orange-icon">restore_from_trash</i>
                <span data-i18n-key="restore_text">Restore or</span>
                <i class="material-icons red-icon">delete_for_ever</i>
                <span data-i18n-key="delete_text">Delete Trash Items</span>
              </h4>
              <div id="restoreFilesList"
                style="max-height:300px; overflow-y:auto; border:1px solid #ccc; padding:10px; margin-bottom:10px;">
                <!-- Trash items will be loaded here -->
              </div>
              <div style="text-align: right;">
                <button id="restoreSelectedBtn" class="btn btn-primary" data-i18n-key="restore_selected">Restore
                  Selected</button>
                <button id="restoreAllBtn" class="btn btn-secondary" data-i18n-key="restore_all">Restore All</button>
                <button id="deleteTrashSelectedBtn" class="btn btn-warning" data-i18n-key="delete_selected_trash">Delete
                  Selected</button>
                <button id="deleteAllBtn" class="btn btn-danger" data-i18n-key="delete_all">Delete All</button>
                <button id="closeRestoreModal" class="btn btn-dark" data-i18n-key="close">Close</button>
              </div>
            </div>
          </div>
          <button id="addUserBtn" data-i18n-title="add_user" style="display: none;">
            <i class="material-icons">person_add</i>
          </button>
          <button id="removeUserBtn" data-i18n-title="remove_user" style="display: none;">
            <i class="material-icons">person_remove</i>
          </button>
          <button id="darkModeToggle" class="btn-icon" aria-label="Toggle dark mode">
            <span class="material-icons" id="darkModeIcon">
              dark_mode
            </span>
          </button>
        </div>
      </div>
    </div>
  </header>

  <div id="loadingOverlay"></div>

  <!-- Custom Toast Container -->
  <div id="customToast"></div>
  <div id="hiddenCardsContainer" style="display:none;"></div>

  <div class="row mt-4" id="loginForm">
    <div class="col-12">
      <form id="authForm" method="post">
        <div class="form-group">
          <label for="loginUsername" data-i18n-key="user">User:</label>
          <input type="text" class="form-control" id="loginUsername" name="username" required autofocus />
        </div>
        <div class="form-group">
          <label for="loginPassword" data-i18n-key="password">Password:</label>
          <input type="password" class="form-control" id="loginPassword" name="password" required />
        </div>
        <button type="submit" class="btn btn-primary btn-block btn-login" data-i18n-key="login">Login</button>
        <div class="form-group remember-me-container">
          <input type="checkbox" id="rememberMeCheckbox" name="remember_me" />
          <label for="rememberMeCheckbox" data-i18n-key="remember_me">Remember me</label>
        </div>
      </form>
      <!-- OIDC Login Option -->
      <div class="text-center mt-3">
        <button id="oidcLoginBtn" class="btn btn-secondary" data-i18n-key="login_oidc">Login with OIDC</button>
      </div>
      <!-- Basic HTTP Login Option -->
      <div class="text-center mt-3">
        <a href="/api/auth/login_basic.php" class="btn btn-secondary" data-i18n-key="basic_http_login">Use Basic
          HTTP
          Login</a>
      </div>
    </div>
  </div>

  <!-- Main Wrapper: Hidden by default; remove "display: none;" after login -->
  <div class="main-wrapper">
    <!-- Sidebar Drop Zone: Hidden until you drag a card (display controlled by JS) -->
    <div id="sidebarDropArea" class="drop-target-sidebar"></div>
    <!-- Main Column -->
    <div id="mainColumn" class="main-column">
      <div class="container-fluid">
        <!-- Main Operations: Upload and Folder Management -->
        <div id="mainOperations">
          <div class="container" style="max-width: 1400px; margin: 0 auto;">
            <!-- Top Zone: Two columns (60% and 40%) -->
            <div id="uploadFolderRow" class="row">
              <!-- Left Column (60% for Upload Card) -->
              <div id="leftCol" class="col-md-7" style="display: flex; justify-content: center;">
                <div id="uploadCard" class="card" style="width: 100%;">
                  <div class="card-header" data-i18n-key="upload_header">Upload Files/Folders</div>
                  <div class="card-body d-flex flex-column">
                    <form id="uploadFileForm" method="post" enctype="multipart/form-data" class="d-flex flex-column">
                      <div class="form-group flex-grow-1" style="margin-bottom: 1rem;">
                        <div id="uploadDropArea"
                          style="border:2px dashed #ccc; padding:20px; cursor:pointer; display:flex; flex-direction:column; justify-content:center; align-items:center; position:relative;">
                          <span data-i18n-key="upload_instruction">Drop files/folders here or click 'Choose
                            Files'</span>
                          <br />
                          <input type="file" id="file" name="file[]" class="form-control-file" multiple
                            style="opacity:0; position:absolute; width:1px; height:1px;" />
                          <button type="button" id="customChooseBtn" data-i18n-key="choose_files">Choose Files</button>
                        </div>
                      </div>
                      <button type="submit" id="uploadBtn" class="btn btn-primary d-block mx-auto"
                        data-i18n-key="upload">Upload</button>
                      <div id="uploadProgressContainer"></div>
                    </form>
                  </div>
                </div>
              </div>
              <!-- Right Column (40% for Folder Management Card) -->
              <div id="rightCol" class="col-md-5" style="display: flex; justify-content: center;">
                <div id="folderManagementCard" class="card" style="width: 100%; position: relative;">
                  <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
                    <span data-i18n-key="folder_navigation">Folder Navigation &amp; Management</span>
                    <button id="folderHelpBtn" class="btn btn-link" data-i18n-title="folder_help"
                      style="padding: 0; border: none; background: none;">
                      <i class="material-icons folder-help-icon" style="font-size: 24px;">info</i>
                    </button>
                  </div>
                  <div class="card-body custom-folder-card-body">
                    <div class="form-group d-flex align-items-top" style="padding-top:0; margin-bottom:0;">
                      <div id="folderTreeContainer"></div>
                    </div>
                    <div class="folder-actions mt-3">
                      <button id="createFolderBtn" class="btn btn-primary" data-i18n-title="create_folder">
                        <i class="material-icons">create_new_folder</i>
                      </button>
                      <div id="createFolderModal" class="modal">
                        <div class="modal-content">
                          <h4 data-i18n-key="create_folder_title">Create Folder</h4>
                          <input type="text" id="newFolderName" class="form-control"
                            data-i18n-placeholder="enter_folder_name" placeholder="Enter folder name"
                            style="margin-top:10px;" />
                          <div style="margin-top:15px; text-align:right;">
                            <button id="cancelCreateFolder" class="btn btn-secondary"
                              data-i18n-key="cancel">Cancel</button>
                            <button id="submitCreateFolder" class="btn btn-primary"
                              data-i18n-key="create">Create</button>
                          </div>
                        </div>
                      </div>
                      <button id="renameFolderBtn" class="btn btn-warning ml-2" data-i18n-title="rename_folder">
                        <i class="material-icons">drive_file_rename_outline</i>
                      </button>
                      <div id="renameFolderModal" class="modal">
                        <div class="modal-content">
                          <h4 data-i18n-key="rename_folder_title">Rename Folder</h4>
                          <input type="text" id="newRenameFolderName" class="form-control"
                            data-i18n-placeholder="rename_folder_placeholder" placeholder="Enter new folder name"
                            style="margin-top:10px;" />
                          <div style="margin-top:15px; text-align:right;">
                            <button id="cancelRenameFolder" class="btn btn-secondary"
                              data-i18n-key="cancel">Cancel</button>
                            <button id="submitRenameFolder" class="btn btn-primary"
                              data-i18n-key="rename">Rename</button>
                          </div>
                        </div>
                      </div>

                      <button id="shareFolderBtn" class="btn btn-secondary ml-2" data-i18n-title="share_folder">
                        <i class="material-icons">share</i>
                      </button>
                      <button id="deleteFolderBtn" class="btn btn-danger ml-2" data-i18n-title="delete_folder">
                        <i class="material-icons">delete</i>
                      </button>
                      <div id="deleteFolderModal" class="modal">
                        <div class="modal-content">
                          <h4 data-i18n-key="delete_folder_title">Delete Folder</h4>
                          <p id="deleteFolderMessage" data-i18n-key="delete_folder_message">Are you sure you want to
                            delete this folder?</p>
                          <div style="margin-top:15px; text-align:right;">
                            <button id="cancelDeleteFolder" class="btn btn-secondary"
                              data-i18n-key="cancel">Cancel</button>
                            <button id="confirmDeleteFolder" class="btn btn-danger"
                              data-i18n-key="delete">Delete</button>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div id="folderHelpTooltip" class="folder-help-tooltip"
                      style="display: none; position: absolute; top: 50px; right: 15px; background: #fff; border: 1px solid #ccc; padding: 10px; z-index: 1000; box-shadow: 2px 2px 6px rgba(0,0,0,0.2);">
                      <ul class="folder-help-list" style="margin: 0; padding-left: 20px;">
                        <li data-i18n-key="folder_help_item_1">Click on a folder in the tree to view its files.</li>
                        <li data-i18n-key="folder_help_item_2">Use [-] to collapse and [+] to expand folders.</li>
                        <li data-i18n-key="folder_help_item_3">Select a folder and click "Create Folder" to add a
                          subfolder.</li>
                        <li data-i18n-key="folder_help_item_4">To rename or delete a folder, select it and then click
                          the appropriate button.</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div> <!-- end uploadFolderRow -->
          </div> <!-- end container -->
        </div> <!-- end mainOperations -->

        <!-- File List Section -->
        <div id="fileListContainer" style="display: none;">
          <h2 id="fileListTitle" data-i18n-key="file_list_title">Files in (Root)</h2>
          <div id="fileListActions" class="file-list-actions">
            <button id="deleteSelectedBtn" class="btn action-btn" style="display: none;"
              data-i18n-key="delete_files">Delete Files</button>
            <div id="deleteFilesModal" class="modal">
              <div class="modal-content">
                <h4 data-i18n-key="delete_selected_files_title">Delete Selected Files</h4>
                <p id="deleteFilesMessage" data-i18n-key="delete_files_message">Are you sure you want to delete the
                  selected files?</p>
                <div class="modal-footer">
                  <button id="cancelDeleteFiles" class="btn btn-secondary" data-i18n-key="cancel">Cancel</button>
                  <button id="confirmDeleteFiles" class="btn btn-danger" data-i18n-key="delete">Delete</button>
                </div>
              </div>
            </div>
            <button id="copySelectedBtn" class="btn action-btn" style="display: none;" disabled
              data-i18n-key="copy_files">Copy Files</button>
            <div id="copyFilesModal" class="modal">
              <div class="modal-content">
                <h4 data-i18n-key="copy_files_title">Copy Selected Files</h4>
                <p id="copyFilesMessage" data-i18n-key="copy_files_message">Select a target folder for copying the
                  selected files:</p>
                <select id="copyTargetFolder" class="form-control modal-input"></select>
                <div class="modal-footer">
                  <button id="cancelCopyFiles" class="btn btn-secondary" data-i18n-key="cancel">Cancel</button>
                  <button id="confirmCopyFiles" class="btn btn-primary" data-i18n-key="copy">Copy</button>
                </div>
              </div>
            </div>
            <button id="moveSelectedBtn" class="btn action-btn" style="display: none;" disabled
              data-i18n-key="move_files">Move Files</button>
            <div id="moveFilesModal" class="modal">
              <div class="modal-content">
                <h4 data-i18n-key="move_files_title">Move Selected Files</h4>
                <p id="moveFilesMessage" data-i18n-key="move_files_message">Select a target folder for moving the
                  selected files:</p>
                <select id="moveTargetFolder" class="form-control modal-input"></select>
                <div class="modal-footer">
                  <button id="cancelMoveFiles" class="btn btn-secondary" data-i18n-key="cancel">Cancel</button>
                  <button id="confirmMoveFiles" class="btn btn-primary" data-i18n-key="move">Move</button>
                </div>
              </div>
            </div>
            <button id="downloadZipBtn" class="btn action-btn" style="display: none;" disabled
              data-i18n-key="download_zip">Download ZIP</button>
            <button id="extractZipBtn" class="btn action-btn btn-sm btn-info" data-i18n-title="extract_zip"
              data-i18n-key="extract_zip_button">Extract Zip</button>
              <div id="createDropdown" class="dropdown-container" style="position:relative; display:inline-block;">
                <button id="createBtn"  class="btn action-btn" data-i18n-key="create">
                  ${t('create')} <span class="material-icons" style="font-size:16px;vertical-align:middle;">arrow_drop_down</span>
                </button>
                <ul
                  id="createMenu"
                  class="dropdown-menu"
                  style="
                    display: none;
                    position: absolute;
                    top: 100%;
                    left: 0;
                    margin: 4px 0 0;
                    padding: 0;
                    list-style: none;
                    background: #fff;
                    border: 1px solid #ccc;
                    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
                    z-index: 1000;
                    min-width: 140px;
                  "
                >
                  <li id="createFileOption" class="dropdown-item" data-i18n-key="create_file" style="padding:8px 12px; cursor:pointer;">
                    ${t('create_file')}
                  </li>
                  <li id="createFolderOption" class="dropdown-item" data-i18n-key="create_folder" style="padding:8px 12px; cursor:pointer;">
                    ${t('create_folder')}
                  </li>
                </ul>
              </div>
            <!-- Create File Modal -->
            <div id="createFileModal" class="modal" style="display:none;">
              <div class="modal-content">
                <h4 data-i18n-key="create_new_file">Create New File</h4>
                <input
                  type="text"
                  id="createFileNameInput"
                  class="form-control"
                  placeholder="Enter filename…"
                  data-i18n-placeholder="newfile_placeholder"
                />
                <div class="modal-footer" style="margin-top:1rem; text-align:right;">
                  <button id="cancelCreateFile" class="btn btn-secondary" data-i18n-key="cancel">Cancel</button>
                  <button id="confirmCreateFile" class="btn btn-primary" data-i18n-key="create">Create</button>
                </div>
              </div>
            </div>
            <div id="downloadZipModal" class="modal" style="display:none;">
              <div class="modal-content">
                <h4 data-i18n-key="download_zip_title">Download Selected Files as Zip</h4>
                <p data-i18n-key="download_zip_prompt">Enter a name for the zip file:</p>
                <input type="text" id="zipFileNameInput" class="form-control" data-i18n-placeholder="zip_placeholder"
                  placeholder="files.zip" />
                <div class="modal-footer" style="margin-top:15px; text-align:right;">
                  <button id="cancelDownloadZip" class="btn btn-secondary" data-i18n-key="cancel">Cancel</button>
                  <button id="confirmDownloadZip" class="btn btn-primary" data-i18n-key="download">Download</button>
                </div>
              </div>
            </div>
          </div>
          <div id="fileList"></div>
        </div>
      </div> <!-- end container-fluid -->
    </div> <!-- end mainColumn -->
  </div> <!-- end main-wrapper -->

  <!-- Download Progress Modal -->
  <div id="downloadProgressModal" class="modal" style="display: none;">
    <div class="modal-content" style="text-align: center; padding: 20px;">
      <h4 id="downloadProgressTitle" data-i18n-key="preparing_download">
        Preparing your download...
      </h4>

      <!-- spinner -->
      <span class="material-icons download-spinner">autorenew</span>

      <!-- these were missing -->
      <progress id="downloadProgressBar" value="0" max="100" style="width:100%; height:1.5em; display:none;"></progress>
      <p>
        <span id="downloadProgressPercent" style="display:none;">0%</span>
      </p>
    </div>
  </div>

  <!-- Single File Download Modal -->
  <div id="downloadFileModal" class="modal" style="display: none;">
    <div class="modal-content" style="text-align: center; padding: 20px;">
      <h4 data-i18n-key="download_file">Download File</h4>
      <p data-i18n-key="confirm_or_change_filename">Confirm or change the download file name:</p>
      <input type="text" id="downloadFileNameInput" class="form-control" data-i18n-placeholder="filename"
        placeholder="Filename" />
      <div style="margin-top: 15px; text-align: right;">
        <button id="cancelDownloadFile" class="btn btn-secondary" data-i18n-key="cancel">Cancel</button>
        <button id="confirmSingleDownloadButton" class="btn btn-primary" data-i18n-key="download">Download</button>
      </div>
    </div>
  </div>

  <!-- Change Password, Add User, Remove User, Rename File, and Custom Confirm Modals (unchanged) -->
  <div id="changePasswordModal" class="modal" style="display:none;">
    <div class="modal-content" style="max-width:400px; margin:auto;">
      <span id="closeChangePasswordModal" class="editor-close-btn">&times;</span>
      <h3 data-i18n-key="change_password_title">Change Password</h3>
      <input type="password" id="oldPassword" class="form-control" data-i18n-placeholder="old_password"
        placeholder="Old Password" style="width:100%; margin: 5px 0;" />
      <input type="password" id="newPassword" class="form-control" data-i18n-placeholder="new_password"
        placeholder="New Password" style="width:100%; margin: 5px 0;" />
      <input type="password" id="confirmPassword" class="form-control" data-i18n-placeholder="confirm_new_password"
        placeholder="Confirm New Password" style="width:100%; margin: 5px 0;" />
      <button id="saveNewPasswordBtn" class="btn btn-primary" data-i18n-key="save" style="width:100%;">Save</button>
    </div>
  </div>
  <div id="addUserModal" class="modal" style="display:none;">
    <div class="modal-content">
      <h3 data-i18n-key="create_new_user_title">Create New User</h3>
      <!-- 1) Add a form around these fields -->
      <form id="addUserForm">
        <label for="newUsername" data-i18n-key="username">Username:</label>
        <input type="text" id="newUsername" class="form-control" required />

        <label for="addUserPassword" data-i18n-key="password">Password:</label>
        <input type="password" id="addUserPassword" class="form-control" required />

        <div id="adminCheckboxContainer">
          <input type="checkbox" id="isAdmin" />
          <label for="isAdmin" data-i18n-key="grant_admin">Grant Admin Access</label>
        </div>

        <div class="button-container">
          <!-- Cancel stays type="button" -->
          <button type="button" id="cancelUserBtn" class="btn btn-secondary" data-i18n-key="cancel">
            Cancel
          </button>
          <!-- Save becomes type="submit" -->
          <button type="submit" id="saveUserBtn" class="btn btn-primary" data-i18n-key="save_user">
            Save User
          </button>
        </div>
      </form>
    </div>
  </div>
  <div id="removeUserModal" class="modal" style="display:none;">
    <div class="modal-content">
      <h3 data-i18n-key="remove_user_title">Remove User</h3>
      <label for="removeUsernameSelect" data-i18n-key="select_user_remove">Select a user to remove:</label>
      <select id="removeUsernameSelect" class="form-control"></select>
      <div class="button-container">
        <button id="cancelRemoveUserBtn" class="btn btn-secondary" data-i18n-key="cancel">Cancel</button>
        <button id="deleteUserBtn" class="btn btn-danger" data-i18n-key="delete_user">Delete User</button>
      </div>
    </div>
  </div>
  <div id="renameFileModal" class="modal" style="display:none;">
    <div class="modal-content">
      <h4 data-i18n-key="rename_file_title">Rename File</h4>
      <input type="text" id="newFileName" class="form-control" data-i18n-placeholder="rename_file_placeholder"
        placeholder="Enter new file name" style="margin-top:10px;" />
      <div style="margin-top:15px; text-align:right;">
        <button id="cancelRenameFile" class="btn btn-secondary" data-i18n-key="cancel">Cancel</button>
        <button id="submitRenameFile" class="btn btn-primary" data-i18n-key="rename">Rename</button>
      </div>
    </div>
  </div>
  <div id="customConfirmModal" class="modal" style="display:none;">
    <div class="modal-content">
      <p id="confirmMessage"></p>
      <div class="modal-actions">
        <button id="confirmYesBtn" class="btn btn-primary" data-i18n-key="yes">Yes</button>
        <button id="confirmNoBtn" class="btn btn-secondary" data-i18n-key="no">No</button>
      </div>
    </div>
  </div>
  <script type="module" src="js/main.js"></script>
</body>

</html>