{"version": "1.130.0", "results": [{"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/kalanakt_All-Url-Uploader/Dockerfile", "start": {"line": 11, "col": 1, "offset": 165}, "end": {"line": 11, "col": 26, "offset": 190}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD [\"python3\", \"bot.py\"]", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/kalanakt_All-Url-Uploader/.all-contributorsrc", "downloaded_repos/kalanakt_All-Url-Uploader/.dockerignore", "downloaded_repos/kalanakt_All-Url-Uploader/.env.example", "downloaded_repos/kalanakt_All-Url-Uploader/.github/FUNDING.yml", "downloaded_repos/kalanakt_All-Url-Uploader/.github/ISSUE_TEMPLATE/bug_report.md", "downloaded_repos/kalanakt_All-Url-Uploader/.github/ISSUE_TEMPLATE/feature_request.md", "downloaded_repos/kalanakt_All-Url-Uploader/.github/workflows/bandit.yml", "downloaded_repos/kalanakt_All-Url-Uploader/.github/workflows/codeql.yml", "downloaded_repos/kalanakt_All-Url-Uploader/.github/workflows/docker-image.yml", "downloaded_repos/kalanakt_All-Url-Uploader/.github/workflows/docker-publish.yml", "downloaded_repos/kalanakt_All-Url-Uploader/.github/workflows/pylint.yml", "downloaded_repos/kalanakt_All-Url-Uploader/.github/workflows/pyre.yml", "downloaded_repos/kalanakt_All-Url-Uploader/.gitignore", "downloaded_repos/kalanakt_All-Url-Uploader/.hintrc", "downloaded_repos/kalanakt_All-Url-Uploader/.idea/.gitignore", "downloaded_repos/kalanakt_All-Url-Uploader/.idea/All-Url-Uploader.iml", "downloaded_repos/kalanakt_All-Url-Uploader/.idea/inspectionProfiles/Project_Default.xml", "downloaded_repos/kalanakt_All-Url-Uploader/.idea/inspectionProfiles/profiles_settings.xml", "downloaded_repos/kalanakt_All-Url-Uploader/.idea/misc.xml", "downloaded_repos/kalanakt_All-Url-Uploader/.idea/modules.xml", "downloaded_repos/kalanakt_All-Url-Uploader/.idea/vcs.xml", "downloaded_repos/kalanakt_All-Url-Uploader/.pylintrc", "downloaded_repos/kalanakt_All-Url-Uploader/CODE_OF_CONDUCT.md", "downloaded_repos/kalanakt_All-Url-Uploader/CONTRIBUTING.md", "downloaded_repos/kalanakt_All-Url-Uploader/Dockerfile", "downloaded_repos/kalanakt_All-Url-Uploader/LICENSE", "downloaded_repos/kalanakt_All-Url-Uploader/NEW_README.md", "downloaded_repos/kalanakt_All-Url-Uploader/Procfile", "downloaded_repos/kalanakt_All-Url-Uploader/README.md", "downloaded_repos/kalanakt_All-Url-Uploader/SECURITY.md", "downloaded_repos/kalanakt_All-Url-Uploader/app.json", "downloaded_repos/kalanakt_All-Url-Uploader/app.py", "downloaded_repos/kalanakt_All-Url-Uploader/asset/README.md", "downloaded_repos/kalanakt_All-Url-Uploader/asset/copyrepolink.png", "downloaded_repos/kalanakt_All-Url-Uploader/asset/forkrepo.png", "downloaded_repos/kalanakt_All-Url-Uploader/asset/herokudep1.png", "downloaded_repos/kalanakt_All-Url-Uploader/asset/herokudep2.png", "downloaded_repos/kalanakt_All-Url-Uploader/asset/redirecttoheroku.png", "downloaded_repos/kalanakt_All-Url-Uploader/asset/tmwad.png", "downloaded_repos/kalanakt_All-Url-Uploader/bot.py", "downloaded_repos/kalanakt_All-Url-Uploader/config.py", "downloaded_repos/kalanakt_All-Url-Uploader/docs/.gitignore", "downloaded_repos/kalanakt_All-Url-Uploader/docs/LICENSE", "downloaded_repos/kalanakt_All-Url-Uploader/docs/README.md", "downloaded_repos/kalanakt_All-Url-Uploader/docs/components/changelog.module.css", "downloaded_repos/kalanakt_All-Url-Uploader/docs/components/changelog.tsx", "downloaded_repos/kalanakt_All-Url-Uploader/docs/components/collaborators.module.css", "downloaded_repos/kalanakt_All-Url-Uploader/docs/components/collaborators.tsx", "downloaded_repos/kalanakt_All-Url-Uploader/docs/components/extratox.module.css", "downloaded_repos/kalanakt_All-Url-Uploader/docs/components/extratox.tsx", "downloaded_repos/kalanakt_All-Url-Uploader/docs/components/followers.module.css", "downloaded_repos/kalanakt_All-Url-Uploader/docs/components/followers.tsx", "downloaded_repos/kalanakt_All-Url-Uploader/docs/components/releases.module.css", "downloaded_repos/kalanakt_All-Url-Uploader/docs/components/releases.tsx", "downloaded_repos/kalanakt_All-Url-Uploader/docs/components/sponsors.module.css", "downloaded_repos/kalanakt_All-Url-Uploader/docs/components/sponsors.tsx", "downloaded_repos/kalanakt_All-Url-Uploader/docs/next-env.d.ts", "downloaded_repos/kalanakt_All-Url-Uploader/docs/next.config.js", "downloaded_repos/kalanakt_All-Url-Uploader/docs/package.json", "downloaded_repos/kalanakt_All-Url-Uploader/docs/pages/_meta.json", "downloaded_repos/kalanakt_All-Url-Uploader/docs/pages/changelog.mdx", "downloaded_repos/kalanakt_All-Url-Uploader/docs/pages/contribution.mdx", "downloaded_repos/kalanakt_All-Url-Uploader/docs/pages/deploy/digital-ocean.mdx", "downloaded_repos/kalanakt_All-Url-Uploader/docs/pages/deploy/heroku.mdx", "downloaded_repos/kalanakt_All-Url-Uploader/docs/pages/deploy/koyeb.mdx", "downloaded_repos/kalanakt_All-Url-Uploader/docs/pages/deploy/railway.mdx", "downloaded_repos/kalanakt_All-Url-Uploader/docs/pages/deploy/render.mdx", "downloaded_repos/kalanakt_All-Url-Uploader/docs/pages/deploy.mdx", "downloaded_repos/kalanakt_All-Url-Uploader/docs/pages/index.mdx", "downloaded_repos/kalanakt_All-Url-Uploader/docs/pages/releases.mdx", "downloaded_repos/kalanakt_All-Url-Uploader/docs/public/.nojekyll", "downloaded_repos/kalanakt_All-Url-Uploader/docs/public/favicon.ico", "downloaded_repos/kalanakt_All-Url-Uploader/docs/public/tmwad.png", "downloaded_repos/kalanakt_All-Url-Uploader/docs/theme.config.tsx", "downloaded_repos/kalanakt_All-Url-Uploader/docs/tsconfig.json", "downloaded_repos/kalanakt_All-Url-Uploader/docs/yarn.lock", "downloaded_repos/kalanakt_All-Url-Uploader/heroku.yml", "downloaded_repos/kalanakt_All-Url-Uploader/plugins/__init__.py", "downloaded_repos/kalanakt_All-Url-Uploader/plugins/button.py", "downloaded_repos/kalanakt_All-Url-Uploader/plugins/callbacks.py", "downloaded_repos/kalanakt_All-Url-Uploader/plugins/commands.py", "downloaded_repos/kalanakt_All-Url-Uploader/plugins/dl_button.py", "downloaded_repos/kalanakt_All-Url-Uploader/plugins/echo.py", "downloaded_repos/kalanakt_All-Url-Uploader/plugins/functions/__init__.py", "downloaded_repos/kalanakt_All-Url-Uploader/plugins/functions/display_progress.py", "downloaded_repos/kalanakt_All-Url-Uploader/plugins/functions/help_Nekmo_ffmpeg.py", "downloaded_repos/kalanakt_All-Url-Uploader/plugins/functions/help_uploadbot.py", "downloaded_repos/kalanakt_All-Url-Uploader/plugins/functions/help_ytdl.py", "downloaded_repos/kalanakt_All-Url-Uploader/plugins/functions/ran_text.py", "downloaded_repos/kalanakt_All-Url-Uploader/plugins/script.py", "downloaded_repos/kalanakt_All-Url-Uploader/plugins/thumbunali.py", "downloaded_repos/kalanakt_All-Url-Uploader/plugins/utitles.py", "downloaded_repos/kalanakt_All-Url-Uploader/plugins/youtube.py", "downloaded_repos/kalanakt_All-Url-Uploader/pyproject.toml", "downloaded_repos/kalanakt_All-Url-Uploader/renovate.json", "downloaded_repos/kalanakt_All-Url-Uploader/requirements.txt", "downloaded_repos/kalanakt_All-Url-Uploader/runtime.txt"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.8064899444580078, "profiling_times": {"config_time": 7.562985420227051, "core_time": 2.987208843231201, "ignores_time": 0.0017914772033691406, "total_time": 10.553041458129883}, "parsing_time": {"total_time": 0.6248495578765869, "per_file_time": {"mean": 0.014877370425633026, "std_dev": 0.0003112926111253029}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 3.9017598628997803, "per_file_time": {"mean": 0.016532880774999075, "std_dev": 0.0021975672611814104}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.8836555480957031, "per_file_and_rule_time": {"mean": 0.0018642522111723694, "std_dev": 3.2214554455657866e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.35735321044921875, "per_def_and_rule_time": {"mean": 0.0025165719045719633, "std_dev": 8.41199416745852e-05}, "very_slow_stats": {"time_ratio": 0.26331355814598945, "count_ratio": 0.007042253521126761}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/kalanakt_All-Url-Uploader/plugins/echo.py", "fline": 23, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.09409594535827637}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}