{"version": "1.130.0", "results": [{"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/admin.php", "start": {"line": 52, "col": 3, "offset": 3483}, "end": {"line": 52, "col": 27, "offset": 3507}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/admin.php", "start": {"line": 52, "col": 8, "offset": 3488}, "end": {"line": 52, "col": 26, "offset": 3506}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/admin.php", "start": {"line": 52, "col": 8, "offset": 3488}, "end": {"line": 52, "col": 26, "offset": 3506}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/admin.php", "start": {"line": 58, "col": 3, "offset": 3745}, "end": {"line": 58, "col": 29, "offset": 3771}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/admin.php", "start": {"line": 58, "col": 8, "offset": 3750}, "end": {"line": 58, "col": 28, "offset": 3770}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/admin.php", "start": {"line": 58, "col": 8, "offset": 3750}, "end": {"line": 58, "col": 28, "offset": 3770}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/calibration.php", "start": {"line": 22, "col": 9, "offset": 859}, "end": {"line": 22, "col": 97, "offset": 947}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/calibration.php", "start": {"line": 22, "col": 9, "offset": 859}, "end": {"line": 22, "col": 97, "offset": 947}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/calibration.php", "start": {"line": 22, "col": 9, "offset": 859}, "end": {"line": 22, "col": 98, "offset": 948}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/calibration.php", "start": {"line": 23, "col": 9, "offset": 957}, "end": {"line": 23, "col": 97, "offset": 1045}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/calibration.php", "start": {"line": 23, "col": 9, "offset": 957}, "end": {"line": 23, "col": 97, "offset": 1045}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/calibration.php", "start": {"line": 23, "col": 9, "offset": 957}, "end": {"line": 23, "col": 98, "offset": 1046}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/config_backup.php", "start": {"line": 156, "col": 12, "offset": 7929}, "end": {"line": 156, "col": 32, "offset": 7949}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/config_backup.php", "start": {"line": 192, "col": 25, "offset": 10267}, "end": {"line": 192, "col": 88, "offset": 10330}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/config_backup.php", "start": {"line": 196, "col": 4, "offset": 10494}, "end": {"line": 196, "col": 101, "offset": 10591}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 334, "col": 4, "offset": 15043}, "end": {"line": 334, "col": 27, "offset": 15066}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 334, "col": 4, "offset": 15043}, "end": {"line": 334, "col": 27, "offset": 15066}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 334, "col": 4, "offset": 15043}, "end": {"line": 334, "col": 28, "offset": 15067}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 336, "col": 4, "offset": 15231}, "end": {"line": 336, "col": 27, "offset": 15254}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 336, "col": 4, "offset": 15231}, "end": {"line": 336, "col": 27, "offset": 15254}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 336, "col": 4, "offset": 15231}, "end": {"line": 336, "col": 28, "offset": 15255}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 354, "col": 4, "offset": 16290}, "end": {"line": 354, "col": 26, "offset": 16312}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 354, "col": 4, "offset": 16290}, "end": {"line": 354, "col": 26, "offset": 16312}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 354, "col": 4, "offset": 16290}, "end": {"line": 354, "col": 27, "offset": 16313}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 468, "col": 4, "offset": 23435}, "end": {"line": 468, "col": 25, "offset": 23456}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 468, "col": 4, "offset": 23435}, "end": {"line": 468, "col": 26, "offset": 23457}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 476, "col": 4, "offset": 23940}, "end": {"line": 476, "col": 30, "offset": 23966}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 476, "col": 4, "offset": 23940}, "end": {"line": 476, "col": 31, "offset": 23967}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 477, "col": 4, "offset": 23971}, "end": {"line": 477, "col": 30, "offset": 23997}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 477, "col": 4, "offset": 23971}, "end": {"line": 477, "col": 31, "offset": 23998}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 478, "col": 4, "offset": 24002}, "end": {"line": 478, "col": 32, "offset": 24030}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 490, "col": 6, "offset": 24622}, "end": {"line": 490, "col": 29, "offset": 24645}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 490, "col": 6, "offset": 24622}, "end": {"line": 490, "col": 30, "offset": 24646}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 502, "col": 4, "offset": 25078}, "end": {"line": 502, "col": 32, "offset": 25106}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 510, "col": 4, "offset": 25383}, "end": {"line": 510, "col": 21, "offset": 25400}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 527, "col": 4, "offset": 26383}, "end": {"line": 527, "col": 25, "offset": 26404}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 527, "col": 4, "offset": 26383}, "end": {"line": 527, "col": 25, "offset": 26404}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 527, "col": 4, "offset": 26383}, "end": {"line": 527, "col": 26, "offset": 26405}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 528, "col": 4, "offset": 26409}, "end": {"line": 528, "col": 25, "offset": 26430}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 528, "col": 4, "offset": 26409}, "end": {"line": 528, "col": 25, "offset": 26430}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 528, "col": 4, "offset": 26409}, "end": {"line": 528, "col": 26, "offset": 26431}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 545, "col": 4, "offset": 27445}, "end": {"line": 545, "col": 25, "offset": 27466}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 545, "col": 4, "offset": 27445}, "end": {"line": 545, "col": 25, "offset": 27466}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 545, "col": 4, "offset": 27445}, "end": {"line": 545, "col": 26, "offset": 27467}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 546, "col": 4, "offset": 27471}, "end": {"line": 546, "col": 25, "offset": 27492}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 546, "col": 4, "offset": 27471}, "end": {"line": 546, "col": 25, "offset": 27492}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 546, "col": 4, "offset": 27471}, "end": {"line": 546, "col": 26, "offset": 27493}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 561, "col": 4, "offset": 28347}, "end": {"line": 561, "col": 22, "offset": 28365}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 561, "col": 4, "offset": 28347}, "end": {"line": 561, "col": 22, "offset": 28365}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 561, "col": 4, "offset": 28347}, "end": {"line": 561, "col": 23, "offset": 28366}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 562, "col": 4, "offset": 28370}, "end": {"line": 562, "col": 23, "offset": 28389}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 562, "col": 4, "offset": 28370}, "end": {"line": 562, "col": 23, "offset": 28389}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 562, "col": 4, "offset": 28370}, "end": {"line": 562, "col": 24, "offset": 28390}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 576, "col": 4, "offset": 29272}, "end": {"line": 576, "col": 22, "offset": 29290}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 576, "col": 4, "offset": 29272}, "end": {"line": 576, "col": 22, "offset": 29290}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 576, "col": 4, "offset": 29272}, "end": {"line": 576, "col": 23, "offset": 29291}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 577, "col": 4, "offset": 29295}, "end": {"line": 577, "col": 23, "offset": 29314}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 577, "col": 4, "offset": 29295}, "end": {"line": 577, "col": 23, "offset": 29314}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 577, "col": 4, "offset": 29295}, "end": {"line": 577, "col": 24, "offset": 29315}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 592, "col": 4, "offset": 30259}, "end": {"line": 592, "col": 21, "offset": 30276}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 592, "col": 4, "offset": 30259}, "end": {"line": 592, "col": 21, "offset": 30276}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 592, "col": 4, "offset": 30259}, "end": {"line": 592, "col": 22, "offset": 30277}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 598, "col": 4, "offset": 30511}, "end": {"line": 598, "col": 25, "offset": 30532}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 598, "col": 4, "offset": 30511}, "end": {"line": 598, "col": 26, "offset": 30533}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 608, "col": 6, "offset": 31214}, "end": {"line": 608, "col": 34, "offset": 31242}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 608, "col": 6, "offset": 31214}, "end": {"line": 608, "col": 35, "offset": 31243}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 617, "col": 4, "offset": 31757}, "end": {"line": 617, "col": 34, "offset": 31787}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 617, "col": 4, "offset": 31757}, "end": {"line": 617, "col": 35, "offset": 31788}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 618, "col": 4, "offset": 31792}, "end": {"line": 618, "col": 31, "offset": 31819}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 618, "col": 4, "offset": 31792}, "end": {"line": 618, "col": 32, "offset": 31820}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 718, "col": 4, "offset": 37283}, "end": {"line": 718, "col": 24, "offset": 37303}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 718, "col": 4, "offset": 37283}, "end": {"line": 718, "col": 24, "offset": 37303}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 718, "col": 4, "offset": 37283}, "end": {"line": 718, "col": 25, "offset": 37304}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 719, "col": 4, "offset": 37308}, "end": {"line": 719, "col": 25, "offset": 37329}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 719, "col": 4, "offset": 37308}, "end": {"line": 719, "col": 25, "offset": 37329}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 719, "col": 4, "offset": 37308}, "end": {"line": 719, "col": 26, "offset": 37330}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 720, "col": 4, "offset": 37334}, "end": {"line": 720, "col": 29, "offset": 37359}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 720, "col": 4, "offset": 37334}, "end": {"line": 720, "col": 29, "offset": 37359}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 720, "col": 4, "offset": 37334}, "end": {"line": 720, "col": 30, "offset": 37360}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 721, "col": 4, "offset": 37364}, "end": {"line": 721, "col": 29, "offset": 37389}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 721, "col": 4, "offset": 37364}, "end": {"line": 721, "col": 29, "offset": 37389}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 721, "col": 4, "offset": 37364}, "end": {"line": 721, "col": 30, "offset": 37390}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 724, "col": 4, "offset": 37451}, "end": {"line": 724, "col": 27, "offset": 37474}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 724, "col": 4, "offset": 37451}, "end": {"line": 724, "col": 27, "offset": 37474}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 724, "col": 4, "offset": 37451}, "end": {"line": 724, "col": 28, "offset": 37475}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 762, "col": 7, "offset": 39391}, "end": {"line": 762, "col": 35, "offset": 39419}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 777, "col": 4, "offset": 40171}, "end": {"line": 777, "col": 22, "offset": 40189}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 778, "col": 4, "offset": 40193}, "end": {"line": 778, "col": 22, "offset": 40211}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 779, "col": 4, "offset": 40215}, "end": {"line": 779, "col": 28, "offset": 40239}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 780, "col": 4, "offset": 40243}, "end": {"line": 780, "col": 34, "offset": 40273}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 781, "col": 4, "offset": 40277}, "end": {"line": 781, "col": 34, "offset": 40307}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 829, "col": 4, "offset": 43285}, "end": {"line": 829, "col": 24, "offset": 43305}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 829, "col": 4, "offset": 43285}, "end": {"line": 829, "col": 24, "offset": 43305}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 829, "col": 4, "offset": 43285}, "end": {"line": 829, "col": 25, "offset": 43306}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 830, "col": 4, "offset": 43310}, "end": {"line": 830, "col": 25, "offset": 43331}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 830, "col": 4, "offset": 43310}, "end": {"line": 830, "col": 25, "offset": 43331}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 830, "col": 4, "offset": 43310}, "end": {"line": 830, "col": 26, "offset": 43332}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 831, "col": 4, "offset": 43336}, "end": {"line": 831, "col": 29, "offset": 43361}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 831, "col": 4, "offset": 43336}, "end": {"line": 831, "col": 29, "offset": 43361}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 831, "col": 4, "offset": 43336}, "end": {"line": 831, "col": 30, "offset": 43362}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 832, "col": 4, "offset": 43366}, "end": {"line": 832, "col": 29, "offset": 43391}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 832, "col": 4, "offset": 43366}, "end": {"line": 832, "col": 29, "offset": 43391}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 832, "col": 4, "offset": 43366}, "end": {"line": 832, "col": 30, "offset": 43392}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 873, "col": 7, "offset": 45378}, "end": {"line": 873, "col": 35, "offset": 45406}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 888, "col": 4, "offset": 46158}, "end": {"line": 888, "col": 22, "offset": 46176}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 889, "col": 4, "offset": 46180}, "end": {"line": 889, "col": 22, "offset": 46198}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 890, "col": 4, "offset": 46202}, "end": {"line": 890, "col": 28, "offset": 46226}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 891, "col": 4, "offset": 46230}, "end": {"line": 891, "col": 34, "offset": 46260}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 892, "col": 4, "offset": 46264}, "end": {"line": 892, "col": 34, "offset": 46294}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 912, "col": 5, "offset": 47710}, "end": {"line": 912, "col": 25, "offset": 47730}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 912, "col": 5, "offset": 47710}, "end": {"line": 912, "col": 25, "offset": 47730}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 912, "col": 5, "offset": 47710}, "end": {"line": 912, "col": 26, "offset": 47731}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 944, "col": 6, "offset": 50008}, "end": {"line": 944, "col": 39, "offset": 50041}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 946, "col": 6, "offset": 50168}, "end": {"line": 946, "col": 39, "offset": 50201}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 956, "col": 6, "offset": 50876}, "end": {"line": 956, "col": 37, "offset": 50907}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 962, "col": 5, "offset": 51149}, "end": {"line": 962, "col": 44, "offset": 51188}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 965, "col": 4, "offset": 51198}, "end": {"line": 965, "col": 25, "offset": 51219}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 965, "col": 4, "offset": 51198}, "end": {"line": 965, "col": 25, "offset": 51219}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 965, "col": 4, "offset": 51198}, "end": {"line": 965, "col": 26, "offset": 51220}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 966, "col": 4, "offset": 51224}, "end": {"line": 966, "col": 27, "offset": 51247}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 966, "col": 4, "offset": 51224}, "end": {"line": 966, "col": 27, "offset": 51247}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 966, "col": 4, "offset": 51224}, "end": {"line": 966, "col": 28, "offset": 51248}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 967, "col": 4, "offset": 51252}, "end": {"line": 967, "col": 30, "offset": 51278}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 967, "col": 4, "offset": 51252}, "end": {"line": 967, "col": 30, "offset": 51278}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 967, "col": 4, "offset": 51252}, "end": {"line": 967, "col": 31, "offset": 51279}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 968, "col": 4, "offset": 51283}, "end": {"line": 968, "col": 31, "offset": 51310}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 968, "col": 4, "offset": 51283}, "end": {"line": 968, "col": 31, "offset": 51310}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 968, "col": 4, "offset": 51283}, "end": {"line": 968, "col": 32, "offset": 51311}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 969, "col": 4, "offset": 51315}, "end": {"line": 969, "col": 34, "offset": 51345}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 969, "col": 4, "offset": 51315}, "end": {"line": 969, "col": 34, "offset": 51345}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 969, "col": 4, "offset": 51315}, "end": {"line": 969, "col": 35, "offset": 51346}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 970, "col": 4, "offset": 51350}, "end": {"line": 970, "col": 33, "offset": 51379}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 970, "col": 4, "offset": 51350}, "end": {"line": 970, "col": 33, "offset": 51379}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 970, "col": 4, "offset": 51350}, "end": {"line": 970, "col": 34, "offset": 51380}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 986, "col": 3, "offset": 52161}, "end": {"line": 986, "col": 34, "offset": 52192}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 2531, "col": 4, "offset": 131357}, "end": {"line": 2531, "col": 26, "offset": 131379}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 2532, "col": 4, "offset": 131383}, "end": {"line": 2532, "col": 23, "offset": 131402}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 2533, "col": 4, "offset": 131406}, "end": {"line": 2533, "col": 22, "offset": 131424}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 2537, "col": 5, "offset": 131626}, "end": {"line": 2537, "col": 25, "offset": 131646}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 3072, "col": 4, "offset": 173716}, "end": {"line": 3072, "col": 90, "offset": 173802}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 3076, "col": 4, "offset": 173942}, "end": {"line": 3076, "col": 83, "offset": 174021}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 3658, "col": 3, "offset": 201586}, "end": {"line": 3658, "col": 25, "offset": 201608}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 3659, "col": 3, "offset": 201611}, "end": {"line": 3659, "col": 102, "offset": 201710}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "start": {"line": 3661, "col": 3, "offset": 201899}, "end": {"line": 3661, "col": 31, "offset": 201927}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/expert/jitter_test.php", "start": {"line": 23, "col": 5, "offset": 895}, "end": {"line": 23, "col": 85, "offset": 975}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/expert/modem_fw_upgrade.php", "start": {"line": 26, "col": 32, "offset": 1069}, "end": {"line": 26, "col": 147, "offset": 1184}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/sysinfo.php", "start": {"line": 12, "col": 1, "offset": 343}, "end": {"line": 12, "col": 32, "offset": 374}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/wifi/functions.js", "start": {"line": 16, "col": 9, "offset": 430}, "end": {"line": 18, "col": 114, "offset": 867}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/wifi/functions.js", "start": {"line": 31, "col": 9, "offset": 1278}, "end": {"line": 33, "col": 114, "offset": 1699}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/dstarrepeater/last_herd.php", "start": {"line": 57, "col": 5, "offset": 2697}, "end": {"line": 57, "col": 121, "offset": 2813}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/dstarrepeater/link_manager.php", "start": {"line": 43, "col": 3, "offset": 2012}, "end": {"line": 43, "col": 27, "offset": 2036}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/dstarrepeater/link_manager.php", "start": {"line": 43, "col": 8, "offset": 2017}, "end": {"line": 43, "col": 26, "offset": 2035}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/dstarrepeater/link_manager.php", "start": {"line": 43, "col": 8, "offset": 2017}, "end": {"line": 43, "col": 26, "offset": 2035}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/dstarrepeater/link_manager.php", "start": {"line": 55, "col": 3, "offset": 2522}, "end": {"line": 55, "col": 29, "offset": 2548}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/dstarrepeater/link_manager.php", "start": {"line": 55, "col": 8, "offset": 2527}, "end": {"line": 55, "col": 28, "offset": 2547}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/dstarrepeater/link_manager.php", "start": {"line": 55, "col": 8, "offset": 2527}, "end": {"line": 55, "col": 28, "offset": 2547}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/dstarrepeater/local_tx.php", "start": {"line": 55, "col": 5, "offset": 2455}, "end": {"line": 55, "col": 114, "offset": 2564}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/bm_manager.php", "start": {"line": 97, "col": 40, "offset": 4810}, "end": {"line": 97, "col": 49, "offset": 4819}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.backticks-use.backticks-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/functions.php", "start": {"line": 97, "col": 4, "offset": 2698}, "end": {"line": 97, "col": 125, "offset": 2819}, "extra": {"message": "Backticks use may lead to command injection vulnerabilities.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.php.net/manual/en/language.operators.execution.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/BackticksSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.backticks-use.backticks-use", "shortlink": "https://sg.run/4xj9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.backticks-use.backticks-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/functions.php", "start": {"line": 106, "col": 4, "offset": 3257}, "end": {"line": 106, "col": 126, "offset": 3379}, "extra": {"message": "Backticks use may lead to command injection vulnerabilities.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.php.net/manual/en/language.operators.execution.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/BackticksSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.backticks-use.backticks-use", "shortlink": "https://sg.run/4xj9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.backticks-use.backticks-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/functions.php", "start": {"line": 256, "col": 3, "offset": 9281}, "end": {"line": 256, "col": 144, "offset": 9422}, "extra": {"message": "Backticks use may lead to command injection vulnerabilities.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.php.net/manual/en/language.operators.execution.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/BackticksSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.backticks-use.backticks-use", "shortlink": "https://sg.run/4xj9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.backticks-use.backticks-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/functions.php", "start": {"line": 262, "col": 4, "offset": 9694}, "end": {"line": 262, "col": 145, "offset": 9835}, "extra": {"message": "Backticks use may lead to command injection vulnerabilities.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.php.net/manual/en/language.operators.execution.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/BackticksSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.backticks-use.backticks-use", "shortlink": "https://sg.run/4xj9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.backticks-use.backticks-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/functions.php", "start": {"line": 278, "col": 3, "offset": 10322}, "end": {"line": 278, "col": 222, "offset": 10541}, "extra": {"message": "Backticks use may lead to command injection vulnerabilities.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.php.net/manual/en/language.operators.execution.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/BackticksSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.backticks-use.backticks-use", "shortlink": "https://sg.run/4xj9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.backticks-use.backticks-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/functions.php", "start": {"line": 284, "col": 4, "offset": 10828}, "end": {"line": 284, "col": 223, "offset": 11047}, "extra": {"message": "Backticks use may lead to command injection vulnerabilities.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.php.net/manual/en/language.operators.execution.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/BackticksSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.backticks-use.backticks-use", "shortlink": "https://sg.run/4xj9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.backticks-use.backticks-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/functions.php", "start": {"line": 299, "col": 3, "offset": 11582}, "end": {"line": 299, "col": 121, "offset": 11700}, "extra": {"message": "Backticks use may lead to command injection vulnerabilities.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.php.net/manual/en/language.operators.execution.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/BackticksSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.backticks-use.backticks-use", "shortlink": "https://sg.run/4xj9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.backticks-use.backticks-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/functions.php", "start": {"line": 305, "col": 4, "offset": 12036}, "end": {"line": 305, "col": 122, "offset": 12154}, "extra": {"message": "Backticks use may lead to command injection vulnerabilities.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.php.net/manual/en/language.operators.execution.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/BackticksSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.backticks-use.backticks-use", "shortlink": "https://sg.run/4xj9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.backticks-use.backticks-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/functions.php", "start": {"line": 320, "col": 3, "offset": 12683}, "end": {"line": 320, "col": 121, "offset": 12801}, "extra": {"message": "Backticks use may lead to command injection vulnerabilities.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.php.net/manual/en/language.operators.execution.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/BackticksSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.backticks-use.backticks-use", "shortlink": "https://sg.run/4xj9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.backticks-use.backticks-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/functions.php", "start": {"line": 326, "col": 4, "offset": 13088}, "end": {"line": 326, "col": 122, "offset": 13206}, "extra": {"message": "Backticks use may lead to command injection vulnerabilities.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.php.net/manual/en/language.operators.execution.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/BackticksSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.backticks-use.backticks-use", "shortlink": "https://sg.run/4xj9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.backticks-use.backticks-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/functions.php", "start": {"line": 341, "col": 3, "offset": 13732}, "end": {"line": 341, "col": 121, "offset": 13850}, "extra": {"message": "Backticks use may lead to command injection vulnerabilities.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.php.net/manual/en/language.operators.execution.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/BackticksSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.backticks-use.backticks-use", "shortlink": "https://sg.run/4xj9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.backticks-use.backticks-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/functions.php", "start": {"line": 347, "col": 4, "offset": 14135}, "end": {"line": 347, "col": 122, "offset": 14253}, "extra": {"message": "Backticks use may lead to command injection vulnerabilities.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.php.net/manual/en/language.operators.execution.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/BackticksSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.backticks-use.backticks-use", "shortlink": "https://sg.run/4xj9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.backticks-use.backticks-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/functions.php", "start": {"line": 362, "col": 3, "offset": 14788}, "end": {"line": 362, "col": 125, "offset": 14910}, "extra": {"message": "Backticks use may lead to command injection vulnerabilities.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.php.net/manual/en/language.operators.execution.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/BackticksSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.backticks-use.backticks-use", "shortlink": "https://sg.run/4xj9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.backticks-use.backticks-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/functions.php", "start": {"line": 368, "col": 4, "offset": 15201}, "end": {"line": 368, "col": 126, "offset": 15323}, "extra": {"message": "Backticks use may lead to command injection vulnerabilities.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.php.net/manual/en/language.operators.execution.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/BackticksSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.backticks-use.backticks-use", "shortlink": "https://sg.run/4xj9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/functions.php", "start": {"line": 401, "col": 2, "offset": 17931}, "end": {"line": 401, "col": 78, "offset": 18007}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/functions.php", "start": {"line": 402, "col": 19, "offset": 18026}, "end": {"line": 402, "col": 100, "offset": 18107}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/functions.php", "start": {"line": 464, "col": 2, "offset": 20474}, "end": {"line": 464, "col": 78, "offset": 20550}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/functions.php", "start": {"line": 465, "col": 19, "offset": 20569}, "end": {"line": 465, "col": 100, "offset": 20650}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/m17_manager.php", "start": {"line": 50, "col": 3, "offset": 2765}, "end": {"line": 50, "col": 29, "offset": 2791}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/m17_manager.php", "start": {"line": 50, "col": 8, "offset": 2770}, "end": {"line": 50, "col": 28, "offset": 2790}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/m17_manager.php", "start": {"line": 50, "col": 8, "offset": 2770}, "end": {"line": 50, "col": 28, "offset": 2790}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/nxdn_manager.php", "start": {"line": 49, "col": 3, "offset": 2604}, "end": {"line": 49, "col": 29, "offset": 2630}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/nxdn_manager.php", "start": {"line": 49, "col": 8, "offset": 2609}, "end": {"line": 49, "col": 28, "offset": 2629}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/nxdn_manager.php", "start": {"line": 49, "col": 8, "offset": 2609}, "end": {"line": 49, "col": 28, "offset": 2629}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/p25_manager.php", "start": {"line": 49, "col": 3, "offset": 2579}, "end": {"line": 49, "col": 29, "offset": 2605}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/p25_manager.php", "start": {"line": 49, "col": 8, "offset": 2584}, "end": {"line": 49, "col": 28, "offset": 2604}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/p25_manager.php", "start": {"line": 49, "col": 8, "offset": 2584}, "end": {"line": 49, "col": 28, "offset": 2604}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/repeaterinfo.php", "start": {"line": 250, "col": 79, "offset": 14314}, "end": {"line": 250, "col": 253, "offset": 14488}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/repeaterinfo.php", "start": {"line": 251, "col": 40, "offset": 14531}, "end": {"line": 251, "col": 230, "offset": 14721}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/tgif_manager.php", "start": {"line": 132, "col": 20, "offset": 5105}, "end": {"line": 132, "col": 104, "offset": 5189}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/tgif_manager.php", "start": {"line": 133, "col": 34, "offset": 5224}, "end": {"line": 133, "col": 45, "offset": 5235}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/ysf_manager.php", "start": {"line": 50, "col": 3, "offset": 2664}, "end": {"line": 50, "col": 29, "offset": 2690}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/ysf_manager.php", "start": {"line": 50, "col": 8, "offset": 2669}, "end": {"line": 50, "col": 28, "offset": 2689}, "extra": {"message": "User input is passed to a function that executes a shell command. This can lead to remote code execution.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "impact": "HIGH", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-exec.tainted-exec", "shortlink": "https://sg.run/kxEEz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.tainted-exec.tainted-exec", "path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/ysf_manager.php", "start": {"line": 50, "col": 8, "offset": 2669}, "end": {"line": 50, "col": 28, "offset": 2689}, "extra": {"message": "Executing non-constant commands. This can lead to command injection. You should use `escapeshellarg()` when using command.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://www.stackhawk.com/blog/php-command-injection/", "https://brightsec.com/blog/code-injection-php/", "https://www.acunetix.com/websitesecurity/php-security-2/"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.tainted-exec.tainted-exec", "shortlink": "https://sg.run/JAkP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/admin.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/calibration.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/config_backup.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/download_modem_log.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/expert/edit_dapnetgateway.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/expert/edit_dashboard.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/expert/edit_dmrgateway.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/expert/edit_dstarrepeater.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/expert/edit_ircddbgateway.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/expert/edit_mmdvmhost.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/expert/edit_nxdngateway.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/expert/edit_p25gateway.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/expert/edit_starnetserver.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/expert/edit_timeserver.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/expert/edit_ysfgateway.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/expert/fulledit_bmapikey.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/expert/fulledit_cron.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/expert/fulledit_dapnetapi.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/expert/fulledit_dmrgateway.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/expert/fulledit_pistar-remote.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/expert/fulledit_rssidat.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/expert/fulledit_wpaconfig.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/expert/header-menu.inc", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/expert/index.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/expert/jitter_test.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/expert/modem_fw_upgrade.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/expert/ssh_access.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/expert/upgrade.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/live_modem_log.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/pistar-keeper-download.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/power.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/sysinfo.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/update.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/wifi/functions.js", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/wifi/index.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/wifi/phpincs.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/wifi/styles.css", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/wifi/styles.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/wifi.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/api/index.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/api/last_heard.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/apple-touch-icon-precomposed.png", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/apple-touch-icon.png", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/config/browserdetect.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/config/config.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/config/index.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/config/ircddbgateway_languages.inc", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/config/ircddblocal.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/config/language.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/config/version.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/css/index.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/css/ircddb.css", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/css/mini_ircddb.css", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/css/nice-select.min.css", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/css/pistar-css-mini.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/css/pistar-css.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/dstarrepeater/active_reflector_links.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/dstarrepeater/active_starnet_groups.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/dstarrepeater/css_connections.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/dstarrepeater/gateway_software_config.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/dstarrepeater/index.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/dstarrepeater/last_herd.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/dstarrepeater/link_manager.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/dstarrepeater/local_tx.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/dstarrepeater/system.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/functions.js", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/images/20green.png", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/images/20red.png", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/images/download.png", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/images/favicon.ico", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/images/index.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/images/reboot.png", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/images/restore.png", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/images/shutdown.png", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/index.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/lang/catalan_es.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/lang/chinese_cn.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/lang/chinese_hk.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/lang/chinese_tw.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/lang/dutch_nl.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/lang/english_uk.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/lang/english_us.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/lang/french_fr.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/lang/german_de.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/lang/greek_gr.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/lang/hungarian_hu.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/lang/index.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/lang/italian_it.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/lang/japanese_jp.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/lang/norwegian_no.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/lang/polish_pl.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/lang/portuguese_br.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/lang/portuguese_pt.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/lang/romanian_ro.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/lang/slovenian_sl.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/lang/spanish_es.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/lang/spanish_mx.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/lang/swedish_se.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/lang/thai_th.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/lang/turkish_tr.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/bm_links.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/bm_manager.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/functions.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/index.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/lh.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/localtx.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/m17_manager.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/nxdn_manager.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/p25_manager.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/pages.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/repeaterinfo.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/tgif_links.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/tgif_manager.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/tools.php", "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/ysf_manager.php"], "skipped": [{"path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/jquery-timing.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/jquery.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/nice-select.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/plotly-basic.min.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.7236371040344238, "profiling_times": {"config_time": 5.895959854125977, "core_time": 16.48044466972351, "ignores_time": 0.0024385452270507812, "total_time": 22.37978482246399}, "parsing_time": {"total_time": 2.656510591506958, "per_file_time": {"mean": 0.026302085064425326, "std_dev": 0.014746615483524022}, "very_slow_stats": {"time_ratio": 0.4593480666722131, "count_ratio": 0.009900990099009901}, "very_slow_files": [{"fpath": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "ftime": 1.2202630043029785}]}, "scanning_time": {"total_time": 26.702197074890137, "per_file_time": {"mean": 0.08018677800267301, "std_dev": 0.39812854520169655}, "very_slow_stats": {"time_ratio": 0.42293283903019946, "count_ratio": 0.003003003003003003}, "very_slow_files": [{"fpath": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "ftime": 11.293236017227173}]}, "matching_time": {"total_time": 13.35738730430603, "per_file_and_rule_time": {"mean": 0.02578646197742476, "std_dev": 0.01875831049589346}, "very_slow_stats": {"time_ratio": 0.5998779686397896, "count_ratio": 0.04247104247104247}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/wifi.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.19762897491455078}, {"fpath": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "rule_id": "php.lang.security.injection.printed-request.printed-request", "time": 0.23713088035583496}, {"fpath": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "rule_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "time": 0.2570641040802002}, {"fpath": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/repeaterinfo.php", "rule_id": "php.laravel.security.laravel-sql-injection.laravel-sql-injection", "time": 0.2695281505584717}, {"fpath": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/index.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.29417896270751953}, {"fpath": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/repeaterinfo.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.29548192024230957}, {"fpath": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "rule_id": "php.lang.security.injection.tainted-object-instantiation.tainted-object-instantiation", "time": 0.3062479496002197}, {"fpath": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/mmdvmhost/functions.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.7471010684967041}, {"fpath": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 1.3145198822021484}, {"fpath": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "rule_id": "php.laravel.security.laravel-sql-injection.laravel-sql-injection", "time": 2.6271040439605713}]}, "tainting_time": {"total_time": 5.516002178192139, "per_def_and_rule_time": {"mean": 0.011661738220279362, "std_dev": 0.0026801985844325882}, "very_slow_stats": {"time_ratio": 0.6350210647658887, "count_ratio": 0.023255813953488372}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "fline": 1, "rule_id": "php.lang.security.md5-used-as-password.md5-used-as-password", "time": 0.07375383377075195}, {"fpath": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "fline": 1, "rule_id": "php.laravel.security.laravel-sql-injection.laravel-sql-injection", "time": 0.3042418956756592}, {"fpath": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "fline": 1, "rule_id": "php.lang.security.injection.tainted-object-instantiation.tainted-object-instantiation", "time": 0.3086578845977783}, {"fpath": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "fline": 1, "rule_id": "php.lang.security.injection.tainted-url-host.tainted-url-host", "time": 0.3254508972167969}, {"fpath": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "fline": 1, "rule_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "time": 0.3270080089569092}, {"fpath": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "fline": 1, "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.36063694953918457}, {"fpath": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "fline": 1, "rule_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "time": 0.40784311294555664}, {"fpath": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "fline": 1, "rule_id": "php.lang.security.tainted-exec.tainted-exec", "time": 0.4162740707397461}, {"fpath": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "fline": 1, "rule_id": "php.lang.security.injection.tainted-exec.tainted-exec", "time": 0.45720982551574707}, {"fpath": "downloaded_repos/AndyTaylorTweet_Pi-Star_DV_Dash/admin/configure.php", "fline": 1, "rule_id": "php.lang.security.injection.printed-request.printed-request", "time": 0.4714541435241699}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}