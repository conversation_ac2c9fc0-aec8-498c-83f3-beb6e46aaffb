{"version": "1.130.0", "results": [], "errors": [], "paths": {"scanned": ["downloaded_repos/Purfview_IMDb-Scout-Mod/LICENSE", "downloaded_repos/Purfview_IMDb-Scout-Mod/README.md"], "skipped": [{"path": "downloaded_repos/Purfview_IMDb-Scout-Mod/IMDb_Scout_Mod.user.js", "reason": "exceeded_size_limit"}]}, "time": {"rules": [], "rules_parse_time": 2.1883468627929688, "profiling_times": {"config_time": 5.912435293197632, "core_time": 2.6695566177368164, "ignores_time": 0.002069234848022461, "total_time": 8.584854364395142}, "parsing_time": {"total_time": 0.0, "per_file_time": {"mean": 0.0, "std_dev": 0.0}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.005831003189086914, "per_file_time": {"mean": 0.0014577507972717285, "std_dev": 2.2553312462036956e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.0, "per_file_and_rule_time": {"mean": 0.0, "std_dev": 0.0}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.0, "per_def_and_rule_time": {"mean": 0.0, "std_dev": 0.0}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}