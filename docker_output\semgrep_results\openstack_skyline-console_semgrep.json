{"version": "1.130.0", "results": [{"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/openstack_skyline-console/config/webpack.common.js", "start": {"line": 22, "col": 43, "offset": 918}, "end": {"line": 22, "col": 53, "offset": 928}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/openstack_skyline-console/config/webpack.dev.js", "start": {"line": 33, "col": 43, "offset": 1151}, "end": {"line": 33, "col": 53, "offset": 1161}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/openstack_skyline-console/config/webpack.e2e.js", "start": {"line": 29, "col": 43, "offset": 1199}, "end": {"line": 29, "col": 53, "offset": 1209}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/openstack_skyline-console/config/webpack.prod.js", "start": {"line": 30, "col": 43, "offset": 1271}, "end": {"line": 30, "col": 53, "offset": 1281}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "path": "downloaded_repos/openstack_skyline-console/src/client/client/request.js", "start": {"line": 74, "col": 7, "offset": 2166}, "end": {"line": 74, "col": 62, "offset": 2221}, "extra": {"message": "Bracket object notation with user input is present, this might allow an attacker to access all properties of the object and even it's prototype. Use literal values for object properties.", "metadata": {"confidence": "LOW", "owasp": ["A02:2017 - Broken Authentication", "A04:2021 - Insecure Design"], "cwe": ["CWE-522: Insufficiently Protected Credentials"], "category": "security", "technology": ["express"], "references": ["https://github.com/nodesecurity/eslint-plugin-security/blob/3c7522ca1be800353513282867a1034c795d9eb4/docs/the-dangers-of-square-bracket-notation.md"], "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/javascript.express.security.audit.remote-property-injection.remote-property-injection", "shortlink": "https://sg.run/Z4gn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "typescript.react.security.audit.react-dangerouslysetinnerhtml.react-dangerouslysetinnerhtml", "path": "downloaded_repos/openstack_skyline-console/src/resources/dns/record.jsx", "start": {"line": 152, "col": 49, "offset": 7291}, "end": {"line": 152, "col": 69, "offset": 7311}, "extra": {"message": "Detection of dangerouslySetInnerHTML from non-constant definition. This can inadvertently expose users to cross-site scripting (XSS) attacks if this comes from user-provided input. If you have to use dangerouslySetInnerHTML, consider using a sanitization library such as DOMPurify to sanitize your HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://react.dev/reference/react-dom/components/common#dangerously-setting-the-inner-html"], "category": "security", "confidence": "MEDIUM", "technology": ["react"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/typescript.react.security.audit.react-dangerouslysetinnerhtml.react-dangerouslysetinnerhtml", "shortlink": "https://sg.run/rAx6"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "typescript.react.security.audit.react-dangerouslysetinnerhtml.react-dangerouslysetinnerhtml", "path": "downloaded_repos/openstack_skyline-console/src/resources/dns/record.jsx", "start": {"line": 154, "col": 54, "offset": 7380}, "end": {"line": 154, "col": 74, "offset": 7400}, "extra": {"message": "Detection of dangerouslySetInnerHTML from non-constant definition. This can inadvertently expose users to cross-site scripting (XSS) attacks if this comes from user-provided input. If you have to use dangerouslySetInnerHTML, consider using a sanitization library such as DOMPurify to sanitize your HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://react.dev/reference/react-dom/components/common#dangerously-setting-the-inner-html"], "category": "security", "confidence": "MEDIUM", "technology": ["react"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/typescript.react.security.audit.react-dangerouslysetinnerhtml.react-dangerouslysetinnerhtml", "shortlink": "https://sg.run/rAx6"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "typescript.react.security.audit.react-dangerouslysetinnerhtml.react-dangerouslysetinnerhtml", "path": "downloaded_repos/openstack_skyline-console/src/resources/dns/record.jsx", "start": {"line": 177, "col": 49, "offset": 7914}, "end": {"line": 177, "col": 72, "offset": 7937}, "extra": {"message": "Detection of dangerouslySetInnerHTML from non-constant definition. This can inadvertently expose users to cross-site scripting (XSS) attacks if this comes from user-provided input. If you have to use dangerouslySetInnerHTML, consider using a sanitization library such as DOMPurify to sanitize your HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://react.dev/reference/react-dom/components/common#dangerously-setting-the-inner-html"], "category": "security", "confidence": "MEDIUM", "technology": ["react"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/typescript.react.security.audit.react-dangerouslysetinnerhtml.react-dangerouslysetinnerhtml", "shortlink": "https://sg.run/rAx6"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "typescript.react.security.audit.react-dangerouslysetinnerhtml.react-dangerouslysetinnerhtml", "path": "downloaded_repos/openstack_skyline-console/src/resources/dns/record.jsx", "start": {"line": 180, "col": 49, "offset": 8016}, "end": {"line": 180, "col": 72, "offset": 8039}, "extra": {"message": "Detection of dangerouslySetInnerHTML from non-constant definition. This can inadvertently expose users to cross-site scripting (XSS) attacks if this comes from user-provided input. If you have to use dangerouslySetInnerHTML, consider using a sanitization library such as DOMPurify to sanitize your HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://react.dev/reference/react-dom/components/common#dangerously-setting-the-inner-html"], "category": "security", "confidence": "MEDIUM", "technology": ["react"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/typescript.react.security.audit.react-dangerouslysetinnerhtml.react-dangerouslysetinnerhtml", "shortlink": "https://sg.run/rAx6"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/openstack_skyline-console/src/utils/cookie.js", "start": {"line": 20, "col": 38, "offset": 804}, "end": {"line": 20, "col": 75, "offset": 841}, "extra": {"message": "RegExp() called with a `n` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "typescript.react.security.audit.react-dangerouslysetinnerhtml.react-dangerouslysetinnerhtml", "path": "downloaded_repos/openstack_skyline-console/src/utils/translate.js", "start": {"line": 121, "col": 19, "offset": 4181}, "end": {"line": 121, "col": 22, "offset": 4184}, "extra": {"message": "Detection of dangerouslySetInnerHTML from non-constant definition. This can inadvertently expose users to cross-site scripting (XSS) attacks if this comes from user-provided input. If you have to use dangerouslySetInnerHTML, consider using a sanitization library such as DOMPurify to sanitize your HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://react.dev/reference/react-dom/components/common#dangerously-setting-the-inner-html"], "category": "security", "confidence": "MEDIUM", "technology": ["react"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/typescript.react.security.audit.react-dangerouslysetinnerhtml.react-dangerouslysetinnerhtml", "shortlink": "https://sg.run/rAx6"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/openstack_skyline-console/skyline_console/static/basic.bundle.**********.js:\n ", "path": "downloaded_repos/openstack_skyline-console/skyline_console/static/basic.bundle.**********.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "message": "Timeout when running javascript.express.security.injection.raw-html-format.raw-html-format on downloaded_repos/openstack_skyline-console/skyline_console/static/basic.bundle.**********.js:\n ", "path": "downloaded_repos/openstack_skyline-console/skyline_console/static/basic.bundle.**********.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "message": "Timeout when running javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring on downloaded_repos/openstack_skyline-console/skyline_console/static/basic.bundle.**********.js:\n ", "path": "downloaded_repos/openstack_skyline-console/skyline_console/static/basic.bundle.**********.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/openstack_skyline-console/skyline_console/static/common.bundle.**********.js:\n ", "path": "downloaded_repos/openstack_skyline-console/skyline_console/static/common.bundle.**********.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/openstack_skyline-console/skyline_console/static/common.bundle.**********.js:\n ", "path": "downloaded_repos/openstack_skyline-console/skyline_console/static/common.bundle.**********.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "message": "Timeout when running javascript.express.security.injection.raw-html-format.raw-html-format on downloaded_repos/openstack_skyline-console/skyline_console/static/common.bundle.**********.js:\n ", "path": "downloaded_repos/openstack_skyline-console/skyline_console/static/common.bundle.**********.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/openstack_skyline-console/skyline_console/static/compute.bundle.**********.js:\n ", "path": "downloaded_repos/openstack_skyline-console/skyline_console/static/compute.bundle.**********.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/openstack_skyline-console/skyline_console/static/compute.bundle.**********.js:\n ", "path": "downloaded_repos/openstack_skyline-console/skyline_console/static/compute.bundle.**********.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "message": "Timeout when running javascript.express.security.injection.raw-html-format.raw-html-format on downloaded_repos/openstack_skyline-console/skyline_console/static/compute.bundle.**********.js:\n ", "path": "downloaded_repos/openstack_skyline-console/skyline_console/static/compute.bundle.**********.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/openstack_skyline-console/skyline_console/static/identity.bundle.**********.js:\n ", "path": "downloaded_repos/openstack_skyline-console/skyline_console/static/identity.bundle.**********.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/openstack_skyline-console/skyline_console/static/monitor-center.bundle.**********.js:\n ", "path": "downloaded_repos/openstack_skyline-console/skyline_console/static/monitor-center.bundle.**********.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.lang.security.audit.sqli.node-mysql-sqli.node-mysql-sqli", "message": "Timeout when running javascript.lang.security.audit.sqli.node-mysql-sqli.node-mysql-sqli on downloaded_repos/openstack_skyline-console/skyline_console/static/monitor-center.bundle.**********.js:\n ", "path": "downloaded_repos/openstack_skyline-console/skyline_console/static/monitor-center.bundle.**********.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/openstack_skyline-console/skyline_console/static/network.bundle.**********.js:\n ", "path": "downloaded_repos/openstack_skyline-console/skyline_console/static/network.bundle.**********.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/openstack_skyline-console/skyline_console/static/network.bundle.**********.js:\n ", "path": "downloaded_repos/openstack_skyline-console/skyline_console/static/network.bundle.**********.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "message": "Timeout when running javascript.express.security.injection.raw-html-format.raw-html-format on downloaded_repos/openstack_skyline-console/skyline_console/static/network.bundle.**********.js:\n ", "path": "downloaded_repos/openstack_skyline-console/skyline_console/static/network.bundle.**********.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/openstack_skyline-console/skyline_console/static/share.bundle.**********.js:\n ", "path": "downloaded_repos/openstack_skyline-console/skyline_console/static/share.bundle.**********.js"}], "paths": {"scanned": ["downloaded_repos/openstack_skyline-console/.babelrc", "downloaded_repos/openstack_skyline-console/.eslintignore", "downloaded_repos/openstack_skyline-console/.eslintrc", "downloaded_repos/openstack_skyline-console/.gitignore", "downloaded_repos/openstack_skyline-console/.gitreview", "downloaded_repos/openstack_skyline-console/.prettierignore", "downloaded_repos/openstack_skyline-console/.prettierrc", "downloaded_repos/openstack_skyline-console/.stylelintignore", "downloaded_repos/openstack_skyline-console/.stylelintrc.json", "downloaded_repos/openstack_skyline-console/.zuul.yaml", "downloaded_repos/openstack_skyline-console/Gruntfile.js", "downloaded_repos/openstack_skyline-console/LICENSE", "downloaded_repos/openstack_skyline-console/MANIFEST.in", "downloaded_repos/openstack_skyline-console/Makefile", "downloaded_repos/openstack_skyline-console/README/README-ko_KR.rst", "downloaded_repos/openstack_skyline-console/README/README-zh_CN.rst", "downloaded_repos/openstack_skyline-console/README.rst", "downloaded_repos/openstack_skyline-console/config/config.yaml", "downloaded_repos/openstack_skyline-console/config/js-string-replace-loader.js", "downloaded_repos/openstack_skyline-console/config/less-replace-loader.js", "downloaded_repos/openstack_skyline-console/config/server.dev.js", "downloaded_repos/openstack_skyline-console/config/theme-custom.js", "downloaded_repos/openstack_skyline-console/config/theme.js", "downloaded_repos/openstack_skyline-console/config/utils.js", "downloaded_repos/openstack_skyline-console/config/webpack.common.js", "downloaded_repos/openstack_skyline-console/config/webpack.dev.js", "downloaded_repos/openstack_skyline-console/config/webpack.e2e.js", "downloaded_repos/openstack_skyline-console/config/webpack.prod.js", "downloaded_repos/openstack_skyline-console/cypress.json", "downloaded_repos/openstack_skyline-console/doc/requirements.txt", "downloaded_repos/openstack_skyline-console/doc/source/admin/compute.rst", "downloaded_repos/openstack_skyline-console/doc/source/admin/identity.rst", "downloaded_repos/openstack_skyline-console/doc/source/admin/index.rst", "downloaded_repos/openstack_skyline-console/doc/source/admin/storage.rst", "downloaded_repos/openstack_skyline-console/doc/source/conf.py", "downloaded_repos/openstack_skyline-console/doc/source/configuration/index.rst", "downloaded_repos/openstack_skyline-console/doc/source/configuration/skyline-console-settings.rst", "downloaded_repos/openstack_skyline-console/doc/source/contributor/backporting.rst", "downloaded_repos/openstack_skyline-console/doc/source/contributor/contributing.rst", "downloaded_repos/openstack_skyline-console/doc/source/contributor/development.environment.rst", "downloaded_repos/openstack_skyline-console/doc/source/contributor/documentation.rst", "downloaded_repos/openstack_skyline-console/doc/source/contributor/gerrit.rst", "downloaded_repos/openstack_skyline-console/doc/source/contributor/index.rst", "downloaded_repos/openstack_skyline-console/doc/source/contributor/releasenotes.rst", "downloaded_repos/openstack_skyline-console/doc/source/contributor/releases.rst", "downloaded_repos/openstack_skyline-console/doc/source/development/catalog-introduction.rst", "downloaded_repos/openstack_skyline-console/doc/source/development/index.rst", "downloaded_repos/openstack_skyline-console/doc/source/development/ready-to-work.rst", "downloaded_repos/openstack_skyline-console/doc/source/glossary.rst", "downloaded_repos/openstack_skyline-console/doc/source/images/.placeholder", "downloaded_repos/openstack_skyline-console/doc/source/index.rst", "downloaded_repos/openstack_skyline-console/doc/source/install/docker-install-rhel.rst", "downloaded_repos/openstack_skyline-console/doc/source/install/docker-install-ubuntu.rst", "downloaded_repos/openstack_skyline-console/doc/source/install/index.rst", "downloaded_repos/openstack_skyline-console/doc/source/install/installing-guide.rst", "downloaded_repos/openstack_skyline-console/doc/source/install/source-install-rhel.rst", "downloaded_repos/openstack_skyline-console/doc/source/install/source-install-ubuntu.rst", "downloaded_repos/openstack_skyline-console/doc/source/install/system-requirements.rst", "downloaded_repos/openstack_skyline-console/doc/source/user/compute.rst", "downloaded_repos/openstack_skyline-console/doc/source/user/dashboard.rst", "downloaded_repos/openstack_skyline-console/doc/source/user/index.rst", "downloaded_repos/openstack_skyline-console/doc/source/user/network.rst", "downloaded_repos/openstack_skyline-console/doc/source/user/storage.rst", "downloaded_repos/openstack_skyline-console/doc/source/user/supported-browsers.rst", "downloaded_repos/openstack_skyline-console/docs/en/develop/1-ready-to-work.md", "downloaded_repos/openstack_skyline-console/docs/en/develop/2-catalog-introduction.md", "downloaded_repos/openstack_skyline-console/docs/en/develop/3-0-how-to-develop.md", "downloaded_repos/openstack_skyline-console/docs/en/develop/3-1-BaseList-introduction.md", "downloaded_repos/openstack_skyline-console/docs/en/develop/3-10-FormItem-introduction.md", "downloaded_repos/openstack_skyline-console/docs/en/develop/3-11-Action-introduction.md", "downloaded_repos/openstack_skyline-console/docs/en/develop/3-12-Menu-introduction.md", "downloaded_repos/openstack_skyline-console/docs/en/develop/3-13-Route-introduction.md", "downloaded_repos/openstack_skyline-console/docs/en/develop/3-14-I18n-introduction.md", "downloaded_repos/openstack_skyline-console/docs/en/develop/3-2-BaseTabList-introduction.md", "downloaded_repos/openstack_skyline-console/docs/en/develop/3-3-BaseDetail-introduction.md", "downloaded_repos/openstack_skyline-console/docs/en/develop/3-4-BaseDetailInfo-introduction.md", "downloaded_repos/openstack_skyline-console/docs/en/develop/3-5-BaseStore-introduction.md", "downloaded_repos/openstack_skyline-console/docs/en/develop/3-6-FormAction-introduction.md", "downloaded_repos/openstack_skyline-console/docs/en/develop/3-7-ModalAction-introduction.md", "downloaded_repos/openstack_skyline-console/docs/en/develop/3-8-ConfirmAction-introduction.md", "downloaded_repos/openstack_skyline-console/docs/en/develop/3-9-StepAction-introduction.md", "downloaded_repos/openstack_skyline-console/docs/en/develop/images/detail/image-detail-info.png", "downloaded_repos/openstack_skyline-console/docs/en/develop/images/detail/volume.png", "downloaded_repos/openstack_skyline-console/docs/en/develop/images/form/create-success.png", "downloaded_repos/openstack_skyline-console/docs/en/develop/images/form/page.png", "downloaded_repos/openstack_skyline-console/docs/en/develop/images/list/batch.png", "downloaded_repos/openstack_skyline-console/docs/en/develop/images/list/download.png", "downloaded_repos/openstack_skyline-console/docs/en/develop/images/list/fresh.png", "downloaded_repos/openstack_skyline-console/docs/en/develop/images/list/hide.png", "downloaded_repos/openstack_skyline-console/docs/en/develop/images/list/pagination.png", "downloaded_repos/openstack_skyline-console/docs/en/develop/images/list/search-example.png", "downloaded_repos/openstack_skyline-console/docs/en/develop/images/list/search.png", "downloaded_repos/openstack_skyline-console/docs/en/develop/images/list/stop-auto-refresh.png", "downloaded_repos/openstack_skyline-console/docs/en/develop/images/list/tab-list.png", "downloaded_repos/openstack_skyline-console/docs/en/develop/images/list/tab-service.png", "downloaded_repos/openstack_skyline-console/docs/en/develop/images/list/volumes.png", "downloaded_repos/openstack_skyline-console/docs/en/develop/images/store/response-key.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/1-ready-to-work.md", "downloaded_repos/openstack_skyline-console/docs/zh/develop/2-catalog-introduction.md", "downloaded_repos/openstack_skyline-console/docs/zh/develop/3-0-how-to-develop.md", "downloaded_repos/openstack_skyline-console/docs/zh/develop/3-1-BaseList-introduction.md", "downloaded_repos/openstack_skyline-console/docs/zh/develop/3-10-FormItem-introduction.md", "downloaded_repos/openstack_skyline-console/docs/zh/develop/3-11-Action-introduction.md", "downloaded_repos/openstack_skyline-console/docs/zh/develop/3-12-Menu-introduction.md", "downloaded_repos/openstack_skyline-console/docs/zh/develop/3-13-Route-introduction.md", "downloaded_repos/openstack_skyline-console/docs/zh/develop/3-14-I18n-introduction.md", "downloaded_repos/openstack_skyline-console/docs/zh/develop/3-2-BaseTabList-introduction.md", "downloaded_repos/openstack_skyline-console/docs/zh/develop/3-3-BaseDetail-introduction.md", "downloaded_repos/openstack_skyline-console/docs/zh/develop/3-4-BaseDetailInfo-introduction.md", "downloaded_repos/openstack_skyline-console/docs/zh/develop/3-5-BaseStore-introduction.md", "downloaded_repos/openstack_skyline-console/docs/zh/develop/3-6-FormAction-introduction.md", "downloaded_repos/openstack_skyline-console/docs/zh/develop/3-7-ModalAction-introduction.md", "downloaded_repos/openstack_skyline-console/docs/zh/develop/3-8-ConfirmAction-introduction.md", "downloaded_repos/openstack_skyline-console/docs/zh/develop/3-9-StepAction-introduction.md", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/detail/image-detail-info.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/detail/volume.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/ace-editor.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/action.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/add-select.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/check-group.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/check.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/confirm.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/courgette.log", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/create-success.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/descriptions.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/form-divider.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/form-extra.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/form-label.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/form-tip.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/input-int.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/input-json.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/input-name.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/input-number.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/input-password.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/input.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/instance-action.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/instance-volume.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/ip-distributer.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/ip-input.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/label-col.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/mac-address.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/member-allocator.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/metadata-transfer.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/modal.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/more.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/network-select-table.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/network-select.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/page.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/port-range.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/radio.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/select-table-tabs.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/select-table.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/select.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/slider-input.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/step.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/switch.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/tab-select-table.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/textarea-from-file.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/textarea.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/title.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/transfer.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/upload.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/volume-action.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/volume-select-table.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/form/wrapper-col.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/i18n/english.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/i18n/i18n.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/list/batch.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/list/download.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/list/fresh.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/list/hide.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/list/pagination.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/list/search-example.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/list/search.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/list/stop-auto-refresh.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/list/tab-list.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/list/tab-service.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/list/volumes.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/menu/admin-menu.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/menu/console-menu.png", "downloaded_repos/openstack_skyline-console/docs/zh/develop/images/store/response-key.png", "downloaded_repos/openstack_skyline-console/jest.config.js", "downloaded_repos/openstack_skyline-console/jsconfig.json", "downloaded_repos/openstack_skyline-console/package.json", "downloaded_repos/openstack_skyline-console/playbooks/devstack/compute/run-devstack.yaml", "downloaded_repos/openstack_skyline-console/playbooks/devstack/compute/run-e2etests.yaml", "downloaded_repos/openstack_skyline-console/playbooks/devstack/network/run-devstack.yaml", "downloaded_repos/openstack_skyline-console/playbooks/devstack/network/run-e2etests.yaml", "downloaded_repos/openstack_skyline-console/playbooks/devstack/other/run-devstack.yaml", "downloaded_repos/openstack_skyline-console/playbooks/devstack/other/run-e2etests.yaml", "downloaded_repos/openstack_skyline-console/playbooks/devstack/post.yaml", "downloaded_repos/openstack_skyline-console/playbooks/devstack/pre.yaml", "downloaded_repos/openstack_skyline-console/playbooks/devstack/storage/run-devstack.yaml", "downloaded_repos/openstack_skyline-console/playbooks/devstack/storage/run-e2etests.yaml", "downloaded_repos/openstack_skyline-console/playbooks/python-tarball/post.yaml", "downloaded_repos/openstack_skyline-console/playbooks/python-tarball/pre.yaml", "downloaded_repos/openstack_skyline-console/playbooks/python-tarball/run.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/.placeholder", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Add-JWT-Expire-Check-c4dce4d269782a8c.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/FIX-ZUN-UI-997f060449876f33.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Fix-Change-Password-And-Password-Validation-1a9633368fb8dbcb.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Fix-Label-Of-Node-Count-In-Magnum-Cluster-207d96a2899569b4.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Fix-Network-Availability-And-Usage-Stats-Showing-46c89740ccc36e04.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Optimize-Configuration-Reading-5914bb51c3ecb0c4.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Optimize-ID-Name-Column-1b5248237f6f7053.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Optimize-System-Setting-Display-48aff7362665a942.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Show-Data-Disk-Of-Snapshot-3b23e998665187c1.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Support-A-Custom-Theme-fe56c9dc8d3bfe03.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Support-Application-Credential-Unrestricted-1940ac44f96576ac.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Support-Attach-Network-And-Detach-Network-For-Zun-Container-e41980df3f67c5b5.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Support-Clear-Selected-80e66080d37c96e3.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Support-Custom-Button-For-Upload-c44a4ff8e55c5961.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Support-Custom-Local-Language-84ad3016c2469a51.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Support-Disable-Enable-Router-SNAT-1f09d5e8aa1a07bb.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Support-Domain-Name-Filter-1d433cb2d548ca2f.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Support-Edit-Domain-Name-874d87160550c8f0.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Support-External-Network-Name-For-Router-760ab8b579e548c7.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Support-Flavor-Add-CPU-Memory-Search-f6b17125d7f82df7.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Support-Fwaas-V2-055c1e400f093ede.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Support-Global-Navigation-45412aa3603c4f14.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Support-Insert-Headers-For-LB-Listener-32142f05b85eec4f.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Support-Instance-For-Openstack-Service-Monitor-4e2fb683f379b639.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Support-Korean-I18n-6f258836f7b30db9.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Support-No-Boot-From-Volume-Instance-09f802a7c0c53052.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Support-No-Cinder-73ab2fe7c0a40324.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Support-Progress-And-Abort-For-Download-cf85833aaac25fec.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Support-Quick-Create-Keypair-In-Magnum-0b10264145d52825.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Support-Quota-Of-Magnum-Cluster-67f1fba7a4adba4d.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Support-Refresh-Button-For-Select-Table-5312ff8105325055.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Support-SSO-Login-via-OpenID-8ed955e35ebb1f1d.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Support-Setting-Admin-State-Up-For-Pool-eca4a829af8c0446.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Support-User-Default-Project-e939be97d8f7e5cf.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Support-create-cluster-in-template-b7a38cd1a90f30bd.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Support-magnum-in-administrator-platform-07bc09b2eaeaf87f.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Support-non-Root-Users-0792a1ba891b28eb.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Support-upgrade-cluster-and-quota-info-when-resize-cluster-a963cfeb4cad01a6.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Update-Avatar-Click-d2ca694d56e15b75.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Update-Domain-Input-For-Login-8acd4d7ad63f0dc2.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Update-Editing-Health-Monitor-90ff623bcdb06f3b.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Update-Editing-LB-Listener-aa133b1e5243bb71.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Update-Editing-Pool-Info-7094e9265fe01b84.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/Update-Table-Header-e279ab2ff792cbb6.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/add-admin-state-loadbalancer-switches-721264bd7d7bcf75.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/add-russian-language-support-bdb66e3240a0a4a5.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/add-turkish-language-support-31da83e300fab0bf.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/feat-volume-deletion-on-instance-delete-9a83da6ca4fd2107.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/feat_adding_terminal_zun_detail-7a1e18e800b20a2d.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/feat_docker_images_to_glance-47b377ab582be109.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/feat_instance_log_to_nova-c14b17bdd1e4b708.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/feat_rbac_policies_to_neutron-1578109a8642e3a3.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/notes/zed-rc1-releasenotes-b1d539839f771e2e.yaml", "downloaded_repos/openstack_skyline-console/releasenotes/source/2023.1.rst", "downloaded_repos/openstack_skyline-console/releasenotes/source/2023.2.rst", "downloaded_repos/openstack_skyline-console/releasenotes/source/2024.1.rst", "downloaded_repos/openstack_skyline-console/releasenotes/source/2024.2.rst", "downloaded_repos/openstack_skyline-console/releasenotes/source/2025.1.rst", "downloaded_repos/openstack_skyline-console/releasenotes/source/_static/.placeholder", "downloaded_repos/openstack_skyline-console/releasenotes/source/_templates/.placeholder", "downloaded_repos/openstack_skyline-console/releasenotes/source/conf.py", "downloaded_repos/openstack_skyline-console/releasenotes/source/index.rst", "downloaded_repos/openstack_skyline-console/releasenotes/source/unreleased.rst", "downloaded_repos/openstack_skyline-console/releasenotes/source/zed.rst", "downloaded_repos/openstack_skyline-console/requirements.txt", "downloaded_repos/openstack_skyline-console/setup.cfg", "downloaded_repos/openstack_skyline-console/setup.py", "downloaded_repos/openstack_skyline-console/skyline_console/__init__.py", "downloaded_repos/openstack_skyline-console/skyline_console/__main__.py", "downloaded_repos/openstack_skyline-console/skyline_console/py.typed", "downloaded_repos/openstack_skyline-console/skyline_console/static/Container.bundle.**********.js", "downloaded_repos/openstack_skyline-console/skyline_console/static/Container.bundle.**********.js.gz", "downloaded_repos/openstack_skyline-console/skyline_console/static/asset/image/cloud.**********.png", "downloaded_repos/openstack_skyline-console/skyline_console/static/asset/image/empty-card.**********.svg", "downloaded_repos/openstack_skyline-console/skyline_console/static/asset/image/load-balancer.**********.png", "downloaded_repos/openstack_skyline-console/skyline_console/static/asset/image/login-full.**********.png", "downloaded_repos/openstack_skyline-console/skyline_console/static/asset/image/loginRightLogo.png", "downloaded_repos/openstack_skyline-console/skyline_console/static/asset/image/logo-extend.svg", "downloaded_repos/openstack_skyline-console/skyline_console/static/asset/image/logo-small.svg", "downloaded_repos/openstack_skyline-console/skyline_console/static/asset/image/logo.png", "downloaded_repos/openstack_skyline-console/skyline_console/static/asset/image/router.**********.png", "downloaded_repos/openstack_skyline-console/skyline_console/static/auth.bundle.**********.js", "downloaded_repos/openstack_skyline-console/skyline_console/static/auth.bundle.**********.js.gz", "downloaded_repos/openstack_skyline-console/skyline_console/static/base.bundle.**********.js", "downloaded_repos/openstack_skyline-console/skyline_console/static/base.bundle.**********.js.gz", "downloaded_repos/openstack_skyline-console/skyline_console/static/basic.bundle.**********.js", "downloaded_repos/openstack_skyline-console/skyline_console/static/basic.bundle.**********.js.gz", "downloaded_repos/openstack_skyline-console/skyline_console/static/common.bundle.**********.js", "downloaded_repos/openstack_skyline-console/skyline_console/static/common.bundle.**********.js.gz", "downloaded_repos/openstack_skyline-console/skyline_console/static/compute.bundle.**********.js", "downloaded_repos/openstack_skyline-console/skyline_console/static/compute.bundle.**********.js.gz", "downloaded_repos/openstack_skyline-console/skyline_console/static/configuration.bundle.**********.js", "downloaded_repos/openstack_skyline-console/skyline_console/static/configuration.bundle.**********.js.gz", "downloaded_repos/openstack_skyline-console/skyline_console/static/container-infra.bundle.**********.js", "downloaded_repos/openstack_skyline-console/skyline_console/static/container-infra.bundle.**********.js.gz", "downloaded_repos/openstack_skyline-console/skyline_console/static/f7186078e00d958aa2b316483dfc7e1c.js", "downloaded_repos/openstack_skyline-console/skyline_console/static/f7186078e00d958aa2b316483dfc7e1c.js.gz", "downloaded_repos/openstack_skyline-console/skyline_console/static/favicon.ico", "downloaded_repos/openstack_skyline-console/skyline_console/static/heat.bundle.**********.js", "downloaded_repos/openstack_skyline-console/skyline_console/static/heat.bundle.**********.js.gz", "downloaded_repos/openstack_skyline-console/skyline_console/static/identity.bundle.**********.js", "downloaded_repos/openstack_skyline-console/skyline_console/static/identity.bundle.**********.js.gz", "downloaded_repos/openstack_skyline-console/skyline_console/static/index.html", "downloaded_repos/openstack_skyline-console/skyline_console/static/main.bundle.**********.js.gz", "downloaded_repos/openstack_skyline-console/skyline_console/static/management.bundle.**********.js", "downloaded_repos/openstack_skyline-console/skyline_console/static/monitor-center.bundle.**********.js", "downloaded_repos/openstack_skyline-console/skyline_console/static/monitor-center.bundle.**********.js.gz", "downloaded_repos/openstack_skyline-console/skyline_console/static/network.bundle.**********.js", "downloaded_repos/openstack_skyline-console/skyline_console/static/network.bundle.**********.js.gz", "downloaded_repos/openstack_skyline-console/skyline_console/static/runtime.**********.js", "downloaded_repos/openstack_skyline-console/skyline_console/static/share.bundle.**********.js", "downloaded_repos/openstack_skyline-console/skyline_console/static/share.bundle.**********.js.gz", "downloaded_repos/openstack_skyline-console/skyline_console/static/storage.bundle.**********.js", "downloaded_repos/openstack_skyline-console/skyline_console/static/storage.bundle.**********.js.gz", "downloaded_repos/openstack_skyline-console/skyline_console/static/user-center.bundle.**********.js", "downloaded_repos/openstack_skyline-console/skyline_console/static/user-center.bundle.**********.js.gz", "downloaded_repos/openstack_skyline-console/skyline_console/version.py", "downloaded_repos/openstack_skyline-console/src/asset/image/admin-image.svg", "downloaded_repos/openstack_skyline-console/src/asset/image/admin-instance.svg", "downloaded_repos/openstack_skyline-console/src/asset/image/admin-network.svg", "downloaded_repos/openstack_skyline-console/src/asset/image/admin-router.svg", "downloaded_repos/openstack_skyline-console/src/asset/image/admin-security-group.svg", "downloaded_repos/openstack_skyline-console/src/asset/image/admin-volume.svg", "downloaded_repos/openstack_skyline-console/src/asset/image/animbus.png", "downloaded_repos/openstack_skyline-console/src/asset/image/arch.svg", "downloaded_repos/openstack_skyline-console/src/asset/image/centos.svg", "downloaded_repos/openstack_skyline-console/src/asset/image/cloud-logo-white.svg", "downloaded_repos/openstack_skyline-console/src/asset/image/cloud-logo.svg", "downloaded_repos/openstack_skyline-console/src/asset/image/cloud.png", "downloaded_repos/openstack_skyline-console/src/asset/image/coreos.svg", "downloaded_repos/openstack_skyline-console/src/asset/image/debian.svg", "downloaded_repos/openstack_skyline-console/src/asset/image/empty-card.svg", "downloaded_repos/openstack_skyline-console/src/asset/image/favicon.ico", "downloaded_repos/openstack_skyline-console/src/asset/image/fedora.svg", "downloaded_repos/openstack_skyline-console/src/asset/image/freebsd.svg", "downloaded_repos/openstack_skyline-console/src/asset/image/global-menu.png", "downloaded_repos/openstack_skyline-console/src/asset/image/image.svg", "downloaded_repos/openstack_skyline-console/src/asset/image/instance.png", "downloaded_repos/openstack_skyline-console/src/asset/image/instance.svg", "downloaded_repos/openstack_skyline-console/src/asset/image/interface.png", "downloaded_repos/openstack_skyline-console/src/asset/image/interface.svg", "downloaded_repos/openstack_skyline-console/src/asset/image/load-balancer.png", "downloaded_repos/openstack_skyline-console/src/asset/image/lock.svg", "downloaded_repos/openstack_skyline-console/src/asset/image/login-full.png", "downloaded_repos/openstack_skyline-console/src/asset/image/loginRightLogo.png", "downloaded_repos/openstack_skyline-console/src/asset/image/logo.png", "downloaded_repos/openstack_skyline-console/src/asset/image/others.svg", "downloaded_repos/openstack_skyline-console/src/asset/image/overview-instance.svg", "downloaded_repos/openstack_skyline-console/src/asset/image/overview-network.svg", "downloaded_repos/openstack_skyline-console/src/asset/image/overview-router.svg", "downloaded_repos/openstack_skyline-console/src/asset/image/overview-volume.svg", "downloaded_repos/openstack_skyline-console/src/asset/image/profile.svg", "downloaded_repos/openstack_skyline-console/src/asset/image/router.png", "downloaded_repos/openstack_skyline-console/src/asset/image/security-group.svg", "downloaded_repos/openstack_skyline-console/src/asset/image/ubuntu.svg", "downloaded_repos/openstack_skyline-console/src/asset/image/unlock.svg", "downloaded_repos/openstack_skyline-console/src/asset/image/volume.svg", "downloaded_repos/openstack_skyline-console/src/asset/image/windows.svg", "downloaded_repos/openstack_skyline-console/src/asset/template/index.html", "downloaded_repos/openstack_skyline-console/src/client/barbican/index.js", "downloaded_repos/openstack_skyline-console/src/client/cinder/index.js", "downloaded_repos/openstack_skyline-console/src/client/client/base.js", "downloaded_repos/openstack_skyline-console/src/client/client/constants.js", "downloaded_repos/openstack_skyline-console/src/client/client/request.js", "downloaded_repos/openstack_skyline-console/src/client/designate/index.js", "downloaded_repos/openstack_skyline-console/src/client/glance/index.js", "downloaded_repos/openstack_skyline-console/src/client/heat/index.js", "downloaded_repos/openstack_skyline-console/src/client/index.js", "downloaded_repos/openstack_skyline-console/src/client/ironic/index.js", "downloaded_repos/openstack_skyline-console/src/client/keystone/index.js", "downloaded_repos/openstack_skyline-console/src/client/magnum/index.js", "downloaded_repos/openstack_skyline-console/src/client/manila/index.js", "downloaded_repos/openstack_skyline-console/src/client/masakari/index.js", "downloaded_repos/openstack_skyline-console/src/client/neutron/index.js", "downloaded_repos/openstack_skyline-console/src/client/nova/index.js", "downloaded_repos/openstack_skyline-console/src/client/octavia/index.js", "downloaded_repos/openstack_skyline-console/src/client/placement/index.js", "downloaded_repos/openstack_skyline-console/src/client/skyline/index.js", "downloaded_repos/openstack_skyline-console/src/client/swift/index.js", "downloaded_repos/openstack_skyline-console/src/client/trove/index.js", "downloaded_repos/openstack_skyline-console/src/client/zun/index.js", "downloaded_repos/openstack_skyline-console/src/components/Cards/NotFound/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/Cards/NotFound/index.less", "downloaded_repos/openstack_skyline-console/src/components/CodeEditor/AceEditor.jsx", "downloaded_repos/openstack_skyline-console/src/components/CodeEditor/custom.less", "downloaded_repos/openstack_skyline-console/src/components/CodeEditor/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/CodeEditor/index.less", "downloaded_repos/openstack_skyline-console/src/components/Confirm/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/Confirm/index.less", "downloaded_repos/openstack_skyline-console/src/components/DetailCard/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/DetailCard/index.less", "downloaded_repos/openstack_skyline-console/src/components/Form/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/Form/index.less", "downloaded_repos/openstack_skyline-console/src/components/FormItem/AddSelect/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/AddSelect/index.less", "downloaded_repos/openstack_skyline-console/src/components/FormItem/Checkbox/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/CheckboxGroup/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/Descriptions/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/Descriptions/index.less", "downloaded_repos/openstack_skyline-console/src/components/FormItem/IPDistributor/IPAddress.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/IPDistributor/Item.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/IPDistributor/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/InputInt/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/InstanceVolume/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/InstanceVolume/index.less", "downloaded_repos/openstack_skyline-console/src/components/FormItem/InternationalPhoneNumberInput/countries.js", "downloaded_repos/openstack_skyline-console/src/components/FormItem/InternationalPhoneNumberInput/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/IpInput/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/IpInput/index.less", "downloaded_repos/openstack_skyline-console/src/components/FormItem/IpInputSimple/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/IpInputSimple/index.less", "downloaded_repos/openstack_skyline-console/src/components/FormItem/JsonInput/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/KeyValueInput/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/Label/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/Label/index.less", "downloaded_repos/openstack_skyline-console/src/components/FormItem/MacAddressInput/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/MemberAllocator/IPAddress.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/MemberAllocator/Item.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/MemberAllocator/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/MetadataTransfer/EnumSelect.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/MetadataTransfer/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/More/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/More/index.less", "downloaded_repos/openstack_skyline-console/src/components/FormItem/NUMAInput/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/NUMAInput/index.less", "downloaded_repos/openstack_skyline-console/src/components/FormItem/NameInput/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/NetworkSelect/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/NetworkSelect/index.less", "downloaded_repos/openstack_skyline-console/src/components/FormItem/NetworkSelectTable/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/PortRange/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/Radio/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/Radio/index.less", "downloaded_repos/openstack_skyline-console/src/components/FormItem/Select/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/Select/index.less", "downloaded_repos/openstack_skyline-console/src/components/FormItem/SelectTable/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/SelectTable/index.less", "downloaded_repos/openstack_skyline-console/src/components/FormItem/SelectWithInput/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/SliderInput/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/Switch/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/TabSelectTable/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/TextareaFromFile/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/Title/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/Title/index.less", "downloaded_repos/openstack_skyline-console/src/components/FormItem/Transfer/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/TreeSelect/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/Upload/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/VolumeSelectTable/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/FormItem/index.less", "downloaded_repos/openstack_skyline-console/src/components/ImageType/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/ImageType/index.less", "downloaded_repos/openstack_skyline-console/src/components/InfoButton/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/Infos/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/Infos/index.less", "downloaded_repos/openstack_skyline-console/src/components/Layout/GlobalHeader/AvatarDropdown.jsx", "downloaded_repos/openstack_skyline-console/src/components/Layout/GlobalHeader/OpenRc.jsx", "downloaded_repos/openstack_skyline-console/src/components/Layout/GlobalHeader/Password.jsx", "downloaded_repos/openstack_skyline-console/src/components/Layout/GlobalHeader/ProjectDropdown.jsx", "downloaded_repos/openstack_skyline-console/src/components/Layout/GlobalHeader/ProjectTable.jsx", "downloaded_repos/openstack_skyline-console/src/components/Layout/GlobalHeader/RightContent.jsx", "downloaded_repos/openstack_skyline-console/src/components/Layout/GlobalHeader/Token.jsx", "downloaded_repos/openstack_skyline-console/src/components/Layout/GlobalHeader/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/Layout/GlobalHeader/index.less", "downloaded_repos/openstack_skyline-console/src/components/Layout/GlobalNav/Left/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/Layout/GlobalNav/Left/index.less", "downloaded_repos/openstack_skyline-console/src/components/Layout/GlobalNav/Right/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/Layout/GlobalNav/Right/index.less", "downloaded_repos/openstack_skyline-console/src/components/Layout/GlobalNav/common.jsx", "downloaded_repos/openstack_skyline-console/src/components/Layout/GlobalNav/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/Layout/GlobalNav/index.less", "downloaded_repos/openstack_skyline-console/src/components/Layout/HeaderDropdown/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/Layout/HeaderDropdown/index.less", "downloaded_repos/openstack_skyline-console/src/components/Loading/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/MagicInput/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/MagicInput/index.less", "downloaded_repos/openstack_skyline-console/src/components/ModalButton/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/Notify/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/Notify/index.less", "downloaded_repos/openstack_skyline-console/src/components/PageLoading/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/Pagination/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/Pagination/index.less", "downloaded_repos/openstack_skyline-console/src/components/Popover/PopActionEvent.jsx", "downloaded_repos/openstack_skyline-console/src/components/Popover/Popover.jsx", "downloaded_repos/openstack_skyline-console/src/components/Popover/PopoverNetworks.jsx", "downloaded_repos/openstack_skyline-console/src/components/Popover/PopoverSubnets.jsx", "downloaded_repos/openstack_skyline-console/src/components/Progress/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/ProjectProgress/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/PrometheusChart/BaseCard.jsx", "downloaded_repos/openstack_skyline-console/src/components/PrometheusChart/ChartCard.jsx", "downloaded_repos/openstack_skyline-console/src/components/PrometheusChart/CircleWithRightLegend.jsx", "downloaded_repos/openstack_skyline-console/src/components/PrometheusChart/component/BaseContent.jsx", "downloaded_repos/openstack_skyline-console/src/components/PrometheusChart/component/Charts.jsx", "downloaded_repos/openstack_skyline-console/src/components/PrometheusChart/component/Intervals.jsx", "downloaded_repos/openstack_skyline-console/src/components/PrometheusChart/component/context.js", "downloaded_repos/openstack_skyline-console/src/components/PrometheusChart/component/hooks/useIntervals.jsx", "downloaded_repos/openstack_skyline-console/src/components/PrometheusChart/component/hooks/useNodeSelect.jsx", "downloaded_repos/openstack_skyline-console/src/components/PrometheusChart/component/hooks/useRangeSelect.jsx", "downloaded_repos/openstack_skyline-console/src/components/PrometheusChart/component/styles.less", "downloaded_repos/openstack_skyline-console/src/components/PrometheusChart/style.less", "downloaded_repos/openstack_skyline-console/src/components/PrometheusChart/utils/baseProps.js", "downloaded_repos/openstack_skyline-console/src/components/PrometheusChart/utils/dataHandler.js", "downloaded_repos/openstack_skyline-console/src/components/PrometheusChart/utils/fetchNodes.js", "downloaded_repos/openstack_skyline-console/src/components/PrometheusChart/utils/index.js", "downloaded_repos/openstack_skyline-console/src/components/PrometheusChart/utils/utils.js", "downloaded_repos/openstack_skyline-console/src/components/QuotaChart/Info.jsx", "downloaded_repos/openstack_skyline-console/src/components/QuotaChart/Line.jsx", "downloaded_repos/openstack_skyline-console/src/components/QuotaChart/Ring.jsx", "downloaded_repos/openstack_skyline-console/src/components/QuotaChart/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/SelectLang/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/SelectLang/index.less", "downloaded_repos/openstack_skyline-console/src/components/SimpleForm/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/Status/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/StepForm/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/StepForm/index.less", "downloaded_repos/openstack_skyline-console/src/components/TableButton/RuleButton.jsx", "downloaded_repos/openstack_skyline-console/src/components/TableButton/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/Tables/Base/Action/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/Tables/Base/ActionButton/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/Tables/Base/ActionButton/index.less", "downloaded_repos/openstack_skyline-console/src/components/Tables/Base/BatchActionButtons/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/Tables/Base/BatchActionButtons/index.less", "downloaded_repos/openstack_skyline-console/src/components/Tables/Base/CustomColumns/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/Tables/Base/CustomColumns/index.less", "downloaded_repos/openstack_skyline-console/src/components/Tables/Base/Download/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/Tables/Base/Download/index.less", "downloaded_repos/openstack_skyline-console/src/components/Tables/Base/ItemActionButtons/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/Tables/Base/ItemActionButtons/index.less", "downloaded_repos/openstack_skyline-console/src/components/Tables/Base/PrimaryActionButtons/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/Tables/Base/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/Tables/Base/index.less", "downloaded_repos/openstack_skyline-console/src/components/Tables/SimpleTable/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/Tables/SimpleTable/index.less", "downloaded_repos/openstack_skyline-console/src/components/Tags/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/TimeFilter/index.jsx", "downloaded_repos/openstack_skyline-console/src/components/TimeFilter/index.less", "downloaded_repos/openstack_skyline-console/src/components/VisibleObserver/index.jsx", "downloaded_repos/openstack_skyline-console/src/containers/Action/ConfirmAction/index.jsx", "downloaded_repos/openstack_skyline-console/src/containers/Action/FormAction/index.jsx", "downloaded_repos/openstack_skyline-console/src/containers/Action/ModalAction/index.jsx", "downloaded_repos/openstack_skyline-console/src/containers/Action/StepAction/index.jsx", "downloaded_repos/openstack_skyline-console/src/containers/Action/index.jsx", "downloaded_repos/openstack_skyline-console/src/containers/BaseDetail/index.jsx", "downloaded_repos/openstack_skyline-console/src/containers/BaseDetail/index.less", "downloaded_repos/openstack_skyline-console/src/containers/List/index.jsx", "downloaded_repos/openstack_skyline-console/src/containers/List/index.less", "downloaded_repos/openstack_skyline-console/src/containers/TabDetail/index.jsx", "downloaded_repos/openstack_skyline-console/src/containers/TabDetail/index.less", "downloaded_repos/openstack_skyline-console/src/containers/TabList/index.jsx", "downloaded_repos/openstack_skyline-console/src/containers/TabList/index.less", "downloaded_repos/openstack_skyline-console/src/core/App.jsx", "downloaded_repos/openstack_skyline-console/src/core/i18n.js", "downloaded_repos/openstack_skyline-console/src/core/index.jsx", "downloaded_repos/openstack_skyline-console/src/core/routes.js", "downloaded_repos/openstack_skyline-console/src/layouts/Auth/index.jsx", "downloaded_repos/openstack_skyline-console/src/layouts/Auth/index.less", "downloaded_repos/openstack_skyline-console/src/layouts/Base/Menu.jsx", "downloaded_repos/openstack_skyline-console/src/layouts/Base/Right.jsx", "downloaded_repos/openstack_skyline-console/src/layouts/Base/index.jsx", "downloaded_repos/openstack_skyline-console/src/layouts/Base/index.less", "downloaded_repos/openstack_skyline-console/src/layouts/Basic/index.jsx", "downloaded_repos/openstack_skyline-console/src/layouts/Blank/index.jsx", "downloaded_repos/openstack_skyline-console/src/layouts/Blank/index.less", "downloaded_repos/openstack_skyline-console/src/layouts/admin-menu.jsx", "downloaded_repos/openstack_skyline-console/src/layouts/menu.jsx", "downloaded_repos/openstack_skyline-console/src/layouts/user-menu.jsx", "downloaded_repos/openstack_skyline-console/src/locales/en.json", "downloaded_repos/openstack_skyline-console/src/locales/index.js", "downloaded_repos/openstack_skyline-console/src/locales/ko-kr.json", "downloaded_repos/openstack_skyline-console/src/locales/ru.json", "downloaded_repos/openstack_skyline-console/src/locales/tr-tr.json", "downloaded_repos/openstack_skyline-console/src/locales/zh-hans.json", "downloaded_repos/openstack_skyline-console/src/pages/auth/App.jsx", "downloaded_repos/openstack_skyline-console/src/pages/auth/containers/ChangePassword/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/auth/containers/ChangePassword/index.less", "downloaded_repos/openstack_skyline-console/src/pages/auth/containers/Login/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/auth/containers/Login/index.less", "downloaded_repos/openstack_skyline-console/src/pages/auth/routes/index.js", "downloaded_repos/openstack_skyline-console/src/pages/base/App.jsx", "downloaded_repos/openstack_skyline-console/src/pages/base/containers/404/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/base/containers/AdminOverview/components/ComputeService.jsx", "downloaded_repos/openstack_skyline-console/src/pages/base/containers/AdminOverview/components/NetworkService.jsx", "downloaded_repos/openstack_skyline-console/src/pages/base/containers/AdminOverview/components/PlatformInfo.jsx", "downloaded_repos/openstack_skyline-console/src/pages/base/containers/AdminOverview/components/ResourceOverview.jsx", "downloaded_repos/openstack_skyline-console/src/pages/base/containers/AdminOverview/components/VirtualResource.jsx", "downloaded_repos/openstack_skyline-console/src/pages/base/containers/AdminOverview/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/base/containers/AdminOverview/style.less", "downloaded_repos/openstack_skyline-console/src/pages/base/containers/Overview/components/ProjectInfo.jsx", "downloaded_repos/openstack_skyline-console/src/pages/base/containers/Overview/components/QuotaOverview.jsx", "downloaded_repos/openstack_skyline-console/src/pages/base/containers/Overview/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/base/containers/Overview/style.less", "downloaded_repos/openstack_skyline-console/src/pages/base/routes/index.js", "downloaded_repos/openstack_skyline-console/src/pages/basic/App.jsx", "downloaded_repos/openstack_skyline-console/src/pages/basic/routes/index.js", "downloaded_repos/openstack_skyline-console/src/pages/compute/App.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/BareMetalNode/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/BareMetalNode/Detail/Port/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/BareMetalNode/Detail/Port/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/BareMetalNode/Detail/Port/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/BareMetalNode/Detail/Port/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/BareMetalNode/Detail/Port/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/BareMetalNode/Detail/PortGroup/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/BareMetalNode/Detail/PortGroup/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/BareMetalNode/Detail/PortGroup/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/BareMetalNode/Detail/PortGroup/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/BareMetalNode/Detail/PortGroup/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/BareMetalNode/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/BareMetalNode/actions/ClearMaintenance.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/BareMetalNode/actions/Create/DriveInfo.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/BareMetalNode/actions/Create/DriveInterface.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/BareMetalNode/actions/Create/NodeInfo.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/BareMetalNode/actions/Create/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/BareMetalNode/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/BareMetalNode/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/BareMetalNode/actions/Inspect.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/BareMetalNode/actions/ManageState.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/BareMetalNode/actions/PowerOff.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/BareMetalNode/actions/PowerOn.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/BareMetalNode/actions/SetBootDevice.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/BareMetalNode/actions/SetMaintenance.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/BareMetalNode/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/BareMetalNode/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Flavor/Arm/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Flavor/Arm/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Flavor/Arm/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Flavor/BareMetal/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Flavor/BareMetal/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Flavor/BareMetal/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Flavor/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Flavor/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Flavor/Heterogeneous/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Flavor/Heterogeneous/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Flavor/Heterogeneous/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Flavor/Other/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Flavor/Other/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Flavor/X86/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Flavor/X86/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Flavor/X86/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Flavor/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Flavor/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Flavor/actions/ManageAccess.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Flavor/actions/ManageMetadata.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Flavor/actions/StepCreate/AccessTypeSetting.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Flavor/actions/StepCreate/ParamSetting.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Flavor/actions/StepCreate/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Flavor/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Flavor/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/HostAggregate/Aggregate/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/HostAggregate/Aggregate/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/HostAggregate/Aggregate/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/HostAggregate/Aggregate/actions/ManageHost.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/HostAggregate/Aggregate/actions/ManageMetadata.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/HostAggregate/Aggregate/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/HostAggregate/Aggregate/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/HostAggregate/AvailabilityZone/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/HostAggregate/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Hypervisors/ComputeHost/actions/Disable.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Hypervisors/ComputeHost/actions/Enable.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Hypervisors/ComputeHost/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Hypervisors/ComputeHost/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Hypervisors/Hypervisor/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Hypervisors/Hypervisor/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Hypervisors/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Image/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Image/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Image/Image.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Image/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Image/actions/CreateInstance.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Image/actions/CreateIronic.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Image/actions/CreateVolume.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Image/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Image/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Image/actions/ManageAccess.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Image/actions/ManageMetadata.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Image/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Image/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/Detail/ActionLog/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/Detail/BaseDetail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/Detail/BaseDetail/index.less", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/Detail/Log/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/Detail/SecurityGroup/action/Detach.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/Detail/SecurityGroup/action/ManageSecurityGroup.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/Detail/SecurityGroup/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/Detail/SecurityGroup/index.less", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/AssociateFip.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/AttachInterface.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/AttachVolume.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/ChangePassword.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/ConfirmResize.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/Console.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/CreateImage.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/CreateIronic/BaseStep/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/CreateIronic/ConfirmStep/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/CreateIronic/NetworkStep/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/CreateIronic/SystemStep/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/CreateIronic/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/CreateIronic/index.less", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/CreateSnapshot.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/DeleteIronic.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/DetachInterface.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/DetachVolume.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/DisassociateFip.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/LiveMigrate.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/Lock.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/ManageSecurityGroup.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/Migrate.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/ModifyTags.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/Pause.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/Reboot.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/Rebuild.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/RebuildSelect.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/Rescue.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/Resize.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/Resume.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/RevertResize.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/Shelve.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/SoftDelete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/SoftReboot.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/Start.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/StepCreate/BaseStep/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/StepCreate/ConfirmStep/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/StepCreate/NetworkStep/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/StepCreate/SystemStep/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/StepCreate/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/StepCreate/index.less", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/Stop.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/Suspend.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/Unlock.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/Unpause.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/Unshelve.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/actions/index.less", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/components/FlavorSelectTable.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/components/index.less", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Instance/index.less", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/InstanceSnapshot/actions/CreateInstance.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/InstanceSnapshot/actions/CreateVolume.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/InstanceSnapshot/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/InstanceSnapshot/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/InstanceSnapshot/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/InstanceSnapshot/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Keypair/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Keypair/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Keypair/Detail/index.less", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Keypair/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Keypair/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Keypair/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/Keypair/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/ServerGroup/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/ServerGroup/Detail/index.less", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/ServerGroup/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/ServerGroup/actions/CreateInstance.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/ServerGroup/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/ServerGroup/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/containers/ServerGroup/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/compute/routes/index.js", "downloaded_repos/openstack_skyline-console/src/pages/configuration/App.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/Metadata/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/Metadata/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/Metadata/Detail/styles.less", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/Metadata/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/Metadata/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/Metadata/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/Metadata/actions/Manage.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/Metadata/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/Metadata/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/Setting/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/Setting/actions/Reset.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/Setting/actions/View.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/Setting/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/Setting/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/SystemInfo/Catalog.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/SystemInfo/CinderService/actions/Disable.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/SystemInfo/CinderService/actions/Enable.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/SystemInfo/CinderService/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/SystemInfo/CinderService/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/SystemInfo/ComputeService/actions/Disable.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/SystemInfo/ComputeService/actions/Enable.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/SystemInfo/ComputeService/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/SystemInfo/ComputeService/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/SystemInfo/HeatService.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/SystemInfo/NeutronAgent/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/SystemInfo/NeutronAgent/Detail/Network/actions/Add.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/SystemInfo/NeutronAgent/Detail/Network/actions/Remove.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/SystemInfo/NeutronAgent/Detail/Network/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/SystemInfo/NeutronAgent/Detail/Network/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/SystemInfo/NeutronAgent/Detail/Router/actions/Add.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/SystemInfo/NeutronAgent/Detail/Router/actions/Remove.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/SystemInfo/NeutronAgent/Detail/Router/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/SystemInfo/NeutronAgent/Detail/Router/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/SystemInfo/NeutronAgent/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/SystemInfo/NeutronAgent/actions/Disable.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/SystemInfo/NeutronAgent/actions/Enable.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/SystemInfo/NeutronAgent/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/SystemInfo/NeutronAgent/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/containers/SystemInfo/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/configuration/routes/index.js", "downloaded_repos/openstack_skyline-console/src/pages/container-infra/App.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-infra/containers/ClusterTemplates/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-infra/containers/ClusterTemplates/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-infra/containers/ClusterTemplates/actions/CreateCluster.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-infra/containers/ClusterTemplates/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-infra/containers/ClusterTemplates/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-infra/containers/ClusterTemplates/actions/StepCreate/StepInfo/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-infra/containers/ClusterTemplates/actions/StepCreate/StepLabel/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-infra/containers/ClusterTemplates/actions/StepCreate/StepNetwork/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-infra/containers/ClusterTemplates/actions/StepCreate/StepNodeSpec/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-infra/containers/ClusterTemplates/actions/StepCreate/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-infra/containers/ClusterTemplates/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-infra/containers/ClusterTemplates/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-infra/containers/Clusters/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-infra/containers/Clusters/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-infra/containers/Clusters/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-infra/containers/Clusters/actions/Resize.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-infra/containers/Clusters/actions/StepCreate/StepInfo/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-infra/containers/Clusters/actions/StepCreate/StepLabel/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-infra/containers/Clusters/actions/StepCreate/StepManagement/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-infra/containers/Clusters/actions/StepCreate/StepNetworks/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-infra/containers/Clusters/actions/StepCreate/StepNodeSpec/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-infra/containers/Clusters/actions/StepCreate/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-infra/containers/Clusters/actions/Upgrade.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-infra/containers/Clusters/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-infra/containers/Clusters/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-infra/routes/index.js", "downloaded_repos/openstack_skyline-console/src/pages/container-service/App.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Capsules/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Capsules/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Capsules/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Capsules/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Capsules/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Capsules/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Containers/Detail/ActionLogs.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Containers/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Containers/Detail/Console.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Containers/Detail/Logs.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Containers/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Containers/actions/AttachNetwork.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Containers/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Containers/actions/DetachNetwork.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Containers/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Containers/actions/ExecuteCommand.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Containers/actions/ForceDelete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Containers/actions/Kill.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Containers/actions/Pause.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Containers/actions/Reboot.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Containers/actions/Rebuild.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Containers/actions/Start.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Containers/actions/StepCreate/StepInfo/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Containers/actions/StepCreate/StepNetworks/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Containers/actions/StepCreate/StepOthers/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Containers/actions/StepCreate/StepSpec/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Containers/actions/StepCreate/StepVolumes/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Containers/actions/StepCreate/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Containers/actions/Stop.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Containers/actions/Unpause.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Containers/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Containers/components/ExposedPorts.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Containers/components/ZunVolume.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Containers/components/index.less", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Containers/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Hosts/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Hosts/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Hosts/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/containers/Services/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/container-service/routes/index.js", "downloaded_repos/openstack_skyline-console/src/pages/database/App.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Backups/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Backups/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Backups/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Backups/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Backups/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Backups/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Configurations/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Configurations/Detail/Instances.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Configurations/Detail/Values.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Configurations/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Configurations/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Configurations/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Configurations/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Configurations/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Instances/Detail/Backups.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Instances/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Instances/Detail/DatabaseAction.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Instances/Detail/DatabaseCreate.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Instances/Detail/DatabaseDelete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Instances/Detail/Databases.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Instances/Detail/Defaults.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Instances/Detail/Logs.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Instances/Detail/UserAction.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Instances/Detail/UserCreate.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Instances/Detail/UserDatabase.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Instances/Detail/UserDelete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Instances/Detail/Users.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Instances/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Instances/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Instances/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Instances/actions/Reboot.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Instances/actions/ResizeVolume.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Instances/actions/Restart.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Instances/actions/StepCreate/StepAdvanced/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Instances/actions/StepCreate/StepDetails/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Instances/actions/StepCreate/StepInitializeDatabases/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Instances/actions/StepCreate/StepNetworking/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Instances/actions/StepCreate/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Instances/actions/Stop.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Instances/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/containers/Instances/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/database/routes/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/ha/App.jsx", "downloaded_repos/openstack_skyline-console/src/pages/ha/containers/Hosts/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/ha/containers/Hosts/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/ha/containers/Hosts/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/ha/containers/Hosts/actions/Update.jsx", "downloaded_repos/openstack_skyline-console/src/pages/ha/containers/Hosts/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/ha/containers/Hosts/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/ha/containers/Notifications/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/ha/containers/Notifications/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/ha/containers/Notifications/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/ha/containers/Segments/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/ha/containers/Segments/Detail/HostDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/ha/containers/Segments/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/ha/containers/Segments/actions/AddHost.jsx", "downloaded_repos/openstack_skyline-console/src/pages/ha/containers/Segments/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/ha/containers/Segments/actions/StepCreate/StepHost.jsx", "downloaded_repos/openstack_skyline-console/src/pages/ha/containers/Segments/actions/StepCreate/StepSegment.jsx", "downloaded_repos/openstack_skyline-console/src/pages/ha/containers/Segments/actions/StepCreate/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/ha/containers/Segments/actions/Update.jsx", "downloaded_repos/openstack_skyline-console/src/pages/ha/containers/Segments/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/ha/containers/Segments/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/ha/routes/index.js", "downloaded_repos/openstack_skyline-console/src/pages/heat/App.jsx", "downloaded_repos/openstack_skyline-console/src/pages/heat/containers/Stack/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/heat/containers/Stack/Detail/Event.jsx", "downloaded_repos/openstack_skyline-console/src/pages/heat/containers/Stack/Detail/Resource.jsx", "downloaded_repos/openstack_skyline-console/src/pages/heat/containers/Stack/Detail/Template.jsx", "downloaded_repos/openstack_skyline-console/src/pages/heat/containers/Stack/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/heat/containers/Stack/actions/Abandon.jsx", "downloaded_repos/openstack_skyline-console/src/pages/heat/containers/Stack/actions/Create/Parameter.jsx", "downloaded_repos/openstack_skyline-console/src/pages/heat/containers/Stack/actions/Create/Template.jsx", "downloaded_repos/openstack_skyline-console/src/pages/heat/containers/Stack/actions/Create/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/heat/containers/Stack/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/heat/containers/Stack/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/heat/containers/Stack/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/heat/containers/Stack/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/heat/routes/index.js", "downloaded_repos/openstack_skyline-console/src/pages/identity/App.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Domain/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Domain/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Domain/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Domain/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Domain/actions/Enable.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Domain/actions/Forbidden.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Domain/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Domain/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Domain/index.less", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Project/Detail/Quota.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Project/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Project/Detail/index.less", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Project/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Project/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Project/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Project/actions/Enable.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Project/actions/Forbidden.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Project/actions/ManageQuota.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Project/actions/ManageUser.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Project/actions/ManageUserGroup.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Project/actions/ModifyTags.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Project/actions/RemoveDefaultProject.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Project/actions/SetDefaultProject.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Project/actions/Start.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Project/actions/Stop.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Project/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Project/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Project/index.less", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Role/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Role/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Role/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Role/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Role/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Role/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/Role/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/User/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/User/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/User/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/User/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/User/actions/Enable.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/User/actions/Forbidden.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/User/actions/Password.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/User/actions/SetDefaultProject.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/User/actions/SystemRole.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/User/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/User/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/UserGroup/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/UserGroup/Detail/index.less", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/UserGroup/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/UserGroup/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/UserGroup/actions/DomainPermission.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/UserGroup/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/UserGroup/actions/ManageUser.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/UserGroup/actions/SystemPermission.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/UserGroup/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/containers/UserGroup/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/identity/routes/index.js", "downloaded_repos/openstack_skyline-console/src/pages/management/App.jsx", "downloaded_repos/openstack_skyline-console/src/pages/management/containers/RecycleBin/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/management/containers/RecycleBin/actions/Recover.jsx", "downloaded_repos/openstack_skyline-console/src/pages/management/containers/RecycleBin/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/management/containers/RecycleBin/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/management/routes/index.js", "downloaded_repos/openstack_skyline-console/src/pages/monitor/App.jsx", "downloaded_repos/openstack_skyline-console/src/pages/monitor/containers/OpenstackService/Services.jsx", "downloaded_repos/openstack_skyline-console/src/pages/monitor/containers/OpenstackService/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/monitor/containers/OpenstackService/index.less", "downloaded_repos/openstack_skyline-console/src/pages/monitor/containers/OtherService/components/Memcache/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/monitor/containers/OtherService/components/Mysql/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/monitor/containers/OtherService/components/RabbitMQ/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/monitor/containers/OtherService/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/monitor/containers/OtherService/index.less", "downloaded_repos/openstack_skyline-console/src/pages/monitor/containers/Overview/components/AlertInfo/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/monitor/containers/Overview/components/Tops/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/monitor/containers/Overview/config.jsx", "downloaded_repos/openstack_skyline-console/src/pages/monitor/containers/Overview/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/monitor/containers/Overview/index.less", "downloaded_repos/openstack_skyline-console/src/pages/monitor/containers/PhysicalNode/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/monitor/containers/StorageCluster/RenderTabs.jsx", "downloaded_repos/openstack_skyline-console/src/pages/monitor/containers/StorageCluster/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/monitor/containers/StorageCluster/index.less", "downloaded_repos/openstack_skyline-console/src/pages/monitor/routes/index.js", "downloaded_repos/openstack_skyline-console/src/pages/network/App.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Certificate/Certificate.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Certificate/Detail/Container/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Certificate/Detail/Container/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Certificate/Detail/Secret/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Certificate/Detail/Secret/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Certificate/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Certificate/actions/DeleteContainer.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Certificate/actions/DeleteSecret.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Certificate/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Certificate/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/DNS/Reverse/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/DNS/Reverse/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/DNS/Reverse/actions/Set.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/DNS/Reverse/actions/Unset.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/DNS/Reverse/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/DNS/Reverse/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/DNS/Zones/Detail/BaseDetail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/DNS/Zones/Detail/RecordSets/Detail/BaseDetail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/DNS/Zones/Detail/RecordSets/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/DNS/Zones/Detail/RecordSets/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/DNS/Zones/Detail/RecordSets/actions/Update.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/DNS/Zones/Detail/RecordSets/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/DNS/Zones/Detail/RecordSets/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/DNS/Zones/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/DNS/Zones/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/DNS/Zones/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/DNS/Zones/actions/Records/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/DNS/Zones/actions/Update.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/DNS/Zones/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/DNS/Zones/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Firewall/Firewall/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Firewall/Firewall/Detail/Port.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Firewall/Firewall/Detail/PortDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Firewall/Firewall/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Firewall/Firewall/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Firewall/Firewall/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Firewall/Firewall/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Firewall/Firewall/actions/ManagePort.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Firewall/Firewall/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Firewall/Firewall/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Firewall/Policy/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Firewall/Policy/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Firewall/Policy/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Firewall/Policy/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Firewall/Policy/actions/InsertRule.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Firewall/Policy/actions/RemoveRule.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Firewall/Policy/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Firewall/Policy/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Firewall/Rule/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Firewall/Rule/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Firewall/Rule/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Firewall/Rule/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Firewall/Rule/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Firewall/Rule/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Firewall/Rule/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Firewall/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/FloatingIp/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/FloatingIp/Detail/PortForwarding/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/FloatingIp/Detail/PortForwarding/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/FloatingIp/Detail/PortForwarding/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/FloatingIp/Detail/PortForwarding/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/FloatingIp/Detail/PortForwarding/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/FloatingIp/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/FloatingIp/actions/Allocate.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/FloatingIp/actions/Associate.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/FloatingIp/actions/Disassociate.js", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/FloatingIp/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/FloatingIp/actions/Release.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/FloatingIp/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/FloatingIp/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/LoadBalancers/Listener/Actions/CreateListener.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/LoadBalancers/Listener/Actions/CreatePool.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/LoadBalancers/Listener/Actions/DeleteListener.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/LoadBalancers/Listener/Actions/DeletePool.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/LoadBalancers/Listener/Actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/LoadBalancers/Listener/Actions/EditHealthMonitor.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/LoadBalancers/Listener/Actions/EditPoolInfo.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/LoadBalancers/Listener/Actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/LoadBalancers/Listener/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/LoadBalancers/Listener/Detail/Member/Actions/CreateMember.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/LoadBalancers/Listener/Detail/Member/Actions/DeleteMember.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/LoadBalancers/Listener/Detail/Member/Actions/EditMember.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/LoadBalancers/Listener/Detail/Member/Actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/LoadBalancers/Listener/Detail/Member/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/LoadBalancers/Listener/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/LoadBalancers/Listener/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/LoadBalancers/LoadBalancerInstance/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/LoadBalancers/LoadBalancerInstance/actions/AssociateFIP.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/LoadBalancers/LoadBalancerInstance/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/LoadBalancers/LoadBalancerInstance/actions/DisassociateFip.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/LoadBalancers/LoadBalancerInstance/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/LoadBalancers/LoadBalancerInstance/actions/StepCreate/BaseStep/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/LoadBalancers/LoadBalancerInstance/actions/StepCreate/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/LoadBalancers/LoadBalancerInstance/actions/index.js", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/LoadBalancers/LoadBalancerInstance/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/LoadBalancers/StepCreateComponents/HealthMonitorStep/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/LoadBalancers/StepCreateComponents/ListenerStep/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/LoadBalancers/StepCreateComponents/MemberStep/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/LoadBalancers/StepCreateComponents/PoolStep/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/LoadBalancers/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Network/Detail/Detail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Network/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Network/Network.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Network/actions/CreateNetwork.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Network/actions/CreateSubnet.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Network/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Network/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Network/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Network/actions/networkUtil.js", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Network/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Port/Detail/AllowedAddressPair/actions/CreateAllowedAddressPair.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Port/Detail/AllowedAddressPair/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Port/Detail/AllowedAddressPair/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Port/Detail/AllowedAddressPair/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Port/Detail/BaseDetail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Port/Detail/FixedIPs/actions/ReleaseIP.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Port/Detail/FixedIPs/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Port/Detail/FixedIPs/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Port/Detail/SecurityGroups/actions/Detach.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Port/Detail/SecurityGroups/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Port/Detail/SecurityGroups/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Port/Detail/SecurityGroups/index.less", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Port/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Port/Detail/index.less", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Port/actions/AllocateIP.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Port/actions/AssociateFIP.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Port/actions/Attach.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Port/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Port/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Port/actions/Detach.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Port/actions/DisAssociateFIP.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Port/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Port/actions/ManageSecurityGroup.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Port/actions/ModifyQoS.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Port/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Port/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/QoSPolicy/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/QoSPolicy/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/QoSPolicy/QoSPolicy.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/QoSPolicy/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/QoSPolicy/actions/CreateBandwidthLimitRule.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/QoSPolicy/actions/CreateDSCPMarkingRules.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/QoSPolicy/actions/DSCPMarkingItems.js", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/QoSPolicy/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/QoSPolicy/actions/DeleteBandwidthEgressRules.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/QoSPolicy/actions/DeleteBandwidthIngressRules.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/QoSPolicy/actions/DeleteDSCPMarkingRules.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/QoSPolicy/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/QoSPolicy/actions/EditBandwidthEgressRule.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/QoSPolicy/actions/EditBandwidthIngressRule.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/QoSPolicy/actions/EditDSCPMarkingRule.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/QoSPolicy/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/QoSPolicy/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/RbacPolicies/Detail/Detail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/RbacPolicies/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/RbacPolicies/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/RbacPolicies/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/RbacPolicies/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/RbacPolicies/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/RbacPolicies/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Router/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Router/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Router/Port/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Router/Port/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Router/Port/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Router/StaticRouter/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Router/StaticRouter/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Router/StaticRouter/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Router/StaticRouter/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Router/actions/AssociateFip.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Router/actions/CloseGateway.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Router/actions/ConnectSubnet.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Router/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Router/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Router/actions/DisableSnat.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Router/actions/DisassociateFip.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Router/actions/DisconnectSubnet.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Router/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Router/actions/EnableSnat.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Router/actions/SetGateway.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Router/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Router/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/SecurityGroup/Detail/Rule/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/SecurityGroup/Detail/Rule/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/SecurityGroup/Detail/Rule/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/SecurityGroup/Detail/Rule/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/SecurityGroup/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/SecurityGroup/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/SecurityGroup/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/SecurityGroup/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/SecurityGroup/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/SecurityGroup/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Subnet/Detail/Detail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Subnet/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Subnet/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Subnet/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Subnet/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Subnet/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Topology/InstanceCard.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Topology/NodeCard.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Topology/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/Topology/index.less", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/VPN/EndpointGroup/actions/Create.js", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/VPN/EndpointGroup/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/VPN/EndpointGroup/actions/Edit.js", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/VPN/EndpointGroup/actions/index.js", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/VPN/EndpointGroup/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/VPN/IKEPolicy/actions/Create.js", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/VPN/IKEPolicy/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/VPN/IKEPolicy/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/VPN/IKEPolicy/actions/index.js", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/VPN/IKEPolicy/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/VPN/IPsecPolicy/actions/Create.js", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/VPN/IPsecPolicy/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/VPN/IPsecPolicy/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/VPN/IPsecPolicy/actions/index.js", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/VPN/IPsecPolicy/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/VPN/IPsecSiteConnection/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/VPN/IPsecSiteConnection/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/VPN/IPsecSiteConnection/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/VPN/IPsecSiteConnection/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/VPN/IPsecSiteConnection/actions/Edit.js", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/VPN/IPsecSiteConnection/actions/components/LocalSubnet.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/VPN/IPsecSiteConnection/actions/index.js", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/VPN/IPsecSiteConnection/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/VPN/VPNGateway/actions/Create.js", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/VPN/VPNGateway/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/VPN/VPNGateway/actions/Edit.js", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/VPN/VPNGateway/actions/index.js", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/VPN/VPNGateway/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/containers/VPN/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/network/routes/index.js", "downloaded_repos/openstack_skyline-console/src/pages/share/App.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/Share/Detail/AccessRule/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/Share/Detail/AccessRule/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/Share/Detail/AccessRule/actions/ManageMetadata.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/Share/Detail/AccessRule/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/Share/Detail/AccessRule/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/Share/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/Share/Detail/Metadata/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/Share/Detail/Metadata/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/Share/Detail/Metadata/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/Share/Detail/Metadata/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/Share/Detail/Metadata/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/Share/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/Share/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/Share/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/Share/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/Share/actions/Extend.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/Share/actions/ManageAccessRule.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/Share/actions/ManageMetadata.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/Share/actions/ResetStatus.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/Share/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/Share/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareGroup/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareGroup/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareGroup/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareGroup/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareGroup/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareGroup/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareGroup/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareGroupType/Detail/ExtraSpec/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareGroupType/Detail/ExtraSpec/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareGroupType/Detail/ExtraSpec/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareGroupType/Detail/ExtraSpec/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareGroupType/Detail/ExtraSpec/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareGroupType/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareGroupType/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareGroupType/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareGroupType/actions/ManageAccess.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareGroupType/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareGroupType/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareInstance/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareInstance/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareInstance/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareInstance/actions/ResetStatus.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareInstance/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareInstance/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareNetwork/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareNetwork/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareNetwork/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareNetwork/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareNetwork/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareNetwork/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareNetwork/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareServer/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareServer/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareServer/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareServer/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareServer/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareType/Detail/ExtraSpec/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareType/Detail/ExtraSpec/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareType/Detail/ExtraSpec/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareType/Detail/ExtraSpec/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareType/Detail/ExtraSpec/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareType/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareType/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareType/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareType/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareType/actions/ManageAccess.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareType/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/ShareType/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/containers/Storage/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/share/routes/index.js", "downloaded_repos/openstack_skyline-console/src/pages/storage/App.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Backup/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Backup/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Backup/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Backup/actions/CreateVolume.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Backup/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Backup/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Backup/actions/Restore.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Backup/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Backup/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Container/Detail/actions/CopyFile.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Container/Detail/actions/CreateFolder.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Container/Detail/actions/CutFile.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Container/Detail/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Container/Detail/actions/Download.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Container/Detail/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Container/Detail/actions/PasteFile.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Container/Detail/actions/Rename.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Container/Detail/actions/UploadFile.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Container/Detail/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Container/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Container/Detail/index.less", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Container/actions/Access.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Container/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Container/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Container/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Container/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Snapshot/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Snapshot/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Snapshot/actions/CreateVolume.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Snapshot/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Snapshot/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Snapshot/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Snapshot/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Storage/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Volume/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Volume/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Volume/actions/AcceptVolumeTransfer.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Volume/actions/Attach.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Volume/actions/Bootable.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Volume/actions/CancelTransfer.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Volume/actions/ChangeType.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Volume/actions/CloneVolume.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Volume/actions/Create/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Volume/actions/Create/index.less", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Volume/actions/CreateBackup.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Volume/actions/CreateImage.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Volume/actions/CreateInstance.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Volume/actions/CreateSnapshot.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Volume/actions/CreateTransfer.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Volume/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Volume/actions/Detach.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Volume/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Volume/actions/ExtendVolume.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Volume/actions/Migrate.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Volume/actions/Restore.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Volume/actions/UpdateStatus.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Volume/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/Volume/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/VolumeType/QosSpec/Detail/ExtraSpec/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/VolumeType/QosSpec/Detail/ExtraSpec/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/VolumeType/QosSpec/Detail/ExtraSpec/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/VolumeType/QosSpec/Detail/ExtraSpec/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/VolumeType/QosSpec/Detail/ExtraSpec/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/VolumeType/QosSpec/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/VolumeType/QosSpec/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/VolumeType/QosSpec/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/VolumeType/QosSpec/actions/EditConsumer.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/VolumeType/QosSpec/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/VolumeType/QosSpec/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/VolumeType/VolumeType/Detail/BaseDetail.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/VolumeType/VolumeType/Detail/ExtraSpec/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/VolumeType/VolumeType/Detail/ExtraSpec/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/VolumeType/VolumeType/Detail/ExtraSpec/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/VolumeType/VolumeType/Detail/ExtraSpec/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/VolumeType/VolumeType/Detail/ExtraSpec/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/VolumeType/VolumeType/Detail/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/VolumeType/VolumeType/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/VolumeType/VolumeType/actions/CreateEncryption.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/VolumeType/VolumeType/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/VolumeType/VolumeType/actions/DeleteEncryption.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/VolumeType/VolumeType/actions/Edit.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/VolumeType/VolumeType/actions/ManageAccess.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/VolumeType/VolumeType/actions/ManageQos.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/VolumeType/VolumeType/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/VolumeType/VolumeType/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/containers/VolumeType/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/storage/routes/index.js", "downloaded_repos/openstack_skyline-console/src/pages/user-center/App.jsx", "downloaded_repos/openstack_skyline-console/src/pages/user-center/containers/Credentials/actions/Create.jsx", "downloaded_repos/openstack_skyline-console/src/pages/user-center/containers/Credentials/actions/Delete.jsx", "downloaded_repos/openstack_skyline-console/src/pages/user-center/containers/Credentials/actions/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/user-center/containers/Credentials/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/user-center/containers/UserCenter/index.jsx", "downloaded_repos/openstack_skyline-console/src/pages/user-center/containers/UserCenter/styles.less", "downloaded_repos/openstack_skyline-console/src/pages/user-center/routes/index.js", "downloaded_repos/openstack_skyline-console/src/resources/cinder/backup.jsx", "downloaded_repos/openstack_skyline-console/src/resources/cinder/cinder-pool.jsx", "downloaded_repos/openstack_skyline-console/src/resources/cinder/snapshot.jsx", "downloaded_repos/openstack_skyline-console/src/resources/cinder/volume-type.js", "downloaded_repos/openstack_skyline-console/src/resources/cinder/volume.jsx", "downloaded_repos/openstack_skyline-console/src/resources/dns/record.jsx", "downloaded_repos/openstack_skyline-console/src/resources/dns/zone.js", "downloaded_repos/openstack_skyline-console/src/resources/glance/image.jsx", "downloaded_repos/openstack_skyline-console/src/resources/glance/instance-snapshot.js", "downloaded_repos/openstack_skyline-console/src/resources/heat/stack.js", "downloaded_repos/openstack_skyline-console/src/resources/ironic/ironic.js", "downloaded_repos/openstack_skyline-console/src/resources/keystone/domain.jsx", "downloaded_repos/openstack_skyline-console/src/resources/keystone/openstack-rc.js", "downloaded_repos/openstack_skyline-console/src/resources/keystone/project.jsx", "downloaded_repos/openstack_skyline-console/src/resources/keystone/role.js", "downloaded_repos/openstack_skyline-console/src/resources/magnum/cluster.js", "downloaded_repos/openstack_skyline-console/src/resources/magnum/template.js", "downloaded_repos/openstack_skyline-console/src/resources/manila/share-group-type.jsx", "downloaded_repos/openstack_skyline-console/src/resources/manila/share-group.js", "downloaded_repos/openstack_skyline-console/src/resources/manila/share-network.jsx", "downloaded_repos/openstack_skyline-console/src/resources/manila/share-server.js", "downloaded_repos/openstack_skyline-console/src/resources/manila/share-type.js", "downloaded_repos/openstack_skyline-console/src/resources/manila/share.js", "downloaded_repos/openstack_skyline-console/src/resources/neutron/firewall-policy.jsx", "downloaded_repos/openstack_skyline-console/src/resources/neutron/firewall-port.js", "downloaded_repos/openstack_skyline-console/src/resources/neutron/firewall-rule.js", "downloaded_repos/openstack_skyline-console/src/resources/neutron/firewall.js", "downloaded_repos/openstack_skyline-console/src/resources/neutron/floatingip.js", "downloaded_repos/openstack_skyline-console/src/resources/neutron/network.jsx", "downloaded_repos/openstack_skyline-console/src/resources/neutron/neutron.js", "downloaded_repos/openstack_skyline-console/src/resources/neutron/port.jsx", "downloaded_repos/openstack_skyline-console/src/resources/neutron/qos-policy.jsx", "downloaded_repos/openstack_skyline-console/src/resources/neutron/rbac-policy.js", "downloaded_repos/openstack_skyline-console/src/resources/neutron/router.jsx", "downloaded_repos/openstack_skyline-console/src/resources/neutron/security-group-rule.jsx", "downloaded_repos/openstack_skyline-console/src/resources/neutron/security-group.jsx", "downloaded_repos/openstack_skyline-console/src/resources/neutron/topology-color.js", "downloaded_repos/openstack_skyline-console/src/resources/neutron/vpn.js", "downloaded_repos/openstack_skyline-console/src/resources/nova/flavor.js", "downloaded_repos/openstack_skyline-console/src/resources/nova/hypervisor.jsx", "downloaded_repos/openstack_skyline-console/src/resources/nova/instance.jsx", "downloaded_repos/openstack_skyline-console/src/resources/nova/keypair.jsx", "downloaded_repos/openstack_skyline-console/src/resources/nova/keypair.less", "downloaded_repos/openstack_skyline-console/src/resources/nova/server-group.js", "downloaded_repos/openstack_skyline-console/src/resources/nova/service.js", "downloaded_repos/openstack_skyline-console/src/resources/octavia/lb.jsx", "downloaded_repos/openstack_skyline-console/src/resources/octavia/pool.js", "downloaded_repos/openstack_skyline-console/src/resources/octavia/secrets.jsx", "downloaded_repos/openstack_skyline-console/src/resources/prometheus/metricDict.js", "downloaded_repos/openstack_skyline-console/src/resources/prometheus/monitoring.js", "downloaded_repos/openstack_skyline-console/src/resources/skyline/policy.js", "downloaded_repos/openstack_skyline-console/src/resources/skyline/setting.js", "downloaded_repos/openstack_skyline-console/src/resources/swift/container.js", "downloaded_repos/openstack_skyline-console/src/resources/trove/database.js", "downloaded_repos/openstack_skyline-console/src/resources/zun/actions.jsx", "downloaded_repos/openstack_skyline-console/src/resources/zun/capsule.js", "downloaded_repos/openstack_skyline-console/src/resources/zun/container.js", "downloaded_repos/openstack_skyline-console/src/stores/barbican/containers.js", "downloaded_repos/openstack_skyline-console/src/stores/barbican/secrets.js", "downloaded_repos/openstack_skyline-console/src/stores/base-list.js", "downloaded_repos/openstack_skyline-console/src/stores/base.js", "downloaded_repos/openstack_skyline-console/src/stores/cinder/backup.js", "downloaded_repos/openstack_skyline-console/src/stores/cinder/extra-spec.js", "downloaded_repos/openstack_skyline-console/src/stores/cinder/pool.js", "downloaded_repos/openstack_skyline-console/src/stores/cinder/qos-spec-key.js", "downloaded_repos/openstack_skyline-console/src/stores/cinder/qos-spec.js", "downloaded_repos/openstack_skyline-console/src/stores/cinder/service.js", "downloaded_repos/openstack_skyline-console/src/stores/cinder/snapshot-volume.js", "downloaded_repos/openstack_skyline-console/src/stores/cinder/snapshot.js", "downloaded_repos/openstack_skyline-console/src/stores/cinder/volume-type.js", "downloaded_repos/openstack_skyline-console/src/stores/cinder/volume.js", "downloaded_repos/openstack_skyline-console/src/stores/designate/base.js", "downloaded_repos/openstack_skyline-console/src/stores/designate/record-set.js", "downloaded_repos/openstack_skyline-console/src/stores/designate/reverse.js", "downloaded_repos/openstack_skyline-console/src/stores/designate/zones.js", "downloaded_repos/openstack_skyline-console/src/stores/glance/image.js", "downloaded_repos/openstack_skyline-console/src/stores/glance/instance-snapshot.js", "downloaded_repos/openstack_skyline-console/src/stores/glance/metadata.js", "downloaded_repos/openstack_skyline-console/src/stores/heat/event.js", "downloaded_repos/openstack_skyline-console/src/stores/heat/resource.js", "downloaded_repos/openstack_skyline-console/src/stores/heat/service.js", "downloaded_repos/openstack_skyline-console/src/stores/heat/stack.js", "downloaded_repos/openstack_skyline-console/src/stores/index.jsx", "downloaded_repos/openstack_skyline-console/src/stores/ironic/ironic.js", "downloaded_repos/openstack_skyline-console/src/stores/ironic/port-group.js", "downloaded_repos/openstack_skyline-console/src/stores/ironic/port.js", "downloaded_repos/openstack_skyline-console/src/stores/keystone/catalog.js", "downloaded_repos/openstack_skyline-console/src/stores/keystone/credential.js", "downloaded_repos/openstack_skyline-console/src/stores/keystone/domain.js", "downloaded_repos/openstack_skyline-console/src/stores/keystone/project.js", "downloaded_repos/openstack_skyline-console/src/stores/keystone/role.js", "downloaded_repos/openstack_skyline-console/src/stores/keystone/tag.js", "downloaded_repos/openstack_skyline-console/src/stores/keystone/user-group.js", "downloaded_repos/openstack_skyline-console/src/stores/keystone/user.js", "downloaded_repos/openstack_skyline-console/src/stores/magnum/clusterAdmin.js", "downloaded_repos/openstack_skyline-console/src/stores/magnum/clusterTemplates.js", "downloaded_repos/openstack_skyline-console/src/stores/magnum/clusterTemplatesAdmin.js", "downloaded_repos/openstack_skyline-console/src/stores/magnum/clusters.js", "downloaded_repos/openstack_skyline-console/src/stores/manila/extra-spec.js", "downloaded_repos/openstack_skyline-console/src/stores/manila/group-spec.js", "downloaded_repos/openstack_skyline-console/src/stores/manila/pool.js", "downloaded_repos/openstack_skyline-console/src/stores/manila/share-access-rule.js", "downloaded_repos/openstack_skyline-console/src/stores/manila/share-group-type.js", "downloaded_repos/openstack_skyline-console/src/stores/manila/share-group.js", "downloaded_repos/openstack_skyline-console/src/stores/manila/share-instance.js", "downloaded_repos/openstack_skyline-console/src/stores/manila/share-metadata.js", "downloaded_repos/openstack_skyline-console/src/stores/manila/share-network.js", "downloaded_repos/openstack_skyline-console/src/stores/manila/share-server.js", "downloaded_repos/openstack_skyline-console/src/stores/manila/share-type.js", "downloaded_repos/openstack_skyline-console/src/stores/manila/share.js", "downloaded_repos/openstack_skyline-console/src/stores/masakari/hosts.js", "downloaded_repos/openstack_skyline-console/src/stores/masakari/notifications.js", "downloaded_repos/openstack_skyline-console/src/stores/masakari/segments.js", "downloaded_repos/openstack_skyline-console/src/stores/neutron/agent-network.js", "downloaded_repos/openstack_skyline-console/src/stores/neutron/agent-router.js", "downloaded_repos/openstack_skyline-console/src/stores/neutron/agent.js", "downloaded_repos/openstack_skyline-console/src/stores/neutron/firewall-policy.js", "downloaded_repos/openstack_skyline-console/src/stores/neutron/firewall-rule.js", "downloaded_repos/openstack_skyline-console/src/stores/neutron/firewall.js", "downloaded_repos/openstack_skyline-console/src/stores/neutron/fixed-ip.js", "downloaded_repos/openstack_skyline-console/src/stores/neutron/floatingIp.js", "downloaded_repos/openstack_skyline-console/src/stores/neutron/network.js", "downloaded_repos/openstack_skyline-console/src/stores/neutron/neutron.js", "downloaded_repos/openstack_skyline-console/src/stores/neutron/port-extension.js", "downloaded_repos/openstack_skyline-console/src/stores/neutron/port-forwarding.js", "downloaded_repos/openstack_skyline-console/src/stores/neutron/port.js", "downloaded_repos/openstack_skyline-console/src/stores/neutron/qos-policy.js", "downloaded_repos/openstack_skyline-console/src/stores/neutron/rbac-policies.js", "downloaded_repos/openstack_skyline-console/src/stores/neutron/router.js", "downloaded_repos/openstack_skyline-console/src/stores/neutron/security-group.js", "downloaded_repos/openstack_skyline-console/src/stores/neutron/security-rule.js", "downloaded_repos/openstack_skyline-console/src/stores/neutron/static-route.js", "downloaded_repos/openstack_skyline-console/src/stores/neutron/subnet.js", "downloaded_repos/openstack_skyline-console/src/stores/neutron/vpn-endpoint-group.js", "downloaded_repos/openstack_skyline-console/src/stores/neutron/vpn-ike-policy.js", "downloaded_repos/openstack_skyline-console/src/stores/neutron/vpn-ipsec-connection.js", "downloaded_repos/openstack_skyline-console/src/stores/neutron/vpn-ipsec-policy.js", "downloaded_repos/openstack_skyline-console/src/stores/neutron/vpn-service.js", "downloaded_repos/openstack_skyline-console/src/stores/nova/action-log.js", "downloaded_repos/openstack_skyline-console/src/stores/nova/aggregate.js", "downloaded_repos/openstack_skyline-console/src/stores/nova/compute-host.js", "downloaded_repos/openstack_skyline-console/src/stores/nova/flavor.js", "downloaded_repos/openstack_skyline-console/src/stores/nova/hypervisor.js", "downloaded_repos/openstack_skyline-console/src/stores/nova/instance-volume.js", "downloaded_repos/openstack_skyline-console/src/stores/nova/instance.js", "downloaded_repos/openstack_skyline-console/src/stores/nova/keypair.js", "downloaded_repos/openstack_skyline-console/src/stores/nova/server-group.js", "downloaded_repos/openstack_skyline-console/src/stores/nova/server.js", "downloaded_repos/openstack_skyline-console/src/stores/nova/tag.js", "downloaded_repos/openstack_skyline-console/src/stores/nova/zone.js", "downloaded_repos/openstack_skyline-console/src/stores/octavia/health-monitor.js", "downloaded_repos/openstack_skyline-console/src/stores/octavia/listener.js", "downloaded_repos/openstack_skyline-console/src/stores/octavia/loadbalancer.js", "downloaded_repos/openstack_skyline-console/src/stores/octavia/pool-member.js", "downloaded_repos/openstack_skyline-console/src/stores/octavia/pool.js", "downloaded_repos/openstack_skyline-console/src/stores/overview-admin.js", "downloaded_repos/openstack_skyline-console/src/stores/project.js", "downloaded_repos/openstack_skyline-console/src/stores/prometheus/monitor-base.js", "downloaded_repos/openstack_skyline-console/src/stores/prometheus/openstack-service.js", "downloaded_repos/openstack_skyline-console/src/stores/prometheus/storage-cluster.js", "downloaded_repos/openstack_skyline-console/src/stores/root.js", "downloaded_repos/openstack_skyline-console/src/stores/skyline/recycle-server.js", "downloaded_repos/openstack_skyline-console/src/stores/skyline/server-group-instance.js", "downloaded_repos/openstack_skyline-console/src/stores/skyline/setting.js", "downloaded_repos/openstack_skyline-console/src/stores/skyline/skyline.js", "downloaded_repos/openstack_skyline-console/src/stores/swift/container.js", "downloaded_repos/openstack_skyline-console/src/stores/swift/object.js", "downloaded_repos/openstack_skyline-console/src/stores/trove/backups.js", "downloaded_repos/openstack_skyline-console/src/stores/trove/configurations.js", "downloaded_repos/openstack_skyline-console/src/stores/trove/instanceBackups.js", "downloaded_repos/openstack_skyline-console/src/stores/trove/instances-database.js", "downloaded_repos/openstack_skyline-console/src/stores/trove/instances-user.js", "downloaded_repos/openstack_skyline-console/src/stores/trove/instances.js", "downloaded_repos/openstack_skyline-console/src/stores/trove/instancesLogs.js", "downloaded_repos/openstack_skyline-console/src/stores/zun/action-log.js", "downloaded_repos/openstack_skyline-console/src/stores/zun/capsules.js", "downloaded_repos/openstack_skyline-console/src/stores/zun/containers.js", "downloaded_repos/openstack_skyline-console/src/stores/zun/hosts.js", "downloaded_repos/openstack_skyline-console/src/stores/zun/services.js", "downloaded_repos/openstack_skyline-console/src/styles/base.less", "downloaded_repos/openstack_skyline-console/src/styles/main.less", "downloaded_repos/openstack_skyline-console/src/styles/reset.less", "downloaded_repos/openstack_skyline-console/src/styles/variables-custom.less", "downloaded_repos/openstack_skyline-console/src/styles/variables.less", "downloaded_repos/openstack_skyline-console/src/utils/RouterConfig.jsx", "downloaded_repos/openstack_skyline-console/src/utils/code.js", "downloaded_repos/openstack_skyline-console/src/utils/constants.js", "downloaded_repos/openstack_skyline-console/src/utils/content.jsx", "downloaded_repos/openstack_skyline-console/src/utils/cookie.js", "downloaded_repos/openstack_skyline-console/src/utils/file.js", "downloaded_repos/openstack_skyline-console/src/utils/file.spec.js", "downloaded_repos/openstack_skyline-console/src/utils/index.js", "downloaded_repos/openstack_skyline-console/src/utils/index.test.js", "downloaded_repos/openstack_skyline-console/src/utils/local-storage.js", "downloaded_repos/openstack_skyline-console/src/utils/local-storage.spec.js", "downloaded_repos/openstack_skyline-console/src/utils/route-map.jsx", "downloaded_repos/openstack_skyline-console/src/utils/route-map.spec.js", "downloaded_repos/openstack_skyline-console/src/utils/table.jsx", "downloaded_repos/openstack_skyline-console/src/utils/table.test.js", "downloaded_repos/openstack_skyline-console/src/utils/time.js", "downloaded_repos/openstack_skyline-console/src/utils/time.spec.js", "downloaded_repos/openstack_skyline-console/src/utils/translate.js", "downloaded_repos/openstack_skyline-console/src/utils/translate.spec.js", "downloaded_repos/openstack_skyline-console/src/utils/validate.js", "downloaded_repos/openstack_skyline-console/src/utils/yaml.js", "downloaded_repos/openstack_skyline-console/src/utils/yaml.spec.js", "downloaded_repos/openstack_skyline-console/test-requirements.txt", "downloaded_repos/openstack_skyline-console/tools/git_config/commit_message.txt", "downloaded_repos/openstack_skyline-console/tox.ini", "downloaded_repos/openstack_skyline-console/yarn.lock"], "skipped": [{"path": "downloaded_repos/openstack_skyline-console/doc/source/test/catalog-introduction.rst", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/doc/source/test/how-to-edit-e2e-case.rst", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/doc/source/test/index.rst", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/doc/source/test/ready-to-work.rst", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/1-ready-to-work.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/2-catalog-introduction.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/3-0-how-to-edit-e2e-case.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/3-1-E2E-form-operation.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/3-2-E2E-table-operation.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/3-3-E2E-detail-operation.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/3-4-E2E-resource-operation.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/console.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/detail/list.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/detail/name.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/detail/tab.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/form/add-select.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/form/attach-file-image.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/form/attach-file.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/form/checkbox.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/form/click-confirm-submit.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/form/click-form-submit.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/form/click-modal-submit.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/form/click-step-next.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/form/disable-action.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/form/form-name.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/form/input.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/form/key-value.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/form/more-open.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/form/more.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/form/notice.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/form/radio.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/form/select-all.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/form/select-table-option.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/form/select-table-search.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/form/select-table.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/form/select-value.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/form/select.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/form/switch.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/form/tab.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/form/textarea-json.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/form/transfer-left-click.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/form/transfer-left.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/form/transfer-right-check.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/form/transfer-right.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/form/unselect-all.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/form/wait-form-loading.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/gui-list.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/gui-work.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/result.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/table/action-by-title-2.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/table/action-by-title.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/table/action-in-more.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/table/action-in-sub.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/table/check-first-row.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/table/check-value.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/table/click-first.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/table/confirm-action.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/table/confirm-in-sub.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/table/confirm-more-1.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/table/confirm-more-2.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/table/detail-1.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/table/detail-2.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/table/disable-first.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/table/disable-more-action.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/table/first-confirm-2.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/table/first-confirm.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/table/header-btn-index.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/table/header-btn-title.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/table/header-confirm-title.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/table/search-select-1.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/table/search-select-2.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/table/search-select-3.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/table/search-text-1.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/table/search-text-2.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/table/search-text-3.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/table/search.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/table/select-first.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/table/simple-search.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/table/wait-1.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/table/wait-2.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/e2e/table/wait-table-loading.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/en/test/images/unit/result.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/1-ready-to-work.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/2-catalog-introduction.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/3-0-how-to-edit-e2e-case.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/3-1-E2E-form-operation.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/3-2-E2E-table-operation.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/3-3-E2E-detail-operation.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/3-4-E2E-resource-operation.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/console.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/detail/list.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/detail/name.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/detail/tab.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/form/add-select.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/form/attach-file-image.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/form/attach-file.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/form/checkbox.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/form/click-confirm-submit.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/form/click-form-submit.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/form/click-modal-submit.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/form/click-step-next.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/form/disable-action.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/form/form-name.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/form/input.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/form/key-value.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/form/more-open.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/form/more.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/form/notice.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/form/radio.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/form/select-all.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/form/select-table-option.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/form/select-table-search.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/form/select-table.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/form/select-value.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/form/select.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/form/switch.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/form/tab.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/form/textarea-json.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/form/transfer-left-click.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/form/transfer-left.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/form/transfer-right-check.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/form/transfer-right.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/form/unselect-all.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/form/wait-form-loading.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/gui-list.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/gui-work.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/result.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/table/action-by-title-2.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/table/action-by-title.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/table/action-in-more.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/table/action-in-sub.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/table/check-first-row.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/table/check-value.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/table/click-first.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/table/confirm-action.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/table/confirm-in-sub.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/table/confirm-more-1.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/table/confirm-more-2.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/table/detail-1.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/table/detail-2.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/table/disable-first.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/table/disable-more-action.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/table/first-confirm-2.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/table/first-confirm.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/table/header-btn-index.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/table/header-btn-title.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/table/header-confirm-title.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/table/search-select-1.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/table/search-select-2.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/table/search-select-3.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/table/search-text-1.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/table/search-text-2.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/table/search-text-3.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/table/search.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/table/select-first.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/table/simple-search.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/table/wait-1.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/table/wait-2.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/e2e/table/wait-table-loading.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/docs/zh/test/images/unit/result.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/skyline_console/static/basic.bundle.**********.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openstack_skyline-console/skyline_console/static/common.bundle.**********.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openstack_skyline-console/skyline_console/static/compute.bundle.**********.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openstack_skyline-console/skyline_console/static/identity.bundle.**********.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openstack_skyline-console/skyline_console/static/main.bundle.**********.js", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openstack_skyline-console/skyline_console/static/monitor-center.bundle.**********.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openstack_skyline-console/skyline_console/static/network.bundle.**********.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openstack_skyline-console/skyline_console/static/share.bundle.**********.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openstack_skyline-console/skyline_console/static/vendor.bundle.**********.js", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openstack_skyline-console/skyline_console/static/vendor.bundle.**********.js.gz", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/config/config-compute.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/config/config-network.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/config/config-other.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/config/config-storage.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/config/config.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/fixtures/container-file.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/fixtures/example.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/fixtures/flavor-family.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/fixtures/keypair", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/fixtures/metadata.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/fixtures/profile.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/fixtures/stack-content.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/fixtures/stack-params.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/fixtures/zunCapsuleTemplate.yaml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/compute/aggregate.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/compute/baremetal.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/compute/flavor.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/compute/hypervisor.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/compute/image.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/compute/instance.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/compute/ironic.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/compute/keypair.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/compute/server-group.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/configuration/metadata.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/configuration/setting.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/configuration/system.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/error.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/heat/stack.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/identity/domain.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/identity/project.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/identity/role.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/identity/user-group.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/identity/user.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/login.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/management/recycle-bin.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/manila/share-type.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/network/firewall-policy.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/network/firewall-rule.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/network/firewall.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/network/floatingip.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/network/lb.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/network/network.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/network/port.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/network/qos-policy.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/network/router.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/network/security-group.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/network/topology.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/network/vpn.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/storage/backup.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/storage/qos.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/storage/snapshot.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/storage/storage.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/storage/swift.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/storage/volume-type.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/storage/volume.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/zun/capsule.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/integration/pages/zun/container.spec.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/plugins/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/support/commands.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/support/common.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/support/constants.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/support/detail-commands.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/support/form-commands.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/support/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/support/resource-commands.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/support/table-commands.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/e2e/utils/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/unit/local-storage-mock.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/unit/locales/en-US.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/unit/locales/zh-hans.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/unit/setup-tests.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openstack_skyline-console/test/unit/svg-mock.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 4.1134889125823975, "profiling_times": {"config_time": 5.957250118255615, "core_time": 38.97319459915161, "ignores_time": 0.002032041549682617, "total_time": 44.93355679512024}, "parsing_time": {"total_time": 41.93651509284973, "per_file_time": {"mean": 0.032358422139544554, "std_dev": 0.008499422436278903}, "very_slow_stats": {"time_ratio": 0.23048725010395862, "count_ratio": 0.010802469135802469}, "very_slow_files": [{"fpath": "downloaded_repos/openstack_skyline-console/src/components/Form/index.jsx", "ftime": 0.43711018562316895}, {"fpath": "downloaded_repos/openstack_skyline-console/src/utils/validate.js", "ftime": 0.45409297943115234}, {"fpath": "downloaded_repos/openstack_skyline-console/src/components/FormItem/SelectTable/index.jsx", "ftime": 0.47185611724853516}, {"fpath": "downloaded_repos/openstack_skyline-console/src/components/MagicInput/index.jsx", "ftime": 0.5476329326629639}, {"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/container-infra.bundle.**********.js", "ftime": 0.6759519577026367}, {"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/f7186078e00d958aa2b316483dfc7e1c.js", "ftime": 0.7576398849487305}, {"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/base.bundle.**********.js", "ftime": 0.840191125869751}, {"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/configuration.bundle.**********.js", "ftime": 0.8968398571014404}, {"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/Container.bundle.**********.js", "ftime": 1.031013011932373}, {"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/storage.bundle.**********.js", "ftime": 2.032104969024658}]}, "scanning_time": {"total_time": 281.41145944595337, "per_file_time": {"mean": 0.06141673056437219, "std_dev": 0.4904512231339593}, "very_slow_stats": {"time_ratio": 0.5438217226589611, "count_ratio": 0.004583151462243562}, "very_slow_files": [{"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/network.bundle.**********.js", "ftime": 5.229282855987549}, {"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/common.bundle.**********.js", "ftime": 5.29099702835083}, {"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/compute.bundle.**********.js", "ftime": 5.295238971710205}, {"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/basic.bundle.**********.js", "ftime": 5.29890513420105}, {"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/container-infra.bundle.**********.js", "ftime": 9.02531099319458}, {"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/Container.bundle.**********.js", "ftime": 12.48387598991394}, {"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/identity.bundle.**********.js", "ftime": 17.546430110931396}, {"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/storage.bundle.**********.js", "ftime": 18.74617099761963}, {"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/share.bundle.**********.js", "ftime": 20.272501945495605}, {"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/monitor-center.bundle.**********.js", "ftime": 26.923386096954346}]}, "matching_time": {"total_time": 114.36441469192505, "per_file_and_rule_time": {"mean": 0.011659130868786336, "std_dev": 0.007100999050271454}, "very_slow_stats": {"time_ratio": 0.5112203486730031, "count_ratio": 0.012947293302069528}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/storage.bundle.**********.js", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 1.451050043106079}, {"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/monitor-center.bundle.**********.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 1.5385708808898926}, {"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/Container.bundle.**********.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 1.5637621879577637}, {"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/storage.bundle.**********.js", "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 1.7264900207519531}, {"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/share.bundle.**********.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 1.8657090663909912}, {"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/Container.bundle.**********.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 1.892542839050293}, {"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/identity.bundle.**********.js", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 2.1799888610839844}, {"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/share.bundle.**********.js", "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 2.4292991161346436}, {"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/monitor-center.bundle.**********.js", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 2.63612699508667}, {"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/storage.bundle.**********.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 3.5605928897857666}]}, "tainting_time": {"total_time": 45.884521484375, "per_def_and_rule_time": {"mean": 0.0009754984689579474, "std_dev": 0.00021774390861565798}, "very_slow_stats": {"time_ratio": 0.39096146546093763, "count_ratio": 0.001849607755596658}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/f7186078e00d958aa2b316483dfc7e1c.js", "fline": 1, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 0.415693998336792}, {"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/monitor-center.bundle.**********.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.4962790012359619}, {"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/identity.bundle.**********.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.49965715408325195}, {"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/storage.bundle.**********.js", "fline": 1, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.6276068687438965}, {"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/monitor-center.bundle.**********.js", "fline": 1, "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 0.8413980007171631}, {"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/storage.bundle.**********.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.9127039909362793}, {"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/share.bundle.**********.js", "fline": 1, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.9185159206390381}, {"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/monitor-center.bundle.**********.js", "fline": 1, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 1.1442019939422607}, {"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/share.bundle.**********.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 1.145766019821167}, {"fpath": "downloaded_repos/openstack_skyline-console/skyline_console/static/monitor-center.bundle.**********.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 1.544924020767212}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1160046848}, "engine_requested": "OSS", "skipped_rules": []}