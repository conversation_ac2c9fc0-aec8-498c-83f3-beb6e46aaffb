{"version": "1.130.0", "results": [{"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/mkalioby_django-passkeys/example/test_app/templates/login.html", "start": {"line": 32, "col": 9, "offset": 902}, "end": {"line": 50, "col": 16, "offset": 2048}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.csrf-exempt.no-csrf-exempt", "path": "downloaded_repos/mkalioby_django-passkeys/passkeys/FIDO2.py", "start": {"line": 76, "col": 1, "offset": 2680}, "end": {"line": 99, "col": 101, "offset": 3777}, "extra": {"message": "Detected usage of @csrf_exempt, which indicates that there is no CSRF token set for this route. This could lead to an attacker manipulating the user's account and exfiltration of private data. Instead, create a function without this decorator.", "metadata": {"cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["django"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.audit.csrf-exempt.no-csrf-exempt", "shortlink": "https://sg.run/rd5e"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.csrf-exempt.no-csrf-exempt", "path": "downloaded_repos/mkalioby_django-passkeys/passkeys/FIDO2.py", "start": {"line": 118, "col": 1, "offset": 4311}, "end": {"line": 157, "col": 16, "offset": 5948}, "extra": {"message": "Detected usage of @csrf_exempt, which indicates that there is no CSRF token set for this route. This could lead to an attacker manipulating the user's account and exfiltration of private data. Instead, create a function without this decorator.", "metadata": {"cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["django"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.audit.csrf-exempt.no-csrf-exempt", "shortlink": "https://sg.run/rd5e"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": "Syntax error", "message": "Syntax error at line downloaded_repos/mkalioby_django-passkeys/passkeys/templates/check_passkeys.js:1:\n `function check_passkey(platform_authenticator = true,success_func, fail_func)\n{\n    {% if request.session.passkey.cross_platform != False %}\n    if (platform_authenticator)\n    {\n        PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable()\n  .then((available) => {\n    if (available) {\n      success_func();\n    }\n    else{\n        fail_func();\n    }\n    })\n  }\n    success_func();\n    {% endif%}\n}\n\nfunction check_passkeys(success_func, fail_func)\n{\n    PublicKeyCredential.isUserVerifyingPlatformAuthenticatorAvailable()\n  .then((available) => {\n    if (available) {\n      success_func();\n    } else {\n      fail_func()\n    }\n  }).catch((err) => {\n    // Something went wrong\n    console.error(err);\n  });\n}` was unexpected", "path": "downloaded_repos/mkalioby_django-passkeys/passkeys/templates/check_passkeys.js"}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mkalioby_django-passkeys/example/test_app/templates/base.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 18, "offset": 17}}]], "message": "Syntax error at line downloaded_repos/mkalioby_django-passkeys/example/test_app/templates/base.html:1:\n `{% load static %}` was unexpected", "path": "downloaded_repos/mkalioby_django-passkeys/example/test_app/templates/base.html", "spans": [{"file": "downloaded_repos/mkalioby_django-passkeys/example/test_app/templates/base.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 18, "offset": 17}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mkalioby_django-passkeys/example/test_app/templates/home.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 20, "offset": 63}}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/test_app/templates/home.html", "start": {"line": 33, "col": 7, "offset": 0}, "end": {"line": 33, "col": 21, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/mkalioby_django-passkeys/example/test_app/templates/home.html:1:\n `{% extends 'base.html' %}\n{% load static %}\n{% block content %}` was unexpected", "path": "downloaded_repos/mkalioby_django-passkeys/example/test_app/templates/home.html", "spans": [{"file": "downloaded_repos/mkalioby_django-passkeys/example/test_app/templates/home.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 20, "offset": 63}}, {"file": "downloaded_repos/mkalioby_django-passkeys/example/test_app/templates/home.html", "start": {"line": 33, "col": 7, "offset": 0}, "end": {"line": 33, "col": 21, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mkalioby_django-passkeys/example/test_app/templates/login.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 18, "offset": 17}}]], "message": "Syntax error at line downloaded_repos/mkalioby_django-passkeys/example/test_app/templates/login.html:1:\n `{% load static %}` was unexpected", "path": "downloaded_repos/mkalioby_django-passkeys/example/test_app/templates/login.html", "spans": [{"file": "downloaded_repos/mkalioby_django-passkeys/example/test_app/templates/login.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 18, "offset": 17}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mkalioby_django-passkeys/example/test_app/templates/logout.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 18, "offset": 17}}]], "message": "Syntax error at line downloaded_repos/mkalioby_django-passkeys/example/test_app/templates/logout.html:1:\n `{% load static %}` was unexpected", "path": "downloaded_repos/mkalioby_django-passkeys/example/test_app/templates/logout.html", "spans": [{"file": "downloaded_repos/mkalioby_django-passkeys/example/test_app/templates/logout.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 18, "offset": 17}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mkalioby_django-passkeys/passkeys/templates/PassKeys.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 16, "offset": 85}}, {"path": "downloaded_repos/mkalioby_django-passkeys/passkeys/templates/PassKeys.html", "start": {"line": 109, "col": 1, "offset": 0}, "end": {"line": 111, "col": 16, "offset": 50}}, {"path": "downloaded_repos/mkalioby_django-passkeys/passkeys/templates/PassKeys.html", "start": {"line": 151, "col": 5, "offset": 0}, "end": {"line": 152, "col": 15, "offset": 41}}]], "message": "Syntax error at line downloaded_repos/mkalioby_django-passkeys/passkeys/templates/PassKeys.html:1:\n `{% extends \"PassKeys_base.html\" %}\n{% load static %}\n{% block head %}\n{{block.super}}` was unexpected", "path": "downloaded_repos/mkalioby_django-passkeys/passkeys/templates/PassKeys.html", "spans": [{"file": "downloaded_repos/mkalioby_django-passkeys/passkeys/templates/PassKeys.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 16, "offset": 85}}, {"file": "downloaded_repos/mkalioby_django-passkeys/passkeys/templates/PassKeys.html", "start": {"line": 109, "col": 1, "offset": 0}, "end": {"line": 111, "col": 16, "offset": 50}}, {"file": "downloaded_repos/mkalioby_django-passkeys/passkeys/templates/PassKeys.html", "start": {"line": 151, "col": 5, "offset": 0}, "end": {"line": 152, "col": 15, "offset": 41}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/mkalioby_django-passkeys/passkeys/templates/passkeys.js", "start": {"line": 1, "col": 2, "offset": 0}, "end": {"line": 1, "col": 17, "offset": 15}}, {"path": "downloaded_repos/mkalioby_django-passkeys/passkeys/templates/passkeys.js", "start": {"line": 2, "col": 1, "offset": 0}, "end": {"line": 79, "col": 14, "offset": 2594}}]], "message": "Syntax error at line downloaded_repos/mkalioby_django-passkeys/passkeys/templates/passkeys.js:1:\n `% load static %` was unexpected", "path": "downloaded_repos/mkalioby_django-passkeys/passkeys/templates/passkeys.js", "spans": [{"file": "downloaded_repos/mkalioby_django-passkeys/passkeys/templates/passkeys.js", "start": {"line": 1, "col": 2, "offset": 0}, "end": {"line": 1, "col": 17, "offset": 15}}, {"file": "downloaded_repos/mkalioby_django-passkeys/passkeys/templates/passkeys.js", "start": {"line": 2, "col": 1, "offset": 0}, "end": {"line": 79, "col": 14, "offset": 2594}}]}], "paths": {"scanned": ["downloaded_repos/mkalioby_django-passkeys/.github/workflows/basic_checks.yml", "downloaded_repos/mkalioby_django-passkeys/.github/workflows/run_tox.yml", "downloaded_repos/mkalioby_django-passkeys/.gitignore", "downloaded_repos/mkalioby_django-passkeys/CHANGELOG.md", "downloaded_repos/mkalioby_django-passkeys/Example.md", "downloaded_repos/mkalioby_django-passkeys/LICENSE", "downloaded_repos/mkalioby_django-passkeys/MANIFEST.in", "downloaded_repos/mkalioby_django-passkeys/README.md", "downloaded_repos/mkalioby_django-passkeys/SECURITY.md", "downloaded_repos/mkalioby_django-passkeys/coverage.svg", "downloaded_repos/mkalioby_django-passkeys/example/.coveragerc", "downloaded_repos/mkalioby_django-passkeys/example/__init__.py", "downloaded_repos/mkalioby_django-passkeys/example/example-ssl-requirements.txt", "downloaded_repos/mkalioby_django-passkeys/example/manage.py", "downloaded_repos/mkalioby_django-passkeys/example/static/css/sb-admin.min.css", "downloaded_repos/mkalioby_django-passkeys/example/static/js/sb-admin.js", "downloaded_repos/mkalioby_django-passkeys/example/static/mfa/css/bootstrap-toggle.min.css", "downloaded_repos/mkalioby_django-passkeys/example/test_app/__init__.py", "downloaded_repos/mkalioby_django-passkeys/example/test_app/auth.py", "downloaded_repos/mkalioby_django-passkeys/example/test_app/settings.py", "downloaded_repos/mkalioby_django-passkeys/example/test_app/soft_webauthn.py", "downloaded_repos/mkalioby_django-passkeys/example/test_app/templates/base.html", "downloaded_repos/mkalioby_django-passkeys/example/test_app/templates/home.html", "downloaded_repos/mkalioby_django-passkeys/example/test_app/templates/login.html", "downloaded_repos/mkalioby_django-passkeys/example/test_app/templates/logout.html", "downloaded_repos/mkalioby_django-passkeys/example/test_app/test_settings.py", "downloaded_repos/mkalioby_django-passkeys/example/test_app/urls.py", "downloaded_repos/mkalioby_django-passkeys/example/test_app/views.py", "downloaded_repos/mkalioby_django-passkeys/example/test_app/wsgi.py", "downloaded_repos/mkalioby_django-passkeys/imgs/conditionalUI.png", "downloaded_repos/mkalioby_django-passkeys/passkeys/FIDO2.py", "downloaded_repos/mkalioby_django-passkeys/passkeys/__init__.py", "downloaded_repos/mkalioby_django-passkeys/passkeys/backend.py", "downloaded_repos/mkalioby_django-passkeys/passkeys/migrations/0001_initial.py", "downloaded_repos/mkalioby_django-passkeys/passkeys/migrations/__init__.py", "downloaded_repos/mkalioby_django-passkeys/passkeys/models.py", "downloaded_repos/mkalioby_django-passkeys/passkeys/static/passkeys/css/bootstrap-toggle.min.css", "downloaded_repos/mkalioby_django-passkeys/passkeys/static/passkeys/imgs/FIDO-Passkey_Icon-White.png", "downloaded_repos/mkalioby_django-passkeys/passkeys/static/passkeys/js/base64url.js", "downloaded_repos/mkalioby_django-passkeys/passkeys/static/passkeys/js/helpers.js", "downloaded_repos/mkalioby_django-passkeys/passkeys/templates/PassKeys.html", "downloaded_repos/mkalioby_django-passkeys/passkeys/templates/PassKeys_base.html", "downloaded_repos/mkalioby_django-passkeys/passkeys/templates/check_passkeys.js", "downloaded_repos/mkalioby_django-passkeys/passkeys/templates/modal.html", "downloaded_repos/mkalioby_django-passkeys/passkeys/templates/passkeys.js", "downloaded_repos/mkalioby_django-passkeys/passkeys/urls.py", "downloaded_repos/mkalioby_django-passkeys/passkeys/views.py", "downloaded_repos/mkalioby_django-passkeys/requirements.txt", "downloaded_repos/mkalioby_django-passkeys/requirements_test.txt", "downloaded_repos/mkalioby_django-passkeys/setup.py", "downloaded_repos/mkalioby_django-passkeys/tox.ini"], "skipped": [{"path": "downloaded_repos/mkalioby_django-passkeys/example/static/js/sb-admin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/mfa/js/bootstrap-toggle.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/bootstrap/js/bootstrap.bundle.js.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/bootstrap/js/bootstrap.bundle.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/bootstrap/js/bootstrap.bundle.min.js.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/bootstrap/js/bootstrap.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/bootstrap/js/bootstrap.min.js.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/LICENSE.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/css/all.min.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/js/all.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/js/all.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/js/brands.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/js/brands.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/js/fontawesome.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/js/fontawesome.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/js/regular.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/js/regular.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/js/solid.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/js/solid.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/js/v4-shims.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/js/v4-shims.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/package.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/500px.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/accessible-icon.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/accusoft.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/acquisitions-incorporated.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/adn.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/adobe.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/adversal.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/affiliatetheme.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/airbnb.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/algolia.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/alipay.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/amazon-pay.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/amazon.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/amilia.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/android.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/angellist.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/angrycreative.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/angular.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/app-store-ios.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/app-store.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/apper.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/apple-pay.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/apple.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/artstation.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/asymmetrik.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/atlassian.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/audible.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/autoprefixer.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/avianex.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/aviato.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/aws.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/bandcamp.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/battle-net.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/behance-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/behance.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/bimobject.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/bitbucket.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/bitcoin.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/bity.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/black-tie.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/blackberry.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/blogger-b.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/blogger.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/bluetooth-b.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/bluetooth.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/bootstrap.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/btc.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/buffer.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/buromobelexperte.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/buysellads.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/canadian-maple-leaf.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/cc-amazon-pay.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/cc-amex.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/cc-apple-pay.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/cc-diners-club.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/cc-discover.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/cc-jcb.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/cc-mastercard.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/cc-paypal.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/cc-stripe.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/cc-visa.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/centercode.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/centos.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/chrome.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/chromecast.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/cloudscale.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/cloudsmith.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/cloudversify.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/codepen.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/codiepie.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/confluence.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/connectdevelop.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/contao.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/cpanel.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/creative-commons-by.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/creative-commons-nc-eu.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/creative-commons-nc-jp.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/creative-commons-nc.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/creative-commons-nd.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/creative-commons-pd-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/creative-commons-pd.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/creative-commons-remix.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/creative-commons-sa.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/creative-commons-sampling-plus.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/creative-commons-sampling.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/creative-commons-share.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/creative-commons-zero.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/creative-commons.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/critical-role.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/css3-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/css3.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/cuttlefish.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/d-and-d-beyond.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/d-and-d.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/dashcube.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/delicious.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/deploydog.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/deskpro.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/dev.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/deviantart.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/dhl.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/diaspora.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/digg.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/digital-ocean.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/discord.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/discourse.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/dochub.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/docker.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/draft2digital.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/dribbble-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/dribbble.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/dropbox.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/drupal.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/dyalog.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/earlybirds.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/ebay.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/edge.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/elementor.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/ello.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/ember.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/empire.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/envira.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/erlang.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/ethereum.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/etsy.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/evernote.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/expeditedssl.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/facebook-f.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/facebook-messenger.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/facebook-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/facebook.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/fantasy-flight-games.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/fedex.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/fedora.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/figma.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/firefox.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/first-order-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/first-order.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/firstdraft.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/flickr.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/flipboard.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/fly.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/font-awesome-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/font-awesome-flag.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/font-awesome-logo-full.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/font-awesome.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/fonticons-fi.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/fonticons.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/fort-awesome-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/fort-awesome.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/forumbee.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/foursquare.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/free-code-camp.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/freebsd.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/fulcrum.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/galactic-republic.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/galactic-senate.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/get-pocket.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/gg-circle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/gg.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/git-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/git-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/git.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/github-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/github-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/github.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/gitkraken.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/gitlab.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/gitter.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/glide-g.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/glide.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/gofore.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/goodreads-g.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/goodreads.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/google-drive.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/google-play.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/google-plus-g.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/google-plus-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/google-plus.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/google-wallet.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/google.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/gratipay.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/grav.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/gripfire.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/grunt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/gulp.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/hacker-news-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/hacker-news.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/hackerrank.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/hips.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/hire-a-helper.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/hooli.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/hornbill.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/hotjar.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/houzz.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/html5.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/hubspot.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/imdb.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/instagram.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/intercom.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/internet-explorer.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/invision.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/ioxhost.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/itch-io.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/itunes-note.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/itunes.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/java.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/jedi-order.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/jenkins.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/jira.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/joget.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/joomla.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/js-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/js.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/jsfiddle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/kaggle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/keybase.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/keycdn.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/kickstarter-k.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/kickstarter.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/korvue.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/laravel.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/lastfm-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/lastfm.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/leanpub.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/less.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/line.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/linkedin-in.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/linkedin.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/linode.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/linux.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/lyft.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/magento.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/mailchimp.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/mandalorian.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/markdown.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/mastodon.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/maxcdn.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/medapps.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/medium-m.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/medium.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/medrt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/meetup.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/megaport.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/mendeley.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/microsoft.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/mix.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/mixcloud.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/mizuni.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/modx.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/monero.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/napster.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/neos.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/nimblr.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/node-js.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/node.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/npm.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/ns8.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/nutritionix.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/odnoklassniki-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/odnoklassniki.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/old-republic.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/opencart.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/openid.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/opera.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/optin-monster.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/osi.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/page4.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/pagelines.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/palfed.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/patreon.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/paypal.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/penny-arcade.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/periscope.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/phabricator.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/phoenix-framework.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/phoenix-squadron.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/php.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/pied-piper-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/pied-piper-hat.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/pied-piper-pp.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/pied-piper.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/pinterest-p.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/pinterest-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/pinterest.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/playstation.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/product-hunt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/pushed.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/python.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/qq.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/quinscape.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/quora.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/r-project.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/raspberry-pi.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/ravelry.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/react.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/reacteurope.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/readme.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/rebel.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/red-river.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/reddit-alien.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/reddit-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/reddit.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/redhat.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/renren.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/replyd.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/researchgate.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/resolving.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/rev.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/rocketchat.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/rockrms.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/safari.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/salesforce.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/sass.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/schlix.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/scribd.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/searchengin.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/sellcast.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/sellsy.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/servicestack.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/shirtsinbulk.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/shopware.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/simplybuilt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/sistrix.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/sith.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/sketch.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/skyatlas.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/skype.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/slack-hash.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/slack.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/slideshare.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/snapchat-ghost.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/snapchat-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/snapchat.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/soundcloud.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/sourcetree.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/speakap.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/speaker-deck.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/spotify.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/squarespace.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/stack-exchange.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/stack-overflow.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/stackpath.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/staylinked.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/steam-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/steam-symbol.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/steam.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/sticker-mule.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/strava.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/stripe-s.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/stripe.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/studiovinari.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/stumbleupon-circle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/stumbleupon.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/superpowers.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/supple.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/suse.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/symfony.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/teamspeak.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/telegram-plane.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/telegram.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/tencent-weibo.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/the-red-yeti.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/themeco.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/themeisle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/think-peaks.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/trade-federation.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/trello.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/tripadvisor.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/tumblr-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/tumblr.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/twitch.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/twitter-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/twitter.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/typo3.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/uber.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/ubuntu.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/uikit.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/uniregistry.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/untappd.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/ups.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/usb.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/usps.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/ussunnah.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/vaadin.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/viacoin.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/viadeo-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/viadeo.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/viber.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/vimeo-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/vimeo-v.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/vimeo.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/vine.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/vk.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/vnv.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/vuejs.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/waze.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/weebly.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/weibo.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/weixin.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/whatsapp-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/whatsapp.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/whmcs.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/wikipedia-w.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/windows.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/wix.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/wizards-of-the-coast.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/wolf-pack-battalion.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/wordpress-simple.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/wordpress.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/wpbeginner.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/wpexplorer.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/wpforms.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/wpressr.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/xbox.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/xing-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/xing.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/y-combinator.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/yahoo.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/yammer.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/yandex-international.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/yandex.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/yarn.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/yelp.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/yoast.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/youtube-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/youtube.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/brands/zhihu.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/address-book.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/address-card.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/angry.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/arrow-alt-circle-down.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/arrow-alt-circle-left.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/arrow-alt-circle-right.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/arrow-alt-circle-up.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/bell-slash.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/bell.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/bookmark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/building.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/calendar-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/calendar-check.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/calendar-minus.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/calendar-plus.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/calendar-times.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/calendar.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/caret-square-down.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/caret-square-left.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/caret-square-right.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/caret-square-up.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/chart-bar.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/check-circle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/check-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/circle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/clipboard.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/clock.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/clone.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/closed-captioning.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/comment-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/comment-dots.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/comment.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/comments.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/compass.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/copy.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/copyright.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/credit-card.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/dizzy.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/dot-circle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/edit.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/envelope-open.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/envelope.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/eye-slash.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/eye.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/file-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/file-archive.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/file-audio.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/file-code.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/file-excel.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/file-image.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/file-pdf.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/file-powerpoint.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/file-video.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/file-word.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/file.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/flag.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/flushed.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/folder-open.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/folder.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/font-awesome-logo-full.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/frown-open.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/frown.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/futbol.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/gem.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/grimace.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/grin-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/grin-beam-sweat.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/grin-beam.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/grin-hearts.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/grin-squint-tears.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/grin-squint.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/grin-stars.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/grin-tears.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/grin-tongue-squint.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/grin-tongue-wink.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/grin-tongue.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/grin-wink.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/grin.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/hand-lizard.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/hand-paper.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/hand-peace.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/hand-point-down.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/hand-point-left.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/hand-point-right.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/hand-point-up.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/hand-pointer.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/hand-rock.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/hand-scissors.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/hand-spock.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/handshake.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/hdd.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/heart.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/hospital.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/hourglass.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/id-badge.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/id-card.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/image.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/images.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/keyboard.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/kiss-beam.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/kiss-wink-heart.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/kiss.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/laugh-beam.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/laugh-squint.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/laugh-wink.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/laugh.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/lemon.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/life-ring.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/lightbulb.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/list-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/map.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/meh-blank.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/meh-rolling-eyes.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/meh.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/minus-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/money-bill-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/moon.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/newspaper.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/object-group.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/object-ungroup.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/paper-plane.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/pause-circle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/play-circle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/plus-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/question-circle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/registered.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/sad-cry.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/sad-tear.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/save.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/share-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/smile-beam.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/smile-wink.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/smile.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/snowflake.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/star-half.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/star.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/sticky-note.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/stop-circle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/sun.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/surprise.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/thumbs-down.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/thumbs-up.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/times-circle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/tired.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/trash-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/user-circle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/user.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/window-close.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/window-maximize.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/window-minimize.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/regular/window-restore.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/ad.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/address-book.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/address-card.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/adjust.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/air-freshener.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/align-center.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/align-justify.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/align-left.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/align-right.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/allergies.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/ambulance.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/american-sign-language-interpreting.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/anchor.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/angle-double-down.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/angle-double-left.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/angle-double-right.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/angle-double-up.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/angle-down.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/angle-left.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/angle-right.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/angle-up.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/angry.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/ankh.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/apple-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/archive.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/archway.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/arrow-alt-circle-down.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/arrow-alt-circle-left.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/arrow-alt-circle-right.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/arrow-alt-circle-up.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/arrow-circle-down.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/arrow-circle-left.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/arrow-circle-right.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/arrow-circle-up.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/arrow-down.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/arrow-left.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/arrow-right.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/arrow-up.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/arrows-alt-h.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/arrows-alt-v.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/arrows-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/assistive-listening-systems.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/asterisk.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/at.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/atlas.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/atom.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/audio-description.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/award.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/baby-carriage.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/baby.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/backspace.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/backward.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/bacon.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/balance-scale-left.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/balance-scale-right.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/balance-scale.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/ban.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/band-aid.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/barcode.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/bars.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/baseball-ball.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/basketball-ball.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/bath.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/battery-empty.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/battery-full.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/battery-half.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/battery-quarter.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/battery-three-quarters.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/bed.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/beer.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/bell-slash.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/bell.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/bezier-curve.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/bible.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/bicycle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/biking.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/binoculars.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/biohazard.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/birthday-cake.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/blender-phone.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/blender.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/blind.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/blog.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/bold.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/bolt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/bomb.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/bone.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/bong.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/book-dead.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/book-medical.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/book-open.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/book-reader.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/book.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/bookmark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/border-all.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/border-none.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/border-style.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/bowling-ball.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/box-open.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/box.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/boxes.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/braille.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/brain.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/bread-slice.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/briefcase-medical.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/briefcase.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/broadcast-tower.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/broom.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/brush.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/bug.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/building.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/bullhorn.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/bullseye.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/burn.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/bus-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/bus.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/business-time.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/calculator.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/calendar-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/calendar-check.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/calendar-day.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/calendar-minus.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/calendar-plus.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/calendar-times.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/calendar-week.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/calendar.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/camera-retro.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/camera.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/campground.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/candy-cane.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/cannabis.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/capsules.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/car-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/car-battery.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/car-crash.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/car-side.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/car.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/caret-down.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/caret-left.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/caret-right.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/caret-square-down.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/caret-square-left.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/caret-square-right.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/caret-square-up.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/caret-up.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/carrot.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/cart-arrow-down.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/cart-plus.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/cash-register.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/cat.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/certificate.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/chair.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/chalkboard-teacher.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/chalkboard.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/charging-station.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/chart-area.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/chart-bar.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/chart-line.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/chart-pie.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/check-circle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/check-double.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/check-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/check.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/cheese.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/chess-bishop.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/chess-board.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/chess-king.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/chess-knight.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/chess-pawn.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/chess-queen.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/chess-rook.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/chess.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/chevron-circle-down.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/chevron-circle-left.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/chevron-circle-right.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/chevron-circle-up.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/chevron-down.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/chevron-left.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/chevron-right.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/chevron-up.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/child.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/church.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/circle-notch.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/circle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/city.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/clinic-medical.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/clipboard-check.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/clipboard-list.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/clipboard.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/clock.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/clone.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/closed-captioning.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/cloud-download-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/cloud-meatball.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/cloud-moon-rain.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/cloud-moon.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/cloud-rain.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/cloud-showers-heavy.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/cloud-sun-rain.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/cloud-sun.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/cloud-upload-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/cloud.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/cocktail.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/code-branch.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/code.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/coffee.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/cog.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/cogs.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/coins.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/columns.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/comment-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/comment-dollar.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/comment-dots.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/comment-medical.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/comment-slash.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/comment.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/comments-dollar.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/comments.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/compact-disc.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/compass.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/compress-arrows-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/compress.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/concierge-bell.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/cookie-bite.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/cookie.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/copy.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/copyright.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/couch.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/credit-card.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/crop-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/crop.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/cross.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/crosshairs.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/crow.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/crown.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/crutch.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/cube.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/cubes.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/cut.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/database.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/deaf.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/democrat.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/desktop.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/dharmachakra.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/diagnoses.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/dice-d20.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/dice-d6.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/dice-five.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/dice-four.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/dice-one.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/dice-six.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/dice-three.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/dice-two.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/dice.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/digital-tachograph.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/directions.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/divide.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/dizzy.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/dna.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/dog.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/dollar-sign.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/dolly-flatbed.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/dolly.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/donate.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/door-closed.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/door-open.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/dot-circle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/dove.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/download.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/drafting-compass.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/dragon.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/draw-polygon.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/drum-steelpan.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/drum.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/drumstick-bite.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/dumbbell.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/dumpster-fire.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/dumpster.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/dungeon.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/edit.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/egg.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/eject.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/ellipsis-h.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/ellipsis-v.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/envelope-open-text.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/envelope-open.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/envelope-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/envelope.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/equals.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/eraser.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/ethernet.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/euro-sign.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/exchange-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/exclamation-circle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/exclamation-triangle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/exclamation.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/expand-arrows-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/expand.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/external-link-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/external-link-square-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/eye-dropper.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/eye-slash.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/eye.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/fan.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/fast-backward.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/fast-forward.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/fax.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/feather-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/feather.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/female.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/fighter-jet.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/file-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/file-archive.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/file-audio.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/file-code.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/file-contract.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/file-csv.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/file-download.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/file-excel.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/file-export.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/file-image.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/file-import.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/file-invoice-dollar.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/file-invoice.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/file-medical-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/file-medical.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/file-pdf.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/file-powerpoint.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/file-prescription.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/file-signature.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/file-upload.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/file-video.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/file-word.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/file.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/fill-drip.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/fill.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/film.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/filter.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/fingerprint.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/fire-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/fire-extinguisher.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/fire.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/first-aid.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/fish.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/fist-raised.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/flag-checkered.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/flag-usa.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/flag.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/flask.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/flushed.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/folder-minus.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/folder-open.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/folder-plus.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/folder.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/font-awesome-logo-full.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/font.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/football-ball.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/forward.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/frog.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/frown-open.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/frown.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/funnel-dollar.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/futbol.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/gamepad.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/gas-pump.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/gavel.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/gem.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/genderless.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/ghost.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/gift.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/gifts.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/glass-cheers.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/glass-martini-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/glass-martini.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/glass-whiskey.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/glasses.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/globe-africa.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/globe-americas.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/globe-asia.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/globe-europe.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/globe.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/golf-ball.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/gopuram.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/graduation-cap.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/greater-than-equal.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/greater-than.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/grimace.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/grin-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/grin-beam-sweat.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/grin-beam.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/grin-hearts.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/grin-squint-tears.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/grin-squint.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/grin-stars.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/grin-tears.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/grin-tongue-squint.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/grin-tongue-wink.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/grin-tongue.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/grin-wink.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/grin.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/grip-horizontal.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/grip-lines-vertical.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/grip-lines.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/grip-vertical.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/guitar.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/h-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hamburger.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hammer.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hamsa.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hand-holding-heart.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hand-holding-usd.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hand-holding.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hand-lizard.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hand-middle-finger.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hand-paper.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hand-peace.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hand-point-down.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hand-point-left.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hand-point-right.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hand-point-up.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hand-pointer.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hand-rock.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hand-scissors.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hand-spock.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hands-helping.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hands.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/handshake.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hanukiah.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hard-hat.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hashtag.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hat-wizard.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/haykal.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hdd.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/heading.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/headphones-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/headphones.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/headset.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/heart-broken.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/heart.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/heartbeat.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/helicopter.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/highlighter.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hiking.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hippo.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/history.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hockey-puck.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/holly-berry.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/home.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/horse-head.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/horse.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hospital-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hospital-symbol.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hospital.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hot-tub.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hotdog.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hotel.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hourglass-end.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hourglass-half.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hourglass-start.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hourglass.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/house-damage.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/hryvnia.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/i-cursor.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/ice-cream.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/icicles.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/icons.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/id-badge.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/id-card-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/id-card.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/igloo.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/image.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/images.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/inbox.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/indent.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/industry.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/infinity.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/info-circle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/info.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/italic.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/jedi.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/joint.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/journal-whills.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/kaaba.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/key.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/keyboard.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/khanda.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/kiss-beam.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/kiss-wink-heart.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/kiss.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/kiwi-bird.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/landmark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/language.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/laptop-code.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/laptop-medical.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/laptop.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/laugh-beam.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/laugh-squint.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/laugh-wink.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/laugh.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/layer-group.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/leaf.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/lemon.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/less-than-equal.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/less-than.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/level-down-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/level-up-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/life-ring.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/lightbulb.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/link.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/lira-sign.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/list-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/list-ol.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/list-ul.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/list.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/location-arrow.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/lock-open.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/lock.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/long-arrow-alt-down.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/long-arrow-alt-left.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/long-arrow-alt-right.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/long-arrow-alt-up.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/low-vision.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/luggage-cart.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/magic.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/magnet.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/mail-bulk.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/male.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/map-marked-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/map-marked.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/map-marker-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/map-marker.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/map-pin.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/map-signs.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/map.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/marker.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/mars-double.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/mars-stroke-h.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/mars-stroke-v.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/mars-stroke.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/mars.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/mask.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/medal.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/medkit.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/meh-blank.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/meh-rolling-eyes.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/meh.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/memory.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/menorah.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/mercury.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/meteor.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/microchip.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/microphone-alt-slash.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/microphone-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/microphone-slash.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/microphone.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/microscope.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/minus-circle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/minus-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/minus.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/mitten.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/mobile-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/mobile.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/money-bill-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/money-bill-wave-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/money-bill-wave.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/money-bill.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/money-check-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/money-check.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/monument.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/moon.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/mortar-pestle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/mosque.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/motorcycle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/mountain.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/mouse-pointer.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/mug-hot.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/music.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/network-wired.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/neuter.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/newspaper.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/not-equal.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/notes-medical.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/object-group.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/object-ungroup.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/oil-can.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/om.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/otter.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/outdent.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/pager.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/paint-brush.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/paint-roller.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/palette.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/pallet.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/paper-plane.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/paperclip.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/parachute-box.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/paragraph.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/parking.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/passport.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/pastafarianism.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/paste.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/pause-circle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/pause.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/paw.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/peace.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/pen-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/pen-fancy.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/pen-nib.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/pen-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/pen.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/pencil-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/pencil-ruler.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/people-carry.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/pepper-hot.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/percent.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/percentage.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/person-booth.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/phone-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/phone-slash.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/phone-square-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/phone-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/phone-volume.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/phone.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/photo-video.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/piggy-bank.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/pills.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/pizza-slice.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/place-of-worship.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/plane-arrival.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/plane-departure.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/plane.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/play-circle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/play.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/plug.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/plus-circle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/plus-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/plus.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/podcast.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/poll-h.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/poll.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/poo-storm.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/poo.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/poop.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/portrait.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/pound-sign.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/power-off.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/pray.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/praying-hands.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/prescription-bottle-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/prescription-bottle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/prescription.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/print.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/procedures.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/project-diagram.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/puzzle-piece.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/qrcode.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/question-circle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/question.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/quidditch.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/quote-left.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/quote-right.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/quran.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/radiation-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/radiation.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/rainbow.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/random.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/receipt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/recycle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/redo-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/redo.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/registered.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/remove-format.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/reply-all.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/reply.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/republican.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/restroom.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/retweet.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/ribbon.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/ring.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/road.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/robot.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/rocket.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/route.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/rss-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/rss.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/ruble-sign.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/ruler-combined.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/ruler-horizontal.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/ruler-vertical.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/ruler.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/running.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/rupee-sign.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/sad-cry.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/sad-tear.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/satellite-dish.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/satellite.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/save.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/school.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/screwdriver.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/scroll.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/sd-card.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/search-dollar.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/search-location.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/search-minus.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/search-plus.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/search.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/seedling.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/server.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/shapes.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/share-alt-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/share-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/share-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/share.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/shekel-sign.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/shield-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/ship.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/shipping-fast.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/shoe-prints.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/shopping-bag.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/shopping-basket.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/shopping-cart.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/shower.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/shuttle-van.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/sign-in-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/sign-language.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/sign-out-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/sign.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/signal.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/signature.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/sim-card.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/sitemap.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/skating.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/skiing-nordic.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/skiing.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/skull-crossbones.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/skull.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/slash.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/sleigh.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/sliders-h.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/smile-beam.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/smile-wink.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/smile.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/smog.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/smoking-ban.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/smoking.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/sms.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/snowboarding.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/snowflake.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/snowman.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/snowplow.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/socks.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/solar-panel.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/sort-alpha-down-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/sort-alpha-down.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/sort-alpha-up-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/sort-alpha-up.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/sort-amount-down-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/sort-amount-down.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/sort-amount-up-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/sort-amount-up.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/sort-down.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/sort-numeric-down-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/sort-numeric-down.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/sort-numeric-up-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/sort-numeric-up.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/sort-up.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/sort.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/spa.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/space-shuttle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/spell-check.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/spider.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/spinner.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/splotch.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/spray-can.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/square-full.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/square-root-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/stamp.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/star-and-crescent.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/star-half-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/star-half.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/star-of-david.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/star-of-life.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/star.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/step-backward.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/step-forward.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/stethoscope.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/sticky-note.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/stop-circle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/stop.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/stopwatch.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/store-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/store.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/stream.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/street-view.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/strikethrough.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/stroopwafel.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/subscript.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/subway.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/suitcase-rolling.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/suitcase.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/sun.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/superscript.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/surprise.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/swatchbook.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/swimmer.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/swimming-pool.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/synagogue.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/sync-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/sync.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/syringe.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/table-tennis.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/table.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/tablet-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/tablet.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/tablets.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/tachometer-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/tag.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/tags.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/tape.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/tasks.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/taxi.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/teeth-open.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/teeth.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/temperature-high.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/temperature-low.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/tenge.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/terminal.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/text-height.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/text-width.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/th-large.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/th-list.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/th.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/theater-masks.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/thermometer-empty.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/thermometer-full.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/thermometer-half.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/thermometer-quarter.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/thermometer-three-quarters.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/thermometer.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/thumbs-down.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/thumbs-up.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/thumbtack.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/ticket-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/times-circle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/times.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/tint-slash.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/tint.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/tired.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/toggle-off.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/toggle-on.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/toilet-paper.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/toilet.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/toolbox.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/tools.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/tooth.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/torah.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/torii-gate.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/tractor.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/trademark.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/traffic-light.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/train.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/tram.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/transgender-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/transgender.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/trash-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/trash-restore-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/trash-restore.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/trash.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/tree.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/trophy.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/truck-loading.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/truck-monster.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/truck-moving.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/truck-pickup.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/truck.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/tshirt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/tty.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/tv.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/umbrella-beach.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/umbrella.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/underline.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/undo-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/undo.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/universal-access.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/university.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/unlink.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/unlock-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/unlock.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/upload.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/user-alt-slash.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/user-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/user-astronaut.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/user-check.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/user-circle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/user-clock.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/user-cog.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/user-edit.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/user-friends.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/user-graduate.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/user-injured.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/user-lock.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/user-md.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/user-minus.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/user-ninja.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/user-nurse.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/user-plus.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/user-secret.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/user-shield.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/user-slash.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/user-tag.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/user-tie.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/user-times.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/user.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/users-cog.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/users.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/utensil-spoon.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/utensils.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/vector-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/venus-double.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/venus-mars.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/venus.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/vial.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/vials.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/video-slash.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/video.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/vihara.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/voicemail.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/volleyball-ball.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/volume-down.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/volume-mute.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/volume-off.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/volume-up.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/vote-yea.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/vr-cardboard.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/walking.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/wallet.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/warehouse.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/water.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/wave-square.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/weight-hanging.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/weight.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/wheelchair.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/wifi.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/wind.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/window-close.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/window-maximize.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/window-minimize.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/window-restore.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/wine-bottle.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/wine-glass-alt.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/wine-glass.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/won-sign.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/wrench.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/x-ray.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/yen-sign.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/svgs/solid/yin-yang.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/webfonts/fa-brands-400.eot", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/webfonts/fa-brands-400.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/webfonts/fa-brands-400.ttf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/webfonts/fa-brands-400.woff", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/webfonts/fa-brands-400.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/webfonts/fa-regular-400.eot", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/webfonts/fa-regular-400.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/webfonts/fa-regular-400.ttf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/webfonts/fa-regular-400.woff", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/webfonts/fa-regular-400.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/webfonts/fa-solid-900.eot", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/webfonts/fa-solid-900.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/webfonts/fa-solid-900.ttf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/webfonts/fa-solid-900.woff", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/fontawesome-free/webfonts/fa-solid-900.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/jquery/jquery.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/jquery/jquery.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/jquery/jquery.min.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/jquery/jquery.slim.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/jquery/jquery.slim.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/jquery/jquery.slim.min.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/jquery-easing/jquery.easing.compatibility.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/jquery-easing/jquery.easing.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/static/vendor/jquery-easing/jquery.easing.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/test_app/templates/base.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/test_app/templates/home.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/test_app/templates/login.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/test_app/templates/logout.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/test_app/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/test_app/tests/test_current_platform.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/test_app/tests/test_fido.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/test_app/tests/test_passkeys.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/example/test_app/tests/test_views.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/passkeys/static/passkeys/js/bootstrap-toggle.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/mkalioby_django-passkeys/passkeys/templates/PassKeys.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mkalioby_django-passkeys/passkeys/templates/check_passkeys.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/mkalioby_django-passkeys/passkeys/templates/passkeys.js", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.8807330131530762, "profiling_times": {"config_time": 6.694229602813721, "core_time": 3.943368673324585, "ignores_time": 0.0021021366119384766, "total_time": 10.640880584716797}, "parsing_time": {"total_time": 0.4771769046783447, "per_file_time": {"mean": 0.015905896822611492, "std_dev": 0.00021881397999960788}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 2.8453705310821533, "per_file_time": {"mean": 0.02107681874875669, "std_dev": 0.001960675534470877}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.43164944648742676, "per_file_and_rule_time": {"mean": 0.0019620429385792124, "std_dev": 1.6317865164047344e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.11377644538879395, "per_def_and_rule_time": {"mean": 0.0007155751282314083, "std_dev": 1.2533196904584727e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089476480}, "engine_requested": "OSS", "skipped_rules": []}