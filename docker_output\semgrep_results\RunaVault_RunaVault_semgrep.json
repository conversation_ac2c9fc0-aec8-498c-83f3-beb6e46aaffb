{"version": "1.130.0", "results": [{"check_id": "generic.secrets.security.detected-sonarqube-docs-api-key.detected-sonarqube-docs-api-key", "path": "downloaded_repos/RunaVault_RunaVault/.github/workflows/sonarcloud.yml", "start": {"line": 50, "col": 15, "offset": 1973}, "end": {"line": 50, "col": 92, "offset": 2050}, "extra": {"message": "SonarQube Docs API Key detected", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "source-rule-url": "https://github.com/grab/secret-scanner/blob/master/scanner/signatures/pattern.go", "category": "security", "technology": ["secrets", "sonarqube"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-sonarqube-docs-api-key.detected-sonarqube-docs-api-key", "shortlink": "https://sg.run/x10P"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/RunaVault_RunaVault/backend/__tests__/utils.test.js", "start": {"line": 51, "col": 28, "offset": 1714}, "end": {"line": 51, "col": 99, "offset": 1785}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/RunaVault_RunaVault/backend/__tests__/verifyToken.test.js", "start": {"line": 49, "col": 28, "offset": 1469}, "end": {"line": 49, "col": 99, "offset": 1540}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/RunaVault_RunaVault/frontend/src/SecretsTab.js", "start": {"line": 149, "col": 27, "offset": 5708}, "end": {"line": 149, "col": 79, "offset": 5760}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/RunaVault_RunaVault/frontend/src/SecretsTab.js", "start": {"line": 162, "col": 25, "offset": 6315}, "end": {"line": 162, "col": 69, "offset": 6359}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/RunaVault_RunaVault/frontend/src/SecretsTab.js", "start": {"line": 279, "col": 27, "offset": 10302}, "end": {"line": 279, "col": 79, "offset": 10354}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/RunaVault_RunaVault/frontend/src/SecretsTab.js", "start": {"line": 292, "col": 25, "offset": 10909}, "end": {"line": 292, "col": 69, "offset": 10953}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "terraform.aws.security.aws-dynamodb-table-unencrypted.aws-dynamodb-table-unencrypted", "path": "downloaded_repos/RunaVault_RunaVault/terraform/modules/dynamodb.tf", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 53, "col": 2, "offset": 1034}, "extra": {"message": "By default, AWS DynamoDB Table is encrypted using AWS-managed keys. However, for added security, it's recommended to configure your own AWS KMS encryption key to protect your data in the DynamoDB table. You can either create a new aws_kms_key resource or use the ARN of an existing key in your AWS account to do so.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-326: Inadequate Encryption Strength"], "technology": ["aws", "terraform"], "category": "security", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/terraform.aws.security.aws-dynamodb-table-unencrypted.aws-dynamodb-table-unencrypted", "shortlink": "https://sg.run/Ay4p"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "terraform.aws.security.aws-lambda-x-ray-tracing-not-active.aws-lambda-x-ray-tracing-not-active", "path": "downloaded_repos/RunaVault_RunaVault/terraform/modules/lambda/main.tf", "start": {"line": 37, "col": 1, "offset": 814}, "end": {"line": 62, "col": 2, "offset": 1476}, "extra": {"message": "The AWS Lambda function does not have active X-Ray tracing enabled. X-Ray tracing enables end-to-end debugging and analysis of all function activity. This makes it easier to trace the flow of logs and identify bottlenecks, slow downs and timeouts.", "metadata": {"category": "security", "technology": ["aws", "terraform"], "owasp": ["A09:2021 Security Logging and Monitoring Failures"], "cwe": ["CWE-778: Insufficient Logging"], "references": ["https://cwe.mitre.org/data/definitions/778.html", "https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lambda_function#mode", "https://docs.aws.amazon.com/lambda/latest/dg/services-xray.html"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insufficient Logging"], "source": "https://semgrep.dev/r/terraform.aws.security.aws-lambda-x-ray-tracing-not-active.aws-lambda-x-ray-tracing-not-active", "shortlink": "https://sg.run/wO2Y"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "terraform.aws.security.aws-lambda-environment-unencrypted.aws-lambda-environment-unencrypted", "path": "downloaded_repos/RunaVault_RunaVault/terraform/modules/lambda/main.tf", "start": {"line": 52, "col": 3, "offset": 1322}, "end": {"line": 54, "col": 4, "offset": 1381}, "extra": {"message": "By default, the AWS Lambda Environment is encrypted using AWS-managed keys. However, for added security, it's recommended to configure your own AWS KMS encryption key to protect your environment variables in Lambda. You can either create a new aws_kms_key resource or use the ARN of an existing key in your AWS account to do so.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure"], "cwe": ["CWE-320: CWE CATEGORY: Key Management Errors"], "technology": ["aws", "terraform"], "category": "security", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/terraform.aws.security.aws-lambda-environment-unencrypted.aws-lambda-environment-unencrypted", "shortlink": "https://sg.run/x4lz"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "terraform.aws.security.aws-cloudwatch-log-group-unencrypted.aws-cloudwatch-log-group-unencrypted", "path": "downloaded_repos/RunaVault_RunaVault/terraform/modules/lambda/main.tf", "start": {"line": 93, "col": 1, "offset": 2270}, "end": {"line": 102, "col": 2, "offset": 2544}, "extra": {"message": "By default, AWS CloudWatch Log Group is encrypted using AWS-managed keys. However, for added security, it's recommended to configure your own AWS KMS encryption key to protect your log group in CloudWatch. You can either create a new aws_kms_key resource or use the ARN of an existing key in your AWS account to do so.", "metadata": {"owasp": ["A02:2021 - Cryptographic Failures"], "cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "technology": ["aws", "terraform"], "category": "security", "references": ["https://cwe.mitre.org/data/definitions/732.html"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/terraform.aws.security.aws-cloudwatch-log-group-unencrypted.aws-cloudwatch-log-group-unencrypted", "shortlink": "https://sg.run/Pg6Y"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/RunaVault_RunaVault/.github/workflows/sonarcloud.yml", "downloaded_repos/RunaVault_RunaVault/.github/workflows/test.yml", "downloaded_repos/RunaVault_RunaVault/.gitignore", "downloaded_repos/RunaVault_RunaVault/CHANGELOG.md", "downloaded_repos/RunaVault_RunaVault/CONTRIBUTION.md", "downloaded_repos/RunaVault_RunaVault/LICENSE", "downloaded_repos/RunaVault_RunaVault/README.md", "downloaded_repos/RunaVault_RunaVault/backend/.babelrc", "downloaded_repos/RunaVault_RunaVault/backend/.gitignore", "downloaded_repos/RunaVault_RunaVault/backend/__tests__/add_user_to_groups.test.js", "downloaded_repos/RunaVault_RunaVault/backend/__tests__/create_group.test.js", "downloaded_repos/RunaVault_RunaVault/backend/__tests__/create_secret.test.js", "downloaded_repos/RunaVault_RunaVault/backend/__tests__/create_user.test.js", "downloaded_repos/RunaVault_RunaVault/backend/__tests__/delete_group.test.js", "downloaded_repos/RunaVault_RunaVault/backend/__tests__/delete_secret.test.js", "downloaded_repos/RunaVault_RunaVault/backend/__tests__/edit_secret.test.js", "downloaded_repos/RunaVault_RunaVault/backend/__tests__/edit_users.test.js", "downloaded_repos/RunaVault_RunaVault/backend/__tests__/formatResponse.test.js", "downloaded_repos/RunaVault_RunaVault/backend/__tests__/getAuthToken.test.js", "downloaded_repos/RunaVault_RunaVault/backend/__tests__/get_secret.test.js", "downloaded_repos/RunaVault_RunaVault/backend/__tests__/list_groups.test.js", "downloaded_repos/RunaVault_RunaVault/backend/__tests__/list_secrets.test.js", "downloaded_repos/RunaVault_RunaVault/backend/__tests__/list_user_groups.test.js", "downloaded_repos/RunaVault_RunaVault/backend/__tests__/list_users.test.js", "downloaded_repos/RunaVault_RunaVault/backend/__tests__/parseBody.test.js", "downloaded_repos/RunaVault_RunaVault/backend/__tests__/remove_user_from_groups.test.js", "downloaded_repos/RunaVault_RunaVault/backend/__tests__/sanitizeObject.test.js", "downloaded_repos/RunaVault_RunaVault/backend/__tests__/sanitizeString.test.js", "downloaded_repos/RunaVault_RunaVault/backend/__tests__/share_directory.test.js", "downloaded_repos/RunaVault_RunaVault/backend/__tests__/simple.test.js", "downloaded_repos/RunaVault_RunaVault/backend/__tests__/utils.test.js", "downloaded_repos/RunaVault_RunaVault/backend/__tests__/verifyToken.test.js", "downloaded_repos/RunaVault_RunaVault/backend/add_user_to_groups/index.js", "downloaded_repos/RunaVault_RunaVault/backend/add_user_to_groups/package-lock.json", "downloaded_repos/RunaVault_RunaVault/backend/add_user_to_groups/package.json", "downloaded_repos/RunaVault_RunaVault/backend/create_group/index.js", "downloaded_repos/RunaVault_RunaVault/backend/create_group/package-lock.json", "downloaded_repos/RunaVault_RunaVault/backend/create_group/package.json", "downloaded_repos/RunaVault_RunaVault/backend/create_secret/index.js", "downloaded_repos/RunaVault_RunaVault/backend/create_secret/package-lock.json", "downloaded_repos/RunaVault_RunaVault/backend/create_secret/package.json", "downloaded_repos/RunaVault_RunaVault/backend/create_user/index.js", "downloaded_repos/RunaVault_RunaVault/backend/create_user/package-lock.json", "downloaded_repos/RunaVault_RunaVault/backend/create_user/package.json", "downloaded_repos/RunaVault_RunaVault/backend/delete_group/index.js", "downloaded_repos/RunaVault_RunaVault/backend/delete_group/package-lock.json", "downloaded_repos/RunaVault_RunaVault/backend/delete_group/package.json", "downloaded_repos/RunaVault_RunaVault/backend/delete_secret/index.js", "downloaded_repos/RunaVault_RunaVault/backend/delete_secret/package-lock.json", "downloaded_repos/RunaVault_RunaVault/backend/delete_secret/package.json", "downloaded_repos/RunaVault_RunaVault/backend/edit_secret/index.js", "downloaded_repos/RunaVault_RunaVault/backend/edit_secret/package-lock.json", "downloaded_repos/RunaVault_RunaVault/backend/edit_secret/package.json", "downloaded_repos/RunaVault_RunaVault/backend/edit_users/index.js", "downloaded_repos/RunaVault_RunaVault/backend/edit_users/package-lock.json", "downloaded_repos/RunaVault_RunaVault/backend/edit_users/package.json", "downloaded_repos/RunaVault_RunaVault/backend/get_secret/index.js", "downloaded_repos/RunaVault_RunaVault/backend/get_secret/package-lock.json", "downloaded_repos/RunaVault_RunaVault/backend/get_secret/package.json", "downloaded_repos/RunaVault_RunaVault/backend/jest.config.cjs", "downloaded_repos/RunaVault_RunaVault/backend/layers/nodejs/package-lock.json", "downloaded_repos/RunaVault_RunaVault/backend/layers/nodejs/package.json", "downloaded_repos/RunaVault_RunaVault/backend/layers/nodejs/utils.js", "downloaded_repos/RunaVault_RunaVault/backend/list_groups/index.js", "downloaded_repos/RunaVault_RunaVault/backend/list_groups/package-lock.json", "downloaded_repos/RunaVault_RunaVault/backend/list_groups/package.json", "downloaded_repos/RunaVault_RunaVault/backend/list_secrets/index.js", "downloaded_repos/RunaVault_RunaVault/backend/list_secrets/package-lock.json", "downloaded_repos/RunaVault_RunaVault/backend/list_secrets/package.json", "downloaded_repos/RunaVault_RunaVault/backend/list_user_groups/index.js", "downloaded_repos/RunaVault_RunaVault/backend/list_user_groups/package-lock.json", "downloaded_repos/RunaVault_RunaVault/backend/list_user_groups/package.json", "downloaded_repos/RunaVault_RunaVault/backend/list_users/index.js", "downloaded_repos/RunaVault_RunaVault/backend/list_users/package-lock.json", "downloaded_repos/RunaVault_RunaVault/backend/list_users/package.json", "downloaded_repos/RunaVault_RunaVault/backend/package-lock.json", "downloaded_repos/RunaVault_RunaVault/backend/package.json", "downloaded_repos/RunaVault_RunaVault/backend/remove_user_from_groups/index.js", "downloaded_repos/RunaVault_RunaVault/backend/remove_user_from_groups/package-lock.json", "downloaded_repos/RunaVault_RunaVault/backend/remove_user_from_groups/package.json", "downloaded_repos/RunaVault_RunaVault/backend/share_directory/index.js", "downloaded_repos/RunaVault_RunaVault/backend/share_directory/package-lock.json", "downloaded_repos/RunaVault_RunaVault/backend/share_directory/package.json", "downloaded_repos/RunaVault_RunaVault/frontend/package-lock.json", "downloaded_repos/RunaVault_RunaVault/frontend/package.json", "downloaded_repos/RunaVault_RunaVault/frontend/public/favicon.ico", "downloaded_repos/RunaVault_RunaVault/frontend/public/index.html", "downloaded_repos/RunaVault_RunaVault/frontend/public/logo192.png", "downloaded_repos/RunaVault_RunaVault/frontend/public/logo512.png", "downloaded_repos/RunaVault_RunaVault/frontend/public/manifest.json", "downloaded_repos/RunaVault_RunaVault/frontend/public/robots.txt", "downloaded_repos/RunaVault_RunaVault/frontend/src/AdminPanel.js", "downloaded_repos/RunaVault_RunaVault/frontend/src/App.css", "downloaded_repos/RunaVault_RunaVault/frontend/src/App.js", "downloaded_repos/RunaVault_RunaVault/frontend/src/App.test.js", "downloaded_repos/RunaVault_RunaVault/frontend/src/AppContext.js", "downloaded_repos/RunaVault_RunaVault/frontend/src/BackgroundAnimation.js", "downloaded_repos/RunaVault_RunaVault/frontend/src/CreateSecretForm.js", "downloaded_repos/RunaVault_RunaVault/frontend/src/CryptoUtils.js", "downloaded_repos/RunaVault_RunaVault/frontend/src/SecretsTab.js", "downloaded_repos/RunaVault_RunaVault/frontend/src/ThemeContext.js", "downloaded_repos/RunaVault_RunaVault/frontend/src/UserInfoTab.js", "downloaded_repos/RunaVault_RunaVault/frontend/src/index.css", "downloaded_repos/RunaVault_RunaVault/frontend/src/index.js", "downloaded_repos/RunaVault_RunaVault/frontend/src/setupTests.js", "downloaded_repos/RunaVault_RunaVault/img/admin.jpg", "downloaded_repos/RunaVault_RunaVault/img/create.jpg", "downloaded_repos/RunaVault_RunaVault/img/main.jpg", "downloaded_repos/RunaVault_RunaVault/img/runavault-diagram.png", "downloaded_repos/RunaVault_RunaVault/terraform/application/backend.tf", "downloaded_repos/RunaVault_RunaVault/terraform/application/main.tf", "downloaded_repos/RunaVault_RunaVault/terraform/modules/acm.tf", "downloaded_repos/RunaVault_RunaVault/terraform/modules/api_gateway/main.tf", "downloaded_repos/RunaVault_RunaVault/terraform/modules/api_gateway/output.tf", "downloaded_repos/RunaVault_RunaVault/terraform/modules/api_gateway/variables.tf", "downloaded_repos/RunaVault_RunaVault/terraform/modules/api_gateway.tf", "downloaded_repos/RunaVault_RunaVault/terraform/modules/cognito.tf", "downloaded_repos/RunaVault_RunaVault/terraform/modules/data.tf", "downloaded_repos/RunaVault_RunaVault/terraform/modules/dynamodb.tf", "downloaded_repos/RunaVault_RunaVault/terraform/modules/frontend.tf", "downloaded_repos/RunaVault_RunaVault/terraform/modules/kms.tf", "downloaded_repos/RunaVault_RunaVault/terraform/modules/lambda/main.tf", "downloaded_repos/RunaVault_RunaVault/terraform/modules/lambda/outputs.tf", "downloaded_repos/RunaVault_RunaVault/terraform/modules/lambda/variables.tf", "downloaded_repos/RunaVault_RunaVault/terraform/modules/lambda_add_user_to_groups.tf", "downloaded_repos/RunaVault_RunaVault/terraform/modules/lambda_create_group.tf", "downloaded_repos/RunaVault_RunaVault/terraform/modules/lambda_create_secret.tf", "downloaded_repos/RunaVault_RunaVault/terraform/modules/lambda_create_user.tf", "downloaded_repos/RunaVault_RunaVault/terraform/modules/lambda_delete_group.tf", "downloaded_repos/RunaVault_RunaVault/terraform/modules/lambda_delete_secret.tf", "downloaded_repos/RunaVault_RunaVault/terraform/modules/lambda_edit_secret.tf", "downloaded_repos/RunaVault_RunaVault/terraform/modules/lambda_edit_users.tf", "downloaded_repos/RunaVault_RunaVault/terraform/modules/lambda_get_secret.tf", "downloaded_repos/RunaVault_RunaVault/terraform/modules/lambda_layers.tf", "downloaded_repos/RunaVault_RunaVault/terraform/modules/lambda_list_groups.tf", "downloaded_repos/RunaVault_RunaVault/terraform/modules/lambda_list_secrets.tf", "downloaded_repos/RunaVault_RunaVault/terraform/modules/lambda_list_user_groups.tf", "downloaded_repos/RunaVault_RunaVault/terraform/modules/lambda_list_users.tf", "downloaded_repos/RunaVault_RunaVault/terraform/modules/lambda_remove_user_from_groups.tf", "downloaded_repos/RunaVault_RunaVault/terraform/modules/lambda_share_directory.tf", "downloaded_repos/RunaVault_RunaVault/terraform/modules/locals.tf", "downloaded_repos/RunaVault_RunaVault/terraform/modules/outputs.tf", "downloaded_repos/RunaVault_RunaVault/terraform/modules/providers.tf", "downloaded_repos/RunaVault_RunaVault/terraform/modules/route53.tf", "downloaded_repos/RunaVault_RunaVault/terraform/modules/variables.tf"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.6846339702606201, "profiling_times": {"config_time": 6.542311906814575, "core_time": 7.92221474647522, "ignores_time": 0.0018088817596435547, "total_time": 14.467154741287231}, "parsing_time": {"total_time": 6.4125542640686035, "per_file_time": {"mean": 0.05213458751275288, "std_dev": 0.017710022816473964}, "very_slow_stats": {"time_ratio": 0.4236071695178375, "count_ratio": 0.032520325203252036}, "very_slow_files": [{"fpath": "downloaded_repos/RunaVault_RunaVault/frontend/src/AdminPanel.js", "ftime": 0.30554699897766113}, {"fpath": "downloaded_repos/RunaVault_RunaVault/frontend/src/App.js", "ftime": 0.5780739784240723}, {"fpath": "downloaded_repos/RunaVault_RunaVault/frontend/src/SecretsTab.js", "ftime": 0.7438910007476807}, {"fpath": "downloaded_repos/RunaVault_RunaVault/frontend/package-lock.json", "ftime": 1.0888919830322266}]}, "scanning_time": {"total_time": 43.84321737289429, "per_file_time": {"mean": 0.10488807983946005, "std_dev": 0.18941902811130235}, "very_slow_stats": {"time_ratio": 0.4569348509548948, "count_ratio": 0.01674641148325359}, "very_slow_files": [{"fpath": "downloaded_repos/RunaVault_RunaVault/backend/__tests__/edit_secret.test.js", "ftime": 1.5873799324035645}, {"fpath": "downloaded_repos/RunaVault_RunaVault/frontend/package-lock.json", "ftime": 1.782196044921875}, {"fpath": "downloaded_repos/RunaVault_RunaVault/backend/share_directory/index.js", "ftime": 1.9513239860534668}, {"fpath": "downloaded_repos/RunaVault_RunaVault/frontend/package-lock.json", "ftime": 2.166527032852173}, {"fpath": "downloaded_repos/RunaVault_RunaVault/frontend/src/AdminPanel.js", "ftime": 2.9097890853881836}, {"fpath": "downloaded_repos/RunaVault_RunaVault/frontend/src/App.js", "ftime": 4.53004789352417}, {"fpath": "downloaded_repos/RunaVault_RunaVault/frontend/src/SecretsTab.js", "ftime": 5.106230020523071}]}, "matching_time": {"total_time": 20.107604265213013, "per_file_and_rule_time": {"mean": 0.015587290128072105, "std_dev": 0.003892649661311331}, "very_slow_stats": {"time_ratio": 0.5241311847335118, "count_ratio": 0.03333333333333333}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/RunaVault_RunaVault/frontend/package-lock.json", "rule_id": "json.aws.security.public-s3-policy-statement.public-s3-policy-statement", "time": 0.25179505348205566}, {"fpath": "downloaded_repos/RunaVault_RunaVault/frontend/package-lock.json", "rule_id": "json.aws.security.public-s3-bucket.public-s3-bucket", "time": 0.2592339515686035}, {"fpath": "downloaded_repos/RunaVault_RunaVault/frontend/src/App.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.2656559944152832}, {"fpath": "downloaded_repos/RunaVault_RunaVault/frontend/src/AdminPanel.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.3421480655670166}, {"fpath": "downloaded_repos/RunaVault_RunaVault/frontend/src/CreateSecretForm.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.34480786323547363}, {"fpath": "downloaded_repos/RunaVault_RunaVault/backend/share_directory/index.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.3529691696166992}, {"fpath": "downloaded_repos/RunaVault_RunaVault/frontend/src/App.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.4642488956451416}, {"fpath": "downloaded_repos/RunaVault_RunaVault/frontend/src/SecretsTab.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.8215060234069824}, {"fpath": "downloaded_repos/RunaVault_RunaVault/frontend/src/SecretsTab.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.8429610729217529}, {"fpath": "downloaded_repos/RunaVault_RunaVault/frontend/src/App.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 1.3860769271850586}]}, "tainting_time": {"total_time": 8.274207592010498, "per_def_and_rule_time": {"mean": 0.02248425976089809, "std_dev": 0.0015312475452555344}, "very_slow_stats": {"time_ratio": 0.5175522270255, "count_ratio": 0.10869565217391304}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/RunaVault_RunaVault/backend/list_users/index.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.1387479305267334}, {"fpath": "downloaded_repos/RunaVault_RunaVault/frontend/src/SecretsTab.js", "fline": 14, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.14034414291381836}, {"fpath": "downloaded_repos/RunaVault_RunaVault/frontend/src/SecretsTab.js", "fline": 14, "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 0.15289616584777832}, {"fpath": "downloaded_repos/RunaVault_RunaVault/frontend/src/AdminPanel.js", "fline": 7, "rule_id": "javascript.express.security.require-request.require-request", "time": 0.15291690826416016}, {"fpath": "downloaded_repos/RunaVault_RunaVault/backend/share_directory/index.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.16698598861694336}, {"fpath": "downloaded_repos/RunaVault_RunaVault/frontend/src/AdminPanel.js", "fline": 7, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.1875159740447998}, {"fpath": "downloaded_repos/RunaVault_RunaVault/backend/share_directory/index.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.18761515617370605}, {"fpath": "downloaded_repos/RunaVault_RunaVault/frontend/src/AdminPanel.js", "fline": 7, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.21904897689819336}, {"fpath": "downloaded_repos/RunaVault_RunaVault/frontend/src/SecretsTab.js", "fline": 14, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.29355502128601074}, {"fpath": "downloaded_repos/RunaVault_RunaVault/frontend/src/SecretsTab.js", "fline": 14, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.37502408027648926}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}