<?php
namespace Concrete\Core\Permission\Access\ListItem;

class AddBlockToAreaAreaListItem extends AreaListItem
{
    protected $customBlockTypeArray = array();
    protected $blockTypesAllowedPermission = 'N';

    public function setBlockTypesAllowedPermission($permission)
    {
        $this->blockTypesAllowedPermission = $permission;
    }
    public function getBlockTypesAllowedPermission()
    {
        return $this->blockTypesAllowedPermission;
    }
    public function setBlockTypesAllowedArray($btIDs)
    {
        $this->customBlockTypeArray = $btIDs;
    }
    public function getBlockTypesAllowedArray()
    {
        return $this->customBlockTypeArray;
    }
}
