{"version": "1.130.0", "results": [{"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/SiddhantSadangi_st_login_form/assets/sidebar.html", "start": {"line": 9, "col": 9, "offset": 262}, "end": {"line": 9, "col": 83, "offset": 336}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/SiddhantSadangi_st_login_form/.github/FUNDING.yml", "downloaded_repos/SiddhantSadangi_st_login_form/.github/ISSUE_TEMPLATE/bug_report.md", "downloaded_repos/SiddhantSadangi_st_login_form/.github/ISSUE_TEMPLATE/feature_request.md", "downloaded_repos/SiddhantSadangi_st_login_form/.github/dependabot.yml", "downloaded_repos/SiddhantSadangi_st_login_form/.github/workflows/publish_PYPI_each_tag.yml", "downloaded_repos/SiddhantSadangi_st_login_form/.gitignore", "downloaded_repos/SiddhantSadangi_st_login_form/.pre-commit-config.yaml", "downloaded_repos/SiddhantSadangi_st_login_form/.sourcery.yaml", "downloaded_repos/SiddhantSadangi_st_login_form/.streamlit/config.toml", "downloaded_repos/SiddhantSadangi_st_login_form/CODE_OF_CONDUCT.md", "downloaded_repos/SiddhantSadangi_st_login_form/LICENSE", "downloaded_repos/SiddhantSadangi_st_login_form/README.md", "downloaded_repos/SiddhantSadangi_st_login_form/adhoc.ipynb", "downloaded_repos/SiddhantSadangi_st_login_form/assets/screenshot.png", "downloaded_repos/SiddhantSadangi_st_login_form/assets/sidebar.html", "downloaded_repos/SiddhantSadangi_st_login_form/demo.py", "downloaded_repos/SiddhantSadangi_st_login_form/requirements.txt", "downloaded_repos/SiddhantSadangi_st_login_form/setup.py", "downloaded_repos/SiddhantSadangi_st_login_form/src/st_login_form/__init__.py", "downloaded_repos/SiddhantSadangi_st_login_form/src/st_login_form/_helpers/__init__.py", "downloaded_repos/SiddhantSadangi_st_login_form/src/st_login_form/_helpers/auth.py", "downloaded_repos/SiddhantSadangi_st_login_form/src/st_login_form/_helpers/forms.py", "downloaded_repos/SiddhantSadangi_st_login_form/test.py"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 2.4323439598083496, "profiling_times": {"config_time": 7.812143564224243, "core_time": 3.558427572250366, "ignores_time": 0.00323486328125, "total_time": 11.374875783920288}, "parsing_time": {"total_time": 0.25161290168762207, "per_file_time": {"mean": 0.01797235012054443, "std_dev": 2.0103227052053232e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.171731948852539, "per_file_time": {"mean": 0.019528865814208988, "std_dev": 0.0012081024164767005}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.1800248622894287, "per_file_and_rule_time": {"mean": 0.0008491738787237202, "std_dev": 3.940062305720421e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.023827552795410156, "per_def_and_rule_time": {"mean": 0.0007942517598470051, "std_dev": 1.1440367883450465e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089364160}, "engine_requested": "OSS", "skipped_rules": []}