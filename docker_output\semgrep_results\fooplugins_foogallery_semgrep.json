{"version": "1.130.0", "results": [{"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/fooplugins_foogallery/extensions/albums/admin/class-metaboxes.php", "start": {"line": 435, "col": 19, "offset": 16398}, "end": {"line": 435, "col": 74, "offset": 16453}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.phpinfo-use.phpinfo-use", "path": "downloaded_repos/fooplugins_foogallery/includes/admin/view-system-info.php", "start": {"line": 26, "col": 2, "offset": 615}, "end": {"line": 26, "col": 15, "offset": 628}, "extra": {"message": "The 'phpinfo' function may reveal sensitive information about your environment.", "metadata": {"cwe": ["CWE-200: Exposure of Sensitive Information to an Unauthorized Actor"], "references": ["https://www.php.net/manual/en/function.phpinfo", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/PhpinfosSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2021 - Broken Access Control"], "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.phpinfo-use.phpinfo-use", "shortlink": "https://sg.run/W82E"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/fooplugins_foogallery/js/admin-foogallery-edit.js", "start": {"line": 252, "col": 17, "offset": 9738}, "end": {"line": 252, "col": 39, "offset": 9760}, "extra": {"message": "RegExp() called with a `$` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/fooplugins_foogallery/js/admin-foogallery-edit.js", "start": {"line": 252, "col": 17, "offset": 9738}, "end": {"line": 252, "col": 39, "offset": 9760}, "extra": {"message": "RegExp() called with a `item` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/fooplugins_foogallery/pro/js/foogallery.media-views.js", "start": {"line": 1146, "col": 18, "offset": 41389}, "end": {"line": 1146, "col": 37, "offset": 41408}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/fooplugins_foogallery/pro/js/foogallery.media-views.js", "start": {"line": 1361, "col": 18, "offset": 47010}, "end": {"line": 1361, "col": 38, "offset": 47030}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/fooplugins_foogallery/pro/js/foogallery.media-views.js", "start": {"line": 1915, "col": 12, "offset": 64023}, "end": {"line": 1915, "col": 36, "offset": 64047}, "extra": {"message": "RegExp() called with a `pattern` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/fooplugins_foogallery/pro/js/foogallery.media-views.js", "start": {"line": 2420, "col": 12, "offset": 80878}, "end": {"line": 2420, "col": 36, "offset": 80902}, "extra": {"message": "RegExp() called with a `pattern` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-open-redirect.express-open-redirect", "message": "Timeout when running javascript.express.security.audit.express-open-redirect.express-open-redirect on downloaded_repos/fooplugins_foogallery/extensions/default-templates/shared/js/foogallery.js:\n ", "path": "downloaded_repos/fooplugins_foogallery/extensions/default-templates/shared/js/foogallery.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/fooplugins_foogallery/extensions/default-templates/shared/js/foogallery.js:\n ", "path": "downloaded_repos/fooplugins_foogallery/extensions/default-templates/shared/js/foogallery.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/fooplugins_foogallery/extensions/default-templates/shared/js/foogallery.js:\n ", "path": "downloaded_repos/fooplugins_foogallery/extensions/default-templates/shared/js/foogallery.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-open-redirect.express-open-redirect", "message": "Timeout when running javascript.express.security.audit.express-open-redirect.express-open-redirect on downloaded_repos/fooplugins_foogallery/freemius/assets/js/pricing/freemius-pricing.js:\n ", "path": "downloaded_repos/fooplugins_foogallery/freemius/assets/js/pricing/freemius-pricing.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/fooplugins_foogallery/freemius/assets/js/pricing/freemius-pricing.js:\n ", "path": "downloaded_repos/fooplugins_foogallery/freemius/assets/js/pricing/freemius-pricing.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/fooplugins_foogallery/freemius/assets/js/pricing/freemius-pricing.js:\n ", "path": "downloaded_repos/fooplugins_foogallery/freemius/assets/js/pricing/freemius-pricing.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-open-redirect.express-open-redirect", "message": "Timeout when running javascript.express.security.audit.express-open-redirect.express-open-redirect on downloaded_repos/fooplugins_foogallery/pro/extensions/default-templates/shared/js/foogallery.js:\n ", "path": "downloaded_repos/fooplugins_foogallery/pro/extensions/default-templates/shared/js/foogallery.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/fooplugins_foogallery/pro/extensions/default-templates/shared/js/foogallery.js:\n ", "path": "downloaded_repos/fooplugins_foogallery/pro/extensions/default-templates/shared/js/foogallery.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/fooplugins_foogallery/pro/extensions/default-templates/shared/js/foogallery.js:\n ", "path": "downloaded_repos/fooplugins_foogallery/pro/extensions/default-templates/shared/js/foogallery.js"}], "paths": {"scanned": ["downloaded_repos/fooplugins_foogallery/.gitignore", "downloaded_repos/fooplugins_foogallery/AGENTS.md", "downloaded_repos/fooplugins_foogallery/LICENSE.txt", "downloaded_repos/fooplugins_foogallery/README.md", "downloaded_repos/fooplugins_foogallery/README.txt", "downloaded_repos/fooplugins_foogallery/assets/foogallery.jpg", "downloaded_repos/fooplugins_foogallery/assets/image-placeholder.png", "downloaded_repos/fooplugins_foogallery/assets/logo.png", "downloaded_repos/fooplugins_foogallery/changelog.txt", "downloaded_repos/fooplugins_foogallery/composer.json", "downloaded_repos/fooplugins_foogallery/composer.lock", "downloaded_repos/fooplugins_foogallery/css/admin-foogallery-gallery-piles.css", "downloaded_repos/fooplugins_foogallery/css/admin-foogallery.css", "downloaded_repos/fooplugins_foogallery/css/admin-page-foogallery-extensions.css", "downloaded_repos/fooplugins_foogallery/css/admin-page-foogallery-help.css", "downloaded_repos/fooplugins_foogallery/css/admin-page-foogallery-settings.css", "downloaded_repos/fooplugins_foogallery/css/admin-tinymce.css", "downloaded_repos/fooplugins_foogallery/css/foogallery-foovideo-overrides.css", "downloaded_repos/fooplugins_foogallery/css/foogallery.admin.datasources.css", "downloaded_repos/fooplugins_foogallery/css/foogallery.admin.min.css", "downloaded_repos/fooplugins_foogallery/extensions/albums/admin/class-columns.php", "downloaded_repos/fooplugins_foogallery/extensions/albums/admin/class-metaboxes.php", "downloaded_repos/fooplugins_foogallery/extensions/albums/admin/index.php", "downloaded_repos/fooplugins_foogallery/extensions/albums/album-default.php", "downloaded_repos/fooplugins_foogallery/extensions/albums/album-stack.php", "downloaded_repos/fooplugins_foogallery/extensions/albums/class-albums-extension.php", "downloaded_repos/fooplugins_foogallery/extensions/albums/class-foogallery-album.php", "downloaded_repos/fooplugins_foogallery/extensions/albums/class-posttypes.php", "downloaded_repos/fooplugins_foogallery/extensions/albums/css/admin-foogallery-album.css", "downloaded_repos/fooplugins_foogallery/extensions/albums/css/album-default.css", "downloaded_repos/fooplugins_foogallery/extensions/albums/css/album-stack.css", "downloaded_repos/fooplugins_foogallery/extensions/albums/foogallery-albums.png", "downloaded_repos/fooplugins_foogallery/extensions/albums/functions.php", "downloaded_repos/fooplugins_foogallery/extensions/albums/js/admin-foogallery-album.js", "downloaded_repos/fooplugins_foogallery/extensions/albums/js/album-stack.js", "downloaded_repos/fooplugins_foogallery/extensions/albums/public/class-foogallery-album-template-loader.php", "downloaded_repos/fooplugins_foogallery/extensions/albums/public/class-rewrite-rules.php", "downloaded_repos/fooplugins_foogallery/extensions/albums/public/class-shortcodes.php", "downloaded_repos/fooplugins_foogallery/extensions/default-templates/carousel/class-carousel-gallery-template.php", "downloaded_repos/fooplugins_foogallery/extensions/default-templates/carousel/gallery-carousel.php", "downloaded_repos/fooplugins_foogallery/extensions/default-templates/class-default-templates-extension.php", "downloaded_repos/fooplugins_foogallery/extensions/default-templates/class-default-templates.php", "downloaded_repos/fooplugins_foogallery/extensions/default-templates/default/class-default-gallery-template.php", "downloaded_repos/fooplugins_foogallery/extensions/default-templates/default/gallery-default.php", "downloaded_repos/fooplugins_foogallery/extensions/default-templates/default/js/admin-gallery-default.js", "downloaded_repos/fooplugins_foogallery/extensions/default-templates/functions.php", "downloaded_repos/fooplugins_foogallery/extensions/default-templates/image-viewer/class-image-viewer-gallery-template.php", "downloaded_repos/fooplugins_foogallery/extensions/default-templates/image-viewer/gallery-image-viewer.php", "downloaded_repos/fooplugins_foogallery/extensions/default-templates/image-viewer/js/admin-gallery-image-viewer.js", "downloaded_repos/fooplugins_foogallery/extensions/default-templates/justified/class-justified-gallery-template.php", "downloaded_repos/fooplugins_foogallery/extensions/default-templates/justified/gallery-justified.php", "downloaded_repos/fooplugins_foogallery/extensions/default-templates/masonry/class-masonry-gallery-template.php", "downloaded_repos/fooplugins_foogallery/extensions/default-templates/masonry/gallery-masonry.php", "downloaded_repos/fooplugins_foogallery/extensions/default-templates/masonry/js/admin-gallery-masonry.js", "downloaded_repos/fooplugins_foogallery/extensions/default-templates/shared/css/admin-foogallery.css", "downloaded_repos/fooplugins_foogallery/extensions/default-templates/shared/css/foogallery.css", "downloaded_repos/fooplugins_foogallery/extensions/default-templates/shared/css/foogallery.min.css", "downloaded_repos/fooplugins_foogallery/extensions/default-templates/shared/img/icons.svg", "downloaded_repos/fooplugins_foogallery/extensions/default-templates/shared/js/foogallery.js", "downloaded_repos/fooplugins_foogallery/extensions/default-templates/shared/js/foogallery.polyfills.js", "downloaded_repos/fooplugins_foogallery/extensions/default-templates/simple-portfolio/class-simple-portfolio-gallery-template.php", "downloaded_repos/fooplugins_foogallery/extensions/default-templates/simple-portfolio/gallery-simple_portfolio.php", "downloaded_repos/fooplugins_foogallery/extensions/default-templates/thumbnail/class-thumbnail-gallery-template.php", "downloaded_repos/fooplugins_foogallery/extensions/default-templates/thumbnail/gallery-thumbnail.php", "downloaded_repos/fooplugins_foogallery/extensions/default-templates/thumbnail/js/admin-gallery-thumbnail.js", "downloaded_repos/fooplugins_foogallery/extensions/demo-content-generator/class-demo-content-generator.php", "downloaded_repos/fooplugins_foogallery/extensions/demo-content-generator/includes/class-lorem-ipsum.php", "downloaded_repos/fooplugins_foogallery/extensions/demo-content-generator/includes/class-pixabay.php", "downloaded_repos/fooplugins_foogallery/extensions/demo-content-generator/view-demo-content.php", "downloaded_repos/fooplugins_foogallery/extensions/import-export/class-foogallery-export-view-helper.php", "downloaded_repos/fooplugins_foogallery/extensions/import-export/class-foogallery-import-export-extension.php", "downloaded_repos/fooplugins_foogallery/extensions/import-export/class-foogallery-import-export.php", "downloaded_repos/fooplugins_foogallery/extensions/import-export/class-foogallery-import-view-helper.php", "downloaded_repos/fooplugins_foogallery/extensions/import-export/functions.php", "downloaded_repos/fooplugins_foogallery/extensions/import-export/view-import-export.php", "downloaded_repos/fooplugins_foogallery/foogallery.php", "downloaded_repos/fooplugins_foogallery/freemius/LICENSE.txt", "downloaded_repos/fooplugins_foogallery/freemius/README.md", "downloaded_repos/fooplugins_foogallery/freemius/assets/css/admin/account.css", "downloaded_repos/fooplugins_foogallery/freemius/assets/css/admin/add-ons.css", "downloaded_repos/fooplugins_foogallery/freemius/assets/css/admin/affiliation.css", "downloaded_repos/fooplugins_foogallery/freemius/assets/css/admin/checkout.css", "downloaded_repos/fooplugins_foogallery/freemius/assets/css/admin/clone-resolution.css", "downloaded_repos/fooplugins_foogallery/freemius/assets/css/admin/common.css", "downloaded_repos/fooplugins_foogallery/freemius/assets/css/admin/connect.css", "downloaded_repos/fooplugins_foogallery/freemius/assets/css/admin/debug.css", "downloaded_repos/fooplugins_foogallery/freemius/assets/css/admin/dialog-boxes.css", "downloaded_repos/fooplugins_foogallery/freemius/assets/css/admin/gdpr-optin-notice.css", "downloaded_repos/fooplugins_foogallery/freemius/assets/css/admin/index.php", "downloaded_repos/fooplugins_foogallery/freemius/assets/css/admin/optout.css", "downloaded_repos/fooplugins_foogallery/freemius/assets/css/admin/plugins.css", "downloaded_repos/fooplugins_foogallery/freemius/assets/css/customizer.css", "downloaded_repos/fooplugins_foogallery/freemius/assets/css/index.php", "downloaded_repos/fooplugins_foogallery/freemius/assets/img/index.php", "downloaded_repos/fooplugins_foogallery/freemius/assets/img/plugin-icon.png", "downloaded_repos/fooplugins_foogallery/freemius/assets/img/theme-icon.png", "downloaded_repos/fooplugins_foogallery/freemius/assets/index.php", "downloaded_repos/fooplugins_foogallery/freemius/assets/js/index.php", "downloaded_repos/fooplugins_foogallery/freemius/assets/js/jquery.form.js", "downloaded_repos/fooplugins_foogallery/freemius/assets/js/nojquery.ba-postmessage.js", "downloaded_repos/fooplugins_foogallery/freemius/assets/js/postmessage.js", "downloaded_repos/fooplugins_foogallery/freemius/assets/js/pricing/14fb1bd5b7c41648488b06147f50a0dc.svg", "downloaded_repos/fooplugins_foogallery/freemius/assets/js/pricing/178afa6030e76635dbe835e111d2c507.png", "downloaded_repos/fooplugins_foogallery/freemius/assets/js/pricing/27b5a722a5553d9de0170325267fccec.png", "downloaded_repos/fooplugins_foogallery/freemius/assets/js/pricing/4375c4a3ddc6f637c2ab9a2d7220f91e.png", "downloaded_repos/fooplugins_foogallery/freemius/assets/js/pricing/4529cac82a2d1f300d3c4702b7b5e8f3.svg", "downloaded_repos/fooplugins_foogallery/freemius/assets/js/pricing/5480ed23b199531a8cbc05924f26952b.png", "downloaded_repos/fooplugins_foogallery/freemius/assets/js/pricing/b4f3b958f4a019862d81b15f3f8eee3a.svg", "downloaded_repos/fooplugins_foogallery/freemius/assets/js/pricing/c03f665db27af43971565560adfba594.png", "downloaded_repos/fooplugins_foogallery/freemius/assets/js/pricing/cb5fc4f6ec7ada72e986f6e7dde365bf.png", "downloaded_repos/fooplugins_foogallery/freemius/assets/js/pricing/dd89563360f0272635c8f0ab7d7f1402.png", "downloaded_repos/fooplugins_foogallery/freemius/assets/js/pricing/e366d70661d8ad2493bd6afbd779f125.png", "downloaded_repos/fooplugins_foogallery/freemius/assets/js/pricing/f18006f6535a1a6e9c6bfbffafe6f18a.svg", "downloaded_repos/fooplugins_foogallery/freemius/assets/js/pricing/f3aac72a8e63997d6bb888f816457e9b.png", "downloaded_repos/fooplugins_foogallery/freemius/assets/js/pricing/f928f1be99776af83e8e6be4baf8ffe7.svg", "downloaded_repos/fooplugins_foogallery/freemius/assets/js/pricing/fde48e4609a6ddc11d639fc2421f2afd.png", "downloaded_repos/fooplugins_foogallery/freemius/assets/js/pricing/freemius-pricing.js", "downloaded_repos/fooplugins_foogallery/freemius/assets/js/pricing/freemius-pricing.js.LICENSE.txt", "downloaded_repos/fooplugins_foogallery/freemius/composer.json", "downloaded_repos/fooplugins_foogallery/freemius/config.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/class-freemius-abstract.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/class-fs-admin-notices.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/class-fs-api.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/class-fs-garbage-collector.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/class-fs-lock.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/class-fs-logger.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/class-fs-options.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/class-fs-plugin-updater.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/class-fs-security.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/class-fs-storage.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/class-fs-user-lock.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/customizer/class-fs-customizer-support-section.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/customizer/class-fs-customizer-upsell-control.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/customizer/index.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/debug/class-fs-debug-bar-panel.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/debug/debug-bar-start.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/debug/index.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/entities/class-fs-affiliate-terms.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/entities/class-fs-affiliate.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/entities/class-fs-billing.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/entities/class-fs-entity.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/entities/class-fs-payment.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/entities/class-fs-plugin-info.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/entities/class-fs-plugin-license.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/entities/class-fs-plugin-plan.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/entities/class-fs-plugin-tag.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/entities/class-fs-plugin.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/entities/class-fs-pricing.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/entities/class-fs-scope-entity.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/entities/class-fs-site.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/entities/class-fs-subscription.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/entities/class-fs-user.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/entities/index.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/fs-core-functions.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/fs-essential-functions.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/fs-html-escaping-functions.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/fs-plugin-info-dialog.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/index.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/l10n.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/managers/class-fs-admin-menu-manager.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/managers/class-fs-admin-notice-manager.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/managers/class-fs-cache-manager.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/managers/class-fs-checkout-manager.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/managers/class-fs-clone-manager.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/managers/class-fs-contact-form-manager.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/managers/class-fs-debug-manager.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/managers/class-fs-gdpr-manager.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/managers/class-fs-key-value-storage.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/managers/class-fs-license-manager.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/managers/class-fs-option-manager.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/managers/class-fs-permission-manager.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/managers/class-fs-plan-manager.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/managers/class-fs-plugin-manager.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/managers/index.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/sdk/Exceptions/ArgumentNotExistException.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/sdk/Exceptions/EmptyArgumentException.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/sdk/Exceptions/Exception.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/sdk/Exceptions/InvalidArgumentException.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/sdk/Exceptions/OAuthException.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/sdk/Exceptions/index.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/sdk/FreemiusBase.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/sdk/FreemiusWordPress.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/sdk/LICENSE.txt", "downloaded_repos/fooplugins_foogallery/freemius/includes/sdk/index.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/supplements/fs-essential-functions-1.1.7.1.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/supplements/fs-essential-functions-2.2.1.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/supplements/fs-migration-2.5.1.php", "downloaded_repos/fooplugins_foogallery/freemius/includes/supplements/index.php", "downloaded_repos/fooplugins_foogallery/freemius/index.php", "downloaded_repos/fooplugins_foogallery/freemius/languages/freemius-cs_CZ.mo", "downloaded_repos/fooplugins_foogallery/freemius/languages/freemius-da_DK.mo", "downloaded_repos/fooplugins_foogallery/freemius/languages/freemius-de_DE.mo", "downloaded_repos/fooplugins_foogallery/freemius/languages/freemius-es_ES.mo", "downloaded_repos/fooplugins_foogallery/freemius/languages/freemius-fr_FR.mo", "downloaded_repos/fooplugins_foogallery/freemius/languages/freemius-he_IL.mo", "downloaded_repos/fooplugins_foogallery/freemius/languages/freemius-hu_HU.mo", "downloaded_repos/fooplugins_foogallery/freemius/languages/freemius-it_IT.mo", "downloaded_repos/fooplugins_foogallery/freemius/languages/freemius-ja.mo", "downloaded_repos/fooplugins_foogallery/freemius/languages/freemius-nl_NL.mo", "downloaded_repos/fooplugins_foogallery/freemius/languages/freemius-ru_RU.mo", "downloaded_repos/fooplugins_foogallery/freemius/languages/freemius-ta.mo", "downloaded_repos/fooplugins_foogallery/freemius/languages/freemius-zh_CN.mo", "downloaded_repos/fooplugins_foogallery/freemius/languages/freemius.pot", "downloaded_repos/fooplugins_foogallery/freemius/languages/index.php", "downloaded_repos/fooplugins_foogallery/freemius/require.php", "downloaded_repos/fooplugins_foogallery/freemius/start.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/account/billing.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/account/index.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/account/partials/activate-license-button.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/account/partials/addon.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/account/partials/deactivate-license-button.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/account/partials/disconnect-button.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/account/partials/index.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/account/partials/site.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/account/payments.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/account.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/add-ons.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/add-trial-to-pricing.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/admin-notice.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/ajax-loader.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/api-connectivity-message-js.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/auto-installation.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/checkout/frame.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/checkout/process-redirect.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/checkout/redirect.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/checkout.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/clone-resolution-js.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/connect/index.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/connect/permission.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/connect/permissions-group.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/connect.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/contact.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/debug/api-calls.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/debug/index.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/debug/logger.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/debug/plugins-themes-sync.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/debug/scheduled-crons.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/debug.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/email.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/forms/affiliation.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/forms/data-debug-mode.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/forms/deactivation/contact.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/forms/deactivation/form.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/forms/deactivation/index.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/forms/deactivation/retry-skip.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/forms/email-address-update.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/forms/index.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/forms/license-activation.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/forms/optout.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/forms/premium-versions-upgrade-handler.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/forms/premium-versions-upgrade-metadata.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/forms/resend-key.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/forms/subscription-cancellation.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/forms/trial-start.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/forms/user-change.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/gdpr-optin-js.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/index.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/js/index.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/js/jquery.content-change.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/js/open-license-activation.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/js/permissions.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/js/style-premium-theme.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/partials/index.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/partials/network-activation.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/plugin-icon.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/plugin-info/description.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/plugin-info/features.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/plugin-info/index.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/plugin-info/screenshots.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/pricing.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/secure-https-header.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/sticky-admin-notice-js.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/tabs-capture-js.php", "downloaded_repos/fooplugins_foogallery/freemius/templates/tabs.php", "downloaded_repos/fooplugins_foogallery/gulpfile.js", "downloaded_repos/fooplugins_foogallery/gutenberg/assets/blocks.asset.php", "downloaded_repos/fooplugins_foogallery/gutenberg/assets/blocks.css", "downloaded_repos/fooplugins_foogallery/gutenberg/assets/blocks.js", "downloaded_repos/fooplugins_foogallery/gutenberg/class-foogallery-blocks.php", "downloaded_repos/fooplugins_foogallery/gutenberg/class-foogallery-gutenberg.php", "downloaded_repos/fooplugins_foogallery/gutenberg/class-foogallery-rest-routes.php", "downloaded_repos/fooplugins_foogallery/gutenberg/config/blocks.js", "downloaded_repos/fooplugins_foogallery/gutenberg/config/wp-scripts.helper.js", "downloaded_repos/fooplugins_foogallery/gutenberg/src/block/edit/components/block-controls.js", "downloaded_repos/fooplugins_foogallery/gutenberg/src/block/edit/components/index.js", "downloaded_repos/fooplugins_foogallery/gutenberg/src/block/edit/components/inspector-controls/editor.scss", "downloaded_repos/fooplugins_foogallery/gutenberg/src/block/edit/components/inspector-controls/index.js", "downloaded_repos/fooplugins_foogallery/gutenberg/src/block/edit/components/modal/editor.scss", "downloaded_repos/fooplugins_foogallery/gutenberg/src/block/edit/components/modal/index.js", "downloaded_repos/fooplugins_foogallery/gutenberg/src/block/edit/components/modal/item.js", "downloaded_repos/fooplugins_foogallery/gutenberg/src/block/edit/components/placeholder.js", "downloaded_repos/fooplugins_foogallery/gutenberg/src/block/edit/components/server-side-render.js", "downloaded_repos/fooplugins_foogallery/gutenberg/src/block/edit/index.js", "downloaded_repos/fooplugins_foogallery/gutenberg/src/block/edit/rendered.js", "downloaded_repos/fooplugins_foogallery/gutenberg/src/block/edit/views/duplicate.js", "downloaded_repos/fooplugins_foogallery/gutenberg/src/block/edit/views/empty.js", "downloaded_repos/fooplugins_foogallery/gutenberg/src/block/edit/views/index.js", "downloaded_repos/fooplugins_foogallery/gutenberg/src/block/edit/views/populated.js", "downloaded_repos/fooplugins_foogallery/gutenberg/src/block/editor.scss", "downloaded_repos/fooplugins_foogallery/gutenberg/src/block/index.js", "downloaded_repos/fooplugins_foogallery/gutenberg/src/index.js", "downloaded_repos/fooplugins_foogallery/gutenberg/src/readme.md", "downloaded_repos/fooplugins_foogallery/includes/admin/class-admin-notices.php", "downloaded_repos/fooplugins_foogallery/includes/admin/class-admin.php", "downloaded_repos/fooplugins_foogallery/includes/admin/class-attachment-fields.php", "downloaded_repos/fooplugins_foogallery/includes/admin/class-columns.php", "downloaded_repos/fooplugins_foogallery/includes/admin/class-demo-content.php", "downloaded_repos/fooplugins_foogallery/includes/admin/class-extensions.php", "downloaded_repos/fooplugins_foogallery/includes/admin/class-gallery-attachment-modal.php", "downloaded_repos/fooplugins_foogallery/includes/admin/class-gallery-datasources.php", "downloaded_repos/fooplugins_foogallery/includes/admin/class-gallery-editor.php", "downloaded_repos/fooplugins_foogallery/includes/admin/class-gallery-metabox-fields.php", "downloaded_repos/fooplugins_foogallery/includes/admin/class-gallery-metabox-items.php", "downloaded_repos/fooplugins_foogallery/includes/admin/class-gallery-metabox-settings-helper.php", "downloaded_repos/fooplugins_foogallery/includes/admin/class-gallery-metabox-settings.php", "downloaded_repos/fooplugins_foogallery/includes/admin/class-gallery-metaboxes.php", "downloaded_repos/fooplugins_foogallery/includes/admin/class-menu.php", "downloaded_repos/fooplugins_foogallery/includes/admin/class-pro-promotion.php", "downloaded_repos/fooplugins_foogallery/includes/admin/class-settings.php", "downloaded_repos/fooplugins_foogallery/includes/admin/class-silent-installer-skin.php", "downloaded_repos/fooplugins_foogallery/includes/admin/demo-content-galleries.php", "downloaded_repos/fooplugins_foogallery/includes/admin/demo-content-images.php", "downloaded_repos/fooplugins_foogallery/includes/admin/index.php", "downloaded_repos/fooplugins_foogallery/includes/admin/pro-features.php", "downloaded_repos/fooplugins_foogallery/includes/admin/view-features.php", "downloaded_repos/fooplugins_foogallery/includes/admin/view-help-demos.php", "downloaded_repos/fooplugins_foogallery/includes/admin/view-help-getting-started.php", "downloaded_repos/fooplugins_foogallery/includes/admin/view-help-pro.php", "downloaded_repos/fooplugins_foogallery/includes/admin/view-help.php", "downloaded_repos/fooplugins_foogallery/includes/admin/view-system-info.php", "downloaded_repos/fooplugins_foogallery/includes/class-attachment-filters.php", "downloaded_repos/fooplugins_foogallery/includes/class-foogallery-animated-gif-support.php", "downloaded_repos/fooplugins_foogallery/includes/class-foogallery-attachment-custom-class.php", "downloaded_repos/fooplugins_foogallery/includes/class-foogallery-attachment-type.php", "downloaded_repos/fooplugins_foogallery/includes/class-foogallery-attachment.php", "downloaded_repos/fooplugins_foogallery/includes/class-foogallery-cache.php", "downloaded_repos/fooplugins_foogallery/includes/class-foogallery-common-fields.php", "downloaded_repos/fooplugins_foogallery/includes/class-foogallery-crop-position.php", "downloaded_repos/fooplugins_foogallery/includes/class-foogallery-datasource-media_library.php", "downloaded_repos/fooplugins_foogallery/includes/class-foogallery-debug.php", "downloaded_repos/fooplugins_foogallery/includes/class-foogallery-extensions-compatibility.php", "downloaded_repos/fooplugins_foogallery/includes/class-foogallery-force-https.php", "downloaded_repos/fooplugins_foogallery/includes/class-foogallery-lazyload.php", "downloaded_repos/fooplugins_foogallery/includes/class-foogallery-lightbox.php", "downloaded_repos/fooplugins_foogallery/includes/class-foogallery-paging.php", "downloaded_repos/fooplugins_foogallery/includes/class-foogallery-sitemaps.php", "downloaded_repos/fooplugins_foogallery/includes/class-foogallery-widget.php", "downloaded_repos/fooplugins_foogallery/includes/class-foogallery.php", "downloaded_repos/fooplugins_foogallery/includes/class-gallery-advanced-settings.php", "downloaded_repos/fooplugins_foogallery/includes/class-il8n.php", "downloaded_repos/fooplugins_foogallery/includes/class-override-thumbnail.php", "downloaded_repos/fooplugins_foogallery/includes/class-posttypes.php", "downloaded_repos/fooplugins_foogallery/includes/class-retina.php", "downloaded_repos/fooplugins_foogallery/includes/class-thumbnail-dimensions.php", "downloaded_repos/fooplugins_foogallery/includes/class-thumbnails.php", "downloaded_repos/fooplugins_foogallery/includes/class-version-check.php", "downloaded_repos/fooplugins_foogallery/includes/compatibility/class-autoptimize-compatibility.php", "downloaded_repos/fooplugins_foogallery/includes/compatibility/class-elasticpress-compatibility.php", "downloaded_repos/fooplugins_foogallery/includes/compatibility/class-elementor-compatibility.php", "downloaded_repos/fooplugins_foogallery/includes/compatibility/class-foobox-compatibility.php", "downloaded_repos/fooplugins_foogallery/includes/compatibility/class-foogallery-compatibility.php", "downloaded_repos/fooplugins_foogallery/includes/compatibility/class-foovideo-compatibility.php", "downloaded_repos/fooplugins_foogallery/includes/compatibility/class-jetpack-compatibility.php", "downloaded_repos/fooplugins_foogallery/includes/compatibility/class-polylang-compatibility.php", "downloaded_repos/fooplugins_foogallery/includes/compatibility/class-responsive-lightbox-dfactory-compatibility.php", "downloaded_repos/fooplugins_foogallery/includes/compatibility/class-wpoptimize-compatibility.php", "downloaded_repos/fooplugins_foogallery/includes/compatibility/class-wprocket-compatibility.php", "downloaded_repos/fooplugins_foogallery/includes/compatibility/elementor/class-elementor-foogallery-widget.php", "downloaded_repos/fooplugins_foogallery/includes/compatibility/view-foovideo-offer.php", "downloaded_repos/fooplugins_foogallery/includes/constants.php", "downloaded_repos/fooplugins_foogallery/includes/extensions/class-extension.php", "downloaded_repos/fooplugins_foogallery/includes/extensions/class-extensions-api.php", "downloaded_repos/fooplugins_foogallery/includes/extensions/class-extensions-loader.php", "downloaded_repos/fooplugins_foogallery/includes/foopluginbase/.gitignore", "downloaded_repos/fooplugins_foogallery/includes/foopluginbase/README.md", "downloaded_repos/fooplugins_foogallery/includes/foopluginbase/bootstrapper.php", "downloaded_repos/fooplugins_foogallery/includes/foopluginbase/changelog.txt", "downloaded_repos/fooplugins_foogallery/includes/foopluginbase/classes/class-foo-friendly-dates.php", "downloaded_repos/fooplugins_foogallery/includes/foopluginbase/classes/class-foo-plugin-base.php", "downloaded_repos/fooplugins_foogallery/includes/foopluginbase/classes/class-foo-plugin-file-locator.php", "downloaded_repos/fooplugins_foogallery/includes/foopluginbase/classes/class-foo-plugin-metabox-sanity.php", "downloaded_repos/fooplugins_foogallery/includes/foopluginbase/classes/class-foo-plugin-options.php", "downloaded_repos/fooplugins_foogallery/includes/foopluginbase/classes/class-foo-plugin-settings.php", "downloaded_repos/fooplugins_foogallery/includes/foopluginbase/classes/class-foo-plugin-textdomain.php", "downloaded_repos/fooplugins_foogallery/includes/foopluginbase/classes/index.php", "downloaded_repos/fooplugins_foogallery/includes/foopluginbase/css/admin-settings.css", "downloaded_repos/fooplugins_foogallery/includes/foopluginbase/css/index.php", "downloaded_repos/fooplugins_foogallery/includes/foopluginbase/functions/arrays.php", "downloaded_repos/fooplugins_foogallery/includes/foopluginbase/functions/dates.php", "downloaded_repos/fooplugins_foogallery/includes/foopluginbase/functions/general.php", "downloaded_repos/fooplugins_foogallery/includes/foopluginbase/functions/index.php", "downloaded_repos/fooplugins_foogallery/includes/foopluginbase/functions/screen.php", "downloaded_repos/fooplugins_foogallery/includes/foopluginbase/functions/strings.php", "downloaded_repos/fooplugins_foogallery/includes/foopluginbase/index.php", "downloaded_repos/fooplugins_foogallery/includes/foopluginbase/js/admin-settings.js", "downloaded_repos/fooplugins_foogallery/includes/foopluginbase/js/index.php", "downloaded_repos/fooplugins_foogallery/includes/foopluginbase/views/index.php", "downloaded_repos/fooplugins_foogallery/includes/foopluginbase/views/settings.php", "downloaded_repos/fooplugins_foogallery/includes/functions.php", "downloaded_repos/fooplugins_foogallery/includes/includes.php", "downloaded_repos/fooplugins_foogallery/includes/index.php", "downloaded_repos/fooplugins_foogallery/includes/public/class-admin-bar.php", "downloaded_repos/fooplugins_foogallery/includes/public/class-aioseo-sitemaps.php", "downloaded_repos/fooplugins_foogallery/includes/public/class-css-load-optimizer.php", "downloaded_repos/fooplugins_foogallery/includes/public/class-foogallery-template-loader.php", "downloaded_repos/fooplugins_foogallery/includes/public/class-public.php", "downloaded_repos/fooplugins_foogallery/includes/public/class-rank-math-seo-sitemaps.php", "downloaded_repos/fooplugins_foogallery/includes/public/class-shortcodes.php", "downloaded_repos/fooplugins_foogallery/includes/public/class-yoast-seo-sitemaps.php", "downloaded_repos/fooplugins_foogallery/includes/public/index.php", "downloaded_repos/fooplugins_foogallery/includes/render-functions.php", "downloaded_repos/fooplugins_foogallery/includes/thumbs/class-foogallery-thumb-engine.php", "downloaded_repos/fooplugins_foogallery/includes/thumbs/class-foogallery-thumb-image-editor-gd.php", "downloaded_repos/fooplugins_foogallery/includes/thumbs/class-foogallery-thumb-image-editor-imagick.php", "downloaded_repos/fooplugins_foogallery/includes/thumbs/class-foogallery-thumb-manager.php", "downloaded_repos/fooplugins_foogallery/includes/thumbs/default/class-foogallery-thumb-engine-default.php", "downloaded_repos/fooplugins_foogallery/includes/thumbs/default/class-foogallery-thumb-generator-background-fill.php", "downloaded_repos/fooplugins_foogallery/includes/thumbs/default/class-foogallery-thumb-generator.php", "downloaded_repos/fooplugins_foogallery/includes/thumbs/dummy/class-foogallery-thumb-engine-dummy.php", "downloaded_repos/fooplugins_foogallery/includes/thumbs/includes.php", "downloaded_repos/fooplugins_foogallery/includes/thumbs/shortpixel/class-foogallery-thumb-engine-shortpixel.php", "downloaded_repos/fooplugins_foogallery/index.html", "downloaded_repos/fooplugins_foogallery/js/admin-foogallery-attachment-autosave.js", "downloaded_repos/fooplugins_foogallery/js/admin-foogallery-edit.js", "downloaded_repos/fooplugins_foogallery/js/admin-foogallery-editor.js", "downloaded_repos/fooplugins_foogallery/js/admin-foogallery-elementor-preview.js", "downloaded_repos/fooplugins_foogallery/js/admin-page-foogallery-extensions-modal.js", "downloaded_repos/fooplugins_foogallery/js/admin-page-foogallery-extensions.js", "downloaded_repos/fooplugins_foogallery/js/admin-page-foogallery-settings.js", "downloaded_repos/fooplugins_foogallery/js/admin-tinymce.js", "downloaded_repos/fooplugins_foogallery/js/admin-uploader.js", "downloaded_repos/fooplugins_foogallery/js/foogallery.admin.datasources.js", "downloaded_repos/fooplugins_foogallery/languages/foogallery.mo", "downloaded_repos/fooplugins_foogallery/languages/foogallery.pot", "downloaded_repos/fooplugins_foogallery/languages/index.php", "downloaded_repos/fooplugins_foogallery/lib/selectize/foogallery.selectize.js", "downloaded_repos/fooplugins_foogallery/lib/selectize/selectize.css", "downloaded_repos/fooplugins_foogallery/lib/spectrum/spectrum.css", "downloaded_repos/fooplugins_foogallery/lib/spectrum/spectrum.js", "downloaded_repos/fooplugins_foogallery/package-lock.json", "downloaded_repos/fooplugins_foogallery/package.json", "downloaded_repos/fooplugins_foogallery/pro/assets/chrome.png", "downloaded_repos/fooplugins_foogallery/pro/assets/desktop.png", "downloaded_repos/fooplugins_foogallery/pro/assets/edge.png", "downloaded_repos/fooplugins_foogallery/pro/assets/firefox.png", "downloaded_repos/fooplugins_foogallery/pro/assets/ie_9-11.png", "downloaded_repos/fooplugins_foogallery/pro/assets/mobile.png", "downloaded_repos/fooplugins_foogallery/pro/assets/opera.png", "downloaded_repos/fooplugins_foogallery/pro/assets/partial.png", "downloaded_repos/fooplugins_foogallery/pro/assets/safari-ios.png", "downloaded_repos/fooplugins_foogallery/pro/assets/safari.png", "downloaded_repos/fooplugins_foogallery/pro/assets/samsung-internet.png", "downloaded_repos/fooplugins_foogallery/pro/assets/spinner.gif", "downloaded_repos/fooplugins_foogallery/pro/assets/uc.png", "downloaded_repos/fooplugins_foogallery/pro/assets/uploader-icons.png", "downloaded_repos/fooplugins_foogallery/pro/assets/video-thumbnail.png", "downloaded_repos/fooplugins_foogallery/pro/css/foogallery.admin.bulk.management.css", "downloaded_repos/fooplugins_foogallery/pro/css/foogallery.admin.datasources.folders.css", "downloaded_repos/fooplugins_foogallery/pro/css/foogallery.admin.datasources.taxonomy.css", "downloaded_repos/fooplugins_foogallery/pro/css/foogallery.admin.datasources.woocommerce.css", "downloaded_repos/fooplugins_foogallery/pro/css/foogallery.admin.filtering.css", "downloaded_repos/fooplugins_foogallery/pro/css/foogallery.admin.woocommerce.css", "downloaded_repos/fooplugins_foogallery/pro/css/foogallery.media-views.min.css", "downloaded_repos/fooplugins_foogallery/pro/extensions/default-templates/class-foogallery-pro-default-templates.php", "downloaded_repos/fooplugins_foogallery/pro/extensions/default-templates/foogrid/class-foogrid-gallery-template.php", "downloaded_repos/fooplugins_foogallery/pro/extensions/default-templates/foogrid/gallery-foogridpro.php", "downloaded_repos/fooplugins_foogallery/pro/extensions/default-templates/polaroid/class-polaroid-gallery-template.php", "downloaded_repos/fooplugins_foogallery/pro/extensions/default-templates/polaroid/gallery-polaroid_new.php", "downloaded_repos/fooplugins_foogallery/pro/extensions/default-templates/product/class-product-gallery-template.php", "downloaded_repos/fooplugins_foogallery/pro/extensions/default-templates/product/gallery-product.php", "downloaded_repos/fooplugins_foogallery/pro/extensions/default-templates/shared/css/foogallery.css", "downloaded_repos/fooplugins_foogallery/pro/extensions/default-templates/shared/css/foogallery.min.css", "downloaded_repos/fooplugins_foogallery/pro/extensions/default-templates/shared/img/icons.svg", "downloaded_repos/fooplugins_foogallery/pro/extensions/default-templates/shared/js/foogallery.js", "downloaded_repos/fooplugins_foogallery/pro/extensions/default-templates/slider/assets/video-layout-horizontal.png", "downloaded_repos/fooplugins_foogallery/pro/extensions/default-templates/slider/assets/video-layout-vertical.png", "downloaded_repos/fooplugins_foogallery/pro/extensions/default-templates/slider/class-slider-gallery-template.php", "downloaded_repos/fooplugins_foogallery/pro/extensions/default-templates/slider/gallery-slider.php", "downloaded_repos/fooplugins_foogallery/pro/extensions/whitelabelling/foogallery-whitelabelling-extension.php", "downloaded_repos/fooplugins_foogallery/pro/foogallery-pro.php", "downloaded_repos/fooplugins_foogallery/pro/functions.php", "downloaded_repos/fooplugins_foogallery/pro/includes/class-foogallery-pro-advanced-captions.php", "downloaded_repos/fooplugins_foogallery/pro/includes/class-foogallery-pro-advanced-gallery-settings.php", "downloaded_repos/fooplugins_foogallery/pro/includes/class-foogallery-pro-advanced-thumbnails.php", "downloaded_repos/fooplugins_foogallery/pro/includes/class-foogallery-pro-attachment-taxonomies.php", "downloaded_repos/fooplugins_foogallery/pro/includes/class-foogallery-pro-bulk-copy.php", "downloaded_repos/fooplugins_foogallery/pro/includes/class-foogallery-pro-bulk-management.php", "downloaded_repos/fooplugins_foogallery/pro/includes/class-foogallery-pro-buttons.php", "downloaded_repos/fooplugins_foogallery/pro/includes/class-foogallery-pro-datasource-folders.php", "downloaded_repos/fooplugins_foogallery/pro/includes/class-foogallery-pro-datasource-lightroom.php", "downloaded_repos/fooplugins_foogallery/pro/includes/class-foogallery-pro-datasource-mediacategories.php", "downloaded_repos/fooplugins_foogallery/pro/includes/class-foogallery-pro-datasource-mediatags.php", "downloaded_repos/fooplugins_foogallery/pro/includes/class-foogallery-pro-datasource-post-query.php", "downloaded_repos/fooplugins_foogallery/pro/includes/class-foogallery-pro-datasource-realmedialibrary.php", "downloaded_repos/fooplugins_foogallery/pro/includes/class-foogallery-pro-datasource-taxonomy-base.php", "downloaded_repos/fooplugins_foogallery/pro/includes/class-foogallery-pro-exif.php", "downloaded_repos/fooplugins_foogallery/pro/includes/class-foogallery-pro-filtering.php", "downloaded_repos/fooplugins_foogallery/pro/includes/class-foogallery-pro-gallery-blueprints.php", "downloaded_repos/fooplugins_foogallery/pro/includes/class-foogallery-pro-gallery-override.php", "downloaded_repos/fooplugins_foogallery/pro/includes/class-foogallery-pro-instagram-filters.php", "downloaded_repos/fooplugins_foogallery/pro/includes/class-foogallery-pro-instagram-helper.php", "downloaded_repos/fooplugins_foogallery/pro/includes/class-foogallery-pro-paging.php", "downloaded_repos/fooplugins_foogallery/pro/includes/class-foogallery-pro-presets.php", "downloaded_repos/fooplugins_foogallery/pro/includes/class-foogallery-pro-ribbons.php", "downloaded_repos/fooplugins_foogallery/pro/includes/class-foogallery-pro-whitelabelling.php", "downloaded_repos/fooplugins_foogallery/pro/includes/class-foogallery-pro-wplr-support.php", "downloaded_repos/fooplugins_foogallery/pro/includes/foogallery-media-views-templates.php", "downloaded_repos/fooplugins_foogallery/pro/includes/protection/class-foogallery-image-editor-helper-base.php", "downloaded_repos/fooplugins_foogallery/pro/includes/protection/class-foogallery-image-editor-helper-gd.php", "downloaded_repos/fooplugins_foogallery/pro/includes/protection/class-foogallery-image-editor-helper-imagick.php", "downloaded_repos/fooplugins_foogallery/pro/includes/protection/class-foogallery-pro-protection.php", "downloaded_repos/fooplugins_foogallery/pro/includes/protection/class-foogallery-watermark.php", "downloaded_repos/fooplugins_foogallery/pro/includes/protection/watermarks/watermark-aperture.png", "downloaded_repos/fooplugins_foogallery/pro/includes/protection/watermarks/watermark-camera.png", "downloaded_repos/fooplugins_foogallery/pro/includes/protection/watermarks/watermark-copy.png", "downloaded_repos/fooplugins_foogallery/pro/includes/protection/watermarks/watermark-copyright.png", "downloaded_repos/fooplugins_foogallery/pro/includes/protection/watermarks/watermark-image.png", "downloaded_repos/fooplugins_foogallery/pro/includes/protection/watermarks/watermark.svg", "downloaded_repos/fooplugins_foogallery/pro/includes/video/class-foogallery-pro-video-base.php", "downloaded_repos/fooplugins_foogallery/pro/includes/video/class-foogallery-pro-video-import.php", "downloaded_repos/fooplugins_foogallery/pro/includes/video/class-foogallery-pro-video-legacy.php", "downloaded_repos/fooplugins_foogallery/pro/includes/video/class-foogallery-pro-video-migration-helper.php", "downloaded_repos/fooplugins_foogallery/pro/includes/video/class-foogallery-pro-video-oembed.php", "downloaded_repos/fooplugins_foogallery/pro/includes/video/class-foogallery-pro-video-query.php", "downloaded_repos/fooplugins_foogallery/pro/includes/video/class-foogallery-pro-video-self-hosted.php", "downloaded_repos/fooplugins_foogallery/pro/includes/video/class-foogallery-pro-video-vimeo.php", "downloaded_repos/fooplugins_foogallery/pro/includes/video/class-foogallery-pro-video-youtube.php", "downloaded_repos/fooplugins_foogallery/pro/includes/video/class-foogallery-pro-video.php", "downloaded_repos/fooplugins_foogallery/pro/includes/video/functions.php", "downloaded_repos/fooplugins_foogallery/pro/includes/video/view-video-migration.php", "downloaded_repos/fooplugins_foogallery/pro/includes/woocommerce/class-foogallery-pro-datasource-products.php", "downloaded_repos/fooplugins_foogallery/pro/includes/woocommerce/class-foogallery-pro-woocommerce-base.php", "downloaded_repos/fooplugins_foogallery/pro/includes/woocommerce/class-foogallery-pro-woocommerce-downloads.php", "downloaded_repos/fooplugins_foogallery/pro/includes/woocommerce/class-foogallery-pro-woocommerce-master-product.php", "downloaded_repos/fooplugins_foogallery/pro/includes/woocommerce/class-foogallery-pro-woocommerce.php", "downloaded_repos/fooplugins_foogallery/pro/js/foogallery.admin.bulk.management.js", "downloaded_repos/fooplugins_foogallery/pro/js/foogallery.admin.datasources.folders.js", "downloaded_repos/fooplugins_foogallery/pro/js/foogallery.admin.datasources.instagram.js", "downloaded_repos/fooplugins_foogallery/pro/js/foogallery.admin.datasources.post.query.js", "downloaded_repos/fooplugins_foogallery/pro/js/foogallery.admin.datasources.taxonomy.js", "downloaded_repos/fooplugins_foogallery/pro/js/foogallery.admin.datasources.woocommerce.js", "downloaded_repos/fooplugins_foogallery/pro/js/foogallery.admin.filtering.js", "downloaded_repos/fooplugins_foogallery/pro/js/foogallery.admin.woocommerce.js", "downloaded_repos/fooplugins_foogallery/pro/js/foogallery.media-views.js", "downloaded_repos/fooplugins_foogallery/templates/index.php", "downloaded_repos/fooplugins_foogallery/templates/where-are-the-templates.php", "downloaded_repos/fooplugins_foogallery/views/index.php", "downloaded_repos/fooplugins_foogallery/wpml-config.xml"], "skipped": [{"path": "downloaded_repos/fooplugins_foogallery/extensions/default-templates/shared/js/foogallery.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fooplugins_foogallery/extensions/default-templates/shared/js/foogallery.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fooplugins_foogallery/extensions/default-templates/shared/js/foogallery.polyfills.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fooplugins_foogallery/freemius/assets/js/pricing/freemius-pricing.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fooplugins_foogallery/freemius/includes/class-freemius.php", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/fooplugins_foogallery/includes/foopluginbase/tests/class-foo-friendly-dates-tests.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fooplugins_foogallery/includes/thumbs/default/tests/test1.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fooplugins_foogallery/includes/thumbs/default/tests/test2.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fooplugins_foogallery/includes/thumbs/default/tests/test3", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fooplugins_foogallery/includes/thumbs/default/tests/test4.gif", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fooplugins_foogallery/includes/thumbs/default/tests/test5.jpg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fooplugins_foogallery/includes/thumbs/default/tests/test6.bmp", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fooplugins_foogallery/js/foogallery.admin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fooplugins_foogallery/lib/selectize/selectize.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fooplugins_foogallery/pro/extensions/default-templates/shared/js/foogallery.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/fooplugins_foogallery/pro/extensions/default-templates/shared/js/foogallery.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fooplugins_foogallery/pro/js/foobox.video.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/fooplugins_foogallery/pro/js/foogallery.media-views.min.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.839857816696167, "profiling_times": {"config_time": 7.083049535751343, "core_time": 12.180246114730835, "ignores_time": 0.0019693374633789062, "total_time": 19.267027854919434}, "parsing_time": {"total_time": 13.023326635360718, "per_file_time": {"mean": 0.030860963590902193, "std_dev": 0.011013368281724218}, "very_slow_stats": {"time_ratio": 0.3564279161803325, "count_ratio": 0.014218009478672985}, "very_slow_files": [{"fpath": "downloaded_repos/fooplugins_foogallery/extensions/albums/js/album-stack.js", "ftime": 0.3520021438598633}, {"fpath": "downloaded_repos/fooplugins_foogallery/extensions/default-templates/shared/js/foogallery.polyfills.js", "ftime": 0.44913506507873535}, {"fpath": "downloaded_repos/fooplugins_foogallery/js/admin-foogallery-edit.js", "ftime": 0.5868370532989502}, {"fpath": "downloaded_repos/fooplugins_foogallery/pro/js/foogallery.media-views.js", "ftime": 0.7618789672851562}, {"fpath": "downloaded_repos/fooplugins_foogallery/package-lock.json", "ftime": 1.0423109531402588}, {"fpath": "downloaded_repos/fooplugins_foogallery/lib/spectrum/spectrum.js", "ftime": 1.4497129917144775}]}, "scanning_time": {"total_time": 110.11075782775879, "per_file_time": {"mean": 0.07234609581324487, "std_dev": 0.20020809740118614}, "very_slow_stats": {"time_ratio": 0.6317260230942628, "count_ratio": 0.013797634691195795}, "very_slow_files": [{"fpath": "downloaded_repos/fooplugins_foogallery/extensions/default-templates/shared/js/foogallery.js", "ftime": 2.459043025970459}, {"fpath": "downloaded_repos/fooplugins_foogallery/freemius/includes/fs-plugin-info-dialog.php", "ftime": 3.4628090858459473}, {"fpath": "downloaded_repos/fooplugins_foogallery/pro/includes/woocommerce/class-foogallery-pro-woocommerce.php", "ftime": 3.5600697994232178}, {"fpath": "downloaded_repos/fooplugins_foogallery/js/admin-foogallery-edit.js", "ftime": 4.236367225646973}, {"fpath": "downloaded_repos/fooplugins_foogallery/extensions/default-templates/shared/js/foogallery.polyfills.js", "ftime": 4.294574975967407}, {"fpath": "downloaded_repos/fooplugins_foogallery/freemius/assets/js/pricing/freemius-pricing.js", "ftime": 5.232148885726929}, {"fpath": "downloaded_repos/fooplugins_foogallery/extensions/default-templates/shared/js/foogallery.js", "ftime": 5.366945028305054}, {"fpath": "downloaded_repos/fooplugins_foogallery/pro/extensions/default-templates/shared/js/foogallery.js", "ftime": 5.381925821304321}, {"fpath": "downloaded_repos/fooplugins_foogallery/lib/spectrum/spectrum.js", "ftime": 5.983958005905151}, {"fpath": "downloaded_repos/fooplugins_foogallery/pro/js/foogallery.media-views.js", "ftime": 7.823292970657349}]}, "matching_time": {"total_time": 38.577452421188354, "per_file_and_rule_time": {"mean": 0.036053693851577924, "std_dev": 0.012789504392594197}, "very_slow_stats": {"time_ratio": 0.6662423899479896, "count_ratio": 0.08411214953271028}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/fooplugins_foogallery/freemius/includes/fs-plugin-info-dialog.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.6133501529693604}, {"fpath": "downloaded_repos/fooplugins_foogallery/includes/functions.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.6730899810791016}, {"fpath": "downloaded_repos/fooplugins_foogallery/extensions/default-templates/shared/js/foogallery.polyfills.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.6765360832214355}, {"fpath": "downloaded_repos/fooplugins_foogallery/pro/js/foogallery.media-views.js", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 0.6910920143127441}, {"fpath": "downloaded_repos/fooplugins_foogallery/pro/includes/woocommerce/class-foogallery-pro-woocommerce.php", "rule_id": "php.lang.security.injection.tainted-callable.tainted-callable", "time": 0.7541520595550537}, {"fpath": "downloaded_repos/fooplugins_foogallery/lib/spectrum/spectrum.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.7737271785736084}, {"fpath": "downloaded_repos/fooplugins_foogallery/pro/includes/woocommerce/class-foogallery-pro-woocommerce.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.7757821083068848}, {"fpath": "downloaded_repos/fooplugins_foogallery/freemius/includes/managers/class-fs-clone-manager.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.8339118957519531}, {"fpath": "downloaded_repos/fooplugins_foogallery/lib/spectrum/spectrum.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.885390043258667}, {"fpath": "downloaded_repos/fooplugins_foogallery/pro/js/foogallery.media-views.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 2.288843870162964}]}, "tainting_time": {"total_time": 18.160796880722046, "per_def_and_rule_time": {"mean": 0.0023999995877787817, "std_dev": 0.00019555223876827798}, "very_slow_stats": {"time_ratio": 0.4150217830319928, "count_ratio": 0.007532707810228624}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/fooplugins_foogallery/pro/js/foogallery.media-views.js", "fline": 1, "rule_id": "javascript.express.security.cors-misconfiguration.cors-misconfiguration", "time": 0.1969308853149414}, {"fpath": "downloaded_repos/fooplugins_foogallery/pro/js/foogallery.media-views.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.21138787269592285}, {"fpath": "downloaded_repos/fooplugins_foogallery/pro/js/foogallery.media-views.js", "fline": 1, "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 0.22155499458312988}, {"fpath": "downloaded_repos/fooplugins_foogallery/pro/js/foogallery.media-views.js", "fline": 1, "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.22217202186584473}, {"fpath": "downloaded_repos/fooplugins_foogallery/js/admin-foogallery-edit.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.2222909927368164}, {"fpath": "downloaded_repos/fooplugins_foogallery/pro/js/foogallery.media-views.js", "fline": 1, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 0.22339797019958496}, {"fpath": "downloaded_repos/fooplugins_foogallery/pro/js/foogallery.media-views.js", "fline": 1, "rule_id": "javascript.express.security.x-frame-options-misconfiguration.x-frame-options-misconfiguration", "time": 0.228532075881958}, {"fpath": "downloaded_repos/fooplugins_foogallery/pro/js/foogallery.media-views.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.26366710662841797}, {"fpath": "downloaded_repos/fooplugins_foogallery/js/admin-foogallery-edit.js", "fline": 1, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 0.28235697746276855}, {"fpath": "downloaded_repos/fooplugins_foogallery/extensions/albums/js/album-stack.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.6187889575958252}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1094137856}, "engine_requested": "OSS", "skipped_rules": []}