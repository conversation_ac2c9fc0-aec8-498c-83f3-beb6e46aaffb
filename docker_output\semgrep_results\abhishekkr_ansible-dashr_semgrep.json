{"version": "1.130.0", "results": [{"check_id": "go.lang.security.audit.net.use-tls.use-tls", "path": "downloaded_repos/abhishekkr_ansible-dashr/dashr.go", "start": {"line": 38, "col": 12, "offset": 1373}, "end": {"line": 38, "col": 55, "offset": 1416}, "extra": {"message": "Found an HTTP server without TLS. Use 'http.ListenAndServeTLS' instead. See https://golang.org/pkg/net/http/#ListenAndServeTLS for more information.", "fix": "http.ListenAndServeTLS(connection_string, certFile, keyFile, nil)", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "references": ["https://golang.org/pkg/net/http/#ListenAndServeTLS"], "category": "security", "technology": ["go"], "confidence": "MEDIUM", "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/go.lang.security.audit.net.use-tls.use-tls", "shortlink": "https://sg.run/dKbY"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/abhishekkr_ansible-dashr/www-data/dashboard.html", "start": {"line": 75, "col": 9, "offset": 2999}, "end": {"line": 75, "col": 91, "offset": 3081}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/abhishekkr_ansible-dashr/www-data/hosts.html", "start": {"line": 89, "col": 9, "offset": 3207}, "end": {"line": 89, "col": 91, "offset": 3289}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/abhishekkr_ansible-dashr/www-data/index.html", "start": {"line": 78, "col": 9, "offset": 3141}, "end": {"line": 78, "col": 91, "offset": 3223}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/abhishekkr_ansible-dashr/www-data/js/main.dashboard.js", "start": {"line": 112, "col": 3, "offset": 4010}, "end": {"line": 112, "col": 205, "offset": 4212}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/abhishekkr_ansible-dashr/www-data/js/main.dashboard.js", "start": {"line": 113, "col": 3, "offset": 4215}, "end": {"line": 113, "col": 162, "offset": 4374}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/abhishekkr_ansible-dashr/www-data/js/main.dashboard.js", "start": {"line": 114, "col": 3, "offset": 4377}, "end": {"line": 114, "col": 162, "offset": 4536}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/abhishekkr_ansible-dashr/www-data/js/main.hosts.js", "start": {"line": 15, "col": 3, "offset": 730}, "end": {"line": 15, "col": 41, "offset": 768}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/abhishekkr_ansible-dashr/www-data/js/main.hosts.js", "start": {"line": 35, "col": 3, "offset": 1673}, "end": {"line": 35, "col": 31, "offset": 1701}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/abhishekkr_ansible-dashr/www-data/js/main.hosts.js", "start": {"line": 49, "col": 3, "offset": 2266}, "end": {"line": 49, "col": 63, "offset": 2326}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/abhishekkr_ansible-dashr/www-data/js/main.hosts.js", "start": {"line": 55, "col": 3, "offset": 2533}, "end": {"line": 55, "col": 59, "offset": 2589}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/abhishekkr_ansible-dashr/www-data/js/main.hosts.js", "start": {"line": 56, "col": 3, "offset": 2592}, "end": {"line": 56, "col": 122, "offset": 2711}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/abhishekkr_ansible-dashr/www-data/js/main.playbook.parser.js", "start": {"line": 114, "col": 3, "offset": 3220}, "end": {"line": 114, "col": 51, "offset": 3268}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/abhishekkr_ansible-dashr/www-data/js/main.playbook.parser.js", "start": {"line": 122, "col": 3, "offset": 3555}, "end": {"line": 122, "col": 38, "offset": 3590}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/abhishekkr_ansible-dashr/www-data/js/main.playbook.parser.js", "start": {"line": 138, "col": 3, "offset": 4290}, "end": {"line": 138, "col": 47, "offset": 4334}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/abhishekkr_ansible-dashr/www-data/js/main.roles.parser.js", "start": {"line": 45, "col": 3, "offset": 1313}, "end": {"line": 45, "col": 42, "offset": 1352}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/abhishekkr_ansible-dashr/www-data/js/main.roles.parser.js", "start": {"line": 63, "col": 3, "offset": 2059}, "end": {"line": 63, "col": 38, "offset": 2094}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/abhishekkr_ansible-dashr/www-data/js/main.roles.parser.js", "start": {"line": 75, "col": 3, "offset": 2452}, "end": {"line": 75, "col": 43, "offset": 2492}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/abhishekkr_ansible-dashr/www-data/playbooks.html", "start": {"line": 79, "col": 9, "offset": 2817}, "end": {"line": 79, "col": 91, "offset": 2899}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/abhishekkr_ansible-dashr/www-data/roles.html", "start": {"line": 78, "col": 9, "offset": 2764}, "end": {"line": 78, "col": 91, "offset": 2846}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/abhishekkr_ansible-dashr/www-data/runner.html", "start": {"line": 56, "col": 9, "offset": 2112}, "end": {"line": 56, "col": 91, "offset": 2194}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/abhishekkr_ansible-dashr/.gitignore", "downloaded_repos/abhishekkr_ansible-dashr/HowTo.md", "downloaded_repos/abhishekkr_ansible-dashr/LICENSE", "downloaded_repos/abhishekkr_ansible-dashr/README.md", "downloaded_repos/abhishekkr_ansible-dashr/SCREENSHOTS.md", "downloaded_repos/abhishekkr_ansible-dashr/build-server.sh", "downloaded_repos/abhishekkr_ansible-dashr/callback/dashr_callback.py", "downloaded_repos/abhishekkr_ansible-dashr/config/js/main.data.js", "downloaded_repos/abhishekkr_ansible-dashr/dashr.go", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/ansible.cfg", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/group_vars/all", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/group_vars/env.alpha", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/host_vars/db1.alpha.example.com", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/host_vars/db2.alpha.example.com", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/inventories/env.alpha", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/inventories/env.beta", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/inventories/env.local", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/logs/dashr_log_hostlist.yaml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/logs/hosts/127.0.0.1", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/pb01.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/pb02.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/pb03.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/pb04.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/pb05.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/bind/README.md", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/bind/defaults/main.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/bind/files/masters/db.example.com", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/bind/files/named.conf", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/bind/handlers/main.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/bind/meta/main.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/bind/role.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/bind/tasks/main.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/bind/templates/named.conf.local.master.j2", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/bind/templates/named.conf.local.slave.j2", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/bind/templates/named.conf.options.j2", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/bind/vars/main.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/memcached/README.md", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/memcached/defaults/main.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/memcached/handlers/main.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/memcached/meta/main.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/memcached/tasks/main.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/memcached/templates/memcached_debian.j2", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/memcached/templates/memcached_redhat.j2", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/memcached/vars/main.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/mysql/README.md", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/mysql/defaults/main.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/mysql/handlers/main.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/mysql/meta/main.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/mysql/tasks/main.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/mysql/templates/.my.cnf.j2", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/mysql/templates/my.cnf.Debian.j2", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/mysql/templates/my.cnf.RedHat.j2", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/mysql/vars/Debian.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/mysql/vars/RedHat.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/mysql/vars/main.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/nginx/README.md", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/nginx/defaults/main.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/nginx/files/epel.repo", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/nginx/handlers/main.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/nginx/meta/main.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/nginx/tasks/main.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/nginx/templates/default.conf.j2", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/nginx/templates/default.j2", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/nginx/templates/nginx.conf.j2", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/nginx/templates/site.j2", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/nginx/vars/main.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/ntp/.gitignore", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/ntp/README.md", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/ntp/defaults/main.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/ntp/handlers/main.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/ntp/meta/main.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/ntp/tasks/main.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/ntp/templates/ntp.conf.j2", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/ntp/vars/Debian.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/ntp/vars/RedHat.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/redis/README.md", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/redis/defaults/main.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/redis/files/epel6.repo", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/redis/files/epel7.repo", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/redis/handlers/main.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/redis/meta/main.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/redis/tasks/main.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/redis/templates/redis.conf.j2", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/redis/templates/rsyslogd.conf.j2", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/redis/vars/Debian.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/redis/vars/RedHat.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/redis/vars/main.yml", "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/sample-playbook.yml", "downloaded_repos/abhishekkr_ansible-dashr/goansible/log_analyzer.go", "downloaded_repos/abhishekkr_ansible-dashr/screenshots/dashboard.failed.png", "downloaded_repos/abhishekkr_ansible-dashr/screenshots/dashboard.passed.png", "downloaded_repos/abhishekkr_ansible-dashr/screenshots/dashboard.png", "downloaded_repos/abhishekkr_ansible-dashr/screenshots/dashboard.searched.png", "downloaded_repos/abhishekkr_ansible-dashr/screenshots/homepage.png", "downloaded_repos/abhishekkr_ansible-dashr/screenshots/hosts.png", "downloaded_repos/abhishekkr_ansible-dashr/screenshots/playbooks.png", "downloaded_repos/abhishekkr_ansible-dashr/screenshots/roles.png", "downloaded_repos/abhishekkr_ansible-dashr/www-data/404.html", "downloaded_repos/abhishekkr_ansible-dashr/www-data/css/bootstrap-responsive.css", "downloaded_repos/abhishekkr_ansible-dashr/www-data/css/bootstrap-responsive.min.css", "downloaded_repos/abhishekkr_ansible-dashr/www-data/css/bootstrap.css", "downloaded_repos/abhishekkr_ansible-dashr/www-data/css/bootstrap.min.css", "downloaded_repos/abhishekkr_ansible-dashr/www-data/css/docs.css", "downloaded_repos/abhishekkr_ansible-dashr/www-data/css/main.css", "downloaded_repos/abhishekkr_ansible-dashr/www-data/dashboard.html", "downloaded_repos/abhishekkr_ansible-dashr/www-data/favicon.ico", "downloaded_repos/abhishekkr_ansible-dashr/www-data/hosts.html", "downloaded_repos/abhishekkr_ansible-dashr/www-data/humans.txt", "downloaded_repos/abhishekkr_ansible-dashr/www-data/img/glyphicons-halflings-white.png", "downloaded_repos/abhishekkr_ansible-dashr/www-data/img/glyphicons-halflings.png", "downloaded_repos/abhishekkr_ansible-dashr/www-data/index.html", "downloaded_repos/abhishekkr_ansible-dashr/www-data/js/home.js", "downloaded_repos/abhishekkr_ansible-dashr/www-data/js/main.dashboard.js", "downloaded_repos/abhishekkr_ansible-dashr/www-data/js/main.hosts.js", "downloaded_repos/abhishekkr_ansible-dashr/www-data/js/main.js", "downloaded_repos/abhishekkr_ansible-dashr/www-data/js/main.playbook.parser.js", "downloaded_repos/abhishekkr_ansible-dashr/www-data/js/main.roles.parser.js", "downloaded_repos/abhishekkr_ansible-dashr/www-data/js/plugins.js", "downloaded_repos/abhishekkr_ansible-dashr/www-data/playbooks.html", "downloaded_repos/abhishekkr_ansible-dashr/www-data/robots.txt", "downloaded_repos/abhishekkr_ansible-dashr/www-data/roles.html", "downloaded_repos/abhishekkr_ansible-dashr/www-data/runner.html"], "skipped": [{"path": "downloaded_repos/abhishekkr_ansible-dashr/dummy-ansible-files/roles/ntp/tests/role.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abhishekkr_ansible-dashr/www-data/js/js-yaml.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abhishekkr_ansible-dashr/www-data/js/vendor/bootstrap.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abhishekkr_ansible-dashr/www-data/js/vendor/bootstrap.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abhishekkr_ansible-dashr/www-data/js/vendor/highcharts.exporting.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abhishekkr_ansible-dashr/www-data/js/vendor/highcharts.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abhishekkr_ansible-dashr/www-data/js/vendor/jquery-3.7.1.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abhishekkr_ansible-dashr/www-data/js/vendor/jstree.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abhishekkr_ansible-dashr/www-data/js/vendor/list.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abhishekkr_ansible-dashr/www-data/js/vendor/list.pagination.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abhishekkr_ansible-dashr/www-data/js/vendor/modernizr-2.6.1-respond-1.1.0.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/abhishekkr_ansible-dashr/www-data/js/vendor/yaml.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.7094628810882568, "profiling_times": {"config_time": 6.564982891082764, "core_time": 3.058570623397827, "ignores_time": 0.0017063617706298828, "total_time": 9.626259803771973}, "parsing_time": {"total_time": 0.5572271347045898, "per_file_time": {"mean": 0.008987534430719193, "std_dev": 0.0002048112202085588}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 2.667158603668213, "per_file_time": {"mean": 0.008687813041264548, "std_dev": 0.0007746641905511654}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.7233967781066895, "per_file_and_rule_time": {"mean": 0.0005476130038657755, "std_dev": 9.60934868237808e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.15132737159729004, "per_def_and_rule_time": {"mean": 0.000836062826504365, "std_dev": 2.4099551456605167e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}