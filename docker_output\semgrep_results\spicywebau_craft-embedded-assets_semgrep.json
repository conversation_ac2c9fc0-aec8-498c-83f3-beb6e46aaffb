{"version": "1.130.0", "results": [], "errors": [], "paths": {"scanned": ["downloaded_repos/spicywebau_craft-embedded-assets/.ddev/config.yaml", "downloaded_repos/spicywebau_craft-embedded-assets/.github/ISSUE_TEMPLATE/BUG-REPORT.yml", "downloaded_repos/spicywebau_craft-embedded-assets/.github/ISSUE_TEMPLATE/FEATURE-REQUEST.yml", "downloaded_repos/spicywebau_craft-embedded-assets/.github/ISSUE_TEMPLATE/QUESTION.yml", "downloaded_repos/spicywebau_craft-embedded-assets/.github/ISSUE_TEMPLATE/config.yml", "downloaded_repos/spicywebau_craft-embedded-assets/.gitignore", "downloaded_repos/spicywebau_craft-embedded-assets/CHANGELOG.md", "downloaded_repos/spicywebau_craft-embedded-assets/LICENSE", "downloaded_repos/spicywebau_craft-embedded-assets/README.md", "downloaded_repos/spicywebau_craft-embedded-assets/composer.json", "downloaded_repos/spicywebau_craft-embedded-assets/composer.lock", "downloaded_repos/spicywebau_craft-embedded-assets/docs/assets/asset-table.png", "downloaded_repos/spicywebau_craft-embedded-assets/docs/assets/<EMAIL>", "downloaded_repos/spicywebau_craft-embedded-assets/docs/assets/bluesky.png", "downloaded_repos/spicywebau_craft-embedded-assets/docs/assets/google-maps.png", "downloaded_repos/spicywebau_craft-embedded-assets/docs/assets/<EMAIL>", "downloaded_repos/spicywebau_craft-embedded-assets/docs/assets/icon.png", "downloaded_repos/spicywebau_craft-embedded-assets/docs/assets/input.png", "downloaded_repos/spicywebau_craft-embedded-assets/docs/assets/<EMAIL>", "downloaded_repos/spicywebau_craft-embedded-assets/docs/assets/youtube.png", "downloaded_repos/spicywebau_craft-embedded-assets/docs/assets/<EMAIL>", "downloaded_repos/spicywebau_craft-embedded-assets/docs/configuration.md", "downloaded_repos/spicywebau_craft-embedded-assets/docs/console-commands.md", "downloaded_repos/spicywebau_craft-embedded-assets/docs/events.md", "downloaded_repos/spicywebau_craft-embedded-assets/docs/graphql.md", "downloaded_repos/spicywebau_craft-embedded-assets/docs/installation.md", "downloaded_repos/spicywebau_craft-embedded-assets/docs/templating.md", "downloaded_repos/spicywebau_craft-embedded-assets/ecs.php", "downloaded_repos/spicywebau_craft-embedded-assets/package-lock.json", "downloaded_repos/spicywebau_craft-embedded-assets/package.json", "downloaded_repos/spicywebau_craft-embedded-assets/src/Controller.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/Plugin.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/Service.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/Variable.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/actions/Refresh.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/adapters/akamai/Extractor.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/adapters/akamai/detectors/Type.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/adapters/akamai/detectors/Url.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/adapters/bluesky/Extractor.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/adapters/bluesky/OEmbed.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/adapters/default/Extractor.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/adapters/default/detectors/Title.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/adapters/default/detectors/Type.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/adapters/googlemaps/Extractor.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/adapters/googlemaps/detectors/Code.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/adapters/googlemaps/detectors/ProviderName.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/adapters/googlemaps/detectors/Title.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/adapters/googlemaps/detectors/Type.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/adapters/ipcamlive/Extractor.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/adapters/ipcamlive/detectors/Code.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/adapters/ipcamlive/detectors/ProviderName.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/adapters/ipcamlive/detectors/ProviderUrl.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/adapters/ipcamlive/detectors/Type.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/adapters/openstreetmap/Extractor.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/adapters/openstreetmap/detectors/Code.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/adapters/openstreetmap/detectors/Type.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/adapters/pbs/Extractor.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/adapters/pbs/detectors/Code.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/adapters/pbs/detectors/Type.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/adapters/sharepoint/Extractor.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/adapters/sharepoint/detectors/Url.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/assetpreviews/EmbeddedAsset.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/assets/Main.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/assets/Preview.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/assets/main/MainAsset.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/assets/main/src/scripts/Button.ts", "downloaded_repos/spicywebau_craft-embedded-assets/src/assets/main/src/scripts/EmbeddedAssets.ts", "downloaded_repos/spicywebau_craft-embedded-assets/src/assets/main/src/scripts/Emitter.ts", "downloaded_repos/spicywebau_craft-embedded-assets/src/assets/main/src/scripts/Form.ts", "downloaded_repos/spicywebau_craft-embedded-assets/src/assets/main/src/scripts/Modal.ts", "downloaded_repos/spicywebau_craft-embedded-assets/src/assets/main/src/scripts/Preview.ts", "downloaded_repos/spicywebau_craft-embedded-assets/src/assets/main/src/scripts/events.ts", "downloaded_repos/spicywebau_craft-embedded-assets/src/assets/main/src/scripts/main.ts", "downloaded_repos/spicywebau_craft-embedded-assets/src/assets/main/src/scripts/responses.ts", "downloaded_repos/spicywebau_craft-embedded-assets/src/assets/main/src/scripts/types/Craft.d.ts", "downloaded_repos/spicywebau_craft-embedded-assets/src/assets/main/src/scripts/types/Garnish.d.ts", "downloaded_repos/spicywebau_craft-embedded-assets/src/assets/main/src/scripts/utilities.ts", "downloaded_repos/spicywebau_craft-embedded-assets/src/assets/main/src/styles/main.scss", "downloaded_repos/spicywebau_craft-embedded-assets/src/assets/preview/PreviewAsset.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/assets/preview/src/assets/warning.svg", "downloaded_repos/spicywebau_craft-embedded-assets/src/assets/preview/src/scripts/preview.ts", "downloaded_repos/spicywebau_craft-embedded-assets/src/assets/preview/src/styles/preview.scss", "downloaded_repos/spicywebau_craft-embedded-assets/src/console/controllers/RefreshController.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/errors/NotWhitelistedException.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/errors/RefreshException.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/events/BeforeRequestEvent.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/gql/interfaces/EmbeddedAsset.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/gql/resolvers/EmbeddedAsset.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/gql/types/EmbeddedAsset.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/gql/types/generators/EmbeddedAssetType.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/icon.svg", "downloaded_repos/spicywebau_craft-embedded-assets/src/jobs/InstagramRefreshCheck.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/models/EmbeddedAsset.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/models/Settings.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/resources/default-thumb.svg", "downloaded_repos/spicywebau_craft-embedded-assets/src/templates/_components/elementactions/refresh/trigger.twig", "downloaded_repos/spicywebau_craft-embedded-assets/src/templates/_components/embedded_asset_preview.twig", "downloaded_repos/spicywebau_craft-embedded-assets/src/templates/_preview.twig", "downloaded_repos/spicywebau_craft-embedded-assets/src/templates/_previews/default.twig", "downloaded_repos/spicywebau_craft-embedded-assets/src/templates/settings.twig", "downloaded_repos/spicywebau_craft-embedded-assets/src/translations/en/embeddedassets.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/translations/fr/embeddedassets.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/validators/Parameter.php", "downloaded_repos/spicywebau_craft-embedded-assets/src/validators/TwigMarkup.php", "downloaded_repos/spicywebau_craft-embedded-assets/tsconfig.json", "downloaded_repos/spicywebau_craft-embedded-assets/webpack.config.js"], "skipped": [{"path": "downloaded_repos/spicywebau_craft-embedded-assets/src/assets/main/dist/scripts/main.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-embedded-assets/src/assets/main/dist/scripts/main.js.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-embedded-assets/src/assets/main/dist/styles/main.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-embedded-assets/src/assets/main/dist/styles/main.css.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-embedded-assets/src/assets/preview/dist/scripts/preview.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-embedded-assets/src/assets/preview/dist/scripts/preview.js.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-embedded-assets/src/assets/preview/dist/styles/preview.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spicywebau_craft-embedded-assets/src/assets/preview/dist/styles/preview.css.map", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.7049789428710938, "profiling_times": {"config_time": 5.796429395675659, "core_time": 2.780350923538208, "ignores_time": 0.0019276142120361328, "total_time": 8.579415321350098}, "parsing_time": {"total_time": 0.9718565940856934, "per_file_time": {"mean": 0.012958087921142579, "std_dev": 0.0007925691440156999}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 3.1751904487609863, "per_file_time": {"mean": 0.011063381354567898, "std_dev": 0.0014908571993691425}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.7041921615600586, "per_file_and_rule_time": {"mean": 0.0021667451124924874, "std_dev": 5.301024447046892e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.13489603996276855, "per_def_and_rule_time": {"mean": 0.0003887493947053849, "std_dev": 5.945104754045538e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}