{"version": "1.130.0", "results": [{"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/NationalSecurityAgency_skills-docs/docs/.vuepress/components/FormAndPki.vue", "start": {"line": 62, "col": 57, "offset": 1759}, "end": {"line": 62, "col": 70, "offset": 1772}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/NationalSecurityAgency_skills-docs/docs/.vuepress/components/FormAndPki.vue", "start": {"line": 67, "col": 28, "offset": 2009}, "end": {"line": 67, "col": 41, "offset": 2022}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/NationalSecurityAgency_skills-docs/.github/workflows/publish-docs.yml", "downloaded_repos/NationalSecurityAgency_skills-docs/.github/workflows/scripts/gen-screenshots.sh", "downloaded_repos/NationalSecurityAgency_skills-docs/.github/workflows/scripts/move-screenshots.sh", "downloaded_repos/NationalSecurityAgency_skills-docs/.github/workflows/verify-build.yml", "downloaded_repos/NationalSecurityAgency_skills-docs/.gitignore", "downloaded_repos/NationalSecurityAgency_skills-docs/.gitlab-ci.yml", "downloaded_repos/NationalSecurityAgency_skills-docs/CONTRIBUTING_IO.md", "downloaded_repos/NationalSecurityAgency_skills-docs/DISCLAIMER.md", "downloaded_repos/NationalSecurityAgency_skills-docs/INTENT.md", "downloaded_repos/NationalSecurityAgency_skills-docs/LICENSE.txt", "downloaded_repos/NationalSecurityAgency_skills-docs/README.md", "downloaded_repos/NationalSecurityAgency_skills-docs/babel.config.js", "downloaded_repos/NationalSecurityAgency_skills-docs/cypress/e2e/admin-quiz_and_surveys.cy.js", "downloaded_repos/NationalSecurityAgency_skills-docs/cypress/e2e/admin.cy.js", "downloaded_repos/NationalSecurityAgency_skills-docs/cypress/e2e/progress-and-ranking.cy.js", "downloaded_repos/NationalSecurityAgency_skills-docs/cypress/fixtures/example.json", "downloaded_repos/NationalSecurityAgency_skills-docs/cypress/support/commands.js", "downloaded_repos/NationalSecurityAgency_skills-docs/cypress/support/e2e.js", "downloaded_repos/NationalSecurityAgency_skills-docs/cypress.config.js", "downloaded_repos/NationalSecurityAgency_skills-docs/diagrams_work/AuthEndpointFlow(3).drawio", "downloaded_repos/NationalSecurityAgency_skills-docs/diagrams_work/DashboardWithIntegratedApp.jpg", "downloaded_repos/NationalSecurityAgency_skills-docs/diagrams_work/Diagrams.odp", "downloaded_repos/NationalSecurityAgency_skills-docs/diagrams_work/Integration-Overview(2).drawio", "downloaded_repos/NationalSecurityAgency_skills-docs/diagrams_work/Integration-Reporter(1).drawio", "downloaded_repos/NationalSecurityAgency_skills-docs/diagrams_work/SkillTree-MajorComponents(4).drawio", "downloaded_repos/NationalSecurityAgency_skills-docs/diagrams_work/SkillTree-PlatformOverview.drawio", "downloaded_repos/NationalSecurityAgency_skills-docs/diagrams_work/js-deps.jpg", "downloaded_repos/NationalSecurityAgency_skills-docs/diagrams_work/skills-catalog.drawio", "downloaded_repos/NationalSecurityAgency_skills-docs/diagrams_work/skilltree_logo.svg", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/.vuepress/components/Conditional.vue", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/.vuepress/components/ExternalUrl.vue", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/.vuepress/components/FormAndPki.vue", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/.vuepress/components/HomePage.vue", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/.vuepress/components/ReleaseDate.vue", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/.vuepress/components/RequiresRole.vue", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/.vuepress/components/ServiceUrl.vue", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/.vuepress/components/Since.vue", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/.vuepress/components/SkillTreeVideos.vue", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/.vuepress/components/home/<USER>", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/.vuepress/components/home/<USER>", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/.vuepress/components/home/<USER>", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/.vuepress/components/home/<USER>", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/.vuepress/components/home/<USER>", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/.vuepress/components/home/<USER>", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/.vuepress/components/videos/SkillTreeVideo.vue", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/.vuepress/components/videos/skilltree-training-videos.json", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/.vuepress/config.js", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/.vuepress/public/img/browser-icons/chrome.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/.vuepress/public/img/browser-icons/edge.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/.vuepress/public/img/browser-icons/firefox.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/.vuepress/public/img/browser-icons/opera.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/.vuepress/public/img/browser-icons/safari.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/.vuepress/public/img/home/<USER>", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/.vuepress/public/img/home/<USER>", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/.vuepress/public/img/skilltree.ico", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/.vuepress/public/img/skilltree_logo.svg", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/.vuepress/public/img/skilltree_logo_dark_mode.svg", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/.vuepress/public/img/skilltree_logo_v1.svg", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/.vuepress/public/img/skilltree_logo_v1_dark_mode.svg", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/.vuepress/styles/index.scss", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/README.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/contribution/README.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/contribution/architecture.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/contribution/devEnv.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/contribution/diagrams/JsDeps.jpg", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/contribution/diagrams/SkillsServiceArchitecture.jpg", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/install-guide/README.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/install-guide/common/install-tip.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/install-guide/common/install-type-intro.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/install-guide/common/oath2-support.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/install-guide/common/prod-env-tip.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/install-guide/common/prod-install-basic-config.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/install-guide/common/prod-install-basic-jvm-props.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/install-guide/common/service-install-output-and-backend.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/install-guide/common/services-explanations.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/install-guide/common/ssl-props.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/install-guide/common/two-way-ssl-props.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/install-guide/common/user-info-service-props-endpoints.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/install-guide/common/user-info-service-props-ssl.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/install-guide/config.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/install-guide/devInstall.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/install-guide/diagrams/ProdInstall-Pass.jpg", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/install-guide/diagrams/ProdInstall-Pki.jpg", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/install-guide/distributions.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/install-guide/installModes.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/install-guide/prodInstall.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/install-guide/quickStart.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/README.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/accessibility.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/admin-groups.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/badges.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/common/activity-history.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/common/audio.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/common/projects-screenshot.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/common/rte-features-table.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/common/rte.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/common/videos-configure.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/common/videos-intro.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/contact-admins.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/contact-project-users.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/diagrams/DashboardWithIntegratedApp.jpg", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/diagrams/skills-catalog.jpg", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/icons.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/inception.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/learning-path.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/levels.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/metrics.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/notifications.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/progress-and-ranking.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/projects.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/quizzes-and-surveys.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/rich-text-editor.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/screenshots/Component-Private-Project-Invite.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/screenshots/Component-Private-Project-Pending.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/screenshots/Self_Report_Unsubscribe_20220720.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/screenshots/concat_proj_btn.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/screenshots/contact_admins_form.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/screenshots/copy_btn.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/self-reporting.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/settings.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/skills-catalog.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/skills-groups.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/skills.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/subjects.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/dashboard/user-guide/users.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/overview/README.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/overview/diagrams/SkillTree-MajorComponents.jpg", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/overview/diagrams/SkillTree-PlatformOverview.jpg", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/overview/diagrams/SkillTreePlatformOverview.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/overview/diagrams/SkillsPlatformOverview.jpg", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/release-notes/README.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/release-notes/skills-client.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/release-notes/skills-service.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/component-catalog_finalize_alert.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/component-conf-approval-workload-fallback.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/component-conf-approval-workload-skills.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/component-conf-approval-workload-withSkillsAdded.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/component-edit_skill_button.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/component-manage-learning-path.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/component-notifications-btn.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/component-notifications-dropdown.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/component-project-settings.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/component-quiz-grading-email-subscriptions.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/component-quiz-skill.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/component-settings-menu.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/component-table_with_disabled_skills.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/component-user_progress_table.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/component-users_archive.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/component-users_table_ready_to_archive.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/existing-tag-dropdown.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/modal-badges-new_badge.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/modal-edit-skill.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/modal-edit-subjectIcon.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/modal-edit_imported_skill.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/modal-import_catalog_skills.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/modal-new-badge-component-bonus-award.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/modal-new-group.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/modal-new-question-hint-component.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/modal-new-question.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/modal-new-quiz.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/modal-new-skill_self-report-checked.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/modal-new-survey-question.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/modal-projects-new_project.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/modal-share_proj.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/modal-skills-new_skill.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/modal-subjects-new_subject.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-admin-groups-projects.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-admin-groups-quizzes.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-admin-groups.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-badges.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-contact_proj_admins.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-custom-headerAndFooter.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-expiration-config.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-export-to-catalog.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-inception.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-partial-share-proj.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-project-access.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-project-dark-mode.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-project-issues.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-project-learning-path.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-project-levels.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-project-metrics-achievements.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-project-metrics-skills.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-project-metrics-subjects.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-project-notify_users.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-project-self_report.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-project-settings.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-projects-not-root.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-projects.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-quiz-access.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-quiz-grading.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-quiz-results.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-quiz-runs.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-quiz-settings.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-quiz-single-result.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-quiz-skills.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-quiz.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-quizzes.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-settings-email.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-settings-preference.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-settings-security.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-settings-system.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-skills-exported-to-catalog.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-skills-group.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-skills.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-subjects.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-survey.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-topSkill-metrics.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-user-performed-skills.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/page-video-config.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/rich-text-editor-1.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/rich-text-editor-2.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/rich-text-editor-3.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/rich-text-editor-4.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/rich-text-editor-5.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/admin/skill-tags-page.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/progress-and-ranking/client-display-badge-page.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/progress-and-ranking/client-display-card-prerequisites.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/progress-and-ranking/client-display-page-honor-tag.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/progress-and-ranking/client-display-proj.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/progress-and-ranking/client-display-quiz-run.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/progress-and-ranking/client-display-rank.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/progress-and-ranking/client-display-self-report-honor-tag.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/progress-and-ranking/client-display-single-skill-group.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/progress-and-ranking/client-display-skill-page-avatar.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/progress-and-ranking/client-display-skillWithDeps.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/progress-and-ranking/client-display-skills-selfReport.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/progress-and-ranking/client-display-subject-comedy.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/progress-and-ranking/client-display-subject-expandedSkills.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/progress-and-ranking/client-display-subject.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/progress-and-ranking/client-display-themed-proj.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/progress-and-ranking/component-progress-and-rankings-badge-filter.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/progress-and-ranking/component-training-wide-search.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/progress-and-ranking/page-my-quiz-attempts.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/progress-and-ranking/page-my-quiz-single-attempt.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/progress-and-ranking/page-progress-and-rankings-badges.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/progress-and-ranking/page-progress-and-rankings-manage-my-projects.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/progress-and-ranking/page-progress-and-rankings-my-badges.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/progress-and-ranking/page-progress-and-rankings-my-rank.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/progress-and-ranking/page-progress-and-rankings-view-my-usage.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/screenshots/progress-and-ranking/page-progress-and-rankings.png", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/README.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/auth/authForm.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/auth/authPKI.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/auth/endpointParamsForm.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/auth/endpointParamsPKI.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/auth/endpointsAuthPKI.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/auth/endpointsFormPKI.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/auth/reportSkillJavaExampleForm.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/auth/reportSkillJavaExamplePKI.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/auth.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/common/skillsConfiguration/js/clientConfig.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/common/skillsConfiguration/js/configExampleForm.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/common/skillsConfiguration/js/configExamplePki.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/common/skillsConfiguration/ng/clientConfig.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/common/skillsConfiguration/ng/configExampleForm.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/common/skillsConfiguration/ng/configExamplePki.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/common/skillsConfiguration/react/clientConfig.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/common/skillsConfiguration/react/configExampleForm.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/common/skillsConfiguration/react/configExamplePki.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/common/skillsConfiguration/skillsConfigurationHeader.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/common/skillsConfiguration/skillsConfigurationParameters.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/common/skillsConfiguration/vuejs/clientConfig.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/common/skillsConfiguration/vuejs/configExampleForm.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/common/skillsConfiguration/vuejs/configExamplePki.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/common/skillsDisplayArguments.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/common/skillsDisplayIntro.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/common/skillsDisplayOptionsObject.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/common/skillsReporter/globalEventHandling.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/common/skillsReporter/reporterConfiguration.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/common/skillsReporter/responseObject.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/common/slillsDisplayTheme.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/diagrams/AuthEndpointFlow.jpg", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/diagrams/IntegratedApplication.jpg", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/diagrams/Integration-Overview.jpg", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/diagrams/Integration-Reporter.jpg", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/diagrams/Integration-SkillsDisplay.jpg", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/endpoints.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/js.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/skills-client/legacy.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/training-participation/README.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/training-participation/accessibility.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/training-participation/common/accessibility-basic-nav.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/training-participation/common/accessibility-feedback.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/training-participation/common/accessibility-skip-to-content.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/training-participation/common/accessibility-visual-adjustments.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/training-participation/common/screen-reader-support.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/training-participation/take-training.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/training-participation/training-portfolio.md", "downloaded_repos/NationalSecurityAgency_skills-docs/docs/videos/README.md", "downloaded_repos/NationalSecurityAgency_skills-docs/logos/skilltree_log_v1.svg", "downloaded_repos/NationalSecurityAgency_skills-docs/logos/skilltree_logo.png", "downloaded_repos/NationalSecurityAgency_skills-docs/logos/skilltree_logo.svg", "downloaded_repos/NationalSecurityAgency_skills-docs/logos/skilltree_logo_dark_mode.svg", "downloaded_repos/NationalSecurityAgency_skills-docs/logos/skilltree_logo_v1_dark_mode.svg", "downloaded_repos/NationalSecurityAgency_skills-docs/package.json", "downloaded_repos/NationalSecurityAgency_skills-docs/public/index.html", "downloaded_repos/NationalSecurityAgency_skills-docs/src/App.vue", "downloaded_repos/NationalSecurityAgency_skills-docs/src/assets/logo.png", "downloaded_repos/NationalSecurityAgency_skills-docs/src/components/HelloWorld.vue", "downloaded_repos/NationalSecurityAgency_skills-docs/src/main.js"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.6516070365905762, "profiling_times": {"config_time": 6.028564214706421, "core_time": 3.782649040222168, "ignores_time": 0.0018277168273925781, "total_time": 9.814118385314941}, "parsing_time": {"total_time": 0.8975398540496826, "per_file_time": {"mean": 0.04986332522498237, "std_dev": 0.004105613186655572}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 7.120020866394043, "per_file_time": {"mean": 0.011483904623216199, "std_dev": 0.0023847597292254007}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.7142224311828613, "per_file_and_rule_time": {"mean": 0.004825827237722035, "std_dev": 0.000174317462207555}, "very_slow_stats": {"time_ratio": 0.1684012837212159, "count_ratio": 0.006756756756756757}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/NationalSecurityAgency_skills-docs/cypress/e2e/admin.cy.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.12027597427368164}]}, "tainting_time": {"total_time": 0.19264459609985352, "per_def_and_rule_time": {"mean": 0.008375852004341457, "std_dev": 8.661058968510246e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}