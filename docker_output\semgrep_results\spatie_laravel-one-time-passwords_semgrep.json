{"version": "1.130.0", "results": [{"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/spatie_laravel-one-time-passwords/src/Livewire/OneTimePasswordComponent.php", "start": {"line": 105, "col": 16, "offset": 2415}, "end": {"line": 105, "col": 50, "offset": 2449}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/spatie_laravel-one-time-passwords/.github/workflows/run-tests.yml", "start": {"line": 38, "col": 38, "offset": 959}, "end": {"line": 38, "col": 57, "offset": 978}}, {"path": "downloaded_repos/spatie_laravel-one-time-passwords/.github/workflows/run-tests.yml", "start": {"line": 39, "col": 24, "offset": 959}, "end": {"line": 39, "col": 43, "offset": 978}}]], "message": "Syntax error at line downloaded_repos/spatie_laravel-one-time-passwords/.github/workflows/run-tests.yml:38:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ runner.tool_cache` was unexpected", "path": "downloaded_repos/spatie_laravel-one-time-passwords/.github/workflows/run-tests.yml", "spans": [{"file": "downloaded_repos/spatie_laravel-one-time-passwords/.github/workflows/run-tests.yml", "start": {"line": 38, "col": 38, "offset": 959}, "end": {"line": 38, "col": 57, "offset": 978}}, {"file": "downloaded_repos/spatie_laravel-one-time-passwords/.github/workflows/run-tests.yml", "start": {"line": 39, "col": 24, "offset": 959}, "end": {"line": 39, "col": 43, "offset": 978}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/spatie_laravel-one-time-passwords/.github/workflows/run-tests.yml", "start": {"line": 43, "col": 53, "offset": 1146}, "end": {"line": 43, "col": 69, "offset": 1162}}, {"path": "downloaded_repos/spatie_laravel-one-time-passwords/.github/workflows/run-tests.yml", "start": {"line": 44, "col": 19, "offset": 1146}, "end": {"line": 44, "col": 22, "offset": 1149}}]], "message": "Syntax error at line downloaded_repos/spatie_laravel-one-time-passwords/.github/workflows/run-tests.yml:43:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ matrix.laravel` was unexpected", "path": "downloaded_repos/spatie_laravel-one-time-passwords/.github/workflows/run-tests.yml", "spans": [{"file": "downloaded_repos/spatie_laravel-one-time-passwords/.github/workflows/run-tests.yml", "start": {"line": 43, "col": 53, "offset": 1146}, "end": {"line": 43, "col": 69, "offset": 1162}}, {"file": "downloaded_repos/spatie_laravel-one-time-passwords/.github/workflows/run-tests.yml", "start": {"line": 44, "col": 19, "offset": 1146}, "end": {"line": 44, "col": 22, "offset": 1149}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/spatie_laravel-one-time-passwords/src/Models/OneTimePassword.php", "start": {"line": 32, "col": 46, "offset": 0}, "end": {"line": 32, "col": 61, "offset": 15}}]], "message": "Syntax error at line downloaded_repos/spatie_laravel-one-time-passwords/src/Models/OneTimePassword.php:32:\n `Authenticatable` was unexpected", "path": "downloaded_repos/spatie_laravel-one-time-passwords/src/Models/OneTimePassword.php", "spans": [{"file": "downloaded_repos/spatie_laravel-one-time-passwords/src/Models/OneTimePassword.php", "start": {"line": 32, "col": 46, "offset": 0}, "end": {"line": 32, "col": 61, "offset": 15}}]}], "paths": {"scanned": ["downloaded_repos/spatie_laravel-one-time-passwords/.editorconfig", "downloaded_repos/spatie_laravel-one-time-passwords/.gitattributes", "downloaded_repos/spatie_laravel-one-time-passwords/.github/FUNDING.yml", "downloaded_repos/spatie_laravel-one-time-passwords/.github/ISSUE_TEMPLATE/bug.yml", "downloaded_repos/spatie_laravel-one-time-passwords/.github/ISSUE_TEMPLATE/config.yml", "downloaded_repos/spatie_laravel-one-time-passwords/.github/dependabot.yml", "downloaded_repos/spatie_laravel-one-time-passwords/.github/workflows/dependabot-auto-merge.yml", "downloaded_repos/spatie_laravel-one-time-passwords/.github/workflows/fix-php-code-style-issues.yml", "downloaded_repos/spatie_laravel-one-time-passwords/.github/workflows/run-tests.yml", "downloaded_repos/spatie_laravel-one-time-passwords/.github/workflows/update-changelog.yml", "downloaded_repos/spatie_laravel-one-time-passwords/.gitignore", "downloaded_repos/spatie_laravel-one-time-passwords/CHANGELOG.md", "downloaded_repos/spatie_laravel-one-time-passwords/LICENSE.md", "downloaded_repos/spatie_laravel-one-time-passwords/README.md", "downloaded_repos/spatie_laravel-one-time-passwords/composer.json", "downloaded_repos/spatie_laravel-one-time-passwords/config/one-time-passwords.php", "downloaded_repos/spatie_laravel-one-time-passwords/database/factories/UserFactory.php", "downloaded_repos/spatie_laravel-one-time-passwords/database/migrations/create_one_time_passwords_table.php.stub", "downloaded_repos/spatie_laravel-one-time-passwords/docs/_index.md", "downloaded_repos/spatie_laravel-one-time-passwords/docs/about-us.md", "downloaded_repos/spatie_laravel-one-time-passwords/docs/avanced-usage/_index.md", "downloaded_repos/spatie_laravel-one-time-passwords/docs/avanced-usage/customizing-actions.md", "downloaded_repos/spatie_laravel-one-time-passwords/docs/avanced-usage/handling-events.md", "downloaded_repos/spatie_laravel-one-time-passwords/docs/avanced-usage/using-your-own-model.md", "downloaded_repos/spatie_laravel-one-time-passwords/docs/basic-usage/_index.md", "downloaded_repos/spatie_laravel-one-time-passwords/docs/basic-usage/configuring-notifications.md", "downloaded_repos/spatie_laravel-one-time-passwords/docs/basic-usage/consuming-one-time-passwords.md", "downloaded_repos/spatie_laravel-one-time-passwords/docs/basic-usage/creating-one-time-passwords.md", "downloaded_repos/spatie_laravel-one-time-passwords/docs/basic-usage/introducing-one-time-passwords.md", "downloaded_repos/spatie_laravel-one-time-passwords/docs/basic-usage/using-the-livewire-component.md", "downloaded_repos/spatie_laravel-one-time-passwords/docs/changelog.md", "downloaded_repos/spatie_laravel-one-time-passwords/docs/configuring-security/_index.md", "downloaded_repos/spatie_laravel-one-time-passwords/docs/configuring-security/allowing-multiple-passwords.md", "downloaded_repos/spatie_laravel-one-time-passwords/docs/configuring-security/configuring-password-format.md", "downloaded_repos/spatie_laravel-one-time-passwords/docs/configuring-security/enforcing-origin.md", "downloaded_repos/spatie_laravel-one-time-passwords/docs/configuring-security/introduction.md", "downloaded_repos/spatie_laravel-one-time-passwords/docs/configuring-security/setting-default-expiration-time.md", "downloaded_repos/spatie_laravel-one-time-passwords/docs/images/form-code.png", "downloaded_repos/spatie_laravel-one-time-passwords/docs/images/form-email.png", "downloaded_repos/spatie_laravel-one-time-passwords/docs/images/otp-notification.png", "downloaded_repos/spatie_laravel-one-time-passwords/docs/installation-setup.md", "downloaded_repos/spatie_laravel-one-time-passwords/docs/introduction.md", "downloaded_repos/spatie_laravel-one-time-passwords/docs/questions-issues.md", "downloaded_repos/spatie_laravel-one-time-passwords/docs/support-us.md", "downloaded_repos/spatie_laravel-one-time-passwords/phpstan.neon", "downloaded_repos/spatie_laravel-one-time-passwords/phpunit.xml.dist", "downloaded_repos/spatie_laravel-one-time-passwords/resources/lang/ar/form.php", "downloaded_repos/spatie_laravel-one-time-passwords/resources/lang/ar/notifications.php", "downloaded_repos/spatie_laravel-one-time-passwords/resources/lang/ar/validation.php", "downloaded_repos/spatie_laravel-one-time-passwords/resources/lang/en/form.php", "downloaded_repos/spatie_laravel-one-time-passwords/resources/lang/en/notifications.php", "downloaded_repos/spatie_laravel-one-time-passwords/resources/lang/en/validation.php", "downloaded_repos/spatie_laravel-one-time-passwords/resources/lang/fr/form.php", "downloaded_repos/spatie_laravel-one-time-passwords/resources/lang/fr/notifications.php", "downloaded_repos/spatie_laravel-one-time-passwords/resources/lang/fr/validation.php", "downloaded_repos/spatie_laravel-one-time-passwords/resources/lang/pt_BR/form.php", "downloaded_repos/spatie_laravel-one-time-passwords/resources/lang/pt_BR/notifications.php", "downloaded_repos/spatie_laravel-one-time-passwords/resources/lang/pt_BR/validation.php", "downloaded_repos/spatie_laravel-one-time-passwords/resources/lang/pt_PT/form.php", "downloaded_repos/spatie_laravel-one-time-passwords/resources/lang/pt_PT/notifications.php", "downloaded_repos/spatie_laravel-one-time-passwords/resources/lang/pt_PT/validation.php", "downloaded_repos/spatie_laravel-one-time-passwords/resources/views/livewire/email-form.blade.php", "downloaded_repos/spatie_laravel-one-time-passwords/resources/views/livewire/one-time-password-form.blade.php", "downloaded_repos/spatie_laravel-one-time-passwords/resources/views/mail.blade.php", "downloaded_repos/spatie_laravel-one-time-passwords/src/Actions/ConsumeOneTimePasswordAction.php", "downloaded_repos/spatie_laravel-one-time-passwords/src/Actions/CreateOneTimePasswordAction.php", "downloaded_repos/spatie_laravel-one-time-passwords/src/Enums/ConsumeOneTimePasswordResult.php", "downloaded_repos/spatie_laravel-one-time-passwords/src/Events/FailedToConsumeOneTimePassword.php", "downloaded_repos/spatie_laravel-one-time-passwords/src/Events/OneTimePasswordSuccessfullyConsumed.php", "downloaded_repos/spatie_laravel-one-time-passwords/src/Exceptions/InvalidActionClass.php", "downloaded_repos/spatie_laravel-one-time-passwords/src/Exceptions/InvalidConfig.php", "downloaded_repos/spatie_laravel-one-time-passwords/src/Livewire/OneTimePasswordComponent.php", "downloaded_repos/spatie_laravel-one-time-passwords/src/Models/Concerns/HasOneTimePasswords.php", "downloaded_repos/spatie_laravel-one-time-passwords/src/Models/OneTimePassword.php", "downloaded_repos/spatie_laravel-one-time-passwords/src/Notifications/OneTimePasswordNotification.php", "downloaded_repos/spatie_laravel-one-time-passwords/src/OneTimePasswordsServiceProvider.php", "downloaded_repos/spatie_laravel-one-time-passwords/src/Rules/OneTimePasswordRule.php", "downloaded_repos/spatie_laravel-one-time-passwords/src/Support/Config.php", "downloaded_repos/spatie_laravel-one-time-passwords/src/Support/OriginInspector/DefaultOriginEnforcer.php", "downloaded_repos/spatie_laravel-one-time-passwords/src/Support/OriginInspector/DoNotEnforceOrigin.php", "downloaded_repos/spatie_laravel-one-time-passwords/src/Support/OriginInspector/OriginEnforcer.php", "downloaded_repos/spatie_laravel-one-time-passwords/src/Support/PasswordGenerators/NumericOneTimePasswordGenerator.php", "downloaded_repos/spatie_laravel-one-time-passwords/src/Support/PasswordGenerators/OneTimePasswordGenerator.php"], "skipped": [{"path": "downloaded_repos/spatie_laravel-one-time-passwords/.github/workflows/run-tests.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/spatie_laravel-one-time-passwords/src/Models/OneTimePassword.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/spatie_laravel-one-time-passwords/tests/ArchTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spatie_laravel-one-time-passwords/tests/ConfigTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spatie_laravel-one-time-passwords/tests/Livewire/OneTimePasswordComponentTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spatie_laravel-one-time-passwords/tests/Notifications/OneTimePasswordNotificationClassTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spatie_laravel-one-time-passwords/tests/OneTimePasswordsTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spatie_laravel-one-time-passwords/tests/Pest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spatie_laravel-one-time-passwords/tests/TestSupport/Models/User.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/spatie_laravel-one-time-passwords/tests/TestSupport/TestCase.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.920785903930664, "profiling_times": {"config_time": 5.907390594482422, "core_time": 2.743307113647461, "ignores_time": 0.0018422603607177734, "total_time": 8.6534264087677}, "parsing_time": {"total_time": 0.35862112045288086, "per_file_time": {"mean": 0.007471273342768351, "std_dev": 9.387502440451181e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.913097620010376, "per_file_time": {"mean": 0.004266811308459703, "std_dev": 9.717289820128155e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.16673564910888672, "per_file_and_rule_time": {"mean": 0.0005933652993198814, "std_dev": 2.0444498464452255e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.0, "per_def_and_rule_time": {"mean": 0.0, "std_dev": 0.0}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}