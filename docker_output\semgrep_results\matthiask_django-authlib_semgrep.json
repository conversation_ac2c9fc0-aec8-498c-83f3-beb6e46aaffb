{"version": "1.130.0", "results": [{"check_id": "python.django.security.audit.unvalidated-password.unvalidated-password", "path": "downloaded_repos/matthiask_django-authlib/authlib/base_user.py", "start": {"line": 12, "col": 9, "offset": 412}, "end": {"line": 12, "col": 36, "offset": 439}, "extra": {"message": "The password on 'user' is being set without validating the password. Call django.contrib.auth.password_validation.validate_password() with validation functions before setting the password. See https://docs.djangoproject.com/en/3.0/topics/auth/passwords/ for more information.", "fix": "if django.contrib.auth.password_validation.validate_password(password, user=user):\n            user.set_password(password)", "metadata": {"cwe": ["CWE-521: Weak Password Requirements"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://docs.djangoproject.com/en/3.0/topics/auth/passwords/#module-django.contrib.auth.password_validation"], "category": "security", "technology": ["django"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/python.django.security.audit.unvalidated-password.unvalidated-password", "shortlink": "https://sg.run/OPBL"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.secure-cookies.django-secure-set-cookie", "path": "downloaded_repos/matthiask_django-authlib/authlib/views.py", "start": {"line": 31, "col": 13, "offset": 1071}, "end": {"line": 31, "col": 73, "offset": 1131}, "extra": {"message": "Django cookies should be handled securely by setting secure=True, httponly=True, and samesite='Lax' in response.set_cookie(...). If your situation calls for different settings, explicitly disable the setting. If you want to send the cookie over http, set secure=False. If you want to let client-side JavaScript read the cookie, set httponly=False. If you want to attach cookies to requests for external sites, set samesite=None.", "metadata": {"cwe": ["CWE-614: <PERSON><PERSON> in HTTPS Session Without 'Secure' Attribute"], "owasp": ["A05:2021 - Security Misconfiguration"], "asvs": {"control_id": "3.4 Missing <PERSON><PERSON>", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x12-V3-Session-management.md#v34-cookie-based-session-management", "section": "V3: Session Management Verification Requirements", "version": "4"}, "references": ["https://docs.djangoproject.com/en/3.0/ref/request-response/#django.http.HttpResponse.set_cookie", "https://semgrep.dev/blog/2020/bento-check-keeping-cookies-safe-in-flask/", "https://bento.dev/checks/flask/secure-set-cookie/"], "category": "security", "technology": ["django"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["<PERSON><PERSON>"], "source": "https://semgrep.dev/r/python.django.security.audit.secure-cookies.django-secure-set-cookie", "shortlink": "https://sg.run/x1WL"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/matthiask_django-authlib/.github/workflows/tests.yml", "start": {"line": 35, "col": 50, "offset": 765}, "end": {"line": 35, "col": 73, "offset": 788}}]], "message": "Syntax error at line downloaded_repos/matthiask_django-authlib/.github/workflows/tests.yml:35:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ matrix.python-version` was unexpected", "path": "downloaded_repos/matthiask_django-authlib/.github/workflows/tests.yml", "spans": [{"file": "downloaded_repos/matthiask_django-authlib/.github/workflows/tests.yml", "start": {"line": 35, "col": 50, "offset": 765}, "end": {"line": 35, "col": 73, "offset": 788}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/matthi<PERSON>_django-authlib/authlib/admin_oauth/templates/admin/login.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 39, "offset": 89}}, {"path": "downloaded_repos/matthi<PERSON>_django-authlib/authlib/admin_oauth/templates/admin/login.html", "start": {"line": 29, "col": 1, "offset": 0}, "end": {"line": 32, "col": 18, "offset": 53}}, {"path": "downloaded_repos/matthi<PERSON>_django-authlib/authlib/admin_oauth/templates/admin/login.html", "start": {"line": 36, "col": 1, "offset": 0}, "end": {"line": 36, "col": 23, "offset": 22}}]], "message": "Syntax error at line downloaded_repos/matthiask_django-authlib/authlib/admin_oauth/templates/admin/login.html:1:\n `{% extends \"admin/login.html\" %}\n\n{% load i18n %}\n\n{% block extrahead %}{{ block.super }}` was unexpected", "path": "downloaded_repos/matthi<PERSON>_django-authlib/authlib/admin_oauth/templates/admin/login.html", "spans": [{"file": "downloaded_repos/matthi<PERSON>_django-authlib/authlib/admin_oauth/templates/admin/login.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 39, "offset": 89}}, {"file": "downloaded_repos/matthi<PERSON>_django-authlib/authlib/admin_oauth/templates/admin/login.html", "start": {"line": 29, "col": 1, "offset": 0}, "end": {"line": 32, "col": 18, "offset": 53}}, {"file": "downloaded_repos/matthi<PERSON>_django-authlib/authlib/admin_oauth/templates/admin/login.html", "start": {"line": 36, "col": 1, "offset": 0}, "end": {"line": 36, "col": 23, "offset": 22}}]}], "paths": {"scanned": ["downloaded_repos/matthiask_django-authlib/.editorconfig", "downloaded_repos/matthiask_django-authlib/.github/workflows/tests.yml", "downloaded_repos/matthiask_django-authlib/.gitignore", "downloaded_repos/matthiask_django-authlib/.pre-commit-config.yaml", "downloaded_repos/matthiask_django-authlib/.readthedocs.yaml", "downloaded_repos/matthiask_django-authlib/CHANGELOG.rst", "downloaded_repos/matthiask_django-authlib/LICENSE", "downloaded_repos/matthiask_django-authlib/README.rst", "downloaded_repos/matthiask_django-authlib/authlib/__init__.py", "downloaded_repos/matthiask_django-authlib/authlib/_compat.py", "downloaded_repos/matthiask_django-authlib/authlib/admin_oauth/__init__.py", "downloaded_repos/matthiask_django-authlib/authlib/admin_oauth/locale/de/LC_MESSAGES/django.mo", "downloaded_repos/matthiask_django-authlib/authlib/admin_oauth/locale/de/LC_MESSAGES/django.po", "downloaded_repos/matthi<PERSON>_django-authlib/authlib/admin_oauth/templates/admin/login.html", "downloaded_repos/matthi<PERSON>_django-authlib/authlib/admin_oauth/urls.py", "downloaded_repos/matthi<PERSON>_django-authlib/authlib/admin_oauth/views.py", "downloaded_repos/matthiask_django-authlib/authlib/backends.py", "downloaded_repos/matthiask_django-authlib/authlib/base_user.py", "downloaded_repos/matthiask_django-authlib/authlib/email.py", "downloaded_repos/matthiask_django-authlib/authlib/facebook.py", "downloaded_repos/matthiask_django-authlib/authlib/google.py", "downloaded_repos/matthiask_django-authlib/authlib/little_auth/__init__.py", "downloaded_repos/matthiask_django-authlib/authlib/little_auth/admin.py", "downloaded_repos/matthiask_django-authlib/authlib/little_auth/apps.py", "downloaded_repos/matthiask_django-authlib/authlib/little_auth/locale/de/LC_MESSAGES/django.mo", "downloaded_repos/matthiask_django-authlib/authlib/little_auth/locale/de/LC_MESSAGES/django.po", "downloaded_repos/matthiask_django-authlib/authlib/little_auth/migrations/0001_initial.py", "downloaded_repos/matthiask_django-authlib/authlib/little_auth/migrations/0002_user_role.py", "downloaded_repos/matthiask_django-authlib/authlib/little_auth/migrations/__init__.py", "downloaded_repos/matthiask_django-authlib/authlib/little_auth/models.py", "downloaded_repos/matthiask_django-authlib/authlib/locale/de/LC_MESSAGES/django.mo", "downloaded_repos/matthiask_django-authlib/authlib/locale/de/LC_MESSAGES/django.po", "downloaded_repos/matthiask_django-authlib/authlib/roles.py", "downloaded_repos/matthiask_django-authlib/authlib/twitter.py", "downloaded_repos/matthiask_django-authlib/authlib/views.py", "downloaded_repos/matthiask_django-authlib/biome.json", "downloaded_repos/matthiask_django-authlib/docs/.gitignore", "downloaded_repos/matthiask_django-authlib/docs/Makefile", "downloaded_repos/matthiask_django-authlib/docs/conf.py", "downloaded_repos/matthiask_django-authlib/docs/index.rst", "downloaded_repos/matthiask_django-authlib/docs/make.bat", "downloaded_repos/matthiask_django-authlib/pyproject.toml", "downloaded_repos/matthiask_django-authlib/tox.ini"], "skipped": [{"path": "downloaded_repos/matthiask_django-authlib/.github/workflows/tests.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/matthi<PERSON>_django-authlib/authlib/admin_oauth/templates/admin/login.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/matthiask_django-authlib/tests/manage.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/matthiask_django-authlib/tests/testapp/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/matthiask_django-authlib/tests/testapp/settings.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/matthiask_django-authlib/tests/testapp/templates/404.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/matthiask_django-authlib/tests/testapp/templates/base.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/matthiask_django-authlib/tests/testapp/templates/empty.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/matthiask_django-authlib/tests/testapp/templates/registration/email_registration.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/matthiask_django-authlib/tests/testapp/templates/registration/email_registration_email.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/matthiask_django-authlib/tests/testapp/templates/registration/login.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/matthiask_django-authlib/tests/testapp/test_authlib.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/matthiask_django-authlib/tests/testapp/test_registration.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/matthiask_django-authlib/tests/testapp/test_roles.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/matthiask_django-authlib/tests/testapp/urls.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/matthiask_django-authlib/tests/testapp/views.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.8711400032043457, "profiling_times": {"config_time": 6.35536003112793, "core_time": 2.981184720993042, "ignores_time": 0.002016305923461914, "total_time": 9.339534282684326}, "parsing_time": {"total_time": 0.3752005100250244, "per_file_time": {"mean": 0.014430788847116323, "std_dev": 0.00015245086912800843}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 2.67992901802063, "per_file_time": {"mean": 0.023927937660898478, "std_dev": 0.0028816637141401433}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.6878249645233154, "per_file_and_rule_time": {"mean": 0.0027079723012728954, "std_dev": 5.1809916887753776e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.20426273345947266, "per_def_and_rule_time": {"mean": 0.0005520614417823584, "std_dev": 3.680860412154157e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089102016}, "engine_requested": "OSS", "skipped_rules": []}