{"version": "1.130.0", "results": [{"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/article.php", "start": {"line": 17, "col": 1, "offset": 242}, "end": {"line": 17, "col": 62, "offset": 303}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/article.php", "start": {"line": 85, "col": 3, "offset": 4080}, "end": {"line": 85, "col": 64, "offset": 4141}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/article.php", "start": {"line": 172, "col": 2, "offset": 7799}, "end": {"line": 172, "col": 64, "offset": 7861}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/article.php", "start": {"line": 214, "col": 2, "offset": 9403}, "end": {"line": 214, "col": 65, "offset": 9466}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/article.php", "start": {"line": 235, "col": 2, "offset": 10274}, "end": {"line": 235, "col": 64, "offset": 10336}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/article.php", "start": {"line": 312, "col": 7, "offset": 13099}, "end": {"line": 312, "col": 64, "offset": 13156}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/pluxml_PluXml/core/admin/article.php", "start": {"line": 378, "col": 19, "offset": 16072}, "end": {"line": 378, "col": 23, "offset": 16076}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/article.php", "start": {"line": 393, "col": 10, "offset": 16240}, "end": {"line": 393, "col": 71, "offset": 16301}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/article.php", "start": {"line": 608, "col": 1, "offset": 25248}, "end": {"line": 608, "col": 62, "offset": 25309}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/article.php", "start": {"line": 653, "col": 1, "offset": 26652}, "end": {"line": 653, "col": 59, "offset": 26710}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/auth.php", "start": {"line": 66, "col": 1, "offset": 1934}, "end": {"line": 66, "col": 59, "offset": 1992}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/pluxml_PluXml/core/admin/auth.php", "start": {"line": 101, "col": 91, "offset": 3369}, "end": {"line": 101, "col": 133, "offset": 3411}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/pluxml_PluXml/core/admin/auth.php", "start": {"line": 104, "col": 98, "offset": 3572}, "end": {"line": 104, "col": 140, "offset": 3614}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/auth.php", "start": {"line": 228, "col": 2, "offset": 7706}, "end": {"line": 228, "col": 60, "offset": 7764}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/auth.php", "start": {"line": 240, "col": 4, "offset": 8002}, "end": {"line": 240, "col": 60, "offset": 8058}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/auth.php", "start": {"line": 245, "col": 6, "offset": 8245}, "end": {"line": 245, "col": 72, "offset": 8311}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/auth.php", "start": {"line": 265, "col": 6, "offset": 9179}, "end": {"line": 265, "col": 69, "offset": 9242}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/auth.php", "start": {"line": 280, "col": 7, "offset": 9826}, "end": {"line": 280, "col": 75, "offset": 9894}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/auth.php", "start": {"line": 296, "col": 7, "offset": 10580}, "end": {"line": 296, "col": 72, "offset": 10645}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/auth.php", "start": {"line": 309, "col": 7, "offset": 10940}, "end": {"line": 309, "col": 80, "offset": 11013}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/auth.php", "start": {"line": 318, "col": 7, "offset": 11304}, "end": {"line": 318, "col": 77, "offset": 11374}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/auth.php", "start": {"line": 323, "col": 7, "offset": 11488}, "end": {"line": 324, "col": 3, "offset": 11559}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/auth.php", "start": {"line": 358, "col": 8, "offset": 12660}, "end": {"line": 358, "col": 59, "offset": 12711}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/auth.php", "start": {"line": 377, "col": 7, "offset": 13132}, "end": {"line": 377, "col": 65, "offset": 13190}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/categorie.php", "start": {"line": 16, "col": 1, "offset": 209}, "end": {"line": 16, "col": 63, "offset": 271}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/categorie.php", "start": {"line": 52, "col": 8, "offset": 1376}, "end": {"line": 52, "col": 66, "offset": 1434}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/pluxml_PluXml/core/admin/categorie.php", "start": {"line": 99, "col": 21, "offset": 3726}, "end": {"line": 99, "col": 25, "offset": 3730}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/categorie.php", "start": {"line": 130, "col": 8, "offset": 4841}, "end": {"line": 130, "col": 63, "offset": 4896}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/categorie.php", "start": {"line": 135, "col": 1, "offset": 4945}, "end": {"line": 135, "col": 60, "offset": 5004}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/categories.php", "start": {"line": 16, "col": 1, "offset": 214}, "end": {"line": 16, "col": 65, "offset": 278}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/categories.php", "start": {"line": 53, "col": 7, "offset": 1486}, "end": {"line": 53, "col": 67, "offset": 1546}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/categories.php", "start": {"line": 144, "col": 1, "offset": 4968}, "end": {"line": 144, "col": 62, "offset": 5029}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/comment.php", "start": {"line": 16, "col": 1, "offset": 215}, "end": {"line": 16, "col": 62, "offset": 276}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/comment.php", "start": {"line": 155, "col": 8, "offset": 5423}, "end": {"line": 155, "col": 65, "offset": 5480}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/comment.php", "start": {"line": 214, "col": 11, "offset": 8098}, "end": {"line": 214, "col": 65, "offset": 8152}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/comment.php", "start": {"line": 223, "col": 1, "offset": 8234}, "end": {"line": 223, "col": 59, "offset": 8292}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/comment_new.php", "start": {"line": 16, "col": 1, "offset": 205}, "end": {"line": 16, "col": 65, "offset": 269}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/comment_new.php", "start": {"line": 147, "col": 8, "offset": 4664}, "end": {"line": 147, "col": 68, "offset": 4724}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/comment_new.php", "start": {"line": 164, "col": 11, "offset": 5476}, "end": {"line": 164, "col": 68, "offset": 5533}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/comment_new.php", "start": {"line": 185, "col": 10, "offset": 7030}, "end": {"line": 185, "col": 71, "offset": 7091}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/comment_new.php", "start": {"line": 207, "col": 1, "offset": 7910}, "end": {"line": 207, "col": 62, "offset": 7971}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/comments.php", "start": {"line": 16, "col": 1, "offset": 223}, "end": {"line": 16, "col": 63, "offset": 285}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/comments.php", "start": {"line": 128, "col": 7, "offset": 4113}, "end": {"line": 128, "col": 65, "offset": 4171}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/comments.php", "start": {"line": 295, "col": 2, "offset": 9227}, "end": {"line": 295, "col": 67, "offset": 9292}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/comments.php", "start": {"line": 305, "col": 1, "offset": 9576}, "end": {"line": 305, "col": 60, "offset": 9635}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/foot.php", "start": {"line": 5, "col": 7, "offset": 29}, "end": {"line": 5, "col": 82, "offset": 104}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/get_oauth_token.php", "start": {"line": 75, "col": 3, "offset": 3098}, "end": {"line": 75, "col": 20, "offset": 3115}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/index.php", "start": {"line": 16, "col": 1, "offset": 211}, "end": {"line": 16, "col": 60, "offset": 270}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/index.php", "start": {"line": 214, "col": 1, "offset": 6487}, "end": {"line": 215, "col": 3, "offset": 6559}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/index.php", "start": {"line": 453, "col": 2, "offset": 14108}, "end": {"line": 453, "col": 64, "offset": 14170}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/index.php", "start": {"line": 489, "col": 1, "offset": 14954}, "end": {"line": 489, "col": 57, "offset": 15010}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/pluxml_PluXml/core/admin/js/functions.js", "start": {"line": 116, "col": 6, "offset": 3881}, "end": {"line": 116, "col": 99, "offset": 3974}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/pluxml_PluXml/core/admin/js/functions.js", "start": {"line": 119, "col": 6, "offset": 4026}, "end": {"line": 119, "col": 36, "offset": 4056}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/pluxml_PluXml/core/admin/js/mediasManager.js", "start": {"line": 61, "col": 4, "offset": 1542}, "end": {"line": 61, "col": 50, "offset": 1588}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/pluxml_PluXml/core/admin/js/multifiles.js", "start": {"line": 60, "col": 3, "offset": 2052}, "end": {"line": 60, "col": 60, "offset": 2109}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/medias.php", "start": {"line": 21, "col": 1, "offset": 378}, "end": {"line": 21, "col": 61, "offset": 438}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/medias.php", "start": {"line": 134, "col": 7, "offset": 4075}, "end": {"line": 134, "col": 80, "offset": 4148}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/pluxml_PluXml/core/admin/medias.php", "start": {"line": 246, "col": 29, "offset": 8313}, "end": {"line": 246, "col": 41, "offset": 8325}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/pluxml_PluXml/core/admin/medias.php", "start": {"line": 253, "col": 27, "offset": 8529}, "end": {"line": 253, "col": 37, "offset": 8539}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/pluxml_PluXml/core/admin/medias.php", "start": {"line": 266, "col": 27, "offset": 8923}, "end": {"line": 266, "col": 37, "offset": 8933}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/pluxml_PluXml/core/admin/medias.php", "start": {"line": 285, "col": 65, "offset": 9756}, "end": {"line": 285, "col": 70, "offset": 9761}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/medias.php", "start": {"line": 430, "col": 7, "offset": 14693}, "end": {"line": 430, "col": 83, "offset": 14769}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/medias.php", "start": {"line": 458, "col": 1, "offset": 15323}, "end": {"line": 458, "col": 58, "offset": 15380}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/parametres_affichage.php", "start": {"line": 62, "col": 8, "offset": 1833}, "end": {"line": 62, "col": 90, "offset": 1915}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/parametres_affichage.php", "start": {"line": 200, "col": 8, "offset": 7409}, "end": {"line": 200, "col": 87, "offset": 7488}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/parametres_affichage.php", "start": {"line": 207, "col": 1, "offset": 7559}, "end": {"line": 207, "col": 67, "offset": 7625}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/parametres_avances.php", "start": {"line": 60, "col": 8, "offset": 1636}, "end": {"line": 60, "col": 91, "offset": 1719}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/parametres_avances.php", "start": {"line": 320, "col": 8, "offset": 11592}, "end": {"line": 320, "col": 88, "offset": 11672}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/parametres_avances.php", "start": {"line": 325, "col": 1, "offset": 11703}, "end": {"line": 325, "col": 68, "offset": 11770}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/parametres_base.php", "start": {"line": 37, "col": 8, "offset": 811}, "end": {"line": 37, "col": 87, "offset": 890}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/parametres_base.php", "start": {"line": 129, "col": 8, "offset": 4461}, "end": {"line": 129, "col": 84, "offset": 4537}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/parametres_base.php", "start": {"line": 136, "col": 1, "offset": 4608}, "end": {"line": 136, "col": 64, "offset": 4671}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/parametres_edittpl.php", "start": {"line": 156, "col": 8, "offset": 4445}, "end": {"line": 156, "col": 73, "offset": 4510}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/parametres_edittpl.php", "start": {"line": 164, "col": 10, "offset": 4850}, "end": {"line": 164, "col": 72, "offset": 4912}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/parametres_edittpl.php", "start": {"line": 173, "col": 1, "offset": 4980}, "end": {"line": 173, "col": 67, "offset": 5046}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/parametres_infos.php", "start": {"line": 84, "col": 7, "offset": 2745}, "end": {"line": 84, "col": 84, "offset": 2822}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/parametres_plugincss.php", "start": {"line": 71, "col": 11, "offset": 2760}, "end": {"line": 71, "col": 67, "offset": 2816}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/parametres_plugins.php", "start": {"line": 211, "col": 7, "offset": 6987}, "end": {"line": 211, "col": 89, "offset": 7069}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/parametres_plugins.php", "start": {"line": 270, "col": 1, "offset": 8683}, "end": {"line": 270, "col": 67, "offset": 8749}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/parametres_themes.php", "start": {"line": 43, "col": 7, "offset": 1262}, "end": {"line": 43, "col": 87, "offset": 1342}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/parametres_themes.php", "start": {"line": 164, "col": 8, "offset": 4492}, "end": {"line": 164, "col": 85, "offset": 4569}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/parametres_themes.php", "start": {"line": 171, "col": 1, "offset": 4640}, "end": {"line": 171, "col": 65, "offset": 4704}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/parametres_users.php", "start": {"line": 42, "col": 8, "offset": 1200}, "end": {"line": 42, "col": 63, "offset": 1255}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/parametres_users.php", "start": {"line": 132, "col": 1, "offset": 4828}, "end": {"line": 132, "col": 57, "offset": 4884}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/prepend.php", "start": {"line": 72, "col": 1, "offset": 1975}, "end": {"line": 72, "col": 55, "offset": 2029}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/profil.php", "start": {"line": 16, "col": 1, "offset": 201}, "end": {"line": 16, "col": 61, "offset": 261}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/profil.php", "start": {"line": 46, "col": 8, "offset": 995}, "end": {"line": 46, "col": 64, "offset": 1051}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/profil.php", "start": {"line": 74, "col": 8, "offset": 2160}, "end": {"line": 74, "col": 61, "offset": 2213}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/profil.php", "start": {"line": 94, "col": 1, "offset": 2657}, "end": {"line": 94, "col": 58, "offset": 2714}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/statique.php", "start": {"line": 12, "col": 1, "offset": 162}, "end": {"line": 12, "col": 61, "offset": 222}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/statique.php", "start": {"line": 77, "col": 7, "offset": 2809}, "end": {"line": 77, "col": 63, "offset": 2865}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/statique.php", "start": {"line": 140, "col": 8, "offset": 6245}, "end": {"line": 140, "col": 61, "offset": 6298}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/statique.php", "start": {"line": 147, "col": 1, "offset": 6387}, "end": {"line": 147, "col": 58, "offset": 6444}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/statiques.php", "start": {"line": 16, "col": 1, "offset": 218}, "end": {"line": 16, "col": 62, "offset": 279}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/statiques.php", "start": {"line": 60, "col": 7, "offset": 1723}, "end": {"line": 60, "col": 81, "offset": 1797}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/statiques.php", "start": {"line": 174, "col": 1, "offset": 5966}, "end": {"line": 174, "col": 59, "offset": 6024}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/pluxml_PluXml/core/admin/theme/fonts/fontello/demo.html", "start": {"line": 3, "col": 26, "offset": 48}, "end": {"line": 3, "col": 140, "offset": 162}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/top.php", "start": {"line": 3, "col": 6, "offset": 65}, "end": {"line": 3, "col": 36, "offset": 95}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/top.php", "start": {"line": 20, "col": 2, "offset": 657}, "end": {"line": 20, "col": 59, "offset": 714}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/top.php", "start": {"line": 128, "col": 6, "offset": 6419}, "end": {"line": 128, "col": 61, "offset": 6474}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/top.php", "start": {"line": 142, "col": 2, "offset": 6667}, "end": {"line": 142, "col": 58, "offset": 6723}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/user.php", "start": {"line": 16, "col": 1, "offset": 209}, "end": {"line": 16, "col": 59, "offset": 267}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/user.php", "start": {"line": 54, "col": 8, "offset": 1407}, "end": {"line": 54, "col": 62, "offset": 1461}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/user.php", "start": {"line": 81, "col": 8, "offset": 2522}, "end": {"line": 81, "col": 59, "offset": 2573}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/admin/user.php", "start": {"line": 87, "col": 1, "offset": 2623}, "end": {"line": 87, "col": 56, "offset": 2678}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.admin.php", "start": {"line": 45, "col": 3, "offset": 1210}, "end": {"line": 45, "col": 58, "offset": 1265}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.admin.php", "start": {"line": 103, "col": 3, "offset": 2806}, "end": {"line": 103, "col": 66, "offset": 2869}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.admin.php", "start": {"line": 387, "col": 3, "offset": 10564}, "end": {"line": 387, "col": 57, "offset": 10618}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.admin.php", "start": {"line": 392, "col": 11, "offset": 10768}, "end": {"line": 392, "col": 32, "offset": 10789}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.admin.php", "start": {"line": 437, "col": 18, "offset": 12020}, "end": {"line": 437, "col": 27, "offset": 12029}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.admin.php", "start": {"line": 438, "col": 34, "offset": 12067}, "end": {"line": 438, "col": 43, "offset": 12076}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.admin.php", "start": {"line": 741, "col": 5, "offset": 22294}, "end": {"line": 741, "col": 66, "offset": 22355}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.admin.php", "start": {"line": 812, "col": 5, "offset": 24623}, "end": {"line": 812, "col": 63, "offset": 24681}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.admin.php", "start": {"line": 858, "col": 3, "offset": 25783}, "end": {"line": 858, "col": 57, "offset": 25837}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.admin.php", "start": {"line": 976, "col": 4, "offset": 29153}, "end": {"line": 976, "col": 67, "offset": 29216}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.admin.php", "start": {"line": 1012, "col": 5, "offset": 30570}, "end": {"line": 1012, "col": 71, "offset": 30636}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.admin.php", "start": {"line": 1079, "col": 5, "offset": 32906}, "end": {"line": 1079, "col": 68, "offset": 32969}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.admin.php", "start": {"line": 1117, "col": 3, "offset": 34265}, "end": {"line": 1117, "col": 62, "offset": 34324}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.admin.php", "start": {"line": 1143, "col": 6, "offset": 35109}, "end": {"line": 1143, "col": 23, "offset": 35126}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.admin.php", "start": {"line": 1183, "col": 6, "offset": 36997}, "end": {"line": 1183, "col": 71, "offset": 37062}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.admin.php", "start": {"line": 1230, "col": 5, "offset": 39084}, "end": {"line": 1230, "col": 67, "offset": 39146}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.admin.php", "start": {"line": 1336, "col": 3, "offset": 42797}, "end": {"line": 1336, "col": 61, "offset": 42855}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.admin.php", "start": {"line": 1465, "col": 3, "offset": 47700}, "end": {"line": 1465, "col": 63, "offset": 47760}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.admin.php", "start": {"line": 1503, "col": 6, "offset": 49217}, "end": {"line": 1503, "col": 26, "offset": 49237}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.admin.php", "start": {"line": 1523, "col": 4, "offset": 49712}, "end": {"line": 1523, "col": 64, "offset": 49772}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.admin.php", "start": {"line": 1547, "col": 4, "offset": 50383}, "end": {"line": 1547, "col": 66, "offset": 50445}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.admin.php", "start": {"line": 1554, "col": 5, "offset": 50732}, "end": {"line": 1554, "col": 71, "offset": 50798}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.admin.php", "start": {"line": 1678, "col": 33, "offset": 54829}, "end": {"line": 1678, "col": 50, "offset": 54846}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.audit.openssl-decrypt-validate.openssl-decrypt-validate", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.encrypt.php", "start": {"line": 70, "col": 5, "offset": 1863}, "end": {"line": 76, "col": 6, "offset": 1991}, "extra": {"message": "The function `openssl_decrypt` returns either a string of the decrypted data on success or `false` on failure. If the failure case is not handled, this could lead to undefined behavior in your application. Please handle the case where `openssl_decrypt` returns `false`.", "metadata": {"references": ["https://www.php.net/manual/en/function.openssl-decrypt.php"], "cwe": ["CWE-252: Unchecked Return Value"], "owasp": ["A02:2021 - Cryptographic Failures"], "technology": ["php", "openssl"], "category": "security", "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.audit.openssl-decrypt-validate.openssl-decrypt-validate", "shortlink": "https://sg.run/kzn7"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.feed.php", "start": {"line": 51, "col": 3, "offset": 1258}, "end": {"line": 51, "col": 57, "offset": 1312}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.feed.php", "start": {"line": 161, "col": 3, "offset": 5754}, "end": {"line": 161, "col": 63, "offset": 5814}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.feed.php", "start": {"line": 192, "col": 3, "offset": 6811}, "end": {"line": 192, "col": 60, "offset": 6868}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.feed.php", "start": {"line": 224, "col": 3, "offset": 8097}, "end": {"line": 224, "col": 59, "offset": 8153}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.feed.php", "start": {"line": 328, "col": 5, "offset": 11779}, "end": {"line": 328, "col": 64, "offset": 11838}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.feed.php", "start": {"line": 392, "col": 6, "offset": 14626}, "end": {"line": 392, "col": 65, "offset": 14685}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.feed.php", "start": {"line": 446, "col": 5, "offset": 17010}, "end": {"line": 446, "col": 66, "offset": 17071}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.medias.php", "start": {"line": 195, "col": 8, "offset": 6523}, "end": {"line": 195, "col": 44, "offset": 6559}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.medias.php", "start": {"line": 200, "col": 6, "offset": 6684}, "end": {"line": 200, "col": 53, "offset": 6731}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.medias.php", "start": {"line": 204, "col": 6, "offset": 6866}, "end": {"line": 204, "col": 46, "offset": 6906}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.medias.php", "start": {"line": 243, "col": 10, "offset": 7818}, "end": {"line": 243, "col": 25, "offset": 7833}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.motor.php", "start": {"line": 132, "col": 3, "offset": 4582}, "end": {"line": 132, "col": 69, "offset": 4648}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.motor.php", "start": {"line": 148, "col": 3, "offset": 5286}, "end": {"line": 148, "col": 58, "offset": 5341}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.motor.php", "start": {"line": 421, "col": 3, "offset": 16442}, "end": {"line": 421, "col": 64, "offset": 16503}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.motor.php", "start": {"line": 432, "col": 3, "offset": 16666}, "end": {"line": 432, "col": 57, "offset": 16720}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.motor.php", "start": {"line": 525, "col": 6, "offset": 19202}, "end": {"line": 525, "col": 75, "offset": 19271}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.motor.php", "start": {"line": 535, "col": 7, "offset": 19704}, "end": {"line": 535, "col": 83, "offset": 19780}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.motor.php", "start": {"line": 574, "col": 3, "offset": 21161}, "end": {"line": 574, "col": 61, "offset": 21219}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.motor.php", "start": {"line": 758, "col": 5, "offset": 29532}, "end": {"line": 758, "col": 64, "offset": 29591}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.motor.php", "start": {"line": 821, "col": 5, "offset": 31985}, "end": {"line": 821, "col": 63, "offset": 32043}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.motor.php", "start": {"line": 870, "col": 5, "offset": 33367}, "end": {"line": 870, "col": 59, "offset": 33421}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.motor.php", "start": {"line": 1052, "col": 4, "offset": 39167}, "end": {"line": 1052, "col": 62, "offset": 39225}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.motor.php", "start": {"line": 1139, "col": 3, "offset": 42080}, "end": {"line": 1139, "col": 65, "offset": 42142}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.motor.php", "start": {"line": 1259, "col": 7, "offset": 46294}, "end": {"line": 1259, "col": 87, "offset": 46374}, "extra": {"message": "Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues", "metadata": {"cwe": ["CWE-697: Incorrect Comparison"], "references": ["https://www.php.net/manual/en/types.comparisons.php", "https://web.archive.org/web/20210430183236/https://www.whitehatsec.com/blog/magic-hashes/"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.md5-loose-equality.md5-loose-equality", "shortlink": "https://sg.run/Do4G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.motor.php", "start": {"line": 1285, "col": 39, "offset": 46962}, "end": {"line": 1285, "col": 83, "offset": 47006}, "extra": {"message": "Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues", "metadata": {"cwe": ["CWE-697: Incorrect Comparison"], "references": ["https://www.php.net/manual/en/types.comparisons.php", "https://web.archive.org/web/20210430183236/https://www.whitehatsec.com/blog/magic-hashes/"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.md5-loose-equality.md5-loose-equality", "shortlink": "https://sg.run/Do4G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.motor.php", "start": {"line": 1355, "col": 3, "offset": 49842}, "end": {"line": 1355, "col": 66, "offset": 49905}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.plugins.php", "start": {"line": 266, "col": 10, "offset": 8509}, "end": {"line": 266, "col": 45, "offset": 8544}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.plugins.php", "start": {"line": 401, "col": 40, "offset": 12606}, "end": {"line": 401, "col": 57, "offset": 12623}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.plugins.php", "start": {"line": 483, "col": 11, "offset": 14746}, "end": {"line": 483, "col": 26, "offset": 14761}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.plugins.php", "start": {"line": 513, "col": 4, "offset": 15585}, "end": {"line": 513, "col": 28, "offset": 15609}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.plugins.php", "start": {"line": 784, "col": 5, "offset": 23673}, "end": {"line": 784, "col": 69, "offset": 23737}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.show.php", "start": {"line": 67, "col": 3, "offset": 2241}, "end": {"line": 67, "col": 67, "offset": 2305}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.show.php", "start": {"line": 1345, "col": 5, "offset": 44709}, "end": {"line": 1345, "col": 78, "offset": 44782}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.show.php", "start": {"line": 1959, "col": 4, "offset": 65366}, "end": {"line": 1959, "col": 72, "offset": 65434}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.show.php", "start": {"line": 2672, "col": 4, "offset": 88672}, "end": {"line": 2672, "col": 21, "offset": 88689}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.utils.php", "start": {"line": 743, "col": 5, "offset": 26521}, "end": {"line": 743, "col": 22, "offset": 26538}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.php-permissive-cors.php-permissive-cors", "path": "downloaded_repos/pluxml_PluXml/feed.php", "start": {"line": 16, "col": 1, "offset": 454}, "end": {"line": 16, "col": 41, "offset": 494}, "extra": {"message": "Access-Control-Allow-Origin response header is set to \"*\". This will disable CORS Same Origin Policy restrictions.", "metadata": {"references": ["https://developer.mozilla.org/ru/docs/Web/HTTP/Headers/Access-Control-Allow-Origin"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "cwe": ["CWE-346: Origin Validation Error"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/php.lang.security.php-permissive-cors.php-permissive-cors", "shortlink": "https://sg.run/y1XR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/feed.php", "start": {"line": 24, "col": 1, "offset": 713}, "end": {"line": 24, "col": 51, "offset": 763}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/feed.php", "start": {"line": 44, "col": 1, "offset": 1152}, "end": {"line": 44, "col": 49, "offset": 1200}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/index.php", "start": {"line": 20, "col": 1, "offset": 407}, "end": {"line": 20, "col": 48, "offset": 454}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/index.php", "start": {"line": 31, "col": 1, "offset": 671}, "end": {"line": 31, "col": 53, "offset": 723}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/index.php", "start": {"line": 59, "col": 1, "offset": 1465}, "end": {"line": 59, "col": 55, "offset": 1519}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/index.php", "start": {"line": 63, "col": 1, "offset": 1617}, "end": {"line": 63, "col": 55, "offset": 1671}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/index.php", "start": {"line": 67, "col": 1, "offset": 1772}, "end": {"line": 67, "col": 51, "offset": 1822}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/sitemap.php", "start": {"line": 57, "col": 1, "offset": 1517}, "end": {"line": 57, "col": 57, "offset": 1573}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/sitemap.php", "start": {"line": 71, "col": 1, "offset": 2028}, "end": {"line": 71, "col": 60, "offset": 2087}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/sitemap.php", "start": {"line": 100, "col": 1, "offset": 3201}, "end": {"line": 100, "col": 58, "offset": 3258}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/pluxml_PluXml/sitemap.php", "start": {"line": 109, "col": 1, "offset": 3359}, "end": {"line": 109, "col": 53, "offset": 3411}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/pluxml_PluXml/themes/defaut/js/script.js", "start": {"line": 30, "col": 5, "offset": 870}, "end": {"line": 30, "col": 42, "offset": 907}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/pluxml_PluXml/themes/defaut/js/script.js", "start": {"line": 34, "col": 5, "offset": 1011}, "end": {"line": 34, "col": 79, "offset": 1085}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/pluxml_PluXml/themes/defaut/js/script.js", "start": {"line": 59, "col": 4, "offset": 1811}, "end": {"line": 59, "col": 55, "offset": 1862}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/pluxml_PluXml/update/class.plx.updater.php", "start": {"line": 203, "col": 7, "offset": 5007}, "end": {"line": 203, "col": 24, "offset": 5024}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/pluxml_PluXml/update/class.plx.updater.php", "start": {"line": 345, "col": 10, "offset": 8929}, "end": {"line": 345, "col": 25, "offset": 8944}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/pluxml_PluXml/update/update_5.0.php", "start": {"line": 85, "col": 5, "offset": 2380}, "end": {"line": 85, "col": 22, "offset": 2397}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/pluxml_PluXml/update/update_5.0.php", "start": {"line": 162, "col": 3, "offset": 4314}, "end": {"line": 162, "col": 45, "offset": 4356}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/pluxml_PluXml/update/update_5.1.4.php", "start": {"line": 62, "col": 5, "offset": 1698}, "end": {"line": 62, "col": 22, "offset": 1715}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/pluxml_PluXml/update/update_5.1.4.php", "start": {"line": 71, "col": 4, "offset": 1936}, "end": {"line": 71, "col": 21, "offset": 1953}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/pluxml_PluXml/update/update_5.1.php", "start": {"line": 158, "col": 8, "offset": 4644}, "end": {"line": 158, "col": 52, "offset": 4688}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/pluxml_PluXml/update/update_5.5.php", "start": {"line": 37, "col": 7, "offset": 930}, "end": {"line": 37, "col": 30, "offset": 953}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/pluxml_PluXml/update/update_5.5.php", "start": {"line": 72, "col": 6, "offset": 1788}, "end": {"line": 72, "col": 23, "offset": 1805}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/pluxml_PluXml/update/update_5.8.7.php", "start": {"line": 26, "col": 5, "offset": 716}, "end": {"line": 26, "col": 25, "offset": 736}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/pluxml_PluXml/.github/FUNDING.yml", "downloaded_repos/pluxml_PluXml/.github/SECURITY.md", "downloaded_repos/pluxml_PluXml/.gitignore", "downloaded_repos/pluxml_PluXml/LICENSE", "downloaded_repos/pluxml_PluXml/README.md", "downloaded_repos/pluxml_PluXml/config.php", "downloaded_repos/pluxml_PluXml/core/.htaccess", "downloaded_repos/pluxml_PluXml/core/admin/.htaccess", "downloaded_repos/pluxml_PluXml/core/admin/article.php", "downloaded_repos/pluxml_PluXml/core/admin/auth.php", "downloaded_repos/pluxml_PluXml/core/admin/categorie.php", "downloaded_repos/pluxml_PluXml/core/admin/categories.php", "downloaded_repos/pluxml_PluXml/core/admin/comment.php", "downloaded_repos/pluxml_PluXml/core/admin/comment_new.php", "downloaded_repos/pluxml_PluXml/core/admin/comments.php", "downloaded_repos/pluxml_PluXml/core/admin/foot.php", "downloaded_repos/pluxml_PluXml/core/admin/get_oauth_token.php", "downloaded_repos/pluxml_PluXml/core/admin/index.php", "downloaded_repos/pluxml_PluXml/core/admin/js/drag-and-drop.js", "downloaded_repos/pluxml_PluXml/core/admin/js/functions.js", "downloaded_repos/pluxml_PluXml/core/admin/js/medias.js", "downloaded_repos/pluxml_PluXml/core/admin/js/mediasManager.js", "downloaded_repos/pluxml_PluXml/core/admin/js/multifiles.js", "downloaded_repos/pluxml_PluXml/core/admin/js/visual.js", "downloaded_repos/pluxml_PluXml/core/admin/medias.php", "downloaded_repos/pluxml_PluXml/core/admin/parametres_affichage.php", "downloaded_repos/pluxml_PluXml/core/admin/parametres_avances.php", "downloaded_repos/pluxml_PluXml/core/admin/parametres_base.php", "downloaded_repos/pluxml_PluXml/core/admin/parametres_edittpl.php", "downloaded_repos/pluxml_PluXml/core/admin/parametres_help.php", "downloaded_repos/pluxml_PluXml/core/admin/parametres_infos.php", "downloaded_repos/pluxml_PluXml/core/admin/parametres_plugin.php", "downloaded_repos/pluxml_PluXml/core/admin/parametres_plugincss.php", "downloaded_repos/pluxml_PluXml/core/admin/parametres_plugins.php", "downloaded_repos/pluxml_PluXml/core/admin/parametres_themes.php", "downloaded_repos/pluxml_PluXml/core/admin/parametres_users.php", "downloaded_repos/pluxml_PluXml/core/admin/plugin.php", "downloaded_repos/pluxml_PluXml/core/admin/prepend.php", "downloaded_repos/pluxml_PluXml/core/admin/profil.php", "downloaded_repos/pluxml_PluXml/core/admin/statique.php", "downloaded_repos/pluxml_PluXml/core/admin/statiques.php", "downloaded_repos/pluxml_PluXml/core/admin/theme/fonts/fontello/LICENSE.txt", "downloaded_repos/pluxml_PluXml/core/admin/theme/fonts/fontello/README.txt", "downloaded_repos/pluxml_PluXml/core/admin/theme/fonts/fontello/config.json", "downloaded_repos/pluxml_PluXml/core/admin/theme/fonts/fontello/css/animation.css", "downloaded_repos/pluxml_PluXml/core/admin/theme/fonts/fontello/css/fontello-codes.css", "downloaded_repos/pluxml_PluXml/core/admin/theme/fonts/fontello/css/fontello-embedded.css", "downloaded_repos/pluxml_PluXml/core/admin/theme/fonts/fontello/css/fontello-ie7-codes.css", "downloaded_repos/pluxml_PluXml/core/admin/theme/fonts/fontello/css/fontello-ie7.css", "downloaded_repos/pluxml_PluXml/core/admin/theme/fonts/fontello/css/fontello.css", "downloaded_repos/pluxml_PluXml/core/admin/theme/fonts/fontello/demo.html", "downloaded_repos/pluxml_PluXml/core/admin/theme/fonts/fontello/font/fontello.eot", "downloaded_repos/pluxml_PluXml/core/admin/theme/fonts/fontello/font/fontello.svg", "downloaded_repos/pluxml_PluXml/core/admin/theme/fonts/fontello/font/fontello.ttf", "downloaded_repos/pluxml_PluXml/core/admin/theme/fonts/fontello/font/fontello.woff", "downloaded_repos/pluxml_PluXml/core/admin/theme/fonts/fontello/font/fontello.woff2", "downloaded_repos/pluxml_PluXml/core/admin/theme/fonts/fontello.css", "downloaded_repos/pluxml_PluXml/core/admin/theme/fonts/fontello.eot", "downloaded_repos/pluxml_PluXml/core/admin/theme/fonts/fontello.svg", "downloaded_repos/pluxml_PluXml/core/admin/theme/fonts/fontello.ttf", "downloaded_repos/pluxml_PluXml/core/admin/theme/fonts/fontello.woff", "downloaded_repos/pluxml_PluXml/core/admin/theme/fonts/fontello.woff2", "downloaded_repos/pluxml_PluXml/core/admin/theme/images/arrow-down.png", "downloaded_repos/pluxml_PluXml/core/admin/theme/images/date.png", "downloaded_repos/pluxml_PluXml/core/admin/theme/images/file.png", "downloaded_repos/pluxml_PluXml/core/admin/theme/images/icon_plugin.png", "downloaded_repos/pluxml_PluXml/core/admin/theme/images/index.html", "downloaded_repos/pluxml_PluXml/core/admin/theme/images/pluxml.png", "downloaded_repos/pluxml_PluXml/core/admin/theme/images/rss.svg", "downloaded_repos/pluxml_PluXml/core/admin/theme/images/search.png", "downloaded_repos/pluxml_PluXml/core/admin/theme/images/theme.png", "downloaded_repos/pluxml_PluXml/core/admin/theme/images/user.png", "downloaded_repos/pluxml_PluXml/core/admin/theme/index.html", "downloaded_repos/pluxml_PluXml/core/admin/theme/plucss.css", "downloaded_repos/pluxml_PluXml/core/admin/theme/plucss.min.css", "downloaded_repos/pluxml_PluXml/core/admin/theme/theme.css", "downloaded_repos/pluxml_PluXml/core/admin/theme/theme.min.css", "downloaded_repos/pluxml_PluXml/core/admin/top.php", "downloaded_repos/pluxml_PluXml/core/admin/user.php", "downloaded_repos/pluxml_PluXml/core/composer-56.json", "downloaded_repos/pluxml_PluXml/core/composer-72.json", "downloaded_repos/pluxml_PluXml/core/composer-81.json", "downloaded_repos/pluxml_PluXml/core/composer.json", "downloaded_repos/pluxml_PluXml/core/composer.lock", "downloaded_repos/pluxml_PluXml/core/index.html", "downloaded_repos/pluxml_PluXml/core/lang/de/admin.php", "downloaded_repos/pluxml_PluXml/core/lang/de/core.php", "downloaded_repos/pluxml_PluXml/core/lang/de/install.php", "downloaded_repos/pluxml_PluXml/core/lang/de/update.php", "downloaded_repos/pluxml_PluXml/core/lang/en/admin.php", "downloaded_repos/pluxml_PluXml/core/lang/en/core.php", "downloaded_repos/pluxml_PluXml/core/lang/en/install.php", "downloaded_repos/pluxml_PluXml/core/lang/en/update.php", "downloaded_repos/pluxml_PluXml/core/lang/es/admin.php", "downloaded_repos/pluxml_PluXml/core/lang/es/core.php", "downloaded_repos/pluxml_PluXml/core/lang/es/install.php", "downloaded_repos/pluxml_PluXml/core/lang/es/update.php", "downloaded_repos/pluxml_PluXml/core/lang/fr/admin.php", "downloaded_repos/pluxml_PluXml/core/lang/fr/core.php", "downloaded_repos/pluxml_PluXml/core/lang/fr/install.php", "downloaded_repos/pluxml_PluXml/core/lang/fr/update.php", "downloaded_repos/pluxml_PluXml/core/lang/index.html", "downloaded_repos/pluxml_PluXml/core/lang/it/admin.php", "downloaded_repos/pluxml_PluXml/core/lang/it/core.php", "downloaded_repos/pluxml_PluXml/core/lang/it/install.php", "downloaded_repos/pluxml_PluXml/core/lang/it/update.php", "downloaded_repos/pluxml_PluXml/core/lang/nl/admin.php", "downloaded_repos/pluxml_PluXml/core/lang/nl/core.php", "downloaded_repos/pluxml_PluXml/core/lang/nl/install.php", "downloaded_repos/pluxml_PluXml/core/lang/nl/update.php", "downloaded_repos/pluxml_PluXml/core/lang/oc/admin.php", "downloaded_repos/pluxml_PluXml/core/lang/oc/core.php", "downloaded_repos/pluxml_PluXml/core/lang/oc/install.php", "downloaded_repos/pluxml_PluXml/core/lang/oc/update.php", "downloaded_repos/pluxml_PluXml/core/lang/pl/admin.php", "downloaded_repos/pluxml_PluXml/core/lang/pl/core.php", "downloaded_repos/pluxml_PluXml/core/lang/pl/install.php", "downloaded_repos/pluxml_PluXml/core/lang/pl/update.php", "downloaded_repos/pluxml_PluXml/core/lang/pt/admin.php", "downloaded_repos/pluxml_PluXml/core/lang/pt/core.php", "downloaded_repos/pluxml_PluXml/core/lang/pt/install.php", "downloaded_repos/pluxml_PluXml/core/lang/pt/update.php", "downloaded_repos/pluxml_PluXml/core/lang/ro/admin.php", "downloaded_repos/pluxml_PluXml/core/lang/ro/core.php", "downloaded_repos/pluxml_PluXml/core/lang/ro/install.php", "downloaded_repos/pluxml_PluXml/core/lang/ro/update.php", "downloaded_repos/pluxml_PluXml/core/lang/ru/admin.php", "downloaded_repos/pluxml_PluXml/core/lang/ru/core.php", "downloaded_repos/pluxml_PluXml/core/lang/ru/install.php", "downloaded_repos/pluxml_PluXml/core/lang/ru/update.php", "downloaded_repos/pluxml_PluXml/core/lib/.htaccess", "downloaded_repos/pluxml_PluXml/core/lib/class.plx.admin.php", "downloaded_repos/pluxml_PluXml/core/lib/class.plx.capcha.php", "downloaded_repos/pluxml_PluXml/core/lib/class.plx.date.php", "downloaded_repos/pluxml_PluXml/core/lib/class.plx.encrypt.php", "downloaded_repos/pluxml_PluXml/core/lib/class.plx.erreur.php", "downloaded_repos/pluxml_PluXml/core/lib/class.plx.feed.php", "downloaded_repos/pluxml_PluXml/core/lib/class.plx.glob.php", "downloaded_repos/pluxml_PluXml/core/lib/class.plx.medias.php", "downloaded_repos/pluxml_PluXml/core/lib/class.plx.motor.php", "downloaded_repos/pluxml_PluXml/core/lib/class.plx.msg.php", "downloaded_repos/pluxml_PluXml/core/lib/class.plx.plugins.php", "downloaded_repos/pluxml_PluXml/core/lib/class.plx.record.php", "downloaded_repos/pluxml_PluXml/core/lib/class.plx.show.php", "downloaded_repos/pluxml_PluXml/core/lib/class.plx.template.php", "downloaded_repos/pluxml_PluXml/core/lib/class.plx.themes.php", "downloaded_repos/pluxml_PluXml/core/lib/class.plx.timezones.php", "downloaded_repos/pluxml_PluXml/core/lib/class.plx.token.php", "downloaded_repos/pluxml_PluXml/core/lib/class.plx.utils.php", "downloaded_repos/pluxml_PluXml/core/lib/config.php", "downloaded_repos/pluxml_PluXml/core/lib/index.html", "downloaded_repos/pluxml_PluXml/core/lisezmoi.txt", "downloaded_repos/pluxml_PluXml/core/templates/email-lostpassword-de.xml", "downloaded_repos/pluxml_PluXml/core/templates/email-lostpassword-en.xml", "downloaded_repos/pluxml_PluXml/core/templates/email-lostpassword-es.xml", "downloaded_repos/pluxml_PluXml/core/templates/email-lostpassword-fr.xml", "downloaded_repos/pluxml_PluXml/core/templates/email-lostpassword-it.xml", "downloaded_repos/pluxml_PluXml/core/templates/email-lostpassword-nl.xml", "downloaded_repos/pluxml_PluXml/core/templates/email-lostpassword-oc.xml", "downloaded_repos/pluxml_PluXml/core/templates/email-lostpassword-pl.xml", "downloaded_repos/pluxml_PluXml/core/templates/email-lostpassword-pt.xml", "downloaded_repos/pluxml_PluXml/core/templates/email-lostpassword-ro.xml", "downloaded_repos/pluxml_PluXml/core/templates/email-lostpassword-ru.xml", "downloaded_repos/pluxml_PluXml/core/templates/email-lostpassword.xml", "downloaded_repos/pluxml_PluXml/core/templates/install-article.txt", "downloaded_repos/pluxml_PluXml/core/templates/install-page.txt", "downloaded_repos/pluxml_PluXml/data/.htaccess", "downloaded_repos/pluxml_PluXml/favicon.png", "downloaded_repos/pluxml_PluXml/feed.php", "downloaded_repos/pluxml_PluXml/index.php", "downloaded_repos/pluxml_PluXml/install.php", "downloaded_repos/pluxml_PluXml/plugins/.htaccess", "downloaded_repos/pluxml_PluXml/plugins/index.html", "downloaded_repos/pluxml_PluXml/readme/.htaccess", "downloaded_repos/pluxml_PluXml/readme/AUTHORS", "downloaded_repos/pluxml_PluXml/readme/CHANGELOG.md", "downloaded_repos/pluxml_PluXml/sitemap.php", "downloaded_repos/pluxml_PluXml/themes/.htaccess", "downloaded_repos/pluxml_PluXml/themes/defaut/archives.php", "downloaded_repos/pluxml_PluXml/themes/defaut/article-full-width.php", "downloaded_repos/pluxml_PluXml/themes/defaut/article.php", "downloaded_repos/pluxml_PluXml/themes/defaut/categorie-full-width.php", "downloaded_repos/pluxml_PluXml/themes/defaut/categorie.php", "downloaded_repos/pluxml_PluXml/themes/defaut/commentaires.php", "downloaded_repos/pluxml_PluXml/themes/defaut/comments.php", "downloaded_repos/pluxml_PluXml/themes/defaut/css/minify.php", "downloaded_repos/pluxml_PluXml/themes/defaut/css/plucss.css", "downloaded_repos/pluxml_PluXml/themes/defaut/css/plucss.min.css", "downloaded_repos/pluxml_PluXml/themes/defaut/css/print.css", "downloaded_repos/pluxml_PluXml/themes/defaut/css/theme.css", "downloaded_repos/pluxml_PluXml/themes/defaut/css/theme.min.css", "downloaded_repos/pluxml_PluXml/themes/defaut/erreur.php", "downloaded_repos/pluxml_PluXml/themes/defaut/fonts/Apache-License.txt", "downloaded_repos/pluxml_PluXml/themes/defaut/fonts/OpenSans-Regular-webfont.eot", "downloaded_repos/pluxml_PluXml/themes/defaut/fonts/OpenSans-Regular-webfont.svg", "downloaded_repos/pluxml_PluXml/themes/defaut/fonts/OpenSans-Regular-webfont.ttf", "downloaded_repos/pluxml_PluXml/themes/defaut/fonts/OpenSans-Regular-webfont.woff", "downloaded_repos/pluxml_PluXml/themes/defaut/footer.php", "downloaded_repos/pluxml_PluXml/themes/defaut/header.php", "downloaded_repos/pluxml_PluXml/themes/defaut/home.php", "downloaded_repos/pluxml_PluXml/themes/defaut/img/bg.jpg", "downloaded_repos/pluxml_PluXml/themes/defaut/img/favicon.png", "downloaded_repos/pluxml_PluXml/themes/defaut/img/index.html", "downloaded_repos/pluxml_PluXml/themes/defaut/img/pixel-transparent.png", "downloaded_repos/pluxml_PluXml/themes/defaut/img/pluxml-logo-black.png", "downloaded_repos/pluxml_PluXml/themes/defaut/img/rss.svg", "downloaded_repos/pluxml_PluXml/themes/defaut/img/up.svg", "downloaded_repos/pluxml_PluXml/themes/defaut/img/user.svg", "downloaded_repos/pluxml_PluXml/themes/defaut/index.html", "downloaded_repos/pluxml_PluXml/themes/defaut/infos.xml", "downloaded_repos/pluxml_PluXml/themes/defaut/js/script.js", "downloaded_repos/pluxml_PluXml/themes/defaut/lang/de.php", "downloaded_repos/pluxml_PluXml/themes/defaut/lang/en.php", "downloaded_repos/pluxml_PluXml/themes/defaut/lang/es.php", "downloaded_repos/pluxml_PluXml/themes/defaut/lang/fr-help.php", "downloaded_repos/pluxml_PluXml/themes/defaut/lang/fr.php", "downloaded_repos/pluxml_PluXml/themes/defaut/lang/index.html", "downloaded_repos/pluxml_PluXml/themes/defaut/lang/it.php", "downloaded_repos/pluxml_PluXml/themes/defaut/lang/nl.php", "downloaded_repos/pluxml_PluXml/themes/defaut/lang/oc-help.php", "downloaded_repos/pluxml_PluXml/themes/defaut/lang/oc.php", "downloaded_repos/pluxml_PluXml/themes/defaut/lang/pl.php", "downloaded_repos/pluxml_PluXml/themes/defaut/lang/pt.php", "downloaded_repos/pluxml_PluXml/themes/defaut/lang/ro.php", "downloaded_repos/pluxml_PluXml/themes/defaut/lang/ru.php", "downloaded_repos/pluxml_PluXml/themes/defaut/posts.php", "downloaded_repos/pluxml_PluXml/themes/defaut/preview.png", "downloaded_repos/pluxml_PluXml/themes/defaut/sidebar.php", "downloaded_repos/pluxml_PluXml/themes/defaut/static-full-width.php", "downloaded_repos/pluxml_PluXml/themes/defaut/static.php", "downloaded_repos/pluxml_PluXml/themes/defaut/tags.php", "downloaded_repos/pluxml_PluXml/themes/defaut/user.php", "downloaded_repos/pluxml_PluXml/themes/index.html", "downloaded_repos/pluxml_PluXml/update/class.plx.updater.php", "downloaded_repos/pluxml_PluXml/update/index.html", "downloaded_repos/pluxml_PluXml/update/index.php", "downloaded_repos/pluxml_PluXml/update/update_4.2.php", "downloaded_repos/pluxml_PluXml/update/update_5.0.php", "downloaded_repos/pluxml_PluXml/update/update_5.1.1.php", "downloaded_repos/pluxml_PluXml/update/update_5.1.3.php", "downloaded_repos/pluxml_PluXml/update/update_5.1.4.php", "downloaded_repos/pluxml_PluXml/update/update_5.1.6.php", "downloaded_repos/pluxml_PluXml/update/update_5.1.7.php", "downloaded_repos/pluxml_PluXml/update/update_5.1.php", "downloaded_repos/pluxml_PluXml/update/update_5.2.php", "downloaded_repos/pluxml_PluXml/update/update_5.4.php", "downloaded_repos/pluxml_PluXml/update/update_5.5.php", "downloaded_repos/pluxml_PluXml/update/update_5.8.1.php", "downloaded_repos/pluxml_PluXml/update/update_5.8.6.php", "downloaded_repos/pluxml_PluXml/update/update_5.8.7.php", "downloaded_repos/pluxml_PluXml/update/update_5.8.php"], "skipped": [{"path": "downloaded_repos/pluxml_PluXml/core/vendor/autoload.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/composer/ClassLoader.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/composer/InstalledVersions.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/composer/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/composer/autoload_classmap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/composer/autoload_files.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/composer/autoload_namespaces.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/composer/autoload_psr4.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/composer/autoload_real.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/composer/autoload_static.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/composer/installed.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/composer/installed.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/composer/platform_check.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/firebase/php-jwt/CHANGELOG.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/firebase/php-jwt/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/firebase/php-jwt/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/firebase/php-jwt/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/firebase/php-jwt/src/BeforeValidException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/firebase/php-jwt/src/CachedKeySet.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/firebase/php-jwt/src/ExpiredException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/firebase/php-jwt/src/JWK.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/firebase/php-jwt/src/JWT.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/firebase/php-jwt/src/Key.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/firebase/php-jwt/src/SignatureInvalidException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/CHANGELOG.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/UPGRADING.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/BodySummarizer.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/BodySummarizerInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/Client.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/ClientInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/ClientTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/Cookie/CookieJar.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/Cookie/CookieJarInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/Cookie/FileCookieJar.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/Cookie/SessionCookieJar.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/Cookie/SetCookie.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/Exception/BadResponseException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/Exception/ClientException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/Exception/ConnectException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/Exception/GuzzleException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/Exception/InvalidArgumentException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/Exception/RequestException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/Exception/ServerException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/Exception/TooManyRedirectsException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/Exception/TransferException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/Handler/CurlFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/Handler/CurlFactoryInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/Handler/CurlHandler.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/Handler/CurlMultiHandler.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/Handler/EasyHandle.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/Handler/HeaderProcessor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/Handler/MockHandler.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/Handler/Proxy.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/Handler/StreamHandler.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/HandlerStack.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/MessageFormatter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/MessageFormatterInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/Middleware.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/Pool.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/PrepareBodyMiddleware.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/RedirectMiddleware.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/RequestOptions.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/RetryMiddleware.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/TransferStats.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/Utils.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/functions.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/guzzle/src/functions_include.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/promises/CHANGELOG.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/promises/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/promises/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/promises/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/promises/src/AggregateException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/promises/src/CancellationException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/promises/src/Coroutine.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/promises/src/Create.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/promises/src/Each.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/promises/src/EachPromise.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/promises/src/FulfilledPromise.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/promises/src/Is.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/promises/src/Promise.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/promises/src/PromiseInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/promises/src/PromisorInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/promises/src/RejectedPromise.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/promises/src/RejectionException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/promises/src/TaskQueue.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/promises/src/TaskQueueInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/promises/src/Utils.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/CHANGELOG.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/src/AppendStream.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/src/BufferStream.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/src/CachingStream.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/src/DroppingStream.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/src/Exception/MalformedUriException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/src/FnStream.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/src/Header.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/src/HttpFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/src/InflateStream.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/src/LazyOpenStream.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/src/LimitStream.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/src/Message.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/src/MessageTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/src/MimeType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/src/MultipartStream.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/src/NoSeekStream.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/src/PumpStream.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/src/Query.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/src/Request.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/src/Response.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/src/Rfc7230.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/src/ServerRequest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/src/Stream.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/src/StreamDecoratorTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/src/StreamWrapper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/src/UploadedFile.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/src/Uri.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/src/UriComparator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/src/UriNormalizer.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/src/UriResolver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/guzzlehttp/psr7/src/Utils.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/hayageek/oauth2-yahoo/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/hayageek/oauth2-yahoo/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/hayageek/oauth2-yahoo/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/hayageek/oauth2-yahoo/examples/index.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/hayageek/oauth2-yahoo/examples/provider.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/hayageek/oauth2-yahoo/examples/reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/hayageek/oauth2-yahoo/examples/server.sh", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/hayageek/oauth2-yahoo/examples/user.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/hayageek/oauth2-yahoo/phpunit.xml.dist", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/hayageek/oauth2-yahoo/src/Provider/Yahoo.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/hayageek/oauth2-yahoo/src/Provider/YahooUser.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-client/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-client/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-client/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-client/phpunit.xml.dist", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-client/src/Grant/AbstractGrant.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-client/src/Grant/AuthorizationCode.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-client/src/Grant/ClientCredentials.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-client/src/Grant/Exception/InvalidGrantException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-client/src/Grant/GrantFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-client/src/Grant/Password.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-client/src/Grant/RefreshToken.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-client/src/OptionProvider/HttpBasicAuthOptionProvider.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-client/src/OptionProvider/OptionProviderInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-client/src/OptionProvider/PostAuthOptionProvider.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-client/src/Provider/AbstractProvider.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-client/src/Provider/Exception/IdentityProviderException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-client/src/Provider/GenericProvider.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-client/src/Provider/GenericResourceOwner.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-client/src/Provider/ResourceOwnerInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-client/src/Token/AccessToken.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-client/src/Token/AccessTokenInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-client/src/Token/ResourceOwnerAccessTokenInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-client/src/Token/SettableRefreshTokenInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-client/src/Tool/ArrayAccessorTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-client/src/Tool/BearerAuthorizationTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-client/src/Tool/GuardedPropertyTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-client/src/Tool/MacAuthorizationTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-client/src/Tool/ProviderRedirectTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-client/src/Tool/QueryBuilderTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-client/src/Tool/RequestFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-client/src/Tool/RequiredParameterTrait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-google/CHANGELOG.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-google/CONTRIBUTING.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-google/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-google/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-google/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-google/examples/index.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-google/examples/provider.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-google/examples/reset.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-google/examples/server.sh", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-google/examples/user.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-google/phpunit.xml.dist", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-google/src/Exception/HostedDomainException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-google/src/Provider/Google.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/league/oauth2-google/src/Provider/GoogleUser.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/.editorconfig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/COMMITMENT", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/SECURITY.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/VERSION", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/get_oauth_token.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-af.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-ar.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-as.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-az.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-ba.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-be.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-bg.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-bn.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-ca.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-cs.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-da.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-de.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-el.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-eo.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-es.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-et.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-fa.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-fi.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-fo.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-fr.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-gl.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-he.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-hi.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-hr.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-hu.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-hy.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-id.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-it.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-ja.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-ka.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-ko.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-ku.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-lt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-lv.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-mg.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-mn.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-ms.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-nb.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-nl.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-pl.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-pt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-pt_br.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-ro.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-ru.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-si.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-sk.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-sl.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-sr.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-sr_latn.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-sv.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-tl.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-tr.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-uk.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-ur.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-vi.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-zh.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/language/phpmailer.lang-zh_cn.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/src/DSNConfigurator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/src/Exception.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/src/OAuth.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/src/OAuthTokenProvider.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/src/PHPMailer.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/src/POP3.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/phpmailer/phpmailer/src/SMTP.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/psr/http-client/CHANGELOG.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/psr/http-client/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/psr/http-client/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/psr/http-client/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/psr/http-client/src/ClientExceptionInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/psr/http-client/src/ClientInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/psr/http-client/src/NetworkExceptionInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/psr/http-client/src/RequestExceptionInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/psr/http-factory/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/psr/http-factory/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/psr/http-factory/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/psr/http-factory/src/RequestFactoryInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/psr/http-factory/src/ResponseFactoryInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/psr/http-factory/src/ServerRequestFactoryInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/psr/http-factory/src/StreamFactoryInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/psr/http-factory/src/UploadedFileFactoryInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/psr/http-factory/src/UriFactoryInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/psr/http-message/CHANGELOG.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/psr/http-message/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/psr/http-message/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/psr/http-message/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/psr/http-message/docs/PSR7-Interfaces.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/psr/http-message/docs/PSR7-Usage.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/psr/http-message/src/MessageInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/psr/http-message/src/RequestInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/psr/http-message/src/ResponseInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/psr/http-message/src/ServerRequestInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/psr/http-message/src/StreamInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/psr/http-message/src/UploadedFileInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/psr/http-message/src/UriInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/ralouphie/getallheaders/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/ralouphie/getallheaders/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/ralouphie/getallheaders/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/ralouphie/getallheaders/src/getallheaders.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/stevenmaguire/oauth2-microsoft/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/stevenmaguire/oauth2-microsoft/.scrutinizer.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/stevenmaguire/oauth2-microsoft/.travis.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/stevenmaguire/oauth2-microsoft/CHANGELOG.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/stevenmaguire/oauth2-microsoft/CONTRIBUTING.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/stevenmaguire/oauth2-microsoft/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/stevenmaguire/oauth2-microsoft/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/stevenmaguire/oauth2-microsoft/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/stevenmaguire/oauth2-microsoft/phpunit.xml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/stevenmaguire/oauth2-microsoft/src/Provider/Microsoft.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/stevenmaguire/oauth2-microsoft/src/Provider/MicrosoftResourceOwner.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/stevenmaguire/oauth2-microsoft/tests/src/Provider/MicrosoftTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/symfony/deprecation-contracts/CHANGELOG.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/symfony/deprecation-contracts/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/symfony/deprecation-contracts/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/symfony/deprecation-contracts/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/symfony/deprecation-contracts/function.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/thenetworg/oauth2-azure/CHANGELOG.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/thenetworg/oauth2-azure/LICENSE.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/thenetworg/oauth2-azure/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/thenetworg/oauth2-azure/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/thenetworg/oauth2-azure/src/Grant/JwtBearer.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/thenetworg/oauth2-azure/src/Provider/Azure.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/thenetworg/oauth2-azure/src/Provider/AzureResourceOwner.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/thenetworg/oauth2-azure/src/Token/AccessToken.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/thenetworg/oauth2-azure/tests/Fakers/B2cTokenFaker.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/thenetworg/oauth2-azure/tests/Fakers/KeysFaker.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/thenetworg/oauth2-azure/tests/Helper/AzureHelper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/thenetworg/oauth2-azure/tests/Provider/AzureResourceOwnerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/thenetworg/oauth2-azure/tests/Provider/AzureTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/pluxml_PluXml/core/vendor/thenetworg/oauth2-azure/tests/Token/AccessTokenTest.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.61857008934021, "profiling_times": {"config_time": 6.182814836502075, "core_time": 6.6259071826934814, "ignores_time": 0.0020635128021240234, "total_time": 12.811473846435547}, "parsing_time": {"total_time": 3.7340822219848633, "per_file_time": {"mean": 0.023484793848961412, "std_dev": 0.003755625342764504}, "very_slow_stats": {"time_ratio": 0.18820931796420812, "count_ratio": 0.006289308176100629}, "very_slow_files": [{"fpath": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.show.php", "ftime": 0.7027890682220459}]}, "scanning_time": {"total_time": 35.04344439506531, "per_file_time": {"mean": 0.05214798273075191, "std_dev": 0.07428758035671061}, "very_slow_stats": {"time_ratio": 0.4372441142949873, "count_ratio": 0.008928571428571428}, "very_slow_files": [{"fpath": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.plugins.php", "ftime": 1.802616834640503}, {"fpath": "downloaded_repos/pluxml_PluXml/core/admin/article.php", "ftime": 1.9937019348144531}, {"fpath": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.utils.php", "ftime": 2.5934410095214844}, {"fpath": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.motor.php", "ftime": 2.6753921508789062}, {"fpath": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.admin.php", "ftime": 2.954838991165161}, {"fpath": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.show.php", "ftime": 3.302548885345459}]}, "matching_time": {"total_time": 18.715845823287964, "per_file_and_rule_time": {"mean": 0.02961368010013918, "std_dev": 0.005145241532467404}, "very_slow_stats": {"time_ratio": 0.5816023289799164, "count_ratio": 0.07436708860759493}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.plugins.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.34427499771118164}, {"fpath": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.motor.php", "rule_id": "php.lang.security.injection.tainted-session.tainted-session", "time": 0.35101819038391113}, {"fpath": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.admin.php", "rule_id": "php.lang.security.injection.tainted-session.tainted-session", "time": 0.3510899543762207}, {"fpath": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.admin.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.3545091152191162}, {"fpath": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.utils.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.35889196395874023}, {"fpath": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.utils.php", "rule_id": "php.lang.security.injection.tainted-callable.tainted-callable", "time": 0.4255039691925049}, {"fpath": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.motor.php", "rule_id": "php.lang.security.injection.tainted-callable.tainted-callable", "time": 0.43724894523620605}, {"fpath": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.admin.php", "rule_id": "php.lang.security.injection.tainted-callable.tainted-callable", "time": 0.4616079330444336}, {"fpath": "downloaded_repos/pluxml_PluXml/core/admin/article.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.5590231418609619}, {"fpath": "downloaded_repos/pluxml_PluXml/core/lib/class.plx.show.php", "rule_id": "php.lang.security.injection.tainted-callable.tainted-callable", "time": 0.8374018669128418}]}, "tainting_time": {"total_time": 7.22963547706604, "per_def_and_rule_time": {"mean": 0.0024921183995401724, "std_dev": 4.35198548544422e-05}, "very_slow_stats": {"time_ratio": 0.09342337501713617, "count_ratio": 0.003447087211306446}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/pluxml_PluXml/core/admin/medias.php", "fline": 1, "rule_id": "php.lang.security.injection.printed-request.printed-request", "time": 0.05096721649169922}, {"fpath": "downloaded_repos/pluxml_PluXml/core/admin/index.php", "fline": 1, "rule_id": "php.lang.security.injection.tainted-object-instantiation.tainted-object-instantiation", "time": 0.05150604248046875}, {"fpath": "downloaded_repos/pluxml_PluXml/core/admin/index.php", "fline": 1, "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.053256988525390625}, {"fpath": "downloaded_repos/pluxml_PluXml/core/admin/medias.php", "fline": 1, "rule_id": "php.lang.security.injection.tainted-callable.tainted-callable", "time": 0.0560300350189209}, {"fpath": "downloaded_repos/pluxml_PluXml/core/admin/article.php", "fline": 1, "rule_id": "php.lang.security.injection.tainted-callable.tainted-callable", "time": 0.05993986129760742}, {"fpath": "downloaded_repos/pluxml_PluXml/core/admin/article.php", "fline": 1, "rule_id": "php.lang.security.injection.tainted-object-instantiation.tainted-object-instantiation", "time": 0.061978816986083984}, {"fpath": "downloaded_repos/pluxml_PluXml/core/admin/article.php", "fline": 1, "rule_id": "php.lang.security.injection.printed-request.printed-request", "time": 0.06495809555053711}, {"fpath": "downloaded_repos/pluxml_PluXml/core/admin/article.php", "fline": 1, "rule_id": "php.lang.security.injection.tainted-exec.tainted-exec", "time": 0.07546496391296387}, {"fpath": "downloaded_repos/pluxml_PluXml/core/admin/article.php", "fline": 1, "rule_id": "php.lang.security.injection.tainted-session.tainted-session", "time": 0.07884597778320312}, {"fpath": "downloaded_repos/pluxml_PluXml/core/admin/medias.php", "fline": 1, "rule_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "time": 0.12246894836425781}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090729792}, "engine_requested": "OSS", "skipped_rules": []}