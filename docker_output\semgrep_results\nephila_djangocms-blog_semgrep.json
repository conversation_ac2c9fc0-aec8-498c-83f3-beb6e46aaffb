{"version": "1.130.0", "results": [{"check_id": "python.django.security.injection.open-redirect.open-redirect", "path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/admin.py", "start": {"line": 324, "col": 17, "offset": 12179}, "end": {"line": 324, "col": 72, "offset": 12234}, "extra": {"message": "Data from request ($DATA) is passed to redirect(). This is an open redirect and could be exploited. Ensure you are redirecting to safe URLs by using django.utils.http.is_safe_url(). See https://cwe.mitre.org/data/definitions/601.html for more information.", "metadata": {"cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "owasp": ["A01:2021 - Broken Access Control"], "references": ["https://www.djm.org.uk/posts/djangos-little-protections-word-redirect-dangers/", "https://github.com/django/django/blob/d1b7bd030b1db111e1a3505b1fc029ab964382cc/django/utils/http.py#L231"], "category": "security", "technology": ["django"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/python.django.security.injection.open-redirect.open-redirect", "shortlink": "https://sg.run/Ave2"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.injection.open-redirect.open-redirect", "path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/admin.py", "start": {"line": 324, "col": 24, "offset": 12186}, "end": {"line": 324, "col": 72, "offset": 12234}, "extra": {"message": "Data from request ($DATA) is passed to redirect(). This is an open redirect and could be exploited. Ensure you are redirecting to safe URLs by using django.utils.http.is_safe_url(). See https://cwe.mitre.org/data/definitions/601.html for more information.", "metadata": {"cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "owasp": ["A01:2021 - Broken Access Control"], "references": ["https://www.djm.org.uk/posts/djangos-little-protections-word-redirect-dangers/", "https://github.com/django/django/blob/d1b7bd030b1db111e1a3505b1fc029ab964382cc/django/utils/http.py#L231"], "category": "security", "technology": ["django"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/python.django.security.injection.open-redirect.open-redirect", "shortlink": "https://sg.run/Ave2"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/feeds.py", "start": {"line": 49, "col": 16, "offset": 1615}, "end": {"line": 49, "col": 64, "offset": 1663}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/feeds.py", "start": {"line": 53, "col": 20, "offset": 1763}, "end": {"line": 53, "col": 71, "offset": 1814}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/feeds.py", "start": {"line": 54, "col": 16, "offset": 1830}, "end": {"line": 54, "col": 68, "offset": 1882}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/liveblog/static/liveblog/js/liveblog.js", "start": {"line": 2, "col": 31, "offset": 89}, "end": {"line": 2, "col": 36, "offset": 94}, "extra": {"message": "Insecure WebSocket Detected. WebSocket Secure (wss) should be used for all WebSocket connections.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "13.5.1 Insecure WebSocket", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x21-V13-API.md#v135-websocket-security-requirements", "section": "V13: API and Web Service Verification Requirements", "version": "4"}, "category": "security", "technology": ["regex"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/javascript.lang.security.detect-insecure-websocket.detect-insecure-websocket", "shortlink": "https://sg.run/GWyz"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/liveblog/static/liveblog/js/liveblog.js", "start": {"line": 14, "col": 5, "offset": 812}, "end": {"line": 14, "col": 54, "offset": 861}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/archive.html", "start": {"line": 17, "col": 29, "offset": 1018}, "end": {"line": 17, "col": 72, "offset": 1061}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/authors.html", "start": {"line": 10, "col": 17, "offset": 447}, "end": {"line": 10, "col": 61, "offset": 491}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/categories.html", "start": {"line": 10, "col": 21, "offset": 408}, "end": {"line": 10, "col": 67, "offset": 454}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/tags.html", "start": {"line": 12, "col": 21, "offset": 442}, "end": {"line": 12, "col": 62, "offset": 483}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nephila_djangocms-blog/.github/workflows/lint.yml", "start": {"line": 41, "col": 19, "offset": 1422}, "end": {"line": 41, "col": 22, "offset": 1425}}]], "message": "Syntax error at line downloaded_repos/nephila_djangocms-blog/.github/workflows/lint.yml:41:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/nephila_djangocms-blog/.github/workflows/lint.yml", "spans": [{"file": "downloaded_repos/nephila_djangocms-blog/.github/workflows/lint.yml", "start": {"line": 41, "col": 19, "offset": 1422}, "end": {"line": 41, "col": 22, "offset": 1425}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/includes/blog_item.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 61, "offset": 60}}]], "message": "Syntax error at line downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/includes/blog_item.html:1:\n `{% load djangocms_blog i18n easy_thumbnails_tags cms_tags %}` was unexpected", "path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/includes/blog_item.html", "spans": [{"file": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/includes/blog_item.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 61, "offset": 60}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/includes/blog_meta.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 67, "offset": 66}}]], "message": "Syntax error at line downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/includes/blog_meta.html:1:\n `{% load i18n easy_thumbnails_tags cms_tags apphooks_config_tags %}` was unexpected", "path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/includes/blog_meta.html", "spans": [{"file": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/includes/blog_meta.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 67, "offset": 66}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/archive.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 52, "offset": 51}}, {"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/archive.html", "start": {"line": 8, "col": 31, "offset": 0}, "end": {"line": 8, "col": 32, "offset": 1}}, {"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/archive.html", "start": {"line": 12, "col": 39, "offset": 0}, "end": {"line": 12, "col": 40, "offset": 1}}, {"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/archive.html", "start": {"line": 12, "col": 76, "offset": 0}, "end": {"line": 12, "col": 77, "offset": 1}}, {"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/archive.html", "start": {"line": 13, "col": 105, "offset": 0}, "end": {"line": 13, "col": 116, "offset": 11}}, {"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/archive.html", "start": {"line": 13, "col": 149, "offset": 0}, "end": {"line": 13, "col": 150, "offset": 1}}, {"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/archive.html", "start": {"line": 16, "col": 47, "offset": 0}, "end": {"line": 19, "col": 26, "offset": 241}}, {"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/archive.html", "start": {"line": 31, "col": 1, "offset": 0}, "end": {"line": 31, "col": 19, "offset": 18}}]], "message": "Syntax error at line downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/archive.html:1:\n `{% load i18n apphooks_config_tags %}{% spaceless %}` was unexpected", "path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/archive.html", "spans": [{"file": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/archive.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 52, "offset": 51}}, {"file": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/archive.html", "start": {"line": 8, "col": 31, "offset": 0}, "end": {"line": 8, "col": 32, "offset": 1}}, {"file": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/archive.html", "start": {"line": 12, "col": 39, "offset": 0}, "end": {"line": 12, "col": 40, "offset": 1}}, {"file": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/archive.html", "start": {"line": 12, "col": 76, "offset": 0}, "end": {"line": 12, "col": 77, "offset": 1}}, {"file": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/archive.html", "start": {"line": 13, "col": 105, "offset": 0}, "end": {"line": 13, "col": 116, "offset": 11}}, {"file": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/archive.html", "start": {"line": 13, "col": 149, "offset": 0}, "end": {"line": 13, "col": 150, "offset": 1}}, {"file": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/archive.html", "start": {"line": 16, "col": 47, "offset": 0}, "end": {"line": 19, "col": 26, "offset": 241}}, {"file": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/archive.html", "start": {"line": 31, "col": 1, "offset": 0}, "end": {"line": 31, "col": 19, "offset": 18}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/authors.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 73, "offset": 72}}, {"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/authors.html", "start": {"line": 9, "col": 36, "offset": 0}, "end": {"line": 12, "col": 14, "offset": 206}}, {"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/authors.html", "start": {"line": 19, "col": 1, "offset": 0}, "end": {"line": 19, "col": 19, "offset": 18}}]], "message": "Syntax error at line downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/authors.html:1:\n `{% load i18n easy_thumbnails_tags apphooks_config_tags %}{% spaceless %}` was unexpected", "path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/authors.html", "spans": [{"file": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/authors.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 73, "offset": 72}}, {"file": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/authors.html", "start": {"line": 9, "col": 36, "offset": 0}, "end": {"line": 12, "col": 14, "offset": 206}}, {"file": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/authors.html", "start": {"line": 19, "col": 1, "offset": 0}, "end": {"line": 19, "col": 19, "offset": 18}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/categories.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 31, "offset": 30}}, {"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/categories.html", "start": {"line": 9, "col": 42, "offset": 0}, "end": {"line": 12, "col": 18, "offset": 220}}, {"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/categories.html", "start": {"line": 19, "col": 1, "offset": 0}, "end": {"line": 19, "col": 19, "offset": 18}}]], "message": "Syntax error at line downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/categories.html:1:\n `{% load i18n %}{% spaceless %}` was unexpected", "path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/categories.html", "spans": [{"file": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/categories.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 31, "offset": 30}}, {"file": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/categories.html", "start": {"line": 9, "col": 42, "offset": 0}, "end": {"line": 12, "col": 18, "offset": 220}}, {"file": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/categories.html", "start": {"line": 19, "col": 1, "offset": 0}, "end": {"line": 19, "col": 19, "offset": 18}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/tags.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 16, "offset": 53}}, {"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/tags.html", "start": {"line": 11, "col": 37, "offset": 0}, "end": {"line": 14, "col": 18, "offset": 215}}, {"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/tags.html", "start": {"line": 21, "col": 1, "offset": 0}, "end": {"line": 21, "col": 19, "offset": 18}}]], "message": "Syntax error at line downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/tags.html:1:\n `{% load i18n apphooks_config_tags %}\n\n{% spaceless %}` was unexpected", "path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/tags.html", "spans": [{"file": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/tags.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 16, "offset": 53}}, {"file": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/tags.html", "start": {"line": 11, "col": 37, "offset": 0}, "end": {"line": 14, "col": 18, "offset": 215}}, {"file": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/tags.html", "start": {"line": 21, "col": 1, "offset": 0}, "end": {"line": 21, "col": 19, "offset": 18}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/post_detail.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 26, "offset": 113}}, {"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/post_detail.html", "start": {"line": 4, "col": 71, "offset": 0}, "end": {"line": 7, "col": 40, "offset": 121}}, {"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/post_detail.html", "start": {"line": 43, "col": 1, "offset": 0}, "end": {"line": 43, "col": 28, "offset": 27}}]], "message": "Syntax error at line downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/post_detail.html:1:\n `{% extends \"djangocms_blog/base.html\" %}\n{% load i18n easy_thumbnails_tags cms_tags %}\n\n{% block canonical_url %}` was unexpected", "path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/post_detail.html", "spans": [{"file": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/post_detail.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 26, "offset": 113}}, {"file": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/post_detail.html", "start": {"line": 4, "col": 71, "offset": 0}, "end": {"line": 7, "col": 40, "offset": 121}}, {"file": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/post_detail.html", "start": {"line": 43, "col": 1, "offset": 0}, "end": {"line": 43, "col": 28, "offset": 27}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/post_instant_article.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 41, "offset": 40}}, {"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/post_instant_article.html", "start": {"line": 16, "col": 88, "offset": 0}, "end": {"line": 16, "col": 93, "offset": 5}}, {"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/post_instant_article.html", "start": {"line": 17, "col": 86, "offset": 0}, "end": {"line": 17, "col": 91, "offset": 5}}]], "message": "Syntax error at line downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/post_instant_article.html:1:\n `{% load easy_thumbnails_tags cms_tags %}` was unexpected", "path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/post_instant_article.html", "spans": [{"file": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/post_instant_article.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 41, "offset": 40}}, {"file": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/post_instant_article.html", "start": {"line": 16, "col": 88, "offset": 0}, "end": {"line": 16, "col": 93, "offset": 5}}, {"file": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/post_instant_article.html", "start": {"line": 17, "col": 86, "offset": 0}, "end": {"line": 17, "col": 91, "offset": 5}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/post_list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 26, "offset": 119}}, {"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/post_list.html", "start": {"line": 4, "col": 80, "offset": 0}, "end": {"line": 6, "col": 25, "offset": 54}}, {"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/post_list.html", "start": {"line": 40, "col": 1, "offset": 0}, "end": {"line": 41, "col": 19, "offset": 33}}]], "message": "Syntax error at line downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/post_list.html:1:\n `{% extends \"djangocms_blog/base.html\" %}\n{% load i18n easy_thumbnails_tags %}{% spaceless %}\n\n{% block canonical_url %}` was unexpected", "path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/post_list.html", "spans": [{"file": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/post_list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 26, "offset": 119}}, {"file": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/post_list.html", "start": {"line": 4, "col": 80, "offset": 0}, "end": {"line": 6, "col": 25, "offset": 54}}, {"file": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/post_list.html", "start": {"line": 40, "col": 1, "offset": 0}, "end": {"line": 41, "col": 19, "offset": 33}}]}], "paths": {"scanned": ["downloaded_repos/nephila_djangocms-blog/.checkignore", "downloaded_repos/nephila_djangocms-blog/.codeclimate.yml", "downloaded_repos/nephila_djangocms-blog/.coveragerc", "downloaded_repos/nephila_djangocms-blog/.editorconfig", "downloaded_repos/nephila_djangocms-blog/.github/ISSUE_TEMPLATE/---bug-report.md", "downloaded_repos/nephila_djangocms-blog/.github/ISSUE_TEMPLATE/---feature-request.md", "downloaded_repos/nephila_djangocms-blog/.github/pull_request_template.md", "downloaded_repos/nephila_djangocms-blog/.github/workflows/codeql.yml", "downloaded_repos/nephila_djangocms-blog/.github/workflows/lint.yml", "downloaded_repos/nephila_djangocms-blog/.github/workflows/logger.yml", "downloaded_repos/nephila_djangocms-blog/.github/workflows/publish.yml", "downloaded_repos/nephila_djangocms-blog/.github/workflows/test.yml", "downloaded_repos/nephila_djangocms-blog/.gitignore", "downloaded_repos/nephila_djangocms-blog/.pre-commit-config.yaml", "downloaded_repos/nephila_djangocms-blog/.pyup.yml", "downloaded_repos/nephila_djangocms-blog/.readthedocs.yml", "downloaded_repos/nephila_djangocms-blog/.tx/config", "downloaded_repos/nephila_djangocms-blog/AUTHORS.rst", "downloaded_repos/nephila_djangocms-blog/CONTRIBUTING.rst", "downloaded_repos/nephila_djangocms-blog/HISTORY.rst", "downloaded_repos/nephila_djangocms-blog/LICENSE", "downloaded_repos/nephila_djangocms-blog/MANIFEST.in", "downloaded_repos/nephila_djangocms-blog/README.rst", "downloaded_repos/nephila_djangocms-blog/aldryn_config.py", "downloaded_repos/nephila_djangocms-blog/changes/.directory", "downloaded_repos/nephila_djangocms-blog/cms_helper.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/__init__.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/addon.json", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/admin.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/apps.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/cms_appconfig.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/cms_apps.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/cms_menus.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/cms_plugins.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/cms_toolbars.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/cms_wizards.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/feeds.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/fields.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/forms.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/liveblog/__init__.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/liveblog/apps.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/liveblog/cms_plugins.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/liveblog/consumers.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/liveblog/migrations/0001_initial.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/liveblog/migrations/0002_liveblog_title.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/liveblog/migrations/0003_auto_20160917_0123.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/liveblog/migrations/__init__.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/liveblog/models.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/liveblog/routing.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/liveblog/static/liveblog/js/liveblog.js", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/liveblog/templates/liveblog/includes/post_detail.html", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/liveblog/templates/liveblog/plugins/liveblog.html", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/locale/ar/LC_MESSAGES/django.mo", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/locale/ar/LC_MESSAGES/django.po", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/locale/de/LC_MESSAGES/django.mo", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/locale/de/LC_MESSAGES/django.po", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/locale/en/LC_MESSAGES/django.mo", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/locale/en/LC_MESSAGES/django.po", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/locale/es/LC_MESSAGES/django.mo", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/locale/es/LC_MESSAGES/django.po", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/locale/et/LC_MESSAGES/django.mo", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/locale/et/LC_MESSAGES/django.po", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/locale/fr/LC_MESSAGES/django.mo", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/locale/fr/LC_MESSAGES/django.po", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/locale/it/LC_MESSAGES/django.mo", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/locale/it/LC_MESSAGES/django.po", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/locale/lt/LC_MESSAGES/django.mo", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/locale/lt/LC_MESSAGES/django.po", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/locale/nb_NO/LC_MESSAGES/django.mo", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/locale/nb_NO/LC_MESSAGES/django.po", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/locale/nl/LC_MESSAGES/django.mo", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/locale/nl/LC_MESSAGES/django.po", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/locale/pl_PL/LC_MESSAGES/django.mo", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/locale/pl_PL/LC_MESSAGES/django.po", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/locale/pt_BR/LC_MESSAGES/django.mo", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/locale/pt_BR/LC_MESSAGES/django.po", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/locale/ru/LC_MESSAGES/django.mo", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/locale/ru/LC_MESSAGES/django.po", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/locale/sl/LC_MESSAGES/django.mo", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/locale/sl/LC_MESSAGES/django.po", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/locale/tr/LC_MESSAGES/django.mo", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/locale/tr/LC_MESSAGES/django.po", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/managers.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/media/__init__.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/media/base.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0001_initial.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0002_post_sites.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0003_auto_20141201_2252.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0004_auto_20150108_1435.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0005_auto_20150212_1118.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0006_auto_20150214_1907.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0007_auto_20150719_0933.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0008_auto_20150814_0831.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0009_latestpostsplugin_tags_new.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0010_auto_20150923_1151.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0011_auto_20151024_1809.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0012_auto_20151220_1734.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0013_auto_20160201_2235.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0014_auto_20160215_1331.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0015_auto_20160408_1849.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0016_auto_20160502_1741.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0017_thumbnail_move.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0018_thumbnail_move2.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0019_thumbnail_move3.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0020_thumbnail_move4.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0021_auto_20160823_2008.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0021_post_liveblog.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0022_auto_20160605_2305.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0022_auto_20170304_1040.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0023_auto_20160626_1539.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0024_auto_20160706_1524.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0025_auto_20160803_0858.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0026_merge.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0027_post_date_featured.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0028_auto_20170304_1040.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0029_post_related.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0030_auto_20170509_1831.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0031_auto_20170610_1744.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0032_auto_20180109_0023.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0033_auto_20180226_1410.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0034_merge.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0035_posttranslation_subtitle.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0036_auto_20180913_1809.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0037_auto_20190806_0743.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0038_post_media.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0039_auto_20200331_2227.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0040_post_include_in_rss.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0041_auto_20230720_1508.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/0042_alter_authorentriesplugin_cmsplugin_ptr_and_more.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/migrations/__init__.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/models.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/settings.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/sitemaps/__init__.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/static/djangocms_blog/css/djangocms_blog.css", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/static/djangocms_blog/css/djangocms_blog_admin.css", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/taggit_urls.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/base.html", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/includes/blog_item.html", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/includes/blog_meta.html", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/archive.html", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/authors.html", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/authors_posts.html", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/categories.html", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/featured_posts.html", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/latest_entries.html", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/tags.html", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/post_archive.html", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/post_detail.html", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/post_instant_article.html", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/post_list.html", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templatetags/__init__.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templatetags/djangocms_blog.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/urls.py", "downloaded_repos/nephila_djangocms-blog/djangocms_blog/views.py", "downloaded_repos/nephila_djangocms-blog/docs/Makefile", "downloaded_repos/nephila_djangocms-blog/docs/_static/.directory", "downloaded_repos/nephila_djangocms-blog/docs/_templates/.directory", "downloaded_repos/nephila_djangocms-blog/docs/autodoc/admin.rst", "downloaded_repos/nephila_djangocms-blog/docs/autodoc/index.rst", "downloaded_repos/nephila_djangocms-blog/docs/autodoc/menu.rst", "downloaded_repos/nephila_djangocms-blog/docs/autodoc/models.rst", "downloaded_repos/nephila_djangocms-blog/docs/autodoc/plugins.rst", "downloaded_repos/nephila_djangocms-blog/docs/autodoc/settings.rst", "downloaded_repos/nephila_djangocms-blog/docs/autodoc/views.rst", "downloaded_repos/nephila_djangocms-blog/docs/conf.py", "downloaded_repos/nephila_djangocms-blog/docs/contributing.rst", "downloaded_repos/nephila_djangocms-blog/docs/development.rst", "downloaded_repos/nephila_djangocms-blog/docs/features/admin_customization.rst", "downloaded_repos/nephila_djangocms-blog/docs/features/channels.rst", "downloaded_repos/nephila_djangocms-blog/docs/features/cmsplugin_filer.rst", "downloaded_repos/nephila_djangocms-blog/docs/features/extensions.rst", "downloaded_repos/nephila_djangocms-blog/docs/features/home.rst", "downloaded_repos/nephila_djangocms-blog/docs/features/index.rst", "downloaded_repos/nephila_djangocms-blog/docs/features/media.rst", "downloaded_repos/nephila_djangocms-blog/docs/features/menu.rst", "downloaded_repos/nephila_djangocms-blog/docs/features/meta.rst", "downloaded_repos/nephila_djangocms-blog/docs/features/multisite.rst", "downloaded_repos/nephila_djangocms-blog/docs/features/permalinks.rst", "downloaded_repos/nephila_djangocms-blog/docs/features/related.rst", "downloaded_repos/nephila_djangocms-blog/docs/features/shares.rst", "downloaded_repos/nephila_djangocms-blog/docs/features/templates.rst", "downloaded_repos/nephila_djangocms-blog/docs/features/urlconf.rst", "downloaded_repos/nephila_djangocms-blog/docs/features/wizard.rst", "downloaded_repos/nephila_djangocms-blog/docs/history.rst", "downloaded_repos/nephila_djangocms-blog/docs/index.rst", "downloaded_repos/nephila_djangocms-blog/docs/installation.rst", "downloaded_repos/nephila_djangocms-blog/pyproject.toml", "downloaded_repos/nephila_djangocms-blog/requirements-test.txt", "downloaded_repos/nephila_djangocms-blog/requirements.txt", "downloaded_repos/nephila_djangocms-blog/setup.cfg", "downloaded_repos/nephila_djangocms-blog/setup.py", "downloaded_repos/nephila_djangocms-blog/tasks.py", "downloaded_repos/nephila_djangocms-blog/tox.ini"], "skipped": [{"path": "downloaded_repos/nephila_djangocms-blog/.github/workflows/lint.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/liveblog/static/liveblog/js/reconnecting-websocket.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/includes/blog_item.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/includes/blog_meta.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/archive.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/authors.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/categories.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/plugins/tags.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/post_detail.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/post_instant_article.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/templates/djangocms_blog/post_list.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nephila_djangocms-blog/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-blog/tests/base.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-blog/tests/fixtures/vimeo.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-blog/tests/media_app/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-blog/tests/media_app/cms_plugins.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-blog/tests/media_app/migrations/0001_initial.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-blog/tests/media_app/migrations/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-blog/tests/media_app/models.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-blog/tests/media_app/templates/media_app/vimeo.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-blog/tests/media_app/templates/media_app/youtube.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-blog/tests/test_extension.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-blog/tests/test_indexing.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-blog/tests/test_liveblog.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-blog/tests/test_media.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-blog/tests/test_menu.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-blog/tests/test_models.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-blog/tests/test_namespace.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-blog/tests/test_plugins.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-blog/tests/test_setup.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-blog/tests/test_toolbar.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-blog/tests/test_utils/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-blog/tests/test_utils/admin.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-blog/tests/test_utils/blog_urls.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-blog/tests/test_utils/migrations/0001_initial.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-blog/tests/test_utils/migrations/0002_auto_20200516_1230.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-blog/tests/test_utils/migrations/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-blog/tests/test_utils/models.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-blog/tests/test_utils/routing.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-blog/tests/test_utils/templates/blog.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-blog/tests/test_utils/urls.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-blog/tests/test_views.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nephila_djangocms-blog/tests/test_wizards.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.****************, "profiling_times": {"config_time": 6.***************, "core_time": 3.***************, "ignores_time": 0.002043485641479492, "total_time": 9.***************}, "parsing_time": {"total_time": 0.****************, "per_file_time": {"mean": 0.007367587783961621, "std_dev": 0.00021855146889481337}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 7.6684653759002686, "per_file_time": {"mean": 0.015460615677218282, "std_dev": 0.004184072151851781}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 3.2840311527252197, "per_file_and_rule_time": {"mean": 0.003644873643424217, "std_dev": 0.00010512931390743401}, "very_slow_stats": {"time_ratio": 0.07855527261969367, "count_ratio": 0.0022197558268590455}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/models.py", "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.10667896270751953}, {"fpath": "downloaded_repos/nephila_djangocms-blog/djangocms_blog/models.py", "rule_id": "python.lang.security.dangerous-system-call.dangerous-system-call", "time": 0.15129899978637695}]}, "tainting_time": {"total_time": 0.8239297866821289, "per_def_and_rule_time": {"mean": 0.0005201576936124548, "std_dev": 1.6009745257258111e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}