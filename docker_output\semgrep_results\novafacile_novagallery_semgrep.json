{"version": "1.130.0", "results": [], "errors": [], "paths": {"scanned": ["downloaded_repos/novafacile_novagallery/.github/FUNDING.yml", "downloaded_repos/novafacile_novagallery/.gitignore", "downloaded_repos/novafacile_novagallery/CODE_OF_CONDUCT.md", "downloaded_repos/novafacile_novagallery/LICENSE", "downloaded_repos/novafacile_novagallery/README.md", "downloaded_repos/novafacile_novagallery/src/.htaccess", "downloaded_repos/novafacile_novagallery/src/addons/password-protection/addon.php", "downloaded_repos/novafacile_novagallery/src/addons/password-protection/auth.php", "downloaded_repos/novafacile_novagallery/src/addons/password-protection/languages/de.json", "downloaded_repos/novafacile_novagallery/src/addons/password-protection/languages/en.json", "downloaded_repos/novafacile_novagallery/src/addons/password-protection/languages/es.json", "downloaded_repos/novafacile_novagallery/src/addons/password-protection/languages/fi.json", "downloaded_repos/novafacile_novagallery/src/addons/password-protection/languages/fr.json", "downloaded_repos/novafacile_novagallery/src/addons/password-protection/languages/it.json", "downloaded_repos/novafacile_novagallery/src/addons/password-protection/languages/nl.json", "downloaded_repos/novafacile_novagallery/src/addons/password-protection/languages/pt.json", "downloaded_repos/novafacile_novagallery/src/addons/password-protection/pages/login.php", "downloaded_repos/novafacile_novagallery/src/addons/password-protection/pages/logout.php", "downloaded_repos/novafacile_novagallery/src/addons/password-protection/template/login.php", "downloaded_repos/novafacile_novagallery/src/addons/password-protection/template/style.css", "downloaded_repos/novafacile_novagallery/src/addons/robots-meta-tag/addon.php", "downloaded_repos/novafacile_novagallery/src/config/addons.example.php", "downloaded_repos/novafacile_novagallery/src/config/site.example.php", "downloaded_repos/novafacile_novagallery/src/core/app/App.php", "downloaded_repos/novafacile_novagallery/src/core/app/Config.php", "downloaded_repos/novafacile_novagallery/src/core/app/addons/Addon.php", "downloaded_repos/novafacile_novagallery/src/core/app/addons/AddonInterface.php", "downloaded_repos/novafacile_novagallery/src/core/app/addons/addons.php", "downloaded_repos/novafacile_novagallery/src/core/app/novaImage.php", "downloaded_repos/novafacile_novagallery/src/core/app/pages/album.php", "downloaded_repos/novafacile_novagallery/src/core/app/pages/home.php", "downloaded_repos/novafacile_novagallery/src/core/app/router.php", "downloaded_repos/novafacile_novagallery/src/core/init.php", "downloaded_repos/novafacile_novagallery/src/index.php", "downloaded_repos/novafacile_novagallery/src/languages/de.json", "downloaded_repos/novafacile_novagallery/src/languages/en.json", "downloaded_repos/novafacile_novagallery/src/languages/es.json", "downloaded_repos/novafacile_novagallery/src/languages/fi.json", "downloaded_repos/novafacile_novagallery/src/languages/fr.json", "downloaded_repos/novafacile_novagallery/src/languages/it.json", "downloaded_repos/novafacile_novagallery/src/languages/nl.json", "downloaded_repos/novafacile_novagallery/src/languages/pt.json", "downloaded_repos/novafacile_novagallery/src/themes/novagallery/album.php", "downloaded_repos/novafacile_novagallery/src/themes/novagallery/assets/bootstrap.min.css", "downloaded_repos/novafacile_novagallery/src/themes/novagallery/assets/bootstrap.min.css.map", "downloaded_repos/novafacile_novagallery/src/themes/novagallery/assets/novagallery-favicon.png", "downloaded_repos/novafacile_novagallery/src/themes/novagallery/assets/novagallery.css", "downloaded_repos/novafacile_novagallery/src/themes/novagallery/assets/novagallery.js", "downloaded_repos/novafacile_novagallery/src/themes/novagallery/assets/simple-lightbox.min.css", "downloaded_repos/novafacile_novagallery/src/themes/novagallery/error-404.php", "downloaded_repos/novafacile_novagallery/src/themes/novagallery/globals/footer.php", "downloaded_repos/novafacile_novagallery/src/themes/novagallery/globals/header.php", "downloaded_repos/novafacile_novagallery/src/themes/novagallery/home.php", "downloaded_repos/novafacile_novagallery/src/themes/novagallery/index.php", "downloaded_repos/novafacile_novagallery/src/themes/novagallery/metadata.json", "downloaded_repos/novafacile_novagallery/src/themes/novagallery-light/album.php", "downloaded_repos/novafacile_novagallery/src/themes/novagallery-light/assets/bootstrap.min.css", "downloaded_repos/novafacile_novagallery/src/themes/novagallery-light/assets/bootstrap.min.css.map", "downloaded_repos/novafacile_novagallery/src/themes/novagallery-light/assets/novagallery-favicon.png", "downloaded_repos/novafacile_novagallery/src/themes/novagallery-light/assets/novagallery.css", "downloaded_repos/novafacile_novagallery/src/themes/novagallery-light/assets/novagallery.js", "downloaded_repos/novafacile_novagallery/src/themes/novagallery-light/assets/simple-lightbox.min.css", "downloaded_repos/novafacile_novagallery/src/themes/novagallery-light/error-404.php", "downloaded_repos/novafacile_novagallery/src/themes/novagallery-light/globals/footer.php", "downloaded_repos/novafacile_novagallery/src/themes/novagallery-light/globals/header.php", "downloaded_repos/novafacile_novagallery/src/themes/novagallery-light/home.php", "downloaded_repos/novafacile_novagallery/src/themes/novagallery-light/index.php", "downloaded_repos/novafacile_novagallery/src/themes/novagallery-light/metadata.json"], "skipped": [{"path": "downloaded_repos/novafacile_novagallery/src/core/vendor/claviska/SimpleImage.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/novafacile_novagallery/src/core/vendor/novafacile/JsonDB.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/novafacile_novagallery/src/core/vendor/novafacile/SimpleEventDispatcher.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/novafacile_novagallery/src/core/vendor/novafacile/SimpleTranslations.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/novafacile_novagallery/src/core/vendor/novafacile/novaConfig.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/novafacile_novagallery/src/core/vendor/novafacile/novaGallery.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/novafacile_novagallery/src/core/vendor/novafacile/novaPage.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/novafacile_novagallery/src/themes/novagallery/assets/lazyload.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/novafacile_novagallery/src/themes/novagallery/assets/simple-lightbox.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/novafacile_novagallery/src/themes/novagallery-light/assets/lazyload.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/novafacile_novagallery/src/themes/novagallery-light/assets/simple-lightbox.min.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.9869189262390137, "profiling_times": {"config_time": 6.358435153961182, "core_time": 2.8636391162872314, "ignores_time": 0.001882791519165039, "total_time": 9.224900245666504}, "parsing_time": {"total_time": 0.3124353885650635, "per_file_time": {"mean": 0.006008372857020452, "std_dev": 0.00010788802609649585}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.5213003158569336, "per_file_time": {"mean": 0.008092022956685815, "std_dev": 0.0005066041402019813}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.17999529838562012, "per_file_and_rule_time": {"mean": 0.001267572523842395, "std_dev": 1.4027327895614431e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.024253368377685547, "per_def_and_rule_time": {"mean": 0.0002090807618765995, "std_dev": 4.9433351788510686e-08}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089476480}, "engine_requested": "OSS", "skipped_rules": []}