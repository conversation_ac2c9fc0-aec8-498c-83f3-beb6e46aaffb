{"version": "1.130.0", "results": [{"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/usefulteam_jwt-auth/README.md", "start": {"line": 117, "col": 13, "offset": 3678}, "end": {"line": 117, "col": 205, "offset": 3870}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/usefulteam_jwt-auth/README.md", "start": {"line": 155, "col": 24, "offset": 4902}, "end": {"line": 155, "col": 216, "offset": 5094}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/usefulteam_jwt-auth/readme.txt", "start": {"line": 124, "col": 13, "offset": 4465}, "end": {"line": 124, "col": 205, "offset": 4657}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/usefulteam_jwt-auth/readme.txt", "start": {"line": 161, "col": 24, "offset": 5677}, "end": {"line": 161, "col": 216, "offset": 5869}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/usefulteam_jwt-auth/.distignore", "downloaded_repos/usefulteam_jwt-auth/.gitattributes", "downloaded_repos/usefulteam_jwt-auth/.github/workflows/asset-readme-update.yml", "downloaded_repos/usefulteam_jwt-auth/.github/workflows/deploy.yml", "downloaded_repos/usefulteam_jwt-auth/.gitignore", "downloaded_repos/usefulteam_jwt-auth/.wordpress-org/banner-1544x500.png", "downloaded_repos/usefulteam_jwt-auth/.wordpress-org/banner-772x250.png", "downloaded_repos/usefulteam_jwt-auth/.wordpress-org/banner-940x304.png", "downloaded_repos/usefulteam_jwt-auth/.wordpress-org/icon-256x256.png", "downloaded_repos/usefulteam_jwt-auth/FUNDING.yml", "downloaded_repos/usefulteam_jwt-auth/README.md", "downloaded_repos/usefulteam_jwt-auth/class-auth.php", "downloaded_repos/usefulteam_jwt-auth/class-devices.php", "downloaded_repos/usefulteam_jwt-auth/class-setup.php", "downloaded_repos/usefulteam_jwt-auth/class-update.php", "downloaded_repos/usefulteam_jwt-auth/composer.json", "downloaded_repos/usefulteam_jwt-auth/composer.lock", "downloaded_repos/usefulteam_jwt-auth/index.php", "downloaded_repos/usefulteam_jwt-auth/jwt-auth.php", "downloaded_repos/usefulteam_jwt-auth/languages/jwt-auth.pot", "downloaded_repos/usefulteam_jwt-auth/phpcs.xml", "downloaded_repos/usefulteam_jwt-auth/phpunit.xml.dist", "downloaded_repos/usefulteam_jwt-auth/readme.txt"], "skipped": [{"path": "downloaded_repos/usefulteam_jwt-auth/tests/src/AccessTokenTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/usefulteam_jwt-auth/tests/src/RefreshTokenTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/usefulteam_jwt-auth/tests/src/RestTestTrait.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 2.193510055541992, "profiling_times": {"config_time": 6.66671347618103, "core_time": 3.105259656906128, "ignores_time": 0.0021250247955322266, "total_time": 9.775244235992432}, "parsing_time": {"total_time": 0.14835810661315918, "per_file_time": {"mean": 0.014835810661315918, "std_dev": 1.764801543743033e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.7339906692504883, "per_file_time": {"mean": 0.013106976236615862, "std_dev": 0.0013799890255277625}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.2738230228424072, "per_file_and_rule_time": {"mean": 0.002444848418235779, "std_dev": 5.720706490947818e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.07191777229309082, "per_def_and_rule_time": {"mean": 0.0003239539292481568, "std_dev": 2.1872728095674982e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089476480}, "engine_requested": "OSS", "skipped_rules": []}