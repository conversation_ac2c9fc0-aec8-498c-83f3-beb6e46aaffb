{"version": "1.130.0", "results": [], "errors": [], "paths": {"scanned": ["downloaded_repos/iamleot_transferwee/.github/workflows/conftest.yml", "downloaded_repos/iamleot_transferwee/.github/workflows/mdformat.yml", "downloaded_repos/iamleot_transferwee/.github/workflows/python.yml", "downloaded_repos/iamleot_transferwee/.gitignore", "downloaded_repos/iamleot_transferwee/README.md", "downloaded_repos/iamleot_transferwee/TODO.md", "downloaded_repos/iamleot_transferwee/pyproject.toml", "downloaded_repos/iamleot_transferwee/transferwee.py"], "skipped": [{"path": "downloaded_repos/iamleot_transferwee/tests/check.sh", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.9037959575653076, "profiling_times": {"config_time": 6.092331409454346, "core_time": 2.9811253547668457, "ignores_time": 0.0018901824951171875, "total_time": 9.07693886756897}, "parsing_time": {"total_time": 0.0499262809753418, "per_file_time": {"mean": 0.01248157024383545, "std_dev": 8.409353725369327e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.5453033447265625, "per_file_time": {"mean": 0.027265167236328124, "std_dev": 0.008858172826452345}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.33577990531921387, "per_file_and_rule_time": {"mean": 0.0029454377659580166, "std_dev": 0.0001553843898179353}, "very_slow_stats": {"time_ratio": 0.36043264414075066, "count_ratio": 0.008771929824561403}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/iamleot_transferwee/transferwee.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.12102603912353516}]}, "tainting_time": {"total_time": 0.04848456382751465, "per_def_and_rule_time": {"mean": 0.00028859859421139666, "std_dev": 1.936992517200765e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}