{"version": "1.130.0", "results": [{"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/static/wagtail_modeltranslation/js/copy_stream_fields.js", "start": {"line": 32, "col": 5, "offset": 1549}, "end": {"line": 32, "col": 252, "offset": 1796}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/templates/modeltranslation_copy.html", "start": {"line": 3, "col": 21, "offset": 75}, "end": {"line": 3, "col": 77, "offset": 131}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/templates/modeltranslation_copy.html", "start": {"line": 9, "col": 9, "offset": 411}, "end": {"line": 22, "col": 16, "offset": 947}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.csrf-exempt.no-csrf-exempt", "path": "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/wagtail_hooks.py", "start": {"line": 102, "col": 1, "offset": 3453}, "end": {"line": 148, "col": 6, "offset": 5210}, "extra": {"message": "Detected usage of @csrf_exempt, which indicates that there is no CSRF token set for this route. This could lead to an attacker manipulating the user's account and exfiltration of private data. Instead, create a function without this decorator.", "metadata": {"cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["django"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.audit.csrf-exempt.no-csrf-exempt", "shortlink": "https://sg.run/rd5e"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/infoportugal_wagtail-modeltranslation/.github/workflows/python-package.yml", "start": {"line": 86, "col": 49, "offset": 2444}, "end": {"line": 86, "col": 52, "offset": 2447}}, {"path": "downloaded_repos/infoportugal_wagtail-modeltranslation/.github/workflows/python-package.yml", "start": {"line": 87, "col": 40, "offset": 2444}, "end": {"line": 87, "col": 43, "offset": 2447}}]], "message": "Syntax error at line downloaded_repos/infoportugal_wagtail-modeltranslation/.github/workflows/python-package.yml:86:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/infoportugal_wagtail-modeltranslation/.github/workflows/python-package.yml", "spans": [{"file": "downloaded_repos/infoportugal_wagtail-modeltranslation/.github/workflows/python-package.yml", "start": {"line": 86, "col": 49, "offset": 2444}, "end": {"line": 86, "col": 52, "offset": 2447}}, {"file": "downloaded_repos/infoportugal_wagtail-modeltranslation/.github/workflows/python-package.yml", "start": {"line": 87, "col": 40, "offset": 2444}, "end": {"line": 87, "col": 43, "offset": 2447}}]}], "paths": {"scanned": ["downloaded_repos/infoportugal_wagtail-modeltranslation/.bumpversion.cfg", "downloaded_repos/infoportugal_wagtail-modeltranslation/.github/workflows/python-package.yml", "downloaded_repos/infoportugal_wagtail-modeltranslation/.gitignore", "downloaded_repos/infoportugal_wagtail-modeltranslation/.readthedocs.yaml", "downloaded_repos/infoportugal_wagtail-modeltranslation/AUTHORS.rst", "downloaded_repos/infoportugal_wagtail-modeltranslation/Makefile", "downloaded_repos/infoportugal_wagtail-modeltranslation/README.rst", "downloaded_repos/infoportugal_wagtail-modeltranslation/docs/AUTHORS.rst", "downloaded_repos/infoportugal_wagtail-modeltranslation/docs/Installation.rst", "downloaded_repos/infoportugal_wagtail-modeltranslation/docs/Introduction.rst", "downloaded_repos/infoportugal_wagtail-modeltranslation/docs/Makefile", "downloaded_repos/infoportugal_wagtail-modeltranslation/docs/Registering Models.rst", "downloaded_repos/infoportugal_wagtail-modeltranslation/docs/advanced settings.rst", "downloaded_repos/infoportugal_wagtail-modeltranslation/docs/caveats.rst", "downloaded_repos/infoportugal_wagtail-modeltranslation/docs/conf.py", "downloaded_repos/infoportugal_wagtail-modeltranslation/docs/extending.rst", "downloaded_repos/infoportugal_wagtail-modeltranslation/docs/index.rst", "downloaded_repos/infoportugal_wagtail-modeltranslation/docs/make.bat", "downloaded_repos/infoportugal_wagtail-modeltranslation/docs/management commands.rst", "downloaded_repos/infoportugal_wagtail-modeltranslation/docs/recommended reading.rst", "downloaded_repos/infoportugal_wagtail-modeltranslation/docs/releases/0.11.0.rst", "downloaded_repos/infoportugal_wagtail-modeltranslation/docs/releases/0.12.0.rst", "downloaded_repos/infoportugal_wagtail-modeltranslation/docs/releases/0.13.0.rst", "downloaded_repos/infoportugal_wagtail-modeltranslation/docs/releases/0.14.0.rst", "downloaded_repos/infoportugal_wagtail-modeltranslation/docs/releases/0.15.0.rst", "downloaded_repos/infoportugal_wagtail-modeltranslation/docs/releases/0.6.0.rst", "downloaded_repos/infoportugal_wagtail-modeltranslation/docs/releases/index.rst", "downloaded_repos/infoportugal_wagtail-modeltranslation/docs/template tags.rst", "downloaded_repos/infoportugal_wagtail-modeltranslation/docs/upgrade considerations.rst", "downloaded_repos/infoportugal_wagtail-modeltranslation/get-modeltranslation-version.py", "downloaded_repos/infoportugal_wagtail-modeltranslation/get-wagtail-version.py", "downloaded_repos/infoportugal_wagtail-modeltranslation/poetry.lock", "downloaded_repos/infoportugal_wagtail-modeltranslation/pyproject.toml", "downloaded_repos/infoportugal_wagtail-modeltranslation/runtests.py", "downloaded_repos/infoportugal_wagtail-modeltranslation/screenshot.png", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/__init__.py", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/apps.py", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/contextlib.py", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/makemigrations/__init__.py", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/makemigrations/apps.py", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/makemigrations/management/__init__.py", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/makemigrations/management/commands/__init__.py", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/makemigrations/management/commands/makemigrations.py", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/makemigrations/management/commands/makemigrations_original.py", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/management/__init__.py", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/management/commands/__init__.py", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/management/commands/makemigrations_translation.py", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/management/commands/migrate_translation.py", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/management/commands/set_translation_url_paths.py", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/management/commands/sync_page_translation_fields.py", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/management/commands/update_translation_fields.py", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/migrate/__init__.py", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/migrate/apps.py", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/migrate/management/__init__.py", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/migrate/management/commands/__init__.py", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/migrate/management/commands/migrate.py", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/migrate/management/commands/migrate_original.py", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/patch_wagtailadmin.py", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/patch_wagtailadmin_forms.py", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/settings.py", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/signal_handlers.py", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/static/wagtail_modeltranslation/css/admin_patch.css", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/static/wagtail_modeltranslation/css/language_toggles.css", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/static/wagtail_modeltranslation/css/page_editor_modeltranslation.css", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/static/wagtail_modeltranslation/js/copy_stream_fields.js", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/static/wagtail_modeltranslation/js/force_jquery.js", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/static/wagtail_modeltranslation/js/js.cookie.js", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/static/wagtail_modeltranslation/js/language_toggles.js", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/templates/modeltranslation_copy.html", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/templatetags/__init__.py", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/templatetags/wagtail_modeltranslation.py", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/translation.py", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/utils.py", "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/wagtail_hooks.py"], "skipped": [{"path": "downloaded_repos/infoportugal_wagtail-modeltranslation/.github/workflows/python-package.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/tests/apps.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/tests/migrations/0001_initial.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/tests/migrations/0002_titlefieldpanelpagetest.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/tests/migrations/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/tests/models.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/tests/settings.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/tests/test_settings.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/tests/tests.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/tests/translation.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/tests/urls.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/infoportugal_wagtail-modeltranslation/wagtail_modeltranslation/tests/util.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.9484279155731201, "profiling_times": {"config_time": 6.611410140991211, "core_time": 3.4416000843048096, "ignores_time": 0.0021474361419677734, "total_time": 10.056162118911743}, "parsing_time": {"total_time": 0.561410665512085, "per_file_time": {"mean": 0.013692943061270368, "std_dev": 0.00030712659743173935}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 3.622210741043091, "per_file_time": {"mean": 0.01906426705812152, "std_dev": 0.003864230265769165}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 1.0081098079681396, "per_file_and_rule_time": {"mean": 0.00350038127766715, "std_dev": 7.229096635487564e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.33747339248657227, "per_def_and_rule_time": {"mean": 0.0006214979603804278, "std_dev": 1.9048270907261499e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}