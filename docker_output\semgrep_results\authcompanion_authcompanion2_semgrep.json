{"version": "1.130.0", "results": [{"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/authcompanion_authcompanion2/client/index.html", "start": {"line": 7, "col": 5, "offset": 175}, "end": {"line": 7, "col": 98, "offset": 268}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/authcompanion_authcompanion2/client/index.html", "start": {"line": 8, "col": 5, "offset": 273}, "end": {"line": 8, "col": 109, "offset": 377}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/authcompanion_authcompanion2/client/index.html", "start": {"line": 9, "col": 5, "offset": 382}, "end": {"line": 9, "col": 69, "offset": 446}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/authcompanion_authcompanion2/documentation/guide/explore.md", "start": {"line": 18, "col": 1, "offset": 849}, "end": {"line": 18, "col": 337, "offset": 1185}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/authcompanion_authcompanion2/documentation/guide/explore.md", "start": {"line": 24, "col": 42, "offset": 1387}, "end": {"line": 24, "col": 378, "offset": 1723}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/authcompanion_authcompanion2/.dockerignore", "downloaded_repos/authcompanion_authcompanion2/.github/public/admin_dashboard.png", "downloaded_repos/authcompanion_authcompanion2/.github/public/authcLandscape.png", "downloaded_repos/authcompanion_authcompanion2/.github/public/login.png", "downloaded_repos/authcompanion_authcompanion2/.github/public/register.png", "downloaded_repos/authcompanion_authcompanion2/.github/workflows/authc2-ci.yml", "downloaded_repos/authcompanion_authcompanion2/.github/workflows/authc2-deploy-demo.yml", "downloaded_repos/authcompanion_authcompanion2/.github/workflows/authc2-deploy-docs.yml", "downloaded_repos/authcompanion_authcompanion2/.github/workflows/authc2-release-publish-img.yml", "downloaded_repos/authcompanion_authcompanion2/.gitignore", "downloaded_repos/authcompanion_authcompanion2/.prettierignore", "downloaded_repos/authcompanion_authcompanion2/.prettierrc", "downloaded_repos/authcompanion_authcompanion2/.vscode/extensions.json", "downloaded_repos/authcompanion_authcompanion2/.vscode/settings.json", "downloaded_repos/authcompanion_authcompanion2/CHANGELOG.md", "downloaded_repos/authcompanion_authcompanion2/Dockerfile", "downloaded_repos/authcompanion_authcompanion2/Dockerfile.arm64", "downloaded_repos/authcompanion_authcompanion2/LICENSE.md", "downloaded_repos/authcompanion_authcompanion2/README.md", "downloaded_repos/authcompanion_authcompanion2/app.js", "downloaded_repos/authcompanion_authcompanion2/client/App.vue", "downloaded_repos/authcompanion_authcompanion2/client/components/ErrorAlert.vue", "downloaded_repos/authcompanion_authcompanion2/client/components/admin/AdminTable.vue", "downloaded_repos/authcompanion_authcompanion2/client/components/admin/UserModal.vue", "downloaded_repos/authcompanion_authcompanion2/client/index.html", "downloaded_repos/authcompanion_authcompanion2/client/main.js", "downloaded_repos/authcompanion_authcompanion2/client/routes.js", "downloaded_repos/authcompanion_authcompanion2/client/views/NotFound.vue", "downloaded_repos/authcompanion_authcompanion2/client/views/admin/Dashboard.vue", "downloaded_repos/authcompanion_authcompanion2/client/views/admin/Login.vue", "downloaded_repos/authcompanion_authcompanion2/client/views/auth/Home.vue", "downloaded_repos/authcompanion_authcompanion2/client/views/auth/Login.vue", "downloaded_repos/authcompanion_authcompanion2/client/views/auth/Profile.vue", "downloaded_repos/authcompanion_authcompanion2/client/views/auth/Recovery.vue", "downloaded_repos/authcompanion_authcompanion2/client/views/auth/Register.vue", "downloaded_repos/authcompanion_authcompanion2/client/vite.config.js", "downloaded_repos/authcompanion_authcompanion2/config.js", "downloaded_repos/authcompanion_authcompanion2/db/db.js", "downloaded_repos/authcompanion_authcompanion2/db/postgres/0000_powerful_reavers.sql", "downloaded_repos/authcompanion_authcompanion2/db/postgres/meta/0000_snapshot.json", "downloaded_repos/authcompanion_authcompanion2/db/postgres/meta/_journal.json", "downloaded_repos/authcompanion_authcompanion2/db/postgres/pg.config.js", "downloaded_repos/authcompanion_authcompanion2/db/postgres/pg.schema.js", "downloaded_repos/authcompanion_authcompanion2/db/sqlite/0000_sweet_thunderbolt.sql", "downloaded_repos/authcompanion_authcompanion2/db/sqlite/meta/0000_snapshot.json", "downloaded_repos/authcompanion_authcompanion2/db/sqlite/meta/_journal.json", "downloaded_repos/authcompanion_authcompanion2/db/sqlite/sqlite.config.js", "downloaded_repos/authcompanion_authcompanion2/db/sqlite/sqlite.schema.js", "downloaded_repos/authcompanion_authcompanion2/docker-compose.yml", "downloaded_repos/authcompanion_authcompanion2/documentation/.dockerignore", "downloaded_repos/authcompanion_authcompanion2/documentation/.gitignore", "downloaded_repos/authcompanion_authcompanion2/documentation/.vitepress/config.js", "downloaded_repos/authcompanion_authcompanion2/documentation/Caddyfile", "downloaded_repos/authcompanion_authcompanion2/documentation/Dockerfile", "downloaded_repos/authcompanion_authcompanion2/documentation/README.md", "downloaded_repos/authcompanion_authcompanion2/documentation/fly.toml", "downloaded_repos/authcompanion_authcompanion2/documentation/guide/administer.md", "downloaded_repos/authcompanion_authcompanion2/documentation/guide/configuration.md", "downloaded_repos/authcompanion_authcompanion2/documentation/guide/explore.md", "downloaded_repos/authcompanion_authcompanion2/documentation/guide/gettinghelp.md", "downloaded_repos/authcompanion_authcompanion2/documentation/guide/gettingstarted.md", "downloaded_repos/authcompanion_authcompanion2/documentation/guide/integrate.md", "downloaded_repos/authcompanion_authcompanion2/documentation/guide/launch.md", "downloaded_repos/authcompanion_authcompanion2/documentation/guide/licenseoverview.md", "downloaded_repos/authcompanion_authcompanion2/documentation/guide/webforms.md", "downloaded_repos/authcompanion_authcompanion2/documentation/index.md", "downloaded_repos/authcompanion_authcompanion2/documentation/package.json", "downloaded_repos/authcompanion_authcompanion2/documentation/pnpm-lock.yaml", "downloaded_repos/authcompanion_authcompanion2/documentation/public/serverapidoc.png", "downloaded_repos/authcompanion_authcompanion2/documentation/reference/apisummary.md", "downloaded_repos/authcompanion_authcompanion2/documentation/reference/tokens.md", "downloaded_repos/authcompanion_authcompanion2/env.example", "downloaded_repos/authcompanion_authcompanion2/fly.toml", "downloaded_repos/authcompanion_authcompanion2/key/admin.key.js", "downloaded_repos/authcompanion_authcompanion2/key/server.key.js", "downloaded_repos/authcompanion_authcompanion2/package.json", "downloaded_repos/authcompanion_authcompanion2/pnpm-lock.yaml", "downloaded_repos/authcompanion_authcompanion2/pnpm-workspace.yaml", "downloaded_repos/authcompanion_authcompanion2/routes/admin.routes.js", "downloaded_repos/authcompanion_authcompanion2/routes/auth.routes.js", "downloaded_repos/authcompanion_authcompanion2/server.js", "downloaded_repos/authcompanion_authcompanion2/services/admin/create.js", "downloaded_repos/authcompanion_authcompanion2/services/admin/delete.js", "downloaded_repos/authcompanion_authcompanion2/services/admin/list.js", "downloaded_repos/authcompanion_authcompanion2/services/admin/login.js", "downloaded_repos/authcompanion_authcompanion2/services/admin/logout.js", "downloaded_repos/authcompanion_authcompanion2/services/admin/refresh.js", "downloaded_repos/authcompanion_authcompanion2/services/admin/schema/createSchema.js", "downloaded_repos/authcompanion_authcompanion2/services/admin/schema/deleteSchema.js", "downloaded_repos/authcompanion_authcompanion2/services/admin/schema/listSchema.js", "downloaded_repos/authcompanion_authcompanion2/services/admin/schema/loginSchema.js", "downloaded_repos/authcompanion_authcompanion2/services/admin/schema/logoutSchema.js", "downloaded_repos/authcompanion_authcompanion2/services/admin/schema/refreshSchema.js", "downloaded_repos/authcompanion_authcompanion2/services/admin/schema/updateSchema.js", "downloaded_repos/authcompanion_authcompanion2/services/admin/update.js", "downloaded_repos/authcompanion_authcompanion2/services/auth/login.js", "downloaded_repos/authcompanion_authcompanion2/services/auth/profile.js", "downloaded_repos/authcompanion_authcompanion2/services/auth/recovery.js", "downloaded_repos/authcompanion_authcompanion2/services/auth/refresh.js", "downloaded_repos/authcompanion_authcompanion2/services/auth/registration.js", "downloaded_repos/authcompanion_authcompanion2/services/auth/schemas/loginSchema.js", "downloaded_repos/authcompanion_authcompanion2/services/auth/schemas/profileSchema.js", "downloaded_repos/authcompanion_authcompanion2/services/auth/schemas/recoverySchema.js", "downloaded_repos/authcompanion_authcompanion2/services/auth/schemas/refreshSchema.js", "downloaded_repos/authcompanion_authcompanion2/services/auth/schemas/registrationSchema.js", "downloaded_repos/authcompanion_authcompanion2/services/webAuthn/loginOptions.js", "downloaded_repos/authcompanion_authcompanion2/services/webAuthn/loginVerification.js", "downloaded_repos/authcompanion_authcompanion2/services/webAuthn/registrationOptions.js", "downloaded_repos/authcompanion_authcompanion2/services/webAuthn/registrationVerification.js", "downloaded_repos/authcompanion_authcompanion2/services/webAuthn/schemas/loginOptionsSchema.js", "downloaded_repos/authcompanion_authcompanion2/services/webAuthn/schemas/loginVerificationSchema.js", "downloaded_repos/authcompanion_authcompanion2/services/webAuthn/schemas/registerVerificationSchema.js", "downloaded_repos/authcompanion_authcompanion2/services/webAuthn/schemas/registrationOptionsSchema.js", "downloaded_repos/authcompanion_authcompanion2/utils/authenticate.js", "downloaded_repos/authcompanion_authcompanion2/utils/cookies.js", "downloaded_repos/authcompanion_authcompanion2/utils/credential.js", "downloaded_repos/authcompanion_authcompanion2/utils/jwt.js"], "skipped": [{"path": "downloaded_repos/authcompanion_authcompanion2/tests/admin.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/authcompanion_authcompanion2/tests/auth.test.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6999638080596924, "profiling_times": {"config_time": 5.945943832397461, "core_time": 3.475705623626709, "ignores_time": 0.001771688461303711, "total_time": 9.424405336380005}, "parsing_time": {"total_time": 1.2171931266784668, "per_file_time": {"mean": 0.01714356516448545, "std_dev": 0.0004408767413140599}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 8.831276655197144, "per_file_time": {"mean": 0.02895500542687588, "std_dev": 0.008480601720696236}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 4.478547811508179, "per_file_and_rule_time": {"mean": 0.008040480810607145, "std_dev": 0.0002898607731506146}, "very_slow_stats": {"time_ratio": 0.09407890838728815, "count_ratio": 0.005385996409335727}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/authcompanion_authcompanion2/services/webAuthn/registrationVerification.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.1271958351135254}, {"fpath": "downloaded_repos/authcompanion_authcompanion2/services/webAuthn/loginVerification.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.1274280548095703}, {"fpath": "downloaded_repos/authcompanion_authcompanion2/services/auth/profile.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.16671299934387207}]}, "tainting_time": {"total_time": 0.5188403129577637, "per_def_and_rule_time": {"mean": 0.0027451868410463686, "std_dev": 8.500432373750408e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}