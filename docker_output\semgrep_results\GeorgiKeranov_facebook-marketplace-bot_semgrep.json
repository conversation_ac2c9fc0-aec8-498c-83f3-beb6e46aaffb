{"version": "1.130.0", "results": [{"check_id": "python.lang.security.dangerous-globals-use.dangerous-globals-use", "path": "downloaded_repos/<PERSON><PERSON>_facebook-marketplace-bot/helpers/listing_helper.py", "start": {"line": 56, "col": 2, "offset": 2190}, "end": {"line": 56, "col": 26, "offset": 2214}, "extra": {"message": "Found non static data as an index to 'globals()'. This is extremely dangerous because it allows an attacker to execute arbitrary code on the system. Refactor your code not to use 'globals()'.", "metadata": {"cwe": ["CWE-96: Improper Neutralization of Directives in Statically Saved Code ('Static Code Injection')"], "owasp": ["A03:2021 - Injection"], "references": ["https://github.com/mpirnat/lets-be-bad-guys/blob/d92768fb3ade32956abd53bd6bb06e19d634a084/badguys/vulnerable/views.py#L181-L186"], "category": "security", "technology": ["python"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/python.lang.security.dangerous-globals-use.dangerous-globals-use", "shortlink": "https://sg.run/jNzn"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.deserialization.pickle.avoid-pickle", "path": "downloaded_repos/<PERSON><PERSON>_facebook-marketplace-bot/helpers/scraper.py", "start": {"line": 96, "col": 13, "offset": 3448}, "end": {"line": 96, "col": 38, "offset": 3473}, "extra": {"message": "Avoid using `pickle`, which is known to lead to code execution vulnerabilities. When unpickling, the serialized data could be manipulated to run arbitrary code. Instead, consider serializing the relevant data as JSON or a similar text-based serialization format.", "metadata": {"owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "references": ["https://docs.python.org/3/library/pickle.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/python.lang.security.deserialization.pickle.avoid-pickle", "shortlink": "https://sg.run/OPwB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.deserialization.pickle.avoid-pickle", "path": "downloaded_repos/<PERSON><PERSON>_facebook-marketplace-bot/helpers/scraper.py", "start": {"line": 122, "col": 3, "offset": 4147}, "end": {"line": 122, "col": 37, "offset": 4181}, "extra": {"message": "Avoid using `pickle`, which is known to lead to code execution vulnerabilities. When unpickling, the serialized data could be manipulated to run arbitrary code. Instead, consider serializing the relevant data as JSON or a similar text-based serialization format.", "metadata": {"owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "references": ["https://docs.python.org/3/library/pickle.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/python.lang.security.deserialization.pickle.avoid-pickle", "shortlink": "https://sg.run/OPwB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/<PERSON><PERSON>ov_facebook-marketplace-bot/.gitignore", "downloaded_repos/<PERSON><PERSON><PERSON>ov_facebook-marketplace-bot/LICENSE", "downloaded_repos/GeorgiK<PERSON>ov_facebook-marketplace-bot/README.md", "downloaded_repos/<PERSON><PERSON>ov_facebook-marketplace-bot/__init__.py", "downloaded_repos/<PERSON><PERSON>ov_facebook-marketplace-bot/csvs/items.csv", "downloaded_repos/Georg<PERSON>ov_facebook-marketplace-bot/csvs/vehicles.csv", "downloaded_repos/<PERSON><PERSON>_facebook-marketplace-bot/helpers/__init__.py", "downloaded_repos/<PERSON><PERSON>_facebook-marketplace-bot/helpers/csv_helper.py", "downloaded_repos/<PERSON><PERSON>_facebook-marketplace-bot/helpers/listing_helper.py", "downloaded_repos/<PERSON><PERSON>_facebook-marketplace-bot/helpers/scraper.py", "downloaded_repos/<PERSON><PERSON>ov_facebook-marketplace-bot/main.py"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.7220220565795898, "profiling_times": {"config_time": 5.960139513015747, "core_time": 2.509073495864868, "ignores_time": 0.0020732879638671875, "total_time": 8.472462177276611}, "parsing_time": {"total_time": 0.07622027397155762, "per_file_time": {"mean": 0.012703378995259602, "std_dev": 1.4580323019054553e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.3910841941833496, "per_file_time": {"mean": 0.013967292649405343, "std_dev": 0.0005876574118617577}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.0486600399017334, "per_file_and_rule_time": {"mean": 0.001569678706507529, "std_dev": 3.700875625362188e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.010753870010375977, "per_def_and_rule_time": {"mean": 0.0002500900002413018, "std_dev": 6.097075328432145e-08}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090262912}, "engine_requested": "OSS", "skipped_rules": []}