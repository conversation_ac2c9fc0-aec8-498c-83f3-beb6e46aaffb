{"version": "1.130.0", "results": [{"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/Drewpeifer_medialytics/app/index.html", "start": {"line": 8, "col": 5, "offset": 201}, "end": {"line": 8, "col": 68, "offset": 264}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/Drewpeifer_medialytics/app/index.html", "start": {"line": 10, "col": 5, "offset": 309}, "end": {"line": 10, "col": 92, "offset": 396}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/Drewpeifer_medialytics/app/index.html", "start": {"line": 12, "col": 5, "offset": 452}, "end": {"line": 12, "col": 87, "offset": 534}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/Drewpeifer_medialytics/app/index.html", "start": {"line": 13, "col": 5, "offset": 539}, "end": {"line": 13, "col": 85, "offset": 619}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/Drewpeifer_medialytics/docker-compose.yaml", "start": {"line": 3, "col": 3, "offset": 27}, "end": {"line": 3, "col": 14, "offset": 38}, "extra": {"message": "Service 'medialytics' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/Drewpeifer_medialytics/docker-compose.yaml", "start": {"line": 3, "col": 3, "offset": 27}, "end": {"line": 3, "col": 14, "offset": 38}, "extra": {"message": "Service 'medialytics' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/Drewpeifer_medialytics/.dockerignore", "downloaded_repos/Drewpeifer_medialytics/.env.example", "downloaded_repos/Drewpeifer_medialytics/.github/dependabot.yml", "downloaded_repos/Drewpeifer_medialytics/.github/workflows/docker.yml", "downloaded_repos/Drewpeifer_medialytics/.gitignore", "downloaded_repos/Drewpeifer_medialytics/.pre-commit-config.yaml", "downloaded_repos/Drewpeifer_medialytics/Dockerfile", "downloaded_repos/Drewpeifer_medialytics/LICENSE", "downloaded_repos/Drewpeifer_medialytics/README.md", "downloaded_repos/Drewpeifer_medialytics/app/css/styles.css", "downloaded_repos/Drewpeifer_medialytics/app/images/favicon.ico", "downloaded_repos/Drewpeifer_medialytics/app/images/icon-bar_64.png", "downloaded_repos/Drewpeifer_medialytics/app/images/icon-pie_64.png", "downloaded_repos/Drewpeifer_medialytics/app/index.html", "downloaded_repos/Drewpeifer_medialytics/app/js/scripts.js", "downloaded_repos/Drewpeifer_medialytics/docker-compose.override.yaml", "downloaded_repos/Drewpeifer_medialytics/docker-compose.yaml", "downloaded_repos/Drewpeifer_medialytics/entrypoint.sh"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.868351936340332, "profiling_times": {"config_time": 6.046878099441528, "core_time": 4.435193777084351, "ignores_time": 0.0018718242645263672, "total_time": 10.484887838363647}, "parsing_time": {"total_time": 0.2713501453399658, "per_file_time": {"mean": 0.03015001614888509, "std_dev": 0.0010817211047601126}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 2.338770866394043, "per_file_time": {"mean": 0.05197268591986764, "std_dev": 0.08078030701379843}, "very_slow_stats": {"time_ratio": 0.8274367461146911, "count_ratio": 0.022222222222222223}, "very_slow_files": [{"fpath": "downloaded_repos/Drewpeifer_medialytics/app/js/scripts.js", "ftime": 1.9351849555969238}]}, "matching_time": {"total_time": 1.0152313709259033, "per_file_and_rule_time": {"mean": 0.005971949240740607, "std_dev": 0.0005073454339267211}, "very_slow_stats": {"time_ratio": 0.4333445509346814, "count_ratio": 0.01764705882352941}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/Drewpeifer_medialytics/app/js/scripts.js", "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 0.11433005332946777}, {"fpath": "downloaded_repos/Drewpeifer_medialytics/app/js/scripts.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.16124701499938965}, {"fpath": "downloaded_repos/Drewpeifer_medialytics/app/js/scripts.js", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 0.1643679141998291}]}, "tainting_time": {"total_time": 0.30895495414733887, "per_def_and_rule_time": {"mean": 0.025746246178944904, "std_dev": 0.0001551530437349729}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089476480}, "engine_requested": "OSS", "skipped_rules": []}