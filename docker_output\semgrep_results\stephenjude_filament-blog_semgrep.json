{"version": "1.130.0", "results": [], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/stephenjude_filament-blog/.github/workflows/run-tests.yml", "start": {"line": 48, "col": 38, "offset": 1100}, "end": {"line": 48, "col": 57, "offset": 1119}}, {"path": "downloaded_repos/stephenjude_filament-blog/.github/workflows/run-tests.yml", "start": {"line": 49, "col": 24, "offset": 1100}, "end": {"line": 49, "col": 43, "offset": 1119}}]], "message": "Syntax error at line downloaded_repos/stephenjude_filament-blog/.github/workflows/run-tests.yml:48:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ runner.tool_cache` was unexpected", "path": "downloaded_repos/stephenjude_filament-blog/.github/workflows/run-tests.yml", "spans": [{"file": "downloaded_repos/stephenjude_filament-blog/.github/workflows/run-tests.yml", "start": {"line": 48, "col": 38, "offset": 1100}, "end": {"line": 48, "col": 57, "offset": 1119}}, {"file": "downloaded_repos/stephenjude_filament-blog/.github/workflows/run-tests.yml", "start": {"line": 49, "col": 24, "offset": 1100}, "end": {"line": 49, "col": 43, "offset": 1119}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/stephenjude_filament-blog/.github/workflows/run-tests.yml", "start": {"line": 53, "col": 53, "offset": 1287}, "end": {"line": 53, "col": 69, "offset": 1303}}, {"path": "downloaded_repos/stephenjude_filament-blog/.github/workflows/run-tests.yml", "start": {"line": 53, "col": 97, "offset": 1287}, "end": {"line": 53, "col": 115, "offset": 1305}}, {"path": "downloaded_repos/stephenjude_filament-blog/.github/workflows/run-tests.yml", "start": {"line": 54, "col": 19, "offset": 1287}, "end": {"line": 54, "col": 22, "offset": 1290}}]], "message": "Syntax error at line downloaded_repos/stephenjude_filament-blog/.github/workflows/run-tests.yml:53:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ matrix.laravel` was unexpected", "path": "downloaded_repos/stephenjude_filament-blog/.github/workflows/run-tests.yml", "spans": [{"file": "downloaded_repos/stephenjude_filament-blog/.github/workflows/run-tests.yml", "start": {"line": 53, "col": 53, "offset": 1287}, "end": {"line": 53, "col": 69, "offset": 1303}}, {"file": "downloaded_repos/stephenjude_filament-blog/.github/workflows/run-tests.yml", "start": {"line": 53, "col": 97, "offset": 1287}, "end": {"line": 53, "col": 115, "offset": 1305}}, {"file": "downloaded_repos/stephenjude_filament-blog/.github/workflows/run-tests.yml", "start": {"line": 54, "col": 19, "offset": 1287}, "end": {"line": 54, "col": 22, "offset": 1290}}]}], "paths": {"scanned": ["downloaded_repos/stephenjude_filament-blog/.editorconfig", "downloaded_repos/stephenjude_filament-blog/.gitattributes", "downloaded_repos/stephenjude_filament-blog/.github/CONTRIBUTING.md", "downloaded_repos/stephenjude_filament-blog/.github/FUNDING.yml", "downloaded_repos/stephenjude_filament-blog/.github/ISSUE_TEMPLATE/bug_report.yml", "downloaded_repos/stephenjude_filament-blog/.github/ISSUE_TEMPLATE/config.yml", "downloaded_repos/stephenjude_filament-blog/.github/ISSUE_TEMPLATE/feature.yml", "downloaded_repos/stephenjude_filament-blog/.github/SECURITY.md", "downloaded_repos/stephenjude_filament-blog/.github/dependabot.yml", "downloaded_repos/stephenjude_filament-blog/.github/workflows/dependabot-auto-merge.yml", "downloaded_repos/stephenjude_filament-blog/.github/workflows/fix-php-code-style-issues.yml", "downloaded_repos/stephenjude_filament-blog/.github/workflows/phpstan.yml", "downloaded_repos/stephenjude_filament-blog/.github/workflows/run-tests.yml", "downloaded_repos/stephenjude_filament-blog/.github/workflows/update-changelog.yml", "downloaded_repos/stephenjude_filament-blog/.gitignore", "downloaded_repos/stephenjude_filament-blog/CHANGELOG.md", "downloaded_repos/stephenjude_filament-blog/LICENSE.md", "downloaded_repos/stephenjude_filament-blog/README.md", "downloaded_repos/stephenjude_filament-blog/art/banner.jpg", "downloaded_repos/stephenjude_filament-blog/art/screen2.png", "downloaded_repos/stephenjude_filament-blog/art/screen3.png", "downloaded_repos/stephenjude_filament-blog/composer.json", "downloaded_repos/stephenjude_filament-blog/config/filament-blog.php", "downloaded_repos/stephenjude_filament-blog/database/migrations/create_filament_blog_tables.php.stub", "downloaded_repos/stephenjude_filament-blog/phpstan-baseline.neon", "downloaded_repos/stephenjude_filament-blog/phpstan.neon.dist", "downloaded_repos/stephenjude_filament-blog/phpunit.xml.dist", "downloaded_repos/stephenjude_filament-blog/pint.json", "downloaded_repos/stephenjude_filament-blog/resources/lang/en/filament-blog.php", "downloaded_repos/stephenjude_filament-blog/resources/lang/pt_BR/filament-blog.php", "downloaded_repos/stephenjude_filament-blog/src/BlogPlugin.php", "downloaded_repos/stephenjude_filament-blog/src/BlogServiceProvider.php", "downloaded_repos/stephenjude_filament-blog/src/Commands/InstallCommand.php", "downloaded_repos/stephenjude_filament-blog/src/Models/Author.php", "downloaded_repos/stephenjude_filament-blog/src/Models/Category.php", "downloaded_repos/stephenjude_filament-blog/src/Models/Post.php", "downloaded_repos/stephenjude_filament-blog/src/Resources/AuthorResource/Pages/CreateAuthor.php", "downloaded_repos/stephenjude_filament-blog/src/Resources/AuthorResource/Pages/EditAuthor.php", "downloaded_repos/stephenjude_filament-blog/src/Resources/AuthorResource/Pages/ListAuthors.php", "downloaded_repos/stephenjude_filament-blog/src/Resources/AuthorResource.php", "downloaded_repos/stephenjude_filament-blog/src/Resources/CategoryResource/Pages/CreateCategory.php", "downloaded_repos/stephenjude_filament-blog/src/Resources/CategoryResource/Pages/EditCategory.php", "downloaded_repos/stephenjude_filament-blog/src/Resources/CategoryResource/Pages/ListCategories.php", "downloaded_repos/stephenjude_filament-blog/src/Resources/CategoryResource.php", "downloaded_repos/stephenjude_filament-blog/src/Resources/PostResource/Pages/CreatePost.php", "downloaded_repos/stephenjude_filament-blog/src/Resources/PostResource/Pages/EditPost.php", "downloaded_repos/stephenjude_filament-blog/src/Resources/PostResource/Pages/ListPosts.php", "downloaded_repos/stephenjude_filament-blog/src/Resources/PostResource.php", "downloaded_repos/stephenjude_filament-blog/src/Traits/HasContentEditor.php"], "skipped": [{"path": "downloaded_repos/stephenjude_filament-blog/.github/workflows/run-tests.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/stephenjude_filament-blog/art/screen1.png", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/stephenjude_filament-blog/tests/ArchTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/stephenjude_filament-blog/tests/FilamentBlogTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/stephenjude_filament-blog/tests/Models/User.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/stephenjude_filament-blog/tests/Panel/TestPanelProvider.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/stephenjude_filament-blog/tests/Pest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/stephenjude_filament-blog/tests/ResourceTests/AuthorTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/stephenjude_filament-blog/tests/ResourceTests/CategoryTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/stephenjude_filament-blog/tests/ResourceTests/PostTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/stephenjude_filament-blog/tests/TestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/stephenjude_filament-blog/tests/database/factories/AuthorFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/stephenjude_filament-blog/tests/database/factories/CategoryFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/stephenjude_filament-blog/tests/database/factories/PostFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/stephenjude_filament-blog/tests/database/factories/UserFactory.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.5387539863586426, "profiling_times": {"config_time": 5.624754428863525, "core_time": 2.157689332962036, "ignores_time": 0.0029425621032714844, "total_time": 7.78642725944519}, "parsing_time": {"total_time": 0.33817267417907715, "per_file_time": {"mean": 0.009946255122914034, "std_dev": 0.0001426771759023251}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.7849650382995605, "per_file_time": {"mean": 0.005946704835602731, "std_dev": 0.00014653974165175542}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.16880559921264648, "per_file_and_rule_time": {"mean": 0.0005308352176498319, "std_dev": 2.025504160033528e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.0, "per_def_and_rule_time": {"mean": 0.0, "std_dev": 0.0}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}