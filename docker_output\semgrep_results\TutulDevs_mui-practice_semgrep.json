{"version": "1.130.0", "results": [], "errors": [], "paths": {"scanned": ["downloaded_repos/TutulDevs_mui-practice/.gitignore", "downloaded_repos/TutulDevs_mui-practice/README.md", "downloaded_repos/TutulDevs_mui-practice/package-lock.json", "downloaded_repos/TutulDevs_mui-practice/package.json", "downloaded_repos/TutulDevs_mui-practice/public/_redirects", "downloaded_repos/TutulDevs_mui-practice/public/favicon.ico", "downloaded_repos/TutulDevs_mui-practice/public/index.html", "downloaded_repos/TutulDevs_mui-practice/public/logo192.png", "downloaded_repos/TutulDevs_mui-practice/public/logo512.png", "downloaded_repos/TutulDevs_mui-practice/public/manifest.json", "downloaded_repos/TutulDevs_mui-practice/public/robots.txt", "downloaded_repos/TutulDevs_mui-practice/src/App.css", "downloaded_repos/TutulDevs_mui-practice/src/App.js", "downloaded_repos/TutulDevs_mui-practice/src/api/blogApi.js", "downloaded_repos/TutulDevs_mui-practice/src/api/productApi.js", "downloaded_repos/TutulDevs_mui-practice/src/api/userApi.js", "downloaded_repos/TutulDevs_mui-practice/src/components/AuthPages/ButtonGroup.js", "downloaded_repos/TutulDevs_mui-practice/src/components/AuthPages/FormLogin.js", "downloaded_repos/TutulDevs_mui-practice/src/components/AuthPages/FormRegister.js", "downloaded_repos/TutulDevs_mui-practice/src/components/AuthPages/LeftPanel.js", "downloaded_repos/TutulDevs_mui-practice/src/components/AuthPages/SectionDivider.js", "downloaded_repos/TutulDevs_mui-practice/src/components/Blog/BlogFilters.js", "downloaded_repos/TutulDevs_mui-practice/src/components/Blog/BlogHeader.js", "downloaded_repos/TutulDevs_mui-practice/src/components/Blog/BlogSocialInfo.js", "downloaded_repos/TutulDevs_mui-practice/src/components/Blog/Blogs.js", "downloaded_repos/TutulDevs_mui-practice/src/components/Dashboard/ChartCurrentSubject.js", "downloaded_repos/TutulDevs_mui-practice/src/components/Dashboard/ChartSiteVisits.js", "downloaded_repos/TutulDevs_mui-practice/src/components/Dashboard/ConversionRate.js", "downloaded_repos/TutulDevs_mui-practice/src/components/Dashboard/CurrentVisits.js", "downloaded_repos/TutulDevs_mui-practice/src/components/Dashboard/DashCard.js", "downloaded_repos/TutulDevs_mui-practice/src/components/Dashboard/DashCardBox.js", "downloaded_repos/TutulDevs_mui-practice/src/components/Dashboard/DashCardHeader.js", "downloaded_repos/TutulDevs_mui-practice/src/components/Dashboard/InfoCard.js", "downloaded_repos/TutulDevs_mui-practice/src/components/Dashboard/InfoCards.js", "downloaded_repos/TutulDevs_mui-practice/src/components/Dashboard/NewsUpdate.js", "downloaded_repos/TutulDevs_mui-practice/src/components/Dashboard/NewsUpdateItem.js", "downloaded_repos/TutulDevs_mui-practice/src/components/Dashboard/OrderTimeline.js", "downloaded_repos/TutulDevs_mui-practice/src/components/Dashboard/OrderTimelineItem.js", "downloaded_repos/TutulDevs_mui-practice/src/components/Dashboard/SocialTraffic.js", "downloaded_repos/TutulDevs_mui-practice/src/components/Dashboard/SocialTrafficItem.js", "downloaded_repos/TutulDevs_mui-practice/src/components/Dashboard/Tasks.js", "downloaded_repos/TutulDevs_mui-practice/src/components/Dashboard/TasksItem.js", "downloaded_repos/TutulDevs_mui-practice/src/components/Drawer/CustomListItem.js", "downloaded_repos/TutulDevs_mui-practice/src/components/MainHeader/Notifications.js", "downloaded_repos/TutulDevs_mui-practice/src/components/MainHeader/SelectLanguage.js", "downloaded_repos/TutulDevs_mui-practice/src/components/MainHeader/UserMenu.js", "downloaded_repos/TutulDevs_mui-practice/src/components/Product/DrawerContent.js", "downloaded_repos/TutulDevs_mui-practice/src/components/Product/DrawerContentBody.js", "downloaded_repos/TutulDevs_mui-practice/src/components/Product/DrawerContentCard.js", "downloaded_repos/TutulDevs_mui-practice/src/components/Product/FixedCartCount.js", "downloaded_repos/TutulDevs_mui-practice/src/components/Product/ProductActionBar.js", "downloaded_repos/TutulDevs_mui-practice/src/components/Product/ProductColorPreview.js", "downloaded_repos/TutulDevs_mui-practice/src/components/Product/ProductList.js", "downloaded_repos/TutulDevs_mui-practice/src/components/Product/ProductListItem.js", "downloaded_repos/TutulDevs_mui-practice/src/components/Product/ProductPrice.js", "downloaded_repos/TutulDevs_mui-practice/src/components/UI/IntroModal.js", "downloaded_repos/TutulDevs_mui-practice/src/components/UI/MenuArrow.js", "downloaded_repos/TutulDevs_mui-practice/src/components/User/TableToolbar.js", "downloaded_repos/TutulDevs_mui-practice/src/components/User/UserHeader.js", "downloaded_repos/TutulDevs_mui-practice/src/components/User/UserMore.js", "downloaded_repos/TutulDevs_mui-practice/src/components/User/UserTable.js", "downloaded_repos/TutulDevs_mui-practice/src/components/User/UserTableHead.js", "downloaded_repos/TutulDevs_mui-practice/src/images/404.svg", "downloaded_repos/TutulDevs_mui-practice/src/images/auth/login.png", "downloaded_repos/TutulDevs_mui-practice/src/images/auth/register.png", "downloaded_repos/TutulDevs_mui-practice/src/images/avatar_default.jpg", "downloaded_repos/TutulDevs_mui-practice/src/images/ic_flag_de.svg", "downloaded_repos/TutulDevs_mui-practice/src/images/ic_flag_en.svg", "downloaded_repos/TutulDevs_mui-practice/src/images/ic_flag_fr.svg", "downloaded_repos/TutulDevs_mui-practice/src/images/illustration_avatar.png", "downloaded_repos/TutulDevs_mui-practice/src/images/newsList/cover_1.jpg", "downloaded_repos/TutulDevs_mui-practice/src/images/newsList/cover_2.jpg", "downloaded_repos/TutulDevs_mui-practice/src/images/newsList/cover_3.jpg", "downloaded_repos/TutulDevs_mui-practice/src/images/newsList/cover_4.jpg", "downloaded_repos/TutulDevs_mui-practice/src/images/newsList/cover_5.jpg", "downloaded_repos/TutulDevs_mui-practice/src/index.js", "downloaded_repos/TutulDevs_mui-practice/src/layout/AuthLayout.js", "downloaded_repos/TutulDevs_mui-practice/src/layout/DashboardLayout.js", "downloaded_repos/TutulDevs_mui-practice/src/layout/MainHeader.js", "downloaded_repos/TutulDevs_mui-practice/src/layout/SideDrawer.js", "downloaded_repos/TutulDevs_mui-practice/src/pages/404.js", "downloaded_repos/TutulDevs_mui-practice/src/pages/Blog.js", "downloaded_repos/TutulDevs_mui-practice/src/pages/Dashboard.js", "downloaded_repos/TutulDevs_mui-practice/src/pages/Login.js", "downloaded_repos/TutulDevs_mui-practice/src/pages/Products.js", "downloaded_repos/TutulDevs_mui-practice/src/pages/Register.js", "downloaded_repos/TutulDevs_mui-practice/src/pages/User.js", "downloaded_repos/TutulDevs_mui-practice/src/reportWebVitals.js", "downloaded_repos/TutulDevs_mui-practice/src/routes.js", "downloaded_repos/TutulDevs_mui-practice/src/setupTests.js", "downloaded_repos/TutulDevs_mui-practice/src/theme.js"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.7380890846252441, "profiling_times": {"config_time": 5.7103776931762695, "core_time": 3.3090500831604004, "ignores_time": 0.002094268798828125, "total_time": 9.022449731826782}, "parsing_time": {"total_time": 1.6095268726348877, "per_file_time": {"mean": 0.022993241037641256, "std_dev": 0.003861154078111142}, "very_slow_stats": {"time_ratio": 0.3201941386596789, "count_ratio": 0.014285714285714285}, "very_slow_files": [{"fpath": "downloaded_repos/TutulDevs_mui-practice/package-lock.json", "ftime": 0.5153610706329346}]}, "scanning_time": {"total_time": 5.277001619338989, "per_file_time": {"mean": 0.020940482616424547, "std_dev": 0.004935942263331345}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 1.4065258502960205, "per_file_and_rule_time": {"mean": 0.00725013324894856, "std_dev": 0.00018643771619379556}, "very_slow_stats": {"time_ratio": 0.07589267174255267, "count_ratio": 0.005154639175257732}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/TutulDevs_mui-practice/package-lock.json", "rule_id": "json.aws.security.public-s3-bucket.public-s3-bucket", "time": 0.10674500465393066}]}, "tainting_time": {"total_time": 0.2231442928314209, "per_def_and_rule_time": {"mean": 0.005312959353129069, "std_dev": 2.8490788871351025e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}