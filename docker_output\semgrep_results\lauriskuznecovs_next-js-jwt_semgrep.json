{"version": "1.130.0", "results": [{"check_id": "javascript.express.security.audit.express-check-csurf-middleware-usage.express-check-csurf-middleware-usage", "path": "downloaded_repos/lauriskuznecovs_next-js-jwt/backend/server.js", "start": {"line": 9, "col": 7, "offset": 266}, "end": {"line": 9, "col": 22, "offset": 281}, "extra": {"message": "A CSRF middleware was not detected in your express application. Ensure you are either using one such as `csurf` or `csrf` (see rule references) and/or you are properly doing CSRF validation in your routes with a token or cookies.", "metadata": {"category": "security", "references": ["https://www.npmjs.com/package/csurf", "https://www.npmjs.com/package/csrf", "https://cheatsheetseries.owasp.org/cheatsheets/Cross-Site_Request_Forgery_Prevention_Cheat_Sheet.html"], "cwe": ["CWE-352: Cross-Site Request Forgery (CSRF)"], "owasp": ["A01:2021 - Broken Access Control"], "technology": ["javascript", "typescript", "express"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/javascript.express.security.audit.express-check-csurf-middleware-usage.express-check-csurf-middleware-usage", "shortlink": "https://sg.run/BxzR"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/lauriskuznecovs_next-js-jwt/.gitignore", "downloaded_repos/lauriskuznecovs_next-js-jwt/LICENSE", "downloaded_repos/lauriskuznecovs_next-js-jwt/README.md", "downloaded_repos/lauriskuznecovs_next-js-jwt/backend/.eslintrc.js", "downloaded_repos/lauriskuznecovs_next-js-jwt/backend/.gitignore", "downloaded_repos/lauriskuznecovs_next-js-jwt/backend/README.md", "downloaded_repos/lauriskuznecovs_next-js-jwt/backend/controllers/authentication.js", "downloaded_repos/lauriskuznecovs_next-js-jwt/backend/models/User.js", "downloaded_repos/lauriskuznecovs_next-js-jwt/backend/package-lock.json", "downloaded_repos/lauriskuznecovs_next-js-jwt/backend/package.json", "downloaded_repos/lauriskuznecovs_next-js-jwt/backend/router.js", "downloaded_repos/lauriskuznecovs_next-js-jwt/backend/server.js", "downloaded_repos/lauriskuznecovs_next-js-jwt/backend/services/passport.js", "downloaded_repos/lauriskuznecovs_next-js-jwt/frontend/.babelrc", "downloaded_repos/lauriskuznecovs_next-js-jwt/frontend/.eslintrc", "downloaded_repos/lauriskuznecovs_next-js-jwt/frontend/.gitignore", "downloaded_repos/lauriskuznecovs_next-js-jwt/frontend/.prettierignore", "downloaded_repos/lauriskuznecovs_next-js-jwt/frontend/.prettierrc", "downloaded_repos/lauriskuznecovs_next-js-jwt/frontend/README.md", "downloaded_repos/lauriskuznecovs_next-js-jwt/frontend/components/Navigation.js", "downloaded_repos/lauriskuznecovs_next-js-jwt/frontend/next.config.js", "downloaded_repos/lauriskuznecovs_next-js-jwt/frontend/package-lock.json", "downloaded_repos/lauriskuznecovs_next-js-jwt/frontend/package.json", "downloaded_repos/lauriskuznecovs_next-js-jwt/frontend/pages/index.js", "downloaded_repos/lauriskuznecovs_next-js-jwt/frontend/pages/logout.js", "downloaded_repos/lauriskuznecovs_next-js-jwt/frontend/pages/secured.js", "downloaded_repos/lauriskuznecovs_next-js-jwt/frontend/utils/auth.js"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.6738250255584717, "profiling_times": {"config_time": 5.8472864627838135, "core_time": 2.42505145072937, "ignores_time": 0.0016090869903564453, "total_time": 8.274675369262695}, "parsing_time": {"total_time": 0.4490518569946289, "per_file_time": {"mean": 0.028065741062164303, "std_dev": 0.000440869078150996}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.2886767387390137, "per_file_time": {"mean": 0.01840966769627163, "std_dev": 0.0010845852681341146}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.3258035182952881, "per_file_and_rule_time": {"mean": 0.004654335975646972, "std_dev": 3.29400149023026e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.030689477920532227, "per_def_and_rule_time": {"mean": 0.0005790467532175892, "std_dev": 4.2898598120946374e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}