{"version": "1.130.0", "results": [{"check_id": "python.django.security.injection.request-data-write.request-data-write", "path": "downloaded_repos/kennell_curldrop/curldrop/app.py", "start": {"line": 23, "col": 9, "offset": 532}, "end": {"line": 23, "col": 30, "offset": 553}, "extra": {"message": "Found user-controlled request data passed into '.write(...)'. This could be dangerous if a malicious actor is able to control data into sensitive files. For example, a malicious actor could force rolling of critical log files, or cause a denial-of-service by using up available disk space. Instead, ensure that request data is properly escaped or sanitized.", "metadata": {"cwe": ["CWE-93: Improper Neutralization of CRLF Sequences ('CRLF Injection')"], "owasp": ["A03:2021 - Injection"], "category": "security", "technology": ["django"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/python.django.security.injection.request-data-write.request-data-write", "shortlink": "https://sg.run/0Q6j"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/kennell_curldrop/.gitignore", "downloaded_repos/kennell_curldrop/LICENSE.md", "downloaded_repos/kennell_curldrop/README.md", "downloaded_repos/kennell_curldrop/curldrop/__init__.py", "downloaded_repos/kennell_curldrop/curldrop/app.py", "downloaded_repos/kennell_curldrop/curldrop/cli.py", "downloaded_repos/kennell_curldrop/curldrop/server.py", "downloaded_repos/kennell_curldrop/focsec.png", "downloaded_repos/kennell_curldrop/requirements.in", "downloaded_repos/kennell_curldrop/requirements.txt", "downloaded_repos/kennell_curldrop/setup.py"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.7703897953033447, "profiling_times": {"config_time": 8.921542882919312, "core_time": 2.5370519161224365, "ignores_time": 0.002420663833618164, "total_time": 11.461869955062866}, "parsing_time": {"total_time": 0.04662370681762695, "per_file_time": {"mean": 0.009324741363525391, "std_dev": 2.0529389621515295e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.26154494285583496, "per_file_time": {"mean": 0.009686849735401295, "std_dev": 0.000311611063505431}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.04192781448364258, "per_file_and_rule_time": {"mean": 0.0008385562896728515, "std_dev": 2.289397685581207e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.005318880081176758, "per_def_and_rule_time": {"mean": 0.0001297287824677258, "std_dev": 2.033641853237394e-08}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}