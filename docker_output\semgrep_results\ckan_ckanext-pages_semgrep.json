{"version": "1.130.0", "results": [{"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/confirm_delete.html", "start": {"line": 14, "col": 7, "offset": 530}, "end": {"line": 17, "col": 14, "offset": 774}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/base_form.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 64, "col": 119, "offset": 2398}}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/base_form.html", "start": {"line": 80, "col": 59, "offset": 0}, "end": {"line": 80, "col": 60, "offset": 1}}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/base_form.html", "start": {"line": 92, "col": 61, "offset": 0}, "end": {"line": 92, "col": 62, "offset": 1}}]], "message": "Syntax error at line downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/base_form.html:1:\n `{% import 'macros/form.html' as form %}\n{% import 'macros/wysiwyg.html' as wysiwyg %}\n\n{% set data = data or {} %}\n{% set errors = errors or {} %}\n\n{% if type == 'org' %}\n    {% set prefix = 'organization_pages_' %}\n    {% set args = {'id': id} %}\n{% elif type == 'group' %}\n    {% set prefix = 'group_pages_' %}\n    {% set args = {'id': id} %}\n{% elif type == 'blog' %}\n    {% set prefix = 'blog_' %}\n    {% set args = {} %}\n{% else %}\n     {% set prefix = 'pages_' %}\n    {% set args = {} %}\n{% endif %}\n\n{% set page_url = page %}\n{% set prefix = 'pages.' + prefix %}\n\n{% set cancel_url = h.url_for(prefix + 'index', **args) %}\n{% set slug_prefix = cancel_url ~ '/' %}\n{% set slug_domain = h.url_for(prefix + 'index', qualified=true, **args) %}\n{% if page %}\n    {% if type == 'org' %}\n      {% set delete_url = h.url_for('pages.organization_pages_delete', page=page_url, **args) %}\n    {% elif type == 'group' %}\n      {% set delete_url = h.url_for('pages.group_pages_delete', page=page_url, **args) %}\n    {% else %}\n      {% set delete_url = h.url_for('pages.blog_delete' if type == 'blog' else 'pages.delete', page=page_url, **args) %}\n    {% endif %}\n{% endif %}\n\n{% if type == 'blog' %}\n    {% if not page %}\n      <h1>{{ _('Add Blog Article') }}</h1>\n    {% else %}\n      <h1>{{ _('Edit Blog Article') }}</h1>\n    {% endif %}\n    {% set url_placeholder = 'eg. my-blog-article' %}\n    {% set title_placeholder = _('eg. Blog Article Title') %}\n{% else %}\n    {% if not page %}\n      <h1>{{ _('Add page') }}</h1>\n    {% else %}\n      <h1>{{ _('Edit page') }}</h1>\n    {% endif %}\n    {% set url_placeholder = 'eg. my-page' %}\n    {% set title_placeholder = _('eg. Page Title') %}\n{% endif %}\n\n\n<form class=\"dataset-form pages-form\" method=\"post\" action=\"{{ action_url }}\" data-module=\"basic-form\">\n  {{ h.csrf_input() if 'csrf_input' in h }}\n  {% block errors %}{{ form.errors(error_summary) }}{% endblock %}\n\n  {% block form_basic_fields %}\n    {{ form.input('title', id='field-title', label=_('Title'), placeholder=title_placeholder, value... (truncated 367 more characters)", "path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/base_form.html", "spans": [{"file": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/base_form.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 64, "col": 119, "offset": 2398}}, {"file": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/base_form.html", "start": {"line": 80, "col": 59, "offset": 0}, "end": {"line": 80, "col": 60, "offset": 1}}, {"file": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/base_form.html", "start": {"line": 92, "col": 61, "offset": 0}, "end": {"line": 92, "col": 62, "offset": 1}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/blog.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 28, "offset": 167}}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/blog.html", "start": {"line": 33, "col": 1, "offset": 0}, "end": {"line": 36, "col": 73, "offset": 118}}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/blog.html", "start": {"line": 64, "col": 1, "offset": 0}, "end": {"line": 66, "col": 15, "offset": 44}}]], "message": "Syntax error at line downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/blog.html:1:\n `{% extends 'page.html' %}\n{% block bodytag %}{{ super() }} class=\"blog\"{% endblock %}\n{% block subtitle %}{{ c.page.title }}{% endblock %}\n\n{% block primary_content %}` was unexpected", "path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/blog.html", "spans": [{"file": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/blog.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 28, "offset": 167}}, {"file": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/blog.html", "start": {"line": 33, "col": 1, "offset": 0}, "end": {"line": 36, "col": 73, "offset": 118}}, {"file": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/blog.html", "start": {"line": 64, "col": 1, "offset": 0}, "end": {"line": 66, "col": 15, "offset": 44}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/header.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 34, "offset": 53}}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/header.html", "start": {"line": 14, "col": 3, "offset": 0}, "end": {"line": 15, "col": 15, "offset": 28}}]], "message": "Syntax error at line downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/header.html:1:\n `{% ckan_extends %}\n\n{% block header_account_logged %}` was unexpected", "path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/header.html", "spans": [{"file": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/header.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 34, "offset": 53}}, {"file": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/header.html", "start": {"line": 14, "col": 3, "offset": 0}, "end": {"line": 15, "col": 15, "offset": 28}}]}], "paths": {"scanned": ["downloaded_repos/ckan_ckanext-pages/.coveragerc", "downloaded_repos/ckan_ckanext-pages/.github/workflows/test.yml", "downloaded_repos/ckan_ckanext-pages/.gitignore", "downloaded_repos/ckan_ckanext-pages/CHANGELOG.md", "downloaded_repos/ckan_ckanext-pages/LICENSE", "downloaded_repos/ckan_ckanext-pages/MANIFEST.in", "downloaded_repos/ckan_ckanext-pages/README.md", "downloaded_repos/ckan_ckanext-pages/ckanext/__init__.py", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/__init__.py", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/actions.py", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/css/blog.css", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/css/bootstrap.css", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/css/ckedit.css", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/css/pages.css", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/js/ckedit.js", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/js/datepicker.js", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/js/pages-editor.js", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/webassets.yml", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/auth.py", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/blueprint.py", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/db.py", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/i18n/ckanext-pages.pot", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/i18n/de/LC_MESSAGES/ckanext-pages.mo", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/i18n/de/LC_MESSAGES/ckanext-pages.po", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/i18n/es/LC_MESSAGES/ckanext-pages.mo", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/i18n/es/LC_MESSAGES/ckanext-pages.po", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/interfaces.py", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/logic/__init__.py", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/logic/schema.py", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/migration/pages/README", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/migration/pages/alembic.ini", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/migration/pages/env.py", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/migration/pages/script.py.mako", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/migration/pages/versions/1725892d1d94_create_revisions_table.py", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/migration/pages/versions/a756dbd73ead_add_ckanext_pages_table.py", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/plugin.py", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/templates/textbox_form.html", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/templates/textbox_view.html", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/ckedit.css", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/ckedit.js", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/resource.config", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_group/group/read_base.html", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/base.html", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/base_form.html", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/blog.html", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/blog_edit.html", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/blog_edit_base.html", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/blog_list.html", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/blog_revisions.html", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/blog_revisions_preview.html", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/confirm_delete.html", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/edit_base.html", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/group_page.html", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/group_page_edit.html", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/group_page_list.html", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/organization_page.html", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/organization_page_edit.html", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/organization_page_list.html", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/page.html", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/page_edit.html", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/page_revisions.html", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/page_revisions_preview.html", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/pages_list.html", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/snippets/page_item.html", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/snippets/pages_list.html", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/header.html", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/macros/wysiwyg.html", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_organization/organization/read_base.html", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/utils.py", "downloaded_repos/ckan_ckanext-pages/ckanext/pages/validators.py", "downloaded_repos/ckan_ckanext-pages/conftest.py", "downloaded_repos/ckan_ckanext-pages/dev-requirements.txt", "downloaded_repos/ckan_ckanext-pages/requirements.txt", "downloaded_repos/ckan_ckanext-pages/setup.cfg", "downloaded_repos/ckan_ckanext-pages/setup.py", "downloaded_repos/ckan_ckanext-pages/test.ini"], "skipped": [{"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/CHANGES.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/LICENSE.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/adapters/jquery.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/build-config.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/ckeditor.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/config.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/contents.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/af.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/ar.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/az.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/bg.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/bn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/bs.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/cs.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/cy.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/da.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/de-ch.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/de.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/el.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/en-au.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/en-ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/en-gb.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/en.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/eo.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/es-mx.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/es.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/et.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/eu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/fa.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/fi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/fo.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/fr-ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/fr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/gl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/gu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/he.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/hi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/hr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/hu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/id.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/is.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/it.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/ja.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/ka.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/km.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/ko.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/ku.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/lt.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/lv.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/mk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/mn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/ms.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/nb.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/nl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/no.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/oc.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/pl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/pt-br.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/pt.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/ro.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/ru.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/si.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/sk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/sl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/sq.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/sr-latn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/sr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/sv.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/th.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/tr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/tt.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/ug.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/uk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/vi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/zh-cn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/lang/zh.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/a11yhelp.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/_translationstatus.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/af.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/ar.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/az.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/bg.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/cs.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/cy.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/da.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/de-ch.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/de.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/el.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/en-au.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/en-gb.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/en.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/eo.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/es-mx.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/es.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/et.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/eu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/fa.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/fi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/fo.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/fr-ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/fr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/gl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/gu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/he.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/hi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/hr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/hu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/id.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/it.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/ja.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/km.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/ko.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/ku.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/lt.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/lv.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/mk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/mn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/nb.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/nl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/no.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/oc.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/pl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/pt-br.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/pt.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/ro.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/ru.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/si.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/sk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/sl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/sq.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/sr-latn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/sr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/sv.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/th.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/tr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/tt.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/ug.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/uk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/vi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/zh-cn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/zh.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/about/dialogs/about.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/about/dialogs/hidpi/logo_ckeditor.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/about/dialogs/logo_ckeditor.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/dialogs/ckanview.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/icons/ckanview.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/icons/hidpi/ckanview.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/images/placeholder.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/af.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/ar.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/bg.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/bn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/bs.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/cs.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/cy.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/da.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/de.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/el.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/en-au.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/en-ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/en-gb.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/en.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/eo.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/es.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/et.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/eu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/fa.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/fi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/fo.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/fr-ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/fr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/gl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/gu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/he.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/hi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/hr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/hu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/id.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/is.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/it.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/ja.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/ka.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/km.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/ko.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/ku.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/lt.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/lv.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/mk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/mn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/ms.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/nb.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/nl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/no.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/pl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/pt-br.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/pt.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/ro.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/ru.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/si.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/sk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/sl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/sq.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/sr-latn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/sr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/sv.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/th.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/tr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/ug.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/uk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/vi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/zh-cn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/lang/zh.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/ckanview/plugin.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/clipboard/dialogs/paste.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/dialog/dialogDefinition.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/dialog/styles/dialog.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/divarea/plugin.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/divarea/samples/divarea.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/af.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/ar.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/az.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/bg.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/bn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/bs.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/cs.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/cy.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/da.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/de-ch.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/de.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/el.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/en-au.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/en-ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/en-gb.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/en.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/eo.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/es-mx.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/es.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/et.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/eu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/fa.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/fi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/fo.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/fr-ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/fr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/gl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/gu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/he.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/hi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/hr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/hu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/id.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/is.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/it.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/ja.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/ka.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/km.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/ko.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/ku.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/lt.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/lv.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/mk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/mn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/ms.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/nb.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/nl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/no.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/oc.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/pl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/pt-br.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/pt.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/ro.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/ru.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/si.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/sk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/sl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/sq.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/sr-latn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/sr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/sv.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/th.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/tr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/tt.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/ug.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/uk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/vi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/zh-cn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/lang/zh.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/font/plugin.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/icons.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/icons_hidpi.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/image/dialogs/image.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/image/images/noimage.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/link/dialogs/anchor.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/link/dialogs/link.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/link/images/anchor.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/link/images/hidpi/anchor.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/magicline/images/hidpi/icon-rtl.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/magicline/images/hidpi/icon.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/magicline/images/icon-rtl.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/magicline/images/icon.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/pastefromgdocs/filter/default.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/pastefromlibreoffice/filter/default.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/pastefromword/filter/default.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/pastetools/filter/common.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/pastetools/filter/image.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/scayt/CHANGELOG.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/scayt/LICENSE.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/scayt/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/scayt/dialogs/dialog.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/scayt/dialogs/options.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/scayt/dialogs/toolbar.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/scayt/skins/moono-lisa/scayt.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/_translationstatus.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/af.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/ar.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/az.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/bg.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/cs.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/cy.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/da.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/de-ch.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/de.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/el.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/en-au.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/en-ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/en-gb.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/en.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/eo.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/es-mx.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/es.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/et.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/eu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/fa.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/fi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/fr-ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/fr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/gl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/he.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/hr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/hu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/id.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/it.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/ja.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/km.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/ko.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/ku.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/lt.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/lv.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/nb.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/nl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/no.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/oc.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/pl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/pt-br.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/pt.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/ro.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/ru.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/si.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/sk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/sl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/sq.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/sr-latn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/sr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/sv.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/th.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/tr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/tt.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/ug.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/uk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/vi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/zh-cn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/lang/zh.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/specialchar/dialogs/specialchar.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/table/dialogs/table.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/tableselection/styles/tableselection.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/tabletools/dialogs/tableCell.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/dialogs/templates.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/dialogs/templates.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/icons/hidpi/templates-rtl.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/icons/hidpi/templates.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/icons/templates-rtl.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/icons/templates.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/af.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/ar.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/bg.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/bn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/bs.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/cs.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/cy.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/da.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/de.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/el.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/en-au.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/en-ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/en-gb.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/en.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/eo.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/es.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/et.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/eu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/fa.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/fi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/fo.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/fr-ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/fr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/gl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/gu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/he.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/hi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/hr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/hu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/id.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/is.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/it.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/ja.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/ka.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/km.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/ko.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/ku.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/lt.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/lv.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/mk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/mn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/ms.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/nb.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/nl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/no.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/pl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/pt-br.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/pt.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/ro.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/ru.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/si.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/sk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/sl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/sq.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/sr-latn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/sr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/sv.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/th.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/tr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/ug.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/uk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/vi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/zh-cn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/lang/zh.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/plugin.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/templates/default.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/templates/images/template1.gif", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/templates/images/template2.gif", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/templates/templates/images/template3.gif", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/widget/images/handle.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/LICENSE.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/dialogs/ciframe.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/dialogs/tmpFrameset.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/dialogs/wsc.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/dialogs/wsc.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/dialogs/wsc_ie.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/icons/hidpi/spellchecker.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/icons/spellchecker.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/af.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/ar.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/bg.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/bn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/bs.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/cs.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/cy.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/da.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/de.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/el.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/en-au.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/en-ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/en-gb.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/en.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/eo.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/es.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/et.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/eu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/fa.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/fi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/fo.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/fr-ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/fr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/gl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/gu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/he.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/hi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/hr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/hu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/is.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/it.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/ja.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/ka.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/km.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/ko.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/ku.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/lt.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/lv.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/mk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/mn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/ms.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/nb.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/nl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/no.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/pl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/pt-br.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/pt.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/ro.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/ru.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/sk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/sl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/sr-latn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/sr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/sv.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/th.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/tr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/ug.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/uk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/vi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/zh-cn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/lang/zh.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/plugin.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/plugins/wsc/skins/moono-lisa/wsc.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/css/samples.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/img/github-top.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/img/header-bg.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/img/header-separator.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/img/logo.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/img/logo.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/img/navigation-tip.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/index.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/js/sample.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/js/sf.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/ajax.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/api.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/appendto.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/assets/inlineall/logo.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/assets/outputxhtml/outputxhtml.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/assets/posteddata.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/assets/sample.jpg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/assets/uilanguages/languages.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/datafiltering.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/dialog/assets/my_dialog.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/dialog/dialog.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/divreplace.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/enterkey/enterkey.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/htmlwriter/assets/outputforflash/outputforflash.fla", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/htmlwriter/assets/outputforflash/outputforflash.swf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/htmlwriter/assets/outputforflash/swfobject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/htmlwriter/outputforflash.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/htmlwriter/outputhtml.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/index.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/inlineall.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/inlinebycode.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/inlinetextarea.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/jquery.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/magicline/magicline.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/readonly.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/replacebyclass.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/replacebycode.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/sample.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/sample.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/sample_posteddata.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/tabindex.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/toolbar/toolbar.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/uicolor.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/uilanguages.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/wysiwygarea/fullpage.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/old/xhtmlstyle.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/toolbarconfigurator/css/fontello.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/toolbarconfigurator/font/LICENSE.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/toolbarconfigurator/font/config.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/toolbarconfigurator/font/fontello.eot", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/toolbarconfigurator/font/fontello.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/toolbarconfigurator/font/fontello.ttf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/toolbarconfigurator/font/fontello.woff", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/toolbarconfigurator/index.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/toolbarconfigurator/js/abstracttoolbarmodifier.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/toolbarconfigurator/js/fulltoolbareditor.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/toolbarconfigurator/js/toolbarmodifier.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/toolbarconfigurator/js/toolbartextmodifier.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/toolbarconfigurator/lib/codemirror/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/toolbarconfigurator/lib/codemirror/codemirror.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/toolbarconfigurator/lib/codemirror/codemirror.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/toolbarconfigurator/lib/codemirror/javascript.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/toolbarconfigurator/lib/codemirror/neo.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/toolbarconfigurator/lib/codemirror/show-hint.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/samples/toolbarconfigurator/lib/codemirror/show-hint.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/skins/moono-lisa/dialog.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/skins/moono-lisa/dialog_ie.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/skins/moono-lisa/dialog_ie8.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/skins/moono-lisa/dialog_iequirks.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/skins/moono-lisa/editor.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/skins/moono-lisa/editor_gecko.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/skins/moono-lisa/editor_ie.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/skins/moono-lisa/editor_ie8.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/skins/moono-lisa/editor_iequirks.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/skins/moono-lisa/icons.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/skins/moono-lisa/icons_hidpi.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/skins/moono-lisa/images/arrow.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/skins/moono-lisa/images/close.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/skins/moono-lisa/images/hidpi/close.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/skins/moono-lisa/images/hidpi/lock-open.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/skins/moono-lisa/images/hidpi/lock.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/skins/moono-lisa/images/hidpi/refresh.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/skins/moono-lisa/images/lock-open.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/skins/moono-lisa/images/lock.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/skins/moono-lisa/images/refresh.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/skins/moono-lisa/images/spinner.gif", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/skins/moono-lisa/readme.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/styles.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/ckeditor/vendor/promise.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/datepicker/css/datepicker.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/datepicker/js/bootstrap-datepicker.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/datepicker/less/datepicker.less", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/medium/medium-editor.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/medium/medium-editor.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/assets/vendor/medium/medium-editor.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/tests/fixtures.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/tests/test_action.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/tests/test_logic.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/CHANGES.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/LICENSE.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/adapters/jquery.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/build-config.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/ckeditor.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/config.bak", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/contents.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/af.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/ar.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/bg.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/bn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/bs.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/cs.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/cy.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/da.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/de.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/el.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/en-au.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/en-ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/en-gb.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/en.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/eo.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/es.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/et.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/eu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/fa.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/fi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/fo.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/fr-ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/fr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/gl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/gu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/he.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/hi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/hr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/hu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/id.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/is.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/it.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/ja.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/ka.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/km.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/ko.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/ku.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/lt.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/lv.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/mk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/mn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/ms.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/nb.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/nl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/no.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/pl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/pt-br.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/pt.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/ro.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/ru.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/si.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/sk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/sl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/sq.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/sr-latn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/sr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/sv.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/th.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/tr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/ug.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/uk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/vi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/zh-cn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/lang/zh.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/a11yhelp.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/_translationstatus.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/ar.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/bg.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/cs.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/cy.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/da.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/de.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/el.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/en.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/eo.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/es.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/et.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/fa.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/fi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/fr-ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/fr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/gl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/gu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/he.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/hi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/hr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/hu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/id.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/it.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/ja.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/km.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/ko.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/ku.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/lt.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/lv.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/mk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/mn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/nb.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/nl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/no.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/pl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/pt-br.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/pt.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/ro.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/ru.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/si.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/sk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/sl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/sq.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/sr-latn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/sr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/sv.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/th.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/tr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/ug.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/uk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/vi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/zh-cn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/a11yhelp/dialogs/lang/zh.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/about/dialogs/about.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/about/dialogs/hidpi/logo_ckeditor.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/about/dialogs/logo_ckeditor.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/dialogs/ckanview.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/icons/ckanview.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/icons/hidpi/ckanview.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/images/placeholder.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/af.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/ar.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/bg.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/bn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/bs.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/cs.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/cy.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/da.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/de.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/el.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/en-au.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/en-ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/en-gb.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/en.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/eo.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/es.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/et.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/eu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/fa.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/fi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/fo.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/fr-ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/fr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/gl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/gu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/he.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/hi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/hr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/hu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/id.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/is.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/it.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/ja.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/ka.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/km.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/ko.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/ku.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/lt.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/lv.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/mk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/mn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/ms.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/nb.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/nl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/no.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/pl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/pt-br.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/pt.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/ro.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/ru.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/si.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/sk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/sl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/sq.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/sr-latn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/sr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/sv.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/th.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/tr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/ug.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/uk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/vi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/zh-cn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/lang/zh.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/ckanview/plugin.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/clipboard/dialogs/paste.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/dialog/dialogDefinition.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/dialogs/div.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/icons/creatediv.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/icons/hidpi/creatediv.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/af.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/ar.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/bg.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/bn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/bs.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/cs.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/cy.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/da.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/de.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/el.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/en-au.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/en-ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/en-gb.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/en.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/eo.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/es.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/et.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/eu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/fa.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/fi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/fo.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/fr-ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/fr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/gl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/gu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/he.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/hi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/hr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/hu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/id.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/is.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/it.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/ja.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/ka.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/km.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/ko.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/ku.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/lt.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/lv.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/mk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/mn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/ms.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/nb.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/nl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/no.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/pl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/pt-br.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/pt.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/ro.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/ru.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/si.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/sk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/sl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/sq.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/sr-latn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/sr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/sv.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/th.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/tr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/ug.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/uk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/vi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/zh-cn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/lang/zh.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/div/plugin.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/divarea/plugin.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/divarea/samples/divarea.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/fakeobjects/images/spacer.gif", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/icons.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/icons_hidpi.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/image/dialogs/image.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/image/images/noimage.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/link/dialogs/anchor.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/link/dialogs/link.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/link/images/anchor.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/link/images/hidpi/anchor.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/magicline/images/hidpi/icon.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/magicline/images/icon.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/pastefromword/filter/default.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/scayt/LICENSE.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/scayt/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/scayt/dialogs/options.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/scayt/dialogs/toolbar.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/_translationstatus.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/ar.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/bg.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/cs.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/cy.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/de.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/el.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/en.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/eo.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/es.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/et.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/fa.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/fi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/fr-ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/fr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/gl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/he.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/hr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/hu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/id.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/it.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/ja.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/km.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/ku.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/lv.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/nb.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/nl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/no.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/pl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/pt-br.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/pt.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/ru.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/si.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/sk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/sl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/sq.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/sv.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/th.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/tr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/ug.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/uk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/vi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/zh-cn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/lang/zh.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/specialchar/dialogs/specialchar.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/table/dialogs/table.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/tabletools/dialogs/tableCell.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/wsc/LICENSE.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/wsc/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/wsc/dialogs/ciframe.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/wsc/dialogs/tmp.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/wsc/dialogs/tmpFrameset.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/wsc/dialogs/wsc.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/wsc/dialogs/wsc.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/plugins/wsc/dialogs/wsc_ie.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/ajax.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/api.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/appendto.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/assets/inlineall/logo.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/assets/outputxhtml/outputxhtml.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/assets/posteddata.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/assets/sample.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/assets/sample.jpg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/assets/uilanguages/languages.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/datafiltering.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/divreplace.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/index.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/inlineall.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/inlinebycode.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/inlinetextarea.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/jquery.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/plugins/dialog/assets/my_dialog.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/plugins/dialog/dialog.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/plugins/enterkey/enterkey.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/plugins/htmlwriter/assets/outputforflash/outputforflash.fla", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/plugins/htmlwriter/assets/outputforflash/outputforflash.swf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/plugins/htmlwriter/assets/outputforflash/swfobject.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/plugins/htmlwriter/outputforflash.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/plugins/htmlwriter/outputhtml.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/plugins/magicline/magicline.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/plugins/toolbar/toolbar.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/plugins/wysiwygarea/fullpage.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/readonly.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/replacebyclass.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/replacebycode.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/sample.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/sample.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/sample_posteddata.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/tabindex.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/uicolor.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/uilanguages.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/samples/xhtmlstyle.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/skins/moono/dialog.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/skins/moono/dialog_ie.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/skins/moono/dialog_ie7.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/skins/moono/dialog_ie8.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/skins/moono/dialog_iequirks.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/skins/moono/dialog_opera.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/skins/moono/editor.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/skins/moono/editor_gecko.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/skins/moono/editor_ie.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/skins/moono/editor_ie7.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/skins/moono/editor_ie8.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/skins/moono/editor_iequirks.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/skins/moono/icons.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/skins/moono/icons_hidpi.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/skins/moono/images/arrow.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/skins/moono/images/close.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/skins/moono/images/hidpi/close.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/skins/moono/images/hidpi/lock-open.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/skins/moono/images/hidpi/lock.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/skins/moono/images/hidpi/refresh.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/skins/moono/images/lock-open.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/skins/moono/images/lock.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/skins/moono/images/refresh.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/skins/moono/readme.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/textbox/theme/vendor/ckeditor/styles.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/base_form.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/ckanext_pages/blog.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/theme/templates_main/header.html", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.8893918991088867, "profiling_times": {"config_time": 6.884150981903076, "core_time": 4.161469459533691, "ignores_time": 0.0017647743225097656, "total_time": 11.048664331436157}, "parsing_time": {"total_time": 0.4867722988128662, "per_file_time": {"mean": 0.01802860365973579, "std_dev": 0.00019114187890313645}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 3.2216086387634277, "per_file_time": {"mean": 0.01579219920962463, "std_dev": 0.00253369331006716}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.5952534675598145, "per_file_and_rule_time": {"mean": 0.003021591205887383, "std_dev": 3.1067936455866505e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.365964412689209, "per_def_and_rule_time": {"mean": 0.0011920664908443288, "std_dev": 8.623346728430954e-05}, "very_slow_stats": {"time_ratio": 0.4368155385852195, "count_ratio": 0.003257328990228013}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/ckan_ckanext-pages/ckanext/pages/utils.py", "fline": 100, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.15985894203186035}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}