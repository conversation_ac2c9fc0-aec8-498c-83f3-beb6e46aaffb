{"version": "1.130.0", "results": [{"check_id": "python.django.security.audit.unvalidated-password.unvalidated-password", "path": "downloaded_repos/Corvia_django-tenant-users/tenant_users/tenants/models.py", "start": {"line": 285, "col": 9, "offset": 9774}, "end": {"line": 285, "col": 39, "offset": 9804}, "extra": {"message": "The password on 'profile' is being set without validating the password. Call django.contrib.auth.password_validation.validate_password() with validation functions before setting the password. See https://docs.djangoproject.com/en/3.0/topics/auth/passwords/ for more information.", "fix": "if django.contrib.auth.password_validation.validate_password(password, user=profile):\n            profile.set_password(password)", "metadata": {"cwe": ["CWE-521: Weak Password Requirements"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://docs.djangoproject.com/en/3.0/topics/auth/passwords/#module-django.contrib.auth.password_validation"], "category": "security", "technology": ["django"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/python.django.security.audit.unvalidated-password.unvalidated-password", "shortlink": "https://sg.run/OPBL"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.unvalidated-password.unvalidated-password", "path": "downloaded_repos/Corvia_django-tenant-users/tenant_users/tenants/utils.py", "start": {"line": 114, "col": 9, "offset": 3755}, "end": {"line": 114, "col": 54, "offset": 3800}, "extra": {"message": "The password on 'profile' is being set without validating the password. Call django.contrib.auth.password_validation.validate_password() with validation functions before setting the password. See https://docs.djangoproject.com/en/3.0/topics/auth/passwords/ for more information.", "fix": "if django.contrib.auth.password_validation.validate_password(owner_extra[\"password\"], user=profile):\n            profile.set_password(owner_extra[\"password\"])", "metadata": {"cwe": ["CWE-521: Weak Password Requirements"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://docs.djangoproject.com/en/3.0/topics/auth/passwords/#module-django.contrib.auth.password_validation"], "category": "security", "technology": ["django"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/python.django.security.audit.unvalidated-password.unvalidated-password", "shortlink": "https://sg.run/OPBL"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/Corvia_django-tenant-users/.github/workflows/test.yml", "start": {"line": 69, "col": 38, "offset": 1746}, "end": {"line": 69, "col": 61, "offset": 1769}}]], "message": "Syntax error at line downloaded_repos/Corvia_django-tenant-users/.github/workflows/test.yml:69:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ matrix.django-version` was unexpected", "path": "downloaded_repos/Corvia_django-tenant-users/.github/workflows/test.yml", "spans": [{"file": "downloaded_repos/Corvia_django-tenant-users/.github/workflows/test.yml", "start": {"line": 69, "col": 38, "offset": 1746}, "end": {"line": 69, "col": 61, "offset": 1769}}]}], "paths": {"scanned": ["downloaded_repos/Corvia_django-tenant-users/.git-blame-ignore-revs", "downloaded_repos/Corvia_django-tenant-users/.github/ISSUE_TEMPLATE/bug_report.md", "downloaded_repos/Corvia_django-tenant-users/.github/ISSUE_TEMPLATE/feature_request.md", "downloaded_repos/Corvia_django-tenant-users/.github/dependabot.yml", "downloaded_repos/Corvia_django-tenant-users/.github/pull_request_template.md", "downloaded_repos/Corvia_django-tenant-users/.github/workflows/test.yml", "downloaded_repos/Corvia_django-tenant-users/.gitignore", "downloaded_repos/Corvia_django-tenant-users/.readthedocs.yml", "downloaded_repos/Corvia_django-tenant-users/CHANGELOG.md", "downloaded_repos/Corvia_django-tenant-users/LICENSE", "downloaded_repos/Corvia_django-tenant-users/README.rst", "downloaded_repos/Corvia_django-tenant-users/django_test_app/__init__.py", "downloaded_repos/Corvia_django-tenant-users/django_test_app/companies/__init__.py", "downloaded_repos/Corvia_django-tenant-users/django_test_app/companies/apps.py", "downloaded_repos/Corvia_django-tenant-users/django_test_app/companies/migrations/0001_initial.py", "downloaded_repos/Corvia_django-tenant-users/django_test_app/companies/migrations/0002_company_owner.py", "downloaded_repos/Corvia_django-tenant-users/django_test_app/companies/migrations/0003_alter_company_created_alter_company_modified.py", "downloaded_repos/Corvia_django-tenant-users/django_test_app/companies/migrations/0004_company_type.py", "downloaded_repos/Corvia_django-tenant-users/django_test_app/companies/migrations/__init__.py", "downloaded_repos/Corvia_django-tenant-users/django_test_app/companies/models.py", "downloaded_repos/Corvia_django-tenant-users/django_test_app/settings.py", "downloaded_repos/Corvia_django-tenant-users/django_test_app/users/__init__.py", "downloaded_repos/Corvia_django-tenant-users/django_test_app/users/apps.py", "downloaded_repos/Corvia_django-tenant-users/django_test_app/users/migrations/0001_initial.py", "downloaded_repos/Corvia_django-tenant-users/django_test_app/users/migrations/__init__.py", "downloaded_repos/Corvia_django-tenant-users/django_test_app/users/models.py", "downloaded_repos/Corvia_django-tenant-users/docs/Makefile", "downloaded_repos/Corvia_django-tenant-users/docs/conf.py", "downloaded_repos/Corvia_django-tenant-users/docs/index.rst", "downloaded_repos/Corvia_django-tenant-users/docs/pages/concepts.rst", "downloaded_repos/Corvia_django-tenant-users/docs/pages/conributing.rst", "downloaded_repos/Corvia_django-tenant-users/docs/pages/installation.rst", "downloaded_repos/Corvia_django-tenant-users/docs/pages/models.rst", "downloaded_repos/Corvia_django-tenant-users/docs/pages/permissions.rst", "downloaded_repos/Corvia_django-tenant-users/docs/pages/using.rst", "downloaded_repos/Corvia_django-tenant-users/docs/pages/utilities.rst", "downloaded_repos/Corvia_django-tenant-users/manage.py", "downloaded_repos/Corvia_django-tenant-users/poetry.lock", "downloaded_repos/Corvia_django-tenant-users/pyproject.toml", "downloaded_repos/Corvia_django-tenant-users/setup.cfg", "downloaded_repos/Corvia_django-tenant-users/tenant_users/__init__.py", "downloaded_repos/Corvia_django-tenant-users/tenant_users/constants.py", "downloaded_repos/Corvia_django-tenant-users/tenant_users/permissions/__init__.py", "downloaded_repos/Corvia_django-tenant-users/tenant_users/permissions/backend.py", "downloaded_repos/Corvia_django-tenant-users/tenant_users/permissions/functional.py", "downloaded_repos/Corvia_django-tenant-users/tenant_users/permissions/migrations/0001_initial.py", "downloaded_repos/Corvia_django-tenant-users/tenant_users/permissions/migrations/__init__.py", "downloaded_repos/Corvia_django-tenant-users/tenant_users/permissions/models.py", "downloaded_repos/Corvia_django-tenant-users/tenant_users/tenants/__init__.py", "downloaded_repos/Corvia_django-tenant-users/tenant_users/tenants/apps.py", "downloaded_repos/Corvia_django-tenant-users/tenant_users/tenants/management/__init__.py", "downloaded_repos/Corvia_django-tenant-users/tenant_users/tenants/management/commands/__init__.py", "downloaded_repos/Corvia_django-tenant-users/tenant_users/tenants/management/commands/create_public_tenant.py", "downloaded_repos/Corvia_django-tenant-users/tenant_users/tenants/middleware.py", "downloaded_repos/Corvia_django-tenant-users/tenant_users/tenants/migrations/__init__.py", "downloaded_repos/Corvia_django-tenant-users/tenant_users/tenants/models.py", "downloaded_repos/Corvia_django-tenant-users/tenant_users/tenants/tasks.py", "downloaded_repos/Corvia_django-tenant-users/tenant_users/tenants/utils.py"], "skipped": [{"path": "downloaded_repos/Corvia_django-tenant-users/.github/workflows/test.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/Corvia_django-tenant-users/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Corvia_django-tenant-users/tests/conftest.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Corvia_django-tenant-users/tests/fixtures/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Corvia_django-tenant-users/tests/fixtures/db.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Corvia_django-tenant-users/tests/fixtures/settings.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Corvia_django-tenant-users/tests/fixtures/tenant.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Corvia_django-tenant-users/tests/test_tenants/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Corvia_django-tenant-users/tests/test_tenants/test_backend.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Corvia_django-tenant-users/tests/test_tenants/test_commands.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Corvia_django-tenant-users/tests/test_tenants/test_functional.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Corvia_django-tenant-users/tests/test_tenants/test_middleware.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Corvia_django-tenant-users/tests/test_tenants/test_models/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Corvia_django-tenant-users/tests/test_tenants/test_models/test_profilemanager.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Corvia_django-tenant-users/tests/test_tenants/test_models/test_signals.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Corvia_django-tenant-users/tests/test_tenants/test_models/test_userprofile.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Corvia_django-tenant-users/tests/test_tenants/test_multitypes.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Corvia_django-tenant-users/tests/test_tenants/test_permission_models.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Corvia_django-tenant-users/tests/test_tenants/test_tasks.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Corvia_django-tenant-users/tests/test_tenants/test_tenants_models.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Corvia_django-tenant-users/tests/test_tenants/test_utils.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.8545129299163818, "profiling_times": {"config_time": 7.143303155899048, "core_time": 3.1071829795837402, "ignores_time": 0.002124786376953125, "total_time": 10.253436088562012}, "parsing_time": {"total_time": 0.39968442916870117, "per_file_time": {"mean": 0.01051801129391319, "std_dev": 0.00019949548649589492}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 2.8249292373657227, "per_file_time": {"mean": 0.018343696346530658, "std_dev": 0.0024319472494987683}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.5135765075683594, "per_file_and_rule_time": {"mean": 0.0018020228335731909, "std_dev": 2.523161563856277e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.09609723091125488, "per_def_and_rule_time": {"mean": 0.00023211891524457695, "std_dev": 1.8696004509589803e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089476480}, "engine_requested": "OSS", "skipped_rules": []}