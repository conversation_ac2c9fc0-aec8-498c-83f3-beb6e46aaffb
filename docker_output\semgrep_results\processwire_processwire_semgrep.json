{"version": "1.130.0", "results": [{"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/processwire_processwire/install.php", "start": {"line": 1287, "col": 19, "offset": 45254}, "end": {"line": 1287, "col": 40, "offset": 45275}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "path": "downloaded_repos/processwire_processwire/wire/core/FileCompiler.php", "start": {"line": 377, "col": 8, "offset": 10594}, "end": {"line": 377, "col": 47, "offset": 10633}, "extra": {"message": "Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues", "metadata": {"cwe": ["CWE-697: Incorrect Comparison"], "references": ["https://www.php.net/manual/en/types.comparisons.php", "https://web.archive.org/web/20210430183236/https://www.whitehatsec.com/blog/magic-hashes/"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.md5-loose-equality.md5-loose-equality", "shortlink": "https://sg.run/Do4G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "path": "downloaded_repos/processwire_processwire/wire/core/FileCompiler.php", "start": {"line": 377, "col": 51, "offset": 10637}, "end": {"line": 377, "col": 90, "offset": 10676}, "extra": {"message": "Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues", "metadata": {"cwe": ["CWE-697: Incorrect Comparison"], "references": ["https://www.php.net/manual/en/types.comparisons.php", "https://web.archive.org/web/20210430183236/https://www.whitehatsec.com/blog/magic-hashes/"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.md5-loose-equality.md5-loose-equality", "shortlink": "https://sg.run/Do4G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "path": "downloaded_repos/processwire_processwire/wire/core/FileCompiler.php", "start": {"line": 444, "col": 56, "offset": 13226}, "end": {"line": 444, "col": 73, "offset": 13243}, "extra": {"message": "Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues", "metadata": {"cwe": ["CWE-697: Incorrect Comparison"], "references": ["https://www.php.net/manual/en/types.comparisons.php", "https://web.archive.org/web/20210430183236/https://www.whitehatsec.com/blog/magic-hashes/"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.md5-loose-equality.md5-loose-equality", "shortlink": "https://sg.run/Do4G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.md5-loose-equality.md5-loose-equality", "path": "downloaded_repos/processwire_processwire/wire/core/FileCompiler.php", "start": {"line": 469, "col": 7, "offset": 13821}, "end": {"line": 469, "col": 30, "offset": 13844}, "extra": {"message": "Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues", "metadata": {"cwe": ["CWE-697: Incorrect Comparison"], "references": ["https://www.php.net/manual/en/types.comparisons.php", "https://web.archive.org/web/20210430183236/https://www.whitehatsec.com/blog/magic-hashes/"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.md5-loose-equality.md5-loose-equality", "shortlink": "https://sg.run/Do4G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/processwire_processwire/wire/core/ImageSizerEngine.php", "start": {"line": 658, "col": 28, "offset": 16464}, "end": {"line": 658, "col": 41, "offset": 16477}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/processwire_processwire/wire/core/ProcessWire.php", "start": {"line": 1110, "col": 20, "offset": 32237}, "end": {"line": 1110, "col": 59, "offset": 32276}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/processwire_processwire/wire/core/ProcessWire.php", "start": {"line": 1268, "col": 14, "offset": 38160}, "end": {"line": 1268, "col": 28, "offset": 38174}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/processwire_processwire/wire/core/ProcessWire.php", "start": {"line": 1271, "col": 21, "offset": 38273}, "end": {"line": 1271, "col": 32, "offset": 38284}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/processwire_processwire/wire/core/Session.php", "start": {"line": 1428, "col": 3, "offset": 41021}, "end": {"line": 1428, "col": 33, "offset": 41051}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/processwire_processwire/wire/core/Tfa.php", "start": {"line": 363, "col": 4, "offset": 12658}, "end": {"line": 363, "col": 42, "offset": 12696}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/processwire_processwire/wire/core/Tfa.php", "start": {"line": 366, "col": 4, "offset": 12807}, "end": {"line": 366, "col": 36, "offset": 12839}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/processwire_processwire/wire/core/Tfa.php", "start": {"line": 759, "col": 4, "offset": 24903}, "end": {"line": 759, "col": 63, "offset": 24962}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "path": "downloaded_repos/processwire_processwire/wire/core/Tfa.php", "start": {"line": 899, "col": 20, "offset": 29543}, "end": {"line": 899, "col": 49, "offset": 29572}, "extra": {"message": "The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs.", "metadata": {"references": ["https://symfony.com/doc/current/controller.html#redirecting", "https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "category": "security", "technology": ["symfony"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect", "shortlink": "https://sg.run/4ey5"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/processwire_processwire/wire/core/WireDatabaseBackup.php", "start": {"line": 704, "col": 11, "offset": 19766}, "end": {"line": 704, "col": 24, "offset": 19779}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/processwire_processwire/wire/core/WireDatabaseBackup.php", "start": {"line": 938, "col": 3, "offset": 26240}, "end": {"line": 938, "col": 14, "offset": 26251}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/processwire_processwire/wire/core/WireDatabaseBackup.php", "start": {"line": 1123, "col": 3, "offset": 32198}, "end": {"line": 1123, "col": 22, "offset": 32217}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/processwire_processwire/wire/core/WireDatabaseBackup.php", "start": {"line": 1447, "col": 3, "offset": 42764}, "end": {"line": 1447, "col": 42, "offset": 42803}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/processwire_processwire/wire/core/WireFileTools.php", "start": {"line": 360, "col": 7, "offset": 12930}, "end": {"line": 360, "col": 24, "offset": 12947}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.mcrypt-use.mcrypt-use", "path": "downloaded_repos/processwire_processwire/wire/core/WireRandom.php", "start": {"line": 351, "col": 5, "offset": 11245}, "end": {"line": 351, "col": 88, "offset": 11328}, "extra": {"message": "Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL.", "metadata": {"cwe": ["CWE-676: Use of Potentially Dangerous Function"], "references": ["https://www.php.net/manual/en/intro.mcrypt.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/CryptoFunctionsSniff.php"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Dangerous Method or Function"], "source": "https://semgrep.dev/r/php.lang.security.mcrypt-use.mcrypt-use", "shortlink": "https://sg.run/BkZR"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.mcrypt-use.mcrypt-use", "path": "downloaded_repos/processwire_processwire/wire/core/WireRandom.php", "start": {"line": 836, "col": 5, "offset": 27035}, "end": {"line": 836, "col": 65, "offset": 27095}, "extra": {"message": "Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL.", "metadata": {"cwe": ["CWE-676: Use of Potentially Dangerous Function"], "references": ["https://www.php.net/manual/en/intro.mcrypt.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/CryptoFunctionsSniff.php"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Dangerous Method or Function"], "source": "https://semgrep.dev/r/php.lang.security.mcrypt-use.mcrypt-use", "shortlink": "https://sg.run/BkZR"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout.js", "start": {"line": 54, "col": 12, "offset": 1582}, "end": {"line": 54, "col": 20, "offset": 1590}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout.js", "start": {"line": 914, "col": 5, "offset": 35751}, "end": {"line": 914, "col": 25, "offset": 35771}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout.js", "start": {"line": 986, "col": 7, "offset": 38074}, "end": {"line": 986, "col": 36, "offset": 38103}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout.js", "start": {"line": 992, "col": 6, "offset": 38233}, "end": {"line": 992, "col": 30, "offset": 38257}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout.js", "start": {"line": 1151, "col": 14, "offset": 42873}, "end": {"line": 1151, "col": 27, "offset": 42886}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout.js", "start": {"line": 1154, "col": 12, "offset": 42958}, "end": {"line": 1154, "col": 20, "offset": 42966}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout.js", "start": {"line": 4820, "col": 9, "offset": 165753}, "end": {"line": 4820, "col": 51, "offset": 165795}, "extra": {"message": "RegExp() called with a `oPane` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout.js", "start": {"line": 4820, "col": 9, "offset": 165753}, "end": {"line": 4820, "col": 51, "offset": 165795}, "extra": {"message": "RegExp() called with a `pane` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout_and_plugins.js", "start": {"line": 54, "col": 12, "offset": 1582}, "end": {"line": 54, "col": 20, "offset": 1590}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout_and_plugins.js", "start": {"line": 914, "col": 5, "offset": 35751}, "end": {"line": 914, "col": 25, "offset": 35771}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout_and_plugins.js", "start": {"line": 986, "col": 7, "offset": 38074}, "end": {"line": 986, "col": 36, "offset": 38103}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout_and_plugins.js", "start": {"line": 992, "col": 6, "offset": 38233}, "end": {"line": 992, "col": 30, "offset": 38257}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout_and_plugins.js", "start": {"line": 1151, "col": 14, "offset": 42873}, "end": {"line": 1151, "col": 27, "offset": 42886}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout_and_plugins.js", "start": {"line": 1154, "col": 12, "offset": 42958}, "end": {"line": 1154, "col": 20, "offset": 42966}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout_and_plugins.js", "start": {"line": 4820, "col": 9, "offset": 165753}, "end": {"line": 4820, "col": 51, "offset": 165795}, "extra": {"message": "RegExp() called with a `oPane` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout_and_plugins.js", "start": {"line": 4820, "col": 9, "offset": 165753}, "end": {"line": 4820, "col": 51, "offset": 165795}, "extra": {"message": "RegExp() called with a `pane` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeComments/comments.js", "start": {"line": 31, "col": 14, "offset": 646}, "end": {"line": 31, "col": 55, "offset": 687}, "extra": {"message": "RegExp() called with a `name` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeComments/comments.js", "start": {"line": 46, "col": 3, "offset": 1049}, "end": {"line": 46, "col": 38, "offset": 1084}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeRepeater/InputfieldRepeater.js", "start": {"line": 1517, "col": 3, "offset": 50480}, "end": {"line": 1517, "col": 22, "offset": 50499}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/pastetools/filter/common.js", "start": {"line": 23, "col": 82, "offset": 9813}, "end": {"line": 23, "col": 109, "offset": 9840}, "extra": {"message": "RegExp() called with a `b` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/pastetools/filter/image.js", "start": {"line": 5, "col": 353, "offset": 518}, "end": {"line": 5, "col": 400, "offset": 565}, "extra": {"message": "RegExp() called with a `b` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/samples/sourcedialog.html", "start": {"line": 75, "col": 64, "offset": 2624}, "end": {"line": 75, "col": 107, "offset": 2667}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/samples/sourcedialog.html", "start": {"line": 109, "col": 50, "offset": 3439}, "end": {"line": 110, "col": 28, "offset": 3514}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/samples/sourcedialog.html", "start": {"line": 113, "col": 32, "offset": 3569}, "end": {"line": 113, "col": 91, "offset": 3628}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.eval-detected.eval-detected", "path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/jquery-ui-timepicker-addon.js", "start": {"line": 180, "col": 35, "offset": 4347}, "end": {"line": 180, "col": 50, "offset": 4362}, "extra": {"message": "Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources.", "metadata": {"cwe": ["CWE-95: Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection')"], "owasp": ["A03:2021 - Injection"], "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/javascript.browser.security.eval-detected.eval-detected", "shortlink": "https://sg.run/7ope"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.incomplete-sanitization.incomplete-sanitization", "path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/jquery-ui-timepicker-addon.js", "start": {"line": 565, "col": 27, "offset": 19790}, "end": {"line": 565, "col": 97, "offset": 19860}, "extra": {"message": "`$g.css(rtl ? 'marginRight' : 'marginLeft').toString().replace` method will only replace the first occurrence when used with a string argument ('%'). If this method is used for escaping of dangerous data then there is a possibility for a bypass. Try to use sanitization library instead or use a Regex with a global flag.", "metadata": {"cwe": ["CWE-116: Improper Encoding or Escaping of Output"], "category": "security", "technology": ["javascript"], "owasp": ["A03:2021 - Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Encoding"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.incomplete-sanitization.incomplete-sanitization", "shortlink": "https://sg.run/1GbQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/jquery-ui-timepicker-addon.js", "start": {"line": 1256, "col": 19, "offset": 45669}, "end": {"line": 1256, "col": 42, "offset": 45692}, "extra": {"message": "RegExp() called with a `f` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/jquery-ui-timepicker-addon.js", "start": {"line": 1256, "col": 19, "offset": 45669}, "end": {"line": 1256, "col": 42, "offset": 45692}, "extra": {"message": "RegExp() called with a `o` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/InputfieldTinyMCE.js", "start": {"line": 138, "col": 16, "offset": 2850}, "end": {"line": 138, "col": 17, "offset": 2851}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.unicode.security.bidi.contains-bidirectional-characters", "path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/ar.js", "start": {"line": 41, "col": 79, "offset": 2398}, "end": {"line": 41, "col": 82, "offset": 2401}, "extra": {"message": "This code contains bidirectional (bidi) characters. While this is useful for support of right-to-left languages such as Arabic or Hebrew, it can also be used to trick language parsers into executing code in a manner that is different from how it is displayed in code editing and review tools. If this is not what you were expecting, please review this code in an editor that can reveal hidden Unicode characters.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "category": "security", "technology": ["unicode"], "references": ["https://trojansource.codes/"], "confidence": "LOW", "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/generic.unicode.security.bidi.contains-bidirectional-characters", "shortlink": "https://sg.run/nK4r"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/dev/jquery-migrate-debug-3.4.0.js", "start": {"line": 850, "col": 4, "offset": 27590}, "end": {"line": 850, "col": 30, "offset": 27616}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/iframe-resizer.js", "start": {"line": 452, "col": 4, "offset": 11626}, "end": {"line": 452, "col": 56, "offset": 11678}, "extra": {"message": "The target origin of the window.postMessage() API is set to \"*\". This could allow for information disclosure due to the possibility of any origin allowed to receive the message.", "metadata": {"owasp": ["A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-345: Insufficient Verification of Data Authenticity"], "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration", "shortlink": "https://sg.run/PJ4p"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/xregexp.js", "start": {"line": 288, "col": 19, "offset": 12396}, "end": {"line": 294, "col": 14, "offset": 12678}, "extra": {"message": "RegExp() called with a `XRegExp` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/xregexp.js", "start": {"line": 288, "col": 19, "offset": 12396}, "end": {"line": 294, "col": 14, "offset": 12678}, "extra": {"message": "RegExp() called with a `flags` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/xregexp.js", "start": {"line": 288, "col": 19, "offset": 12396}, "end": {"line": 294, "col": 14, "offset": 12678}, "extra": {"message": "RegExp() called with a `left` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/xregexp.js", "start": {"line": 288, "col": 19, "offset": 12396}, "end": {"line": 294, "col": 14, "offset": 12678}, "extra": {"message": "RegExp() called with a `options` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/xregexp.js", "start": {"line": 288, "col": 19, "offset": 12396}, "end": {"line": 294, "col": 14, "offset": 12678}, "extra": {"message": "RegExp() called with a `right` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/xregexp.js", "start": {"line": 2813, "col": 9, "offset": 162322}, "end": {"line": 2813, "col": 40, "offset": 162353}, "extra": {"message": "RegExp() called with a `regex` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/xregexp.js", "start": {"line": 3207, "col": 9, "offset": 175283}, "end": {"line": 3207, "col": 55, "offset": 175329}, "extra": {"message": "RegExp() called with a `flags` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/xregexp.js", "start": {"line": 3207, "col": 9, "offset": 175283}, "end": {"line": 3207, "col": 55, "offset": 175329}, "extra": {"message": "RegExp() called with a `pattern` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/xregexp.js", "start": {"line": 3737, "col": 14, "offset": 195726}, "end": {"line": 3737, "col": 61, "offset": 195773}, "extra": {"message": "RegExp() called with a `search` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/xregexp.js", "start": {"line": 4028, "col": 17, "offset": 206061}, "end": {"line": 4028, "col": 34, "offset": 206078}, "extra": {"message": "RegExp() called with a `regex` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryMagnific/JqueryMagnific.js", "start": {"line": 67, "col": 4, "offset": 1491}, "end": {"line": 67, "col": 24, "offset": 1511}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/JqueryTableSorter.js", "start": {"line": 1064, "col": 7, "offset": 42612}, "end": {"line": 1064, "col": 29, "offset": 42634}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/JqueryTableSorter.js", "start": {"line": 2020, "col": 19, "offset": 76900}, "end": {"line": 2020, "col": 43, "offset": 76924}, "extra": {"message": "RegExp() called with a `table` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/JqueryTableSorter.js", "start": {"line": 2416, "col": 24, "offset": 90966}, "end": {"line": 2416, "col": 64, "offset": 91006}, "extra": {"message": "RegExp() called with a `key` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js", "start": {"line": 540, "col": 17, "offset": 24885}, "end": {"line": 540, "col": 117, "offset": 24985}, "extra": {"message": "RegExp() called with a `c` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js", "start": {"line": 540, "col": 17, "offset": 24885}, "end": {"line": 540, "col": 117, "offset": 24985}, "extra": {"message": "RegExp() called with a `data` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js", "start": {"line": 574, "col": 17, "offset": 26458}, "end": {"line": 574, "col": 117, "offset": 26558}, "extra": {"message": "RegExp() called with a `c` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js", "start": {"line": 574, "col": 17, "offset": 26458}, "end": {"line": 574, "col": 117, "offset": 26558}, "extra": {"message": "RegExp() called with a `data` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js", "start": {"line": 602, "col": 56, "offset": 27582}, "end": {"line": 602, "col": 88, "offset": 27614}, "extra": {"message": "RegExp() called with a `data` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js", "start": {"line": 726, "col": 15, "offset": 33123}, "end": {"line": 729, "col": 9, "offset": 33286}, "extra": {"message": "RegExp() called with a `c` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js", "start": {"line": 726, "col": 15, "offset": 33123}, "end": {"line": 729, "col": 9, "offset": 33286}, "extra": {"message": "RegExp() called with a `data` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js", "start": {"line": 784, "col": 14, "offset": 34997}, "end": {"line": 784, "col": 41, "offset": 35024}, "extra": {"message": "RegExp() called with a `table` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js", "start": {"line": 785, "col": 17, "offset": 35042}, "end": {"line": 785, "col": 52, "offset": 35077}, "extra": {"message": "RegExp() called with a `table` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js", "start": {"line": 786, "col": 24, "offset": 35102}, "end": {"line": 786, "col": 131, "offset": 35209}, "extra": {"message": "RegExp() called with a `$` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js", "start": {"line": 787, "col": 15, "offset": 35225}, "end": {"line": 787, "col": 86, "offset": 35296}, "extra": {"message": "RegExp() called with a `$` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js", "start": {"line": 788, "col": 16, "offset": 35313}, "end": {"line": 788, "col": 94, "offset": 35391}, "extra": {"message": "RegExp() called with a `$` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js", "start": {"line": 789, "col": 16, "offset": 35408}, "end": {"line": 789, "col": 93, "offset": 35485}, "extra": {"message": "RegExp() called with a `$` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js", "start": {"line": 790, "col": 17, "offset": 35503}, "end": {"line": 790, "col": 101, "offset": 35587}, "extra": {"message": "RegExp() called with a `$` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js", "start": {"line": 791, "col": 15, "offset": 35603}, "end": {"line": 791, "col": 92, "offset": 35680}, "extra": {"message": "RegExp() called with a `$` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js", "start": {"line": 792, "col": 16, "offset": 35697}, "end": {"line": 792, "col": 100, "offset": 35781}, "extra": {"message": "RegExp() called with a `$` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js", "start": {"line": 1179, "col": 21, "offset": 51192}, "end": {"line": 1179, "col": 42, "offset": 51213}, "extra": {"message": "RegExp() called with a `attr` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/selectize/js/selectize.js", "start": {"line": 33, "col": 47, "offset": 1302}, "end": {"line": 33, "col": 71, "offset": 1326}, "extra": {"message": "RegExp() called with a `pattern` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/selectize/js/standalone/selectize.js", "start": {"line": 68, "col": 14, "offset": 1982}, "end": {"line": 68, "col": 36, "offset": 2004}, "extra": {"message": "RegExp() called with a `query` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/selectize/js/standalone/selectize.js", "start": {"line": 428, "col": 28, "offset": 10922}, "end": {"line": 428, "col": 54, "offset": 10948}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/selectize/js/standalone/selectize.js", "start": {"line": 670, "col": 47, "offset": 18243}, "end": {"line": 670, "col": 71, "offset": 18267}, "extra": {"message": "RegExp() called with a `pattern` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/scripts/vex.combined.js", "start": {"line": 330, "col": 5, "offset": 8860}, "end": {"line": 330, "col": 25, "offset": 8880}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/scripts/vex.combined.js", "start": {"line": 340, "col": 3, "offset": 9090}, "end": {"line": 340, "col": 41, "offset": 9128}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/scripts/vex.combined.js", "start": {"line": 756, "col": 5, "offset": 22568}, "end": {"line": 756, "col": 25, "offset": 22588}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/scripts/vex.combined.js", "start": {"line": 766, "col": 3, "offset": 22798}, "end": {"line": 766, "col": 41, "offset": 22836}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/scripts/vex.js", "start": {"line": 330, "col": 5, "offset": 8860}, "end": {"line": 330, "col": 25, "offset": 8880}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/scripts/vex.js", "start": {"line": 340, "col": 3, "offset": 9090}, "end": {"line": 340, "col": 41, "offset": 9128}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/InterchangeBuilder.php", "start": {"line": 182, "col": 9, "offset": 6113}, "end": {"line": 182, "col": 56, "offset": 6160}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/processwire_processwire/wire/modules/Page/PageFrontEdit/PageFrontEdit.js", "start": {"line": 271, "col": 5, "offset": 6783}, "end": {"line": 271, "col": 44, "offset": 6822}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.incomplete-sanitization.incomplete-sanitization", "path": "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessField/ProcessField.js", "start": {"line": 27, "col": 9, "offset": 925}, "end": {"line": 27, "col": 28, "offset": 944}, "extra": {"message": "`tpl.replace` method will only replace the first occurrence when used with a string argument ('%'). If this method is used for escaping of dangerous data then there is a possibility for a bypass. Try to use sanitization library instead or use a Regex with a global flag.", "metadata": {"cwe": ["CWE-116: Improper Encoding or Escaping of Output"], "category": "security", "technology": ["javascript"], "owasp": ["A03:2021 - Injection"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Encoding"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.incomplete-sanitization.incomplete-sanitization", "shortlink": "https://sg.run/1GbQ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessModule/ProcessModuleInstall.php", "start": {"line": 385, "col": 4, "offset": 12006}, "end": {"line": 385, "col": 34, "offset": 12036}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "path": "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageLister/ProcessPageLister.js", "start": {"line": 320, "col": 35, "offset": 10536}, "end": {"line": 320, "col": 37, "offset": 10538}, "extra": {"message": "Cannot determine what 'js' is and it is used with a '<script>' tag. This could be susceptible to cross-site scripting (XSS). Ensure 'js' is not externally controlled, or sanitize this data.", "metadata": {"owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "references": ["https://www.developsec.com/2017/11/09/xss-in-a-script-tag/", "https://github.com/juice-shop/juice-shop/blob/1ceb8751e986dacd3214a618c37e7411be6bc11a/routes/videoHandler.ts#L68"], "category": "security", "technology": ["javascript"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "shortlink": "https://sg.run/1Zy1"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageLister/ProcessPageLister.js", "start": {"line": 326, "col": 4, "offset": 10649}, "end": {"line": 326, "col": 23, "offset": 10668}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/processwire_processwire/wire/templates-admin/scripts/main.js", "start": {"line": 514, "col": 6, "offset": 14973}, "end": {"line": 514, "col": 28, "offset": 14995}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout.js:\n ", "path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout_and_plugins.js:\n ", "path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout_and_plugins.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "message": "Timeout when running typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method on downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout_and_plugins.js:\n ", "path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout_and_plugins.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/ckeditor.js:\n ", "path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/ckeditor.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/ckeditor.js:\n ", "path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/ckeditor.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/ckeditor.js:\n ", "path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/ckeditor.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-response.tainted-html-response", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-response.tainted-html-response on downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/JqueryCore.js:\n ", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/JqueryCore.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/JqueryCore.js:\n ", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/JqueryCore.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/JqueryCore.js:\n ", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/JqueryCore.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-response.tainted-html-response", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-response.tainted-html-response on downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/dev/JqueryCore.js:\n ", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/dev/JqueryCore.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/dev/JqueryCore.js:\n ", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/dev/JqueryCore.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/dev/JqueryCore.js:\n ", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/dev/JqueryCore.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.cors-misconfiguration.cors-misconfiguration", "message": "Timeout when running javascript.express.security.cors-misconfiguration.cors-misconfiguration on downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/legacy/JqueryCore.js:\n ", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/legacy/JqueryCore.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "message": "Timeout when running javascript.express.security.injection.raw-html-format.raw-html-format on downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/legacy/JqueryCore.js:\n ", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/legacy/JqueryCore.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.x-frame-options-misconfiguration.x-frame-options-misconfiguration", "message": "Timeout when running javascript.express.security.x-frame-options-misconfiguration.x-frame-options-misconfiguration on downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/legacy/JqueryCore.js:\n ", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/legacy/JqueryCore.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/JqueryUI.js:\n ", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/JqueryUI.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/JqueryUI.js:\n ", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/JqueryUI.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "message": "Timeout when running javascript.express.security.injection.raw-html-format.raw-html-format on downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/JqueryUI.js:\n ", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/JqueryUI.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/dev/JqueryUI.js:\n ", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/dev/JqueryUI.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/dev/JqueryUI.js:\n ", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/dev/JqueryUI.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "message": "Timeout when running javascript.express.security.injection.raw-html-format.raw-html-format on downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/dev/JqueryUI.js:\n ", "path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/dev/JqueryUI.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "php.lang.security.injection.printed-request.printed-request", "message": "Timeout when running php.lang.security.injection.printed-request.printed-request on downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/HTMLPurifier.standalone.php:\n ", "path": "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/HTMLPurifier.standalone.php"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "php.lang.security.injection.tainted-callable.tainted-callable", "message": "Timeout when running php.lang.security.injection.tainted-callable.tainted-callable on downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/HTMLPurifier.standalone.php:\n ", "path": "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/HTMLPurifier.standalone.php"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "php.lang.security.injection.tainted-exec.tainted-exec", "message": "Timeout when running php.lang.security.injection.tainted-exec.tainted-exec on downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/HTMLPurifier.standalone.php:\n ", "path": "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/HTMLPurifier.standalone.php"}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/processwire_processwire/wire/modules/Image/ImageSizerEngineAnimatedGif/gif_encoder.php", "start": {"line": 50, "col": 9, "offset": 0}, "end": {"line": 50, "col": 18, "offset": 9}}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Image/ImageSizerEngineAnimatedGif/gif_encoder.php", "start": {"line": 51, "col": 9, "offset": 0}, "end": {"line": 51, "col": 18, "offset": 9}}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Image/ImageSizerEngineAnimatedGif/gif_encoder.php", "start": {"line": 52, "col": 9, "offset": 0}, "end": {"line": 52, "col": 18, "offset": 9}}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Image/ImageSizerEngineAnimatedGif/gif_encoder.php", "start": {"line": 53, "col": 9, "offset": 0}, "end": {"line": 53, "col": 18, "offset": 9}}]], "message": "Syntax error at line downloaded_repos/processwire_processwire/wire/modules/Image/ImageSizerEngineAnimatedGif/gif_encoder.php:50:\n `'ERR00'=>` was unexpected", "path": "downloaded_repos/processwire_processwire/wire/modules/Image/ImageSizerEngineAnimatedGif/gif_encoder.php", "spans": [{"file": "downloaded_repos/processwire_processwire/wire/modules/Image/ImageSizerEngineAnimatedGif/gif_encoder.php", "start": {"line": 50, "col": 9, "offset": 0}, "end": {"line": 50, "col": 18, "offset": 9}}, {"file": "downloaded_repos/processwire_processwire/wire/modules/Image/ImageSizerEngineAnimatedGif/gif_encoder.php", "start": {"line": 51, "col": 9, "offset": 0}, "end": {"line": 51, "col": 18, "offset": 9}}, {"file": "downloaded_repos/processwire_processwire/wire/modules/Image/ImageSizerEngineAnimatedGif/gif_encoder.php", "start": {"line": 52, "col": 9, "offset": 0}, "end": {"line": 52, "col": 18, "offset": 9}}, {"file": "downloaded_repos/processwire_processwire/wire/modules/Image/ImageSizerEngineAnimatedGif/gif_encoder.php", "start": {"line": 53, "col": 9, "offset": 0}, "end": {"line": 53, "col": 18, "offset": 9}}]}], "paths": {"scanned": ["downloaded_repos/processwire_processwire/.gitattributes", "downloaded_repos/processwire_processwire/.gitignore", "downloaded_repos/processwire_processwire/CONTRIBUTING.md", "downloaded_repos/processwire_processwire/LICENSE.TXT", "downloaded_repos/processwire_processwire/README.md", "downloaded_repos/processwire_processwire/composer.json", "downloaded_repos/processwire_processwire/htaccess.txt", "downloaded_repos/processwire_processwire/index.php", "downloaded_repos/processwire_processwire/install.php", "downloaded_repos/processwire_processwire/site-blank/assets/index.php", "downloaded_repos/processwire_processwire/site-blank/classes/HomePage.php", "downloaded_repos/processwire_processwire/site-blank/config.php", "downloaded_repos/processwire_processwire/site-blank/finished.php", "downloaded_repos/processwire_processwire/site-blank/htaccess.txt", "downloaded_repos/processwire_processwire/site-blank/init.php", "downloaded_repos/processwire_processwire/site-blank/install/files/README.txt", "downloaded_repos/processwire_processwire/site-blank/install/info.php", "downloaded_repos/processwire_processwire/site-blank/install/install.sql", "downloaded_repos/processwire_processwire/site-blank/modules/README.txt", "downloaded_repos/processwire_processwire/site-blank/ready.php", "downloaded_repos/processwire_processwire/site-blank/templates/_init.php", "downloaded_repos/processwire_processwire/site-blank/templates/_main.php", "downloaded_repos/processwire_processwire/site-blank/templates/admin.php", "downloaded_repos/processwire_processwire/site-blank/templates/basic-page.php", "downloaded_repos/processwire_processwire/site-blank/templates/errors/500.html", "downloaded_repos/processwire_processwire/site-blank/templates/errors/README.txt", "downloaded_repos/processwire_processwire/site-blank/templates/home.php", "downloaded_repos/processwire_processwire/site-blank/templates/scripts/main.js", "downloaded_repos/processwire_processwire/site-blank/templates/styles/main.css", "downloaded_repos/processwire_processwire/wire/.editorconfig", "downloaded_repos/processwire_processwire/wire/README.txt", "downloaded_repos/processwire_processwire/wire/config.php", "downloaded_repos/processwire_processwire/wire/core/.phpstorm.meta.php", "downloaded_repos/processwire_processwire/wire/core/AdminTheme.php", "downloaded_repos/processwire_processwire/wire/core/AdminThemeFramework.php", "downloaded_repos/processwire_processwire/wire/core/Breadcrumb.php", "downloaded_repos/processwire_processwire/wire/core/Breadcrumbs.php", "downloaded_repos/processwire_processwire/wire/core/CacheFile.php", "downloaded_repos/processwire_processwire/wire/core/Config.php", "downloaded_repos/processwire_processwire/wire/core/ConfigurableModule.php", "downloaded_repos/processwire_processwire/wire/core/Database.php", "downloaded_repos/processwire_processwire/wire/core/DatabaseMysqli.php", "downloaded_repos/processwire_processwire/wire/core/DatabaseQuery.php", "downloaded_repos/processwire_processwire/wire/core/DatabaseQuerySelect.php", "downloaded_repos/processwire_processwire/wire/core/DatabaseQuerySelectFulltext.php", "downloaded_repos/processwire_processwire/wire/core/DatabaseStopwords.php", "downloaded_repos/processwire_processwire/wire/core/Debug.php", "downloaded_repos/processwire_processwire/wire/core/Exceptions.php", "downloaded_repos/processwire_processwire/wire/core/Field.php", "downloaded_repos/processwire_processwire/wire/core/FieldSelectorInfo.php", "downloaded_repos/processwire_processwire/wire/core/Fieldgroup.php", "downloaded_repos/processwire_processwire/wire/core/Fieldgroups.php", "downloaded_repos/processwire_processwire/wire/core/FieldgroupsArray.php", "downloaded_repos/processwire_processwire/wire/core/Fields.php", "downloaded_repos/processwire_processwire/wire/core/FieldsArray.php", "downloaded_repos/processwire_processwire/wire/core/FieldsTableTools.php", "downloaded_repos/processwire_processwire/wire/core/Fieldtype.php", "downloaded_repos/processwire_processwire/wire/core/FieldtypeMulti.php", "downloaded_repos/processwire_processwire/wire/core/Fieldtypes.php", "downloaded_repos/processwire_processwire/wire/core/FileCompiler.php", "downloaded_repos/processwire_processwire/wire/core/FileCompilerModule.php", "downloaded_repos/processwire_processwire/wire/core/FileLog.php", "downloaded_repos/processwire_processwire/wire/core/FileValidatorModule.php", "downloaded_repos/processwire_processwire/wire/core/FilenameArray.php", "downloaded_repos/processwire_processwire/wire/core/Fuel.php", "downloaded_repos/processwire_processwire/wire/core/Functions.php", "downloaded_repos/processwire_processwire/wire/core/FunctionsAPI.php", "downloaded_repos/processwire_processwire/wire/core/FunctionsWireAPI.php", "downloaded_repos/processwire_processwire/wire/core/HookEvent.php", "downloaded_repos/processwire_processwire/wire/core/ImageInspector.php", "downloaded_repos/processwire_processwire/wire/core/ImageSizer.php", "downloaded_repos/processwire_processwire/wire/core/ImageSizerEngine.php", "downloaded_repos/processwire_processwire/wire/core/ImageSizerEngineGD.php", "downloaded_repos/processwire_processwire/wire/core/Inputfield.php", "downloaded_repos/processwire_processwire/wire/core/InputfieldWrapper.php", "downloaded_repos/processwire_processwire/wire/core/InputfieldsArray.php", "downloaded_repos/processwire_processwire/wire/core/Interfaces.php", "downloaded_repos/processwire_processwire/wire/core/LanguageFunctions.php", "downloaded_repos/processwire_processwire/wire/core/MarkupFieldtype.php", "downloaded_repos/processwire_processwire/wire/core/MarkupQA.php", "downloaded_repos/processwire_processwire/wire/core/Module.php", "downloaded_repos/processwire_processwire/wire/core/ModuleConfig.php", "downloaded_repos/processwire_processwire/wire/core/ModuleJS.php", "downloaded_repos/processwire_processwire/wire/core/ModulePlaceholder.php", "downloaded_repos/processwire_processwire/wire/core/Modules.php", "downloaded_repos/processwire_processwire/wire/core/ModulesClass.php", "downloaded_repos/processwire_processwire/wire/core/ModulesConfigs.php", "downloaded_repos/processwire_processwire/wire/core/ModulesDuplicates.php", "downloaded_repos/processwire_processwire/wire/core/ModulesFiles.php", "downloaded_repos/processwire_processwire/wire/core/ModulesFlags.php", "downloaded_repos/processwire_processwire/wire/core/ModulesInfo.php", "downloaded_repos/processwire_processwire/wire/core/ModulesInstaller.php", "downloaded_repos/processwire_processwire/wire/core/ModulesLoader.php", "downloaded_repos/processwire_processwire/wire/core/Notices.php", "downloaded_repos/processwire_processwire/wire/core/NullField.php", "downloaded_repos/processwire_processwire/wire/core/NullPage.php", "downloaded_repos/processwire_processwire/wire/core/PWGIF.php", "downloaded_repos/processwire_processwire/wire/core/PWPNG.php", "downloaded_repos/processwire_processwire/wire/core/Page.php", "downloaded_repos/processwire_processwire/wire/core/PageAccess.php", "downloaded_repos/processwire_processwire/wire/core/PageAction.php", "downloaded_repos/processwire_processwire/wire/core/PageArray.php", "downloaded_repos/processwire_processwire/wire/core/PageArrayIterator.php", "downloaded_repos/processwire_processwire/wire/core/PageComparison.php", "downloaded_repos/processwire_processwire/wire/core/PageFinder.php", "downloaded_repos/processwire_processwire/wire/core/PageProperties.php", "downloaded_repos/processwire_processwire/wire/core/PageTraversal.php", "downloaded_repos/processwire_processwire/wire/core/PageValues.php", "downloaded_repos/processwire_processwire/wire/core/Pagefile.php", "downloaded_repos/processwire_processwire/wire/core/PagefileExtra.php", "downloaded_repos/processwire_processwire/wire/core/Pagefiles.php", "downloaded_repos/processwire_processwire/wire/core/PagefilesManager.php", "downloaded_repos/processwire_processwire/wire/core/Pageimage.php", "downloaded_repos/processwire_processwire/wire/core/PageimageDebugInfo.php", "downloaded_repos/processwire_processwire/wire/core/PageimageVariations.php", "downloaded_repos/processwire_processwire/wire/core/Pageimages.php", "downloaded_repos/processwire_processwire/wire/core/Pages.php", "downloaded_repos/processwire_processwire/wire/core/PagesAccess.php", "downloaded_repos/processwire_processwire/wire/core/PagesEditor.php", "downloaded_repos/processwire_processwire/wire/core/PagesExportImport.php", "downloaded_repos/processwire_processwire/wire/core/PagesLoader.php", "downloaded_repos/processwire_processwire/wire/core/PagesLoaderCache.php", "downloaded_repos/processwire_processwire/wire/core/PagesNames.php", "downloaded_repos/processwire_processwire/wire/core/PagesParents.php", "downloaded_repos/processwire_processwire/wire/core/PagesPathFinder.php", "downloaded_repos/processwire_processwire/wire/core/PagesRaw.php", "downloaded_repos/processwire_processwire/wire/core/PagesRequest.php", "downloaded_repos/processwire_processwire/wire/core/PagesSortfields.php", "downloaded_repos/processwire_processwire/wire/core/PagesTrash.php", "downloaded_repos/processwire_processwire/wire/core/PagesType.php", "downloaded_repos/processwire_processwire/wire/core/PaginatedArray.php", "downloaded_repos/processwire_processwire/wire/core/Password.php", "downloaded_repos/processwire_processwire/wire/core/Paths.php", "downloaded_repos/processwire_processwire/wire/core/Permission.php", "downloaded_repos/processwire_processwire/wire/core/Permissions.php", "downloaded_repos/processwire_processwire/wire/core/Process.php", "downloaded_repos/processwire_processwire/wire/core/ProcessController.php", "downloaded_repos/processwire_processwire/wire/core/ProcessWire.php", "downloaded_repos/processwire_processwire/wire/core/Punycode.php", "downloaded_repos/processwire_processwire/wire/core/Role.php", "downloaded_repos/processwire_processwire/wire/core/Roles.php", "downloaded_repos/processwire_processwire/wire/core/Sanitizer.php", "downloaded_repos/processwire_processwire/wire/core/Selector.php", "downloaded_repos/processwire_processwire/wire/core/Selectors.php", "downloaded_repos/processwire_processwire/wire/core/Session.php", "downloaded_repos/processwire_processwire/wire/core/SessionCSRF.php", "downloaded_repos/processwire_processwire/wire/core/Template.php", "downloaded_repos/processwire_processwire/wire/core/TemplateFile.php", "downloaded_repos/processwire_processwire/wire/core/Templates.php", "downloaded_repos/processwire_processwire/wire/core/TemplatesArray.php", "downloaded_repos/processwire_processwire/wire/core/Textformatter.php", "downloaded_repos/processwire_processwire/wire/core/Tfa.php", "downloaded_repos/processwire_processwire/wire/core/User.php", "downloaded_repos/processwire_processwire/wire/core/Users.php", "downloaded_repos/processwire_processwire/wire/core/Wire.php", "downloaded_repos/processwire_processwire/wire/core/WireAction.php", "downloaded_repos/processwire_processwire/wire/core/WireArray.php", "downloaded_repos/processwire_processwire/wire/core/WireCache.php", "downloaded_repos/processwire_processwire/wire/core/WireCacheDatabase.php", "downloaded_repos/processwire_processwire/wire/core/WireClassLoader.php", "downloaded_repos/processwire_processwire/wire/core/WireData.php", "downloaded_repos/processwire_processwire/wire/core/WireDataDB.php", "downloaded_repos/processwire_processwire/wire/core/WireDatabaseBackup.php", "downloaded_repos/processwire_processwire/wire/core/WireDatabasePDO.php", "downloaded_repos/processwire_processwire/wire/core/WireDatabasePDOStatement.php", "downloaded_repos/processwire_processwire/wire/core/WireDateTime.php", "downloaded_repos/processwire_processwire/wire/core/WireDebugInfo.php", "downloaded_repos/processwire_processwire/wire/core/WireFileTools.php", "downloaded_repos/processwire_processwire/wire/core/WireHooks.php", "downloaded_repos/processwire_processwire/wire/core/WireHttp.php", "downloaded_repos/processwire_processwire/wire/core/WireInput.php", "downloaded_repos/processwire_processwire/wire/core/WireInputData.php", "downloaded_repos/processwire_processwire/wire/core/WireInputDataCookie.php", "downloaded_repos/processwire_processwire/wire/core/WireLog.php", "downloaded_repos/processwire_processwire/wire/core/WireMail.php", "downloaded_repos/processwire_processwire/wire/core/WireMailInterface.php", "downloaded_repos/processwire_processwire/wire/core/WireMailTools.php", "downloaded_repos/processwire_processwire/wire/core/WireMarkupRegions.php", "downloaded_repos/processwire_processwire/wire/core/WireNumberTools.php", "downloaded_repos/processwire_processwire/wire/core/WireRandom.php", "downloaded_repos/processwire_processwire/wire/core/WireSaveableItems.php", "downloaded_repos/processwire_processwire/wire/core/WireSaveableItemsLookup.php", "downloaded_repos/processwire_processwire/wire/core/WireSessionHandler.php", "downloaded_repos/processwire_processwire/wire/core/WireShutdown.php", "downloaded_repos/processwire_processwire/wire/core/WireTempDir.php", "downloaded_repos/processwire_processwire/wire/core/WireTextTools.php", "downloaded_repos/processwire_processwire/wire/core/WireUpload.php", "downloaded_repos/processwire_processwire/wire/core/admin.php", "downloaded_repos/processwire_processwire/wire/core/boot.php", "downloaded_repos/processwire_processwire/wire/core/install.sql", "downloaded_repos/processwire_processwire/wire/index.config.php", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/AdminThemeDefault.module", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/AdminThemeDefaultHelpers.php", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/controller.php", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/default.php", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/init.php", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/install-foot.inc", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/install-head.inc", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/scripts/install.js", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/scripts/main.js", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/images/animated-overlay.gif", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/images/logo.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/images/slider_handles.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/images/slider_handles2x.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/images/ui-bg_flat_0_aaaaaa_40x100.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/images/ui-bg_flat_75_ffffff_40x100.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/images/ui-bg_glass_55_fbf9ee_1x400.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/images/ui-bg_glass_65_ffffff_1x400.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/images/ui-bg_glass_75_dadada_1x400.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/images/ui-bg_glass_75_e6e6e6_1x400.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/images/ui-bg_glass_95_fef1ec_1x400.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/images/ui-bg_highlight-soft_75_cccccc_1x100.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/images/ui-icons_222222_256x240.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/images/ui-icons_2e83ff_256x240.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/images/ui-icons_454545_256x240.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/images/ui-icons_888888_256x240.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/images/ui-icons_cd0a0a_256x240.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/images/ui-icons_ffffff_256x240.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/install.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/jquery-ui.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/jquery-ui.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/main-classic.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/main-futura.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/main-modern.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/main-warm.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/main.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/sass/_form.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/sass/_inputfields.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/sass/_jquery-ui.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/sass/_masthead.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/sass/_mixins.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/sass/_notifications.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/sass/_pagelist.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/sass/_pagination.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/sass/_reset.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/sass/_table.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/sass/_ui.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/sass/_vars.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/sass/_wiretabs.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/sass/compile.sh", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/sass/main-classic.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/sass/main-futura.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/sass/main-modern.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/sass/main-warm.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/styles/sass/main.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/AdminThemeReno.module", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/AdminThemeRenoHelpers.php", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/README.md", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/controller.php", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/debug.inc", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/default.php", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/init.php", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/scripts/main.js", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/_colors-blue.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/_colors-classic.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/_colors-reno.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/_common.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/_inputfields.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/_jquery-ui.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/_lang.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/_logs.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/_masthead.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/_notifications.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/_pagination-2.6.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/_pagination.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/_reset.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/_search.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/_sidebar.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/_ui.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/blue.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/blue.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/classic.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/classic.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/compile.sh", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/images/logo-dark.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/images/logo-sm.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/images/logo.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/images/slider_handles.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/images/slider_handles2x.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/images/ui-bg_flat_0_aaaaaa_40x100.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/images/ui-bg_flat_75_ffffff_40x100.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/images/ui-bg_glass_55_fbf9ee_1x400.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/images/ui-bg_glass_65_ffffff_1x400.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/images/ui-bg_glass_75_dadada_1x400.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/images/ui-bg_glass_75_e6e6e6_1x400.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/images/ui-bg_glass_95_fef1ec_1x400.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/images/ui-bg_highlight-soft_75_cccccc_1x100.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/images/ui-icons_222222_256x240.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/images/ui-icons_2e83ff_256x240.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/images/ui-icons_454545_256x240.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/images/ui-icons_888888_256x240.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/images/ui-icons_cd0a0a_256x240.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/images/ui-icons_ffffff_256x240.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/main.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/styles/main.scss", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/AdminThemeUikit.module", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/AdminThemeUikitCss.php", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/README.md", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/_body-scripts.php", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/_content-body.php", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/_content-head.php", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/_content.php", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/_footer.php", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/_head.php", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/_main.php", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/_masthead.php", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/_offcanvas.php", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/_search-form.php", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/_sidenav/_sidenav-init.php", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/_sidenav/_sidenav-masthead.php", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/_sidenav/_sidenav-side.php", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/_sidenav/_sidenav-tree.php", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/_sidenav/default.php", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/config-field.js", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/config.php", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/controller.php", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/default.php", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/init.php", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/install-foot.inc", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/install-head.inc", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/interfaces.php", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/LICENSE.txt", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/README.md", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/layout.jquery.json", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/changelog.txt", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/callbacks/jquery.layout.callbacks-latest.js", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/callbacks/jquery.layout.callbacks.min-latest.js", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/callbacks/jquery.layout.pseudoClose.js", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/callbacks/jquery.layout.resizeDataTable.js", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/callbacks/jquery.layout.resizePaneAccordions.js", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/callbacks/jquery.layout.resizeTabLayout.js", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout.js", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout_and_plugins.js", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/layout-default.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/plugins/jquery.layout.browserZoom.js", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/plugins/jquery.layout.buttons.js", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/plugins/jquery.layout.slideOffscreen.js", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/plugins/jquery.layout.state.js", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/scripts/main.js", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/LICENSE.md", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/backgrounds/accordion-close.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/backgrounds/accordion-open.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/backgrounds/divider-icon.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/backgrounds/form-checkbox-indeterminate.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/backgrounds/form-checkbox.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/backgrounds/form-datalist.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/backgrounds/form-radio.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/backgrounds/form-select.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/backgrounds/list-bullet.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/components/close-icon.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/components/close-large.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/components/drop-parent-icon.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/components/marker.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/components/nav-parent-icon-large.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/components/nav-parent-icon.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/components/navbar-parent-icon.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/components/navbar-toggle-icon.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/components/overlay-icon.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/components/pagination-next.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/components/pagination-previous.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/components/search-icon.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/components/search-large.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/components/search-navbar.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/components/slidenav-next-large.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/components/slidenav-next.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/components/slidenav-previous-large.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/components/slidenav-previous.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/components/spinner.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/components/totop.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/500px.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/album.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/android-robot.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/android.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/apple.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/arrow-down.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/arrow-left.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/arrow-right.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/arrow-up.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/bag.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/ban.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/behance.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/bell.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/bold.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/bolt.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/bookmark.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/calendar.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/camera.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/cart.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/check.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/chevron-double-left.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/chevron-double-right.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/chevron-down.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/chevron-left.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/chevron-right.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/chevron-up.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/clock.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/close.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/cloud-download.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/cloud-upload.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/code.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/cog.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/comment.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/commenting.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/comments.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/copy.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/credit-card.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/crosshairs.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/database.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/desktop.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/discord.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/download.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/dribbble.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/etsy.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/expand.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/eye-slash.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/eye.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/facebook.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/file-edit.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/file-pdf.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/file-text.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/file.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/flickr.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/folder.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/forward.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/foursquare.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/future.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/git-branch.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/git-fork.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/github-alt.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/github.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/gitter.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/google.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/grid.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/happy.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/hashtag.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/heart.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/history.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/home.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/image.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/info.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/instagram.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/italic.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/joomla.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/laptop.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/lifesaver.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/link.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/linkedin.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/list.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/location.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/lock.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/mail.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/mastodon.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/menu.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/microphone.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/microsoft.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/minus-circle.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/minus.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/more-vertical.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/more.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/move.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/nut.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/pagekit.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/paint-bucket.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/pencil.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/phone-landscape.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/phone.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/pinterest.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/play-circle.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/play.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/plus-circle.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/plus.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/print.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/pull.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/push.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/question.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/quote-right.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/receiver.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/reddit.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/refresh.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/reply.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/rss.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/search.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/server.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/settings.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/shrink.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/sign-in.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/sign-out.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/social.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/soundcloud.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/star.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/strikethrough.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/table.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/tablet-landscape.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/tablet.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/tag.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/thumbnails.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/tiktok.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/trash.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/triangle-down.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/triangle-left.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/triangle-right.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/triangle-up.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/tripadvisor.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/tumblr.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/tv.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/twitch.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/twitter.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/uikit.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/unlock.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/upload.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/user.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/users.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/video-camera.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/vimeo.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/warning.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/whatsapp.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/wordpress.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/world.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/xing.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/yelp.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/yootheme.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/images/icons/youtube.svg", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/_import.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/accordion.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/alert.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/align.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/animation.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/article.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/background.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/badge.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/base.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/breadcrumb.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/button.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/card.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/close.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/column.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/comment.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/container.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/countdown.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/cover.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/description-list.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/divider.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/dotnav.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/drop.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/dropbar.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/dropdown.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/dropnav.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/flex.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/form-range.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/form.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/grid.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/heading.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/height.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/icon.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/iconnav.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/inverse.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/label.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/leader.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/lightbox.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/link.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/list.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/margin.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/marker.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/mixin.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/modal.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/nav.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/navbar.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/notification.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/offcanvas.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/overlay.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/padding.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/pagination.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/placeholder.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/position.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/print.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/progress.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/search.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/section.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/slidenav.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/slider.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/slideshow.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/sortable.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/spinner.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/sticky.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/subnav.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/svg.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/switcher.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/tab.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/table.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/text.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/thumbnav.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/tile.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/tooltip.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/totop.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/transition.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/utility.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/variables.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/visibility.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/components/width.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/_import.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/accordion.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/alert.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/align.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/animation.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/article.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/background.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/badge.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/base.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/breadcrumb.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/button.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/card.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/close.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/column.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/comment.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/container.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/countdown.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/description-list.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/divider.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/dotnav.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/drop.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/dropbar.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/dropdown.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/form-range.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/form.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/grid.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/heading.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/height.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/icon.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/iconnav.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/inverse.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/label.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/leader.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/lightbox.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/link.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/list.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/margin.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/marker.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/modal.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/nav.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/navbar.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/notification.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/offcanvas.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/overlay.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/padding.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/pagination.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/placeholder.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/position.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/progress.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/search.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/section.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/slidenav.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/slider.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/sortable.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/spinner.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/sticky.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/subnav.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/tab.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/table.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/text.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/thumbnav.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/tile.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/tooltip.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/totop.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/transition.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/utility.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/variables.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/theme/width.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/uikit.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/src/less/uikit.theme.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/images/animated-overlay.gif", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/images/logo.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/images/pw-mark.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/images/slider_handles.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/images/slider_handles2x.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/images/ui-bg_flat_0_aaaaaa_40x100.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/images/ui-bg_flat_75_ffffff_40x100.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/images/ui-bg_glass_55_fbf9ee_1x400.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/images/ui-bg_glass_65_ffffff_1x400.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/images/ui-bg_glass_75_dadada_1x400.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/images/ui-bg_glass_75_e6e6e6_1x400.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/images/ui-bg_glass_95_fef1ec_1x400.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/images/ui-bg_highlight-soft_75_cccccc_1x100.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/images/ui-icons_222222_256x240.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/images/ui-icons_2e83ff_256x240.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/images/ui-icons_454545_256x240.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/images/ui-icons_888888_256x240.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/images/ui-icons_cd0a0a_256x240.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/images/ui-icons_ffffff_256x240.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/_import.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/breadcrumb.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/images/README.md", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/images/animated-overlay.gif", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/images/logo.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/images/pw-mark.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/images/slider_handles.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/images/slider_handles2x.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/images/ui-bg_flat_0_aaaaaa_40x100.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/images/ui-bg_flat_75_ffffff_40x100.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/images/ui-bg_glass_55_fbf9ee_1x400.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/images/ui-bg_glass_65_ffffff_1x400.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/images/ui-bg_glass_75_dadada_1x400.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/images/ui-bg_glass_75_e6e6e6_1x400.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/images/ui-bg_glass_95_fef1ec_1x400.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/images/ui-bg_highlight-soft_75_cccccc_1x100.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/images/ui-icons_222222_256x240.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/images/ui-icons_2e83ff_256x240.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/images/ui-icons_454545_256x240.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/images/ui-icons_888888_256x240.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/images/ui-icons_cd0a0a_256x240.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/images/ui-icons_ffffff_256x240.png", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/nav.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/offcanvas.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-dropdown.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-dropdown.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-init.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-init.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-inputfields-asmselect.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-inputfields-asmselect.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-inputfields-button.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-inputfields-button.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-inputfields-colors.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-inputfields-colors.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-inputfields-file.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-inputfields-file.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-inputfields-horizontal.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-inputfields-horizontal.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-inputfields-image.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-inputfields-image.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-inputfields-language.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-inputfields-language.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-inputfields-misc.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-inputfields-misc.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-inputfields-repeater.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-inputfields-repeater.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-inputfields.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-inputfields.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-jquery-ui-custom.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-jquery-ui-custom.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-jquery-ui.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-jquery-ui.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-login.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-login.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-misc.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-misc.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-mobile.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-mobile.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-notices.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-notices.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-page-edit.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-page-list.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-process.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-process.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-sidebar.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-sidebar.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/pw-theme-reno.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/uk-form.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/uk-form.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/uk-pagination.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/uk-pagination.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/uk-tab.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/uk-tab.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/uk-table.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/uk-table.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/uk-text.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw/uk-text.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/pw.min.css", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/styles/reno.less", "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit-pw/styles/rock.less", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeCache.module", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeCheckbox.module", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeComments/Comment.php", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeComments/CommentArray.php", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeComments/CommentField.php", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeComments/CommentFilter.php", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeComments/CommentFilterAkismet.module", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeComments/CommentForm.php", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeComments/CommentFormCustom.php", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeComments/CommentList.php", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeComments/CommentListCustom.php", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeComments/CommentNotifications.php", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeComments/CommentStars.php", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeComments/FieldtypeComments.module", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeComments/InputfieldCommentsAdmin.css", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeComments/InputfieldCommentsAdmin.module", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeComments/comments.css", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeComments/comments.js", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeDatetime.module", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeDecimal.module", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeEmail.module", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeFieldsetClose.module", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeFieldsetOpen.module", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeFieldsetTabOpen.module", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeFile/FieldtypeFile.module", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeFile/config.php", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeFloat.module", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeImage/FieldtypeImage.module", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeInteger.module", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeModule.module", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeOptions/FieldtypeOptions.module", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeOptions/SelectableOption.php", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeOptions/SelectableOptionArray.php", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeOptions/SelectableOptionConfig.php", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeOptions/SelectableOptionManager.php", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypePage.module", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypePageTable.module", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypePageTitle.module", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypePassword.module", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeRepeater/FieldsetPage.php", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeRepeater/FieldsetPageInstructions.php", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeRepeater/FieldtypeFieldsetPage.module", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeRepeater/FieldtypeRepeater.module", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeRepeater/FieldtypeRepeaterPorter.php", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeRepeater/FieldtypeRepeaterVersions.php", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeRepeater/InputfieldRepeater.css", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeRepeater/InputfieldRepeater.js", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeRepeater/InputfieldRepeater.module", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeRepeater/InputfieldRepeater.scss", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeRepeater/RepeaterField.php", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeRepeater/RepeaterPage.php", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeRepeater/RepeaterPageArray.php", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeRepeater/config.php", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeSelector.module", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeText.module", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeTextarea.module", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeTextareaHelper.php", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeToggle.module", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeURL.module", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/PageField.php", "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/PageTableField.php", "downloaded_repos/processwire_processwire/wire/modules/FileCompilerTags.module", "downloaded_repos/processwire_processwire/wire/modules/Image/ImageSizerEngineAnimatedGif/ImageSizerEngineAnimatedGif.module", "downloaded_repos/processwire_processwire/wire/modules/Image/ImageSizerEngineAnimatedGif/gif_decoder.php", "downloaded_repos/processwire_processwire/wire/modules/Image/ImageSizerEngineAnimatedGif/gif_encoder.php", "downloaded_repos/processwire_processwire/wire/modules/Image/ImageSizerEngineIMagick/ImageSizerEngineIMagick.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldAsmSelect/InputfieldAsmSelect.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldAsmSelect/InputfieldAsmSelect.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldAsmSelect/InputfieldAsmSelect.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldAsmSelect/asmselect/README.txt", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldAsmSelect/asmselect/jquery.asmselect.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldAsmSelect/asmselect/jquery.asmselect.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldButton.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/InputfieldCKEditor.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/InputfieldCKEditor.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/InputfieldCKEditor.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/README.md", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/CHANGES.md", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/LICENSE.md", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/README.md", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/SECURITY.md", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/adapters/jquery.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/bender-runner.config.json", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/build-config.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/ckeditor.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/config.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/contents.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/af.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/ar.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/az.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/bg.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/bn.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/bs.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/ca.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/cs.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/cy.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/da.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/de-ch.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/de.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/el.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/en-au.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/en-ca.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/en-gb.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/en.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/eo.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/es-mx.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/es.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/et.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/eu.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/fa.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/fi.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/fo.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/fr-ca.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/fr.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/gl.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/gu.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/he.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/hi.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/hr.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/hu.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/id.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/is.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/it.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/ja.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/ka.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/km.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/ko.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/ku.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/lt.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/lv.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/mk.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/mn.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/ms.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/nb.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/nl.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/no.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/oc.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/pl.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/pt-br.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/pt.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/ro.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/ru.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/si.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/sk.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/sl.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/sq.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/sr-latn.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/sr.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/sv.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/th.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/tr.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/tt.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/ug.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/uk.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/vi.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/zh-cn.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/lang/zh.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/a11yhelp.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/_translationstatus.txt", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/af.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/ar.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/az.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/bg.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/ca.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/cs.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/cy.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/da.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/de-ch.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/de.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/el.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/en-au.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/en-gb.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/en.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/eo.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/es-mx.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/es.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/et.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/eu.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/fa.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/fi.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/fo.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/fr-ca.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/fr.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/gl.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/gu.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/he.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/hi.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/hr.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/hu.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/id.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/it.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/ja.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/km.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/ko.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/ku.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/lt.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/lv.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/mk.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/mn.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/nb.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/nl.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/no.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/oc.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/pl.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/pt-br.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/pt.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/ro.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/ru.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/si.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/sk.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/sl.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/sq.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/sr-latn.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/sr.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/sv.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/th.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/tr.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/tt.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/ug.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/uk.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/vi.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/zh-cn.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/a11yhelp/dialogs/lang/zh.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/about/dialogs/about.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/about/dialogs/hidpi/logo_ckeditor.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/about/dialogs/logo_ckeditor.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/clipboard/dialogs/paste.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/colordialog/dialogs/colordialog.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/colordialog/dialogs/colordialog.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/copyformatting/cursors/cursor-disabled.svg", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/copyformatting/cursors/cursor.svg", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/copyformatting/styles/copyformatting.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/dialog/dialogDefinition.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/dialog/styles/dialog.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/div/dialogs/div.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/CHANGELOG.md", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/LICENSE.md", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/README.md", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/plugindefinition.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/find/dialogs/find.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/forms/dialogs/button.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/forms/dialogs/checkbox.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/forms/dialogs/form.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/forms/dialogs/hiddenfield.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/forms/dialogs/radio.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/forms/dialogs/select.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/forms/dialogs/textarea.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/forms/dialogs/textfield.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/forms/images/hiddenfield.gif", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/icons.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/icons_hidpi.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/iframe/dialogs/iframe.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/iframe/images/placeholder.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/image/dialogs/image.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/image/images/noimage.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/link/dialogs/anchor.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/link/dialogs/link.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/link/images/anchor.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/link/images/hidpi/anchor.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/liststyle/dialogs/liststyle.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/magicline/images/hidpi/icon-rtl.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/magicline/images/hidpi/icon.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/magicline/images/icon-rtl.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/magicline/images/icon.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/pagebreak/images/pagebreak.gif", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/pastefromgdocs/filter/default.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/pastefromlibreoffice/filter/default.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/pastefromword/filter/default.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/pastetools/filter/common.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/pastetools/filter/image.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/preview/images/pagebreak.gif", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/preview/preview.html", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/preview/styles/screen.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/scayt/CHANGELOG.md", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/scayt/LICENSE.md", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/scayt/README.md", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/scayt/dialogs/dialog.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/scayt/dialogs/options.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/scayt/dialogs/toolbar.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/scayt/skins/moono-lisa/scayt.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/showblocks/images/block_address.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/showblocks/images/block_blockquote.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/showblocks/images/block_div.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/showblocks/images/block_h1.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/showblocks/images/block_h2.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/showblocks/images/block_h3.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/showblocks/images/block_h4.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/showblocks/images/block_h5.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/showblocks/images/block_h6.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/showblocks/images/block_p.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/showblocks/images/block_pre.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/dialogs/smiley.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/angel_smile.gif", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/angel_smile.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/angry_smile.gif", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/angry_smile.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/broken_heart.gif", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/broken_heart.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/confused_smile.gif", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/confused_smile.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/cry_smile.gif", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/cry_smile.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/devil_smile.gif", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/devil_smile.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/embaressed_smile.gif", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/embarrassed_smile.gif", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/embarrassed_smile.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/envelope.gif", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/envelope.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/heart.gif", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/heart.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/kiss.gif", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/kiss.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/lightbulb.gif", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/lightbulb.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/omg_smile.gif", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/omg_smile.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/regular_smile.gif", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/regular_smile.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/sad_smile.gif", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/sad_smile.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/shades_smile.gif", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/shades_smile.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/teeth_smile.gif", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/teeth_smile.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/thumbs_down.gif", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/thumbs_down.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/thumbs_up.gif", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/thumbs_up.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/tongue_smile.gif", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/tongue_smile.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/tounge_smile.gif", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/whatchutalkingabout_smile.gif", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/whatchutalkingabout_smile.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/wink_smile.gif", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/smiley/images/wink_smile.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/_translationstatus.txt", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/af.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/ar.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/az.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/bg.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/ca.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/cs.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/cy.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/da.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/de-ch.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/de.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/el.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/en-au.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/en-ca.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/en-gb.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/en.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/eo.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/es-mx.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/es.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/et.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/eu.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/fa.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/fi.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/fr-ca.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/fr.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/gl.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/he.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/hr.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/hu.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/id.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/it.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/ja.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/km.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/ko.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/ku.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/lt.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/lv.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/nb.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/nl.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/no.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/oc.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/pl.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/pt-br.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/pt.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/ro.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/ru.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/si.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/sk.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/sl.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/sq.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/sr-latn.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/sr.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/sv.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/th.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/tr.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/tt.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/ug.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/uk.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/vi.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/zh-cn.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/lang/zh.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/specialchar/dialogs/specialchar.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/table/dialogs/table.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/tableselection/styles/tableselection.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/tabletools/dialogs/tableCell.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/templates/dialogs/templates.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/templates/dialogs/templates.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/templates/templatedefinition.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/templates/templates/default.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/templates/templates/images/template1.gif", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/templates/templates/images/template2.gif", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/templates/templates/images/template3.gif", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/widget/images/handle.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/skins/moono-lisa/dialog.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/skins/moono-lisa/dialog_ie.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/skins/moono-lisa/dialog_ie8.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/skins/moono-lisa/dialog_iequirks.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/skins/moono-lisa/editor.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/skins/moono-lisa/editor_gecko.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/skins/moono-lisa/editor_ie.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/skins/moono-lisa/editor_ie8.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/skins/moono-lisa/editor_iequirks.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/skins/moono-lisa/icons.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/skins/moono-lisa/icons_hidpi.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/skins/moono-lisa/images/arrow.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/skins/moono-lisa/images/close.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/skins/moono-lisa/images/hidpi/close.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/skins/moono-lisa/images/hidpi/lock-open.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/skins/moono-lisa/images/hidpi/lock.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/skins/moono-lisa/images/hidpi/refresh.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/skins/moono-lisa/images/lock-open.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/skins/moono-lisa/images/lock.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/skins/moono-lisa/images/refresh.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/skins/moono-lisa/images/spinner.gif", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/skins/moono-lisa/readme.md", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/styles.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/contents-inline.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/contents-inline.scss", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/contents.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/mystyles.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/pwimage/images/hidpi/pwimage.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/pwimage/images/pwimage.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/pwimage/plugin.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/pwlink/images/hidpi/pwlink.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/pwlink/images/pwlink.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/pwlink/plugin.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/dialogs/sourcedialog.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/icons/hidpi/sourcedialog-rtl.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/icons/hidpi/sourcedialog.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/icons/sourcedialog-rtl.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/icons/sourcedialog.png", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/af.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/ar.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/bg.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/bn.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/bs.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/ca.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/cs.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/cy.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/da.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/de.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/el.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/en-au.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/en-ca.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/en-gb.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/en.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/eo.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/es.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/et.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/eu.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/fa.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/fi.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/fo.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/fr-ca.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/fr.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/gl.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/gu.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/he.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/hi.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/hr.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/hu.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/id.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/is.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/it.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/ja.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/ka.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/km.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/ko.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/ku.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/lt.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/lv.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/mn.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/ms.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/nb.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/nl.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/no.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/pl.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/pt-br.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/pt.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/ro.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/ru.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/si.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/sk.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/sl.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/sq.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/sr-latn.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/sr.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/sv.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/th.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/tr.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/tt.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/ug.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/uk.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/vi.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/zh-cn.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/lang/zh.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/plugin.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/samples/sourcedialog.html", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCheckbox/InputfieldCheckbox.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCheckbox/InputfieldCheckbox.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCheckboxes/InputfieldCheckboxes.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCheckboxes/InputfieldCheckboxes.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCheckboxes/InputfieldCheckboxes.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/InputfieldDatetime.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/InputfieldDatetime.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/InputfieldDatetime.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/InputfieldDatetime.scss", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/InputfieldDatetimeType.php", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-addon-i18n.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-af.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-am.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-bg.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-ca.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-cs.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-da.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-de.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-el.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-es.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-et.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-eu.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-fa.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-fi.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-fr.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-gl.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-he.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-hr.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-hu.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-id.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-it.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-ja.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-ko.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-lt.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-lv.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-mk.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-nl.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-no.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-pl.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-pt-BR.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-pt.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-ro.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-ru.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-sk.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-sl.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-sr-RS.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-sr-YU.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-sv.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-th.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-tr.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-uk.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-vi.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-zh-CN.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/i18n/jquery-ui-timepicker-zh-TW.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/jquery-ui-sliderAccess.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/jquery-ui-timepicker-addon.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/jquery-ui-timepicker-addon.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/jquery-ui-timepicker-addon.min.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/types/InputfieldDatetimeHtml.php", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/types/InputfieldDatetimeSelect.php", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/types/InputfieldDatetimeText.php", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldEmail.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldFieldset.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldFile/InputfieldFile.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldFile/InputfieldFile.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldFile/InputfieldFile.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldFile/InputfieldFile.scss", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldFile/config.php", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldFloat.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldForm.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldHidden.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldIcon/InputfieldIcon.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldIcon/InputfieldIcon.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldIcon/InputfieldIcon.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldIcon/icons.inc", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldImage/InputfieldImage.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldImage/InputfieldImage.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldImage/InputfieldImage.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldImage/InputfieldImage.scss", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldImage/PWImageResizer.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldImage/config.php", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldImage/exif.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldImage/piexif.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldInteger.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldMarkup.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldName.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPage/InputfieldPage.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPage/InputfieldPage.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPage/InputfieldPage.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPageAutocomplete/InputfieldPageAutocomplete.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPageAutocomplete/InputfieldPageAutocomplete.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPageAutocomplete/InputfieldPageAutocomplete.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPageAutocomplete/InputfieldPageAutocomplete.scss", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPageListSelect/InputfieldPageListSelect.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPageListSelect/InputfieldPageListSelect.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPageListSelect/InputfieldPageListSelectCommon.php", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPageListSelect/InputfieldPageListSelectMultiple.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPageListSelect/InputfieldPageListSelectMultiple.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPageListSelect/InputfieldPageListSelectMultiple.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPageName/InputfieldPageName.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPageName/InputfieldPageName.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPageName/InputfieldPageName.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPageTable/InputfieldPageTable.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPageTable/InputfieldPageTable.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPageTable/InputfieldPageTable.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPageTable/InputfieldPageTableAjax.php", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPageTitle/InputfieldPageTitle.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPageTitle/InputfieldPageTitle.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPassword/InputfieldPassword.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPassword/InputfieldPassword.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPassword/InputfieldPassword.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPassword/complexify/README.md", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPassword/complexify/jquery.complexify.banlist.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPassword/complexify/jquery.complexify.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldRadios/InputfieldRadios.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldRadios/InputfieldRadios.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldSelect.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldSelectMultiple.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldSelector/InputfieldSelector.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldSelector/InputfieldSelector.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldSelector/InputfieldSelector.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldSubmit/InputfieldSubmit.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldSubmit/dropdown.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldText/InputfieldText.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldText/InputfieldTextLength.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTextTags/InputfieldTextTags.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTextTags/InputfieldTextTags.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTextTags/InputfieldTextTags.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTextTags/InputfieldTextTags.scss", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTextarea.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/InputfieldTinyMCE.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/InputfieldTinyMCE.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/InputfieldTinyMCE.module.php", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/InputfieldTinyMCEClass.php", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/InputfieldTinyMCEConfigs.php", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/InputfieldTinyMCEFormats.php", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/InputfieldTinyMCESettings.php", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/InputfieldTinyMCETools.php", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/README.md", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/content_css/_common.scss", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/content_css/_vars.scss", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/content_css/document.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/content_css/document.scss", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/content_css/wire-dark.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/content_css/wire-dark.scss", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/content_css/wire.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/content_css/wire.scss", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/defaults.json", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/defaults.php", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/ar.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/az.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/bg.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/bn_BD.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/ca.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/cs.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/cy.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/da.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/de.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/dv.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/el.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/eo.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/es.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/es_MX.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/et.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/eu.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/fa.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/fi.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/fr.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/fr_FR.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/ga.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/gl.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/he_IL.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/hi.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/hr.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/hu_HU.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/hy.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/id.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/is_IS.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/it.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/ja.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/kab.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/kk.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/ko_KR.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/ku.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/lt.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/lv.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/nb_NO.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/ne.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/nl.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/nl_BE.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/oc.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/pl.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/pt.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/pt_BR.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/ro.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/ru.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/sk.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/sl_SI.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/sq.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/sr.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/sv.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/sv_SE.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/ta.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/tg.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/th_TH.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/tr.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/ug.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/uk.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/vi.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/langs/zh-Hans.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/plugins/hello.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/plugins/pwimage.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/plugins/pwlink.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/setting-names.txt", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/langs/README.md", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/license.txt", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/emoticons/js/emojiimages.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/emoticons/js/emojis.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/ar.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/bg_BG.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/ca.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/cs.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/da.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/de.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/el.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/en.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/es.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/eu.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/fa.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/fi.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/fr_FR.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/he_IL.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/hi.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/hr.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/hu_HU.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/id.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/it.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/ja.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/kk.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/ko_KR.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/ms.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/nb_NO.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/nl.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/pl.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/pt_BR.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/pt_PT.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/ro.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/ru.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/sk.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/sl_SI.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/sv_SE.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/th_TH.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/tr.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/uk.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/vi.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/zh_CN.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/zh_TW.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/content/dark/content.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/content/dark/content.min.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/content/default/content.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/content/default/content.min.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/content/document/content.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/content/document/content.min.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/content/tinymce-5/content.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/content/tinymce-5/content.min.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/content/tinymce-5-dark/content.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/content/tinymce-5-dark/content.min.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/content/writer/content.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/content/writer/content.min.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/ui/oxide/content.inline.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/ui/oxide/content.inline.min.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/ui/oxide/content.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/ui/oxide/content.min.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/ui/oxide/skin.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/ui/oxide/skin.min.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/ui/oxide/skin.shadowdom.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/ui/oxide/skin.shadowdom.min.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/ui/oxide-dark/content.inline.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/ui/oxide-dark/content.inline.min.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/ui/oxide-dark/content.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/ui/oxide-dark/content.min.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/ui/oxide-dark/skin.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/ui/oxide-dark/skin.min.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/ui/oxide-dark/skin.shadowdom.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/ui/oxide-dark/skin.shadowdom.min.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/ui/tinymce-5/content.inline.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/ui/tinymce-5/content.inline.min.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/ui/tinymce-5/content.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/ui/tinymce-5/content.min.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/ui/tinymce-5/skin.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/ui/tinymce-5/skin.min.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/ui/tinymce-5/skin.shadowdom.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/ui/tinymce-5/skin.shadowdom.min.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/ui/tinymce-5-dark/content.inline.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/ui/tinymce-5-dark/content.inline.min.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/ui/tinymce-5-dark/content.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/ui/tinymce-5-dark/content.min.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/ui/tinymce-5-dark/skin.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/ui/tinymce-5-dark/skin.min.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/ui/tinymce-5-dark/skin.shadowdom.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/skins/ui/tinymce-5-dark/skin.shadowdom.min.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/tinymce.d.ts", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldToggle/InputfieldToggle.css", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldToggle/InputfieldToggle.js", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldToggle/InputfieldToggle.module", "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldURL.module", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/JqueryCore.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/JqueryCore.module", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/dev/JqueryCore.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/dev/jquery-migrate-debug-3.4.0.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/iframe-resizer-frame.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/iframe-resizer.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/jquery-migrate-debug-1.4.1.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/jquery.cookie.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/jquery.longclick.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/jquery.simulate.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/legacy/JqueryCore.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/xregexp.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryMagnific/JqueryMagnific.css", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryMagnific/JqueryMagnific.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryMagnific/JqueryMagnific.module", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/JqueryTableSorter.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/JqueryTableSorter.module", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/JqueryUI.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/JqueryUI.module", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/dev/JqueryUI.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-af.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-ar-DZ.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-ar.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-az.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-bg.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-bs.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-ca.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-cs.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-cy-GB.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-da.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-de.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-el.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-en-AU.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-en-GB.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-en-NZ.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-eo.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-es.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-et.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-eu.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-fa.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-fi.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-fo.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-fr-CH.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-fr.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-gl.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-he.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-hi.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-hr.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-hu.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-hy.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-id.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-is.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-it.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-ja.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-ka.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-kk.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-km.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-ko.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-lb.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-lt.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-lv.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-mk.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-ml.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-ms.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-nl-BE.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-nl.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-no.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-pl.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-pt-BR.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-pt.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-rm.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-ro.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-ru.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-sk.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-sl.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-sq.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-sr-SR.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-sr.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-sv.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-ta.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-th.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-tj.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-tr.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-uk.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-vi.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-zh-CN.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-zh-HK.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/i18n/jquery.ui.datepicker-zh-TW.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/modal.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/panel.css", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/panel.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/selectize/LICENSE", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/selectize/css/selectize.bootstrap2.css", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/selectize/css/selectize.bootstrap3.css", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/selectize/css/selectize.css", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/selectize/css/selectize.default.css", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/selectize/css/selectize.legacy.css", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/selectize/js/selectize.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/selectize/js/standalone/selectize.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/selectize/less/plugins/drag_drop.less", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/selectize/less/plugins/dropdown_header.less", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/selectize/less/plugins/optgroup_columns.less", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/selectize/less/plugins/remove_button.less", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/selectize/less/selectize.bootstrap2.less", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/selectize/less/selectize.bootstrap3.less", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/selectize/less/selectize.default.less", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/selectize/less/selectize.legacy.less", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/selectize/less/selectize.less", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/touch.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/LICENSE", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/README.md", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/css/vex-theme-bottom-right-corner.css", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/css/vex-theme-default.css", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/css/vex-theme-flat-attack.css", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/css/vex-theme-os.css", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/css/vex-theme-plain.css", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/css/vex-theme-top.css", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/css/vex-theme-wireframe.css", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/css/vex.css", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/scripts/vex.combined.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/scripts/vex.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/styles/_keyframes.sass", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/styles/_mixins.sass", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/styles/vex-theme-bottom-right-corner.sass", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/styles/vex-theme-default.css", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/styles/vex-theme-default.sass", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/styles/vex-theme-flat-attack.sass", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/styles/vex-theme-os.sass", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/styles/vex-theme-plain.css", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/styles/vex-theme-plain.sass", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/styles/vex-theme-pw.css", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/styles/vex-theme-top.sass", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/styles/vex-theme-wireframe.sass", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/styles/vex.sass", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryWireTabs/JqueryWireTabs.css", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryWireTabs/JqueryWireTabs.js", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryWireTabs/JqueryWireTabs.module", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryWireTabs/README.txt", "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryWireTabs/images/bullet.gif", "downloaded_repos/processwire_processwire/wire/modules/LanguageSupport/FieldtypeLanguageInterface.php", "downloaded_repos/processwire_processwire/wire/modules/LanguageSupport/FieldtypePageTitleLanguage.module", "downloaded_repos/processwire_processwire/wire/modules/LanguageSupport/FieldtypeTextLanguage.module", "downloaded_repos/processwire_processwire/wire/modules/LanguageSupport/FieldtypeTextareaLanguage.module", "downloaded_repos/processwire_processwire/wire/modules/LanguageSupport/Language.php", "downloaded_repos/processwire_processwire/wire/modules/LanguageSupport/LanguageParser.php", "downloaded_repos/processwire_processwire/wire/modules/LanguageSupport/LanguageSupport.css", "downloaded_repos/processwire_processwire/wire/modules/LanguageSupport/LanguageSupport.module", "downloaded_repos/processwire_processwire/wire/modules/LanguageSupport/LanguageSupportFields.module", "downloaded_repos/processwire_processwire/wire/modules/LanguageSupport/LanguageSupportInstall.php", "downloaded_repos/processwire_processwire/wire/modules/LanguageSupport/LanguageSupportPageNames.module", "downloaded_repos/processwire_processwire/wire/modules/LanguageSupport/LanguageTabs.css", "downloaded_repos/processwire_processwire/wire/modules/LanguageSupport/LanguageTabs.js", "downloaded_repos/processwire_processwire/wire/modules/LanguageSupport/LanguageTabs.module", "downloaded_repos/processwire_processwire/wire/modules/LanguageSupport/LanguageTabs.scss", "downloaded_repos/processwire_processwire/wire/modules/LanguageSupport/LanguageTranslator.php", "downloaded_repos/processwire_processwire/wire/modules/LanguageSupport/Languages.php", "downloaded_repos/processwire_processwire/wire/modules/LanguageSupport/LanguagesPageFieldValue.php", "downloaded_repos/processwire_processwire/wire/modules/LanguageSupport/LanguagesValueInterface.php", "downloaded_repos/processwire_processwire/wire/modules/LanguageSupport/ProcessLanguage.css", "downloaded_repos/processwire_processwire/wire/modules/LanguageSupport/ProcessLanguage.js", "downloaded_repos/processwire_processwire/wire/modules/LanguageSupport/ProcessLanguage.module", "downloaded_repos/processwire_processwire/wire/modules/LanguageSupport/ProcessLanguageTranslator.css", "downloaded_repos/processwire_processwire/wire/modules/LanguageSupport/ProcessLanguageTranslator.js", "downloaded_repos/processwire_processwire/wire/modules/LanguageSupport/ProcessLanguageTranslator.module", "downloaded_repos/processwire_processwire/wire/modules/LazyCron.module", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupAdminDataTable/MarkupAdminDataTable.css", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupAdminDataTable/MarkupAdminDataTable.js", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupAdminDataTable/MarkupAdminDataTable.module", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupCache.module", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/MarkupHTMLPurifier.module", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/README.md", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/HTMLPurifier.standalone.php", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/Builder/ConfigSchema.php", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/Builder/Xml.php", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/Exception.php", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/Interchange/Directive.php", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/Interchange/Id.php", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/Interchange.php", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/InterchangeBuilder.php", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/Validator.php", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/ValidatorAtom.php", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Attr.AllowedClasses.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Attr.AllowedFrameTargets.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Attr.AllowedRel.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Attr.AllowedRev.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Attr.ClassUseCDATA.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Attr.DefaultImageAlt.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Attr.DefaultInvalidImage.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Attr.DefaultInvalidImageAlt.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Attr.DefaultTextDir.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Attr.EnableID.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Attr.ForbiddenClasses.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Attr.ID.HTML5.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Attr.IDBlacklist.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Attr.IDBlacklistRegexp.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Attr.IDPrefix.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Attr.IDPrefixLocal.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/AutoFormat.AutoParagraph.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/AutoFormat.Custom.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/AutoFormat.DisplayLinkURI.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/AutoFormat.Linkify.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/AutoFormat.PurifierLinkify.DocURL.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/AutoFormat.PurifierLinkify.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/AutoFormat.RemoveEmpty.Predicate.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/AutoFormat.RemoveEmpty.RemoveNbsp.Exceptions.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/AutoFormat.RemoveEmpty.RemoveNbsp.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/AutoFormat.RemoveEmpty.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/AutoFormat.RemoveSpansWithoutAttributes.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/CSS.AllowDuplicates.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/CSS.AllowImportant.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/CSS.AllowTricky.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/CSS.AllowedFonts.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/CSS.AllowedProperties.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/CSS.DefinitionRev.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/CSS.ForbiddenProperties.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/CSS.MaxImgLength.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/CSS.Proprietary.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/CSS.Trusted.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Cache.DefinitionImpl.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Cache.SerializerPath.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Cache.SerializerPermissions.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Core.AggressivelyFixLt.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Core.AggressivelyRemoveScript.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Core.AllowHostnameUnderscore.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Core.AllowParseManyTags.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Core.CollectErrors.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Core.ColorKeywords.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Core.ConvertDocumentToFragment.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Core.DirectLexLineNumberSyncInterval.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Core.DisableExcludes.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Core.EnableIDNA.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Core.Encoding.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Core.EscapeInvalidChildren.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Core.EscapeInvalidTags.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Core.EscapeNonASCIICharacters.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Core.HiddenElements.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Core.Language.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Core.LegacyEntityDecoder.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Core.LexerImpl.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Core.MaintainLineNumbers.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Core.NormalizeNewlines.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Core.RemoveInvalidImg.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Core.RemoveProcessingInstructions.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Core.RemoveScriptContents.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Filter.Custom.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Filter.ExtractStyleBlocks.Escaping.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Filter.ExtractStyleBlocks.Scope.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Filter.ExtractStyleBlocks.TidyImpl.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Filter.ExtractStyleBlocks.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Filter.YouTube.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/HTML.Allowed.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/HTML.AllowedAttributes.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/HTML.AllowedComments.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/HTML.AllowedCommentsRegexp.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/HTML.AllowedElements.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/HTML.AllowedModules.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/HTML.Attr.Name.UseCDATA.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/HTML.BlockWrapper.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/HTML.CoreModules.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/HTML.CustomDoctype.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/HTML.DefinitionID.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/HTML.DefinitionRev.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/HTML.Doctype.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/HTML.FlashAllowFullScreen.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/HTML.ForbiddenAttributes.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/HTML.ForbiddenElements.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/HTML.Forms.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/HTML.MaxImgLength.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/HTML.Nofollow.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/HTML.Parent.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/HTML.Proprietary.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/HTML.SafeEmbed.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/HTML.SafeIframe.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/HTML.SafeObject.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/HTML.SafeScripting.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/HTML.Strict.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/HTML.TargetBlank.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/HTML.TargetNoopener.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/HTML.TargetNoreferrer.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/HTML.TidyAdd.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/HTML.TidyLevel.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/HTML.TidyRemove.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/HTML.Trusted.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/HTML.XHTML.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Output.CommentScriptContents.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Output.FixInnerHTML.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Output.FlashCompat.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Output.Newline.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Output.SortAttr.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Output.TidyFormat.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/Test.ForceNoIconv.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/URI.AllowedSchemes.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/URI.Base.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/URI.DefaultScheme.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/URI.DefinitionID.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/URI.DefinitionRev.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/URI.Disable.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/URI.DisableExternal.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/URI.DisableExternalResources.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/URI.DisableResources.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/URI.Host.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/URI.HostBlacklist.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/URI.MakeAbsolute.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/URI.Munge.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/URI.MungeResources.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/URI.MungeSecretKey.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/URI.OverrideAllowedSchemes.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/URI.SafeIframeRegexp.txt", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema/info.ini", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/schema.ser", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/EntityLookup/entities.ser", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/Filter/ExtractStyleBlocks.php", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/Filter/YouTube.php", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/Language/messages/en.php", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/Lexer/PH5P.php", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/Printer/CSSDefinition.php", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/Printer/ConfigForm.css", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/Printer/ConfigForm.js", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/Printer/ConfigForm.php", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/Printer/HTMLDefinition.php", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/Printer.php", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupPageArray.module", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupPageFields.module", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupPagerNav/MarkupPagerNav.css", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupPagerNav/MarkupPagerNav.module", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupPagerNav/PagerNav.php", "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupRSS.module", "downloaded_repos/processwire_processwire/wire/modules/Page/PageFrontEdit/PageFrontEdit.css", "downloaded_repos/processwire_processwire/wire/modules/Page/PageFrontEdit/PageFrontEdit.js", "downloaded_repos/processwire_processwire/wire/modules/Page/PageFrontEdit/PageFrontEdit.module", "downloaded_repos/processwire_processwire/wire/modules/Page/PageFrontEdit/PageFrontEdit.scss", "downloaded_repos/processwire_processwire/wire/modules/Page/PageFrontEdit/PageFrontEditConfig.php", "downloaded_repos/processwire_processwire/wire/modules/Page/PageFrontEdit/PageFrontEditLoad.js", "downloaded_repos/processwire_processwire/wire/modules/PagePathHistory.module", "downloaded_repos/processwire_processwire/wire/modules/PagePaths.module", "downloaded_repos/processwire_processwire/wire/modules/PagePermissions.module", "downloaded_repos/processwire_processwire/wire/modules/PageRender.module", "downloaded_repos/processwire_processwire/wire/modules/Pages/PagesVersions/PageVersionInfo.php", "downloaded_repos/processwire_processwire/wire/modules/Pages/PagesVersions/PagesVersions.module.php", "downloaded_repos/processwire_processwire/wire/modules/Pages/PagesVersions/PagesVersionsFiles.php", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessCommentsManager/ProcessCommentsManager.css", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessCommentsManager/ProcessCommentsManager.js", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessCommentsManager/ProcessCommentsManager.module", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessCommentsManager/ProcessCommentsManager.scss", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessField/ProcessField.css", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessField/ProcessField.js", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessField/ProcessField.module", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessField/ProcessFieldExportImport.php", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessForgotPassword/ProcessForgotPassword.module", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessHome.module", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessList.module", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessLogger/LogEntriesArray.php", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessLogger/ProcessLogger.css", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessLogger/ProcessLogger.js", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessLogger/ProcessLogger.module", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessLogin/ProcessLogin.css", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessLogin/ProcessLogin.js", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessLogin/ProcessLogin.module", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessModule/ProcessModule.css", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessModule/ProcessModule.js", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessModule/ProcessModule.module", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessModule/ProcessModule.scss", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessModule/ProcessModuleInstall.php", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageAdd/ProcessPageAdd.css", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageAdd/ProcessPageAdd.js", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageAdd/ProcessPageAdd.module", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageClone.module", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageEdit/PageBookmarks.css", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageEdit/PageBookmarks.php", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageEdit/ProcessPageEdit.css", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageEdit/ProcessPageEdit.js", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageEdit/ProcessPageEdit.module", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageEdit/ProcessPageEdit.scss", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageEditImageSelect/ProcessPageEditImageSelect.css", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageEditImageSelect/ProcessPageEditImageSelect.js", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageEditImageSelect/ProcessPageEditImageSelect.module", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageEditImageSelect/cropper/LICENSE.md", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageEditImageSelect/cropper/cropper.css", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageEditImageSelect/cropper/cropper.js", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageEditImageSelect/cropper/cropper.min.css", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageEditLink/ProcessPageEditLink.css", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageEditLink/ProcessPageEditLink.js", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageEditLink/ProcessPageEditLink.module", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageList/ProcessPageList.css", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageList/ProcessPageList.js", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageList/ProcessPageList.module", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageList/ProcessPageListActions.php", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageList/ProcessPageListRender.php", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageList/ProcessPageListRenderJSON.php", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageList/images/key.png", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageList/images/loading.gif", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageList/images/lock.gif", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageList/images/unlock.png", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageLister/ListerBookmarks.php", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageLister/ProcessPageLister.css", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageLister/ProcessPageLister.info.json", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageLister/ProcessPageLister.js", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageLister/ProcessPageLister.module", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageLister/ProcessPageLister.scss", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageLister/ProcessPageListerBookmarks.php", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageSearch/ProcessPageSearch.css", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageSearch/ProcessPageSearch.js", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageSearch/ProcessPageSearch.module", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageSearch/ProcessPageSearchLive.php", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageSort.module", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageTrash.module", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageType/ProcessPageType.css", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageType/ProcessPageType.js", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageType/ProcessPageType.module", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageView.module", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPagesExportImport/ProcessPagesExportImport.css", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPagesExportImport/ProcessPagesExportImport.js", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPagesExportImport/ProcessPagesExportImport.module", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPermission/ProcessPermission.css", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPermission/ProcessPermission.module", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessProfile/ProcessProfile.js", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessProfile/ProcessProfile.module", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessRecentPages/ProcessRecentPages.module", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessRole/ProcessRole.css", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessRole/ProcessRole.js", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessRole/ProcessRole.module", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessTemplate/ProcessTemplate.css", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessTemplate/ProcessTemplate.js", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessTemplate/ProcessTemplate.module", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessTemplate/ProcessTemplateExportImport.php", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessTemplate/ProcessTemplateFieldCreator.js", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessUser/ProcessUser.js", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessUser/ProcessUser.module", "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessUser/ProcessUserConfig.php", "downloaded_repos/processwire_processwire/wire/modules/Session/SessionHandlerDB/ProcessSessionDB.css", "downloaded_repos/processwire_processwire/wire/modules/Session/SessionHandlerDB/ProcessSessionDB.js", "downloaded_repos/processwire_processwire/wire/modules/Session/SessionHandlerDB/ProcessSessionDB.module", "downloaded_repos/processwire_processwire/wire/modules/Session/SessionHandlerDB/SessionHandlerDB.module", "downloaded_repos/processwire_processwire/wire/modules/Session/SessionLoginThrottle/SessionLoginThrottle.module", "downloaded_repos/processwire_processwire/wire/modules/System/SystemNotifications/FieldtypeNotifications.module", "downloaded_repos/processwire_processwire/wire/modules/System/SystemNotifications/Notification.php", "downloaded_repos/processwire_processwire/wire/modules/System/SystemNotifications/NotificationArray.php", "downloaded_repos/processwire_processwire/wire/modules/System/SystemNotifications/Notifications.css", "downloaded_repos/processwire_processwire/wire/modules/System/SystemNotifications/Notifications.js", "downloaded_repos/processwire_processwire/wire/modules/System/SystemNotifications/SystemNotifications.module", "downloaded_repos/processwire_processwire/wire/modules/System/SystemNotifications/SystemNotificationsConfig.php", "downloaded_repos/processwire_processwire/wire/modules/System/SystemUpdater/SystemUpdate.php", "downloaded_repos/processwire_processwire/wire/modules/System/SystemUpdater/SystemUpdate1.php", "downloaded_repos/processwire_processwire/wire/modules/System/SystemUpdater/SystemUpdate10.php", "downloaded_repos/processwire_processwire/wire/modules/System/SystemUpdater/SystemUpdate11.php", "downloaded_repos/processwire_processwire/wire/modules/System/SystemUpdater/SystemUpdate12.php", "downloaded_repos/processwire_processwire/wire/modules/System/SystemUpdater/SystemUpdate13.php", "downloaded_repos/processwire_processwire/wire/modules/System/SystemUpdater/SystemUpdate14.php", "downloaded_repos/processwire_processwire/wire/modules/System/SystemUpdater/SystemUpdate15.php", "downloaded_repos/processwire_processwire/wire/modules/System/SystemUpdater/SystemUpdate16.php", "downloaded_repos/processwire_processwire/wire/modules/System/SystemUpdater/SystemUpdate17.php", "downloaded_repos/processwire_processwire/wire/modules/System/SystemUpdater/SystemUpdate18.php", "downloaded_repos/processwire_processwire/wire/modules/System/SystemUpdater/SystemUpdate19.php", "downloaded_repos/processwire_processwire/wire/modules/System/SystemUpdater/SystemUpdate20.php", "downloaded_repos/processwire_processwire/wire/modules/System/SystemUpdater/SystemUpdate4.php", "downloaded_repos/processwire_processwire/wire/modules/System/SystemUpdater/SystemUpdate5.php", "downloaded_repos/processwire_processwire/wire/modules/System/SystemUpdater/SystemUpdate6.php", "downloaded_repos/processwire_processwire/wire/modules/System/SystemUpdater/SystemUpdate7.php", "downloaded_repos/processwire_processwire/wire/modules/System/SystemUpdater/SystemUpdate9.php", "downloaded_repos/processwire_processwire/wire/modules/System/SystemUpdater/SystemUpdater.module", "downloaded_repos/processwire_processwire/wire/modules/System/SystemUpdater/SystemUpdaterChecks.php", "downloaded_repos/processwire_processwire/wire/modules/Textformatter/TextformatterEntities.module", "downloaded_repos/processwire_processwire/wire/modules/Textformatter/TextformatterMarkdownExtra/TextformatterMarkdownExtra.module", "downloaded_repos/processwire_processwire/wire/modules/Textformatter/TextformatterMarkdownExtra/parsedown/LICENSE.txt", "downloaded_repos/processwire_processwire/wire/modules/Textformatter/TextformatterMarkdownExtra/parsedown/Parsedown.php", "downloaded_repos/processwire_processwire/wire/modules/Textformatter/TextformatterMarkdownExtra/parsedown/README.md", "downloaded_repos/processwire_processwire/wire/modules/Textformatter/TextformatterMarkdownExtra/parsedown-extra/LICENSE.txt", "downloaded_repos/processwire_processwire/wire/modules/Textformatter/TextformatterMarkdownExtra/parsedown-extra/ParsedownExtra.php", "downloaded_repos/processwire_processwire/wire/modules/Textformatter/TextformatterMarkdownExtra/parsedown-extra/README.md", "downloaded_repos/processwire_processwire/wire/modules/Textformatter/TextformatterNewlineBR.module", "downloaded_repos/processwire_processwire/wire/modules/Textformatter/TextformatterNewlineUL.module", "downloaded_repos/processwire_processwire/wire/modules/Textformatter/TextformatterPstripper.module", "downloaded_repos/processwire_processwire/wire/modules/Textformatter/TextformatterSmartypants/Michelf/License.md", "downloaded_repos/processwire_processwire/wire/modules/Textformatter/TextformatterSmartypants/Michelf/Readme.md", "downloaded_repos/processwire_processwire/wire/modules/Textformatter/TextformatterSmartypants/Michelf/SmartyPants.inc.php", "downloaded_repos/processwire_processwire/wire/modules/Textformatter/TextformatterSmartypants/Michelf/SmartyPants.php", "downloaded_repos/processwire_processwire/wire/modules/Textformatter/TextformatterSmartypants/Michelf/SmartyPantsTypographer.inc.php", "downloaded_repos/processwire_processwire/wire/modules/Textformatter/TextformatterSmartypants/Michelf/SmartyPantsTypographer.php", "downloaded_repos/processwire_processwire/wire/modules/Textformatter/TextformatterSmartypants/TextformatterSmartypants.module", "downloaded_repos/processwire_processwire/wire/modules/Textformatter/TextformatterStripTags.module", "downloaded_repos/processwire_processwire/wire/templates-admin/controller.php", "downloaded_repos/processwire_processwire/wire/templates-admin/debug.inc", "downloaded_repos/processwire_processwire/wire/templates-admin/default.php", "downloaded_repos/processwire_processwire/wire/templates-admin/install-foot.inc", "downloaded_repos/processwire_processwire/wire/templates-admin/install-head.inc", "downloaded_repos/processwire_processwire/wire/templates-admin/notices.inc", "downloaded_repos/processwire_processwire/wire/templates-admin/scripts/inputfields.js", "downloaded_repos/processwire_processwire/wire/templates-admin/scripts/install.js", "downloaded_repos/processwire_processwire/wire/templates-admin/scripts/main.js", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/AdminTheme.css", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/AdminTheme.scss", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/JqueryUI/JqueryUI.css", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/JqueryUI/images/animated-overlay.gif", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/JqueryUI/images/ui-bg_flat_0_000000_40x100.png", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/JqueryUI/images/ui-bg_flat_0_ffffff_40x100.png", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/JqueryUI/images/ui-bg_flat_80_000000_40x100.png", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/JqueryUI/images/ui-bg_glass_30_87a71b_1x400.png", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/JqueryUI/images/ui-bg_glass_30_cdea6d_1x400.png", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/JqueryUI/images/ui-bg_glass_30_db1174_1x400.png", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/JqueryUI/images/ui-bg_glass_30_e4ebee_1x400.png", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/JqueryUI/images/ui-bg_highlight-soft_30_d2e4ea_1x100.png", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/JqueryUI/images/ui-bg_inset-soft_95_fef1ec_1x100.png", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/JqueryUI/images/ui-icons_222222_256x240.png", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/JqueryUI/images/ui-icons_2f4248_256x240.png", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/JqueryUI/images/ui-icons_cd0a0a_256x240.png", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/JqueryUI/images/ui-icons_ffffff_256x240.png", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/HELP-US-OUT.txt", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/css/font-awesome.css", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/css/font-awesome.min.css", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/fonts/FontAwesome.otf", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/fonts/fontawesome-webfont.eot", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/fonts/fontawesome-webfont.svg", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/fonts/fontawesome-webfont.ttf", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/fonts/fontawesome-webfont.woff", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/fonts/fontawesome-webfont.woff2", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/less/animated.less", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/less/bordered-pulled.less", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/less/core.less", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/less/fixed-width.less", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/less/font-awesome.less", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/less/icons.less", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/less/larger.less", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/less/list.less", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/less/mixins.less", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/less/path.less", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/less/rotated-flipped.less", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/less/screen-reader.less", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/less/stacked.less", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/less/variables.less", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/scss/_animated.scss", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/scss/_bordered-pulled.scss", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/scss/_core.scss", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/scss/_fixed-width.scss", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/scss/_icons.scss", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/scss/_larger.scss", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/scss/_list.scss", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/scss/_mixins.scss", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/scss/_path.scss", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/scss/_rotated-flipped.scss", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/scss/_screen-reader.scss", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/scss/_stacked.scss", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/scss/_variables.scss", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/scss/font-awesome.css", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/font-awesome/scss/font-awesome.scss", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/ie.css", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/ie7.css", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/images/bg.gif", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/images/bg_content.gif", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/images/btn-search.png", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/images/bullet_breadcrumb.gif", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/images/logo.gif", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/images/topnav_a_bg.gif", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/inputfields.css", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/install.css", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/main.css", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/reset.css", "downloaded_repos/processwire_processwire/wire/templates-admin/styles/ui.css", "downloaded_repos/processwire_processwire/wire/templates-admin/topnav.inc"], "skipped": [{"path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeDefault/scripts/main.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeReno/scripts/main.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/config-field.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/callbacks/jquery.layout.callbacks-latest.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/callbacks/jquery.layout.callbacks.min-latest.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/callbacks/jquery.layout.pseudoClose.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/callbacks/jquery.layout.resizeDataTable.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/callbacks/jquery.layout.resizePaneAccordions.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/callbacks/jquery.layout.resizeTabLayout.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout_and_plugins.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout_and_plugins.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/plugins/jquery.layout.browserZoom.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/plugins/jquery.layout.buttons.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/plugins/jquery.layout.slideOffscreen.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/plugins/jquery.layout.state.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/scripts/main.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/dist/css/uikit-rtl.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/dist/css/uikit-rtl.min.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/dist/css/uikit.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/dist/css/uikit.min.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/dist/js/components/.gitkeep", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/dist/js/uikit-icons.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/dist/js/uikit-icons.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/dist/js/uikit.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/uikit/dist/js/uikit.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeComments/comments.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeRepeater/InputfieldRepeater.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Image/ImageSizerEngineAnimatedGif/gif_encoder.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldAsmSelect/InputfieldAsmSelect.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldAsmSelect/asmselect/jquery.asmselect.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/InputfieldCKEditor.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/ckeditor.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/_helpers/tools.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/authentication.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/exportpdf.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/manual/configfilename.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/manual/configfilename.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/manual/emptyeditor.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/manual/emptyeditor.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/manual/integration.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/manual/integration.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/manual/integrations/easyimage.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/manual/integrations/easyimage.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/manual/notifications.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/manual/notifications.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/manual/notificationsasync.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/manual/notificationsasync.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/manual/paperformat.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/manual/paperformat.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/manual/readonly.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/manual/readonly.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/manual/stylesheets.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/manual/stylesheets.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/manual/tokenfetching.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/manual/tokenfetching.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/manual/tokentwoeditorscorrect.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/manual/tokentwoeditorscorrect.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/manual/tokentwoeditorswrong.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/manual/tokentwoeditorswrong.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/manual/tokenwithouturl.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/manual/tokenwithouturl.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/manual/wrongendpoint.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/manual/wrongendpoint.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/notification.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/resourcespaths.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/statistics.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/exportpdf/tests/stylesheets.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/vendor/promise.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/pwimage/plugin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/pwlink/plugin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCheckboxes/InputfieldCheckboxes.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/InputfieldDatetime.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/jquery-ui-sliderAccess.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/jquery-ui-timepicker-addon.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldFile/InputfieldFile.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldIcon/InputfieldIcon.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldImage/InputfieldImage.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldImage/PWImageResizer.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldImage/exif.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldImage/piexif.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPage/InputfieldPage.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPageAutocomplete/InputfieldPageAutocomplete.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPageListSelect/InputfieldPageListSelect.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPageListSelect/InputfieldPageListSelectMultiple.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPageName/InputfieldPageName.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPageTable/InputfieldPageTable.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPageTitle/InputfieldPageTitle.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPassword/InputfieldPassword.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPassword/complexify/jquery.complexify.banlist.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldPassword/complexify/jquery.complexify.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldSelector/InputfieldSelector.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldSubmit/dropdown.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldText/InputfieldTextLength.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTextTags/InputfieldTextTags.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/icons/default/icons.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/models/dom/model.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/accordion/plugin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/advlist/plugin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/anchor/plugin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/autolink/plugin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/autoresize/plugin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/autosave/plugin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/charmap/plugin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/code/plugin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/codesample/plugin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/directionality/plugin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/emoticons/js/emojiimages.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/emoticons/js/emojis.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/emoticons/plugin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/fullscreen/plugin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/plugin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/image/plugin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/importcss/plugin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/insertdatetime/plugin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/link/plugin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/lists/plugin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/media/plugin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/nonbreaking/plugin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/pagebreak/plugin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/preview/plugin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/quickbars/plugin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/save/plugin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/searchreplace/plugin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/table/plugin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/template/plugin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/visualblocks/plugin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/visualchars/plugin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/wordcount/plugin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/themes/silver/theme.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/tinymce.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldToggle/InputfieldToggle.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/JqueryCore.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/dev/JqueryCore.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/dev/jquery-migrate-quiet-3.4.0.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/iframe-resizer-frame.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/iframe-resizer.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/jquery-migrate-quiet-1.4.1.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/jquery.cookie.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/jquery.longclick.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/jquery.simulate.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/legacy/JqueryCore.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/xregexp.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryMagnific/JqueryMagnific.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/JqueryUI.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/dev/JqueryUI.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/modal.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/panel.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/selectize/js/selectize.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/selectize/js/standalone/selectize.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/scripts/vex.combined.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/scripts/vex.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryWireTabs/JqueryWireTabs.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/LanguageSupport/LanguageTabs.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/LanguageSupport/ProcessLanguage.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/LanguageSupport/ProcessLanguageTranslator.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupAdminDataTable/MarkupAdminDataTable.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/HTMLPurifier.standalone.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Page/PageFrontEdit/PageFrontEdit.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Page/PageFrontEdit/PageFrontEditLoad.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessCommentsManager/ProcessCommentsManager.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessField/ProcessField.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessLogger/ProcessLogger.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessLogin/ProcessLogin.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessLogin/what-input.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessModule/ProcessModule.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageAdd/ProcessPageAdd.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageEdit/ProcessPageEdit.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageEditImageSelect/ProcessPageEditImageSelect.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageEditImageSelect/cropper/cropper.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageEditLink/ProcessPageEditLink.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageList/ProcessPageList.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageLister/ProcessPageLister.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageSearch/ProcessPageSearch.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageType/ProcessPageType.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPagesExportImport/ProcessPagesExportImport.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessProfile/ProcessProfile.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessRole/ProcessRole.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessTemplate/ProcessTemplate.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessTemplate/ProcessTemplateFieldCreator.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/Process/ProcessUser/ProcessUser.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/modules/System/SystemNotifications/Notifications.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/templates-admin/scripts/inputfields.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/processwire_processwire/wire/templates-admin/scripts/main.min.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6460108757019043, "profiling_times": {"config_time": 5.573832988739014, "core_time": 44.66641044616699, "ignores_time": 0.0018641948699951172, "total_time": 50.243247747421265}, "parsing_time": {"total_time": 70.95178937911987, "per_file_time": {"mean": 0.07352517034105696, "std_dev": 0.02981842855574672}, "very_slow_stats": {"time_ratio": 0.3727873658907473, "count_ratio": 0.03626943005181347}, "very_slow_files": [{"fpath": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/tinymce.d.ts", "ftime": 0.8637897968292236}, {"fpath": "downloaded_repos/processwire_processwire/wire/templates-admin/scripts/inputfields.js", "ftime": 0.986746072769165}, {"fpath": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/jquery-ui-timepicker-addon.js", "ftime": 1.048218011856079}, {"fpath": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/xregexp.js", "ftime": 1.1233088970184326}, {"fpath": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/selectize/js/selectize.js", "ftime": 1.2593190670013428}, {"fpath": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/selectize/js/standalone/selectize.js", "ftime": 1.4295039176940918}, {"fpath": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/JqueryTableSorter.js", "ftime": 1.7486658096313477}, {"fpath": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/emoticons/js/emojis.js", "ftime": 1.7764501571655273}, {"fpath": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js", "ftime": 2.017927885055542}, {"fpath": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/emoticons/js/emojiimages.js", "ftime": 2.0396008491516113}]}, "scanning_time": {"total_time": 498.27193570137024, "per_file_time": {"mean": 0.09451288613455429, "std_dev": 0.9824560498962633}, "very_slow_stats": {"time_ratio": 0.699339753827053, "count_ratio": 0.01024279210925645}, "very_slow_files": [{"fpath": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/selectize/js/selectize.js", "ftime": 10.820249080657959}, {"fpath": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/jquery-ui-timepicker-addon.js", "ftime": 11.266361951828003}, {"fpath": "downloaded_repos/processwire_processwire/wire/templates-admin/scripts/inputfields.js", "ftime": 11.55803894996643}, {"fpath": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/scripts/vex.combined.js", "ftime": 11.731352090835571}, {"fpath": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/selectize/js/standalone/selectize.js", "ftime": 14.499514102935791}, {"fpath": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/dev/JqueryCore.js", "ftime": 15.080518960952759}, {"fpath": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/JqueryTableSorter.js", "ftime": 20.428706884384155}, {"fpath": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js", "ftime": 30.885962963104248}, {"fpath": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout.js", "ftime": 31.05447292327881}, {"fpath": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout_and_plugins.js", "ftime": 33.84899616241455}]}, "matching_time": {"total_time": 175.75541424751282, "per_file_and_rule_time": {"mean": 0.040422128391792274, "std_dev": 0.022115605267651033}, "very_slow_stats": {"time_ratio": 0.7232546088856102, "count_ratio": 0.07704691812327506}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout_and_plugins.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 1.482996940612793}, {"fpath": "downloaded_repos/processwire_processwire/wire/templates-admin/scripts/inputfields.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 1.5134468078613281}, {"fpath": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 1.5344748497009277}, {"fpath": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/emoticons/js/emojis.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 1.6260709762573242}, {"fpath": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/emoticons/js/emojiimages.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 1.6692628860473633}, {"fpath": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout.js", "rule_id": "problem-based-packs.insecure-transport.js-node.using-http-server.using-http-server", "time": 1.8464949131011963}, {"fpath": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 2.072010040283203}, {"fpath": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout_and_plugins.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 2.0978920459747314}, {"fpath": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 2.5482559204101562}, {"fpath": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 3.111018180847168}]}, "tainting_time": {"total_time": 112.2594952583313, "per_def_and_rule_time": {"mean": 0.006674167375643955, "std_dev": 0.005932229686074797}, "very_slow_stats": {"time_ratio": 0.7717569878079122, "count_ratio": 0.014387633769322235}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/jquery-ui-timepicker-addon.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 1.6985299587249756}, {"fpath": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout.js", "fline": 1, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 1.7251508235931396}, {"fpath": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 1.8869779109954834}, {"fpath": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout_and_plugins.js", "fline": 1, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 2.0674757957458496}, {"fpath": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/xregexp.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 2.067884922027588}, {"fpath": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/JqueryTableSorter.js", "fline": 1, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 2.098302125930786}, {"fpath": "downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout_and_plugins.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 2.1459450721740723}, {"fpath": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/JqueryTableSorter.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 2.2096760272979736}, {"fpath": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js", "fline": 1, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 3.9647130966186523}, {"fpath": "downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 4.341749906539917}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1158383360}, "engine_requested": "OSS", "skipped_rules": []}