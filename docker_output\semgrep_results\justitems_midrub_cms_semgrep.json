{"version": "1.130.0", "results": [{"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/admin/helpers/themes.php", "start": {"line": 297, "col": 25, "offset": 8374}, "end": {"line": 297, "col": 120, "offset": 8469}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/admin/helpers/themes.php", "start": {"line": 496, "col": 17, "offset": 17948}, "end": {"line": 496, "col": 30, "offset": 17961}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/helpers/multimedia.php", "start": {"line": 303, "col": 26, "offset": 10190}, "end": {"line": 303, "col": 43, "offset": 10207}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/helpers/multimedia.php", "start": {"line": 306, "col": 26, "offset": 10299}, "end": {"line": 306, "col": 101, "offset": 10374}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/helpers/social.php", "start": {"line": 256, "col": 25, "offset": 7413}, "end": {"line": 256, "col": 123, "offset": 7511}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/helpers/social.php", "start": {"line": 406, "col": 17, "offset": 13616}, "end": {"line": 406, "col": 30, "offset": 13629}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/helpers/themes.php", "start": {"line": 379, "col": 25, "offset": 11273}, "end": {"line": 379, "col": 123, "offset": 11371}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/helpers/themes.php", "start": {"line": 597, "col": 17, "offset": 22094}, "end": {"line": 597, "col": 30, "offset": 22107}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/helpers/plans.php", "start": {"line": 202, "col": 34, "offset": 5918}, "end": {"line": 202, "col": 78, "offset": 5962}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/helpers/plans.php", "start": {"line": 286, "col": 42, "offset": 8547}, "end": {"line": 286, "col": 91, "offset": 8596}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/views/users_alerts/alert.php", "start": {"line": 741, "col": 62, "offset": 48578}, "end": {"line": 741, "col": 85, "offset": 48601}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/plugins/helpers/plugins.php", "start": {"line": 336, "col": 25, "offset": 9941}, "end": {"line": 336, "col": 122, "offset": 10038}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/plugins/helpers/plugins.php", "start": {"line": 581, "col": 17, "offset": 21812}, "end": {"line": 581, "col": 30, "offset": 21825}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/settings/inc/storage_init_hooks.php", "start": {"line": 203, "col": 22, "offset": 6700}, "end": {"line": 203, "col": 39, "offset": 6717}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/settings/inc/storage_init_hooks.php", "start": {"line": 206, "col": 22, "offset": 6797}, "end": {"line": 206, "col": 97, "offset": 6872}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/helpers/apps.php", "start": {"line": 361, "col": 29, "offset": 11233}, "end": {"line": 361, "col": 45, "offset": 11249}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/helpers/apps.php", "start": {"line": 809, "col": 33, "offset": 27871}, "end": {"line": 809, "col": 72, "offset": 27910}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/helpers/apps.php", "start": {"line": 825, "col": 33, "offset": 28666}, "end": {"line": 825, "col": 73, "offset": 28706}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/helpers/apps.php", "start": {"line": 993, "col": 25, "offset": 34277}, "end": {"line": 993, "col": 58, "offset": 34310}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/helpers/frontend_themes.php", "start": {"line": 330, "col": 29, "offset": 10524}, "end": {"line": 330, "col": 45, "offset": 10540}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/helpers/frontend_themes.php", "start": {"line": 778, "col": 33, "offset": 27244}, "end": {"line": 778, "col": 74, "offset": 27285}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/helpers/frontend_themes.php", "start": {"line": 794, "col": 33, "offset": 28049}, "end": {"line": 794, "col": 75, "offset": 28091}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/helpers/frontend_themes.php", "start": {"line": 962, "col": 25, "offset": 33668}, "end": {"line": 962, "col": 60, "offset": 33703}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/helpers/midrub.php", "start": {"line": 97, "col": 21, "offset": 2181}, "end": {"line": 97, "col": 46, "offset": 2206}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/helpers/midrub.php", "start": {"line": 480, "col": 25, "offset": 14179}, "end": {"line": 480, "col": 56, "offset": 14210}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/helpers/plugins.php", "start": {"line": 353, "col": 29, "offset": 10981}, "end": {"line": 353, "col": 45, "offset": 10997}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/helpers/plugins.php", "start": {"line": 801, "col": 33, "offset": 27698}, "end": {"line": 801, "col": 75, "offset": 27740}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/helpers/plugins.php", "start": {"line": 817, "col": 33, "offset": 28508}, "end": {"line": 817, "col": 76, "offset": 28551}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/helpers/plugins.php", "start": {"line": 985, "col": 25, "offset": 34161}, "end": {"line": 985, "col": 61, "offset": 34197}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/helpers/user_components.php", "start": {"line": 348, "col": 29, "offset": 11023}, "end": {"line": 348, "col": 45, "offset": 11039}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/helpers/user_components.php", "start": {"line": 796, "col": 33, "offset": 27868}, "end": {"line": 796, "col": 79, "offset": 27914}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/helpers/user_components.php", "start": {"line": 812, "col": 33, "offset": 28698}, "end": {"line": 812, "col": 79, "offset": 28744}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/helpers/user_components.php", "start": {"line": 980, "col": 25, "offset": 34372}, "end": {"line": 980, "col": 65, "offset": 34412}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/helpers/apps.php", "start": {"line": 163, "col": 25, "offset": 4601}, "end": {"line": 163, "col": 119, "offset": 4695}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/helpers/apps.php", "start": {"line": 384, "col": 17, "offset": 15565}, "end": {"line": 384, "col": 30, "offset": 15578}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/helpers/components.php", "start": {"line": 424, "col": 25, "offset": 12860}, "end": {"line": 424, "col": 119, "offset": 12954}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/helpers/components.php", "start": {"line": 640, "col": 17, "offset": 23510}, "end": {"line": 640, "col": 30, "offset": 23523}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/helpers/social.php", "start": {"line": 255, "col": 25, "offset": 7253}, "end": {"line": 255, "col": 119, "offset": 7347}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/helpers/social.php", "start": {"line": 405, "col": 17, "offset": 13332}, "end": {"line": 405, "col": 30, "offset": 13345}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/helpers/themes.php", "start": {"line": 296, "col": 25, "offset": 8343}, "end": {"line": 296, "col": 119, "offset": 8437}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/helpers/themes.php", "start": {"line": 498, "col": 17, "offset": 17959}, "end": {"line": 498, "col": 30, "offset": 17972}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/classes/media/download.php", "start": {"line": 949, "col": 17, "offset": 37705}, "end": {"line": 949, "col": 34, "offset": 37722}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/classes/media/upload.php", "start": {"line": 963, "col": 17, "offset": 24635}, "end": {"line": 963, "col": 34, "offset": 24652}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/frontend/classes/contents.php", "start": {"line": 195, "col": 25, "offset": 5378}, "end": {"line": 195, "col": 43, "offset": 5396}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/inc/contents/contents_categories.php", "start": {"line": 297, "col": 51, "offset": 10838}, "end": {"line": 297, "col": 74, "offset": 10861}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.curl-ssl-verifypeer-off.curl-ssl-verifypeer-off", "path": "downloaded_repos/justitems_midrub_cms/application/base/inc/curl/delete.php", "start": {"line": 39, "col": 9, "offset": 935}, "end": {"line": 39, "col": 58, "offset": 984}, "extra": {"message": "SSL verification is disabled but should not be (currently CURLOPT_SSL_VERIFYPEER= false)", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.saotn.org/dont-turn-off-curlopt_ssl_verifypeer-fix-php-configuration/"], "category": "security", "technology": ["php"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.curl-ssl-verifypeer-off.curl-ssl-verifypeer-off", "shortlink": "https://sg.run/PJqv"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.curl-ssl-verifypeer-off.curl-ssl-verifypeer-off", "path": "downloaded_repos/justitems_midrub_cms/application/base/inc/curl/post.php", "start": {"line": 39, "col": 9, "offset": 935}, "end": {"line": 39, "col": 58, "offset": 984}, "extra": {"message": "SSL verification is disabled but should not be (currently CURLOPT_SSL_VERIFYPEER= false)", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.saotn.org/dont-turn-off-curlopt_ssl_verifypeer-fix-php-configuration/"], "category": "security", "technology": ["php"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.curl-ssl-verifypeer-off.curl-ssl-verifypeer-off", "shortlink": "https://sg.run/PJqv"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-bcrypt-hash.detected-bcrypt-hash", "path": "downloaded_repos/justitems_midrub_cms/application/base/install/templates/midrub.sql", "start": {"line": 963, "col": 66, "offset": 31126}, "end": {"line": 963, "col": 126, "offset": 31186}, "extra": {"message": "bcrypt hash detected", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "category": "security", "technology": ["secrets", "bcrypt"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-bcrypt-hash.detected-bcrypt-hash", "shortlink": "https://sg.run/3A8G"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/rest/classes/helper.php", "start": {"line": 259, "col": 27, "offset": 6341}, "end": {"line": 259, "col": 48, "offset": 6362}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/rest/classes/helper.php", "start": {"line": 367, "col": 27, "offset": 9628}, "end": {"line": 367, "col": 48, "offset": 9649}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/rest/classes/security.php", "start": {"line": 247, "col": 27, "offset": 6084}, "end": {"line": 247, "col": 48, "offset": 6105}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/rest/classes/security.php", "start": {"line": 354, "col": 27, "offset": 9143}, "end": {"line": 354, "col": 48, "offset": 9164}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/justitems_midrub_cms/application/base/rest/classes/token.php", "start": {"line": 817, "col": 27, "offset": 35378}, "end": {"line": 817, "col": 48, "offset": 35399}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.php-permissive-cors.php-permissive-cors", "path": "downloaded_repos/justitems_midrub_cms/application/base/user/networks/collection/tumblr.php", "start": {"line": 94, "col": 9, "offset": 2292}, "end": {"line": 94, "col": 49, "offset": 2332}, "extra": {"message": "Access-Control-Allow-Origin response header is set to \"*\". This will disable CORS Same Origin Policy restrictions.", "metadata": {"references": ["https://developer.mozilla.org/ru/docs/Web/HTTP/Headers/Access-Control-Allow-Origin"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "cwe": ["CWE-346: Origin Validation Error"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/php.lang.security.php-permissive-cors.php-permissive-cors", "shortlink": "https://sg.run/y1XR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.php-permissive-cors.php-permissive-cors", "path": "downloaded_repos/justitems_midrub_cms/application/base/user/networks/collection/twitter.php", "start": {"line": 540, "col": 25, "offset": 17715}, "end": {"line": 540, "col": 65, "offset": 17755}, "extra": {"message": "Access-Control-Allow-Origin response header is set to \"*\". This will disable CORS Same Origin Policy restrictions.", "metadata": {"references": ["https://developer.mozilla.org/ru/docs/Web/HTTP/Headers/Access-Control-Allow-Origin"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "cwe": ["CWE-346: Origin Validation Error"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/php.lang.security.php-permissive-cors.php-permissive-cors", "shortlink": "https://sg.run/y1XR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.php-permissive-cors.php-permissive-cors", "path": "downloaded_repos/justitems_midrub_cms/application/base/user/networks/collection/twitter.php", "start": {"line": 661, "col": 25, "offset": 22736}, "end": {"line": 661, "col": 65, "offset": 22776}, "extra": {"message": "Access-Control-Allow-Origin response header is set to \"*\". This will disable CORS Same Origin Policy restrictions.", "metadata": {"references": ["https://developer.mozilla.org/ru/docs/Web/HTTP/Headers/Access-Control-Allow-Origin"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "cwe": ["CWE-346: Origin Validation Error"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/php.lang.security.php-permissive-cors.php-permissive-cors", "shortlink": "https://sg.run/y1XR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/controllers/Cron.php", "start": {"line": 122, "col": 17, "offset": 3201}, "end": {"line": 122, "col": 34, "offset": 3218}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/controllers/Cron.php", "start": {"line": 131, "col": 21, "offset": 3531}, "end": {"line": 131, "col": 38, "offset": 3548}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/application/controllers/Cron.php", "start": {"line": 248, "col": 17, "offset": 6708}, "end": {"line": 248, "col": 34, "offset": 6725}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/justitems_midrub_cms/assets/base/user/default/js/libs/lists/posts-list.js", "start": {"line": 602, "col": 25, "offset": 23924}, "end": {"line": 602, "col": 84, "offset": 23983}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/system/core/Common.php", "start": {"line": 113, "col": 5, "offset": 3586}, "end": {"line": 113, "col": 18, "offset": 3599}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.eval-use.eval-use", "path": "downloaded_repos/justitems_midrub_cms/system/core/Loader.php", "start": {"line": 965, "col": 4, "offset": 25133}, "end": {"line": 965, "col": 120, "offset": 25249}, "extra": {"message": "Evaluating non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://www.php.net/manual/en/function.eval", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/NoEvalsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/php.lang.security.eval-use.eval-use", "shortlink": "https://sg.run/J9AP"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/justitems_midrub_cms/system/core/Output.php", "start": {"line": 588, "col": 23, "offset": 14837}, "end": {"line": 588, "col": 34, "offset": 14848}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/system/core/Output.php", "start": {"line": 637, "col": 5, "offset": 15929}, "end": {"line": 637, "col": 24, "offset": 15948}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/justitems_midrub_cms/system/core/Output.php", "start": {"line": 637, "col": 12, "offset": 15936}, "end": {"line": 637, "col": 23, "offset": 15947}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/justitems_midrub_cms/system/core/Output.php", "start": {"line": 642, "col": 9, "offset": 16062}, "end": {"line": 642, "col": 20, "offset": 16073}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/justitems_midrub_cms/system/core/Output.php", "start": {"line": 682, "col": 22, "offset": 17261}, "end": {"line": 682, "col": 31, "offset": 17270}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/justitems_midrub_cms/system/core/Output.php", "start": {"line": 682, "col": 51, "offset": 17290}, "end": {"line": 682, "col": 60, "offset": 17299}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/justitems_midrub_cms/system/core/Output.php", "start": {"line": 689, "col": 22, "offset": 17379}, "end": {"line": 689, "col": 31, "offset": 17388}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/justitems_midrub_cms/system/core/Output.php", "start": {"line": 689, "col": 60, "offset": 17417}, "end": {"line": 689, "col": 69, "offset": 17426}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/justitems_midrub_cms/system/core/Output.php", "start": {"line": 700, "col": 17, "offset": 17619}, "end": {"line": 700, "col": 39, "offset": 17641}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/justitems_midrub_cms/system/core/Output.php", "start": {"line": 703, "col": 30, "offset": 17708}, "end": {"line": 703, "col": 39, "offset": 17717}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/system/core/Output.php", "start": {"line": 709, "col": 5, "offset": 17863}, "end": {"line": 709, "col": 22, "offset": 17880}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/justitems_midrub_cms/system/core/Output.php", "start": {"line": 709, "col": 12, "offset": 17870}, "end": {"line": 709, "col": 21, "offset": 17879}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/system/core/Output.php", "start": {"line": 771, "col": 11, "offset": 19354}, "end": {"line": 771, "col": 30, "offset": 19373}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/justitems_midrub_cms/system/core/Output.php", "start": {"line": 771, "col": 18, "offset": 19361}, "end": {"line": 771, "col": 29, "offset": 19372}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-sql-string.tainted-sql-string", "path": "downloaded_repos/justitems_midrub_cms/system/core/Output.php", "start": {"line": 773, "col": 25, "offset": 19403}, "end": {"line": 773, "col": 64, "offset": 19442}, "extra": {"message": "User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(\"INSERT INTO test(id, label) VALUES (?, ?)\");`) or a safe library.", "metadata": {"cwe": ["CWE-89: Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection')"], "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "references": ["https://owasp.org/www-community/attacks/SQL_Injection"], "category": "security", "technology": ["php"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["SQL Injection"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-sql-string.tainted-sql-string", "shortlink": "https://sg.run/lZYG"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.mcrypt-use.mcrypt-use", "path": "downloaded_repos/justitems_midrub_cms/system/core/compat/password.php", "start": {"line": 134, "col": 5, "offset": 4482}, "end": {"line": 134, "col": 65, "offset": 4542}, "extra": {"message": "Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL.", "metadata": {"cwe": ["CWE-676: Use of Potentially Dangerous Function"], "references": ["https://www.php.net/manual/en/intro.mcrypt.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/CryptoFunctionsSniff.php"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Dangerous Method or Function"], "source": "https://semgrep.dev/r/php.lang.security.mcrypt-use.mcrypt-use", "shortlink": "https://sg.run/BkZR"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/justitems_midrub_cms/system/database/DB_cache.php", "start": {"line": 152, "col": 10, "offset": 4305}, "end": {"line": 152, "col": 33, "offset": 4328}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/system/database/drivers/pdo/subdrivers/pdo_sqlite_forge.php", "start": {"line": 127, "col": 12, "offset": 3574}, "end": {"line": 127, "col": 39, "offset": 3601}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/system/database/drivers/sqlite/sqlite_forge.php", "start": {"line": 96, "col": 49, "offset": 2912}, "end": {"line": 96, "col": 76, "offset": 2939}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/system/database/drivers/sqlite3/sqlite3_forge.php", "start": {"line": 113, "col": 12, "offset": 3315}, "end": {"line": 113, "col": 39, "offset": 3342}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/system/helpers/captcha_helper.php", "start": {"line": 128, "col": 6, "offset": 4059}, "end": {"line": 128, "col": 33, "offset": 4086}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/system/helpers/file_helper.php", "start": {"line": 150, "col": 7, "offset": 4650}, "end": {"line": 150, "col": 24, "offset": 4667}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Cache/drivers/Cache_file.php", "start": {"line": 124, "col": 44, "offset": 3594}, "end": {"line": 124, "col": 74, "offset": 3624}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Cache/drivers/Cache_file.php", "start": {"line": 225, "col": 11, "offset": 5687}, "end": {"line": 225, "col": 65, "offset": 5741}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Cache/drivers/Cache_file.php", "start": {"line": 276, "col": 11, "offset": 6693}, "end": {"line": 276, "col": 65, "offset": 6747}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Cache/drivers/Cache_file.php", "start": {"line": 280, "col": 43, "offset": 6861}, "end": {"line": 280, "col": 73, "offset": 6891}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Cache/drivers/Cache_redis.php", "start": {"line": 183, "col": 11, "offset": 4673}, "end": {"line": 183, "col": 30, "offset": 4692}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.mcrypt-use.mcrypt-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Encrypt.php", "start": {"line": 299, "col": 3, "offset": 7554}, "end": {"line": 299, "col": 77, "offset": 7628}, "extra": {"message": "Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL.", "metadata": {"cwe": ["CWE-676: Use of Potentially Dangerous Function"], "references": ["https://www.php.net/manual/en/intro.mcrypt.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/CryptoFunctionsSniff.php"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Dangerous Method or Function"], "source": "https://semgrep.dev/r/php.lang.security.mcrypt-use.mcrypt-use", "shortlink": "https://sg.run/BkZR"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.mcrypt-use.mcrypt-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Encrypt.php", "start": {"line": 300, "col": 3, "offset": 7631}, "end": {"line": 300, "col": 65, "offset": 7693}, "extra": {"message": "Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL.", "metadata": {"cwe": ["CWE-676: Use of Potentially Dangerous Function"], "references": ["https://www.php.net/manual/en/intro.mcrypt.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/CryptoFunctionsSniff.php"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Dangerous Method or Function"], "source": "https://semgrep.dev/r/php.lang.security.mcrypt-use.mcrypt-use", "shortlink": "https://sg.run/BkZR"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.mcrypt-use.mcrypt-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Encrypt.php", "start": {"line": 301, "col": 3, "offset": 7696}, "end": {"line": 301, "col": 134, "offset": 7827}, "extra": {"message": "Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL.", "metadata": {"cwe": ["CWE-676: Use of Potentially Dangerous Function"], "references": ["https://www.php.net/manual/en/intro.mcrypt.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/CryptoFunctionsSniff.php"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Dangerous Method or Function"], "source": "https://semgrep.dev/r/php.lang.security.mcrypt-use.mcrypt-use", "shortlink": "https://sg.run/BkZR"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.mcrypt-use.mcrypt-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Encrypt.php", "start": {"line": 316, "col": 3, "offset": 8102}, "end": {"line": 316, "col": 77, "offset": 8176}, "extra": {"message": "Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL.", "metadata": {"cwe": ["CWE-676: Use of Potentially Dangerous Function"], "references": ["https://www.php.net/manual/en/intro.mcrypt.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/CryptoFunctionsSniff.php"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Dangerous Method or Function"], "source": "https://semgrep.dev/r/php.lang.security.mcrypt-use.mcrypt-use", "shortlink": "https://sg.run/BkZR"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.mcrypt-use.mcrypt-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Encrypt.php", "start": {"line": 326, "col": 3, "offset": 8346}, "end": {"line": 326, "col": 104, "offset": 8447}, "extra": {"message": "Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL.", "metadata": {"cwe": ["CWE-676: Use of Potentially Dangerous Function"], "references": ["https://www.php.net/manual/en/intro.mcrypt.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/CryptoFunctionsSniff.php"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Dangerous Method or Function"], "source": "https://semgrep.dev/r/php.lang.security.mcrypt-use.mcrypt-use", "shortlink": "https://sg.run/BkZR"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.mcrypt-use.mcrypt-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Encryption.php", "start": {"line": 265, "col": 5, "offset": 6528}, "end": {"line": 265, "col": 41, "offset": 6564}, "extra": {"message": "Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL.", "metadata": {"cwe": ["CWE-676: Use of Potentially Dangerous Function"], "references": ["https://www.php.net/manual/en/intro.mcrypt.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/CryptoFunctionsSniff.php"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Dangerous Method or Function"], "source": "https://semgrep.dev/r/php.lang.security.mcrypt-use.mcrypt-use", "shortlink": "https://sg.run/BkZR"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.mcrypt-use.mcrypt-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Encryption.php", "start": {"line": 353, "col": 4, "offset": 8770}, "end": {"line": 353, "col": 56, "offset": 8822}, "extra": {"message": "Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL.", "metadata": {"cwe": ["CWE-676: Use of Potentially Dangerous Function"], "references": ["https://www.php.net/manual/en/intro.mcrypt.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/CryptoFunctionsSniff.php"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Dangerous Method or Function"], "source": "https://semgrep.dev/r/php.lang.security.mcrypt-use.mcrypt-use", "shortlink": "https://sg.run/BkZR"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.mcrypt-use.mcrypt-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Encryption.php", "start": {"line": 423, "col": 5, "offset": 10572}, "end": {"line": 423, "col": 44, "offset": 10611}, "extra": {"message": "Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL.", "metadata": {"cwe": ["CWE-676: Use of Potentially Dangerous Function"], "references": ["https://www.php.net/manual/en/intro.mcrypt.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/CryptoFunctionsSniff.php"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Dangerous Method or Function"], "source": "https://semgrep.dev/r/php.lang.security.mcrypt-use.mcrypt-use", "shortlink": "https://sg.run/BkZR"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.mcrypt-use.mcrypt-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Encryption.php", "start": {"line": 433, "col": 4, "offset": 10865}, "end": {"line": 433, "col": 63, "offset": 10924}, "extra": {"message": "Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL.", "metadata": {"cwe": ["CWE-676: Use of Potentially Dangerous Function"], "references": ["https://www.php.net/manual/en/intro.mcrypt.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/CryptoFunctionsSniff.php"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Dangerous Method or Function"], "source": "https://semgrep.dev/r/php.lang.security.mcrypt-use.mcrypt-use", "shortlink": "https://sg.run/BkZR"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.mcrypt-use.mcrypt-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Encryption.php", "start": {"line": 453, "col": 3, "offset": 11681}, "end": {"line": 453, "col": 44, "offset": 11722}, "extra": {"message": "Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL.", "metadata": {"cwe": ["CWE-676: Use of Potentially Dangerous Function"], "references": ["https://www.php.net/manual/en/intro.mcrypt.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/CryptoFunctionsSniff.php"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Dangerous Method or Function"], "source": "https://semgrep.dev/r/php.lang.security.mcrypt-use.mcrypt-use", "shortlink": "https://sg.run/BkZR"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.mcrypt-use.mcrypt-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Encryption.php", "start": {"line": 456, "col": 4, "offset": 11774}, "end": {"line": 456, "col": 43, "offset": 11813}, "extra": {"message": "Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL.", "metadata": {"cwe": ["CWE-676: Use of Potentially Dangerous Function"], "references": ["https://www.php.net/manual/en/intro.mcrypt.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/CryptoFunctionsSniff.php"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Dangerous Method or Function"], "source": "https://semgrep.dev/r/php.lang.security.mcrypt-use.mcrypt-use", "shortlink": "https://sg.run/BkZR"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.mcrypt-use.mcrypt-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Encryption.php", "start": {"line": 596, "col": 5, "offset": 14961}, "end": {"line": 596, "col": 44, "offset": 15000}, "extra": {"message": "Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL.", "metadata": {"cwe": ["CWE-676: Use of Potentially Dangerous Function"], "references": ["https://www.php.net/manual/en/intro.mcrypt.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/CryptoFunctionsSniff.php"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Dangerous Method or Function"], "source": "https://semgrep.dev/r/php.lang.security.mcrypt-use.mcrypt-use", "shortlink": "https://sg.run/BkZR"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.mcrypt-use.mcrypt-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Encryption.php", "start": {"line": 602, "col": 3, "offset": 15031}, "end": {"line": 602, "col": 54, "offset": 15082}, "extra": {"message": "Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL.", "metadata": {"cwe": ["CWE-676: Use of Potentially Dangerous Function"], "references": ["https://www.php.net/manual/en/intro.mcrypt.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/CryptoFunctionsSniff.php"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Dangerous Method or Function"], "source": "https://semgrep.dev/r/php.lang.security.mcrypt-use.mcrypt-use", "shortlink": "https://sg.run/BkZR"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.mcrypt-use.mcrypt-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Encryption.php", "start": {"line": 609, "col": 3, "offset": 15307}, "end": {"line": 609, "col": 44, "offset": 15348}, "extra": {"message": "Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL.", "metadata": {"cwe": ["CWE-676: Use of Potentially Dangerous Function"], "references": ["https://www.php.net/manual/en/intro.mcrypt.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/CryptoFunctionsSniff.php"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Dangerous Method or Function"], "source": "https://semgrep.dev/r/php.lang.security.mcrypt-use.mcrypt-use", "shortlink": "https://sg.run/BkZR"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.mcrypt-use.mcrypt-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Encryption.php", "start": {"line": 612, "col": 4, "offset": 15400}, "end": {"line": 612, "col": 43, "offset": 15439}, "extra": {"message": "Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL.", "metadata": {"cwe": ["CWE-676: Use of Potentially Dangerous Function"], "references": ["https://www.php.net/manual/en/intro.mcrypt.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/CryptoFunctionsSniff.php"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Dangerous Method or Function"], "source": "https://semgrep.dev/r/php.lang.security.mcrypt-use.mcrypt-use", "shortlink": "https://sg.run/BkZR"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.audit.openssl-decrypt-validate.openssl-decrypt-validate", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Encryption.php", "start": {"line": 639, "col": 3, "offset": 15919}, "end": {"line": 647, "col": 5, "offset": 16074}, "extra": {"message": "The function `openssl_decrypt` returns either a string of the decrypted data on success or `false` on failure. If the failure case is not handled, this could lead to undefined behavior in your application. Please handle the case where `openssl_decrypt` returns `false`.", "metadata": {"references": ["https://www.php.net/manual/en/function.openssl-decrypt.php"], "cwe": ["CWE-252: Unchecked Return Value"], "owasp": ["A02:2021 - Cryptographic Failures"], "technology": ["php", "openssl"], "category": "security", "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/php.lang.security.audit.openssl-decrypt-validate.openssl-decrypt-validate", "shortlink": "https://sg.run/kzn7"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.mcrypt-use.mcrypt-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Encryption.php", "start": {"line": 743, "col": 3, "offset": 18267}, "end": {"line": 743, "col": 52, "offset": 18316}, "extra": {"message": "Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL.", "metadata": {"cwe": ["CWE-676: Use of Potentially Dangerous Function"], "references": ["https://www.php.net/manual/en/intro.mcrypt.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/CryptoFunctionsSniff.php"], "category": "security", "technology": ["php"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Dangerous Method or Function"], "source": "https://semgrep.dev/r/php.lang.security.mcrypt-use.mcrypt-use", "shortlink": "https://sg.run/BkZR"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.ftp-use.ftp-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Ftp.php", "start": {"line": 179, "col": 4, "offset": 4025}, "end": {"line": 179, "col": 35, "offset": 4056}, "extra": {"message": "FTP allows for unencrypted file transfers. Consider using an encrypted alternative.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.php.net/manual/en/intro.ftp.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/FringeFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.ftp-use.ftp-use", "shortlink": "https://sg.run/RoYN"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.ftp-use.ftp-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Ftp.php", "start": {"line": 194, "col": 3, "offset": 4234}, "end": {"line": 194, "col": 70, "offset": 4301}, "extra": {"message": "FTP allows for unencrypted file transfers. Consider using an encrypted alternative.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.php.net/manual/en/intro.ftp.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/FringeFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.ftp-use.ftp-use", "shortlink": "https://sg.run/RoYN"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.ftp-use.ftp-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Ftp.php", "start": {"line": 241, "col": 3, "offset": 5288}, "end": {"line": 241, "col": 47, "offset": 5332}, "extra": {"message": "FTP allows for unencrypted file transfers. Consider using an encrypted alternative.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.php.net/manual/en/intro.ftp.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/FringeFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.ftp-use.ftp-use", "shortlink": "https://sg.run/RoYN"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.ftp-use.ftp-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Ftp.php", "start": {"line": 272, "col": 3, "offset": 5825}, "end": {"line": 272, "col": 47, "offset": 5869}, "extra": {"message": "FTP allows for unencrypted file transfers. Consider using an encrypted alternative.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.php.net/manual/en/intro.ftp.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/FringeFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.ftp-use.ftp-use", "shortlink": "https://sg.run/RoYN"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.ftp-use.ftp-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Ftp.php", "start": {"line": 327, "col": 3, "offset": 6877}, "end": {"line": 327, "col": 65, "offset": 6939}, "extra": {"message": "FTP allows for unencrypted file transfers. Consider using an encrypted alternative.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.php.net/manual/en/intro.ftp.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/FringeFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.ftp-use.ftp-use", "shortlink": "https://sg.run/RoYN"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.ftp-use.ftp-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Ftp.php", "start": {"line": 375, "col": 3, "offset": 7835}, "end": {"line": 375, "col": 65, "offset": 7897}, "extra": {"message": "FTP allows for unencrypted file transfers. Consider using an encrypted alternative.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.php.net/manual/en/intro.ftp.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/FringeFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.ftp-use.ftp-use", "shortlink": "https://sg.run/RoYN"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.ftp-use.ftp-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Ftp.php", "start": {"line": 407, "col": 3, "offset": 8386}, "end": {"line": 407, "col": 63, "offset": 8446}, "extra": {"message": "FTP allows for unencrypted file transfers. Consider using an encrypted alternative.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.php.net/manual/en/intro.ftp.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/FringeFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.ftp-use.ftp-use", "shortlink": "https://sg.run/RoYN"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.ftp-use.ftp-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Ftp.php", "start": {"line": 451, "col": 3, "offset": 9174}, "end": {"line": 451, "col": 52, "offset": 9223}, "extra": {"message": "FTP allows for unencrypted file transfers. Consider using an encrypted alternative.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.php.net/manual/en/intro.ftp.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/FringeFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.ftp-use.ftp-use", "shortlink": "https://sg.run/RoYN"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.ftp-use.ftp-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Ftp.php", "start": {"line": 551, "col": 3, "offset": 11098}, "end": {"line": 553, "col": 11, "offset": 11171}, "extra": {"message": "FTP allows for unencrypted file transfers. Consider using an encrypted alternative.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.php.net/manual/en/intro.ftp.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/FringeFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.ftp-use.ftp-use", "shortlink": "https://sg.run/RoYN"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.ftp-use.ftp-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Ftp.php", "start": {"line": 648, "col": 3, "offset": 13470}, "end": {"line": 650, "col": 11, "offset": 13537}, "extra": {"message": "FTP allows for unencrypted file transfers. Consider using an encrypted alternative.", "metadata": {"cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.php.net/manual/en/intro.ftp.php", "https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/FringeFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/php.lang.security.ftp-use.ftp-use", "shortlink": "https://sg.run/RoYN"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Image_lib.php", "start": {"line": 902, "col": 4, "offset": 20751}, "end": {"line": 902, "col": 34, "offset": 20781}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Image_lib.php", "start": {"line": 985, "col": 4, "offset": 22472}, "end": {"line": 985, "col": 34, "offset": 22502}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Image_lib.php", "start": {"line": 999, "col": 3, "offset": 22834}, "end": {"line": 999, "col": 42, "offset": 22873}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Session/drivers/Session_files_driver.php", "start": {"line": 329, "col": 12, "offset": 8783}, "end": {"line": 329, "col": 49, "offset": 8820}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Session/drivers/Session_files_driver.php", "start": {"line": 342, "col": 12, "offset": 9065}, "end": {"line": 342, "col": 49, "offset": 9102}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Session/drivers/Session_files_driver.php", "start": {"line": 393, "col": 4, "offset": 10384}, "end": {"line": 393, "col": 65, "offset": 10445}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Upload.php", "start": {"line": 1272, "col": 5, "offset": 29740}, "end": {"line": 1272, "col": 48, "offset": 29783}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Upload.php", "start": {"line": 1282, "col": 5, "offset": 30016}, "end": {"line": 1282, "col": 31, "offset": 30042}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.exec-use.exec-use", "path": "downloaded_repos/justitems_midrub_cms/system/libraries/Upload.php", "start": {"line": 1296, "col": 5, "offset": 30304}, "end": {"line": 1296, "col": 31, "offset": 30330}, "extra": {"message": "Executing non-constant commands. This can lead to command injection.", "metadata": {"cwe": ["CWE-94: Improper Control of Generation of Code ('Code Injection')"], "references": ["https://github.com/FloeDesignTechnologies/phpcs-security-audit/blob/master/Security/Sniffs/BadFunctions/SystemExecFunctionsSniff.php"], "category": "security", "technology": ["php"], "owasp": ["A03:2021 - Injection"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Code Injection"], "source": "https://semgrep.dev/r/php.lang.security.exec-use.exec-use", "shortlink": "https://sg.run/5Q1j"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/justitems_midrub_cms/.htaccess", "downloaded_repos/justitems_midrub_cms/.user.ini", "downloaded_repos/justitems_midrub_cms/README.md", "downloaded_repos/justitems_midrub_cms/application/base/admin/classes/content_category.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/classes/fields.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/classes/icons.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/classes/menu.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/classes/quick_guide.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/classes/settings.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/admin/controllers/ajax.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/admin/controllers/init.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/admin/helpers/invoices.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/admin/helpers/themes.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/admin/helpers/transactions.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/admin/inc/admin_hooks.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/admin/inc/admin_menu.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/admin/inc/admin_pages.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/admin/inc/invoices.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/admin/inc/themes.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/admin/inc/transactions.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/admin/language/english/admin_backup_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/admin/language/english/admin_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/admin/language/english/admin_menu_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/admin/language/index.html", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/admin/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/admin/views/invoice.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/admin/views/invoices/faq.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/admin/views/invoices/menu.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/admin/views/invoices/template.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/admin/views/invoices.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/admin/views/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/admin/views/menu.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/admin/views/modals/theme_installation.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/admin/views/themes.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/admin/views/themes_directory.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/admin/views/transaction.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/admin/views/transactions.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/dashboard/classes/widgets.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/dashboard/controllers/ajax.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/dashboard/controllers/init.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/dashboard/helpers/events.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/dashboard/helpers/widgets.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/dashboard/inc/dashboard_hooks.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/dashboard/inc/dashboard_init.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/dashboard/inc/dashboard_widgets.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/dashboard/inc/general.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/dashboard/inc/parts/dashboard_events.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/dashboard/inc/parts/dashboard_init.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/dashboard/language/english/dashboard_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/dashboard/language/english/dashboard_menu_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/dashboard/language/index.html", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/dashboard/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/dashboard/models/dashboard_model.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/dashboard/models/dashboard_users_model.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/dashboard/views/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/classes/frontend_pages.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/classes/frontend_settings.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/controllers/ajax.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/controllers/init.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/helpers/classification.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/helpers/contents.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/helpers/media.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/helpers/menu.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/helpers/multimedia.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/helpers/settings.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/helpers/social.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/helpers/themes.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/helpers/url.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/inc/contents_categories.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/inc/frontend_hooks.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/inc/frontend_init.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/inc/frontend_pages.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/inc/networks.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/inc/quick_guide.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/interfaces/networks.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/language/english/frontend_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/language/english/frontend_menu_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/language/english/frontend_quick_guide_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/language/english/frontend_social_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/language/index.html", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/networks/collection/facebook.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/networks/collection/twitter.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/views/auth_social.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/views/contents_list.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/views/editor.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/views/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/views/menu.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/views/menu_page.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/views/modals/auth_logo.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/views/modals/classification_manager.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/views/modals/components_selector.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/views/modals/frontend_logo.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/views/modals/installation.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/views/modals/network_installation.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/views/modals/new_item.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/views/modals/page_url.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/views/modals/templates_selector.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/views/modals/upload_media.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/views/network.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/views/networks_directory.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/views/page.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/views/settings/footer.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/views/settings/general.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/views/settings/header.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/views/settings/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/views/settings/menu.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/views/themes.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/frontend/views/themes_directory.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/members/classes/members.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/members/classes/members_general_tab.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/members/classes/members_tabs.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/members/controllers/ajax.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/members/controllers/init.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/members/helpers/countries.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/members/helpers/members.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/members/helpers/plans.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/members/helpers/transactions.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/members/inc/email_templates.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/members/inc/members_fields.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/members/inc/members_general.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/members/inc/members_hooks.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/members/inc/members_init.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/members/inc/members_init_hooks.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/members/inc/members_pages.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/members/language/english/members_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/members/language/index.html", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/members/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/members/views/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/members/views/management/members_edit.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/members/views/management/members_list.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/members/views/management/members_new.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/members/views/menu.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/members/views/no_data.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/classes/alerts.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/classes/email_templates.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/classes/notifications_pages.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/controllers/ajax.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/controllers/init.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/helpers/emails.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/helpers/plans.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/helpers/system_errors.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/helpers/users.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/helpers/users_alerts.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/inc/notifications_alert.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/inc/notifications_hooks.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/inc/notifications_init.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/inc/notifications_init_hooks.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/inc/notifications_pages.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/inc/notifications_template.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/language/english/notifications_alerts_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/language/english/notifications_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/language/index.html", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/models/notifications_alerts_model.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/models/notifications_model.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/views/email_templates/template.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/views/email_templates/templates_list.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/views/email_templates/templates_new.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/views/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/views/menu.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/views/no_data.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/views/system_errors/alert.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/views/system_errors/errors_list.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/views/users_alerts/alert.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/views/users_alerts/alerts_list.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/notifications/views/users_alerts/alerts_new.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/plugins/classes/plugins_pages.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/plugins/classes/plugins_plugin_pages.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/plugins/controllers/ajax.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/plugins/controllers/init.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/plugins/helpers/plugins.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/plugins/inc/plugins.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/plugins/inc/plugins_hooks.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/plugins/inc/plugins_init.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/plugins/inc/plugins_init_hooks.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/plugins/inc/plugins_pages.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/plugins/language/english/plugins_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/plugins/language/english/plugins_menu_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/plugins/language/index.html", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/plugins/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/plugins/views/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/plugins/views/menu.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/plugins/views/modals/plugin_installation.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/plugins/views/plugin.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/plugins/views/plugins/list.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/plugins/views/plugins/plugins_directory.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/profile/classes/plugins_pages.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/profile/classes/plugins_plugin_pages.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/profile/controllers/ajax.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/profile/controllers/init.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/profile/helpers/media.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/profile/helpers/profile.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/profile/inc/general.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/profile/inc/profile_hooks.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/profile/inc/quick_guide.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/profile/language/english/profile_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/profile/language/english/profile_menu_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/profile/language/english/profile_quick_guide_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/profile/language/index.html", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/profile/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/profile/views/modals/image.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/profile/views/parts/menu.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/profile/views/templates/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/profile/views/templates/preferences.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/profile/views/templates/security.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/settings/classes/storage_locations.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/settings/controllers/ajax.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/settings/controllers/init.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/settings/functions.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/settings/helpers/coupons.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/settings/helpers/oauth.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/settings/helpers/referrals.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/settings/helpers/settings.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/settings/helpers/storage.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/settings/inc/settings_hooks.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/settings/inc/storage_init.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/settings/inc/storage_init_hooks.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/settings/language/english/settings_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/settings/language/english/settings_menu_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/settings/language/index.html", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/settings/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/settings/models/oauth_applications_model.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/settings/models/oauth_permissions_model.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/settings/views/affiliates_reports.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/settings/views/affiliates_settings.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/settings/views/apps.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/settings/views/coupon_codes.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/settings/views/gateway.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/settings/views/gateways.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/settings/views/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/settings/views/menu.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/settings/views/modals/new_application.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/settings/views/modals/update_application.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/settings/views/permissions.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/settings/views/smtp.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/settings/views/storage.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/support/controllers/ajax.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/support/controllers/init.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/support/helpers/categories.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/support/helpers/faq.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/support/helpers/tickets.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/support/inc/articles.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/support/inc/categories.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/support/inc/email_templates.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/support/inc/support_pages.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/support/inc/tickets.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/support/language/english/support_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/support/language/index.html", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/support/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/support/models/faq_model.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/support/models/tickets_model.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/support/views/article.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/support/views/articles.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/support/views/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/support/views/menu.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/support/views/modals/categories_manager.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/support/views/ticket.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/support/views/tickets.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/controllers/ajax.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/controllers/init.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/helpers/apps.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/helpers/frontend_themes.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/helpers/midrub.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/helpers/plugins.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/helpers/user_components.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/language/english/updates_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/language/index.html", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/models/updates_model.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/views/apps.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/views/footer.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/views/frontend_themes.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/views/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/views/menu.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/views/plugins.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/updates/views/user_components.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/controllers/ajax.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/controllers/init.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/helpers/apps.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/helpers/components.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/helpers/media.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/helpers/menu.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/helpers/plans.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/helpers/plans_groups.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/helpers/settings.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/helpers/social.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/helpers/themes.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/inc/apps.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/inc/components.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/inc/general_plans_options.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/inc/networks.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/inc/user_hooks.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/inc/user_pages.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/language/english/user_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/language/english/user_menu_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/language/index.html", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/models/user_plans_groups_model.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/views/app.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/views/apps.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/views/apps_directory.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/views/component.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/views/components.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/views/components_directory.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/views/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/views/menu.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/views/menu_page.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/views/modals/app_installation.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/views/modals/component_installation.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/views/modals/manage_plan_text.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/views/modals/network_installation.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/views/modals/new_item.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/views/modals/new_plan.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/views/modals/theme_installation.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/views/modals/user_logo.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/views/network.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/views/networks.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/views/networks_directory.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/views/plan.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/views/plans.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/views/settings/footer.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/views/settings/general.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/views/settings/header.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/views/settings/menu.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/views/themes.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/components/collection/user/views/themes_directory.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/fields/collection/checkbox/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/fields/collection/dropdown/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/fields/collection/dynamic_list/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/fields/collection/dynamic_list_multiselector/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/fields/collection/image/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/fields/collection/info/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/fields/collection/password/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/fields/collection/plan_text/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/fields/collection/text/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/fields/collection/textarea/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/fields/collection/update/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/fields/collection/upload/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/add_circle.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/admin.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/advanced.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/affiliates.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/api.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/api_add.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/app_add.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/appearance.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/apps.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/arrow_down.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/arrow_left_line.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/arrow_ltr.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/arrow_rtl.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/automations.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/bot.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/browse.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/calendar.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/cancel.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/chat.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/chat_small.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/clock.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/close.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/companies.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/component_add.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/components.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/coupons.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/cron_job.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/css.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/dashboard.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/delete.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/disable.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/door.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/email_template.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/email_templates.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/enable.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/error.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/facebook.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/faq.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/features.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/fields.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/filter.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/footer.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/frontend.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/gateway.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/general.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/globe.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/grid.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/groups_accounts.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/header.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/home.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/image.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/image_edit.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/images_search.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/info.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/instagram.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/install.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/integrations.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/invoices.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/ios_arrow_left.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/ios_arrow_right.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/jobs.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/js.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/language.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/law.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/link.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/list.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/mail_template.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/media.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/member_add.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/member_money.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/members.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/members_small.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/menu.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/more.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/my_profile.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/my_security.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/my_settings.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/network.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/network_add.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/networks.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/new_page.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/new_plan.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/new_theme.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/news.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/notification_add.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/notifications.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/pages.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/password.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/payments.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/person.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/person_advanced.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/person_info.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/person_photo.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/placeholders.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/plans.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/plugins.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/plugins_add.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/plugins_small.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/plus.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/posts.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/print.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/print_small.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/question.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/quick_guide.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/reply.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/review.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/save.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/search.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/send.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/settings.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/settings_small.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/share.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/sign_out.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/signup.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/sms.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/smtp.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/snooze.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/social.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/stats.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/storage.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/support.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/support_articles.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/sync.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/system.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/system_errors.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/telegram.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/tickets.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/time.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/transactions.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/twitter.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/update.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/upload.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/user.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/user_settings.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/users.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/video.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/warning.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/watermark.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/icons/collection/whatsapp.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/inc/general.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/inc/init.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/interfaces/components.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/interfaces/fields.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/language/english/base_admin_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/language/index.html", "downloaded_repos/justitems_midrub_cms/application/base/admin/main.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/themes/collection/default/config.json", "downloaded_repos/justitems_midrub_cms/application/base/admin/themes/collection/default/core/inc/general.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/themes/collection/default/language/english/admin_default_theme_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/admin/themes/collection/default/language/index.html", "downloaded_repos/justitems_midrub_cms/application/base/admin/themes/collection/default/main.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/classes/change/change.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/classes/reset/reset.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/classes/signin/signin.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/classes/signup/signup.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/change_password/controllers/ajax.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/change_password/controllers/init.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/change_password/helpers/user.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/change_password/inc/contents_categories.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/change_password/language/english/admin_change_password_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/change_password/language/english/auth_change_password_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/change_password/language/index.html", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/change_password/main.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/change_password/views/layout/footer.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/change_password/views/layout/header.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/change_password/views/layout/index.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/change_password/views/main.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/confirmation/controllers/ajax.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/confirmation/controllers/init.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/confirmation/helpers/user.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/confirmation/inc/contents_categories.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/confirmation/language/english/admin_confirmation_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/confirmation/language/english/auth_confirmation_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/confirmation/language/index.html", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/confirmation/main.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/confirmation/views/layout/footer.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/confirmation/views/layout/header.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/confirmation/views/layout/index.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/confirmation/views/main.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/page/controllers/ajax.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/page/controllers/init.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/page/language/english/admin_page_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/page/language/english/auth_page_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/page/language/index.html", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/page/main.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/page/views/layout/footer.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/page/views/layout/header.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/page/views/layout/index.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/page/views/main.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/reset/controllers/ajax.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/reset/controllers/init.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/reset/helpers/user.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/reset/inc/contents_categories.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/reset/inc/email_templates.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/reset/language/english/admin_reset_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/reset/language/english/auth_reset_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/reset/language/index.html", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/reset/main.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/reset/views/layout/footer.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/reset/views/layout/header.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/reset/views/layout/index.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/reset/views/main.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/signin/controllers/ajax.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/signin/controllers/init.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/signin/controllers/social.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/signin/helpers/user.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/signin/inc/contents_categories.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/signin/language/english/admin_signin_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/signin/language/english/auth_signin_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/signin/language/index.html", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/signin/main.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/signin/views/layout/footer.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/signin/views/layout/header.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/signin/views/layout/index.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/signin/views/main.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/signup/controllers/ajax.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/signup/controllers/init.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/signup/controllers/social.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/signup/helpers/user.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/signup/inc/contents_categories.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/signup/inc/email_templates.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/signup/language/english/admin_signup_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/signup/language/english/auth_signup_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/signup/language/index.html", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/signup/main.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/signup/views/layout/footer.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/signup/views/layout/header.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/signup/views/layout/index.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/signup/views/main.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/upgrade/controllers/ajax.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/upgrade/controllers/init.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/upgrade/helpers/code.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/upgrade/inc/contents_categories.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/upgrade/inc/general.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/upgrade/language/english/auth_upgrade_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/upgrade/language/index.html", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/upgrade/main.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/upgrade/views/coupon-code.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/upgrade/views/gateways.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/upgrade/views/layout/footer.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/upgrade/views/layout/header.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/upgrade/views/layout/index.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/collection/upgrade/views/main.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/inc/change/change.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/inc/contents/contents.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/inc/general.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/inc/reset/reset.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/inc/signin/signin.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/inc/signup/signup.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/interfaces/auth.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/interfaces/social.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/language/english/auth_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/auth/language/index.html", "downloaded_repos/justitems_midrub_cms/application/base/auth/main.php", "downloaded_repos/justitems_midrub_cms/application/base/autoload.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/apps/admin_options.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/apps/admin_options_templates.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/components/admin_options.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/components/admin_options_templates.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/components/user_options.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/contents/admin_contents_meta_templates.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/contents/admin_contents_option_templates.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/contents/contents_categories.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/contents/contents_read.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/email/send.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/hooks.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/media/delete.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/media/download.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/media/thumbnails.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/media/upload.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/menu/create_menu.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/options.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/pages/admin.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/pages/admin_pages.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/pages/admin_support.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/pages/admin_user.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/pages/administrator/members_pages.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/pages/main.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/payments/gateways.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/payments/gateways_options_templates.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/plans/options.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/plans/read.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/properties.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/rest/permissions.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/rss/helpers/clean.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/rss/options/atom.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/rss/options/rss.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/rss/read.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/team/member.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/team/permissions.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/team/permissions_templates.php", "downloaded_repos/justitems_midrub_cms/application/base/classes/time/timezone.php", "downloaded_repos/justitems_midrub_cms/application/base/frontend/classes/breadcrumb.php", "downloaded_repos/justitems_midrub_cms/application/base/frontend/classes/contents.php", "downloaded_repos/justitems_midrub_cms/application/base/frontend/classes/datetime.php", "downloaded_repos/justitems_midrub_cms/application/base/frontend/classes/menu.php", "downloaded_repos/justitems_midrub_cms/application/base/frontend/classes/pagination.php", "downloaded_repos/justitems_midrub_cms/application/base/frontend/classes/replacers.php", "downloaded_repos/justitems_midrub_cms/application/base/frontend/inc/classifications.php", "downloaded_repos/justitems_midrub_cms/application/base/frontend/inc/general.php", "downloaded_repos/justitems_midrub_cms/application/base/frontend/inc/menu.php", "downloaded_repos/justitems_midrub_cms/application/base/frontend/language/english/frontend_default_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/frontend/language/index.html", "downloaded_repos/justitems_midrub_cms/application/base/frontend/main.php", "downloaded_repos/justitems_midrub_cms/application/base/inc/additional.php", "downloaded_repos/justitems_midrub_cms/application/base/inc/apps/admin_options.php", "downloaded_repos/justitems_midrub_cms/application/base/inc/auth/components.php", "downloaded_repos/justitems_midrub_cms/application/base/inc/auth/general.php", "downloaded_repos/justitems_midrub_cms/application/base/inc/components/admin_options.php", "downloaded_repos/justitems_midrub_cms/application/base/inc/components/user_options.php", "downloaded_repos/justitems_midrub_cms/application/base/inc/contents/contents_categories.php", "downloaded_repos/justitems_midrub_cms/application/base/inc/contents/contents_read.php", "downloaded_repos/justitems_midrub_cms/application/base/inc/curl/delete.php", "downloaded_repos/justitems_midrub_cms/application/base/inc/curl/get.php", "downloaded_repos/justitems_midrub_cms/application/base/inc/curl/post.php", "downloaded_repos/justitems_midrub_cms/application/base/inc/general/time.php", "downloaded_repos/justitems_midrub_cms/application/base/inc/general.php", "downloaded_repos/justitems_midrub_cms/application/base/inc/media/save.php", "downloaded_repos/justitems_midrub_cms/application/base/inc/menu/create_menu.php", "downloaded_repos/justitems_midrub_cms/application/base/inc/menu/read_menu.php", "downloaded_repos/justitems_midrub_cms/application/base/inc/pages/administrator/admin_pages.php", "downloaded_repos/justitems_midrub_cms/application/base/inc/pages/administrator/members_pages.php", "downloaded_repos/justitems_midrub_cms/application/base/inc/pages/administrator/support_pages.php", "downloaded_repos/justitems_midrub_cms/application/base/inc/pages/administrator/user_pages.php", "downloaded_repos/justitems_midrub_cms/application/base/inc/payments/gateways.php", "downloaded_repos/justitems_midrub_cms/application/base/inc/plans/options.php", "downloaded_repos/justitems_midrub_cms/application/base/inc/rest/api_permissions.php", "downloaded_repos/justitems_midrub_cms/application/base/inc/team/permissions.php", "downloaded_repos/justitems_midrub_cms/application/base/inc/themes/frontend.php", "downloaded_repos/justitems_midrub_cms/application/base/inc/themes/user.php", "downloaded_repos/justitems_midrub_cms/application/base/inc/translation/general.php", "downloaded_repos/justitems_midrub_cms/application/base/inc/user/general.php", "downloaded_repos/justitems_midrub_cms/application/base/install/inc/general.php", "downloaded_repos/justitems_midrub_cms/application/base/install/main.php", "downloaded_repos/justitems_midrub_cms/application/base/install/temp/autoload.php", "downloaded_repos/justitems_midrub_cms/application/base/install/temp/config.php", "downloaded_repos/justitems_midrub_cms/application/base/install/temp/database.php", "downloaded_repos/justitems_midrub_cms/application/base/install/templates/config.txt", "downloaded_repos/justitems_midrub_cms/application/base/install/templates/database.txt", "downloaded_repos/justitems_midrub_cms/application/base/install/templates/midrub.sql", "downloaded_repos/justitems_midrub_cms/application/base/install/views/database.php", "downloaded_repos/justitems_midrub_cms/application/base/install/views/error.php", "downloaded_repos/justitems_midrub_cms/application/base/install/views/finish.php", "downloaded_repos/justitems_midrub_cms/application/base/install/views/information.php", "downloaded_repos/justitems_midrub_cms/application/base/install/views/install.php", "downloaded_repos/justitems_midrub_cms/application/base/interfaces/plugins.php", "downloaded_repos/justitems_midrub_cms/application/base/language/english/medias_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/language/english/rss_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/language/index.html", "downloaded_repos/justitems_midrub_cms/application/base/main.php", "downloaded_repos/justitems_midrub_cms/application/base/models/base_auth_social.php", "downloaded_repos/justitems_midrub_cms/application/base/models/base_classifications.php", "downloaded_repos/justitems_midrub_cms/application/base/models/base_codes.php", "downloaded_repos/justitems_midrub_cms/application/base/models/base_contents.php", "downloaded_repos/justitems_midrub_cms/application/base/models/base_invoices.php", "downloaded_repos/justitems_midrub_cms/application/base/models/base_model.php", "downloaded_repos/justitems_midrub_cms/application/base/models/base_plans.php", "downloaded_repos/justitems_midrub_cms/application/base/models/base_referrals.php", "downloaded_repos/justitems_midrub_cms/application/base/models/base_rest.php", "downloaded_repos/justitems_midrub_cms/application/base/models/base_teams.php", "downloaded_repos/justitems_midrub_cms/application/base/models/base_transactions.php", "downloaded_repos/justitems_midrub_cms/application/base/models/base_users.php", "downloaded_repos/justitems_midrub_cms/application/base/models/parts/plans/base_plans_update.php", "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/composer.json", "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/composer.lock", "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/controllers/ajax.php", "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/controllers/user.php", "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/helpers/process.php", "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/inc/admin.php", "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/language/english/braintree_admin_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/language/english/braintree_user_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/language/index.html", "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/main.php", "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/views/error.php", "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/views/expired.php", "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/views/main.php", "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/paypal/controllers/ajax.php", "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/paypal/controllers/user.php", "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/paypal/helpers/process.php", "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/paypal/inc/admin.php", "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/paypal/language/english/paypal_admin_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/paypal/language/english/paypal_user_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/paypal/language/index.html", "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/paypal/main.php", "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/paypal/views/error.php", "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/paypal/views/expired.php", "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/paypal/views/main.php", "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/composer.json", "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/composer.lock", "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/controllers/user.php", "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/inc/admin.php", "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/language/english/stripe_admin_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/language/english/stripe_user_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/language/index.html", "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/main.php", "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/views/expired.php", "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/views/main.php", "downloaded_repos/justitems_midrub_cms/application/base/payments/inc/general.php", "downloaded_repos/justitems_midrub_cms/application/base/payments/interfaces/payments.php", "downloaded_repos/justitems_midrub_cms/application/base/payments/main.php", "downloaded_repos/justitems_midrub_cms/application/base/payments/themes/default/main.php", "downloaded_repos/justitems_midrub_cms/application/base/plugins/main.php", "downloaded_repos/justitems_midrub_cms/application/base/rest/classes/app_rest.php", "downloaded_repos/justitems_midrub_cms/application/base/rest/classes/authorize.php", "downloaded_repos/justitems_midrub_cms/application/base/rest/classes/helper.php", "downloaded_repos/justitems_midrub_cms/application/base/rest/classes/security.php", "downloaded_repos/justitems_midrub_cms/application/base/rest/classes/token.php", "downloaded_repos/justitems_midrub_cms/application/base/rest/inc/general.php", "downloaded_repos/justitems_midrub_cms/application/base/rest/language/english/base_rest_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/rest/language/index.html", "downloaded_repos/justitems_midrub_cms/application/base/rest/main.php", "downloaded_repos/justitems_midrub_cms/application/base/user/classes/icons.php", "downloaded_repos/justitems_midrub_cms/application/base/user/default/php/list_accounts.php", "downloaded_repos/justitems_midrub_cms/application/base/user/default/php/modals/time_picker.php", "downloaded_repos/justitems_midrub_cms/application/base/user/default/php/network_connect.php", "downloaded_repos/justitems_midrub_cms/application/base/user/default/php/network_error.php", "downloaded_repos/justitems_midrub_cms/application/base/user/default/php/network_success.php", "downloaded_repos/justitems_midrub_cms/application/base/user/default/php/no_page_found.php", "downloaded_repos/justitems_midrub_cms/application/base/user/default/php/plan_feature_end.php", "downloaded_repos/justitems_midrub_cms/application/base/user/default/php/quick_guide.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/account.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/accounts.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/add.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/add_box.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/add_circle_outline.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/add_comment.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/add_link.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/add_road.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/add_shopping_cart.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/airplay.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/alert.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/align_bottom.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/align_center.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/align_left.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/align_right.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/align_top.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/alternate_email.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/arrow_down.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/arrow_down_line.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/arrow_left.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/arrow_left_circle.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/arrow_left_line.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/arrow_right.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/arrow_right_circle.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/arrow_right_line.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/arrow_up.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/arrow_up_line.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/article.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/article_material.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/assignment_turned_in.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/asterisk.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/attachment.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/attachment_line.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bar_chart.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bar_chart_box.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bar_chart_fill.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bar_chart_horizontal.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_alarm.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_align_bottom.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_align_top.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_arrow_down.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_arrow_left_circle.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_arrow_left_short.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_arrow_right_circle.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_arrow_right_short.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_arrow_up.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_calendar.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_calendar2_check.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_calendar2_event.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_calendar2_minus.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_calendar2_plus.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_calendar4_week.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_check2_circle.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_check_circle.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_chevron_left.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_chevron_right.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_clipboard.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_clock.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_columns_gap.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_event.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_file.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_file_arrow_up.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_filetype_pdf.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_fonts.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_geo.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_grip_vertical.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_hash.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_hashtag.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_hdd_stack.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_image.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_image_alt.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_infinity.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_journal_text.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_journals.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_layout.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_link.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_link_default.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_pencil.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_pin_map.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_planifications.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_planner.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_play_btn.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_plus.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_plus_circle.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_post.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_reply.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_search.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_share.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_tasks.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_textarea_x.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_tiktok.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_video.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_window.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_x.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bi_x_circle.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bold.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/booklet_line.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bookmark.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bookmark_add.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/breakers.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bubble_chart.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/bulk_upload.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/business.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/calendar.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/calendar_line.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/cancel.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/center_focus_weak.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/chart.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/chart_line.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/chat.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/chat_1.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/chat_3.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/chat_4.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/chat_deleted.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/chat_favorites.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/chat_forward.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/chat_history.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/chat_line.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/chat_new.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/chat_seen.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/checkbox.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/checkbox_blank.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/checkbox_circle.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/checkbox_multiple.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/checked.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/child.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/close.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/close_circle.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/close_fill.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/close_line.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/code.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/collections.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/comment.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/comments.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/company.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/conversations.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/dashboard.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/dashboard_line.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/delete.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/delete_bin.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/device_hub.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/dollar.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/door.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/download.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/download_cloud.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/drag_indicator.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/drag_move_fill.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/drawer.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/edit.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/edit_box.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/edit_line.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/email.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/emotion_sad.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/error.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/event_seat.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/export.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/eye.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/fa_align_center.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/fa_align_justify.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/fa_align_left.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/fa_align_right.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/fa_attachment.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/fa_bold.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/fa_italic.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/fa_link.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/fa_list.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/fa_numeric_list.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/fa_underline.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/fa_unlink.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/facebook.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/facebook_white.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/file.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/file_add_line.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/file_cloud.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/file_copy.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/file_list.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/file_reduce_line.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/flow_chart.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/folder.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/folder_add.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/folder_chart_2.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/folder_keyhole.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/folder_line.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/folder_open.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/folder_reduce.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/folder_special.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/folder_transfer.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/folders.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/follow.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/forbid.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/forum.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/funds.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/git_commit_line.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/google.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/grid_fill.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/group_add.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/group_line.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/hard_drive.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/hashtag.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/heart.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/highlight.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/history.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/home.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/home_line.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/home_smile.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/image.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/image_add.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/image_edit.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/image_fill.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/image_material.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/import.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/inbox.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/inbox_archive_line.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/information.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/input_method.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/insert_link.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/insights.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/instagram.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/keyboard.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/keyboard_alt.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/keyboard_tab.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/keywords.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/link.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/linkedin.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/list_check.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/loader.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/lock.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/login_circle.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/mail.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/mail_add.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/mail_check.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/mail_send.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/mail_settings.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/mail_star.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/manage.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/map.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/media.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/menu_add.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/messages.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/minus.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/moderate.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/monitor_heart.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/more.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/movie.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/name.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/networks.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/news.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/newspaper.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/node_tree.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/notification.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/offline_bolt.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/password.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/percent_line.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/perm_phone_msg.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/phone.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/phone_find.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/phonelink_ring.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/photo_resize.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/pie_chart_box_line.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/pinterest.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/planification.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/plans copy.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/plans.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/play.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/play_list.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/playlist_add.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/plus.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/posts.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/preview.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/product.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/public.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/query_stats.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/question_answer.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/queue.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/queue_play_next.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/read_more.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/received.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/refresh.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/repeat.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/repeat_fill.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/reply.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/reply_all.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/reserved.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/restart.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/robot.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/route.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/rss.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/schedule.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/scheduled.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/search.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/search_2.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/security.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/select.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/send.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/send_line.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/send_message.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/send_plane.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/send_plane_line.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/sent.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/settings.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/settings_2.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/settings_line.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/settings_phone.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/share.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/shortcut.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/slideshow.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/smart_toy.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/smartphone.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/sort_asc.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/sort_desc.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/sound_module.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/space_dashboard.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/spellcheck.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/stack.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/star.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/support_agent.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/swap_box.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/team_line.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/telegram.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/terminal.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/text_wrap.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/thumb_down.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/thumb_up.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/time.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/timer.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/toggle.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/tools.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/trash.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/trending_up.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/tumblr.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/twitter.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/unfollow.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/unread_message.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/update.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/upload.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/upload_2.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/upload_cloud.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/usage.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/user.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/user_2_line.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/user_3_line.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/user_add.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/user_fill.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/user_heart.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/user_line.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/user_search.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/user_shared_line.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/video.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/wallet.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/watermark.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/wechat.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/whatsapp.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/window.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/youtube.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/zoom_in.php", "downloaded_repos/justitems_midrub_cms/application/base/user/icons/collection/zoom_out.php", "downloaded_repos/justitems_midrub_cms/application/base/user/inc/general.php", "downloaded_repos/justitems_midrub_cms/application/base/user/inc/hooks.php", "downloaded_repos/justitems_midrub_cms/application/base/user/inc/parts/options/general.php", "downloaded_repos/justitems_midrub_cms/application/base/user/inc/parts/options/media.php", "downloaded_repos/justitems_midrub_cms/application/base/user/inc/parts/options/update.php", "downloaded_repos/justitems_midrub_cms/application/base/user/inc/team.php", "downloaded_repos/justitems_midrub_cms/application/base/user/interfaces/apps.php", "downloaded_repos/justitems_midrub_cms/application/base/user/interfaces/components.php", "downloaded_repos/justitems_midrub_cms/application/base/user/interfaces/networks.php", "downloaded_repos/justitems_midrub_cms/application/base/user/language/english/user_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/user/language/english/user_networks_lang.php", "downloaded_repos/justitems_midrub_cms/application/base/user/language/index.html", "downloaded_repos/justitems_midrub_cms/application/base/user/main.php", "downloaded_repos/justitems_midrub_cms/application/base/user/networks/collection/blogger.php", "downloaded_repos/justitems_midrub_cms/application/base/user/networks/collection/facebook_groups.php", "downloaded_repos/justitems_midrub_cms/application/base/user/networks/collection/facebook_pages.php", "downloaded_repos/justitems_midrub_cms/application/base/user/networks/collection/google_my_business.php", "downloaded_repos/justitems_midrub_cms/application/base/user/networks/collection/imgur.php", "downloaded_repos/justitems_midrub_cms/application/base/user/networks/collection/instagram.php", "downloaded_repos/justitems_midrub_cms/application/base/user/networks/collection/linkedin.php", "downloaded_repos/justitems_midrub_cms/application/base/user/networks/collection/linkedin_companies.php", "downloaded_repos/justitems_midrub_cms/application/base/user/networks/collection/medium.php", "downloaded_repos/justitems_midrub_cms/application/base/user/networks/collection/ok.php", "downloaded_repos/justitems_midrub_cms/application/base/user/networks/collection/pinterest.php", "downloaded_repos/justitems_midrub_cms/application/base/user/networks/collection/reddit.php", "downloaded_repos/justitems_midrub_cms/application/base/user/networks/collection/telegram_channels.php", "downloaded_repos/justitems_midrub_cms/application/base/user/networks/collection/telegram_groups.php", "downloaded_repos/justitems_midrub_cms/application/base/user/networks/collection/tiktok.php", "downloaded_repos/justitems_midrub_cms/application/base/user/networks/collection/tumblr.php", "downloaded_repos/justitems_midrub_cms/application/base/user/networks/collection/twilio.php", "downloaded_repos/justitems_midrub_cms/application/base/user/networks/collection/twilio_numbers.php", "downloaded_repos/justitems_midrub_cms/application/base/user/networks/collection/twitter.php", "downloaded_repos/justitems_midrub_cms/application/base/user/networks/collection/viber.php", "downloaded_repos/justitems_midrub_cms/application/base/user/networks/collection/vk.php", "downloaded_repos/justitems_midrub_cms/application/base/user/networks/collection/vk_communities.php", "downloaded_repos/justitems_midrub_cms/application/base/user/networks/collection/wordpress.php", "downloaded_repos/justitems_midrub_cms/application/base/user/networks/collection/youtube.php", "downloaded_repos/justitems_midrub_cms/application/config/autoload.php", "downloaded_repos/justitems_midrub_cms/application/config/config.php", "downloaded_repos/justitems_midrub_cms/application/config/constants.php", "downloaded_repos/justitems_midrub_cms/application/config/doctypes.php", "downloaded_repos/justitems_midrub_cms/application/config/email.php", "downloaded_repos/justitems_midrub_cms/application/config/foreign_chars.php", "downloaded_repos/justitems_midrub_cms/application/config/hooks.php", "downloaded_repos/justitems_midrub_cms/application/config/migration.php", "downloaded_repos/justitems_midrub_cms/application/config/mimes.php", "downloaded_repos/justitems_midrub_cms/application/config/profiler.php", "downloaded_repos/justitems_midrub_cms/application/config/routes.php", "downloaded_repos/justitems_midrub_cms/application/config/smileys.php", "downloaded_repos/justitems_midrub_cms/application/config/user_agents.php", "downloaded_repos/justitems_midrub_cms/application/controllers/Base.php", "downloaded_repos/justitems_midrub_cms/application/controllers/Coupons.php", "downloaded_repos/justitems_midrub_cms/application/controllers/Cron.php", "downloaded_repos/justitems_midrub_cms/application/controllers/Error_page.php", "downloaded_repos/justitems_midrub_cms/application/controllers/Networks.php", "downloaded_repos/justitems_midrub_cms/application/core/MY_Loader.php", "downloaded_repos/justitems_midrub_cms/application/core/MY_lang.php", "downloaded_repos/justitems_midrub_cms/application/hooks/index.html", "downloaded_repos/justitems_midrub_cms/application/language/english/default_errors_lang.php", "downloaded_repos/justitems_midrub_cms/application/language/index.html", "downloaded_repos/justitems_midrub_cms/application/logs/index.html", "downloaded_repos/justitems_midrub_cms/application/views/errors/cli/error_404.php", "downloaded_repos/justitems_midrub_cms/application/views/errors/cli/error_db.php", "downloaded_repos/justitems_midrub_cms/application/views/errors/cli/error_exception.php", "downloaded_repos/justitems_midrub_cms/application/views/errors/cli/error_general.php", "downloaded_repos/justitems_midrub_cms/application/views/errors/cli/error_php.php", "downloaded_repos/justitems_midrub_cms/application/views/errors/cli/index.html", "downloaded_repos/justitems_midrub_cms/application/views/errors/html/coupon_code.php", "downloaded_repos/justitems_midrub_cms/application/views/errors/html/error_404.php", "downloaded_repos/justitems_midrub_cms/application/views/errors/html/error_db.php", "downloaded_repos/justitems_midrub_cms/application/views/errors/html/error_exception.php", "downloaded_repos/justitems_midrub_cms/application/views/errors/html/error_general.php", "downloaded_repos/justitems_midrub_cms/application/views/errors/html/error_php.php", "downloaded_repos/justitems_midrub_cms/application/views/errors/html/gateways.php", "downloaded_repos/justitems_midrub_cms/application/views/errors/html/index.html", "downloaded_repos/justitems_midrub_cms/application/views/errors/html/maintenance.php", "downloaded_repos/justitems_midrub_cms/application/views/errors/html/no_user_theme.php", "downloaded_repos/justitems_midrub_cms/application/views/errors/html/subscription_expired.php", "downloaded_repos/justitems_midrub_cms/application/views/errors/index.html", "downloaded_repos/justitems_midrub_cms/application/views/oauth/authorize.php", "downloaded_repos/justitems_midrub_cms/assets/.htaccess", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/admin/js/invoices-faq.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/admin/js/invoices-settings.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/admin/js/invoices.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/admin/js/themes.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/admin/js/transactions.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/admin/styles/css/styles.css", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/admin/styles/css/styles.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/admin/styles/css/themes.css", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/admin/styles/css/themes.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/admin/styles/css/transactions.css", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/admin/styles/css/transactions.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/admin/styles/sass/styles.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/admin/styles/sass/themes.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/admin/styles/sass/transactions.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/dashboard/js/main.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/dashboard/js/members.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/dashboard/styles/css/styles.css", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/dashboard/styles/css/styles.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/dashboard/styles/css/tickets.css", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/dashboard/styles/css/tickets.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/dashboard/styles/css/transactions.css", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/dashboard/styles/css/transactions.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/dashboard/styles/sass/styles.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/dashboard/styles/sass/tickets.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/dashboard/styles/sass/transactions.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/frontend/js/contents-list.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/frontend/js/editor.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/frontend/js/menu.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/frontend/js/settings.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/frontend/js/social.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/frontend/js/themes.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/frontend/styles/css/menu.css", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/frontend/styles/css/menu.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/frontend/styles/css/multimedia.css", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/frontend/styles/css/multimedia.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/frontend/styles/css/settings.css", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/frontend/styles/css/settings.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/frontend/styles/css/social.css", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/frontend/styles/css/social.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/frontend/styles/css/styles.css", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/frontend/styles/css/styles.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/frontend/styles/css/themes.css", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/frontend/styles/css/themes.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/frontend/styles/sass/menu.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/frontend/styles/sass/settings.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/frontend/styles/sass/social.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/frontend/styles/sass/styles.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/frontend/styles/sass/themes.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/members/js/all-members.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/members/js/tab-transactions.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/members/styles/css/styles.css", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/members/styles/css/styles.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/members/styles/css/tab-transactions.css", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/members/styles/css/tab-transactions.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/members/styles/sass/styles.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/members/styles/sass/tab-transactions.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/notifications/js/email-templates.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/notifications/js/errors-alerts.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/notifications/js/users-alerts.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/notifications/styles/css/email-templates.css", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/notifications/styles/css/email-templates.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/notifications/styles/css/styles.css", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/notifications/styles/css/styles.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/notifications/styles/css/system-errors.css", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/notifications/styles/css/system-errors.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/notifications/styles/css/users-alerts.css", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/notifications/styles/css/users-alerts.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/notifications/styles/sass/email-templates.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/notifications/styles/sass/styles.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/notifications/styles/sass/system-errors.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/notifications/styles/sass/users-alerts.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/plugins/js/plugins.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/plugins/styles/css/styles.css", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/plugins/styles/css/styles.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/plugins/styles/sass/styles.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/profile/js/main.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/profile/js/preferences.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/profile/js/security.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/profile/styles/css/main.css", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/profile/styles/css/main.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/profile/styles/sass/main.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/settings/js/api.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/settings/js/coupon-codes.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/settings/js/main.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/settings/js/referrals.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/settings/js/settings.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/settings/js/storage.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/settings/styles/css/settings.css", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/settings/styles/css/settings.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/settings/styles/sass/settings.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/support/js/main.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/support/js/tickets.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/support/styles/css/faq.css", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/support/styles/css/faq.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/support/styles/css/styles.css", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/support/styles/css/styles.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/support/styles/css/tickets.css", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/support/styles/css/tickets.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/support/styles/sass/faq.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/support/styles/sass/styles.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/support/styles/sass/tickets.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/updates/js/apps.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/updates/js/frontend-themes.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/updates/js/main.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/updates/js/plugins.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/updates/js/user-components.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/updates/styles/css/styles.css", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/updates/styles/css/styles.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/updates/styles/sass/styles.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/user/js/apps.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/user/js/components.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/user/js/menu.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/user/js/networks.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/user/js/plans.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/user/js/settings.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/user/js/themes.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/user/styles/css/apps.css", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/user/styles/css/apps.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/user/styles/css/components.css", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/user/styles/css/components.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/user/styles/css/menu.css", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/user/styles/css/menu.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/user/styles/css/networks.css", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/user/styles/css/networks.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/user/styles/css/plans.css", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/user/styles/css/plans.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/user/styles/css/settings.css", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/user/styles/css/settings.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/user/styles/css/styles.css", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/user/styles/css/styles.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/user/styles/css/themes.css", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/user/styles/css/themes.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/user/styles/sass/menu.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/user/styles/sass/plans.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/user/styles/sass/styles.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/default/js/quick-guide.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/default/js/upload-box.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/default/styles/_default_badge.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/default/styles/_default_buttons.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/default/styles/_default_card_box.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/default/styles/_default_checkbox.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/default/styles/_default_checkbox_input.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/default/styles/_default_colors.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/default/styles/_default_drag_and_drop.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/default/styles/_default_dropdown.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/default/styles/_default_editor.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/default/styles/_default_fonts.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/default/styles/_default_image_field.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/default/styles/_default_input_group.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/default/styles/_default_list.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/default/styles/_default_modal.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/default/styles/_default_multiselector_dropdown.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/default/styles/_default_navbar.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/default/styles/_default_number_input.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/default/styles/_default_panel.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/default/styles/_default_password_input.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/default/styles/_default_quick_guide.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/default/styles/_default_scrollbar.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/default/styles/_default_select.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/default/styles/_default_settings_options.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/default/styles/_default_shadow.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/default/styles/_default_short_description.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/default/styles/_default_sidebar.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/default/styles/_default_tabs.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/default/styles/_default_text_input.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/default/styles/_default_textarea.scss", "downloaded_repos/justitems_midrub_cms/assets/base/admin/themes/collection/default/js/main.js", "downloaded_repos/justitems_midrub_cms/assets/base/admin/themes/collection/default/screenshot.png", "downloaded_repos/justitems_midrub_cms/assets/base/admin/themes/collection/default/styles/css/main.css", "downloaded_repos/justitems_midrub_cms/assets/base/admin/themes/collection/default/styles/css/main.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/admin/themes/collection/default/styles/sass/main.scss", "downloaded_repos/justitems_midrub_cms/assets/base/auth/collection/change-password/js/main.js", "downloaded_repos/justitems_midrub_cms/assets/base/auth/collection/change-password/styles/css/styles.css", "downloaded_repos/justitems_midrub_cms/assets/base/auth/collection/change-password/styles/css/styles.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/auth/collection/change-password/styles/sass/styles.scss", "downloaded_repos/justitems_midrub_cms/assets/base/auth/collection/confirmation/js/main.js", "downloaded_repos/justitems_midrub_cms/assets/base/auth/collection/confirmation/styles/css/styles.css", "downloaded_repos/justitems_midrub_cms/assets/base/auth/collection/confirmation/styles/css/styles.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/auth/collection/confirmation/styles/sass/styles.scss", "downloaded_repos/justitems_midrub_cms/assets/base/auth/collection/page/styles/css/styles.css", "downloaded_repos/justitems_midrub_cms/assets/base/auth/collection/page/styles/css/styles.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/auth/collection/page/styles/sass/styles.scss", "downloaded_repos/justitems_midrub_cms/assets/base/auth/collection/reset/js/main.js", "downloaded_repos/justitems_midrub_cms/assets/base/auth/collection/reset/styles/css/styles.css", "downloaded_repos/justitems_midrub_cms/assets/base/auth/collection/reset/styles/css/styles.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/auth/collection/reset/styles/sass/styles.scss", "downloaded_repos/justitems_midrub_cms/assets/base/auth/collection/signin/js/main.js", "downloaded_repos/justitems_midrub_cms/assets/base/auth/collection/signin/styles/css/styles.css", "downloaded_repos/justitems_midrub_cms/assets/base/auth/collection/signin/styles/css/styles.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/auth/collection/signin/styles/sass/styles.scss", "downloaded_repos/justitems_midrub_cms/assets/base/auth/collection/signup/js/main.js", "downloaded_repos/justitems_midrub_cms/assets/base/auth/collection/signup/styles/css/styles.css", "downloaded_repos/justitems_midrub_cms/assets/base/auth/collection/signup/styles/css/styles.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/auth/collection/signup/styles/sass/styles.scss", "downloaded_repos/justitems_midrub_cms/assets/base/auth/collection/upgrade/js/main.js", "downloaded_repos/justitems_midrub_cms/assets/base/auth/collection/upgrade/styles/css/styles.css", "downloaded_repos/justitems_midrub_cms/assets/base/auth/collection/upgrade/styles/css/styles.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/auth/collection/upgrade/styles/sass/styles.scss", "downloaded_repos/justitems_midrub_cms/assets/base/install/js/main.js", "downloaded_repos/justitems_midrub_cms/assets/base/install/styles/css/main.css", "downloaded_repos/justitems_midrub_cms/assets/base/install/styles/css/main.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/install/styles/sass/main.scss", "downloaded_repos/justitems_midrub_cms/assets/base/payments/collection/braintree/js/main.js", "downloaded_repos/justitems_midrub_cms/assets/base/payments/collection/braintree/styles/css/styles.css", "downloaded_repos/justitems_midrub_cms/assets/base/payments/collection/braintree/styles/css/styles.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/payments/collection/braintree/styles/sass/styles.scss", "downloaded_repos/justitems_midrub_cms/assets/base/payments/collection/paypal/js/main.js", "downloaded_repos/justitems_midrub_cms/assets/base/payments/collection/paypal/styles/css/styles.css", "downloaded_repos/justitems_midrub_cms/assets/base/payments/collection/paypal/styles/css/styles.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/payments/collection/paypal/styles/sass/styles.scss", "downloaded_repos/justitems_midrub_cms/assets/base/payments/collection/stripe/js/main.js", "downloaded_repos/justitems_midrub_cms/assets/base/payments/collection/stripe/styles/css/styles.css", "downloaded_repos/justitems_midrub_cms/assets/base/payments/collection/stripe/styles/css/styles.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/payments/collection/stripe/styles/sass/styles.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/js/libs/bars/progress-bar-circle.js", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/js/libs/boxes/upload-box.js", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/js/libs/calendars/compact-planner.js", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/js/libs/calendars/dropdown-calendar.js", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/js/libs/calendars/dropdown-time-picker.js", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/js/libs/calendars/minimal-calendar.js", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/js/libs/calendars/planner.js", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/js/libs/calendars/week-calendar.js", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/js/libs/chats/sms-chat.js", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/js/libs/editors/minimal-text-editor.js", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/js/libs/fields/extra-fields.js", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/js/libs/lists/posts-list.js", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/js/libs/spinners/spinner-loading-content.js", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/js/libs/texts/quick-guide.js", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/_activities.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/_area_field.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/_area_separator.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/_box_info.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/_box_option.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/_breadcrumb.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/_buttons.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/_card_box.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/_checkbox.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/_colors.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/_covers.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/_dropdown.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/_dropdown_icon.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/_fieldset.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/_fonts.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/_footer.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/_form.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/_general.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/_input_autocomplete.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/_modal.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/_quick_guide.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/_radio.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/_scrollbar.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/_search.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/_settings_options.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/_short_description.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/_sidebar.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/_tabs.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/_text_editors.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/_text_input.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/_time_input.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/animations/_animations_load_notification.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/animations/_animations_page_loading.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/animations/_animations_rotating.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/animations/_animations_zoom.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/css/messages.css", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/css/messages.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/css/subscription-expired.css", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/css/subscription-expired.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/bars/css/progress-bar-circle.css", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/bars/css/progress-bar-circle.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/bars/sass/progress-bar-circle.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/boxes/css/actions-box.css", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/boxes/css/actions-box.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/boxes/css/activities.css", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/boxes/css/activities.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/boxes/css/default-modal-errors.css", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/boxes/css/default-modal-errors.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/boxes/css/info-boxes.css", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/boxes/css/info-boxes.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/boxes/css/overview.css", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/boxes/css/overview.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/boxes/css/upload-box.css", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/boxes/css/upload-box.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/boxes/sass/actions-box.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/boxes/sass/activities.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/boxes/sass/default-modal-errors.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/boxes/sass/info-boxes.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/boxes/sass/overview.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/boxes/sass/upload-box.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/buttons/css/load-more.css", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/buttons/css/load-more.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/buttons/sass/load-more.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/calendars/css/compact-planner.css", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/calendars/css/compact-planner.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/calendars/css/dropdown-calendar.css", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/calendars/css/dropdown-calendar.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/calendars/css/dropdown-time-picker.css", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/calendars/css/dropdown-time-picker.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/calendars/css/planner.css", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/calendars/css/planner.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/calendars/css/time-picker.css", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/calendars/css/time-picker.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/calendars/css/week-calendar.css", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/calendars/css/week-calendar.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/calendars/sass/compact-planner.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/calendars/sass/dropdown-calendar.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/calendars/sass/dropdown-time-picker.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/calendars/sass/planner.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/calendars/sass/time-picker.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/calendars/sass/week-calendar.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/charts/css/card-stats.css", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/charts/css/card-stats.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/charts/sass/card-stats.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/chats/css/sms-chat.css", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/chats/css/sms-chat.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/chats/sass/sms-chat.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/fields/css/extra-fields.css", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/fields/css/extra-fields.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/fields/sass/extra-fields.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/galleries/css/mini-gallery.css", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/galleries/css/mini-gallery.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/galleries/sass/mini-gallery.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/lists/css/accounts-directory.css", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/lists/css/accounts-directory.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/lists/css/articles-list.css", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/lists/css/articles-list.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/lists/css/buttons-list.css", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/lists/css/buttons-list.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/lists/css/buttons-message-list.css", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/lists/css/buttons-message-list.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/lists/css/posts-list.css", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/lists/css/posts-list.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/lists/css/reports-list.css", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/lists/css/reports-list.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/lists/sass/accounts-directory.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/lists/sass/articles-list.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/lists/sass/buttons-list.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/lists/sass/buttons-message-list.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/lists/sass/posts-list.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/lists/sass/reports-list.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/messages/css/chat.css", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/messages/css/chat.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/messages/sass/chat.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/modals/css/tools.css", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/modals/css/tools.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/modals/sass/tools.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/spinners/css/spinner-loading-content.css", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/spinners/css/spinner-loading-content.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/spinners/sass/spinner-loading-content.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/tabs/css/animated-tabs.css", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/tabs/css/animated-tabs.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/tabs/css/full-tabs.css", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/tabs/css/full-tabs.css.map", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/tabs/sass/animated-tabs.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/libs/tabs/sass/full-tabs.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/sass/messages.scss", "downloaded_repos/justitems_midrub_cms/assets/base/user/default/styles/sass/subscription-expired.scss", "downloaded_repos/justitems_midrub_cms/assets/img/avatar-placeholder.png", "downloaded_repos/justitems_midrub_cms/assets/img/no-image.png", "downloaded_repos/justitems_midrub_cms/assets/js/bootstrap.js", "downloaded_repos/justitems_midrub_cms/assets/js/main.js", "downloaded_repos/justitems_midrub_cms/assets/share/index.html", "downloaded_repos/justitems_midrub_cms/composer.json", "downloaded_repos/justitems_midrub_cms/composer.lock", "downloaded_repos/justitems_midrub_cms/cron.php", "downloaded_repos/justitems_midrub_cms/index.php", "downloaded_repos/justitems_midrub_cms/license", "downloaded_repos/justitems_midrub_cms/loading.html", "downloaded_repos/justitems_midrub_cms/midrub-cms-update", "downloaded_repos/justitems_midrub_cms/system/.htaccess", "downloaded_repos/justitems_midrub_cms/system/core/Benchmark.php", "downloaded_repos/justitems_midrub_cms/system/core/CodeIgniter.php", "downloaded_repos/justitems_midrub_cms/system/core/Common.php", "downloaded_repos/justitems_midrub_cms/system/core/Config.php", "downloaded_repos/justitems_midrub_cms/system/core/Controller.php", "downloaded_repos/justitems_midrub_cms/system/core/Exceptions.php", "downloaded_repos/justitems_midrub_cms/system/core/Hooks.php", "downloaded_repos/justitems_midrub_cms/system/core/Input.php", "downloaded_repos/justitems_midrub_cms/system/core/Lang.php", "downloaded_repos/justitems_midrub_cms/system/core/Loader.php", "downloaded_repos/justitems_midrub_cms/system/core/Log.php", "downloaded_repos/justitems_midrub_cms/system/core/Model.php", "downloaded_repos/justitems_midrub_cms/system/core/Output.php", "downloaded_repos/justitems_midrub_cms/system/core/Router.php", "downloaded_repos/justitems_midrub_cms/system/core/Security.php", "downloaded_repos/justitems_midrub_cms/system/core/URI.php", "downloaded_repos/justitems_midrub_cms/system/core/Utf8.php", "downloaded_repos/justitems_midrub_cms/system/core/compat/hash.php", "downloaded_repos/justitems_midrub_cms/system/core/compat/index.html", "downloaded_repos/justitems_midrub_cms/system/core/compat/mbstring.php", "downloaded_repos/justitems_midrub_cms/system/core/compat/password.php", "downloaded_repos/justitems_midrub_cms/system/core/compat/standard.php", "downloaded_repos/justitems_midrub_cms/system/core/index.html", "downloaded_repos/justitems_midrub_cms/system/database/DB.php", "downloaded_repos/justitems_midrub_cms/system/database/DB_cache.php", "downloaded_repos/justitems_midrub_cms/system/database/DB_driver.php", "downloaded_repos/justitems_midrub_cms/system/database/DB_forge.php", "downloaded_repos/justitems_midrub_cms/system/database/DB_query_builder.php", "downloaded_repos/justitems_midrub_cms/system/database/DB_result.php", "downloaded_repos/justitems_midrub_cms/system/database/DB_utility.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/cubrid/cubrid_driver.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/cubrid/cubrid_forge.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/cubrid/cubrid_result.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/cubrid/cubrid_utility.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/cubrid/index.html", "downloaded_repos/justitems_midrub_cms/system/database/drivers/ibase/ibase_driver.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/ibase/ibase_forge.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/ibase/ibase_result.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/ibase/ibase_utility.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/ibase/index.html", "downloaded_repos/justitems_midrub_cms/system/database/drivers/index.html", "downloaded_repos/justitems_midrub_cms/system/database/drivers/mssql/index.html", "downloaded_repos/justitems_midrub_cms/system/database/drivers/mssql/mssql_driver.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/mssql/mssql_forge.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/mssql/mssql_result.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/mssql/mssql_utility.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/mysql/index.html", "downloaded_repos/justitems_midrub_cms/system/database/drivers/mysql/mysql_driver.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/mysql/mysql_forge.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/mysql/mysql_result.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/mysql/mysql_utility.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/mysqli/index.html", "downloaded_repos/justitems_midrub_cms/system/database/drivers/mysqli/mysqli_driver.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/mysqli/mysqli_forge.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/mysqli/mysqli_result.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/mysqli/mysqli_utility.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/oci8/index.html", "downloaded_repos/justitems_midrub_cms/system/database/drivers/oci8/oci8_driver.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/oci8/oci8_forge.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/oci8/oci8_result.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/oci8/oci8_utility.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/odbc/index.html", "downloaded_repos/justitems_midrub_cms/system/database/drivers/odbc/odbc_driver.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/odbc/odbc_forge.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/odbc/odbc_result.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/odbc/odbc_utility.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/pdo/index.html", "downloaded_repos/justitems_midrub_cms/system/database/drivers/pdo/pdo_driver.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/pdo/pdo_forge.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/pdo/pdo_result.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/pdo/pdo_utility.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/pdo/subdrivers/index.html", "downloaded_repos/justitems_midrub_cms/system/database/drivers/pdo/subdrivers/pdo_4d_driver.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/pdo/subdrivers/pdo_4d_forge.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/pdo/subdrivers/pdo_cubrid_driver.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/pdo/subdrivers/pdo_cubrid_forge.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/pdo/subdrivers/pdo_dblib_driver.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/pdo/subdrivers/pdo_dblib_forge.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/pdo/subdrivers/pdo_firebird_driver.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/pdo/subdrivers/pdo_firebird_forge.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/pdo/subdrivers/pdo_ibm_driver.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/pdo/subdrivers/pdo_ibm_forge.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/pdo/subdrivers/pdo_informix_driver.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/pdo/subdrivers/pdo_informix_forge.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/pdo/subdrivers/pdo_mysql_driver.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/pdo/subdrivers/pdo_mysql_forge.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/pdo/subdrivers/pdo_oci_driver.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/pdo/subdrivers/pdo_oci_forge.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/pdo/subdrivers/pdo_odbc_driver.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/pdo/subdrivers/pdo_odbc_forge.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/pdo/subdrivers/pdo_pgsql_driver.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/pdo/subdrivers/pdo_pgsql_forge.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/pdo/subdrivers/pdo_sqlite_driver.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/pdo/subdrivers/pdo_sqlite_forge.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/pdo/subdrivers/pdo_sqlsrv_driver.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/pdo/subdrivers/pdo_sqlsrv_forge.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/postgre/index.html", "downloaded_repos/justitems_midrub_cms/system/database/drivers/postgre/postgre_driver.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/postgre/postgre_forge.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/postgre/postgre_result.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/postgre/postgre_utility.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/sqlite/index.html", "downloaded_repos/justitems_midrub_cms/system/database/drivers/sqlite/sqlite_driver.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/sqlite/sqlite_forge.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/sqlite/sqlite_result.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/sqlite/sqlite_utility.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/sqlite3/index.html", "downloaded_repos/justitems_midrub_cms/system/database/drivers/sqlite3/sqlite3_driver.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/sqlite3/sqlite3_forge.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/sqlite3/sqlite3_result.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/sqlite3/sqlite3_utility.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/sqlsrv/index.html", "downloaded_repos/justitems_midrub_cms/system/database/drivers/sqlsrv/sqlsrv_driver.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/sqlsrv/sqlsrv_forge.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/sqlsrv/sqlsrv_result.php", "downloaded_repos/justitems_midrub_cms/system/database/drivers/sqlsrv/sqlsrv_utility.php", "downloaded_repos/justitems_midrub_cms/system/database/index.html", "downloaded_repos/justitems_midrub_cms/system/fonts/index.html", "downloaded_repos/justitems_midrub_cms/system/fonts/texb.ttf", "downloaded_repos/justitems_midrub_cms/system/helpers/array_helper.php", "downloaded_repos/justitems_midrub_cms/system/helpers/captcha_helper.php", "downloaded_repos/justitems_midrub_cms/system/helpers/cookie_helper.php", "downloaded_repos/justitems_midrub_cms/system/helpers/date_helper.php", "downloaded_repos/justitems_midrub_cms/system/helpers/directory_helper.php", "downloaded_repos/justitems_midrub_cms/system/helpers/download_helper.php", "downloaded_repos/justitems_midrub_cms/system/helpers/email_helper.php", "downloaded_repos/justitems_midrub_cms/system/helpers/file_helper.php", "downloaded_repos/justitems_midrub_cms/system/helpers/form_helper.php", "downloaded_repos/justitems_midrub_cms/system/helpers/html_helper.php", "downloaded_repos/justitems_midrub_cms/system/helpers/index.html", "downloaded_repos/justitems_midrub_cms/system/helpers/inflector_helper.php", "downloaded_repos/justitems_midrub_cms/system/helpers/language_helper.php", "downloaded_repos/justitems_midrub_cms/system/helpers/number_helper.php", "downloaded_repos/justitems_midrub_cms/system/helpers/path_helper.php", "downloaded_repos/justitems_midrub_cms/system/helpers/security_helper.php", "downloaded_repos/justitems_midrub_cms/system/helpers/smiley_helper.php", "downloaded_repos/justitems_midrub_cms/system/helpers/string_helper.php", "downloaded_repos/justitems_midrub_cms/system/helpers/text_helper.php", "downloaded_repos/justitems_midrub_cms/system/helpers/typography_helper.php", "downloaded_repos/justitems_midrub_cms/system/helpers/url_helper.php", "downloaded_repos/justitems_midrub_cms/system/helpers/xml_helper.php", "downloaded_repos/justitems_midrub_cms/system/index.html", "downloaded_repos/justitems_midrub_cms/system/language/english/calendar_lang.php", "downloaded_repos/justitems_midrub_cms/system/language/english/date_lang.php", "downloaded_repos/justitems_midrub_cms/system/language/english/db_lang.php", "downloaded_repos/justitems_midrub_cms/system/language/english/email_lang.php", "downloaded_repos/justitems_midrub_cms/system/language/english/form_validation_lang.php", "downloaded_repos/justitems_midrub_cms/system/language/english/ftp_lang.php", "downloaded_repos/justitems_midrub_cms/system/language/english/imglib_lang.php", "downloaded_repos/justitems_midrub_cms/system/language/english/index.html", "downloaded_repos/justitems_midrub_cms/system/language/english/migration_lang.php", "downloaded_repos/justitems_midrub_cms/system/language/english/number_lang.php", "downloaded_repos/justitems_midrub_cms/system/language/english/pagination_lang.php", "downloaded_repos/justitems_midrub_cms/system/language/english/profiler_lang.php", "downloaded_repos/justitems_midrub_cms/system/language/english/unit_test_lang.php", "downloaded_repos/justitems_midrub_cms/system/language/english/upload_lang.php", "downloaded_repos/justitems_midrub_cms/system/language/index.html", "downloaded_repos/justitems_midrub_cms/system/libraries/Cache/Cache.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Cache/drivers/Cache_apc.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Cache/drivers/Cache_dummy.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Cache/drivers/Cache_file.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Cache/drivers/Cache_memcached.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Cache/drivers/Cache_redis.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Cache/drivers/Cache_wincache.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Cache/drivers/index.html", "downloaded_repos/justitems_midrub_cms/system/libraries/Cache/index.html", "downloaded_repos/justitems_midrub_cms/system/libraries/Calendar.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Cart.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Driver.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Email.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Encrypt.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Encryption.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Form_validation.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Ftp.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Image_lib.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Javascript/Jquery.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Javascript/index.html", "downloaded_repos/justitems_midrub_cms/system/libraries/Javascript.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Migration.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Pagination.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Parser.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Profiler.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Session/CI_Session_driver_interface.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Session/OldSessionWrapper.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Session/PHP8SessionWrapper.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Session/Session.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Session/SessionHandlerInterface.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Session/SessionUpdateTimestampHandlerInterface.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Session/Session_driver.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Session/drivers/Session_database_driver.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Session/drivers/Session_files_driver.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Session/drivers/Session_memcached_driver.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Session/drivers/Session_redis_driver.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Session/drivers/index.html", "downloaded_repos/justitems_midrub_cms/system/libraries/Session/index.html", "downloaded_repos/justitems_midrub_cms/system/libraries/Table.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Trackback.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Typography.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Unit_test.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Upload.php", "downloaded_repos/justitems_midrub_cms/system/libraries/User_agent.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Xmlrpc.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Xmlrpcs.php", "downloaded_repos/justitems_midrub_cms/system/libraries/Zip.php", "downloaded_repos/justitems_midrub_cms/system/libraries/index.html"], "skipped": [{"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/autoload.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/CHANGELOG.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/AccountUpdaterDailyReport.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/AchMandate.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/AddOn.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/AddOnGateway.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Address.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/AddressGateway.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/AmexExpressCheckoutCard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/AndroidPayCard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/ApplePayCard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/ApplePayGateway.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/ApplePayOptions.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/AuthorizationAdjustment.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Base.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/BinData.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/ClientToken.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/ClientTokenGateway.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Collection.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Configuration.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/ConnectedMerchantPayPalStatusChanged.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/ConnectedMerchantStatusTransitioned.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/CredentialsParser.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/CreditCard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/CreditCardGateway.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/CreditCardVerification.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/CreditCardVerificationGateway.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/CreditCardVerificationSearch.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Customer.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/CustomerGateway.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/CustomerSearch.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Descriptor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Digest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Disbursement.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/DisbursementDetails.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Discount.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/DiscountGateway.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Dispute/EvidenceDetails.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Dispute/StatusHistoryDetails.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Dispute/TransactionDetails.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Dispute.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/DisputeGateway.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/DisputeSearch.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/DocumentUpload.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/DocumentUploadGateway.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/EndsWithNode.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/EqualityNode.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Error/Codes.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Error/ErrorCollection.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Error/Validation.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Error/ValidationErrorCollection.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/EuropeBankAccount.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Exception/Authentication.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Exception/Authorization.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Exception/Configuration.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Exception/Connection.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Exception/GatewayTimeout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Exception/InvalidChallenge.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Exception/InvalidSignature.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Exception/NotFound.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Exception/RequestTimeout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Exception/SSLCaFileNotFound.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Exception/SSLCertificate.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Exception/ServerError.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Exception/ServiceUnavailable.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Exception/TestOperationPerformedInProduction.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Exception/Timeout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Exception/TooManyRequests.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Exception/Unexpected.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Exception/UpgradeRequired.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Exception/ValidationsFailed.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Exception.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/FacilitatedDetails.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/FacilitatorDetails.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Gateway.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/GrantedPaymentInstrumentUpdate.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/GraphQL.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/GraphQLClient.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Http.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/IbanBankAccount.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Instance.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/IsNode.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/KeyValueNode.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/LocalPaymentCompleted.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/MasterpassCard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Merchant.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/MerchantAccount/AddressDetails.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/MerchantAccount/BusinessDetails.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/MerchantAccount/FundingDetails.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/MerchantAccount/IndividualDetails.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/MerchantAccount.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/MerchantAccountGateway.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/MerchantGateway.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Modification.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/MultipleValueNode.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/MultipleValueOrTextNode.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/OAuthAccessRevocation.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/OAuthCredentials.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/OAuthGateway.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/OAuthResult.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/PaginatedCollection.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/PaginatedResult.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/PartialMatchNode.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/PartnerMerchant.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/PayPalAccount.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/PayPalAccountGateway.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/PaymentInstrumentType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/PaymentMethod.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/PaymentMethodGateway.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/PaymentMethodNonce.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/PaymentMethodNonceGateway.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/PaymentMethodParser.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Plan.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/PlanGateway.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/ProcessorResponseTypes.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/RangeNode.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/ResourceCollection.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Result/CreditCardVerification.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Result/Error.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Result/Successful.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Result/UsBankAccountVerification.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/RevokedPaymentMethodMetadata.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/RiskData.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/SamsungPayCard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/SettlementBatchSummary.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/SettlementBatchSummaryGateway.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/SignatureService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Subscription/StatusDetails.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Subscription.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/SubscriptionGateway.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/SubscriptionSearch.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Test/CreditCardNumbers.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Test/MerchantAccount.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Test/Nonces.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Test/Transaction.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Test/TransactionAmounts.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Test/VenmoSdk.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/TestingGateway.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/TextNode.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/ThreeDSecureInfo.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Transaction/AddressDetails.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Transaction/AmexExpressCheckoutCardDetails.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Transaction/AndroidPayCardDetails.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Transaction/ApplePayCardDetails.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Transaction/CreditCardDetails.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Transaction/CustomerDetails.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Transaction/EuropeBankAccountDetails.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Transaction/LocalPaymentDetails.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Transaction/MasterpassCardDetails.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Transaction/PayPalDetails.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Transaction/PayPalHereDetails.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Transaction/SamsungPayCardDetails.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Transaction/StatusDetails.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Transaction/SubscriptionDetails.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Transaction/UsBankAccountDetails.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Transaction/VenmoAccountDetails.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Transaction/VisaCheckoutCardDetails.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Transaction.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/TransactionGateway.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/TransactionLineItem.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/TransactionLineItemGateway.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/TransactionSearch.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/UnknownPaymentMethod.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/UsBankAccount.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/UsBankAccountGateway.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/UsBankAccountVerification.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/UsBankAccountVerificationGateway.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/UsBankAccountVerificationSearch.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Util.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/VenmoAccount.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Version.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/VisaCheckoutCard.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/WebhookNotification.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/WebhookNotificationGateway.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/WebhookTesting.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/WebhookTestingGateway.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Xml/Generator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Xml/Parser.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree/Xml.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/Braintree.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/autoload.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/braintree/braintree_php/lib/ssl/api_braintreegateway_com.ca.crt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/composer/ClassLoader.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/composer/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/composer/autoload_classmap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/composer/autoload_namespaces.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/composer/autoload_psr4.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/composer/autoload_real.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/composer/autoload_static.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/braintree/vendor/composer/installed.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/autoload.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/composer/ClassLoader.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/composer/InstalledVersions.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/composer/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/composer/autoload_classmap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/composer/autoload_namespaces.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/composer/autoload_psr4.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/composer/autoload_real.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/composer/autoload_static.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/composer/installed.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/composer/installed.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/composer/platform_check.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/.coveralls.github-actions.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/.editorconfig", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/.gitignore", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/.php-cs-fixer.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/CHANGELOG.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/CODE_OF_CONDUCT.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/Makefile", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/VERSION", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/build.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/composer.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/data/ca-certificates.crt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/init.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Account.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/AccountLink.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/AlipayAccount.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/ApiOperations/All.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/ApiOperations/Create.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/ApiOperations/Delete.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/ApiOperations/NestedResource.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/ApiOperations/Request.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/ApiOperations/Retrieve.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/ApiOperations/Search.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/ApiOperations/Update.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/ApiRequestor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/ApiResource.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/ApiResponse.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/ApplePayDomain.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/ApplicationFee.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/ApplicationFeeRefund.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Balance.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/BalanceTransaction.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/BankAccount.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/BaseStripeClient.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/BaseStripeClientInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/BillingPortal/Configuration.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/BillingPortal/Session.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/BitcoinReceiver.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/BitcoinTransaction.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Capability.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Card.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Charge.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Checkout/Session.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Collection.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/CountrySpec.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Coupon.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/CreditNote.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/CreditNoteLineItem.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Customer.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/CustomerBalanceTransaction.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Discount.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Dispute.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/EphemeralKey.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/ErrorObject.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Event.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Exception/ApiConnectionException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Exception/ApiErrorException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Exception/AuthenticationException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Exception/BadMethodCallException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Exception/CardException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Exception/ExceptionInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Exception/IdempotencyException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Exception/InvalidArgumentException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Exception/InvalidRequestException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Exception/OAuth/ExceptionInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Exception/OAuth/InvalidClientException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Exception/OAuth/InvalidGrantException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Exception/OAuth/InvalidRequestException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Exception/OAuth/InvalidScopeException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Exception/OAuth/OAuthErrorException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Exception/OAuth/UnknownOAuthErrorException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Exception/OAuth/UnsupportedGrantTypeException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Exception/OAuth/UnsupportedResponseTypeException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Exception/PermissionException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Exception/RateLimitException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Exception/SignatureVerificationException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Exception/UnexpectedValueException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Exception/UnknownApiErrorException.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/ExchangeRate.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/File.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/FileLink.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/HttpClient/ClientInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/HttpClient/CurlClient.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/HttpClient/StreamingClientInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Identity/VerificationReport.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Identity/VerificationSession.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Invoice.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/InvoiceItem.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/InvoiceLineItem.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Issuing/Authorization.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Issuing/Card.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Issuing/CardDetails.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Issuing/Cardholder.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Issuing/Dispute.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Issuing/Transaction.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/LineItem.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/LoginLink.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Mandate.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/OAuth.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/OAuthErrorObject.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Order.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/OrderItem.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/OrderReturn.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/PaymentIntent.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/PaymentLink.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/PaymentMethod.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Payout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Person.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Plan.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Price.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Product.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/PromotionCode.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Quote.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Radar/EarlyFraudWarning.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Radar/ValueList.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Radar/ValueListItem.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Recipient.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/RecipientTransfer.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Refund.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Reporting/ReportRun.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Reporting/ReportType.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/RequestTelemetry.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Review.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/SKU.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/SearchResult.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/AbstractService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/AbstractServiceFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/AccountLinkService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/AccountService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/ApplePayDomainService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/ApplicationFeeService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/BalanceService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/BalanceTransactionService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/BillingPortal/BillingPortalServiceFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/BillingPortal/ConfigurationService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/BillingPortal/SessionService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/ChargeService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/Checkout/CheckoutServiceFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/Checkout/SessionService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/CoreServiceFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/CountrySpecService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/CouponService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/CreditNoteService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/CustomerService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/DisputeService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/EphemeralKeyService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/EventService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/ExchangeRateService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/FileLinkService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/FileService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/Identity/IdentityServiceFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/Identity/VerificationReportService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/Identity/VerificationSessionService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/InvoiceItemService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/InvoiceService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/Issuing/AuthorizationService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/Issuing/CardService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/Issuing/CardholderService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/Issuing/DisputeService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/Issuing/IssuingServiceFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/Issuing/TransactionService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/MandateService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/OAuthService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/OrderReturnService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/OrderService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/PaymentIntentService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/PaymentLinkService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/PaymentMethodService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/PayoutService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/PlanService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/PriceService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/ProductService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/PromotionCodeService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/QuoteService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/Radar/EarlyFraudWarningService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/Radar/RadarServiceFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/Radar/ValueListItemService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/Radar/ValueListService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/RefundService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/Reporting/ReportRunService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/Reporting/ReportTypeService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/Reporting/ReportingServiceFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/ReviewService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/SetupAttemptService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/SetupIntentService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/ShippingRateService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/Sigma/ScheduledQueryRunService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/Sigma/SigmaServiceFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/SkuService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/SourceService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/SubscriptionItemService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/SubscriptionScheduleService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/SubscriptionService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/TaxCodeService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/TaxRateService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/Terminal/ConnectionTokenService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/Terminal/LocationService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/Terminal/ReaderService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/Terminal/TerminalServiceFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/TestHelpers/TestClockService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/TestHelpers/TestHelpersServiceFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/TokenService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/TopupService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/TransferService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Service/WebhookEndpointService.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/SetupAttempt.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/SetupIntent.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/ShippingRate.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Sigma/ScheduledQueryRun.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/SingletonApiResource.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Source.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/SourceTransaction.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Stripe.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/StripeClient.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/StripeClientInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/StripeObject.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/StripeStreamingClientInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Subscription.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/SubscriptionItem.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/SubscriptionSchedule.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/TaxCode.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/TaxId.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/TaxRate.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Terminal/ConnectionToken.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Terminal/Location.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Terminal/Reader.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/TestHelpers/TestClock.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/ThreeDSecure.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Token.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Topup.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Transfer.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/TransferReversal.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/UsageRecord.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/UsageRecordSummary.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Util/CaseInsensitiveArray.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Util/DefaultLogger.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Util/LoggerInterface.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Util/ObjectTypes.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Util/RandomGenerator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Util/RequestOptions.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Util/Set.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Util/Util.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/Webhook.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/WebhookEndpoint.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/lib/WebhookSignature.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/phpdoc.dist.xml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/phpstan-baseline.neon", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/phpstan.neon.dist", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/application/base/payments/collection/stripe/vendor/stripe/stripe-php/update_certs.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/assets/js/jquery.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/vendor/autoload.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/vendor/composer/ClassLoader.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/vendor/composer/InstalledVersions.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/vendor/composer/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/vendor/composer/autoload_classmap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/vendor/composer/autoload_namespaces.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/vendor/composer/autoload_psr4.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/vendor/composer/autoload_real.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/vendor/composer/autoload_static.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/vendor/composer/installed.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/justitems_midrub_cms/vendor/composer/installed.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.7178020477294922, "profiling_times": {"config_time": 5.751705646514893, "core_time": 15.603924036026001, "ignores_time": 0.002080678939819336, "total_time": 21.35863447189331}, "parsing_time": {"total_time": 34.024197816848755, "per_file_time": {"mean": 0.02425103194358429, "std_dev": 0.0034415249504956146}, "very_slow_stats": {"time_ratio": 0.12429331399891172, "count_ratio": 0.006414825374198147}, "very_slow_files": [{"fpath": "downloaded_repos/justitems_midrub_cms/system/core/Loader.php", "ftime": 0.3014800548553467}, {"fpath": "downloaded_repos/justitems_midrub_cms/assets/base/user/default/js/libs/lists/posts-list.js", "ftime": 0.3232760429382324}, {"fpath": "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/user/js/plans.js", "ftime": 0.3942270278930664}, {"fpath": "downloaded_repos/justitems_midrub_cms/system/libraries/Xmlrpc.php", "ftime": 0.41848301887512207}, {"fpath": "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/frontend/js/editor.js", "ftime": 0.4209868907928467}, {"fpath": "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/frontend/js/menu.js", "ftime": 0.4366590976715088}, {"fpath": "downloaded_repos/justitems_midrub_cms/system/libraries/Email.php", "ftime": 0.4619710445404053}, {"fpath": "downloaded_repos/justitems_midrub_cms/assets/base/user/default/js/libs/calendars/planner.js", "ftime": 0.5819070339202881}, {"fpath": "downloaded_repos/justitems_midrub_cms/assets/js/bootstrap.js", "ftime": 0.8899900913238525}]}, "scanning_time": {"total_time": 105.83132982254028, "per_file_time": {"mean": 0.021136674620040062, "std_dev": 0.020407832595034064}, "very_slow_stats": {"time_ratio": 0.2670863851770794, "count_ratio": 0.002396644697423607}, "very_slow_files": [{"fpath": "downloaded_repos/justitems_midrub_cms/system/libraries/Email.php", "ftime": 1.6737289428710938}, {"fpath": "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/notifications/js/users-alerts.js", "ftime": 1.7904760837554932}, {"fpath": "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/user/js/menu.js", "ftime": 1.8369548320770264}, {"fpath": "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/members/js/all-members.js", "ftime": 1.874433994293213}, {"fpath": "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/frontend/js/menu.js", "ftime": 2.0548670291900635}, {"fpath": "downloaded_repos/justitems_midrub_cms/assets/base/admin/default/js/upload-box.js", "ftime": 2.343276023864746}, {"fpath": "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/user/js/plans.js", "ftime": 2.6688811779022217}, {"fpath": "downloaded_repos/justitems_midrub_cms/assets/base/user/default/js/libs/calendars/planner.js", "ftime": 3.1706340312957764}, {"fpath": "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/frontend/js/editor.js", "ftime": 3.41524600982666}, {"fpath": "downloaded_repos/justitems_midrub_cms/assets/js/bootstrap.js", "ftime": 4.155872106552124}]}, "matching_time": {"total_time": 35.26993227005005, "per_file_and_rule_time": {"mean": 0.01401825606917729, "std_dev": 0.002083058970842789}, "very_slow_stats": {"time_ratio": 0.5071516314926144, "count_ratio": 0.03259141494435612}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/justitems_midrub_cms/assets/base/user/default/js/libs/calendars/planner.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.39606189727783203}, {"fpath": "downloaded_repos/justitems_midrub_cms/assets/base/user/default/js/libs/calendars/planner.js", "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.40327000617980957}, {"fpath": "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/user/js/plans.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.4197230339050293}, {"fpath": "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/frontend/js/contents-list.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.43915796279907227}, {"fpath": "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/frontend/js/editor.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.4393460750579834}, {"fpath": "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/frontend/js/editor.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.4419219493865967}, {"fpath": "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/members/js/all-members.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.4621469974517822}, {"fpath": "downloaded_repos/justitems_midrub_cms/assets/js/bootstrap.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.46859288215637207}, {"fpath": "downloaded_repos/justitems_midrub_cms/system/libraries/Email.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.5099399089813232}, {"fpath": "downloaded_repos/justitems_midrub_cms/assets/base/user/default/js/libs/calendars/planner.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.6116859912872314}]}, "tainting_time": {"total_time": 17.047420740127563, "per_def_and_rule_time": {"mean": 0.004590043279517384, "std_dev": 0.0007966531288078336}, "very_slow_stats": {"time_ratio": 0.4840211677802592, "count_ratio": 0.018578352180936994}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/justitems_midrub_cms/assets/base/user/default/js/libs/calendars/planner.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.1631910800933838}, {"fpath": "downloaded_repos/justitems_midrub_cms/assets/js/bootstrap.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.17006301879882812}, {"fpath": "downloaded_repos/justitems_midrub_cms/assets/js/bootstrap.js", "fline": 1, "rule_id": "javascript.express.security.cors-misconfiguration.cors-misconfiguration", "time": 0.1703639030456543}, {"fpath": "downloaded_repos/justitems_midrub_cms/assets/js/bootstrap.js", "fline": 1, "rule_id": "javascript.express.security.require-request.require-request", "time": 0.1732029914855957}, {"fpath": "downloaded_repos/justitems_midrub_cms/assets/js/bootstrap.js", "fline": 1, "rule_id": "javascript.express.security.x-frame-options-misconfiguration.x-frame-options-misconfiguration", "time": 0.1765589714050293}, {"fpath": "downloaded_repos/justitems_midrub_cms/assets/base/user/default/js/libs/calendars/planner.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.21232008934020996}, {"fpath": "downloaded_repos/justitems_midrub_cms/assets/js/bootstrap.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.23508000373840332}, {"fpath": "downloaded_repos/justitems_midrub_cms/assets/base/admin/components/collection/frontend/js/editor.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.3553318977355957}, {"fpath": "downloaded_repos/justitems_midrub_cms/assets/base/admin/default/js/upload-box.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.8985660076141357}, {"fpath": "downloaded_repos/justitems_midrub_cms/assets/base/user/default/js/libs/boxes/upload-box.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 1.1520159244537354}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1154514816}, "engine_requested": "OSS", "skipped_rules": []}