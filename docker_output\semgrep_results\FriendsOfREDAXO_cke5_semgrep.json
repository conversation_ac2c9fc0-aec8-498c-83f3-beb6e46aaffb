{"version": "1.130.0", "results": [{"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/FriendsOfREDAXO_cke5/lib/Cke5/Handler/Cke5FileUploadHandler.php", "start": {"line": 348, "col": 21, "offset": 15227}, "end": {"line": 348, "col": 44, "offset": 15250}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/FriendsOfREDAXO_cke5/lib/Cke5/Handler/Cke5FileUploadHandler.php", "start": {"line": 404, "col": 52, "offset": 18134}, "end": {"line": 404, "col": 73, "offset": 18155}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/FriendsOfREDAXO_cke5/lib/Cke5/Handler/Cke5FileUploadHandler.php", "start": {"line": 414, "col": 48, "offset": 18552}, "end": {"line": 414, "col": 65, "offset": 18569}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/FriendsOfREDAXO_cke5/lib/Cke5/Handler/Cke5FileUploadHandler.php", "start": {"line": 455, "col": 21, "offset": 20517}, "end": {"line": 455, "col": 44, "offset": 20540}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/FriendsOfREDAXO_cke5/lib/Cke5/Handler/Cke5FileUploadHandler.php", "start": {"line": 502, "col": 30, "offset": 22966}, "end": {"line": 502, "col": 47, "offset": 22983}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/FriendsOfREDAXO_cke5/lib/Cke5/Handler/Cke5FileUploadHandler.php", "start": {"line": 545, "col": 21, "offset": 24996}, "end": {"line": 545, "col": 42, "offset": 25017}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/FriendsOfREDAXO_cke5/lib/Cke5/Handler/Cke5FileUploadHandler.php", "start": {"line": 555, "col": 25, "offset": 25479}, "end": {"line": 555, "col": 46, "offset": 25500}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/FriendsOfREDAXO_cke5/lib/Cke5/Handler/Cke5FileUploadHandler.php", "start": {"line": 562, "col": 25, "offset": 25731}, "end": {"line": 562, "col": 46, "offset": 25752}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/FriendsOfREDAXO_cke5/lib/Cke5/Handler/Cke5FileUploadHandler.php", "start": {"line": 568, "col": 21, "offset": 25975}, "end": {"line": 568, "col": 42, "offset": 25996}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/FriendsOfREDAXO_cke5/lib/Cke5/Handler/Cke5FileUploadHandler.php", "start": {"line": 577, "col": 25, "offset": 26418}, "end": {"line": 577, "col": 46, "offset": 26439}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/FriendsOfREDAXO_cke5/.gitattributes", "downloaded_repos/FriendsOfREDAXO_cke5/.github/workflows/publish-to-redaxo.yml", "downloaded_repos/FriendsOfREDAXO_cke5/CHANGELOG.md", "downloaded_repos/FriendsOfREDAXO_cke5/LICENSE.md", "downloaded_repos/FriendsOfREDAXO_cke5/README.de.md", "downloaded_repos/FriendsOfREDAXO_cke5/README.md", "downloaded_repos/FriendsOfREDAXO_cke5/assets/cke5.js", "downloaded_repos/FriendsOfREDAXO_cke5/assets/cke5_content_styles.css", "downloaded_repos/FriendsOfREDAXO_cke5/assets/css/cke5.css", "downloaded_repos/FriendsOfREDAXO_cke5/assets/css/cke5_dark.css", "downloaded_repos/FriendsOfREDAXO_cke5/assets/css/cke5_upload.css", "downloaded_repos/FriendsOfREDAXO_cke5/assets/css/dark.css", "downloaded_repos/FriendsOfREDAXO_cke5/assets/fonts/montserrat-v14-latin-700.eot", "downloaded_repos/FriendsOfREDAXO_cke5/assets/fonts/montserrat-v14-latin-700.svg", "downloaded_repos/FriendsOfREDAXO_cke5/assets/fonts/montserrat-v14-latin-700.ttf", "downloaded_repos/FriendsOfREDAXO_cke5/assets/fonts/montserrat-v14-latin-700.woff", "downloaded_repos/FriendsOfREDAXO_cke5/assets/fonts/montserrat-v14-latin-700.woff2", "downloaded_repos/FriendsOfREDAXO_cke5/assets/fonts/montserrat-v14-latin-regular.eot", "downloaded_repos/FriendsOfREDAXO_cke5/assets/fonts/montserrat-v14-latin-regular.svg", "downloaded_repos/FriendsOfREDAXO_cke5/assets/fonts/montserrat-v14-latin-regular.ttf", "downloaded_repos/FriendsOfREDAXO_cke5/assets/fonts/montserrat-v14-latin-regular.woff", "downloaded_repos/FriendsOfREDAXO_cke5/assets/fonts/montserrat-v14-latin-regular.woff2", "downloaded_repos/FriendsOfREDAXO_cke5/assets/images/1125px-columbia.sts-1.01.jpg", "downloaded_repos/FriendsOfREDAXO_cke5/assets/images/1148px-enterprise_free_flight.jpg", "downloaded_repos/FriendsOfREDAXO_cke5/assets/images/604px-sts-95_launch.jpg", "downloaded_repos/FriendsOfREDAXO_cke5/assets/images/711px-space_shuttle_endeavour_launches_on_sts-99.jpg", "downloaded_repos/FriendsOfREDAXO_cke5/assets/images/STS-103_Hubble_EVA.jpg", "downloaded_repos/FriendsOfREDAXO_cke5/assets/images/Space_Shuttle_Program_Commemorative_Patch.png", "downloaded_repos/FriendsOfREDAXO_cke5/assets/images/atlantis_lands_kl.jpg", "downloaded_repos/FriendsOfREDAXO_cke5/assets/images/cke5.svg", "downloaded_repos/FriendsOfREDAXO_cke5/assets/images/cke5_black.svg", "downloaded_repos/FriendsOfREDAXO_cke5/assets/images/cke5_white.svg", "downloaded_repos/FriendsOfREDAXO_cke5/assets/images/header-patrick-fore-357913-unsplash.jpg", "downloaded_repos/FriendsOfREDAXO_cke5/assets/images/space_shuttle_challenger_lands_for_the_first_time_completing_sts-6.jpg", "downloaded_repos/FriendsOfREDAXO_cke5/assets/js/cke5profile_edit.js", "downloaded_repos/FriendsOfREDAXO_cke5/assets/js/cke5style_edit.js", "downloaded_repos/FriendsOfREDAXO_cke5/boot.php", "downloaded_repos/FriendsOfREDAXO_cke5/custom_data/autogenerated-custom-styles.css", "downloaded_repos/FriendsOfREDAXO_cke5/custom_data/custom-styles.css", "downloaded_repos/FriendsOfREDAXO_cke5/data.sql", "downloaded_repos/FriendsOfREDAXO_cke5/db.php", "downloaded_repos/FriendsOfREDAXO_cke5/install.php", "downloaded_repos/FriendsOfREDAXO_cke5/lang/de_de.lang", "downloaded_repos/FriendsOfREDAXO_cke5/lang/en_gb.lang", "downloaded_repos/FriendsOfREDAXO_cke5/lang/es_es.lang", "downloaded_repos/FriendsOfREDAXO_cke5/lang/sv_se.lang", "downloaded_repos/FriendsOfREDAXO_cke5/lib/Cke5/Creator/Cke5ProfilesApi.php", "downloaded_repos/FriendsOfREDAXO_cke5/lib/Cke5/Creator/Cke5ProfilesCreator.php", "downloaded_repos/FriendsOfREDAXO_cke5/lib/Cke5/Handler/Cke5CssHandler.php", "downloaded_repos/FriendsOfREDAXO_cke5/lib/Cke5/Handler/Cke5DatabaseHandler.php", "downloaded_repos/FriendsOfREDAXO_cke5/lib/Cke5/Handler/Cke5ExtensionHandler.php", "downloaded_repos/FriendsOfREDAXO_cke5/lib/Cke5/Handler/Cke5FileRestoreHandler.php", "downloaded_repos/FriendsOfREDAXO_cke5/lib/Cke5/Handler/Cke5FileUploadHandler.php", "downloaded_repos/FriendsOfREDAXO_cke5/lib/Cke5/Handler/Cke5UploadHandler.php", "downloaded_repos/FriendsOfREDAXO_cke5/lib/Cke5/Provider/Cke5AssetsProvider.php", "downloaded_repos/FriendsOfREDAXO_cke5/lib/Cke5/Provider/Cke5NavigationProvider.php", "downloaded_repos/FriendsOfREDAXO_cke5/lib/Cke5/Utils/CKE5ISO6391.php", "downloaded_repos/FriendsOfREDAXO_cke5/lib/Cke5/Utils/Cke5FormHelper.php", "downloaded_repos/FriendsOfREDAXO_cke5/lib/Cke5/Utils/Cke5Lang.php", "downloaded_repos/FriendsOfREDAXO_cke5/lib/Cke5/Utils/Cke5ListHelper.php", "downloaded_repos/FriendsOfREDAXO_cke5/lib/Cke5/Utils/Cke5PreviewHelper.php", "downloaded_repos/FriendsOfREDAXO_cke5/package.yml", "downloaded_repos/FriendsOfREDAXO_cke5/pages/config.php", "downloaded_repos/FriendsOfREDAXO_cke5/pages/index.php", "downloaded_repos/FriendsOfREDAXO_cke5/pages/main.demo.php", "downloaded_repos/FriendsOfREDAXO_cke5/pages/main.preview.php", "downloaded_repos/FriendsOfREDAXO_cke5/pages/profiles.customise.groups.php", "downloaded_repos/FriendsOfREDAXO_cke5/pages/profiles.customise.styles.php", "downloaded_repos/FriendsOfREDAXO_cke5/pages/profiles.customise.template_groups.php", "downloaded_repos/FriendsOfREDAXO_cke5/pages/profiles.customise.templates.php", "downloaded_repos/FriendsOfREDAXO_cke5/pages/profiles.export.php", "downloaded_repos/FriendsOfREDAXO_cke5/pages/profiles.import.php", "downloaded_repos/FriendsOfREDAXO_cke5/pages/profiles.overview.php", "downloaded_repos/FriendsOfREDAXO_cke5/pages/profiles.profiles.php", "downloaded_repos/FriendsOfREDAXO_cke5/uninstall.php", "downloaded_repos/FriendsOfREDAXO_cke5/update.php"], "skipped": [{"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/alphanum/jquery.alphanum.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/bootstrap-slider/bootstrap-slider.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/bootstrap-slider/bootstrap-slider.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/bootstrap-slider/bootstrap-slider.min.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/bootstrap-slider/bootstrap-slider.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/bootstrap-toggle/bootstrap-toggle.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/bootstrap-toggle/bootstrap-toggle.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/bootstrap-toggle/bootstrap-toggle.min.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/bootstrap-toggle/bootstrap-toggle.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/bootstrap-toggle/bootstrap-toggle.min.js.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/cke5InputTags/cke5InputTags.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/cke5InputTags/cke5InputTags.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/ckeditor.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/ckeditor.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/ckeditor.js.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/plugins/ckeditor5-paste-plaintext/src/augmentation.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/plugins/ckeditor5-paste-plaintext/src/index.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/plugins/ckeditor5-paste-plaintext/src/plaintext.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/plugins/ckeditor5-paste-plaintext/src/plaintextcommand.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/plugins/ckeditor5-paste-plaintext/src/plaintextui.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/plugins/ckeditor5-paste-plaintext/src/translation.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/plugins/ckeditor5-reximage/src/augmentation.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/plugins/ckeditor5-reximage/src/index.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/plugins/ckeditor5-reximage/src/reximage.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/plugins/ckeditor5-rexlink/src/augmentation.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/plugins/ckeditor5-rexlink/src/index.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/plugins/ckeditor5-rexlink/src/rexlink.d.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/af.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/ar.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/ast.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/az.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/bg.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/bn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/bs.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/ca.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/cs.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/da.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/de-ch.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/de.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/el.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/en-au.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/en-gb.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/eo.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/es-co.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/es.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/et.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/eu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/fa.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/fi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/fr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/gl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/gu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/he.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/hi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/hr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/hu.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/hy.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/id.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/it.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/ja.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/jv.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/kk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/km.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/kn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/ko.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/ku.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/lt.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/lv.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/ms.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/nb.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/ne.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/nl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/no.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/oc.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/pl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/pt-br.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/pt.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/ro.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/ru.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/si.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/sk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/sl.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/sq.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/sr-latn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/sr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/sv.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/th.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/ti.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/tk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/tr.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/tt.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/ug.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/uk.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/ur.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/uz.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/vi.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/zh-cn.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/ckeditor5-classic/translations/zh.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/colpick/LICENSE", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/colpick/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/colpick/bower.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/colpick/css/colpick.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/colpick/example/index.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/colpick/js/colpick.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/colpick/package.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/multiinput/Gruntfile.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/multiinput/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/multiinput/dist/css/jq.multiinput.min.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/multiinput/dist/js/jq.multiinput.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/multiinput/index.html", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/multiinput/package-lock.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/multiinput/package.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/multiinput/src/js/jq.multiinput.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/multiinput/src/scss/jq.multiinput.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/rainbow-json/rainbowjson.css", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/FriendsOfREDAXO_cke5/assets/vendor/rainbow-json/rainbowjson.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.8073301315307617, "profiling_times": {"config_time": 6.020107984542847, "core_time": 3.520803213119507, "ignores_time": 0.0021467208862304688, "total_time": 9.543839693069458}, "parsing_time": {"total_time": 1.1581361293792725, "per_file_time": {"mean": 0.031300976469710064, "std_dev": 0.001522267295420625}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 3.235214948654175, "per_file_time": {"mean": 0.017117539410868642, "std_dev": 0.004249940947496937}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.6694281101226807, "per_file_and_rule_time": {"mean": 0.005355424880981445, "std_dev": 0.0002418152096966878}, "very_slow_stats": {"time_ratio": 0.21061227978637964, "count_ratio": 0.008}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/FriendsOfREDAXO_cke5/assets/js/cke5profile_edit.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.1409897804260254}]}, "tainting_time": {"total_time": 0.22244620323181152, "per_def_and_rule_time": {"mean": 0.001064335900630677, "std_dev": 2.7562125601374605e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}