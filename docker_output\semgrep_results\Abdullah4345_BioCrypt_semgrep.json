{"version": "1.130.0", "results": [{"check_id": "python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "path": "downloaded_repos/Abdullah4345_BioCrypt/BioCrypt-Personal-V0.4 Source-code/main.py", "start": {"line": 51, "col": 27, "offset": 1367}, "end": {"line": 51, "col": 31, "offset": 1371}, "extra": {"message": "Found 'subprocess' function 'Popen' with 'shell=True'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use 'shell=False' instead.", "fix": "False", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b602_subprocess_popen_with_shell_equals_true.html", "owasp": ["A01:2017 - Injection", "A03:2021 - Injection"], "cwe": ["CWE-78: Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')"], "references": ["https://stackoverflow.com/questions/3172470/actual-meaning-of-shell-true-in-subprocess", "https://docs.python.org/3/library/subprocess.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["secure default"], "likelihood": "HIGH", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Command Injection"], "source": "https://semgrep.dev/r/python.lang.security.audit.subprocess-shell-true.subprocess-shell-true", "shortlink": "https://sg.run/J92w"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/Abdullah4345_BioCrypt/.github/FUNDING.yml", "downloaded_repos/Abdullah4345_BioCrypt/.github/ISSUE_TEMPLATE/bug_report.md", "downloaded_repos/Abdullah4345_BioCrypt/.github/ISSUE_TEMPLATE/feature_request.md", "downloaded_repos/Abdullah4345_BioCrypt/BioCrypt-Personal-V0.4 Source-code/BioCrypt-Personal-V0.4.spec", "downloaded_repos/Abdullah4345_BioCrypt/BioCrypt-Personal-V0.4 Source-code/data/encryption_keys.json", "downloaded_repos/Abdullah4345_BioCrypt/BioCrypt-Personal-V0.4 Source-code/data/file_names.json", "downloaded_repos/Abdullah4345_BioCrypt/BioCrypt-Personal-V0.4 Source-code/data/local_storage.txt", "downloaded_repos/Abdullah4345_BioCrypt/BioCrypt-Personal-V0.4 Source-code/data/theme_settings.csv", "downloaded_repos/Abdullah4345_BioCrypt/BioCrypt-Personal-V0.4 Source-code/data/user_info.csv", "downloaded_repos/Abdullah4345_BioCrypt/BioCrypt-Personal-V0.4 Source-code/main.py", "downloaded_repos/Abdullah4345_BioCrypt/BioCrypt-Personal-V0.4 Source-code/requirements.txt", "downloaded_repos/Abdullah4345_BioCrypt/BioCrypt-Personal-V0.4 Source-code/resources/app_icon.icns", "downloaded_repos/Abdullah4345_BioCrypt/BioCrypt-Personal-V0.4 Source-code/sub_programs/__init__.py", "downloaded_repos/Abdullah4345_BioCrypt/BioCrypt-Personal-V0.4 Source-code/sub_programs/__pycache__/IPFS.cpython-313.pyc", "downloaded_repos/Abdullah4345_BioCrypt/BioCrypt-Personal-V0.4 Source-code/sub_programs/__pycache__/__init__.cpython-313.pyc", "downloaded_repos/Abdullah4345_BioCrypt/BioCrypt-Personal-V0.4 Source-code/sub_programs/__pycache__/safe_mail.cpython-313.pyc", "downloaded_repos/Abdullah4345_BioCrypt/BioCrypt-Personal-V0.4 Source-code/sub_programs/safe_mail.py", "downloaded_repos/Abdullah4345_BioCrypt/CODE_OF_CONDUCT.md", "downloaded_repos/Abdullah4345_BioCrypt/CONTRIBUTING.md", "downloaded_repos/Abdullah4345_BioCrypt/LICENSE", "downloaded_repos/Abdullah4345_BioCrypt/README.md", "downloaded_repos/Abdullah4345_BioCrypt/SECURITY.md"], "skipped": [{"path": "downloaded_repos/Abdullah4345_BioCrypt/powerpoint overview.pptx", "reason": "exceeded_size_limit"}]}, "time": {"rules": [], "rules_parse_time": 2.0032880306243896, "profiling_times": {"config_time": 6.430749893188477, "core_time": 6.328185319900513, "ignores_time": 0.0022704601287841797, "total_time": 12.76240348815918}, "parsing_time": {"total_time": 0.12895917892456055, "per_file_time": {"mean": 0.021493196487426758, "std_dev": 0.00023909538451031645}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 3.9644763469696045, "per_file_time": {"mean": 0.07928952693939206, "std_dev": 0.2428091935013569}, "very_slow_stats": {"time_ratio": 0.8889817364084512, "count_ratio": 0.02}, "very_slow_files": [{"fpath": "downloaded_repos/Abdullah4345_BioCrypt/BioCrypt-Personal-V0.4 Source-code/main.py", "ftime": 3.5243470668792725}]}, "matching_time": {"total_time": 2.9358644485473633, "per_file_and_rule_time": {"mean": 0.029067964837102604, "std_dev": 0.009719363600828201}, "very_slow_stats": {"time_ratio": 0.7081250728850662, "count_ratio": 0.07920792079207921}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/Abdullah4345_BioCrypt/BioCrypt-Personal-V0.4 Source-code/main.py", "rule_id": "python.lang.security.dangerous-system-call.dangerous-system-call", "time": 0.11672115325927734}, {"fpath": "downloaded_repos/Abdullah4345_BioCrypt/BioCrypt-Personal-V0.4 Source-code/main.py", "rule_id": "python.django.security.injection.code.user-eval-format-string.user-eval-format-string", "time": 0.11871099472045898}, {"fpath": "downloaded_repos/Abdullah4345_BioCrypt/BioCrypt-Personal-V0.4 Source-code/main.py", "rule_id": "python.django.security.injection.code.user-exec-format-string.user-exec-format-string", "time": 0.12880516052246094}, {"fpath": "downloaded_repos/Abdullah4345_BioCrypt/BioCrypt-Personal-V0.4 Source-code/main.py", "rule_id": "python.django.security.injection.ssrf.ssrf-injection-requests.ssrf-injection-requests", "time": 0.14586997032165527}, {"fpath": "downloaded_repos/Abdullah4345_BioCrypt/BioCrypt-Personal-V0.4 Source-code/main.py", "rule_id": "python.django.security.injection.command.command-injection-os-system.command-injection-os-system", "time": 0.18104100227355957}, {"fpath": "downloaded_repos/Abdullah4345_BioCrypt/BioCrypt-Personal-V0.4 Source-code/main.py", "rule_id": "python.django.security.injection.path-traversal.path-traversal-open.path-traversal-open", "time": 0.22644686698913574}, {"fpath": "downloaded_repos/Abdullah4345_BioCrypt/BioCrypt-Personal-V0.4 Source-code/main.py", "rule_id": "python.django.security.injection.request-data-write.request-data-write", "time": 0.25847911834716797}, {"fpath": "downloaded_repos/Abdullah4345_BioCrypt/BioCrypt-Personal-V0.4 Source-code/main.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.9028849601745605}]}, "tainting_time": {"total_time": 0.41611313819885254, "per_def_and_rule_time": {"mean": 0.0004947837552899554, "std_dev": 2.0218845615061148e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089364160}, "engine_requested": "OSS", "skipped_rules": []}