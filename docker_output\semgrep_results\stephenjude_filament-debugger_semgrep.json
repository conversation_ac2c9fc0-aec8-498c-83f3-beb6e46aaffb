{"version": "1.130.0", "results": [], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/stephenjude_filament-debugger/.github/workflows/run-tests.yml", "start": {"line": 39, "col": 38, "offset": 938}, "end": {"line": 39, "col": 57, "offset": 957}}, {"path": "downloaded_repos/stephenjude_filament-debugger/.github/workflows/run-tests.yml", "start": {"line": 40, "col": 24, "offset": 938}, "end": {"line": 40, "col": 43, "offset": 957}}]], "message": "Syntax error at line downloaded_repos/stephenjude_filament-debugger/.github/workflows/run-tests.yml:39:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ runner.tool_cache` was unexpected", "path": "downloaded_repos/stephenjude_filament-debugger/.github/workflows/run-tests.yml", "spans": [{"file": "downloaded_repos/stephenjude_filament-debugger/.github/workflows/run-tests.yml", "start": {"line": 39, "col": 38, "offset": 938}, "end": {"line": 39, "col": 57, "offset": 957}}, {"file": "downloaded_repos/stephenjude_filament-debugger/.github/workflows/run-tests.yml", "start": {"line": 40, "col": 24, "offset": 938}, "end": {"line": 40, "col": 43, "offset": 957}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/stephenjude_filament-debugger/.github/workflows/run-tests.yml", "start": {"line": 44, "col": 53, "offset": 1125}, "end": {"line": 44, "col": 69, "offset": 1141}}, {"path": "downloaded_repos/stephenjude_filament-debugger/.github/workflows/run-tests.yml", "start": {"line": 44, "col": 97, "offset": 1125}, "end": {"line": 44, "col": 115, "offset": 1143}}, {"path": "downloaded_repos/stephenjude_filament-debugger/.github/workflows/run-tests.yml", "start": {"line": 45, "col": 19, "offset": 1125}, "end": {"line": 45, "col": 22, "offset": 1128}}]], "message": "Syntax error at line downloaded_repos/stephenjude_filament-debugger/.github/workflows/run-tests.yml:44:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ matrix.laravel` was unexpected", "path": "downloaded_repos/stephenjude_filament-debugger/.github/workflows/run-tests.yml", "spans": [{"file": "downloaded_repos/stephenjude_filament-debugger/.github/workflows/run-tests.yml", "start": {"line": 44, "col": 53, "offset": 1125}, "end": {"line": 44, "col": 69, "offset": 1141}}, {"file": "downloaded_repos/stephenjude_filament-debugger/.github/workflows/run-tests.yml", "start": {"line": 44, "col": 97, "offset": 1125}, "end": {"line": 44, "col": 115, "offset": 1143}}, {"file": "downloaded_repos/stephenjude_filament-debugger/.github/workflows/run-tests.yml", "start": {"line": 45, "col": 19, "offset": 1125}, "end": {"line": 45, "col": 22, "offset": 1128}}]}], "paths": {"scanned": ["downloaded_repos/stephenjude_filament-debugger/.editorconfig", "downloaded_repos/stephenjude_filament-debugger/.gitattributes", "downloaded_repos/stephenjude_filament-debugger/.github/ISSUE_TEMPLATE/config.yml", "downloaded_repos/stephenjude_filament-debugger/.github/dependabot.yml", "downloaded_repos/stephenjude_filament-debugger/.github/workflows/dependabot-auto-merge.yml", "downloaded_repos/stephenjude_filament-debugger/.github/workflows/fix-php-code-style-issues.yml", "downloaded_repos/stephenjude_filament-debugger/.github/workflows/run-tests.yml", "downloaded_repos/stephenjude_filament-debugger/.github/workflows/update-changelog.yml", "downloaded_repos/stephenjude_filament-debugger/.gitignore", "downloaded_repos/stephenjude_filament-debugger/CHANGELOG.md", "downloaded_repos/stephenjude_filament-debugger/LICENSE.md", "downloaded_repos/stephenjude_filament-debugger/README.md", "downloaded_repos/stephenjude_filament-debugger/art/banner.jpg", "downloaded_repos/stephenjude_filament-debugger/art/screen1.png", "downloaded_repos/stephenjude_filament-debugger/composer.json", "downloaded_repos/stephenjude_filament-debugger/phpunit.xml.dist", "downloaded_repos/stephenjude_filament-debugger/pint.json", "downloaded_repos/stephenjude_filament-debugger/src/DebuggerPlugin.php", "downloaded_repos/stephenjude_filament-debugger/src/DebuggerServiceProvider.php", "downloaded_repos/stephenjude_filament-debugger/src/Traits/HasAuthorization.php", "downloaded_repos/stephenjude_filament-debugger/src/Traits/HasGroup.php", "downloaded_repos/stephenjude_filament-debugger/src/Traits/HasHorizon.php", "downloaded_repos/stephenjude_filament-debugger/src/Traits/HasPulse.php", "downloaded_repos/stephenjude_filament-debugger/src/Traits/HasTelescope.php"], "skipped": [{"path": "downloaded_repos/stephenjude_filament-debugger/.github/workflows/run-tests.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/stephenjude_filament-debugger/tests/Common/AdminPanelProvider.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/stephenjude_filament-debugger/tests/DebuggerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/stephenjude_filament-debugger/tests/Pest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/stephenjude_filament-debugger/tests/TestCase.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.568004846572876, "profiling_times": {"config_time": 5.510118246078491, "core_time": 2.124868869781494, "ignores_time": 0.0016026496887207031, "total_time": 7.637360334396362}, "parsing_time": {"total_time": 0.21936941146850586, "per_file_time": {"mean": 0.014624627431233723, "std_dev": 1.693503205135332e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.4851195812225342, "per_file_time": {"mean": 0.007700310813056098, "std_dev": 0.00013849648022392784}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.11827778816223145, "per_file_and_rule_time": {"mean": 0.0006160301466782888, "std_dev": 2.0403109633270685e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.0, "per_def_and_rule_time": {"mean": 0.0, "std_dev": 0.0}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090729792}, "engine_requested": "OSS", "skipped_rules": []}