{"version": "1.130.0", "results": [], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/stephenjude_filament-debugger/.github/workflows/run-tests.yml", "start": {"line": 39, "col": 38, "offset": 938}, "end": {"line": 39, "col": 57, "offset": 957}}, {"path": "downloaded_repos/stephenjude_filament-debugger/.github/workflows/run-tests.yml", "start": {"line": 40, "col": 24, "offset": 938}, "end": {"line": 40, "col": 43, "offset": 957}}]], "message": "Syntax error at line downloaded_repos/stephenjude_filament-debugger/.github/workflows/run-tests.yml:39:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ runner.tool_cache` was unexpected", "path": "downloaded_repos/stephenjude_filament-debugger/.github/workflows/run-tests.yml", "spans": [{"file": "downloaded_repos/stephenjude_filament-debugger/.github/workflows/run-tests.yml", "start": {"line": 39, "col": 38, "offset": 938}, "end": {"line": 39, "col": 57, "offset": 957}}, {"file": "downloaded_repos/stephenjude_filament-debugger/.github/workflows/run-tests.yml", "start": {"line": 40, "col": 24, "offset": 938}, "end": {"line": 40, "col": 43, "offset": 957}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/stephenjude_filament-debugger/.github/workflows/run-tests.yml", "start": {"line": 44, "col": 53, "offset": 1125}, "end": {"line": 44, "col": 69, "offset": 1141}}, {"path": "downloaded_repos/stephenjude_filament-debugger/.github/workflows/run-tests.yml", "start": {"line": 44, "col": 97, "offset": 1125}, "end": {"line": 44, "col": 115, "offset": 1143}}, {"path": "downloaded_repos/stephenjude_filament-debugger/.github/workflows/run-tests.yml", "start": {"line": 45, "col": 19, "offset": 1125}, "end": {"line": 45, "col": 22, "offset": 1128}}]], "message": "Syntax error at line downloaded_repos/stephenjude_filament-debugger/.github/workflows/run-tests.yml:44:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ matrix.laravel` was unexpected", "path": "downloaded_repos/stephenjude_filament-debugger/.github/workflows/run-tests.yml", "spans": [{"file": "downloaded_repos/stephenjude_filament-debugger/.github/workflows/run-tests.yml", "start": {"line": 44, "col": 53, "offset": 1125}, "end": {"line": 44, "col": 69, "offset": 1141}}, {"file": "downloaded_repos/stephenjude_filament-debugger/.github/workflows/run-tests.yml", "start": {"line": 44, "col": 97, "offset": 1125}, "end": {"line": 44, "col": 115, "offset": 1143}}, {"file": "downloaded_repos/stephenjude_filament-debugger/.github/workflows/run-tests.yml", "start": {"line": 45, "col": 19, "offset": 1125}, "end": {"line": 45, "col": 22, "offset": 1128}}]}], "paths": {"scanned": ["downloaded_repos/stephenjude_filament-debugger/.editorconfig", "downloaded_repos/stephenjude_filament-debugger/.gitattributes", "downloaded_repos/stephenjude_filament-debugger/.github/ISSUE_TEMPLATE/config.yml", "downloaded_repos/stephenjude_filament-debugger/.github/dependabot.yml", "downloaded_repos/stephenjude_filament-debugger/.github/workflows/dependabot-auto-merge.yml", "downloaded_repos/stephenjude_filament-debugger/.github/workflows/fix-php-code-style-issues.yml", "downloaded_repos/stephenjude_filament-debugger/.github/workflows/run-tests.yml", "downloaded_repos/stephenjude_filament-debugger/.github/workflows/update-changelog.yml", "downloaded_repos/stephenjude_filament-debugger/.gitignore", "downloaded_repos/stephenjude_filament-debugger/CHANGELOG.md", "downloaded_repos/stephenjude_filament-debugger/LICENSE.md", "downloaded_repos/stephenjude_filament-debugger/README.md", "downloaded_repos/stephenjude_filament-debugger/art/banner.jpg", "downloaded_repos/stephenjude_filament-debugger/art/screen1.png", "downloaded_repos/stephenjude_filament-debugger/composer.json", "downloaded_repos/stephenjude_filament-debugger/phpunit.xml.dist", "downloaded_repos/stephenjude_filament-debugger/pint.json", "downloaded_repos/stephenjude_filament-debugger/src/DebuggerPlugin.php", "downloaded_repos/stephenjude_filament-debugger/src/DebuggerServiceProvider.php", "downloaded_repos/stephenjude_filament-debugger/src/Traits/HasAuthorization.php", "downloaded_repos/stephenjude_filament-debugger/src/Traits/HasGroup.php", "downloaded_repos/stephenjude_filament-debugger/src/Traits/HasHorizon.php", "downloaded_repos/stephenjude_filament-debugger/src/Traits/HasPulse.php", "downloaded_repos/stephenjude_filament-debugger/src/Traits/HasTelescope.php"], "skipped": [{"path": "downloaded_repos/stephenjude_filament-debugger/.github/workflows/run-tests.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/stephenjude_filament-debugger/tests/Common/AdminPanelProvider.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/stephenjude_filament-debugger/tests/DebuggerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/stephenjude_filament-debugger/tests/Pest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/stephenjude_filament-debugger/tests/TestCase.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6589701175689697, "profiling_times": {"config_time": 5.8287436962127686, "core_time": 2.229440927505493, "ignores_time": 0.002239227294921875, "total_time": 8.061338901519775}, "parsing_time": {"total_time": 0.2577040195465088, "per_file_time": {"mean": 0.01718026796976725, "std_dev": 3.376605983450847e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.5469715595245361, "per_file_time": {"mean": 0.008682088246421207, "std_dev": 0.0001921950298191906}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.1416184902191162, "per_file_and_rule_time": {"mean": 0.0007375963032245635, "std_dev": 3.371635928103855e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.0, "per_def_and_rule_time": {"mean": 0.0, "std_dev": 0.0}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1085719040}, "engine_requested": "OSS", "skipped_rules": []}