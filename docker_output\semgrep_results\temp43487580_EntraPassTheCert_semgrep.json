{"version": "1.130.0", "results": [{"check_id": "python.requests.security.disabled-cert-validation.disabled-cert-validation", "path": "downloaded_repos/temp43487580_EntraPassTheCert/entraptc.py", "start": {"line": 176, "col": 20, "offset": 5774}, "end": {"line": 176, "col": 141, "offset": 5895}, "extra": {"message": "Certificate verification has been explicitly disabled. This permits insecure connections to insecure servers. Re-enable certification validation.", "fix": "requests.post(f'https://login.microsoftonline.com/common/oauth2/token', data=request_data, proxies=proxies, verify=True)", "metadata": {"cwe": ["CWE-295: Improper Certificate Validation"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A07:2021 - Identification and Authentication Failures"], "references": ["https://stackoverflow.com/questions/41740361/is-it-safe-to-disable-ssl-certificate-verification-in-pythonss-requests-lib"], "category": "security", "technology": ["requests"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/python.requests.security.disabled-cert-validation.disabled-cert-validation", "shortlink": "https://sg.run/AlYp"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.insecure-hash-algorithms.insecure-hash-algorithm-sha1", "path": "downloaded_repos/temp43487580_EntraPassTheCert/modules/kerberos/krb5.py", "start": {"line": 43, "col": 64, "offset": 1875}, "end": {"line": 43, "col": 82, "offset": 1893}, "extra": {"message": "Detected SHA1 hash algorithm which is considered insecure. SHA1 is not collision resistant and is therefore not suitable as a cryptographic signature. Use SHA256 or SHA3 instead.", "fix": "hashlib.sha256(data)", "metadata": {"source-rule-url": "https://github.com/PyCQA/bandit/blob/d5f8fa0d89d7b11442fc6ec80ca42953974354c8/bandit/blacklists/calls.py#L59", "cwe": ["CWE-327: Use of a Broken or Risky Cryptographic Algorithm"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "bandit-code": "B303", "asvs": {"control_id": "6.2.2 Insecure Custom Algorithm", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x14-V6-Cryptography.md#v62-algorithms", "section": "V6 Stored Cryptography Verification Requirements", "version": "4"}, "references": ["https://www.schneier.com/blog/archives/2012/10/when_will_we_se.html", "https://www.trendmicro.com/vinfo/us/security/news/vulnerabilities-and-exploits/sha-1-collision-signals-the-end-of-the-algorithm-s-viability", "http://2012.sharcs.org/slides/stevens.pdf", "https://pycryptodome.readthedocs.io/en/latest/src/hash/sha3_256.html"], "category": "security", "technology": ["python"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/python.lang.security.insecure-hash-algorithms.insecure-hash-algorithm-sha1", "shortlink": "https://sg.run/ydYx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.insecure-hash-algorithms.insecure-hash-algorithm-sha1", "path": "downloaded_repos/temp43487580_EntraPassTheCert/modules/kerberos/krb5.py", "start": {"line": 71, "col": 31, "offset": 3017}, "end": {"line": 71, "col": 60, "offset": 3046}, "extra": {"message": "Detected SHA1 hash algorithm which is considered insecure. SHA1 is not collision resistant and is therefore not suitable as a cryptographic signature. Use SHA256 or SHA3 instead.", "fix": "hashlib.sha256(req_body.dump())", "metadata": {"source-rule-url": "https://github.com/PyCQA/bandit/blob/d5f8fa0d89d7b11442fc6ec80ca42953974354c8/bandit/blacklists/calls.py#L59", "cwe": ["CWE-327: Use of a Broken or Risky Cryptographic Algorithm"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "bandit-code": "B303", "asvs": {"control_id": "6.2.2 Insecure Custom Algorithm", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x14-V6-Cryptography.md#v62-algorithms", "section": "V6 Stored Cryptography Verification Requirements", "version": "4"}, "references": ["https://www.schneier.com/blog/archives/2012/10/when_will_we_se.html", "https://www.trendmicro.com/vinfo/us/security/news/vulnerabilities-and-exploits/sha-1-collision-signals-the-end-of-the-algorithm-s-viability", "http://2012.sharcs.org/slides/stevens.pdf", "https://pycryptodome.readthedocs.io/en/latest/src/hash/sha3_256.html"], "category": "security", "technology": ["python"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/python.lang.security.insecure-hash-algorithms.insecure-hash-algorithm-sha1", "shortlink": "https://sg.run/ydYx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.insecure-hash-algorithms.insecure-hash-algorithm-sha1", "path": "downloaded_repos/temp43487580_EntraPassTheCert/modules/kerberos/krb5.py", "start": {"line": 132, "col": 25, "offset": 5742}, "end": {"line": 132, "col": 66, "offset": 5783}, "extra": {"message": "Detected SHA1 hash algorithm which is considered insecure. SHA1 is not collision resistant and is therefore not suitable as a cryptographic signature. Use SHA256 or SHA3 instead.", "fix": "hashlib.sha256(bytes([currentNum]) + value)", "metadata": {"source-rule-url": "https://github.com/PyCQA/bandit/blob/d5f8fa0d89d7b11442fc6ec80ca42953974354c8/bandit/blacklists/calls.py#L59", "cwe": ["CWE-327: Use of a Broken or Risky Cryptographic Algorithm"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "bandit-code": "B303", "asvs": {"control_id": "6.2.2 Insecure Custom Algorithm", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x14-V6-Cryptography.md#v62-algorithms", "section": "V6 Stored Cryptography Verification Requirements", "version": "4"}, "references": ["https://www.schneier.com/blog/archives/2012/10/when_will_we_se.html", "https://www.trendmicro.com/vinfo/us/security/news/vulnerabilities-and-exploits/sha-1-collision-signals-the-end-of-the-algorithm-s-viability", "http://2012.sharcs.org/slides/stevens.pdf", "https://pycryptodome.readthedocs.io/en/latest/src/hash/sha3_256.html"], "category": "security", "technology": ["python"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/python.lang.security.insecure-hash-algorithms.insecure-hash-algorithm-sha1", "shortlink": "https://sg.run/ydYx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.ssl.no-set-ciphers.no-set-ciphers", "path": "downloaded_repos/temp43487580_EntraPassTheCert/modules/rdp/connection.py", "start": {"line": 283, "col": 6, "offset": 11758}, "end": {"line": 283, "col": 44, "offset": 11796}, "extra": {"message": "The 'ssl' module disables insecure cipher suites by default. Therefore, use of 'set_ciphers()' should only be used when you have very specialized requirements. Otherwise, you risk lowering the security of the SSL channel.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-326: Inadequate Encryption Strength"], "asvs": {"control_id": "9.1.3 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v91-client-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "references": ["https://docs.python.org/3/library/ssl.html#cipher-selection", "https://docs.python.org/3/library/ssl.html#ssl.SSLContext.set_ciphers"], "category": "security", "technology": ["ssl"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.ssl.no-set-ciphers.no-set-ciphers", "shortlink": "https://sg.run/0Q0v"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/temp43487580_EntraPassTheCert/modules/rdp/credsspnative.py", "start": {"line": 195, "col": 5, "offset": 6428}, "end": {"line": 195, "col": 77, "offset": 6500}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret 'CredSSP - sending initial auth token: %s' % result.native being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/temp43487580_EntraPassTheCert/modules/rdp/credsspnative.py", "start": {"line": 200, "col": 6, "offset": 6648}, "end": {"line": 200, "col": 72, "offset": 6714}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret 'CredSSP - got token from server: %s' % tdata.native being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/temp43487580_EntraPassTheCert/modules/rdp/credsspnative.py", "start": {"line": 294, "col": 7, "offset": 10662}, "end": {"line": 294, "col": 72, "offset": 10727}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret 'CredSSP - sending credentials: %s' % result.native being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.pycryptodome.security.insecure-cipher-algorithm-rc4.insecure-cipher-algorithm-rc4", "path": "downloaded_repos/temp43487580_EntraPassTheCert/modules/rpc/rpcrt.py", "start": {"line": 1136, "col": 35, "offset": 46449}, "end": {"line": 1136, "col": 68, "offset": 46482}, "extra": {"message": "Detected ARC4 cipher algorithm which is considered insecure. This algorithm is not cryptographically secure and can be reversed easily. Use secure stream ciphers such as ChaCha20, XChaCha20 and Salsa20, or a block cipher such as AES with a block size of 128 bits. When using a block cipher, use a modern mode of operation that also provides authentication, such as GCM.", "metadata": {"source-rule-url": "https://github.com/PyCQA/bandit/blob/d5f8fa0d89d7b11442fc6ec80ca42953974354c8/bandit/blacklists/calls.py#L84", "cwe": ["CWE-327: Use of a Broken or Risky Cryptographic Algorithm"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "bandit-code": "B304", "references": ["https://cwe.mitre.org/data/definitions/326.html", "https://www.pycryptodome.org/src/cipher/cipher"], "category": "security", "technology": ["pycryptodome"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "HIGH", "functional-categories": ["crypto::search::symmetric-algorithm::pycryptodome", "crypto::search::symmetric-algorithm::pycryptodomex"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/python.pycryptodome.security.insecure-cipher-algorithm-rc4.insecure-cipher-algorithm-rc4", "shortlink": "https://sg.run/Eo6N"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.pycryptodome.security.insecure-cipher-algorithm-rc4.insecure-cipher-algorithm-rc4", "path": "downloaded_repos/temp43487580_EntraPassTheCert/modules/rpc/rpcrt.py", "start": {"line": 1138, "col": 35, "offset": 46586}, "end": {"line": 1138, "col": 68, "offset": 46619}, "extra": {"message": "Detected ARC4 cipher algorithm which is considered insecure. This algorithm is not cryptographically secure and can be reversed easily. Use secure stream ciphers such as ChaCha20, XChaCha20 and Salsa20, or a block cipher such as AES with a block size of 128 bits. When using a block cipher, use a modern mode of operation that also provides authentication, such as GCM.", "metadata": {"source-rule-url": "https://github.com/PyCQA/bandit/blob/d5f8fa0d89d7b11442fc6ec80ca42953974354c8/bandit/blacklists/calls.py#L84", "cwe": ["CWE-327: Use of a Broken or Risky Cryptographic Algorithm"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "bandit-code": "B304", "references": ["https://cwe.mitre.org/data/definitions/326.html", "https://www.pycryptodome.org/src/cipher/cipher"], "category": "security", "technology": ["pycryptodome"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "HIGH", "functional-categories": ["crypto::search::symmetric-algorithm::pycryptodome", "crypto::search::symmetric-algorithm::pycryptodomex"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/python.pycryptodome.security.insecure-cipher-algorithm-rc4.insecure-cipher-algorithm-rc4", "shortlink": "https://sg.run/Eo6N"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.pycryptodome.security.insecure-cipher-algorithm-rc4.insecure-cipher-algorithm-rc4", "path": "downloaded_repos/temp43487580_EntraPassTheCert/modules/rpc/rpcrt.py", "start": {"line": 1146, "col": 34, "offset": 47070}, "end": {"line": 1146, "col": 67, "offset": 47103}, "extra": {"message": "Detected ARC4 cipher algorithm which is considered insecure. This algorithm is not cryptographically secure and can be reversed easily. Use secure stream ciphers such as ChaCha20, XChaCha20 and Salsa20, or a block cipher such as AES with a block size of 128 bits. When using a block cipher, use a modern mode of operation that also provides authentication, such as GCM.", "metadata": {"source-rule-url": "https://github.com/PyCQA/bandit/blob/d5f8fa0d89d7b11442fc6ec80ca42953974354c8/bandit/blacklists/calls.py#L84", "cwe": ["CWE-327: Use of a Broken or Risky Cryptographic Algorithm"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "bandit-code": "B304", "references": ["https://cwe.mitre.org/data/definitions/326.html", "https://www.pycryptodome.org/src/cipher/cipher"], "category": "security", "technology": ["pycryptodome"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "HIGH", "functional-categories": ["crypto::search::symmetric-algorithm::pycryptodome", "crypto::search::symmetric-algorithm::pycryptodomex"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/python.pycryptodome.security.insecure-cipher-algorithm-rc4.insecure-cipher-algorithm-rc4", "shortlink": "https://sg.run/Eo6N"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/temp43487580_EntraPassTheCert/LICENSE", "downloaded_repos/temp43487580_EntraPassTheCert/README.md", "downloaded_repos/temp43487580_EntraPassTheCert/entraptc.py", "downloaded_repos/temp43487580_EntraPassTheCert/modules/kerberos/PkinitAsnNew.py", "downloaded_repos/temp43487580_EntraPassTheCert/modules/kerberos/__init__.py", "downloaded_repos/temp43487580_EntraPassTheCert/modules/kerberos/impacketTGS.py", "downloaded_repos/temp43487580_EntraPassTheCert/modules/kerberos/krb5.py", "downloaded_repos/temp43487580_EntraPassTheCert/modules/negoex/helper.py", "downloaded_repos/temp43487580_EntraPassTheCert/modules/negoex/packets.py", "downloaded_repos/temp43487580_EntraPassTheCert/modules/negoex/structs.py", "downloaded_repos/temp43487580_EntraPassTheCert/modules/rdp/connection.py", "downloaded_repos/temp43487580_EntraPassTheCert/modules/rdp/credssp.py", "downloaded_repos/temp43487580_EntraPassTheCert/modules/rdp/credsspnative.py", "downloaded_repos/temp43487580_EntraPassTheCert/modules/rdp/kerberos.py", "downloaded_repos/temp43487580_EntraPassTheCert/modules/rdp/rdp.py", "downloaded_repos/temp43487580_EntraPassTheCert/modules/rdp/spnegoex.py", "downloaded_repos/temp43487580_EntraPassTheCert/modules/rdp/spnegoexnative.py", "downloaded_repos/temp43487580_EntraPassTheCert/modules/rpc/__init__.py", "downloaded_repos/temp43487580_EntraPassTheCert/modules/rpc/rpcrt.py", "downloaded_repos/temp43487580_EntraPassTheCert/modules/rpc/transport.py", "downloaded_repos/temp43487580_EntraPassTheCert/modules/smb/smb3.py", "downloaded_repos/temp43487580_EntraPassTheCert/modules/smb/smbconnection.py", "downloaded_repos/temp43487580_EntraPassTheCert/modules/winrm/__init__.py", "downloaded_repos/temp43487580_EntraPassTheCert/modules/winrm/encryption.py", "downloaded_repos/temp43487580_EntraPassTheCert/modules/winrm/exceptions.py", "downloaded_repos/temp43487580_EntraPassTheCert/modules/winrm/protocol.py", "downloaded_repos/temp43487580_EntraPassTheCert/modules/winrm/transport.py", "downloaded_repos/temp43487580_EntraPassTheCert/requirements.txt"], "skipped": [{"path": "downloaded_repos/temp43487580_EntraPassTheCert/modules/winrm/vendor/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/temp43487580_EntraPassTheCert/modules/winrm/vendor/__pycache__/__init__.cpython-312.pyc", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/temp43487580_EntraPassTheCert/modules/winrm/vendor/__pycache__/__init__.cpython-313.pyc", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/temp43487580_EntraPassTheCert/modules/winrm/vendor/requests_kerberos/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/temp43487580_EntraPassTheCert/modules/winrm/vendor/requests_kerberos/compat.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/temp43487580_EntraPassTheCert/modules/winrm/vendor/requests_kerberos/exceptions.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/temp43487580_EntraPassTheCert/modules/winrm/vendor/requests_kerberos/kerberos_.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/temp43487580_EntraPassTheCert/modules/winrm/vendor/requests_pku2u/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/temp43487580_EntraPassTheCert/modules/winrm/vendor/requests_pku2u/__pycache__/__init__.cpython-312.pyc", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/temp43487580_EntraPassTheCert/modules/winrm/vendor/requests_pku2u/__pycache__/__init__.cpython-313.pyc", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/temp43487580_EntraPassTheCert/modules/winrm/vendor/requests_pku2u/__pycache__/pku2u.cpython-312.pyc", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/temp43487580_EntraPassTheCert/modules/winrm/vendor/requests_pku2u/__pycache__/pku2u.cpython-313.pyc", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/temp43487580_EntraPassTheCert/modules/winrm/vendor/requests_pku2u/pku2u.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.9846100807189941, "profiling_times": {"config_time": 7.014141082763672, "core_time": 6.627300024032593, "ignores_time": 0.0020494461059570312, "total_time": 13.644580602645874}, "parsing_time": {"total_time": 1.3910956382751465, "per_file_time": {"mean": 0.05564382553100586, "std_dev": 0.0053394671623951595}, "very_slow_stats": {"time_ratio": 0.24898066354304385, "count_ratio": 0.04}, "very_slow_files": [{"fpath": "downloaded_repos/temp43487580_EntraPassTheCert/modules/rdp/connection.py", "ftime": 0.3463559150695801}]}, "scanning_time": {"total_time": 17.577305793762207, "per_file_time": {"mean": 0.2170037752316322, "std_dev": 0.3655249691258682}, "very_slow_stats": {"time_ratio": 0.5780471757011679, "count_ratio": 0.04938271604938271}, "very_slow_files": [{"fpath": "downloaded_repos/temp43487580_EntraPassTheCert/modules/smb/smbconnection.py", "ftime": 1.5667500495910645}, {"fpath": "downloaded_repos/temp43487580_EntraPassTheCert/modules/rpc/rpcrt.py", "ftime": 2.1698520183563232}, {"fpath": "downloaded_repos/temp43487580_EntraPassTheCert/modules/rdp/connection.py", "ftime": 2.5793449878692627}, {"fpath": "downloaded_repos/temp43487580_EntraPassTheCert/modules/smb/smb3.py", "ftime": 3.844564914703369}]}, "matching_time": {"total_time": 9.394228458404541, "per_file_and_rule_time": {"mean": 0.025389806644336602, "std_dev": 0.0038801079747469076}, "very_slow_stats": {"time_ratio": 0.5008980959209183, "count_ratio": 0.05675675675675676}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/temp43487580_EntraPassTheCert/modules/smb/smb3.py", "rule_id": "python.django.security.injection.command.command-injection-os-system.command-injection-os-system", "time": 0.20592403411865234}, {"fpath": "downloaded_repos/temp43487580_EntraPassTheCert/modules/rdp/connection.py", "rule_id": "python.django.security.injection.path-traversal.path-traversal-open.path-traversal-open", "time": 0.20746707916259766}, {"fpath": "downloaded_repos/temp43487580_EntraPassTheCert/entraptc.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.2159709930419922}, {"fpath": "downloaded_repos/temp43487580_EntraPassTheCert/modules/rpc/rpcrt.py", "rule_id": "python.lang.security.dangerous-system-call.dangerous-system-call", "time": 0.22712206840515137}, {"fpath": "downloaded_repos/temp43487580_EntraPassTheCert/modules/rdp/connection.py", "rule_id": "python.lang.security.dangerous-system-call.dangerous-system-call", "time": 0.2466418743133545}, {"fpath": "downloaded_repos/temp43487580_EntraPassTheCert/modules/rpc/rpcrt.py", "rule_id": "python.django.security.injection.path-traversal.path-traversal-open.path-traversal-open", "time": 0.2796809673309326}, {"fpath": "downloaded_repos/temp43487580_EntraPassTheCert/modules/smb/smb3.py", "rule_id": "python.django.security.injection.path-traversal.path-traversal-open.path-traversal-open", "time": 0.2803618907928467}, {"fpath": "downloaded_repos/temp43487580_EntraPassTheCert/modules/winrm/protocol.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.3243849277496338}, {"fpath": "downloaded_repos/temp43487580_EntraPassTheCert/modules/smb/smb3.py", "rule_id": "python.lang.security.dangerous-system-call.dangerous-system-call", "time": 0.3795328140258789}, {"fpath": "downloaded_repos/temp43487580_EntraPassTheCert/modules/smb/smb3.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.777595043182373}]}, "tainting_time": {"total_time": 3.6720962524414062, "per_def_and_rule_time": {"mean": 0.0010002986250180893, "std_dev": 1.1825479352808895e-05}, "very_slow_stats": {"time_ratio": 0.08820035569665226, "count_ratio": 0.001362026695723236}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/temp43487580_EntraPassTheCert/modules/rdp/credsspnative.py", "fline": 155, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.053375959396362305}, {"fpath": "downloaded_repos/temp43487580_EntraPassTheCert/modules/smb/smbconnection.py", "fline": 307, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.06061100959777832}, {"fpath": "downloaded_repos/temp43487580_EntraPassTheCert/modules/rpc/rpcrt.py", "fline": 984, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.0635380744934082}, {"fpath": "downloaded_repos/temp43487580_EntraPassTheCert/entraptc.py", "fline": 132, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.07240414619445801}, {"fpath": "downloaded_repos/temp43487580_EntraPassTheCert/modules/smb/smb3.py", "fline": 945, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.07395100593566895}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089364160}, "engine_requested": "OSS", "skipped_rules": []}