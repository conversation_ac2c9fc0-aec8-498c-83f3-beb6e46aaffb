"""initial tables

Revision ID: 15c3881d9904
Revises: 
Create Date: 2022-05-29 00:33:35.100468

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '15c3881d9904'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.<PERSON>umn('username', sa.String(length=100), nullable=True),
    sa.Column('password', sa.String(length=100), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('username')
    )
    op.create_table('video',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('video_id', sa.String(length=32), nullable=False),
    sa.Column('extension', sa.String(length=8), nullable=False),
    sa.Column('path', sa.String(length=2048), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_video_path'), 'video', ['path'], unique=False)
    op.create_index(op.f('ix_video_video_id'), 'video', ['video_id'], unique=False)
    op.create_table('video_info',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('video_id', sa.String(length=32), nullable=False),
    sa.Column('title', sa.String(length=256), nullable=True),
    sa.Column('description', sa.String(length=2048), nullable=True),
    sa.Column('info', sa.Text(), nullable=True),
    sa.Column('duration', sa.Float(), nullable=True),
    sa.Column('width', sa.Integer(), nullable=True),
    sa.Column('height', sa.Integer(), nullable=True),
    sa.Column('private', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['video_id'], ['video.video_id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_video_info_title'), 'video_info', ['title'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_video_info_title'), table_name='video_info')
    op.drop_table('video_info')
    op.drop_index(op.f('ix_video_video_id'), table_name='video')
    op.drop_index(op.f('ix_video_path'), table_name='video')
    op.drop_table('video')
    op.drop_table('user')
    # ### end Alembic commands ###
