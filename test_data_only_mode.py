#!/usr/bin/env python3

"""
Test Data-Only Mode - Repository analysis without Semgrep scanning
"""

import os
import sys
import subprocess
import time

def main():
    print("📊 DATA-ONLY MODE TEST")
    print("=" * 40)
    print("Testing repository analysis without Semgrep scanning")
    print("This mode is much faster and focuses on repository metadata analysis")
    print()
    
    # Check GitHub token
    if not os.getenv("GITHUB_TOKEN"):
        print("❌ Please set GITHUB_TOKEN environment variable")
        print("   export GITHUB_TOKEN='your_token_here'")
        sys.exit(1)
    
    print("✅ GitHub token is set")
    print()
    
    # Test configurations
    test_configs = [
        {
            "name": "Quick Data Analysis",
            "args": ["--max-repos", "10", "--top-repos", "5", "--pages", "1", "--skip-semgrep"],
            "description": "Fast analysis of 10 repos, keep top 5"
        },
        {
            "name": "Comprehensive Data Analysis", 
            "args": ["--max-repos", "30", "--top-repos", "15", "--pages", "2", "--skip-semgrep"],
            "description": "Detailed analysis of 30 repos, keep top 15"
        }
    ]
    
    for i, config in enumerate(test_configs, 1):
        print(f"\n🧪 TEST {i}: {config['name']}")
        print(f"Description: {config['description']}")
        print(f"Arguments: {' '.join(config['args'])}")
        
        response = input(f"\nRun this test? (y/n): ").lower().strip()
        if response != 'y':
            print("Skipping...")
            continue
        
        try:
            cmd = [sys.executable, "unified_vulnerability_scanner.py"] + config["args"]
            
            print(f"\n🚀 Running: {' '.join(cmd)}")
            start_time = time.time()
            
            result = subprocess.run(cmd, check=True)
            
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"\n✅ Test completed in {duration:.1f} seconds")
            
            # Check results
            if os.path.exists("unified_vulnerability_results.csv"):
                with open("unified_vulnerability_results.csv", 'r', encoding='utf-8', errors='replace') as f:
                    lines = f.readlines()
                    print(f"📄 Results: {len(lines) - 1} repositories analyzed")
                    
                    if len(lines) > 1:
                        print("\n🏆 TOP 3 RESULTS:")
                        for j, line in enumerate(lines[1:4], 1):
                            try:
                                parts = line.strip().split(',')
                                if len(parts) >= 3:
                                    rank = parts[0]
                                    repo_name = parts[1]
                                    final_score = parts[15] if len(parts) > 15 else "N/A"
                                    health_score = parts[9] if len(parts) > 9 else "N/A"
                                    security_score = parts[10] if len(parts) > 10 else "N/A"
                                    dev_score = parts[11] if len(parts) > 11 else "N/A"
                                    
                                    print(f"  {rank}. {repo_name}")
                                    print(f"     Final Score: {final_score}")
                                    print(f"     Health: {health_score}, Security: {security_score}, Dev: {dev_score}")
                            except Exception as e:
                                print(f"  Error parsing line {j}: {e}")
                
                # Rename results file for this test
                new_name = f"data_only_test_{i}_results.csv"
                os.rename("unified_vulnerability_results.csv", new_name)
                print(f"📁 Results saved as: {new_name}")
            else:
                print("❌ No results file generated")
                
        except subprocess.CalledProcessError as e:
            print(f"\n❌ Test failed: {e}")
        except KeyboardInterrupt:
            print(f"\n⚠️ Test interrupted")
        
        print("\n" + "="*50)
    
    print(f"\n📊 DATA-ONLY MODE TESTING COMPLETE")
    print("\nBenefits of data-only mode:")
    print("✅ Much faster execution (no repository cloning)")
    print("✅ No Semgrep dependencies required")
    print("✅ Lower API rate limit usage")
    print("✅ Focus on repository health and practices")
    print("✅ Ideal for initial reconnaissance")
    
    print(f"\nUse cases:")
    print("🎯 Quick repository assessment")
    print("🎯 Development practice evaluation")
    print("🎯 Security hygiene auditing")
    print("🎯 Large-scale repository screening")

if __name__ == "__main__":
    main()
