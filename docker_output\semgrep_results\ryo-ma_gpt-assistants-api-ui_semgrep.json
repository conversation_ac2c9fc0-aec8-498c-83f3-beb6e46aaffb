{"version": "1.130.0", "results": [{"check_id": "dockerfile.security.missing-user-entrypoint.missing-user-entrypoint", "path": "downloaded_repos/ryo-ma_gpt-assistants-api-ui/Dockerfile", "start": {"line": 18, "col": 1, "offset": 323}, "end": {"line": 18, "col": 32, "offset": 354}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nENTRYPOINT [\"streamlit\", \"run\"]", "metadata": {"cwe": ["CWE-269: Improper Privilege Management"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user-entrypoint.missing-user-entrypoint", "shortlink": "https://sg.run/k281"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "dockerfile.security.missing-user.missing-user", "path": "downloaded_repos/ryo-ma_gpt-assistants-api-ui/Dockerfile", "start": {"line": 20, "col": 1, "offset": 356}, "end": {"line": 20, "col": 15, "offset": 370}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nCMD [\"app.py\"]", "metadata": {"cwe": ["CWE-250: Execution with Unnecessary Privileges"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user.missing-user", "shortlink": "https://sg.run/Gbvn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/ryo-ma_gpt-assistants-api-ui/.gitignore", "downloaded_repos/ryo-ma_gpt-assistants-api-ui/.streamlit/sample-secrets.toml", "downloaded_repos/ryo-ma_gpt-assistants-api-ui/Dockerfile", "downloaded_repos/ryo-ma_gpt-assistants-api-ui/LICENSE", "downloaded_repos/ryo-ma_gpt-assistants-api-ui/README.md", "downloaded_repos/ryo-ma_gpt-assistants-api-ui/app.py", "downloaded_repos/ryo-ma_gpt-assistants-api-ui/docker-compose.yml", "downloaded_repos/ryo-ma_gpt-assistants-api-ui/gpt_assistants_api_ui/README.md", "downloaded_repos/ryo-ma_gpt-assistants-api-ui/gpt_assistants_api_ui/__init__.py", "downloaded_repos/ryo-ma_gpt-assistants-api-ui/poetry.lock", "downloaded_repos/ryo-ma_gpt-assistants-api-ui/pyproject.toml", "downloaded_repos/ryo-ma_gpt-assistants-api-ui/tools.py"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.9897630214691162, "profiling_times": {"config_time": 6.230383634567261, "core_time": 2.746919631958008, "ignores_time": 0.14162564277648926, "total_time": 9.11973762512207}, "parsing_time": {"total_time": 0.05693197250366211, "per_file_time": {"mean": 0.011386394500732422, "std_dev": 2.227031984602945e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.32112884521484375, "per_file_time": {"mean": 0.011073408455684268, "std_dev": 0.00046631602390591434}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.04501509666442871, "per_file_and_rule_time": {"mean": 0.0008336129011931242, "std_dev": 2.531154209636472e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.008925676345825195, "per_def_and_rule_time": {"mean": 0.0003570270538330077, "std_dev": 2.2594985293835626e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}