# SPDX-License-Identifier: 0BSD

[codespell]

# Skip all translation files and a few other autogenerated files.
# The autotool files should have their typos fixed in the upstream, but
# until then we will blacklist them here.
# THANKS contains names, some do trigger codespell.
skip = *.po,*.pot,./po4a/man,./doc/api,./configure,./autom4te.cache,./m4/libtool.m4,./build-aux/depcomp,./build-aux/ltmain.sh,./build-aux/config.guess,./build-aux/config.rpath,./m4/po.m4,./build-aux/config.sub,THANKS

# Ignore false positive matching words. Ideally codespell would allow
# ignoring words for specific files, but that does not appear to be
# supported. Instead we need to hope we do not make these typos.
# Additionally, the ignored words must be specified lower-case even though
# some of the false positives only occurred upper-case.
# ANS - used as a variable name in xzmore.in.
# bu - groff syntax for creating a bullet list item, used in xz.1.
# te - groff syntax, used in xz.1.
# caf - command line options for tar example, used in xz.1.
ignore-words-list = ans,bu,te,caf

# Add extra dictionaries to help improvement comments, docs, etc.
builtin = clear,rare,informal,usage,names

# Always default to highest interactive level to avoid accidentally
# changing a false positive or picking the wrong replacement.
interactive = 3

# Ignore a URL with debbugs.
ignore-regex = \bhttps://debbugs\.gnu\.org\b
