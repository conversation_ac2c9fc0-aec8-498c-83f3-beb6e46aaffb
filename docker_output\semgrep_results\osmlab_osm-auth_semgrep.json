{"version": "1.130.0", "results": [{"check_id": "html.security.audit.missing-integrity.missing-integrity", "path": "downloaded_repos/osmlab_osm-auth/index.html", "start": {"line": 91, "col": 3, "offset": 1837}, "end": {"line": 91, "col": 63, "offset": 1897}, "extra": {"message": "This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-353: Missing Support for Integrity Check"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/html.security.audit.missing-integrity.missing-integrity", "shortlink": "https://sg.run/krXA"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/osmlab_osm-auth/index.html", "start": {"line": 112, "col": 5, "offset": 2646}, "end": {"line": 112, "col": 66, "offset": 2707}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/osmlab_osm-auth/.editorconfig", "downloaded_repos/osmlab_osm-auth/.gitattributes", "downloaded_repos/osmlab_osm-auth/.github/dependabot.yml", "downloaded_repos/osmlab_osm-auth/.github/workflows/build.yml", "downloaded_repos/osmlab_osm-auth/.gitignore", "downloaded_repos/osmlab_osm-auth/CHANGELOG.md", "downloaded_repos/osmlab_osm-auth/CODE_OF_CONDUCT.md", "downloaded_repos/osmlab_osm-auth/LICENSE.md", "downloaded_repos/osmlab_osm-auth/README.md", "downloaded_repos/osmlab_osm-auth/RELEASE.md", "downloaded_repos/osmlab_osm-auth/docs/GitHub-Mark-32px.png", "downloaded_repos/osmlab_osm-auth/docs/icon-avatar.svg", "downloaded_repos/osmlab_osm-auth/eslint.config.js", "downloaded_repos/osmlab_osm-auth/index.html", "downloaded_repos/osmlab_osm-auth/land.html", "downloaded_repos/osmlab_osm-auth/land_single.html", "downloaded_repos/osmlab_osm-auth/package.json", "downloaded_repos/osmlab_osm-auth/src/osm-auth.d.ts", "downloaded_repos/osmlab_osm-auth/src/osm-auth.mjs"], "skipped": [{"path": "downloaded_repos/osmlab_osm-auth/dist/osm-auth.cjs", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/osmlab_osm-auth/dist/osm-auth.cjs.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/osmlab_osm-auth/dist/osm-auth.iife.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/osmlab_osm-auth/dist/osm-auth.iife.js.map", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/osmlab_osm-auth/test/index.test.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.7666771411895752, "profiling_times": {"config_time": 6.829405307769775, "core_time": 2.698573589324951, "ignores_time": 0.0026273727416992188, "total_time": 9.531327486038208}, "parsing_time": {"total_time": 0.1263129711151123, "per_file_time": {"mean": 0.014034774568345811, "std_dev": 6.758638061663439e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.605461835861206, "per_file_time": {"mean": 0.012882166720451189, "std_dev": 0.002675814161843513}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.24747204780578613, "per_file_and_rule_time": {"mean": 0.0024261965471155497, "std_dev": 4.359834364627722e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.07628083229064941, "per_def_and_rule_time": {"mean": 0.0003331040711382071, "std_dev": 5.150714247189554e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089476480}, "engine_requested": "OSS", "skipped_rules": []}