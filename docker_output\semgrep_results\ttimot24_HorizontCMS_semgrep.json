{"version": "1.130.0", "results": [{"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/ttimot24_HorizontCMS/docker-compose.yml", "start": {"line": 4, "col": 3, "offset": 26}, "end": {"line": 4, "col": 10, "offset": 33}, "extra": {"message": "Service 'hcms_db' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/ttimot24_HorizontCMS/docker-compose.yml", "start": {"line": 4, "col": 3, "offset": 26}, "end": {"line": 4, "col": 10, "offset": 33}, "extra": {"message": "Service 'hcms_db' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/ttimot24_HorizontCMS/docker-compose.yml", "start": {"line": 23, "col": 3, "offset": 516}, "end": {"line": 23, "col": 13, "offset": 526}, "extra": {"message": "Service 'phpmyadmin' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/ttimot24_HorizontCMS/docker-compose.yml", "start": {"line": 23, "col": 3, "offset": 516}, "end": {"line": 23, "col": 13, "offset": 526}, "extra": {"message": "Service 'phpmyadmin' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/ttimot24_HorizontCMS/docker-compose.yml", "start": {"line": 37, "col": 3, "offset": 793}, "end": {"line": 37, "col": 17, "offset": 807}, "extra": {"message": "Service 'hcms_migration' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/ttimot24_HorizontCMS/docker-compose.yml", "start": {"line": 37, "col": 3, "offset": 793}, "end": {"line": 37, "col": 17, "offset": 807}, "extra": {"message": "Service 'hcms_migration' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/ttimot24_HorizontCMS/docker-compose.yml", "start": {"line": 53, "col": 3, "offset": 1328}, "end": {"line": 53, "col": 18, "offset": 1343}, "extra": {"message": "Service 'hcms_admin_user' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/ttimot24_HorizontCMS/docker-compose.yml", "start": {"line": 53, "col": 3, "offset": 1328}, "end": {"line": 53, "col": 18, "offset": 1343}, "extra": {"message": "Service 'hcms_admin_user' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/ttimot24_HorizontCMS/docker-compose.yml", "start": {"line": 71, "col": 3, "offset": 1912}, "end": {"line": 71, "col": 11, "offset": 1920}, "extra": {"message": "Service 'hcms_web' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/ttimot24_HorizontCMS/docker-compose.yml", "start": {"line": 71, "col": 3, "offset": 1912}, "end": {"line": 71, "col": 11, "offset": 1920}, "extra": {"message": "Service 'hcms_web' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/ttimot24_HorizontCMS/resources/vue/ts/components/file-manager/FileManager.ts", "start": {"line": 345, "col": 27, "offset": 11139}, "end": {"line": 345, "col": 82, "offset": 11194}, "extra": {"message": "RegExp() called with a `paramName` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.injection.tainted-filename.tainted-filename", "path": "downloaded_repos/ttimot24_HorizontCMS/server.php", "start": {"line": 17, "col": 33, "offset": 473}, "end": {"line": 17, "col": 59, "offset": 499}, "extra": {"message": "File name based on user input risks server-side request forgery.", "metadata": {"technology": ["php"], "category": "security", "cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "impact": "MEDIUM", "likelihood": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/php.lang.security.injection.tainted-filename.tainted-filename", "shortlink": "https://sg.run/Ayqp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/ttimot24_HorizontCMS/Dockerfile:1:\n Tok.NoTokenLocation(\"Could not convert (line 29, column 42) into a byte offset in file downloaded_repos/ttimot24_HorizontCMS/Dockerfile. Invalid location for token \\\"install\\\".\")", "path": "downloaded_repos/ttimot24_HorizontCMS/Dockerfile"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/ttimot24_HorizontCMS/resources/js/dragndrop.js:\n ", "path": "downloaded_repos/ttimot24_HorizontCMS/resources/js/dragndrop.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/ttimot24_HorizontCMS/resources/js/dragndrop.js:\n ", "path": "downloaded_repos/ttimot24_HorizontCMS/resources/js/dragndrop.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/ttimot24_HorizontCMS/resources/js/dragndrop.js:\n ", "path": "downloaded_repos/ttimot24_HorizontCMS/resources/js/dragndrop.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/ttimot24_HorizontCMS/resources/js/lock-screen.js:\n ", "path": "downloaded_repos/ttimot24_HorizontCMS/resources/js/lock-screen.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/ttimot24_HorizontCMS/resources/js/lock-screen.js:\n ", "path": "downloaded_repos/ttimot24_HorizontCMS/resources/js/lock-screen.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/ttimot24_HorizontCMS/resources/js/lock-screen.js:\n ", "path": "downloaded_repos/ttimot24_HorizontCMS/resources/js/lock-screen.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/ttimot24_HorizontCMS/resources/js/pages.js:\n ", "path": "downloaded_repos/ttimot24_HorizontCMS/resources/js/pages.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/ttimot24_HorizontCMS/resources/js/pages.js:\n ", "path": "downloaded_repos/ttimot24_HorizontCMS/resources/js/pages.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/ttimot24_HorizontCMS/resources/js/pages.js:\n ", "path": "downloaded_repos/ttimot24_HorizontCMS/resources/js/pages.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/ttimot24_HorizontCMS/resources/static/js/LockScreen.js:\n ", "path": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/LockScreen.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.vm-runincontext-injection.vm-runincontext-injection", "message": "Timeout when running javascript.aws-lambda.security.vm-runincontext-injection.vm-runincontext-injection on downloaded_repos/ttimot24_HorizontCMS/resources/static/js/LockScreen.js:\n ", "path": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/LockScreen.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/ttimot24_HorizontCMS/resources/static/js/LockScreen.js:\n ", "path": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/LockScreen.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/ttimot24_HorizontCMS/resources/static/js/dragndrop.js:\n ", "path": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/dragndrop.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/ttimot24_HorizontCMS/resources/static/js/dragndrop.js:\n ", "path": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/dragndrop.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/ttimot24_HorizontCMS/resources/static/js/dragndrop.js:\n ", "path": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/dragndrop.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/ttimot24_HorizontCMS/resources/static/js/filemanager.js:\n ", "path": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/filemanager.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/ttimot24_HorizontCMS/resources/static/js/filemanager.js:\n ", "path": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/filemanager.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/ttimot24_HorizontCMS/resources/static/js/filemanager.js:\n ", "path": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/filemanager.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/ttimot24_HorizontCMS/resources/static/js/lock-screen.js:\n ", "path": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/lock-screen.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/ttimot24_HorizontCMS/resources/static/js/lock-screen.js:\n ", "path": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/lock-screen.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/ttimot24_HorizontCMS/resources/static/js/lock-screen.js:\n ", "path": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/lock-screen.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/ttimot24_HorizontCMS/resources/static/js/pages.js:\n ", "path": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/pages.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/ttimot24_HorizontCMS/resources/static/js/pages.js:\n ", "path": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/pages.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/ttimot24_HorizontCMS/resources/static/js/pages.js:\n ", "path": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/pages.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/ttimot24_HorizontCMS/resources/static/js/texteditor.js:\n ", "path": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/texteditor.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.vm-runincontext-injection.vm-runincontext-injection", "message": "Timeout when running javascript.aws-lambda.security.vm-runincontext-injection.vm-runincontext-injection on downloaded_repos/ttimot24_HorizontCMS/resources/static/js/texteditor.js:\n ", "path": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/texteditor.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/ttimot24_HorizontCMS/resources/static/js/texteditor.js:\n ", "path": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/texteditor.js"}], "paths": {"scanned": ["downloaded_repos/ttimot24_HorizontCMS/.dockerignore", "downloaded_repos/ttimot24_HorizontCMS/.gitattributes", "downloaded_repos/ttimot24_HorizontCMS/.github/workflows/github-ci.yml", "downloaded_repos/ttimot24_HorizontCMS/.gitignore", "downloaded_repos/ttimot24_HorizontCMS/.htaccess", "downloaded_repos/ttimot24_HorizontCMS/Dockerfile", "downloaded_repos/ttimot24_HorizontCMS/LICENSE", "downloaded_repos/ttimot24_HorizontCMS/README.md", "downloaded_repos/ttimot24_HorizontCMS/SECURITY.md", "downloaded_repos/ttimot24_HorizontCMS/app/Console/Commands/InstallCommand.php", "downloaded_repos/ttimot24_HorizontCMS/app/Console/Commands/PluginCommand.php", "downloaded_repos/ttimot24_HorizontCMS/app/Console/Commands/ThemeCommand.php", "downloaded_repos/ttimot24_HorizontCMS/app/Console/Commands/UpgradeCommand.php", "downloaded_repos/ttimot24_HorizontCMS/app/Console/Commands/UserCommand.php", "downloaded_repos/ttimot24_HorizontCMS/app/Console/Commands/VersionCommand.php", "downloaded_repos/ttimot24_HorizontCMS/app/Console/Kernel.php", "downloaded_repos/ttimot24_HorizontCMS/app/Controllers/Auth/ForgotPasswordController.php", "downloaded_repos/ttimot24_HorizontCMS/app/Controllers/Auth/LoginController.php", "downloaded_repos/ttimot24_HorizontCMS/app/Controllers/Auth/RegisterController.php", "downloaded_repos/ttimot24_HorizontCMS/app/Controllers/Auth/ResetPasswordController.php", "downloaded_repos/ttimot24_HorizontCMS/app/Controllers/BlogpostCategoryController.php", "downloaded_repos/ttimot24_HorizontCMS/app/Controllers/BlogpostCommentController.php", "downloaded_repos/ttimot24_HorizontCMS/app/Controllers/BlogpostController.php", "downloaded_repos/ttimot24_HorizontCMS/app/Controllers/DashboardController.php", "downloaded_repos/ttimot24_HorizontCMS/app/Controllers/FileManagerController.php", "downloaded_repos/ttimot24_HorizontCMS/app/Controllers/HeaderImageController.php", "downloaded_repos/ttimot24_HorizontCMS/app/Controllers/InstallController.php", "downloaded_repos/ttimot24_HorizontCMS/app/Controllers/LogController.php", "downloaded_repos/ttimot24_HorizontCMS/app/Controllers/PageController.php", "downloaded_repos/ttimot24_HorizontCMS/app/Controllers/PluginController.php", "downloaded_repos/ttimot24_HorizontCMS/app/Controllers/ScheduleController.php", "downloaded_repos/ttimot24_HorizontCMS/app/Controllers/SearchController.php", "downloaded_repos/ttimot24_HorizontCMS/app/Controllers/SettingsController.php", "downloaded_repos/ttimot24_HorizontCMS/app/Controllers/ThemeController.php", "downloaded_repos/ttimot24_HorizontCMS/app/Controllers/Trait/UploadsImage.php", "downloaded_repos/ttimot24_HorizontCMS/app/Controllers/UpgradeController.php", "downloaded_repos/ttimot24_HorizontCMS/app/Controllers/UserController.php", "downloaded_repos/ttimot24_HorizontCMS/app/Controllers/UserRoleController.php", "downloaded_repos/ttimot24_HorizontCMS/app/Controllers/WebsiteController.php", "downloaded_repos/ttimot24_HorizontCMS/app/Exceptions/FileNotFoundException.php", "downloaded_repos/ttimot24_HorizontCMS/app/Exceptions/Handler.php", "downloaded_repos/ttimot24_HorizontCMS/app/Exceptions/WebsiteExceptionHandler.php", "downloaded_repos/ttimot24_HorizontCMS/app/Helpers/Functions/link.php", "downloaded_repos/ttimot24_HorizontCMS/app/Helpers/Html.php", "downloaded_repos/ttimot24_HorizontCMS/app/Helpers/Security.php", "downloaded_repos/ttimot24_HorizontCMS/app/Helpers/SocialLink.php", "downloaded_repos/ttimot24_HorizontCMS/app/Helpers/UrlManager.php", "downloaded_repos/ttimot24_HorizontCMS/app/HorizontCMS.php", "downloaded_repos/ttimot24_HorizontCMS/app/Http/Kernel.php", "downloaded_repos/ttimot24_HorizontCMS/app/Http/Middleware/AdminMiddleware.php", "downloaded_repos/ttimot24_HorizontCMS/app/Http/Middleware/BaseUrlMiddleware.php", "downloaded_repos/ttimot24_HorizontCMS/app/Http/Middleware/EmailConfigMiddleware.php", "downloaded_repos/ttimot24_HorizontCMS/app/Http/Middleware/EncryptCookies.php", "downloaded_repos/ttimot24_HorizontCMS/app/Http/Middleware/HttpsMiddleware.php", "downloaded_repos/ttimot24_HorizontCMS/app/Http/Middleware/InstallerMiddleware.php", "downloaded_repos/ttimot24_HorizontCMS/app/Http/Middleware/LogLastUserActivity.php", "downloaded_repos/ttimot24_HorizontCMS/app/Http/Middleware/MenuMiddleware.php", "downloaded_repos/ttimot24_HorizontCMS/app/Http/Middleware/NavbarPluginMiddleware.php", "downloaded_repos/ttimot24_HorizontCMS/app/Http/Middleware/PluginMiddleware.php", "downloaded_repos/ttimot24_HorizontCMS/app/Http/Middleware/RedirectIfAuthenticated.php", "downloaded_repos/ttimot24_HorizontCMS/app/Http/Middleware/SettingsMiddleware.php", "downloaded_repos/ttimot24_HorizontCMS/app/Http/Middleware/VerifyCsrfToken.php", "downloaded_repos/ttimot24_HorizontCMS/app/Http/Middleware/WebsiteMiddleware.php", "downloaded_repos/ttimot24_HorizontCMS/app/Http/RouteResolver.php", "downloaded_repos/ttimot24_HorizontCMS/app/Interfaces/PluginInterface.php", "downloaded_repos/ttimot24_HorizontCMS/app/Listeners/UserEventListener.php", "downloaded_repos/ttimot24_HorizontCMS/app/Model/Blogpost.php", "downloaded_repos/ttimot24_HorizontCMS/app/Model/BlogpostCategory.php", "downloaded_repos/ttimot24_HorizontCMS/app/Model/BlogpostComment.php", "downloaded_repos/ttimot24_HorizontCMS/app/Model/HeaderImage.php", "downloaded_repos/ttimot24_HorizontCMS/app/Model/Page.php", "downloaded_repos/ttimot24_HorizontCMS/app/Model/Plugin.php", "downloaded_repos/ttimot24_HorizontCMS/app/Model/ScheduledTask.php", "downloaded_repos/ttimot24_HorizontCMS/app/Model/Settings.php", "downloaded_repos/ttimot24_HorizontCMS/app/Model/Trait/Draftable.php", "downloaded_repos/ttimot24_HorizontCMS/app/Model/Trait/HasAuthor.php", "downloaded_repos/ttimot24_HorizontCMS/app/Model/Trait/HasImage.php", "downloaded_repos/ttimot24_HorizontCMS/app/Model/Trait/IsActive.php", "downloaded_repos/ttimot24_HorizontCMS/app/Model/Trait/PaginateSortAndFilter.php", "downloaded_repos/ttimot24_HorizontCMS/app/Model/User.php", "downloaded_repos/ttimot24_HorizontCMS/app/Model/UserRole.php", "downloaded_repos/ttimot24_HorizontCMS/app/Model/Visits.php", "downloaded_repos/ttimot24_HorizontCMS/app/Providers/AppServiceProvider.php", "downloaded_repos/ttimot24_HorizontCMS/app/Providers/AuthServiceProvider.php", "downloaded_repos/ttimot24_HorizontCMS/app/Providers/BroadcastServiceProvider.php", "downloaded_repos/ttimot24_HorizontCMS/app/Providers/EventServiceProvider.php", "downloaded_repos/ttimot24_HorizontCMS/app/Providers/Gates/PermissionsGate.php", "downloaded_repos/ttimot24_HorizontCMS/app/Providers/ModuleLoaderServiceProvider.php", "downloaded_repos/ttimot24_HorizontCMS/app/Providers/PluginServiceProvider.php", "downloaded_repos/ttimot24_HorizontCMS/app/Providers/RouteServiceProvider.php", "downloaded_repos/ttimot24_HorizontCMS/app/Providers/ThemeServiceProvider.php", "downloaded_repos/ttimot24_HorizontCMS/app/Services/BladeThemeEngine.php", "downloaded_repos/ttimot24_HorizontCMS/app/Services/DotEnvGenerator.php", "downloaded_repos/ttimot24_HorizontCMS/app/Services/SearchEngine.php", "downloaded_repos/ttimot24_HorizontCMS/app/Services/ShortCode.php", "downloaded_repos/ttimot24_HorizontCMS/app/Services/Theme.php", "downloaded_repos/ttimot24_HorizontCMS/app/Services/ThemeEngine.php", "downloaded_repos/ttimot24_HorizontCMS/app/Services/Website.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/auth/login.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/auth/passwords/email.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/auth/passwords/reset.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/auth/register.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/auth/welcome.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/blogposts/category/edit.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/blogposts/category/index.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/blogposts/category/view.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/blogposts/comments.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/blogposts/form.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/blogposts/index.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/blogposts/view.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/breadcrumb.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/confirm_delete.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/dashboard/index.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/errors/404.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/errors/503.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/errors/exception.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/errors/unauthorized.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/image_details.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/install/index.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/install/step1.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/install/step2.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/install/step3.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/install/step4.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/layout.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/media/embed.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/media/filemanager.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/media/fmframe.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/media/header_images.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/messages.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/navbar.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/pages/form.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/pages/index.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/plugin/index.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/plugin/store.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/search/index.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/settings/adminarea.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/settings/database.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/settings/index.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/settings/log.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/settings/schedules.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/settings/server.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/settings/socialmedia.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/settings/website.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/theme/index.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/theme/options.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/theme/store.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/upgrade/index.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/users/form.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/users/index.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/users/roles/create.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/users/roles/index.blade.php", "downloaded_repos/ttimot24_HorizontCMS/app/View/users/view.blade.php", "downloaded_repos/ttimot24_HorizontCMS/artisan", "downloaded_repos/ttimot24_HorizontCMS/bootstrap/app.php", "downloaded_repos/ttimot24_HorizontCMS/bootstrap/autoload.php", "downloaded_repos/ttimot24_HorizontCMS/bootstrap/cache/.gitignore", "downloaded_repos/ttimot24_HorizontCMS/bootstrap/loader.php", "downloaded_repos/ttimot24_HorizontCMS/composer.json", "downloaded_repos/ttimot24_HorizontCMS/composer.lock", "downloaded_repos/ttimot24_HorizontCMS/config/app.php", "downloaded_repos/ttimot24_HorizontCMS/config/auth.php", "downloaded_repos/ttimot24_HorizontCMS/config/broadcasting.php", "downloaded_repos/ttimot24_HorizontCMS/config/cache.php", "downloaded_repos/ttimot24_HorizontCMS/config/compile.php", "downloaded_repos/ttimot24_HorizontCMS/config/cors.php", "downloaded_repos/ttimot24_HorizontCMS/config/database.php", "downloaded_repos/ttimot24_HorizontCMS/config/filesystems.php", "downloaded_repos/ttimot24_HorizontCMS/config/hashing.php", "downloaded_repos/ttimot24_HorizontCMS/config/horizontcms.php", "downloaded_repos/ttimot24_HorizontCMS/config/laravel-menu/settings.php", "downloaded_repos/ttimot24_HorizontCMS/config/laravel-menu/views.php", "downloaded_repos/ttimot24_HorizontCMS/config/logging.php", "downloaded_repos/ttimot24_HorizontCMS/config/mail.php", "downloaded_repos/ttimot24_HorizontCMS/config/purifier.php", "downloaded_repos/ttimot24_HorizontCMS/config/queue.php", "downloaded_repos/ttimot24_HorizontCMS/config/self-update.php", "downloaded_repos/ttimot24_HorizontCMS/config/services.php", "downloaded_repos/ttimot24_HorizontCMS/config/session.php", "downloaded_repos/ttimot24_HorizontCMS/config/view.php", "downloaded_repos/ttimot24_HorizontCMS/database/.gitignore", "downloaded_repos/ttimot24_HorizontCMS/database/migrations/.gitkeep", "downloaded_repos/ttimot24_HorizontCMS/database/migrations/2016_10_12_000000_create_users_table.php", "downloaded_repos/ttimot24_HorizontCMS/database/migrations/2016_10_12_100000_create_password_resets_table.php", "downloaded_repos/ttimot24_HorizontCMS/database/migrations/2016_10_19_000000_create_blogposts_table.php", "downloaded_repos/ttimot24_HorizontCMS/database/migrations/2016_10_20_000000_create_blogpost_category_table.php", "downloaded_repos/ttimot24_HorizontCMS/database/migrations/2016_10_20_000000_create_blogpost_comment_table.php", "downloaded_repos/ttimot24_HorizontCMS/database/migrations/2016_10_20_000000_create_header_images_table.php", "downloaded_repos/ttimot24_HorizontCMS/database/migrations/2016_10_20_000000_create_page_table.php", "downloaded_repos/ttimot24_HorizontCMS/database/migrations/2016_10_20_000000_create_settings_table.php", "downloaded_repos/ttimot24_HorizontCMS/database/migrations/2016_10_20_000000_create_user_roles_table.php", "downloaded_repos/ttimot24_HorizontCMS/database/migrations/2016_10_20_000000_create_visits_table.php", "downloaded_repos/ttimot24_HorizontCMS/database/migrations/2016_12_14_000000_create_plugins_table.php", "downloaded_repos/ttimot24_HorizontCMS/database/migrations/2016_12_14_000000_create_schedules_table.php", "downloaded_repos/ttimot24_HorizontCMS/database/migrations/2025_06_16_135145_create_jobs_table.php", "downloaded_repos/ttimot24_HorizontCMS/database/seeders/.gitkeep", "downloaded_repos/ttimot24_HorizontCMS/database/seeders/ContentSeeder.php", "downloaded_repos/ttimot24_HorizontCMS/database/seeders/DatabaseSeeder.php", "downloaded_repos/ttimot24_HorizontCMS/database/seeders/SchedulerSeeder.php", "downloaded_repos/ttimot24_HorizontCMS/database/seeders/SettingsSeeder.php", "downloaded_repos/ttimot24_HorizontCMS/database/seeders/UserRolesSeeder.php", "downloaded_repos/ttimot24_HorizontCMS/docker-compose.yml", "downloaded_repos/ttimot24_HorizontCMS/eslint.config.mjs", "downloaded_repos/ttimot24_HorizontCMS/index.php", "downloaded_repos/ttimot24_HorizontCMS/package-lock.json", "downloaded_repos/ttimot24_HorizontCMS/package.json", "downloaded_repos/ttimot24_HorizontCMS/phpunit.xml", "downloaded_repos/ttimot24_HorizontCMS/plugins/.gitkeep", "downloaded_repos/ttimot24_HorizontCMS/resources/css/horizontcms-next.css", "downloaded_repos/ttimot24_HorizontCMS/resources/favicon.ico", "downloaded_repos/ttimot24_HorizontCMS/resources/images/icons/dir.png", "downloaded_repos/ttimot24_HorizontCMS/resources/images/icons/favicon16.png", "downloaded_repos/ttimot24_HorizontCMS/resources/images/icons/file.png", "downloaded_repos/ttimot24_HorizontCMS/resources/images/icons/mp3.png", "downloaded_repos/ttimot24_HorizontCMS/resources/images/icons/newspaper.png", "downloaded_repos/ttimot24_HorizontCMS/resources/images/icons/page.png", "downloaded_repos/ttimot24_HorizontCMS/resources/images/icons/plugin.png", "downloaded_repos/ttimot24_HorizontCMS/resources/images/icons/profile.png", "downloaded_repos/ttimot24_HorizontCMS/resources/js/dragndrop.js", "downloaded_repos/ttimot24_HorizontCMS/resources/js/filemanager.js", "downloaded_repos/ttimot24_HorizontCMS/resources/js/lock-screen.js", "downloaded_repos/ttimot24_HorizontCMS/resources/js/pages.js", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/en/actions.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/en/auth.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/en/blogpost.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/en/category.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/en/comment.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/en/dashboard.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/en/file.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/en/login.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/en/message.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/en/navbar.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/en/page.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/en/pagination.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/en/passwords.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/en/plugin.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/en/schedules.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/en/search.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/en/settings.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/en/theme.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/en/user.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/en/validation.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/hu/actions.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/hu/auth.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/hu/blogpost.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/hu/category.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/hu/comment.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/hu/dashboard.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/hu/file.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/hu/login.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/hu/message.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/hu/navbar.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/hu/page.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/hu/pagination.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/hu/passwords.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/hu/plugin.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/hu/schedules.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/hu/search.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/hu/settings.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/hu/theme.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/hu/user.php", "downloaded_repos/ttimot24_HorizontCMS/resources/lang/hu/validation.php", "downloaded_repos/ttimot24_HorizontCMS/resources/logo.png", "downloaded_repos/ttimot24_HorizontCMS/resources/mix-manifest.json", "downloaded_repos/ttimot24_HorizontCMS/resources/robots.txt", "downloaded_repos/ttimot24_HorizontCMS/resources/static/404.php", "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/LockScreen.js", "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/TextEditor.js", "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/dragndrop.js", "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/filemanager.js", "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/lock-screen.js", "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/pages.js", "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/texteditor.js", "downloaded_repos/ttimot24_HorizontCMS/resources/static/website_down.php", "downloaded_repos/ttimot24_HorizontCMS/resources/vue/sass/_variables.scss", "downloaded_repos/ttimot24_HorizontCMS/resources/vue/sass/horizontcms-next.scss", "downloaded_repos/ttimot24_HorizontCMS/resources/vue/ts/app.ts", "downloaded_repos/ttimot24_HorizontCMS/resources/vue/ts/components/delete-modal/DeleteModal.ts", "downloaded_repos/ttimot24_HorizontCMS/resources/vue/ts/components/delete-modal/DeleteModal.vue", "downloaded_repos/ttimot24_HorizontCMS/resources/vue/ts/components/file-manager/FileManager.ts", "downloaded_repos/ttimot24_HorizontCMS/resources/vue/ts/components/file-manager/FileManager.vue", "downloaded_repos/ttimot24_HorizontCMS/resources/vue/ts/components/lock-screen/LockScreen.ts", "downloaded_repos/ttimot24_HorizontCMS/resources/vue/ts/components/lock-screen/LockScreen.vue", "downloaded_repos/ttimot24_HorizontCMS/resources/vue/ts/components/text-editor/TextEditor.ts", "downloaded_repos/ttimot24_HorizontCMS/resources/vue/ts/components/text-editor/TextEditor.vue", "downloaded_repos/ttimot24_HorizontCMS/resources/vue/ts/dragndrop.js", "downloaded_repos/ttimot24_HorizontCMS/resources/vue/ts/environments/environment.ts", "downloaded_repos/ttimot24_HorizontCMS/resources/vue/ts/global.d.ts", "downloaded_repos/ttimot24_HorizontCMS/resources/vue/ts/index.d.ts", "downloaded_repos/ttimot24_HorizontCMS/resources/vue/ts/main.ts", "downloaded_repos/ttimot24_HorizontCMS/resources/vue/ts/model/FileManagerResponse.ts", "downloaded_repos/ttimot24_HorizontCMS/resources/vue/ts/pages.ts", "downloaded_repos/ttimot24_HorizontCMS/routes/api.php", "downloaded_repos/ttimot24_HorizontCMS/routes/backend.php", "downloaded_repos/ttimot24_HorizontCMS/routes/console.php", "downloaded_repos/ttimot24_HorizontCMS/routes/web.php", "downloaded_repos/ttimot24_HorizontCMS/server.php", "downloaded_repos/ttimot24_HorizontCMS/storage/app/.gitignore", "downloaded_repos/ttimot24_HorizontCMS/storage/app/public/.gitignore", "downloaded_repos/ttimot24_HorizontCMS/storage/framework/.gitignore", "downloaded_repos/ttimot24_HorizontCMS/storage/framework/sessions/.gitignore", "downloaded_repos/ttimot24_HorizontCMS/storage/framework/views/.gitignore", "downloaded_repos/ttimot24_HorizontCMS/storage/images/header_images/abovethecity.jpg", "downloaded_repos/ttimot24_HorizontCMS/themes/TheWright/app/Controllers/StartController.php", "downloaded_repos/ttimot24_HorizontCMS/themes/TheWright/footer.php", "downloaded_repos/ttimot24_HorizontCMS/themes/TheWright/header.php", "downloaded_repos/ttimot24_HorizontCMS/themes/TheWright/index.php", "downloaded_repos/ttimot24_HorizontCMS/themes/TheWright/messages.php", "downloaded_repos/ttimot24_HorizontCMS/themes/TheWright/page.example.php", "downloaded_repos/ttimot24_HorizontCMS/themes/TheWright/page_templates/blog.php", "downloaded_repos/ttimot24_HorizontCMS/themes/TheWright/preview.jpg", "downloaded_repos/ttimot24_HorizontCMS/themes/TheWright/sidebar.php", "downloaded_repos/ttimot24_HorizontCMS/themes/TheWright/sitelinks.php", "downloaded_repos/ttimot24_HorizontCMS/themes/TheWright/theme_info.xml", "downloaded_repos/ttimot24_HorizontCMS/tsconfig.json", "downloaded_repos/ttimot24_HorizontCMS/webpack.mix.js"], "skipped": [{"path": "downloaded_repos/ttimot24_HorizontCMS/Dockerfile", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/app/View/vendor/laravel-menu/bootstrap-navbar-items-right.blade.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/app/View/vendor/laravel-menu/bootstrap-navbar-items.blade.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/fonts/vendor/bootstrap-icons/bootstrap-icons.woff", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/fonts/vendor/bootstrap-icons/bootstrap-icons.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/fonts/vendor/fontawesome-4.7/fontawesome-webfont.eot", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/fonts/vendor/fontawesome-4.7/fontawesome-webfont.svg", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/fonts/vendor/fontawesome-4.7/fontawesome-webfont.ttf", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/fonts/vendor/fontawesome-4.7/fontawesome-webfont.woff", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/fonts/vendor/fontawesome-4.7/fontawesome-webfont.woff2", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/images/vendor/bootstrap-fileinput/loading-sm.gif", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/images/vendor/bootstrap-fileinput/loading.gif", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/js/app.js", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/js/dragndrop.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/js/lock-screen.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/js/main.js", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/js/pages.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/LockScreen.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/app.js", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/dragndrop.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/filemanager.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/lock-screen.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/pages.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/texteditor.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/tests/CreatesApplication.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/tests/ModelFactory.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/tests/TestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/tests/acceptance/LoginBrowserTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/tests/functional/LoginTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/tests/integration/BlogpostControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/tests/unit/AppTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/tests/unit/ExceptionHandlerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/tests/unit/Middleware/AdminMiddlewareTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/tests/unit/Middleware/InstallerMiddlewareTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/tests/unit/Middleware/SettingsMiddlewareTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/tests/unit/PluginModelTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/tests/unit/RouteResolverTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/tests/unit/SearchEngineTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/tests/unit/WidgetResolverTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/views/vendor/self-update/mails/update-available.blade.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/ttimot24_HorizontCMS/resources/views/vendor/self-update/self-update.blade.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.7183988094329834, "profiling_times": {"config_time": 5.97469687461853, "core_time": 26.94902229309082, "ignores_time": 0.0020122528076171875, "total_time": 32.927056550979614}, "parsing_time": {"total_time": 3.483264207839966, "per_file_time": {"mean": 0.013194182605454408, "std_dev": 0.005607048193808879}, "very_slow_stats": {"time_ratio": 0.4578782328575294, "count_ratio": 0.007575757575757576}, "very_slow_files": [{"fpath": "downloaded_repos/ttimot24_HorizontCMS/package-lock.json", "ftime": 0.49572086334228516}, {"fpath": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/TextEditor.js", "ftime": 1.0991899967193604}]}, "scanning_time": {"total_time": 119.90519165992737, "per_file_time": {"mean": 0.13263848634947722, "std_dev": 1.26260032717125}, "very_slow_stats": {"time_ratio": 0.8967001066225747, "count_ratio": 0.02101769911504425}, "very_slow_files": [{"fpath": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/TextEditor.js", "ftime": 3.7823379039764404}, {"fpath": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/filemanager.js", "ftime": 5.443077802658081}, {"fpath": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/lock-screen.js", "ftime": 5.508043050765991}, {"fpath": "downloaded_repos/ttimot24_HorizontCMS/resources/js/lock-screen.js", "ftime": 5.5608110427856445}, {"fpath": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/dragndrop.js", "ftime": 5.5777459144592285}, {"fpath": "downloaded_repos/ttimot24_HorizontCMS/resources/js/pages.js", "ftime": 5.5808680057525635}, {"fpath": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/pages.js", "ftime": 5.607755184173584}, {"fpath": "downloaded_repos/ttimot24_HorizontCMS/resources/js/dragndrop.js", "ftime": 5.677051782608032}, {"fpath": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/texteditor.js", "ftime": 18.890886068344116}, {"fpath": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/LockScreen.js", "ftime": 22.39789605140686}]}, "matching_time": {"total_time": 4.7305498123168945, "per_file_and_rule_time": {"mean": 0.008776530264038764, "std_dev": 0.0020166716137529016}, "very_slow_stats": {"time_ratio": 0.5475032865684033, "count_ratio": 0.01855287569573284}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/ttimot24_HorizontCMS/resources/vue/ts/components/file-manager/FileManager.ts", "rule_id": "javascript.lang.security.detect-eval-with-expression.detect-eval-with-expression", "time": 0.11166787147521973}, {"fpath": "downloaded_repos/ttimot24_HorizontCMS/package-lock.json", "rule_id": "json.aws.security.public-s3-policy-statement.public-s3-policy-statement", "time": 0.12647795677185059}, {"fpath": "downloaded_repos/ttimot24_HorizontCMS/resources/vue/ts/components/file-manager/FileManager.ts", "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.15431904792785645}, {"fpath": "downloaded_repos/ttimot24_HorizontCMS/package-lock.json", "rule_id": "json.aws.security.public-s3-bucket.public-s3-bucket", "time": 0.15442800521850586}, {"fpath": "downloaded_repos/ttimot24_HorizontCMS/resources/vue/ts/components/file-manager/FileManager.ts", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.15801286697387695}, {"fpath": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/TextEditor.js", "rule_id": "problem-based-packs.insecure-transport.js-node.using-http-server.using-http-server", "time": 0.15927386283874512}, {"fpath": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/TextEditor.js", "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.22206997871398926}, {"fpath": "downloaded_repos/ttimot24_HorizontCMS/resources/vue/ts/components/file-manager/FileManager.ts", "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 0.2725059986114502}, {"fpath": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/TextEditor.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.4598989486694336}, {"fpath": "downloaded_repos/ttimot24_HorizontCMS/resources/static/js/TextEditor.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.7713370323181152}]}, "tainting_time": {"total_time": 0.9151525497436523, "per_def_and_rule_time": {"mean": 0.0012400441053437023, "std_dev": 2.130621507921625e-05}, "very_slow_stats": {"time_ratio": 0.1196742520636052, "count_ratio": 0.0027100271002710027}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/ttimot24_HorizontCMS/resources/vue/ts/components/file-manager/FileManager.ts", "fline": 1, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.051168203353881836}, {"fpath": "downloaded_repos/ttimot24_HorizontCMS/resources/vue/ts/components/file-manager/FileManager.ts", "fline": 1, "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 0.058351993560791016}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}