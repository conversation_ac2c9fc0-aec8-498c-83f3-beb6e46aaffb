{"version": "1.130.0", "results": [{"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/Scrivito_scrivito_example_app_js/src/prerenderContent/reportError.js", "start": {"line": 3, "col": 15, "offset": 140}, "end": {"line": 3, "col": 45, "offset": 170}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/Scrivito_scrivito_example_app_js/src/prerenderContent/storeResult.js", "start": {"line": 8, "col": 30, "offset": 232}, "end": {"line": 8, "col": 39, "offset": 241}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/Scrivito_scrivito_example_app_js/src/prerenderContent/storeResult.js", "start": {"line": 8, "col": 41, "offset": 243}, "end": {"line": 8, "col": 49, "offset": 251}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/Scrivito_scrivito_example_app_js/src/catch_all_index.html", "start": {"line": 15, "col": 5, "offset": 0}, "end": {"line": 15, "col": 43, "offset": 38}}, {"path": "downloaded_repos/Scrivito_scrivito_example_app_js/src/catch_all_index.html", "start": {"line": 26, "col": 5, "offset": 0}, "end": {"line": 26, "col": 43, "offset": 38}}]], "message": "Syntax error at line downloaded_repos/Scrivito_scrivito_example_app_js/src/catch_all_index.html:15:\n `<%= htmlWebpackPlugin.tags.headTags %>` was unexpected", "path": "downloaded_repos/Scrivito_scrivito_example_app_js/src/catch_all_index.html", "spans": [{"file": "downloaded_repos/Scrivito_scrivito_example_app_js/src/catch_all_index.html", "start": {"line": 15, "col": 5, "offset": 0}, "end": {"line": 15, "col": 43, "offset": 38}}, {"file": "downloaded_repos/Scrivito_scrivito_example_app_js/src/catch_all_index.html", "start": {"line": 26, "col": 5, "offset": 0}, "end": {"line": 26, "col": 43, "offset": 38}}]}], "paths": {"scanned": ["downloaded_repos/Scrivito_scrivito_example_app_js/.circleci/config.yml", "downloaded_repos/Scrivito_scrivito_example_app_js/.env.example", "downloaded_repos/Scrivito_scrivito_example_app_js/.eslintrc", "downloaded_repos/Scrivito_scrivito_example_app_js/.gitignore", "downloaded_repos/Scrivito_scrivito_example_app_js/.nvmrc", "downloaded_repos/Scrivito_scrivito_example_app_js/.prettierrc.json", "downloaded_repos/Scrivito_scrivito_example_app_js/LICENSE", "downloaded_repos/Scrivito_scrivito_example_app_js/generator-scrivito/app/index.js", "downloaded_repos/Scrivito_scrivito_example_app_js/generator-scrivito/generators/obj/index.js", "downloaded_repos/Scrivito_scrivito_example_app_js/generator-scrivito/generators/obj/templates/X.scss.ejs", "downloaded_repos/Scrivito_scrivito_example_app_js/generator-scrivito/generators/obj/templates/XComponent.js.ejs", "downloaded_repos/Scrivito_scrivito_example_app_js/generator-scrivito/generators/obj/templates/XEditingConfig.js.ejs", "downloaded_repos/Scrivito_scrivito_example_app_js/generator-scrivito/generators/obj/templates/XObjClass.js.ejs", "downloaded_repos/Scrivito_scrivito_example_app_js/generator-scrivito/generators/widget/index.js", "downloaded_repos/Scrivito_scrivito_example_app_js/generator-scrivito/generators/widget/templates/X.scss.ejs", "downloaded_repos/Scrivito_scrivito_example_app_js/generator-scrivito/generators/widget/templates/XWidgetClass.js.ejs", "downloaded_repos/Scrivito_scrivito_example_app_js/generator-scrivito/generators/widget/templates/XWidgetComponent.js.ejs", "downloaded_repos/Scrivito_scrivito_example_app_js/generator-scrivito/generators/widget/templates/XWidgetEditingConfig.js.ejs", "downloaded_repos/Scrivito_scrivito_example_app_js/generator-scrivito/package-lock.json", "downloaded_repos/Scrivito_scrivito_example_app_js/generator-scrivito/package.json", "downloaded_repos/Scrivito_scrivito_example_app_js/netlify.toml", "downloaded_repos/Scrivito_scrivito_example_app_js/package-lock.json", "downloaded_repos/Scrivito_scrivito_example_app_js/package.json", "downloaded_repos/Scrivito_scrivito_example_app_js/public/_headers", "downloaded_repos/Scrivito_scrivito_example_app_js/public/_headersCsp.json", "downloaded_repos/Scrivito_scrivito_example_app_js/public/_redirects", "downloaded_repos/Scrivito_scrivito_example_app_js/public/js_snippets_before_body_end.js", "downloaded_repos/Scrivito_scrivito_example_app_js/public/js_snippets_head.js", "downloaded_repos/Scrivito_scrivito_example_app_js/public/robots.txt", "downloaded_repos/Scrivito_scrivito_example_app_js/readme.mdown", "downloaded_repos/Scrivito_scrivito_example_app_js/readme.prepackaged.mdown", "downloaded_repos/Scrivito_scrivito_example_app_js/src/App.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/AnimateOnReveal.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/AuthorImage.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/BlogPost/BlogPostAuthor.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/BlogPost/BlogPostDate.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/BlogPost/BlogPostMorePosts.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/BlogPost/BlogPostNavigation.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/BlogPost/BlogPostPreviewList.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/BlogPost/BlogPostTagList.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/CookieConsentBanner.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/CookieConsentContext.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/CurrentPageMetadata.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/ErrorBoundary.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/Footer.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/Icon.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/InPlaceEditingPlaceholder.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/Intercom.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/Navigation/CollapseToggle.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/Navigation/FullNavigation.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/Navigation/LandingPageNavigation.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/Navigation/Logo.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/Navigation/Nav.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/Navigation/NavChild.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/Navigation/NavigationSection.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/Navigation/ScrollToNextSectionLink.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/Navigation/Search.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/Navigation/currentPageNavigationOptions.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/Navigation.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/NotFoundErrorPage.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/SchemaDotOrg/dataFromAddressWidget.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/SchemaDotOrg/dataFromAuthor.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/SchemaDotOrg/dataFromBlog.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/SchemaDotOrg/dataFromBlogPost.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/SchemaDotOrg/dataFromEvent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/SchemaDotOrg/dataFromJob.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/SchemaDotOrg.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/ScrivitoExtensions/ColumnsEditorTab.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/ScrivitoExtensions/ContentProperty.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/ScrivitoExtensions/IconEditorTab/AllIcons.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/ScrivitoExtensions/IconEditorTab/IconSearch.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/ScrivitoExtensions/IconEditorTab/IconSearchResults.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/ScrivitoExtensions/IconEditorTab/SingleIcon.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/ScrivitoExtensions/IconEditorTab/fontAwesomeIcons.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/ScrivitoExtensions/IconEditorTab.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/ScrivitoExtensions/SocialCardsTab.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/ScrivitoExtensions/index.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/TagList.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/Tracking.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/WrapIfClassName.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/Author/AuthorComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/Author/AuthorEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/Author/AuthorObjClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/Blog/BlogComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/Blog/BlogEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/Blog/BlogObjClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/BlogPost/BlogPostComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/BlogPost/BlogPostEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/BlogPost/BlogPostObjClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/Download/DownloadEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/Download/DownloadObjClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/Event/EventComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/Event/EventEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/Event/EventObjClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/Homepage/HomepageComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/Homepage/HomepageEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/Homepage/HomepageObjClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/Image/ImageEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/Image/ImageObjClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/Job/JobComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/Job/JobEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/Job/JobObjClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/LandingPage/LandingPageComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/LandingPage/LandingPageEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/LandingPage/LandingPageObjClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/Page/PageComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/Page/PageEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/Page/PageObjClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/Redirect/RedirectComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/Redirect/RedirectEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/Redirect/RedirectObjClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/RedirectToUi/RedirectToUiComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/RedirectToUi/RedirectToUiEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/RedirectToUi/RedirectToUiObjClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/SearchResults/SearchInput.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/SearchResults/SearchResultItem.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/SearchResults/SearchResultItem.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/SearchResults/SearchResultsComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/SearchResults/SearchResultsEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/SearchResults/SearchResultsObjClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/SearchResults/SearchResultsTagList.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/SearchResults/ShowMoreButton.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/Video/VideoEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/Video/VideoObjClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/_defaultPageAttributes.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/_defaultPageEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/_metadataAttributes.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/_metadataEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/editingConfigs.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Objs/index.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/AddressWidget/AddressWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/AddressWidget/AddressWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/AddressWidget/AddressWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/BlogOverviewWidget/BlogOverviewWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/BlogOverviewWidget/BlogOverviewWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/BlogOverviewWidget/BlogOverviewWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/BoxWidget/BoxWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/BoxWidget/BoxWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/BoxWidget/BoxWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/ButtonWidget/ButtonWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/ButtonWidget/ButtonWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/ButtonWidget/ButtonWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/CarouselWidget/CarouselWidget.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/CarouselWidget/CarouselWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/CarouselWidget/CarouselWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/CarouselWidget/CarouselWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/ColumnContainerWidget/ColumnContainerWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/ColumnContainerWidget/ColumnContainerWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/ColumnContainerWidget/ColumnContainerWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/ColumnWidget/ColumnWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/ColumnWidget/ColumnWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/DividerWidget/DividerWidget.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/DividerWidget/DividerWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/DividerWidget/DividerWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/DividerWidget/DividerWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/EventOverviewWidget/EventOverviewWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/EventOverviewWidget/EventOverviewWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/EventOverviewWidget/EventOverviewWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/FactWidget/FactWidget.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/FactWidget/FactWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/FactWidget/FactWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/FactWidget/FactWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/FeaturePanelWidget/FeaturePanelWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/FeaturePanelWidget/FeaturePanelWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/FeaturePanelWidget/FeaturePanelWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/FormButtonWidget/FormButtonWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/FormButtonWidget/FormButtonWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/FormButtonWidget/FormButtonWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/FormCheckboxWidget/FormCheckboxWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/FormCheckboxWidget/FormCheckboxWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/FormCheckboxWidget/FormCheckboxWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/FormContainerWidget/FormContainerWidget.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/FormContainerWidget/FormContainerWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/FormContainerWidget/FormContainerWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/FormContainerWidget/FormContainerWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/FormContainerWidget/FormIdComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/FormContainerWidget/utils/getFieldName.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/FormContainerWidget/utils/getFormContainer.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/FormContainerWidget/utils/isCustomType.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/FormContainerWidget/utils/isFieldNameUnique.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/FormContainerWidget/utils/pseudoRandom32CharHex.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/FormContainerWidget/utils/scrollIntoView.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/FormContainerWidget/utils/validations/customFieldNameValidation.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/FormContainerWidget/utils/validations/insideFormContainerValidation.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/FormContainerWidget/utils/validations/typeValidation.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/FormHiddenFieldWidget/FormHiddenFieldWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/FormHiddenFieldWidget/FormHiddenFieldWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/FormHiddenFieldWidget/FormHiddenFieldWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/FormInputFieldWidget/FormInputFieldWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/FormInputFieldWidget/FormInputFieldWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/FormInputFieldWidget/FormInputFieldWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/GalleryWidget/GalleryWidget.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/GalleryWidget/GalleryWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/GalleryWidget/GalleryWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/GalleryWidget/GalleryWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/GoogleMapsWidget/GoogleMapsWidget.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/GoogleMapsWidget/GoogleMapsWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/GoogleMapsWidget/GoogleMapsWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/GoogleMapsWidget/GoogleMapsWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/GroupWidget/GroupWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/GroupWidget/GroupWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/GroupWidget/GroupWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/HeadlineWidget/HeadlineWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/HeadlineWidget/HeadlineWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/HeadlineWidget/HeadlineWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/IconContainerWidget/IconContainerWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/IconContainerWidget/IconContainerWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/IconContainerWidget/IconContainerWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/IconWidget/IconWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/IconWidget/IconWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/IconWidget/IconWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/ImageWidget/ImageWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/ImageWidget/ImageWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/ImageWidget/ImageWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/JobOverviewWidget/JobOverviewWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/JobOverviewWidget/JobOverviewWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/JobOverviewWidget/JobOverviewWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/LinkContainerWidget/LinkContainerWidget.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/LinkContainerWidget/LinkContainerWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/LinkContainerWidget/LinkContainerWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/LinkContainerWidget/LinkContainerWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/LinkWidget/LinkWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/LinkWidget/LinkWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/LinkWidget/LinkWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/PriceWidget/PriceWidget.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/PriceWidget/PriceWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/PriceWidget/PriceWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/PriceWidget/PriceWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/PricingSpecWidget/PricingSpecWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/PricingSpecWidget/PricingSpecWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/PricingSpecWidget/PricingSpecWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/PricingWidget/PricingWidget.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/PricingWidget/PricingWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/PricingWidget/PricingWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/PricingWidget/PricingWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/SectionWidget/SectionWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/SectionWidget/SectionWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/SectionWidget/SectionWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/SpaceWidget/SpaceWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/SpaceWidget/SpaceWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/SpaceWidget/SpaceWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/TableRowWidget/TableRowWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/TableRowWidget/TableRowWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/TableRowWidget/TableRowWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/TableWidget/TableWidget.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/TableWidget/TableWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/TableWidget/TableWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/TableWidget/TableWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/TestimonialSliderWidget/TestimonialSliderWidget.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/TestimonialSliderWidget/TestimonialSliderWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/TestimonialSliderWidget/TestimonialSliderWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/TestimonialSliderWidget/TestimonialSliderWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/TestimonialWidget/TestimonialWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/TestimonialWidget/TestimonialWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/TextWidget/TextWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/TextWidget/TextWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/TextWidget/TextWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/ThumbnailGalleryImageWidget/ThumbnailGalleryImageWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/ThumbnailGalleryImageWidget/ThumbnailGalleryImageWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/ThumbnailGalleryWidget/ThumbnailGalleryWidget.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/ThumbnailGalleryWidget/ThumbnailGalleryWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/ThumbnailGalleryWidget/ThumbnailGalleryWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/ThumbnailGalleryWidget/ThumbnailGalleryWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/TickListItemWidget/TickListItemWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/TickListItemWidget/TickListItemWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/TickListItemWidget/TickListItemWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/TickListWidget/TickListWidget.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/TickListWidget/TickListWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/TickListWidget/TickListWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/TickListWidget/TickListWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/VideoWidget/VideoWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/VideoWidget/VideoWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/VideoWidget/VideoWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/VideoWidget/videoPlaceholder.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/VimeoVideoWidget/VimeoVideoWidget.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/VimeoVideoWidget/VimeoVideoWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/VimeoVideoWidget/VimeoVideoWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/VimeoVideoWidget/VimeoVideoWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/YoutubeVideoWidget/YoutubeVideoWidget.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/YoutubeVideoWidget/YoutubeVideoWidgetClass.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/YoutubeVideoWidget/YoutubeVideoWidgetComponent.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/YoutubeVideoWidget/YoutubeVideoWidgetEditingConfig.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/editingConfigs.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/Widgets/index.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/_scrivito_extensions.html", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/fonts/Manrope-Bold-700.woff2", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/fonts/Manrope-Medium-500.woff2", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/fonts/fontawesome-webfont.eot", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/fonts/fontawesome-webfont.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/fonts/fontawesome-webfont.ttf", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/fonts/fontawesome-webfont.woff", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/fonts/fontawesome-webfont.woff2", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/address_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/arrow_next.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/arrow_prev.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/author_obj.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/blog_obj.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/blog_overview_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/blog_post_obj.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/box_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/button_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/carousel_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/column_container_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/column_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/cookie_consent_icon.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/divider_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/event_obj.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/event_overview_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/fact_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/favicon.png", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/feature_panel_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/form_button_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/form_checkbox_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/form_container_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/form_hidden_field_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/form_input_field_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/gallery_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/google_maps_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/group_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/headline_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/homepage_obj.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/icon_container_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/icon_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/image_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/job_obj.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/job_overview_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/landing_page_obj.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/link_list_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/link_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/page_obj.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/price_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/pricing_spec_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/pricing_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/redirect_obj.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/search_results_obj.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/section_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/space_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/table_row_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/table_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/testimonial_slider_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/testimonial_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/text_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/thumbnail_gallery_image_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/thumbnail_gallery_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/tick_list_item_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/tick_list_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/video_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/vimeo_video_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/images/youtube_video_widget.svg", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/_blog.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/_fontawesome.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/_grid_layout_editor.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/_icon_editor.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/_loader.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/_mixins.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/_navigation.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/_scrivito_extensions.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/_social_cards.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/_variables.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_accordion.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_alert.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_badge.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_breadcrumb.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_button-group.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_buttons.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_card.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_carousel.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_close.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_containers.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_dropdown.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_forms.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_functions.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_grid.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_helpers.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_images.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_list-group.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_mixins.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_modal.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_nav.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_navbar.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_offcanvas.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_pagination.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_placeholders.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_popover.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_progress.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_reboot.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_root.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_spinners.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_tables.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_toasts.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_tooltip.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_transitions.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_type.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_utilities.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/_variables.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/bootstrap-grid.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/bootstrap-reboot.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/bootstrap-utilities.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/bootstrap.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/forms/_floating-labels.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/forms/_form-check.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/forms/_form-control.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/forms/_form-range.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/forms/_form-select.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/forms/_form-text.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/forms/_input-group.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/forms/_labels.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/forms/_validation.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/helpers/_clearfix.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/helpers/_colored-links.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/helpers/_position.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/helpers/_ratio.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/helpers/_stacks.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/helpers/_stretched-link.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/helpers/_text-truncation.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/helpers/_visually-hidden.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/helpers/_vr.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/mixins/_alert.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/mixins/_backdrop.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/mixins/_border-radius.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/mixins/_box-shadow.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/mixins/_breakpoints.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/mixins/_buttons.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/mixins/_caret.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/mixins/_clearfix.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/mixins/_color-scheme.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/mixins/_container.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/mixins/_deprecate.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/mixins/_forms.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/mixins/_gradients.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/mixins/_grid.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/mixins/_image.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/mixins/_list-group.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/mixins/_lists.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/mixins/_pagination.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/mixins/_reset-text.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/mixins/_resize.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/mixins/_table-variants.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/mixins/_text-truncate.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/mixins/_transition.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/mixins/_utilities.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/mixins/_visually-hidden.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/utilities/_api.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/fontawesome/_animated.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/fontawesome/_bordered-pulled.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/fontawesome/_core.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/fontawesome/_fixed-width.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/fontawesome/_icons.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/fontawesome/_larger.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/fontawesome/_list.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/fontawesome/_mixins.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/fontawesome/_path.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/fontawesome/_rotated-flipped.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/fontawesome/_screen-reader.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/fontawesome/_stacked.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/fontawesome/_variables.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/index.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/variables/_colors.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/variables/_responsive.scss", "downloaded_repos/Scrivito_scrivito_example_app_js/src/catch_all_index.html", "downloaded_repos/Scrivito_scrivito_example_app_js/src/config/history.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/config/index.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/config/objClassForContentType.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/config/scrivito.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/config/scrivitoContentBrowser.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/config/windowScrivito.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/index.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/prerenderContent/contentHash.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/prerenderContent/extendRedirects.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/prerenderContent/filenameFromUrl.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/prerenderContent/generateHtml.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/prerenderContent/generatePreloadDump.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/prerenderContent/prerenderObj.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/prerenderContent/prerenderObjs.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/prerenderContent/prerenderSitemap.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/prerenderContent/reportError.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/prerenderContent/storeResult.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/prerender_content.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/scrivito_extensions.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/tracking.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/utils/alignmentClassName.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/utils/formatDate.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/utils/getMetadata.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/utils/googleMapsApiKey.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/utils/googleMapsImageUrl.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/utils/isImageObj.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/utils/isVideoObj.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/utils/navigateToBlogWithTag.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/utils/placeholderCss.js", "downloaded_repos/Scrivito_scrivito_example_app_js/src/utils/urlFromBinaryObj.js", "downloaded_repos/Scrivito_scrivito_example_app_js/webpack.config.js", "downloaded_repos/Scrivito_scrivito_example_app_js/webpack.config.production.js"], "skipped": [{"path": "downloaded_repos/Scrivito_scrivito_example_app_js/src/assets/stylesheets/bootstrap5/vendor/_rfs.scss", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Scrivito_scrivito_example_app_js/src/catch_all_index.html", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.8672139644622803, "profiling_times": {"config_time": 6.0354063510894775, "core_time": 5.105675458908081, "ignores_time": 0.0019414424896240234, "total_time": 11.143868684768677}, "parsing_time": {"total_time": 3.2495365142822266, "per_file_time": {"mean": 0.011482461181209273, "std_dev": 0.003448246738389839}, "very_slow_stats": {"time_ratio": 0.4167948685590655, "count_ratio": 0.007067137809187279}, "very_slow_files": [{"fpath": "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/ScrivitoExtensions/IconEditorTab/fontAwesomeIcons.js", "ftime": 0.5264151096343994}, {"fpath": "downloaded_repos/Scrivito_scrivito_example_app_js/package-lock.json", "ftime": 0.8279750347137451}]}, "scanning_time": {"total_time": 13.429026126861572, "per_file_time": {"mean": 0.010599073501863919, "std_dev": 0.003972292108971193}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 3.5678482055664062, "per_file_and_rule_time": {"mean": 0.0037955831974110727, "std_dev": 0.00013694476300596102}, "very_slow_stats": {"time_ratio": 0.12827331629761893, "count_ratio": 0.0031914893617021275}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/ScrivitoExtensions/IconEditorTab/fontAwesomeIcons.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.11113882064819336}, {"fpath": "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/ScrivitoExtensions/ColumnsEditorTab.js", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 0.11571192741394043}, {"fpath": "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/ScrivitoExtensions/IconEditorTab/fontAwesomeIcons.js", "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 0.23080897331237793}]}, "tainting_time": {"total_time": 1.0064971446990967, "per_def_and_rule_time": {"mean": 0.0013998569467303153, "std_dev": 1.2055557486527315e-05}, "very_slow_stats": {"time_ratio": 0.05355893740576636, "count_ratio": 0.0013908205841446453}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/Scrivito_scrivito_example_app_js/src/Components/ScrivitoExtensions/ContentProperty.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.053906917572021484}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1092521024}, "engine_requested": "OSS", "skipped_rules": []}