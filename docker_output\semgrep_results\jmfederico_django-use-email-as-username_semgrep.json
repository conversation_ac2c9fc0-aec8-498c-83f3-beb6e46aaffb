{"version": "1.130.0", "results": [{"check_id": "python.django.security.audit.unvalidated-password.unvalidated-password", "path": "downloaded_repos/jmfederico_django-use-email-as-username/django_use_email_as_username/models.py", "start": {"line": 20, "col": 9, "offset": 745}, "end": {"line": 20, "col": 36, "offset": 772}, "extra": {"message": "The password on 'user' is being set without validating the password. Call django.contrib.auth.password_validation.validate_password() with validation functions before setting the password. See https://docs.djangoproject.com/en/3.0/topics/auth/passwords/ for more information.", "fix": "if django.contrib.auth.password_validation.validate_password(password, user=user):\n            user.set_password(password)", "metadata": {"cwe": ["CWE-521: Weak Password Requirements"], "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://docs.djangoproject.com/en/3.0/topics/auth/passwords/#module-django.contrib.auth.password_validation"], "category": "security", "technology": ["django"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/python.django.security.audit.unvalidated-password.unvalidated-password", "shortlink": "https://sg.run/OPBL"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/jmfederico_django-use-email-as-username/.coveragerc", "downloaded_repos/jmfederico_django-use-email-as-username/.github/ISSUE_TEMPLATE.md", "downloaded_repos/jmfederico_django-use-email-as-username/.github/stale.yml", "downloaded_repos/jmfederico_django-use-email-as-username/.github/workflows/tests.yml", "downloaded_repos/jmfederico_django-use-email-as-username/.gitignore", "downloaded_repos/jmfederico_django-use-email-as-username/CONTRIBUTING.rst", "downloaded_repos/jmfederico_django-use-email-as-username/HISTORY.rst", "downloaded_repos/jmfederico_django-use-email-as-username/LICENSE", "downloaded_repos/jmfederico_django-use-email-as-username/README.rst", "downloaded_repos/jmfederico_django-use-email-as-username/django_use_email_as_username/__init__.py", "downloaded_repos/jmfederico_django-use-email-as-username/django_use_email_as_username/admin.py", "downloaded_repos/jmfederico_django-use-email-as-username/django_use_email_as_username/apps.py", "downloaded_repos/jmfederico_django-use-email-as-username/django_use_email_as_username/management/commands/app_template/__init__.py-tpl", "downloaded_repos/jmfederico_django-use-email-as-username/django_use_email_as_username/management/commands/app_template/admin.py-tpl", "downloaded_repos/jmfederico_django-use-email-as-username/django_use_email_as_username/management/commands/app_template/apps.py-tpl", "downloaded_repos/jmfederico_django-use-email-as-username/django_use_email_as_username/management/commands/app_template/migrations/__init__.py-tpl", "downloaded_repos/jmfederico_django-use-email-as-username/django_use_email_as_username/management/commands/app_template/models.py-tpl", "downloaded_repos/jmfederico_django-use-email-as-username/django_use_email_as_username/management/commands/create_custom_user_app.py", "downloaded_repos/jmfederico_django-use-email-as-username/django_use_email_as_username/models.py", "downloaded_repos/jmfederico_django-use-email-as-username/docs/<PERSON>file", "downloaded_repos/jmfederico_django-use-email-as-username/docs/conf.py", "downloaded_repos/jmfederico_django-use-email-as-username/docs/contributing.rst", "downloaded_repos/jmfederico_django-use-email-as-username/docs/history.rst", "downloaded_repos/jmfederico_django-use-email-as-username/docs/index.rst", "downloaded_repos/jmfederico_django-use-email-as-username/docs/readme.rst", "downloaded_repos/jmfederico_django-use-email-as-username/example/README.rst", "downloaded_repos/jmfederico_django-use-email-as-username/example/custom_user/__init__.py", "downloaded_repos/jmfederico_django-use-email-as-username/example/custom_user/admin.py", "downloaded_repos/jmfederico_django-use-email-as-username/example/custom_user/apps.py", "downloaded_repos/jmfederico_django-use-email-as-username/example/custom_user/migrations/0001_initial.py", "downloaded_repos/jmfederico_django-use-email-as-username/example/custom_user/migrations/__init__.py", "downloaded_repos/jmfederico_django-use-email-as-username/example/custom_user/models.py", "downloaded_repos/jmfederico_django-use-email-as-username/example/example/__init__.py", "downloaded_repos/jmfederico_django-use-email-as-username/example/example/settings.py", "downloaded_repos/jmfederico_django-use-email-as-username/example/example/urls.py", "downloaded_repos/jmfederico_django-use-email-as-username/example/example/wsgi.py", "downloaded_repos/jmfederico_django-use-email-as-username/example/manage.py", "downloaded_repos/jmfederico_django-use-email-as-username/manage.py", "downloaded_repos/jmfederico_django-use-email-as-username/pyproject.toml", "downloaded_repos/jmfederico_django-use-email-as-username/runtests.py", "downloaded_repos/jmfederico_django-use-email-as-username/tox.ini"], "skipped": [{"path": "downloaded_repos/jmfederico_django-use-email-as-username/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jmfederico_django-use-email-as-username/tests/apps/custom_user_test/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jmfederico_django-use-email-as-username/tests/apps/custom_user_test/admin.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jmfederico_django-use-email-as-username/tests/apps/custom_user_test/apps.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jmfederico_django-use-email-as-username/tests/apps/custom_user_test/migrations/0001_initial.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jmfederico_django-use-email-as-username/tests/apps/custom_user_test/migrations/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jmfederico_django-use-email-as-username/tests/apps/custom_user_test/models.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jmfederico_django-use-email-as-username/tests/settings.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jmfederico_django-use-email-as-username/tests/test.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/jmfederico_django-use-email-as-username/tests/urls.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.8004977703094482, "profiling_times": {"config_time": 6.402369260787964, "core_time": 2.729682683944702, "ignores_time": 0.0034067630767822266, "total_time": 9.136335372924805}, "parsing_time": {"total_time": 0.2852301597595215, "per_file_time": {"mean": 0.013582388559977215, "std_dev": 5.897943475925815e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.7366533279418945, "per_file_time": {"mean": 0.016860711921765973, "std_dev": 0.001192691993493932}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.18483829498291016, "per_file_and_rule_time": {"mean": 0.0010384173875444393, "std_dev": 8.107833286572692e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.02457404136657715, "per_def_and_rule_time": {"mean": 0.0005850962230137416, "std_dev": 1.4282695901807083e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089364160}, "engine_requested": "OSS", "skipped_rules": []}