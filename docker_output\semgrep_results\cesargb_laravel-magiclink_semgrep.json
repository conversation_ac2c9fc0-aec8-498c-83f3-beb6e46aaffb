{"version": "1.130.0", "results": [{"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/cesargb_laravel-magiclink/src/Actions/ResponseAction.php", "start": {"line": 76, "col": 36, "offset": 1705}, "end": {"line": 76, "col": 68, "offset": 1737}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/cesargb_laravel-magiclink/src/MagicLink.php", "start": {"line": 62, "col": 20, "offset": 1456}, "end": {"line": 62, "col": 54, "offset": 1490}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unserialize-use.unserialize-use", "path": "downloaded_repos/cesargb_laravel-magiclink/src/MagicLink.php", "start": {"line": 65, "col": 16, "offset": 1518}, "end": {"line": 65, "col": 35, "offset": 1537}, "extra": {"message": "Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers).", "metadata": {"references": ["https://www.php.net/manual/en/function.unserialize.php", "https://owasp.org/www-project-top-ten/2017/A8_2017-Insecure_Deserialization.html"], "category": "security", "technology": ["php"], "owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/php.lang.security.unserialize-use.unserialize-use", "shortlink": "https://sg.run/b24E"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/cesargb_laravel-magiclink/.github/workflows/tests.yml", "start": {"line": 46, "col": 53, "offset": 1056}, "end": {"line": 46, "col": 69, "offset": 1072}}, {"path": "downloaded_repos/cesargb_laravel-magiclink/.github/workflows/tests.yml", "start": {"line": 46, "col": 97, "offset": 1056}, "end": {"line": 46, "col": 115, "offset": 1074}}, {"path": "downloaded_repos/cesargb_laravel-magiclink/.github/workflows/tests.yml", "start": {"line": 47, "col": 19, "offset": 1056}, "end": {"line": 47, "col": 22, "offset": 1059}}]], "message": "Syntax error at line downloaded_repos/cesargb_laravel-magiclink/.github/workflows/tests.yml:46:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ matrix.laravel` was unexpected", "path": "downloaded_repos/cesargb_laravel-magiclink/.github/workflows/tests.yml", "spans": [{"file": "downloaded_repos/cesargb_laravel-magiclink/.github/workflows/tests.yml", "start": {"line": 46, "col": 53, "offset": 1056}, "end": {"line": 46, "col": 69, "offset": 1072}}, {"file": "downloaded_repos/cesargb_laravel-magiclink/.github/workflows/tests.yml", "start": {"line": 46, "col": 97, "offset": 1056}, "end": {"line": 46, "col": 115, "offset": 1074}}, {"file": "downloaded_repos/cesargb_laravel-magiclink/.github/workflows/tests.yml", "start": {"line": 47, "col": 19, "offset": 1056}, "end": {"line": 47, "col": 22, "offset": 1059}}]}], "paths": {"scanned": ["downloaded_repos/cesargb_laravel-magiclink/.gitattributes", "downloaded_repos/cesargb_laravel-magiclink/.github/dependabot.yml", "downloaded_repos/cesargb_laravel-magiclink/.github/release.yml", "downloaded_repos/cesargb_laravel-magiclink/.github/workflows/phpstan.yml", "downloaded_repos/cesargb_laravel-magiclink/.github/workflows/style-fix.yml", "downloaded_repos/cesargb_laravel-magiclink/.github/workflows/tests.yml", "downloaded_repos/cesargb_laravel-magiclink/.gitignore", "downloaded_repos/cesargb_laravel-magiclink/.php-cs-fixer.dist.php", "downloaded_repos/cesargb_laravel-magiclink/CONTRIBUTING.md", "downloaded_repos/cesargb_laravel-magiclink/LICENSE.md", "downloaded_repos/cesargb_laravel-magiclink/README.md", "downloaded_repos/cesargb_laravel-magiclink/composer.json", "downloaded_repos/cesargb_laravel-magiclink/config/magiclink.php", "downloaded_repos/cesargb_laravel-magiclink/databases/migrations/2017_07_06_000000_create_table_magic_links.php", "downloaded_repos/cesargb_laravel-magiclink/databases/migrations/2021_03_06_211907_add_access_code_to_magic_links_table.php", "downloaded_repos/cesargb_laravel-magiclink/databases/migrations/2024_12_25_000000_add_indexes_to_magic_links_table.php", "downloaded_repos/cesargb_laravel-magiclink/phpstan.neon.dist", "downloaded_repos/cesargb_laravel-magiclink/phpunit.postgres.xml", "downloaded_repos/cesargb_laravel-magiclink/phpunit.xml.dist", "downloaded_repos/cesargb_laravel-magiclink/resources/views/ask-for-access-code-form.blade.php", "downloaded_repos/cesargb_laravel-magiclink/routes/routes.php", "downloaded_repos/cesargb_laravel-magiclink/src/AccessCode.php", "downloaded_repos/cesargb_laravel-magiclink/src/Actions/ActionAbstract.php", "downloaded_repos/cesargb_laravel-magiclink/src/Actions/ControllerAction.php", "downloaded_repos/cesargb_laravel-magiclink/src/Actions/DownloadFileAction.php", "downloaded_repos/cesargb_laravel-magiclink/src/Actions/LoginAction.php", "downloaded_repos/cesargb_laravel-magiclink/src/Actions/ResponseAction.php", "downloaded_repos/cesargb_laravel-magiclink/src/Actions/ViewAction.php", "downloaded_repos/cesargb_laravel-magiclink/src/Controllers/MagicLinkController.php", "downloaded_repos/cesargb_laravel-magiclink/src/Events/MagicLinkWasCreated.php", "downloaded_repos/cesargb_laravel-magiclink/src/Events/MagicLinkWasDeleted.php", "downloaded_repos/cesargb_laravel-magiclink/src/Events/MagicLinkWasVisited.php", "downloaded_repos/cesargb_laravel-magiclink/src/MagicLink.php", "downloaded_repos/cesargb_laravel-magiclink/src/MagicLinkServiceProvider.php", "downloaded_repos/cesargb_laravel-magiclink/src/Middlewares/MagiclinkMiddleware.php", "downloaded_repos/cesargb_laravel-magiclink/src/Responses/AbortResponse.php", "downloaded_repos/cesargb_laravel-magiclink/src/Responses/RedirectResponse.php", "downloaded_repos/cesargb_laravel-magiclink/src/Responses/Response.php", "downloaded_repos/cesargb_laravel-magiclink/src/Responses/ResponseContract.php", "downloaded_repos/cesargb_laravel-magiclink/src/Responses/ViewResponse.php"], "skipped": [{"path": "downloaded_repos/cesargb_laravel-magiclink/.github/workflows/tests.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/cesargb_laravel-magiclink/tests/AccessCodeTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/cesargb_laravel-magiclink/tests/Actions/ControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/cesargb_laravel-magiclink/tests/Actions/DownloadFileTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/cesargb_laravel-magiclink/tests/Actions/LoginTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/cesargb_laravel-magiclink/tests/Actions/ResponseTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/cesargb_laravel-magiclink/tests/Actions/ViewTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/cesargb_laravel-magiclink/tests/ConfigTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/cesargb_laravel-magiclink/tests/Events/MagicLinkWasCreatedTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/cesargb_laravel-magiclink/tests/Events/MagicLinkWasVisitedTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/cesargb_laravel-magiclink/tests/FeatureTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/cesargb_laravel-magiclink/tests/Http/HttpHeadTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/cesargb_laravel-magiclink/tests/Http/HttpTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/cesargb_laravel-magiclink/tests/Http/HttpThrottleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/cesargb_laravel-magiclink/tests/MagicLinkDeleteTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/cesargb_laravel-magiclink/tests/MagicLinkTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/cesargb_laravel-magiclink/tests/Responses/AbortResponseTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/cesargb_laravel-magiclink/tests/Responses/RedirectResponseTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/cesargb_laravel-magiclink/tests/Responses/ResponseTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/cesargb_laravel-magiclink/tests/Responses/ViewResponseTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/cesargb_laravel-magiclink/tests/TestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/cesargb_laravel-magiclink/tests/TestSupport/CustomAutenticable.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/cesargb_laravel-magiclink/tests/TestSupport/CustomUserProvider.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/cesargb_laravel-magiclink/tests/TestSupport/MyController.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/cesargb_laravel-magiclink/tests/TestSupport/User.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/cesargb_laravel-magiclink/tests/stubs/resources/views/access-code-custom.blade.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/cesargb_laravel-magiclink/tests/stubs/resources/views/view.blade.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/cesargb_laravel-magiclink/tests/stubs/routes.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/cesargb_laravel-magiclink/tests/stubs/storage/app/text.txt", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/cesargb_laravel-magiclink/tests/stubs/storage/app_alternative/text_alternative.txt", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.7444870471954346, "profiling_times": {"config_time": 6.109984397888184, "core_time": 2.5334715843200684, "ignores_time": 0.0017642974853515625, "total_time": 8.646114826202393}, "parsing_time": {"total_time": 0.303727388381958, "per_file_time": {"mean": 0.00949148088693619, "std_dev": 7.200892136477455e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.6988997459411621, "per_file_time": {"mean": 0.006240176303046092, "std_dev": 0.00013152071715625454}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.14802837371826172, "per_file_and_rule_time": {"mean": 0.0008001533714500634, "std_dev": 3.78662102548831e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.0, "per_def_and_rule_time": {"mean": 0.0, "std_dev": 0.0}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}