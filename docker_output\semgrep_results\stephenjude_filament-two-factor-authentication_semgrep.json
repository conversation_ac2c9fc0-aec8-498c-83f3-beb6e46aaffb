{"version": "1.130.0", "results": [{"check_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "path": "downloaded_repos/stephenjude_filament-two-factor-authentication/bin/build.js", "start": {"line": 37, "col": 33, "offset": 1003}, "end": {"line": 37, "col": 126, "offset": 1096}, "extra": {"message": "Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string.", "metadata": {"cwe": ["CWE-134: Use of Externally-Controlled Format String"], "owasp": ["A01:2021 - Broken Access Control"], "category": "security", "technology": ["javascript"], "subcategory": ["audit"], "likelihood": "MEDIUM", "impact": "LOW", "confidence": "LOW", "references": ["https://cwe.mitre.org/data/definitions/134.html"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "shortlink": "https://sg.run/7Y5R"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/stephenjude_filament-two-factor-authentication/.github/workflows/run-tests.yml", "start": {"line": 42, "col": 38, "offset": 1068}, "end": {"line": 42, "col": 57, "offset": 1087}}, {"path": "downloaded_repos/stephenjude_filament-two-factor-authentication/.github/workflows/run-tests.yml", "start": {"line": 43, "col": 24, "offset": 1068}, "end": {"line": 43, "col": 43, "offset": 1087}}]], "message": "Syntax error at line downloaded_repos/stephenjude_filament-two-factor-authentication/.github/workflows/run-tests.yml:42:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ runner.tool_cache` was unexpected", "path": "downloaded_repos/stephenjude_filament-two-factor-authentication/.github/workflows/run-tests.yml", "spans": [{"file": "downloaded_repos/stephenjude_filament-two-factor-authentication/.github/workflows/run-tests.yml", "start": {"line": 42, "col": 38, "offset": 1068}, "end": {"line": 42, "col": 57, "offset": 1087}}, {"file": "downloaded_repos/stephenjude_filament-two-factor-authentication/.github/workflows/run-tests.yml", "start": {"line": 43, "col": 24, "offset": 1068}, "end": {"line": 43, "col": 43, "offset": 1087}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/stephenjude_filament-two-factor-authentication/.github/workflows/run-tests.yml", "start": {"line": 47, "col": 53, "offset": 1255}, "end": {"line": 47, "col": 69, "offset": 1271}}, {"path": "downloaded_repos/stephenjude_filament-two-factor-authentication/.github/workflows/run-tests.yml", "start": {"line": 47, "col": 97, "offset": 1255}, "end": {"line": 47, "col": 115, "offset": 1273}}, {"path": "downloaded_repos/stephenjude_filament-two-factor-authentication/.github/workflows/run-tests.yml", "start": {"line": 47, "col": 137, "offset": 1255}, "end": {"line": 47, "col": 152, "offset": 1270}}, {"path": "downloaded_repos/stephenjude_filament-two-factor-authentication/.github/workflows/run-tests.yml", "start": {"line": 48, "col": 19, "offset": 1255}, "end": {"line": 48, "col": 22, "offset": 1258}}]], "message": "Syntax error at line downloaded_repos/stephenjude_filament-two-factor-authentication/.github/workflows/run-tests.yml:47:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ matrix.laravel` was unexpected", "path": "downloaded_repos/stephenjude_filament-two-factor-authentication/.github/workflows/run-tests.yml", "spans": [{"file": "downloaded_repos/stephenjude_filament-two-factor-authentication/.github/workflows/run-tests.yml", "start": {"line": 47, "col": 53, "offset": 1255}, "end": {"line": 47, "col": 69, "offset": 1271}}, {"file": "downloaded_repos/stephenjude_filament-two-factor-authentication/.github/workflows/run-tests.yml", "start": {"line": 47, "col": 97, "offset": 1255}, "end": {"line": 47, "col": 115, "offset": 1273}}, {"file": "downloaded_repos/stephenjude_filament-two-factor-authentication/.github/workflows/run-tests.yml", "start": {"line": 47, "col": 137, "offset": 1255}, "end": {"line": 47, "col": 152, "offset": 1270}}, {"file": "downloaded_repos/stephenjude_filament-two-factor-authentication/.github/workflows/run-tests.yml", "start": {"line": 48, "col": 19, "offset": 1255}, "end": {"line": 48, "col": 22, "offset": 1258}}]}], "paths": {"scanned": ["downloaded_repos/stephenjude_filament-two-factor-authentication/.editorconfig", "downloaded_repos/stephenjude_filament-two-factor-authentication/.gitattributes", "downloaded_repos/stephenjude_filament-two-factor-authentication/.github/CONTRIBUTING.md", "downloaded_repos/stephenjude_filament-two-factor-authentication/.github/FUNDING.yml", "downloaded_repos/stephenjude_filament-two-factor-authentication/.github/ISSUE_TEMPLATE/bug.yml", "downloaded_repos/stephenjude_filament-two-factor-authentication/.github/ISSUE_TEMPLATE/config.yml", "downloaded_repos/stephenjude_filament-two-factor-authentication/.github/SECURITY.md", "downloaded_repos/stephenjude_filament-two-factor-authentication/.github/dependabot.yml", "downloaded_repos/stephenjude_filament-two-factor-authentication/.github/workflows/dependabot-auto-merge.yml", "downloaded_repos/stephenjude_filament-two-factor-authentication/.github/workflows/fix-php-code-style-issues.yml", "downloaded_repos/stephenjude_filament-two-factor-authentication/.github/workflows/run-tests.yml", "downloaded_repos/stephenjude_filament-two-factor-authentication/.github/workflows/update-changelog.yml", "downloaded_repos/stephenjude_filament-two-factor-authentication/.gitignore", "downloaded_repos/stephenjude_filament-two-factor-authentication/.prettierrc", "downloaded_repos/stephenjude_filament-two-factor-authentication/CHANGELOG.md", "downloaded_repos/stephenjude_filament-two-factor-authentication/LICENSE.md", "downloaded_repos/stephenjude_filament-two-factor-authentication/README.md", "downloaded_repos/stephenjude_filament-two-factor-authentication/art/1.jpeg", "downloaded_repos/stephenjude_filament-two-factor-authentication/art/2.jpeg", "downloaded_repos/stephenjude_filament-two-factor-authentication/art/3.jpeg", "downloaded_repos/stephenjude_filament-two-factor-authentication/art/4.jpeg", "downloaded_repos/stephenjude_filament-two-factor-authentication/art/5.png", "downloaded_repos/stephenjude_filament-two-factor-authentication/art/banner.jpg", "downloaded_repos/stephenjude_filament-two-factor-authentication/bin/build.js", "downloaded_repos/stephenjude_filament-two-factor-authentication/composer.json", "downloaded_repos/stephenjude_filament-two-factor-authentication/database/migrations/add_two_factor_authentication_columns.php.stub", "downloaded_repos/stephenjude_filament-two-factor-authentication/package-lock.json", "downloaded_repos/stephenjude_filament-two-factor-authentication/package.json", "downloaded_repos/stephenjude_filament-two-factor-authentication/phpstan-baseline.neon", "downloaded_repos/stephenjude_filament-two-factor-authentication/phpstan.neon.dist", "downloaded_repos/stephenjude_filament-two-factor-authentication/phpunit.xml.dist", "downloaded_repos/stephenjude_filament-two-factor-authentication/pint.json", "downloaded_repos/stephenjude_filament-two-factor-authentication/postcss.config.cjs", "downloaded_repos/stephenjude_filament-two-factor-authentication/resources/css/index.css", "downloaded_repos/stephenjude_filament-two-factor-authentication/resources/js/index.js", "downloaded_repos/stephenjude_filament-two-factor-authentication/resources/lang/de/actions.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/resources/lang/de/components.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/resources/lang/de/pages.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/resources/lang/de/plugin.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/resources/lang/de/section.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/resources/lang/en/actions.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/resources/lang/en/components.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/resources/lang/en/pages.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/resources/lang/en/plugin.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/resources/lang/en/section.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/resources/lang/pl/actions.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/resources/lang/pl/components.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/resources/lang/pl/pages.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/resources/lang/pl/plugin.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/resources/lang/pl/section.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/resources/views/.gitkeep", "downloaded_repos/stephenjude_filament-two-factor-authentication/resources/views/components/enable.blade.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/resources/views/components/enabled.blade.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/resources/views/components/logout.blade.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/resources/views/components/partials/passkey-authenticate-script.blade.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/resources/views/components/passkey-login.blade.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/resources/views/components/recovery-codes.blade.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/resources/views/components/setup-confirmation.blade.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/resources/views/livewire/passkey-authentication.blade.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/resources/views/livewire/two-factor-authentication.blade.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/resources/views/pages/challenge.blade.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/resources/views/pages/recovery.blade.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/resources/views/pages/setup.blade.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/src/Actions/ConfirmTwoFactorAuthentication.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/src/Actions/DisableTwoFactorAuthentication.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/src/Actions/EnableTwoFactorAuthentication.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/src/Actions/GenerateNewRecoveryCodes.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/src/Actions/RecoveryCode.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/src/Contracts/TwoFactorAuthenticationProvider.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/src/Events/RecoveryCodeReplaced.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/src/Events/RecoveryCodesGenerated.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/src/Events/TwoFactorAuthenticationChallenged.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/src/Events/TwoFactorAuthenticationConfirmed.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/src/Events/TwoFactorAuthenticationDisabled.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/src/Events/TwoFactorAuthenticationEnabled.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/src/Events/TwoFactorAuthenticationEvent.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/src/Events/TwoFactorAuthenticationFailed.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/src/Events/ValidTwoFactorAuthenticationCodeProvided.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/src/Events/ValidTwoFactorRecoveryCodeProvided.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/src/Livewire/BaseLivewireComponent.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/src/Livewire/Defaults.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/src/Livewire/PasskeyAuthentication.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/src/Livewire/TwoFactorAuthentication.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/src/Middleware/ForceTwoFactorSetup.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/src/Middleware/TwoFactorChallenge.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/src/Pages/BaseSimplePage.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/src/Pages/Challenge.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/src/Pages/Recovery.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/src/Pages/Setup.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/src/TwoFactorAuthenticatable.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/src/TwoFactorAuthenticationPlugin.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/src/TwoFactorAuthenticationProvider.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/src/TwoFactorAuthenticationServiceProvider.php", "downloaded_repos/stephenjude_filament-two-factor-authentication/tailwind.config.js"], "skipped": [{"path": "downloaded_repos/stephenjude_filament-two-factor-authentication/.github/workflows/run-tests.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/stephenjude_filament-two-factor-authentication/resources/dist/.gitkeep", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/stephenjude_filament-two-factor-authentication/resources/dist/filament-two-factor-authentication.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/stephenjude_filament-two-factor-authentication/tests/ArchTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/stephenjude_filament-two-factor-authentication/tests/Common/AdminPanelProvider.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/stephenjude_filament-two-factor-authentication/tests/Common/User.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/stephenjude_filament-two-factor-authentication/tests/Pest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/stephenjude_filament-two-factor-authentication/tests/TestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/stephenjude_filament-two-factor-authentication/tests/TwoFactorAuthenticationPluginTest.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.7233240604400635, "profiling_times": {"config_time": 5.935154676437378, "core_time": 2.6062471866607666, "ignores_time": 0.001974344253540039, "total_time": 8.544182300567627}, "parsing_time": {"total_time": 0.5510039329528809, "per_file_time": {"mean": 0.007547999081546313, "std_dev": 0.00027632447388587866}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.488844394683838, "per_file_time": {"mean": 0.005704384653961064, "std_dev": 0.00023296614623663256}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.2709512710571289, "per_file_and_rule_time": {"mean": 0.0008064025924319314, "std_dev": 5.684338398032957e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.012418985366821289, "per_def_and_rule_time": {"mean": 0.0006536308087800679, "std_dev": 2.490834913071233e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}