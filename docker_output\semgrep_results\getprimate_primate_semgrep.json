{"version": "1.130.0", "results": [{"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getprimate_primate/src/workbench/directives/record-map.js", "start": {"line": 300, "col": 9, "offset": 9351}, "end": {"line": 300, "col": 71, "offset": 9413}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getprimate_primate/src/workbench/directives/sidebar-nav.js", "start": {"line": 54, "col": 5, "offset": 1274}, "end": {"line": 54, "col": 27, "offset": 1296}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getprimate_primate/src/workbench/directives/sidebar-nav.js", "start": {"line": 72, "col": 9, "offset": 1682}, "end": {"line": 72, "col": 31, "offset": 1704}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getprimate_primate/src/workbench/directives/sidebar-nav.js", "start": {"line": 75, "col": 9, "offset": 1762}, "end": {"line": 75, "col": 34, "offset": 1787}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getprimate_primate/src/workbench/directives/sidebar-nav.js", "start": {"line": 100, "col": 9, "offset": 2416}, "end": {"line": 100, "col": 51, "offset": 2458}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/getprimate_primate/src/workbench/directives/token-input.js", "start": {"line": 25, "col": 9, "offset": 723}, "end": {"line": 25, "col": 96, "offset": 810}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "path": "downloaded_repos/getprimate_primate/src/workbench/services/rest-provider.js", "start": {"line": 78, "col": 13, "offset": 2885}, "end": {"line": 78, "col": 62, "offset": 2934}, "extra": {"message": "Bracket object notation with user input is present, this might allow an attacker to access all properties of the object and even it's prototype. Use literal values for object properties.", "metadata": {"confidence": "LOW", "owasp": ["A02:2017 - Broken Authentication", "A04:2021 - Insecure Design"], "cwe": ["CWE-522: Insufficiently Protected Credentials"], "category": "security", "technology": ["express"], "references": ["https://github.com/nodesecurity/eslint-plugin-security/blob/3c7522ca1be800353513282867a1034c795d9eb4/docs/the-dangers-of-square-bracket-notation.md"], "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/javascript.express.security.audit.remote-property-injection.remote-property-injection", "shortlink": "https://sg.run/Z4gn"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/getprimate_primate/src/workbench/static/chart-segment.js", "start": {"line": 215, "col": 5, "offset": 5857}, "end": {"line": 215, "col": 42, "offset": 5894}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/getprimate_primate/src/workbench/static/chart-segment.js", "start": {"line": 1013, "col": 5, "offset": 25143}, "end": {"line": 1013, "col": 54, "offset": 25192}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/getprimate_primate/src/workbench/static/chart-segment.js", "start": {"line": 1478, "col": 7, "offset": 39592}, "end": {"line": 1478, "col": 42, "offset": 39627}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/getprimate_primate/src/workbench/static/views/certificate-edit.html", "start": {"line": 100, "col": 9, "offset": 5723}, "end": {"line": 108, "col": 16, "offset": 6440}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/getprimate_primate/src/workbench/static/views/upstream-edit.html", "start": {"line": 352, "col": 9, "offset": 26485}, "end": {"line": 360, "col": 16, "offset": 27216}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [], "paths": {"scanned": ["downloaded_repos/getprimate_primate/.eslintrc.json", "downloaded_repos/getprimate_primate/.gitignore", "downloaded_repos/getprimate_primate/.prettierignore", "downloaded_repos/getprimate_primate/.prettierrc.js", "downloaded_repos/getprimate_primate/CHANGELOG.md", "downloaded_repos/getprimate_primate/CONTRIBUTING.md", "downloaded_repos/getprimate_primate/Gruntfile.js", "downloaded_repos/getprimate_primate/LICENSE", "downloaded_repos/getprimate_primate/README.md", "downloaded_repos/getprimate_primate/TODO.md", "downloaded_repos/getprimate_primate/logo-banner.png", "downloaded_repos/getprimate_primate/package.json", "downloaded_repos/getprimate_primate/resources/dmg-background.png", "downloaded_repos/getprimate_primate/resources/icons/128x128.png", "downloaded_repos/getprimate_primate/resources/icons/16x16.png", "downloaded_repos/getprimate_primate/resources/icons/256x256.png", "downloaded_repos/getprimate_primate/resources/icons/32x32.png", "downloaded_repos/getprimate_primate/resources/icons/48x48.png", "downloaded_repos/getprimate_primate/resources/icons/512x512.png", "downloaded_repos/getprimate_primate/resources/icons/64x64.png", "downloaded_repos/getprimate_primate/resources/icons/app-scalable.icns", "downloaded_repos/getprimate_primate/resources/icons/app-scalable.ico", "downloaded_repos/getprimate_primate/resources/icons/dmg-scalable.icns", "downloaded_repos/getprimate_primate/resources/icons/installer-scalable.ico", "downloaded_repos/getprimate_primate/resources/icons/uninstaller-scalable.ico", "downloaded_repos/getprimate_primate/resources/license.txt", "downloaded_repos/getprimate_primate/resources/themes/README.md", "downloaded_repos/getprimate_primate/resources/themes/default-dark/default-dark.json", "downloaded_repos/getprimate_primate/resources/themes/default-dark/theme.json", "downloaded_repos/getprimate_primate/resources/themes/default-light/default-light.json", "downloaded_repos/getprimate_primate/resources/themes/default-light/theme.json", "downloaded_repos/getprimate_primate/resources/themes/green-ocean/ocean-theme.json", "downloaded_repos/getprimate_primate/resources/themes/green-ocean/theme.json", "downloaded_repos/getprimate_primate/screenshot.png", "downloaded_repos/getprimate_primate/src/platform/config/config-manager.js", "downloaded_repos/getprimate_primate/src/platform/config/gateway-base.js", "downloaded_repos/getprimate_primate/src/platform/config/workbench-base.js", "downloaded_repos/getprimate_primate/src/platform/constant/paths.js", "downloaded_repos/getprimate_primate/src/platform/constant/product.js", "downloaded_repos/getprimate_primate/src/platform/ipc/ipc-bridge.js", "downloaded_repos/getprimate_primate/src/platform/ipc/ipc-server.js", "downloaded_repos/getprimate_primate/src/platform/main.js", "downloaded_repos/getprimate_primate/src/platform/preload.js", "downloaded_repos/getprimate_primate/src/platform/renderer/about-dialog.js", "downloaded_repos/getprimate_primate/src/platform/renderer/menu.js", "downloaded_repos/getprimate_primate/src/platform/renderer/renderer-window.js", "downloaded_repos/getprimate_primate/src/platform/renderer/window-manager.js", "downloaded_repos/getprimate_primate/src/platform/system/directory.js", "downloaded_repos/getprimate_primate/src/platform/system/filesystem.js", "downloaded_repos/getprimate_primate/src/platform/theme/theme-scanner.js", "downloaded_repos/getprimate_primate/src/workbench/bootstrap.html", "downloaded_repos/getprimate_primate/src/workbench/bootstrap.js", "downloaded_repos/getprimate_primate/src/workbench/controllers/ca-edit.js", "downloaded_repos/getprimate_primate/src/workbench/controllers/certificate-edit.js", "downloaded_repos/getprimate_primate/src/workbench/controllers/certificate-list.js", "downloaded_repos/getprimate_primate/src/workbench/controllers/client-setup.js", "downloaded_repos/getprimate_primate/src/workbench/controllers/consumer-edit.js", "downloaded_repos/getprimate_primate/src/workbench/controllers/consumer-list.js", "downloaded_repos/getprimate_primate/src/workbench/controllers/footer.js", "downloaded_repos/getprimate_primate/src/workbench/controllers/header.js", "downloaded_repos/getprimate_primate/src/workbench/controllers/node-config.js", "downloaded_repos/getprimate_primate/src/workbench/controllers/overview.js", "downloaded_repos/getprimate_primate/src/workbench/controllers/plugin-edit.js", "downloaded_repos/getprimate_primate/src/workbench/controllers/plugin-list.js", "downloaded_repos/getprimate_primate/src/workbench/controllers/release-info.js", "downloaded_repos/getprimate_primate/src/workbench/controllers/route-edit.js", "downloaded_repos/getprimate_primate/src/workbench/controllers/route-list.js", "downloaded_repos/getprimate_primate/src/workbench/controllers/service-edit.js", "downloaded_repos/getprimate_primate/src/workbench/controllers/service-list.js", "downloaded_repos/getprimate_primate/src/workbench/controllers/settings.js", "downloaded_repos/getprimate_primate/src/workbench/controllers/sidebar.js", "downloaded_repos/getprimate_primate/src/workbench/controllers/tag-search.js", "downloaded_repos/getprimate_primate/src/workbench/controllers/upstream-edit.js", "downloaded_repos/getprimate_primate/src/workbench/controllers/upstream-list.js", "downloaded_repos/getprimate_primate/src/workbench/controllers/upstream-target.js", "downloaded_repos/getprimate_primate/src/workbench/controllers/welcome-intro.js", "downloaded_repos/getprimate_primate/src/workbench/dashboard.html", "downloaded_repos/getprimate_primate/src/workbench/dashboard.js", "downloaded_repos/getprimate_primate/src/workbench/directives/clipboard-text.js", "downloaded_repos/getprimate_primate/src/workbench/directives/multi-check.js", "downloaded_repos/getprimate_primate/src/workbench/directives/record-map.js", "downloaded_repos/getprimate_primate/src/workbench/directives/record-text.js", "downloaded_repos/getprimate_primate/src/workbench/directives/sidebar-nav.js", "downloaded_repos/getprimate_primate/src/workbench/directives/token-input.js", "downloaded_repos/getprimate_primate/src/workbench/exception/error.js", "downloaded_repos/getprimate_primate/src/workbench/helpers/date-lib.js", "downloaded_repos/getprimate_primate/src/workbench/helpers/notebook.js", "downloaded_repos/getprimate_primate/src/workbench/helpers/release-repo.js", "downloaded_repos/getprimate_primate/src/workbench/helpers/rest-toolkit.js", "downloaded_repos/getprimate_primate/src/workbench/interface/theme-engine.js", "downloaded_repos/getprimate_primate/src/workbench/lib/core-toolkit.js", "downloaded_repos/getprimate_primate/src/workbench/lib/version-utils.js", "downloaded_repos/getprimate_primate/src/workbench/models/ca-model.js", "downloaded_repos/getprimate_primate/src/workbench/models/certificate-model.js", "downloaded_repos/getprimate_primate/src/workbench/models/consumer-model.js", "downloaded_repos/getprimate_primate/src/workbench/models/plugin-model.js", "downloaded_repos/getprimate_primate/src/workbench/models/release-model.js", "downloaded_repos/getprimate_primate/src/workbench/models/route-model.js", "downloaded_repos/getprimate_primate/src/workbench/models/service-model.js", "downloaded_repos/getprimate_primate/src/workbench/models/setup-model.js", "downloaded_repos/getprimate_primate/src/workbench/models/sni-model.js", "downloaded_repos/getprimate_primate/src/workbench/models/upstream-model.js", "downloaded_repos/getprimate_primate/src/workbench/models/user-auth-model.js", "downloaded_repos/getprimate_primate/src/workbench/primate.js", "downloaded_repos/getprimate_primate/src/workbench/services/http-interceptor-factory.js", "downloaded_repos/getprimate_primate/src/workbench/services/logger-factory.js", "downloaded_repos/getprimate_primate/src/workbench/services/rest-provider.js", "downloaded_repos/getprimate_primate/src/workbench/services/toast-factory.js", "downloaded_repos/getprimate_primate/src/workbench/services/view-frame-provider.js", "downloaded_repos/getprimate_primate/src/workbench/static/chart-esm.js", "downloaded_repos/getprimate_primate/src/workbench/static/chart-segment.js", "downloaded_repos/getprimate_primate/src/workbench/static/css/font-face.css", "downloaded_repos/getprimate_primate/src/workbench/static/css/layout.css", "downloaded_repos/getprimate_primate/src/workbench/static/fonts/Material-Icons-Outlined.woff2", "downloaded_repos/getprimate_primate/src/workbench/static/fonts/Roboto-Black.ttf", "downloaded_repos/getprimate_primate/src/workbench/static/fonts/Roboto-BlackItalic.ttf", "downloaded_repos/getprimate_primate/src/workbench/static/fonts/Roboto-Italic.ttf", "downloaded_repos/getprimate_primate/src/workbench/static/fonts/Roboto-Light.ttf", "downloaded_repos/getprimate_primate/src/workbench/static/fonts/Roboto-LightItalic.ttf", "downloaded_repos/getprimate_primate/src/workbench/static/fonts/Roboto-Medium.ttf", "downloaded_repos/getprimate_primate/src/workbench/static/fonts/Roboto-MediumItalic.ttf", "downloaded_repos/getprimate_primate/src/workbench/static/fonts/Roboto-Regular.ttf", "downloaded_repos/getprimate_primate/src/workbench/static/fonts/Roboto-Thin.ttf", "downloaded_repos/getprimate_primate/src/workbench/static/fonts/Roboto-ThinItalic.ttf", "downloaded_repos/getprimate_primate/src/workbench/static/images/logo-128x128.png", "downloaded_repos/getprimate_primate/src/workbench/static/images/logo-256x256.png", "downloaded_repos/getprimate_primate/src/workbench/static/images/logo-64x64.png", "downloaded_repos/getprimate_primate/src/workbench/static/views/ca-edit.html", "downloaded_repos/getprimate_primate/src/workbench/static/views/certificate-edit.html", "downloaded_repos/getprimate_primate/src/workbench/static/views/certificate-list.html", "downloaded_repos/getprimate_primate/src/workbench/static/views/client-setup.html", "downloaded_repos/getprimate_primate/src/workbench/static/views/consumer-edit.html", "downloaded_repos/getprimate_primate/src/workbench/static/views/consumer-list.html", "downloaded_repos/getprimate_primate/src/workbench/static/views/node-config.html", "downloaded_repos/getprimate_primate/src/workbench/static/views/overview.html", "downloaded_repos/getprimate_primate/src/workbench/static/views/plugin-edit.html", "downloaded_repos/getprimate_primate/src/workbench/static/views/plugin-list.html", "downloaded_repos/getprimate_primate/src/workbench/static/views/plugin-static-record.html", "downloaded_repos/getprimate_primate/src/workbench/static/views/release-info.html", "downloaded_repos/getprimate_primate/src/workbench/static/views/route-edit.html", "downloaded_repos/getprimate_primate/src/workbench/static/views/route-list.html", "downloaded_repos/getprimate_primate/src/workbench/static/views/service-edit.html", "downloaded_repos/getprimate_primate/src/workbench/static/views/service-list.html", "downloaded_repos/getprimate_primate/src/workbench/static/views/settings.html", "downloaded_repos/getprimate_primate/src/workbench/static/views/tag-search.html", "downloaded_repos/getprimate_primate/src/workbench/static/views/upstream-edit.html", "downloaded_repos/getprimate_primate/src/workbench/static/views/upstream-list.html", "downloaded_repos/getprimate_primate/src/workbench/static/views/upstream-target.html", "downloaded_repos/getprimate_primate/src/workbench/static/views/welcome-intro.html", "downloaded_repos/getprimate_primate/src/workbench/template.js", "downloaded_repos/getprimate_primate/yarn.lock"], "skipped": [{"path": "downloaded_repos/getprimate_primate/build/builder-config.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/getprimate_primate/build/builder-platform.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/getprimate_primate/build/builder-wrapper.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/getprimate_primate/build/constant.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/getprimate_primate/build/grunt-config.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/getprimate_primate/build/grunt-task.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/getprimate_primate/src/workbench/static/angular-route.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/getprimate_primate/src/workbench/static/angular.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/getprimate_primate/test/electron-test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/getprimate_primate/test/themes.js", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6399099826812744, "profiling_times": {"config_time": 5.8615782260894775, "core_time": 20.49365735054016, "ignores_time": 0.001781463623046875, "total_time": 26.35776448249817}, "parsing_time": {"total_time": 4.662001371383667, "per_file_time": {"mean": 0.045705895797879086, "std_dev": 0.02999053594786556}, "very_slow_stats": {"time_ratio": 0.5066281317168674, "count_ratio": 0.0196078431372549}, "very_slow_files": [{"fpath": "downloaded_repos/getprimate_primate/src/workbench/static/chart-segment.js", "ftime": 0.7615110874176025}, {"fpath": "downloaded_repos/getprimate_primate/src/workbench/static/chart-esm.js", "ftime": 1.6003899574279785}]}, "scanning_time": {"total_time": 30.93081784248352, "per_file_time": {"mean": 0.07507480058855223, "std_dev": 0.7771941868978832}, "very_slow_stats": {"time_ratio": 0.6410271466791941, "count_ratio": 0.0048543689320388345}, "very_slow_files": [{"fpath": "downloaded_repos/getprimate_primate/src/workbench/static/chart-segment.js", "ftime": 2.09214186668396}, {"fpath": "downloaded_repos/getprimate_primate/src/workbench/static/chart-esm.js", "ftime": 17.735352039337158}]}, "matching_time": {"total_time": 13.247411966323853, "per_file_and_rule_time": {"mean": 0.029179321511726544, "std_dev": 0.021196037777223564}, "very_slow_stats": {"time_ratio": 0.6446009067831063, "count_ratio": 0.037444933920704845}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/getprimate_primate/src/workbench/static/chart-segment.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.18052101135253906}, {"fpath": "downloaded_repos/getprimate_primate/src/workbench/static/chart-esm.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.18908405303955078}, {"fpath": "downloaded_repos/getprimate_primate/src/workbench/static/chart-segment.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.19533896446228027}, {"fpath": "downloaded_repos/getprimate_primate/src/workbench/static/chart-esm.js", "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.32587099075317383}, {"fpath": "downloaded_repos/getprimate_primate/src/workbench/static/chart-esm.js", "rule_id": "javascript.lang.security.audit.code-string-concat.code-string-concat", "time": 0.33249902725219727}, {"fpath": "downloaded_repos/getprimate_primate/src/workbench/static/chart-esm.js", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 0.835258960723877}, {"fpath": "downloaded_repos/getprimate_primate/src/workbench/static/chart-esm.js", "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.9155869483947754}, {"fpath": "downloaded_repos/getprimate_primate/src/workbench/static/chart-esm.js", "rule_id": "problem-based-packs.insecure-transport.js-node.using-http-server.using-http-server", "time": 1.0385069847106934}, {"fpath": "downloaded_repos/getprimate_primate/src/workbench/static/chart-esm.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 1.299386978149414}, {"fpath": "downloaded_repos/getprimate_primate/src/workbench/static/chart-esm.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 2.2506728172302246}]}, "tainting_time": {"total_time": 8.667497158050537, "per_def_and_rule_time": {"mean": 0.0006602298261769143, "std_dev": 1.0090721913230794e-05}, "very_slow_stats": {"time_ratio": 0.05397880922320822, "count_ratio": 0.00022851919561243144}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/getprimate_primate/src/workbench/static/chart-esm.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.06991219520568848}, {"fpath": "downloaded_repos/getprimate_primate/src/workbench/static/chart-esm.js", "fline": 1, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.18886089324951172}, {"fpath": "downloaded_repos/getprimate_primate/src/workbench/static/chart-esm.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.20908808708190918}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}