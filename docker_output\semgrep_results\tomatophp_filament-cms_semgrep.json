{"version": "1.130.0", "results": [], "errors": [], "paths": {"scanned": ["downloaded_repos/tomatophp_filament-cms/.DS_Store", "downloaded_repos/tomatophp_filament-cms/.github/FUNDING.yml", "downloaded_repos/tomatophp_filament-cms/3x1io-tomato-cms.md", "downloaded_repos/tomatophp_filament-cms/CHANGELOG.md", "downloaded_repos/tomatophp_filament-cms/LICENSE.md", "downloaded_repos/tomatophp_filament-cms/README.md", "downloaded_repos/tomatophp_filament-cms/SECURITY.md", "downloaded_repos/tomatophp_filament-cms/arts/category-list.png", "downloaded_repos/tomatophp_filament-cms/arts/create-category.png", "downloaded_repos/tomatophp_filament-cms/arts/create-field.png", "downloaded_repos/tomatophp_filament-cms/arts/create-form.png", "downloaded_repos/tomatophp_filament-cms/arts/create-post.png", "downloaded_repos/tomatophp_filament-cms/arts/form-fields.png", "downloaded_repos/tomatophp_filament-cms/arts/form-list.png", "downloaded_repos/tomatophp_filament-cms/arts/page-builder-preview.png", "downloaded_repos/tomatophp_filament-cms/arts/page-builder.png", "downloaded_repos/tomatophp_filament-cms/arts/post-seo.png", "downloaded_repos/tomatophp_filament-cms/arts/posts-list.png", "downloaded_repos/tomatophp_filament-cms/arts/preview-form.png", "downloaded_repos/tomatophp_filament-cms/arts/view-post.png", "downloaded_repos/tomatophp_filament-cms/arts/view-request.png", "downloaded_repos/tomatophp_filament-cms/composer.json", "downloaded_repos/tomatophp_filament-cms/config/.gitkeep", "downloaded_repos/tomatophp_filament-cms/config/filament-cms.php", "downloaded_repos/tomatophp_filament-cms/database/migrations/.gitkeep", "downloaded_repos/tomatophp_filament-cms/database/migrations/2021_11_23_130725_create_categories_table.php", "downloaded_repos/tomatophp_filament-cms/database/migrations/2021_11_23_130726_create_categories_metas_table.php", "downloaded_repos/tomatophp_filament-cms/database/migrations/2021_11_23_130847_create_posts_table.php", "downloaded_repos/tomatophp_filament-cms/database/migrations/2021_11_23_130848_create_post_metas_table.php", "downloaded_repos/tomatophp_filament-cms/database/migrations/2021_11_23_130849_create_posts_has_tags_table.php", "downloaded_repos/tomatophp_filament-cms/database/migrations/2021_11_23_130850_create_posts_has_category_table.php", "downloaded_repos/tomatophp_filament-cms/database/migrations/2023_01_19_115146_create_forms_table.php", "downloaded_repos/tomatophp_filament-cms/database/migrations/2023_01_19_115147_create_form_options_table.php", "downloaded_repos/tomatophp_filament-cms/database/migrations/2023_01_19_115148_create_form_requests_table.php", "downloaded_repos/tomatophp_filament-cms/database/migrations/2023_01_19_115148_update_form_options_table.php", "downloaded_repos/tomatophp_filament-cms/database/migrations/2023_01_19_115149_create_form_request_metas_table.php", "downloaded_repos/tomatophp_filament-cms/database/migrations/2023_08_15_110333_create_tickets_table.php", "downloaded_repos/tomatophp_filament-cms/database/migrations/2023_08_15_110444_create_ticket_comments_table.php", "downloaded_repos/tomatophp_filament-cms/database/migrations/2023_09_11_224125_create_comments_table.php", "downloaded_repos/tomatophp_filament-cms/database/migrations/2023_11_23_190637_themes_settings.php", "downloaded_repos/tomatophp_filament-cms/database/migrations/2024_09_18_150652_add_team_id_to_categories_table.php", "downloaded_repos/tomatophp_filament-cms/module.json", "downloaded_repos/tomatophp_filament-cms/resources/js/.gitkeep", "downloaded_repos/tomatophp_filament-cms/resources/lang/.gitkeep", "downloaded_repos/tomatophp_filament-cms/resources/lang/ar/messages.php", "downloaded_repos/tomatophp_filament-cms/resources/lang/en/messages.php", "downloaded_repos/tomatophp_filament-cms/resources/views/.gitkeep", "downloaded_repos/tomatophp_filament-cms/resources/views/components/empty.blade.php", "downloaded_repos/tomatophp_filament-cms/resources/views/components/tables/columns/rate.blade.php", "downloaded_repos/tomatophp_filament-cms/resources/views/forms/components/toggle-buttons.blade.php", "downloaded_repos/tomatophp_filament-cms/resources/views/pages/table.blade.php", "downloaded_repos/tomatophp_filament-cms/resources/views/pages/themes.blade.php", "downloaded_repos/tomatophp_filament-cms/resources/views/themes/builder-toolbar-livewire.blade.php", "downloaded_repos/tomatophp_filament-cms/resources/views/themes/builder-toolbar.blade.php", "downloaded_repos/tomatophp_filament-cms/routes/api.php", "downloaded_repos/tomatophp_filament-cms/routes/web.php", "downloaded_repos/tomatophp_filament-cms/src/Browser/Actions/CreateBrowser.php", "downloaded_repos/tomatophp_filament-cms/src/Browser/Chrome.php", "downloaded_repos/tomatophp_filament-cms/src/Console/.gitkeep", "downloaded_repos/tomatophp_filament-cms/src/Console/FilamentCmsInstall.php", "downloaded_repos/tomatophp_filament-cms/src/Console/FilamentFormGenerator.php", "downloaded_repos/tomatophp_filament-cms/src/Console/FilamentThemeGenerator.php", "downloaded_repos/tomatophp_filament-cms/src/Events/PostCreated.php", "downloaded_repos/tomatophp_filament-cms/src/Events/PostDeleted.php", "downloaded_repos/tomatophp_filament-cms/src/Events/PostUpdated.php", "downloaded_repos/tomatophp_filament-cms/src/Facades/FilamentCMS.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Pages/Themes.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Pages/Traits/HasShield.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/CategoriesMetaResource/Pages/CreateCategoriesMeta.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/CategoriesMetaResource/Pages/EditCategoriesMeta.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/CategoriesMetaResource/Pages/ListCategoriesMetas.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/CategoriesMetaResource.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/CategoryResource/Pages/CreateCategory.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/CategoryResource/Pages/EditCategory.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/CategoryResource/Pages/ListCategories.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/CategoryResource.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/FormOptionResource/Pages/CreateFormOption.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/FormOptionResource/Pages/EditFormOption.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/FormOptionResource/Pages/ListFormOptions.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/FormOptionResource.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/FormRequestMetaResource/Pages/CreateFormRequestMeta.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/FormRequestMetaResource/Pages/EditFormRequestMeta.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/FormRequestMetaResource/Pages/ListFormRequestMetas.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/FormRequestMetaResource.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/FormRequestResource/Pages/CreateFormRequest.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/FormRequestResource/Pages/EditFormRequest.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/FormRequestResource/Pages/ListFormRequests.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/FormRequestResource.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/FormResource/Pages/CreateForm.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/FormResource/Pages/EditForm.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/FormResource/Pages/ListForms.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/FormResource/RelationManagers/FormFieldsRelation.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/FormResource/RelationManagers/FormRequestsRelation.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/FormResource.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/PostMetaResource/Pages/CreatePostMeta.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/PostMetaResource/Pages/EditPostMeta.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/PostMetaResource/Pages/ListPostMetas.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/PostMetaResource.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/PostResource/Export/ExportPosts.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/PostResource/Import/ImportPosts.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/PostResource/Pages/CreatePost.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/PostResource/Pages/EditPost.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/PostResource/Pages/ListPosts.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/PostResource/Pages/ViewPost.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/PostResource/RelationManagers/PostCommentsRelation.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/PostResource.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/TicketCommentResource/Pages/CreateTicketComment.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/TicketCommentResource/Pages/EditTicketComment.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/TicketCommentResource/Pages/ListTicketComments.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/TicketCommentResource.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/TicketResource/Pages/CreateTicket.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/TicketResource/Pages/EditTicket.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/TicketResource/Pages/ListTickets.php", "downloaded_repos/tomatophp_filament-cms/src/Filament/Resources/TicketResource.php", "downloaded_repos/tomatophp_filament-cms/src/FilamentCMSPlugin.php", "downloaded_repos/tomatophp_filament-cms/src/FilamentCmsServiceProvider.php", "downloaded_repos/tomatophp_filament-cms/src/Generator/GenerateTheme.php", "downloaded_repos/tomatophp_filament-cms/src/Generator/Traits/GenerateInfo.php", "downloaded_repos/tomatophp_filament-cms/src/Generator/Traits/GenerateModule.php", "downloaded_repos/tomatophp_filament-cms/src/Generator/Traits/GenerateReadMe.php", "downloaded_repos/tomatophp_filament-cms/src/Http/Controllers/.gitkeep", "downloaded_repos/tomatophp_filament-cms/src/Http/Controllers/BuilderController.php", "downloaded_repos/tomatophp_filament-cms/src/Http/Middleware/.gitkeep", "downloaded_repos/tomatophp_filament-cms/src/Http/Requests/.gitkeep", "downloaded_repos/tomatophp_filament-cms/src/Infolists/Components/MarkdownEntry.php", "downloaded_repos/tomatophp_filament-cms/src/Jobs/BehanceMetaGetterJob.php", "downloaded_repos/tomatophp_filament-cms/src/Jobs/GitHubMetaGetterJob.php", "downloaded_repos/tomatophp_filament-cms/src/Jobs/GitHubMetaRefreshJob.php", "downloaded_repos/tomatophp_filament-cms/src/Jobs/YoutubeMetaGetterJob.php", "downloaded_repos/tomatophp_filament-cms/src/Livewire/BuilderToolbar.php", "downloaded_repos/tomatophp_filament-cms/src/Models/.gitkeep", "downloaded_repos/tomatophp_filament-cms/src/Models/CategoriesMeta.php", "downloaded_repos/tomatophp_filament-cms/src/Models/Category.php", "downloaded_repos/tomatophp_filament-cms/src/Models/Comment.php", "downloaded_repos/tomatophp_filament-cms/src/Models/Form.php", "downloaded_repos/tomatophp_filament-cms/src/Models/FormOption.php", "downloaded_repos/tomatophp_filament-cms/src/Models/FormRequest.php", "downloaded_repos/tomatophp_filament-cms/src/Models/FormRequestMeta.php", "downloaded_repos/tomatophp_filament-cms/src/Models/Post.php", "downloaded_repos/tomatophp_filament-cms/src/Models/PostMeta.php", "downloaded_repos/tomatophp_filament-cms/src/Models/Theme.php", "downloaded_repos/tomatophp_filament-cms/src/Models/Ticket.php", "downloaded_repos/tomatophp_filament-cms/src/Models/TicketComment.php", "downloaded_repos/tomatophp_filament-cms/src/Services/Behance.php", "downloaded_repos/tomatophp_filament-cms/src/Services/Contracts/CmsAuthor.php", "downloaded_repos/tomatophp_filament-cms/src/Services/Contracts/CmsFormFieldType.php", "downloaded_repos/tomatophp_filament-cms/src/Services/Contracts/CmsType.php", "downloaded_repos/tomatophp_filament-cms/src/Services/Contracts/Form.php", "downloaded_repos/tomatophp_filament-cms/src/Services/Contracts/FormInput.php", "downloaded_repos/tomatophp_filament-cms/src/Services/Contracts/FormInputOption.php", "downloaded_repos/tomatophp_filament-cms/src/Services/Contracts/Page.php", "downloaded_repos/tomatophp_filament-cms/src/Services/Contracts/Section.php", "downloaded_repos/tomatophp_filament-cms/src/Services/FilamentCMSAuthors.php", "downloaded_repos/tomatophp_filament-cms/src/Services/FilamentCMSFormBuilder.php", "downloaded_repos/tomatophp_filament-cms/src/Services/FilamentCMSFormFields.php", "downloaded_repos/tomatophp_filament-cms/src/Services/FilamentCMSServices.php", "downloaded_repos/tomatophp_filament-cms/src/Services/FilamentCMSThemes.php", "downloaded_repos/tomatophp_filament-cms/src/Services/FilamentCMSTypes.php", "downloaded_repos/tomatophp_filament-cms/src/Services/FilamentFormsServices.php", "downloaded_repos/tomatophp_filament-cms/src/Settings/ThemesSettings.php", "downloaded_repos/tomatophp_filament-cms/src/Traits/HasPosts.php", "downloaded_repos/tomatophp_filament-cms/src/Transformers/FAQResource.php", "downloaded_repos/tomatophp_filament-cms/src/Transformers/PagesResource.php", "downloaded_repos/tomatophp_filament-cms/src/Transformers/TicketResource.php", "downloaded_repos/tomatophp_filament-cms/src/Transformers/TicketsCommentsResource.php", "downloaded_repos/tomatophp_filament-cms/src/Transformers/TicketsResource.php", "downloaded_repos/tomatophp_filament-cms/src/Views/BuilderToolbar.php", "downloaded_repos/tomatophp_filament-cms/src/helpers.php", "downloaded_repos/tomatophp_filament-cms/stubs/migration.stub", "downloaded_repos/tomatophp_filament-cms/stubs/readme.stub"], "skipped": [{"path": "downloaded_repos/tomatophp_filament-cms/arts/3x1io-tomato-cms.jpg", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/tomatophp_filament-cms/tests/Feature/.gitkeep", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/tomatophp_filament-cms/tests/Unit/.gitkeep", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.7790980339050293, "profiling_times": {"config_time": 6.022318363189697, "core_time": 2.967942714691162, "ignores_time": 0.0017635822296142578, "total_time": 8.993314027786255}, "parsing_time": {"total_time": 1.0345897674560547, "per_file_time": {"mean": 0.0074970273004061954, "std_dev": 0.0002895349357850787}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 2.0410633087158203, "per_file_time": {"mean": 0.004270006921999626, "std_dev": 0.00017201937789976173}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.1897284984588623, "per_file_and_rule_time": {"mean": 0.000998571044520328, "std_dev": 8.447281999128239e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.01346588134765625, "per_def_and_rule_time": {"mean": 0.0016832351684570312, "std_dev": 5.232641825614337e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}