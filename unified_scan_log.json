{"scan_timestamp": "2025-07-27T14:20:09.189462", "scan_statistics": {"total_repos_found": 431, "repos_cloned": 50, "semgrep_scans_completed": 0, "high_risk_repos": 0, "vulnerabilities_found": 0}, "configuration": {"github_token": "****************************************", "search_pages": 3, "min_stars": 50, "max_stars": 2000, "updated_days_ago": 120, "max_repos_to_analyze": 100, "semgrep_timeout": 300, "clone_timeout": 120, "max_concurrent_scans": 1, "output_csv": "unified_vulnerability_results.csv", "semgrep_results_dir": "semgrep_results", "repos_dir": "downloaded_repos", "detailed_log": "unified_scan_log.json", "use_docker": true, "docker_image": "returntocorp/semgrep:latest"}, "total_repositories_analyzed": 50, "top_10_repositories": [{"repo_name": "uasoft-indonesia/badaso", "repo_url": "https://github.com/uasoft-indonesia/badaso", "clone_url": "https://github.com/uasoft-indonesia/badaso.git", "stars": 1237, "issues": 1, "language": "PHP", "description": "Laravel Vue headless CMS / admin panel / dashboard / builder / API CRUD generator, anything !", "risk_score": 59, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 59.0, "risk_factors": "High-risk application type (+24); Critical features (+4); Vulnerability keywords (+6)", "last_updated": "2025-06-13T09:54:58Z", "created_at": "2021-03-15T04:40:50Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "moonshine-software/moonshine", "repo_url": "https://github.com/moonshine-software/moonshine", "clone_url": "https://github.com/moonshine-software/moonshine.git", "stars": 1089, "issues": 8, "language": "PHP", "description": "Laravel Admin panel and more. Simple for beginners and powerful for experts. Using Blade, Alpine.js and Tailwind CSS.", "risk_score": 56, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 56.0, "risk_factors": "High-risk application type (+24); Critical features (+4); Vulnerability keywords (+3)", "last_updated": "2025-07-27T18:55:38Z", "created_at": "2022-05-12T15:31:35Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "alperenersoy/filament-export", "repo_url": "https://github.com/alperenersoy/filament-export", "clone_url": "https://github.com/alperenersoy/filament-export.git", "stars": 257, "issues": 17, "language": "PHP", "description": "Customizable export and print functionality for Filament Admin Panel", "risk_score": 44, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 44.0, "risk_factors": "High-risk application type (+8); Critical features (+8); Vulnerability keywords (+3)", "last_updated": "2025-06-30T18:46:38Z", "created_at": "2022-05-06T19:54:09Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "e107inc/e107", "repo_url": "https://github.com/e107inc/e107", "clone_url": "https://github.com/e107inc/e107.git", "stars": 329, "issues": 691, "language": "PHP", "description": "e107 Bootstrap CMS (Content Management System) v2 with PHP, MySQL, HTML5, jQuery and Twitter Bootstrap. Issue Discussion Room: https://gitter.im/e107inc/e107 ", "risk_score": 44, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 44.0, "risk_factors": "High-risk application type (+8); Vulnerability keywords (+6); Optimal star range (+10)", "last_updated": "2025-07-20T18:33:58Z", "created_at": "2012-11-16T19:49:01Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "lara-zeus/sky", "repo_url": "https://github.com/lara-zeus/sky", "clone_url": "https://github.com/lara-zeus/sky.git", "stars": 191, "issues": 1, "language": "PHP", "description": "CMS for your website. it include posts, pages, tags, and categories. with a frontend scaffolding ready to use", "risk_score": 44, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 44.0, "risk_factors": "High-risk application type (+16); Vulnerability keywords (+3); Optimal star range (+10)", "last_updated": "2025-07-08T10:22:33Z", "created_at": "2022-03-28T21:38:35Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "ClassicPress/ClassicPress", "repo_url": "https://github.com/ClassicPress/ClassicPress", "clone_url": "https://github.com/ClassicPress/ClassicPress.git", "stars": 807, "issues": 39, "language": "PHP", "description": "The CMS for Creators. Stable. Lightweight. Instantly Familiar. Forked from WordPress.", "risk_score": 41, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 41.0, "risk_factors": "High-risk application type (+16); Optimal star range (+10); High-risk language: php (+10)", "last_updated": "2025-07-27T10:05:16Z", "created_at": "2018-08-29T10:02:51Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "stephenjude/filament-blog", "repo_url": "https://github.com/stephenjude/filament-blog", "clone_url": "https://github.com/stephenjude/filament-blog.git", "stars": 189, "issues": 2, "language": "PHP", "description": "A faceless blog content manager with configurable richtext and markdown support for filament admin panel", "risk_score": 40, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 40.0, "risk_factors": "High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3)", "last_updated": "2025-06-17T08:42:27Z", "created_at": "2022-02-12T19:22:31Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "bezhanSalleh/filament-exceptions", "repo_url": "https://github.com/bezhanSalleh/filament-exceptions", "clone_url": "https://github.com/bezhanSalleh/filament-exceptions.git", "stars": 182, "issues": 2, "language": "PHP", "description": "A Simple & Beautiful Pluggable Exception Viewer for FilamentPHP's Admin Panel", "risk_score": 40, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 40.0, "risk_factors": "High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3)", "last_updated": "2025-06-16T10:45:33Z", "created_at": "2022-09-04T05:40:51Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "et-nik/gameap", "repo_url": "https://github.com/et-nik/gameap", "clone_url": "https://github.com/et-nik/gameap.git", "stars": 115, "issues": 40, "language": "PHP", "description": "Game Admin Panel (GameAP) is the opensource game servers control panel.", "risk_score": 40, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 40.0, "risk_factors": "High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3)", "last_updated": "2025-05-15T07:09:45Z", "created_at": "2018-11-21T12:01:13Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "saade/filament-laravel-log", "repo_url": "https://github.com/saade/filament-laravel-log", "clone_url": "https://github.com/saade/filament-laravel-log.git", "stars": 111, "issues": 9, "language": "PHP", "description": "Read Laravel logs from the Filament admin panel", "risk_score": 40, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 40.0, "risk_factors": "High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3)", "last_updated": "2025-07-21T18:47:54Z", "created_at": "2022-03-18T03:09:11Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}], "all_repositories_summary": [{"repo_name": "uasoft-indonesia/badaso", "final_score": 59.0, "semgrep_vulnerabilities": 0}, {"repo_name": "moonshine-software/moonshine", "final_score": 56.0, "semgrep_vulnerabilities": 0}, {"repo_name": "alperenersoy/filament-export", "final_score": 44.0, "semgrep_vulnerabilities": 0}, {"repo_name": "e107inc/e107", "final_score": 44.0, "semgrep_vulnerabilities": 0}, {"repo_name": "lara-zeus/sky", "final_score": 44.0, "semgrep_vulnerabilities": 0}, {"repo_name": "ClassicPress/ClassicPress", "final_score": 41.0, "semgrep_vulnerabilities": 0}, {"repo_name": "stephenjude/filament-blog", "final_score": 40.0, "semgrep_vulnerabilities": 0}, {"repo_name": "bezhanSalleh/filament-exceptions", "final_score": 40.0, "semgrep_vulnerabilities": 0}, {"repo_name": "et-nik/gameap", "final_score": 40.0, "semgrep_vulnerabilities": 0}, {"repo_name": "saade/filament-laravel-log", "final_score": 40.0, "semgrep_vulnerabilities": 0}, {"repo_name": "processwire/processwire", "final_score": 38.0, "semgrep_vulnerabilities": 0}, {"repo_name": "concretecms/concretecms", "final_score": 38.0, "semgrep_vulnerabilities": 0}, {"repo_name": "redaxo/redaxo", "final_score": 38.0, "semgrep_vulnerabilities": 0}, {"repo_name": "silverstripe/silverstripe-elemental", "final_score": 38.0, "semgrep_vulnerabilities": 0}, {"repo_name": "oc-shopaholic/oc-shopaholic-plugin", "final_score": 37.0, "semgrep_vulnerabilities": 0}, {"repo_name": "ashraf-kabir/personal-blog", "final_score": 36.0, "semgrep_vulnerabilities": 0}, {"repo_name": "zenphoto/zenphoto", "final_score": 36.0, "semgrep_vulnerabilities": 0}, {"repo_name": "nukeviet/nukeviet", "final_score": 36.0, "semgrep_vulnerabilities": 0}, {"repo_name": "maxsite/cms", "final_score": 36.0, "semgrep_vulnerabilities": 0}, {"repo_name": "JibayMcs/filament-tour", "final_score": 35.0, "semgrep_vulnerabilities": 0}, {"repo_name": "kleeja-official/kleeja", "final_score": 35.0, "semgrep_vulnerabilities": 0}, {"repo_name": "codepress/admin-columns", "final_score": 33.0, "semgrep_vulnerabilities": 0}, {"repo_name": "aimeos/ai-cms-grapesjs", "final_score": 33.0, "semgrep_vulnerabilities": 0}, {"repo_name": "webreinvent/vaahcms", "final_score": 33.0, "semgrep_vulnerabilities": 0}, {"repo_name": "roe<PERSON><PERSON>jan-elsinga/aloia-cms", "final_score": 33.0, "semgrep_vulnerabilities": 0}, {"repo_name": "nystudio107/craft-seomatic", "final_score": 33.0, "semgrep_vulnerabilities": 0}, {"repo_name": "FusionGen/FusionGEN", "final_score": 33.0, "semgrep_vulnerabilities": 0}, {"repo_name": "concretecms-community-store/community_store", "final_score": 33.0, "semgrep_vulnerabilities": 0}, {"repo_name": "spatie/livewire-filepond", "final_score": 31.0, "semgrep_vulnerabilities": 0}, {"repo_name": "stephen<PERSON>de/filament-debugger", "final_score": 30.0, "semgrep_vulnerabilities": 0}, {"repo_name": "dustin10/VichUploaderBundle", "final_score": 30.0, "semgrep_vulnerabilities": 0}, {"repo_name": "TypiCMS/Base", "final_score": 28.0, "semgrep_vulnerabilities": 0}, {"repo_name": "danpros/htmly", "final_score": 28.0, "semgrep_vulnerabilities": 0}, {"repo_name": "WonderCMS/wondercms", "final_score": 28.0, "semgrep_vulnerabilities": 0}, {"repo_name": "spicywebau/craft-neo", "final_score": 28.0, "semgrep_vulnerabilities": 0}, {"repo_name": "craftcms/commerce", "final_score": 28.0, "semgrep_vulnerabilities": 0}, {"repo_name": "pluxml/PluXml", "final_score": 28.0, "semgrep_vulnerabilities": 0}, {"repo_name": "rainlab/builder-plugin", "final_score": 28.0, "semgrep_vulnerabilities": 0}, {"repo_name": "spicywebau/craft-embedded-assets", "final_score": 28.0, "semgrep_vulnerabilities": 0}, {"repo_name": "putyourlightson/craft-blitz", "final_score": 28.0, "semgrep_vulnerabilities": 0}, {"repo_name": "nextcloud/cms_pico", "final_score": 28.0, "semgrep_vulnerabilities": 0}, {"repo_name": "lautaroangelico/WebEngine", "final_score": 28.0, "semgrep_vulnerabilities": 0}, {"repo_name": "haxtheweb/haxcms-php", "final_score": 28.0, "semgrep_vulnerabilities": 0}, {"repo_name": "Flute-CMS/cms", "final_score": 28.0, "semgrep_vulnerabilities": 0}, {"repo_name": "TeamEasy/EasyCMS", "final_score": 28.0, "semgrep_vulnerabilities": 0}, {"repo_name": "aymanalhattami/filament-context-menu", "final_score": 25.0, "semgrep_vulnerabilities": 0}, {"repo_name": "fsi-open/admin-bundle", "final_score": 25.0, "semgrep_vulnerabilities": 0}, {"repo_name": "Ecodev/graphql-upload", "final_score": 25.0, "semgrep_vulnerabilities": 0}, {"repo_name": "mostafaznv/larupload", "final_score": 25.0, "semgrep_vulnerabilities": 0}, {"repo_name": "solutionforest/filament-scaffold", "final_score": 13.0, "semgrep_vulnerabilities": 0}]}