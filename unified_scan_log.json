{"scan_timestamp": "2025-07-29T08:28:17.744388", "scan_statistics": {"total_repos_found": 434, "repos_cloned": 0, "semgrep_scans_completed": 0, "high_risk_repos": 0, "vulnerabilities_found": 0}, "configuration": {"github_token": "****************************************", "search_pages": 10, "min_stars": 50, "max_stars": 2000, "updated_days_ago": 120, "max_repos_to_analyze": 100, "top_repos_to_keep": 100, "semgrep_timeout": 300, "clone_timeout": 120, "max_concurrent_scans": 2, "output_csv": "unified_vulnerability_results.csv", "semgrep_results_dir": "semgrep_results", "repos_dir": "downloaded_repos", "detailed_log": "unified_scan_log.json", "use_docker": true, "docker_image": "returntocorp/semgrep:latest", "in_container": false, "skip_semgrep": true, "input_csv": null, "semgrep_only": false}, "total_repositories_analyzed": 100, "top_repositories": [{"repo_name": "lara-zeus/sky", "repo_url": "https://github.com/lara-zeus/sky", "clone_url": "https://github.com/lara-zeus/sky.git", "stars": 191, "issues": 1, "language": "PHP", "description": "CMS for your website. it include posts, pages, tags, and categories. with a frontend scaffolding ready to use", "risk_score": 44, "health_score": 87, "security_score": 70, "development_score": 100, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 64.9, "risk_factors": "High-risk application type (+16); Vulnerability keywords (+3); Optimal star range (+10)", "health_factors": "Recently active (last 30 days); Few open issues; Recent release", "security_factors": "Has .github/SECURITY.md policy; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-07-08T10:22:33Z", "created_at": "2022-03-28T21:38:35Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "uasoft-indonesia/badaso", "repo_url": "https://github.com/uasoft-indonesia/badaso", "clone_url": "https://github.com/uasoft-indonesia/badaso.git", "stars": 1236, "issues": 1, "language": "PHP", "description": "Laravel Vue headless CMS / admin panel / dashboard / builder / API CRUD generator, anything !", "risk_score": 59, "health_score": 80, "security_score": 40, "development_score": 80, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 63.5, "risk_factors": "High-risk application type (+24); Critical features (+4); Vulnerability keywords (+6)", "health_factors": "Moderately active (last 90 days); Few open issues; Recent release", "security_factors": "Has SECURITY.md policy; No branch protection; Has CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-06-13T09:54:58Z", "created_at": "2021-03-15T04:40:50Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "moonshine-software/moonshine", "repo_url": "https://github.com/moonshine-software/moonshine", "clone_url": "https://github.com/moonshine-software/moonshine.git", "stars": 1089, "issues": 7, "language": "PHP", "description": "Laravel Admin panel and more. Simple for beginners and powerful for experts. Using Blade, Alpine.js and Tailwind CSS.", "risk_score": 56, "health_score": 80, "security_score": 50, "development_score": 65, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 61.25, "risk_factors": "High-risk application type (+24); Critical features (+4); Vulnerability keywords (+3)", "health_factors": "Recently active (last 30 days); Few open issues; Recent release", "security_factors": "No security policy found; No branch protection; Has CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-07-27T18:55:38Z", "created_at": "2022-05-12T15:31:35Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "stephenjude/filament-blog", "repo_url": "https://github.com/stephenjude/filament-blog", "clone_url": "https://github.com/stephenjude/filament-blog.git", "stars": 189, "issues": 1, "language": "PHP", "description": "A faceless blog content manager with configurable richtext and markdown support for filament admin panel", "risk_score": 40, "health_score": 90, "security_score": 55, "development_score": 100, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 61.25, "risk_factors": "High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3)", "health_factors": "Recently active (last 30 days); Few open issues; Recent release", "security_factors": "Has .github/SECURITY.md policy; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-07-28T19:10:43Z", "created_at": "2022-02-12T19:22:31Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "WordPress/two-factor", "repo_url": "https://github.com/WordPress/two-factor", "clone_url": "https://github.com/WordPress/two-factor.git", "stars": 779, "issues": 73, "language": "PHP", "description": "Two-Factor Authentication for WordPress.", "risk_score": 42, "health_score": 75, "security_score": 75, "development_score": 85, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 60.0, "risk_factors": "High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3)", "health_factors": "Recently active (last 30 days); Many open issues (73); Recent release", "security_factors": "Has SECURITY.md policy; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-07-21T23:08:47Z", "created_at": "2014-10-23T20:03:04Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "saade/filament-laravel-log", "repo_url": "https://github.com/saade/filament-laravel-log", "clone_url": "https://github.com/saade/filament-laravel-log.git", "stars": 111, "issues": 9, "language": "PHP", "description": "Read Laravel logs from the Filament admin panel", "risk_score": 40, "health_score": 85, "security_score": 70, "development_score": 80, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 59.5, "risk_factors": "High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3)", "health_factors": "Recently active (last 30 days); Few open issues; Release within year", "security_factors": "Has .github/SECURITY.md policy; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-07-21T18:47:54Z", "created_at": "2022-03-18T03:09:11Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "JibayMcs/filament-tour", "repo_url": "https://github.com/JibayMcs/filament-tour", "clone_url": "https://github.com/JibayMcs/filament-tour.git", "stars": 118, "issues": 6, "language": "PHP", "description": "Let's embed the power of DriverJS to your filament admin panel, and guide peoples through your app", "risk_score": 35, "health_score": 80, "security_score": 55, "development_score": 100, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 56.75, "risk_factors": "High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3)", "health_factors": "Moderately active (last 90 days); Few open issues; Recent release", "security_factors": "Has .github/SECURITY.md policy; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-06-19T13:47:52Z", "created_at": "2023-08-17T07:25:01Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "alperenersoy/filament-export", "repo_url": "https://github.com/alperenersoy/filament-export", "clone_url": "https://github.com/alperenersoy/filament-export.git", "stars": 257, "issues": 17, "language": "PHP", "description": "Customizable export and print functionality for Filament Admin Panel", "risk_score": 44, "health_score": 85, "security_score": 40, "development_score": 75, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 56.25, "risk_factors": "High-risk application type (+8); Critical features (+8); Vulnerability keywords (+3)", "health_factors": "Recently active (last 30 days); Moderate open issues; Recent release", "security_factors": "No security policy found; No branch protection; Has CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-06-30T18:46:38Z", "created_at": "2022-05-06T19:54:09Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "codeigniter4/shield", "repo_url": "https://github.com/codeigniter4/shield", "clone_url": "https://github.com/codeigniter4/shield.git", "stars": 394, "issues": 15, "language": "PHP", "description": "Authentication and Authorization for CodeIgniter 4", "risk_score": 32, "health_score": 85, "security_score": 55, "development_score": 100, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 56.25, "risk_factors": "Critical features (+4); Vulnerability keywords (+3); Optimal star range (+10)", "health_factors": "Recently active (last 30 days); Moderate open issues; Recent release", "security_factors": "Has SECURITY.md policy; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-07-16T18:19:56Z", "created_at": "2020-12-30T06:55:29Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "ClassicPress/ClassicPress", "repo_url": "https://github.com/ClassicPress/ClassicPress", "clone_url": "https://github.com/ClassicPress/ClassicPress.git", "stars": 808, "issues": 43, "language": "PHP", "description": "The CMS for Creators. Stable. Lightweight. Instantly Familiar. Forked from WordPress.", "risk_score": 41, "health_score": 67, "security_score": 50, "development_score": 95, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 55.65, "risk_factors": "High-risk application type (+16); Optimal star range (+10); High-risk language: php (+10)", "health_factors": "Recently active (last 30 days); Moderate open issues; Release within year", "security_factors": "No security policy found; No branch protection; Has CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-07-28T17:37:02Z", "created_at": "2018-08-29T10:02:51Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "maize-tech/laravel-magic-login", "repo_url": "https://github.com/maize-tech/laravel-magic-login", "clone_url": "https://github.com/maize-tech/laravel-magic-login.git", "stars": 160, "issues": 0, "language": "PHP", "description": "Easily add passwordless authentication into your application", "risk_score": 35, "health_score": 87, "security_score": 40, "development_score": 90, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 54.400000000000006, "risk_factors": "Critical features (+4); Vulnerability keywords (+6); Optimal star range (+10)", "health_factors": "Recently active (last 30 days); No open issues; Recent release", "security_factors": "No security policy found; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-07-29T01:50:13Z", "created_at": "2022-12-07T14:52:33Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "qcod/laravel-imageup", "repo_url": "https://github.com/qcod/laravel-imageup", "clone_url": "https://github.com/qcod/laravel-imageup.git", "stars": 769, "issues": 7, "language": "PHP", "description": "Auto Image & file upload, resize and crop for Laravel eloquent model using Intervention image", "risk_score": 40, "health_score": 77, "security_score": 25, "development_score": 100, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 54.15, "risk_factors": "High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3)", "health_factors": "Moderately active (last 90 days); Few open issues; Recent release", "security_factors": "No security policy found; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-05-15T03:56:48Z", "created_at": "2018-09-21T11:49:28Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "simplesamlphp/simplesamlphp", "repo_url": "https://github.com/simplesamlphp/simplesamlphp", "clone_url": "https://github.com/simplesamlphp/simplesamlphp.git", "stars": 1115, "issues": 114, "language": "PHP", "description": "SimpleSAMLphp is an application written in native PHP that deals with authentication.", "risk_score": 37, "health_score": 65, "security_score": 50, "development_score": 100, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 54.0, "risk_factors": "Critical features (+4); Vulnerability keywords (+3); Optimal star range (+10)", "health_factors": "Recently active (last 30 days); Many open issues (114); Recent release", "security_factors": "Has SECURITY.md policy; No branch protection; Has CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-07-25T12:21:47Z", "created_at": "2014-02-25T09:01:45Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "scheb/2fa", "repo_url": "https://github.com/scheb/2fa", "clone_url": "https://github.com/scheb/2fa.git", "stars": 553, "issues": 6, "language": "PHP", "description": "Two-factor authentication for Symfony applications 🔐", "risk_score": 32, "health_score": 77, "security_score": 50, "development_score": 100, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 53.9, "risk_factors": "Critical features (+4); Vulnerability keywords (+3); Optimal star range (+10)", "health_factors": "Moderately active (last 90 days); Few open issues; Recent release", "security_factors": "Has SECURITY.md policy; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-06-27T12:43:42Z", "created_at": "2020-02-09T15:59:35Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "stephen<PERSON>de/filament-debugger", "repo_url": "https://github.com/stephenjude/filament-debugger", "clone_url": "https://github.com/stephenjude/filament-debugger.git", "stars": 93, "issues": 0, "language": "PHP", "description": "Easily add Telescope, Horizon and Laravel Pulse to Filament admin panel.", "risk_score": 30, "health_score": 95, "security_score": 40, "development_score": 90, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 53.5, "risk_factors": "High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3)", "health_factors": "Recently active (last 30 days); No open issues; Recent release", "security_factors": "No security policy found; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-07-28T06:46:38Z", "created_at": "2022-08-11T08:54:27Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "renoki-co/php-k8s", "repo_url": "https://github.com/renoki-co/php-k8s", "clone_url": "https://github.com/renoki-co/php-k8s.git", "stars": 314, "issues": 4, "language": "PHP", "description": "Unofficial PHP client for Kubernetes. It supports any form of authentication, the exec API, and it has an easy implementation for CRDs.", "risk_score": 38, "health_score": 75, "security_score": 30, "development_score": 100, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 53.5, "risk_factors": "Critical features (+4); Vulnerability keywords (+9); Optimal star range (+10)", "health_factors": "Recently active (last 30 days); Few open issues; Old releases", "security_factors": "No security policy found; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-07-21T09:24:33Z", "created_at": "2020-04-29T17:19:13Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "spatie/livewire-filepond", "repo_url": "https://github.com/spatie/livewire-filepond", "clone_url": "https://github.com/spatie/livewire-filepond.git", "stars": 289, "issues": 9, "language": "PHP", "description": "Upload files using Filepond in Livewire components", "risk_score": 31, "health_score": 77, "security_score": 55, "development_score": 95, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 53.4, "risk_factors": "High-risk application type (+8); Vulnerability keywords (+3); Optimal star range (+10)", "health_factors": "Moderately active (last 90 days); Few open issues; Recent release", "security_factors": "No security policy found; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-05-26T07:36:17Z", "created_at": "2024-07-13T14:21:32Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "e107inc/e107", "repo_url": "https://github.com/e107inc/e107", "clone_url": "https://github.com/e107inc/e107.git", "stars": 329, "issues": 691, "language": "PHP", "description": "e107 Bootstrap CMS (Content Management System) v2 with PHP, MySQL, HTML5, jQuery and Twitter Bootstrap. Issue Discussion Room: https://gitter.im/e107inc/e107 ", "risk_score": 44, "health_score": 50, "security_score": 65, "development_score": 75, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 53.0, "risk_factors": "High-risk application type (+8); Vulnerability keywords (+6); Optimal star range (+10)", "health_factors": "Recently active (last 30 days); Many open issues (691); Old releases", "security_factors": "Has SECURITY.md policy; No branch protection; Has CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-07-20T18:33:58Z", "created_at": "2012-11-16T19:49:01Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "silverstripe/silverstripe-elemental", "repo_url": "https://github.com/silverstripe/silverstripe-elemental", "clone_url": "https://github.com/silverstripe/silverstripe-elemental.git", "stars": 112, "issues": 119, "language": "PHP", "description": "Create pages in Silverstripe CMS using content blocks", "risk_score": 38, "health_score": 75, "security_score": 40, "development_score": 85, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 52.75, "risk_factors": "High-risk application type (+8); Optimal star range (+10); High issue count (+10)", "health_factors": "Recently active (last 30 days); Many open issues (119); Recent release", "security_factors": "No security policy found; No branch protection; Has CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-07-23T01:37:03Z", "created_at": "2014-08-26T05:27:34Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "rappasoft/laravel-authentication-log", "repo_url": "https://github.com/rappasoft/laravel-authentication-log", "clone_url": "https://github.com/rappasoft/laravel-authentication-log.git", "stars": 901, "issues": 20, "language": "PHP", "description": "Log user authentication details and send new device notifications.", "risk_score": 32, "health_score": 67, "security_score": 55, "development_score": 100, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 52.65, "risk_factors": "Critical features (+4); Vulnerability keywords (+3); Optimal star range (+10)", "health_factors": "Moderately active (last 90 days); Moderate open issues; Release within year", "security_factors": "Has .github/SECURITY.md policy; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-05-24T03:13:59Z", "created_at": "2021-09-29T20:40:50Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "Laragear/WebAuthn", "repo_url": "https://github.com/Laragear/WebAuthn", "clone_url": "https://github.com/Laragear/WebAuthn.git", "stars": 364, "issues": 0, "language": "PHP", "description": "Authenticate users with Passkeys: fingerprints, patterns and biometric data.", "risk_score": 28, "health_score": 92, "security_score": 45, "development_score": 85, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 51.900000000000006, "risk_factors": "Vulnerability keywords (+3); Optimal star range (+10); High-risk language: php (+10)", "health_factors": "Recently active (last 30 days); No open issues; Recent release", "security_factors": "No security policy found; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-07-16T15:11:56Z", "created_at": "2022-06-14T09:13:53Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "usefulteam/jwt-auth", "repo_url": "https://github.com/usefulteam/jwt-auth", "clone_url": "https://github.com/usefulteam/jwt-auth.git", "stars": 133, "issues": 52, "language": "PHP", "description": "WordPress JSON Web Token Authentication", "risk_score": 48, "health_score": 60, "security_score": 40, "development_score": 65, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 51.75, "risk_factors": "High-risk application type (+8); Critical features (+4); Vulnerability keywords (+6)", "health_factors": "Recently active (last 30 days); Many open issues (52); Old releases", "security_factors": "No security policy found; Security info in README; No branch protection", "development_factors": "Some semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-07-24T15:16:38Z", "created_at": "2020-05-05T09:19:46Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "laravel/fortify", "repo_url": "https://github.com/laravel/fortify", "clone_url": "https://github.com/laravel/fortify.git", "stars": 1673, "issues": 1, "language": "PHP", "description": "Backend controllers and scaffolding for Laravel authentication.", "risk_score": 22, "health_score": 87, "security_score": 60, "development_score": 95, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 51.650000000000006, "risk_factors": "Critical features (+4); Vulnerability keywords (+3); High-risk language: php (+10)", "health_factors": "Recently active (last 30 days); Few open issues; Recent release", "security_factors": "Has .github/SECURITY.md policy; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-07-22T15:26:32Z", "created_at": "2020-08-31T19:23:52Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "concretecms/concretecms", "repo_url": "https://github.com/concretecms/concretecms", "clone_url": "https://github.com/concretecms/concretecms.git", "stars": 801, "issues": 548, "language": "PHP", "description": "Official repository for Concrete CMS development", "risk_score": 38, "health_score": 62, "security_score": 35, "development_score": 100, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 51.65, "risk_factors": "High-risk application type (+8); Optimal star range (+10); High issue count (+10)", "health_factors": "Recently active (last 30 days); Many open issues (548); Recent release", "security_factors": "No security policy found; No branch protection; Has CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-07-17T21:19:33Z", "created_at": "2014-04-22T18:12:28Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "stephenjude/filament-two-factor-authentication", "repo_url": "https://github.com/stephenjude/filament-two-factor-authentication", "clone_url": "https://github.com/stephenjude/filament-two-factor-authentication.git", "stars": 70, "issues": 3, "language": "PHP", "description": "Add two factor authentication (2FA) to Filament panels.", "risk_score": 17, "health_score": 87, "security_score": 70, "development_score": 100, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 51.400000000000006, "risk_factors": "Critical features (+4); Vulnerability keywords (+3); High-risk language: php (+10)", "health_factors": "Recently active (last 30 days); Few open issues; Recent release", "security_factors": "Has .github/SECURITY.md policy; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-07-24T11:51:54Z", "created_at": "2024-08-13T13:48:28Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "aymanalhattami/filament-context-menu", "repo_url": "https://github.com/aymanalhattami/filament-context-menu", "clone_url": "https://github.com/aymanalhattami/filament-context-menu.git", "stars": 97, "issues": 5, "language": "PHP", "description": "Add a context menu (right click menu) for resource pages, custom pages and table cells of Filament Admin Panel.", "risk_score": 25, "health_score": 67, "security_score": 70, "development_score": 100, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 51.4, "risk_factors": "High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3)", "health_factors": "Moderately active (last 90 days); Few open issues; Release within year", "security_factors": "Has .github/SECURITY.md policy; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-06-16T14:34:57Z", "created_at": "2024-04-17T18:05:01Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "DirectoryTree/LdapRecord-Laravel", "repo_url": "https://github.com/DirectoryTree/LdapRecord-Laravel", "clone_url": "https://github.com/DirectoryTree/LdapRecord-Laravel.git", "stars": 548, "issues": 5, "language": "PHP", "description": "Multi-domain LDAP Authentication & Management for Laravel.", "risk_score": 36, "health_score": 77, "security_score": 35, "development_score": 85, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 51.4, "risk_factors": "Critical features (+8); Vulnerability keywords (+3); Optimal star range (+10)", "health_factors": "Moderately active (last 90 days); Few open issues; Recent release", "security_factors": "No security policy found; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-06-23T18:32:29Z", "created_at": "2019-04-18T03:18:50Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "pantheon-systems/wp-saml-auth", "repo_url": "https://github.com/pantheon-systems/wp-saml-auth", "clone_url": "https://github.com/pantheon-systems/wp-saml-auth.git", "stars": 93, "issues": 33, "language": "PHP", "description": "Rock-solid SAML authentication for WordPress built on a modern foundation.", "risk_score": 25, "health_score": 85, "security_score": 55, "development_score": 90, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 51.25, "risk_factors": "High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3)", "health_factors": "Recently active (last 30 days); Moderate open issues; Recent release", "security_factors": "No security policy found; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-07-22T15:16:04Z", "created_at": "2016-04-08T11:39:24Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "spatie/laravel-one-time-passwords", "repo_url": "https://github.com/spatie/laravel-one-time-passwords", "clone_url": "https://github.com/spatie/laravel-one-time-passwords.git", "stars": 122, "issues": 1, "language": "PHP", "description": "Use one time passwords (OTP) to authenticate in your Laravel app", "risk_score": 26, "health_score": 87, "security_score": 40, "development_score": 95, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 50.650000000000006, "risk_factors": "Vulnerability keywords (+6); Optimal star range (+10); High-risk language: php (+10)", "health_factors": "Recently active (last 30 days); Few open issues; Recent release", "security_factors": "No security policy found; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-07-28T18:03:44Z", "created_at": "2025-04-10T13:40:25Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "coderello/laravel-passport-social-grant", "repo_url": "https://github.com/coderello/laravel-passport-social-grant", "clone_url": "https://github.com/coderello/laravel-passport-social-grant.git", "stars": 179, "issues": 0, "language": "PHP", "description": "🔒 API authentication via social networks for your Laravel application", "risk_score": 30, "health_score": 77, "security_score": 30, "development_score": 100, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 49.9, "risk_factors": "Critical features (+4); Vulnerability keywords (+6); Optimal star range (+10)", "health_factors": "Recently active (last 30 days); No open issues; Recent release", "security_factors": "No security policy found; No branch protection; Has CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-07-13T11:38:24Z", "created_at": "2018-07-06T11:12:57Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "TypiCMS/Base", "repo_url": "https://github.com/TypiCMS/Base", "clone_url": "https://github.com/TypiCMS/Base.git", "stars": 1450, "issues": 13, "language": "PHP", "description": "Multilingual CMS built with Laravel.", "risk_score": 28, "health_score": 72, "security_score": 40, "development_score": 95, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 48.65, "risk_factors": "High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10)", "health_factors": "Recently active (last 30 days); Moderate open issues; Recent release", "security_factors": "Has SECURITY.md policy; No branch protection; No CI/CD workflows", "development_factors": "Follows semantic versioning; Has testing infrastructure; Comprehensive README", "last_updated": "2025-07-25T12:05:29Z", "created_at": "2014-12-26T14:15:30Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "cesargb/laravel-magiclink", "repo_url": "https://github.com/cesargb/laravel-magiclink", "clone_url": "https://github.com/cesargb/laravel-magiclink.git", "stars": 428, "issues": 4, "language": "PHP", "description": "Create link for authenticate in Laravel without password or get private content", "risk_score": 26, "health_score": 77, "security_score": 35, "development_score": 100, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 48.65, "risk_factors": "Vulnerability keywords (+6); Optimal star range (+10); High-risk language: php (+10)", "health_factors": "Moderately active (last 90 days); Few open issues; Recent release", "security_factors": "No security policy found; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-06-20T11:50:34Z", "created_at": "2017-07-25T18:31:29Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "redaxo/redaxo", "repo_url": "https://github.com/redaxo/redaxo", "clone_url": "https://github.com/redaxo/redaxo.git", "stars": 339, "issues": 130, "language": "PHP", "description": "REDAXO, a PHP-based CMS since 2004. Both simple and flexible.", "risk_score": 38, "health_score": 65, "security_score": 45, "development_score": 65, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 48.5, "risk_factors": "High-risk application type (+8); Optimal star range (+10); High issue count (+10)", "health_factors": "Recently active (last 30 days); Many open issues (130); Recent release", "security_factors": "Has SECURITY.md policy; No branch protection; Has CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-07-27T16:02:31Z", "created_at": "2010-07-04T16:46:03Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "10up/eight-day-week", "repo_url": "https://github.com/10up/eight-day-week", "clone_url": "https://github.com/10up/eight-day-week.git", "stars": 88, "issues": 9, "language": "PHP", "description": "Optimize print publication workflows by using WordPress as your print CMS.", "risk_score": 26, "health_score": 75, "security_score": 50, "development_score": 85, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 48.25, "risk_factors": "High-risk application type (+16); High-risk language: php (+10)", "health_factors": "Moderately active (last 90 days); Few open issues; Release within year", "security_factors": "No security policy found; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-06-23T15:14:05Z", "created_at": "2015-10-08T15:48:20Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "andrewdwallo/filament-companies", "repo_url": "https://github.com/andrewdwallo/filament-companies", "clone_url": "https://github.com/andrewdwallo/filament-companies.git", "stars": 331, "issues": 3, "language": "PHP", "description": "A comprehensive Laravel authentication and authorization system designed for Filament, focusing on multi-tenant company management.", "risk_score": 32, "health_score": 80, "security_score": 20, "development_score": 85, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 47.75, "risk_factors": "Critical features (+4); Vulnerability keywords (+3); Optimal star range (+10)", "health_factors": "Moderately active (last 90 days); Few open issues; Recent release", "security_factors": "No security policy found; No branch protection; Has CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-06-16T05:24:25Z", "created_at": "2022-12-18T02:26:37Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "haxtheweb/haxcms-php", "repo_url": "https://github.com/haxtheweb/haxcms-php", "clone_url": "https://github.com/haxtheweb/haxcms-php.git", "stars": 125, "issues": 4, "language": "PHP", "description": "HAX + CMS to manage your microsite universe with PHP backend", "risk_score": 28, "health_score": 77, "security_score": 35, "development_score": 85, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 47.4, "risk_factors": "High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10)", "health_factors": "Recently active (last 30 days); Few open issues; Recent release", "security_factors": "Has SECURITY.md policy; No branch protection; No CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-07-25T17:09:51Z", "created_at": "2018-07-19T13:41:35Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "concretecms-community-store/community_store", "repo_url": "https://github.com/concretecms-community-store/community_store", "clone_url": "https://github.com/concretecms-community-store/community_store.git", "stars": 109, "issues": 56, "language": "PHP", "description": "An open, free and community developed eCommerce system for Concrete CMS", "risk_score": 33, "health_score": 70, "security_score": 40, "development_score": 70, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 47.0, "risk_factors": "High-risk application type (+8); Optimal star range (+10); High issue count (+5)", "health_factors": "Recently active (last 30 days); Many open issues (56); Release within year", "security_factors": "No security policy found; No branch protection; Has CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-07-22T11:17:06Z", "created_at": "2016-02-20T11:13:54Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "dustin10/VichUploaderBundle", "repo_url": "https://github.com/dustin10/VichUploaderBundle", "clone_url": "https://github.com/dustin10/VichUploaderBundle.git", "stars": 1888, "issues": 55, "language": "PHP", "description": "A simple Symfony bundle to ease file uploads with ORM entities and ODM documents.", "risk_score": 30, "health_score": 72, "security_score": 15, "development_score": 100, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 46.65, "risk_factors": "High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3)", "health_factors": "Recently active (last 30 days); Many open issues (55); Recent release", "security_factors": "No security policy found; No branch protection; Has CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-07-23T06:29:54Z", "created_at": "2011-12-01T22:18:22Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "oc-shopaholic/oc-shopaholic-plugin", "repo_url": "https://github.com/oc-shopaholic/oc-shopaholic-plugin", "clone_url": "https://github.com/oc-shopaholic/oc-shopaholic-plugin.git", "stars": 426, "issues": 91, "language": "PHP", "description": "🛍️ No. 1 e-commerce platform for October CMS ", "risk_score": 37, "health_score": 57, "security_score": 20, "development_score": 90, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 46.4, "risk_factors": "High-risk application type (+8); Optimal star range (+10); High issue count (+9)", "health_factors": "Recently active (last 30 days); Many open issues (91); Old releases", "security_factors": "No security policy found; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has testing infrastructure; Comprehensive README", "last_updated": "2025-07-02T20:18:20Z", "created_at": "2016-10-26T09:09:04Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "s-cart/s-cart", "repo_url": "https://github.com/s-cart/s-cart", "clone_url": "https://github.com/s-cart/s-cart.git", "stars": 721, "issues": 4, "language": "PHP", "description": "Free Laravel e-commerce for business: shopping cart, cms content, and more...", "risk_score": 33, "health_score": 70, "security_score": 30, "development_score": 75, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 46.25, "risk_factors": "High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10)", "health_factors": "Recently active (last 30 days); Few open issues; Recent release", "security_factors": "No security policy found; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-07-28T23:11:02Z", "created_at": "2020-04-01T16:14:53Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "solutionforest/filament-scaffold", "repo_url": "https://github.com/solutionforest/filament-scaffold", "clone_url": "https://github.com/solutionforest/filament-scaffold.git", "stars": 56, "issues": 0, "language": "PHP", "description": "Filament scaffold is a toolkit for Filament Admin that simplifies the generation of resources in the panel", "risk_score": 13, "health_score": 87, "security_score": 45, "development_score": 100, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 45.650000000000006, "risk_factors": "Vulnerability keywords (+3); High-risk language: php (+10)", "health_factors": "Recently active (last 30 days); No open issues; Recent release", "security_factors": "Has .github/SECURITY.md policy; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-07-28T16:04:18Z", "created_at": "2024-07-22T04:22:46Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "danpros/htmly", "repo_url": "https://github.com/danpros/htmly", "clone_url": "https://github.com/danpros/htmly.git", "stars": 1212, "issues": 24, "language": "PHP", "description": "Simple and fast databaseless PHP blogging platform, and Flat-File CMS", "risk_score": 28, "health_score": 82, "security_score": 35, "development_score": 65, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 45.400000000000006, "risk_factors": "High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10)", "health_factors": "Recently active (last 30 days); Moderate open issues; Recent release", "security_factors": "Has SECURITY.md policy; No branch protection; No CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-07-19T00:42:40Z", "created_at": "2013-12-25T01:35:51Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "WonderCMS/wondercms", "repo_url": "https://github.com/WonderCMS/wondercms", "clone_url": "https://github.com/WonderCMS/wondercms.git", "stars": 702, "issues": 3, "language": "PHP", "description": "Fast and small flat file CMS (5 files). Built with PHP, JSON database.", "risk_score": 28, "health_score": 85, "security_score": 30, "development_score": 65, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 45.25, "risk_factors": "High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10)", "health_factors": "Recently active (last 30 days); Few open issues; Release within year", "security_factors": "Has .github/SECURITY.md policy; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-07-12T17:42:45Z", "created_at": "2013-12-15T13:27:38Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "nextcloud/cms_pico", "repo_url": "https://github.com/nextcloud/cms_pico", "clone_url": "https://github.com/nextcloud/cms_pico.git", "stars": 137, "issues": 23, "language": "PHP", "description": "🗃 Integrate Pico CMS and let your users manage their own websites", "risk_score": 28, "health_score": 60, "security_score": 50, "development_score": 75, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 44.75, "risk_factors": "High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10)", "health_factors": "Moderately active (last 90 days); Moderate open issues; Old releases", "security_factors": "No security policy found; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-06-06T16:21:29Z", "created_at": "2017-09-06T10:21:09Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "DirectoryTree/<PERSON>ender", "repo_url": "https://github.com/DirectoryTree/Bartender", "clone_url": "https://github.com/DirectoryTree/Bartender.git", "stars": 274, "issues": 0, "language": "PHP", "description": "An opinionated way to authenticate users using Laravel Socialite.", "risk_score": 23, "health_score": 75, "security_score": 35, "development_score": 85, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 44.5, "risk_factors": "Vulnerability keywords (+3); Optimal star range (+10); High-risk language: php (+10)", "health_factors": "Recently active (last 30 days); No open issues; Release within year", "security_factors": "No security policy found; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-07-18T14:16:57Z", "created_at": "2024-04-01T13:22:45Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "Really-Simple-Plugins/really-simple-ssl", "repo_url": "https://github.com/Really-Simple-Plugins/really-simple-ssl", "clone_url": "https://github.com/Really-Simple-Plugins/really-simple-ssl.git", "stars": 156, "issues": 5, "language": "PHP", "description": "Easily improve site security with WordPress Hardening, Two-Factor Authentication (2FA), Login Protection, Vulnerability Detection and SSL certificate generation.", "risk_score": 38, "health_score": 75, "security_score": 15, "development_score": 55, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 44.5, "risk_factors": "High-risk application type (+8); Critical features (+4); Vulnerability keywords (+6)", "health_factors": "Recently active (last 30 days); Few open issues; Release within year", "security_factors": "No security policy found; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-07-29T11:40:18Z", "created_at": "2017-01-19T12:28:45Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "zenphoto/zenphoto", "repo_url": "https://github.com/zenphoto/zenphoto", "clone_url": "https://github.com/zenphoto/zenphoto.git", "stars": 311, "issues": 24, "language": "PHP", "description": "The Zenphoto open-source gallery and CMS project", "risk_score": 36, "health_score": 72, "security_score": 30, "development_score": 50, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 44.4, "risk_factors": "High-risk application type (+16); Optimal star range (+10); High-risk language: php (+10)", "health_factors": "Recently active (last 30 days); Moderate open issues; Recent release", "security_factors": "Has SECURITY.md policy; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-07-11T21:03:47Z", "created_at": "2012-10-19T14:13:03Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "enhavo/enhavo", "repo_url": "https://github.com/enhavo/enhavo", "clone_url": "https://github.com/enhavo/enhavo.git", "stars": 88, "issues": 39, "language": "PHP", "description": "Modern CMS based on fullstack symfony and vue", "risk_score": 18, "health_score": 72, "security_score": 50, "development_score": 90, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 44.4, "risk_factors": "High-risk application type (+8); High-risk language: php (+10)", "health_factors": "Recently active (last 30 days); Moderate open issues; Recent release", "security_factors": "Has SECURITY.md policy; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-07-25T08:51:59Z", "created_at": "2015-07-02T15:17:17Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "codepress/admin-columns", "repo_url": "https://github.com/codepress/admin-columns", "clone_url": "https://github.com/codepress/admin-columns.git", "stars": 67, "issues": 6, "language": "PHP", "description": "Admin Columns allows you to manage and organize columns in the posts, users, comments, and media lists tables in the WordPress admin panel. Transform the WordPress admin screens into beautiful, clear overviews.", "risk_score": 33, "health_score": 75, "security_score": 20, "development_score": 65, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 44.25, "risk_factors": "High-risk application type (+16); Critical features (+4); Vulnerability keywords (+3)", "health_factors": "Recently active (last 30 days); Few open issues; Recent release", "security_factors": "No security policy found; No branch protection; No CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-07-29T13:08:42Z", "created_at": "2013-03-13T11:40:07Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "webreinvent/vaahcms", "repo_url": "https://github.com/webreinvent/vaahcms", "clone_url": "https://github.com/webreinvent/vaahcms.git", "stars": 572, "issues": 11, "language": "PHP", "description": "VaahCMS is a laravel based open-source web application development platform shipped with a headless content management system (CMS).", "risk_score": 33, "health_score": 75, "security_score": 15, "development_score": 70, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 44.25, "risk_factors": "High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10)", "health_factors": "Recently active (last 30 days); Moderate open issues; Recent release", "security_factors": "No security policy found; No branch protection; Has CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-07-22T12:59:20Z", "created_at": "2019-05-11T13:41:08Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "FriendsOfTYPO3/content-blocks", "repo_url": "https://github.com/FriendsOfTYPO3/content-blocks", "clone_url": "https://github.com/FriendsOfTYPO3/content-blocks.git", "stars": 83, "issues": 31, "language": "PHP", "description": "TYPO3 CMS Content Blocks - Content Types API", "risk_score": 26, "health_score": 85, "security_score": 30, "development_score": 65, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 44.25, "risk_factors": "High-risk application type (+8); Vulnerability keywords (+3); High-risk language: php (+10)", "health_factors": "Recently active (last 30 days); Moderate open issues; Recent release", "security_factors": "No security policy found; No branch protection; Has CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-07-26T22:37:19Z", "created_at": "2023-06-25T09:57:44Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "spicywebau/craft-neo", "repo_url": "https://github.com/spicywebau/craft-neo", "clone_url": "https://github.com/spicywebau/craft-neo.git", "stars": 396, "issues": 34, "language": "PHP", "description": "A Matrix-like field type for Craft CMS that uses existing fields", "risk_score": 28, "health_score": 72, "security_score": 30, "development_score": 75, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 44.15, "risk_factors": "High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10)", "health_factors": "Recently active (last 30 days); Moderate open issues; Recent release", "security_factors": "No security policy found; No branch protection; Has CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-07-29T03:47:19Z", "created_at": "2015-12-07T10:00:40Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "craftcms/commerce", "repo_url": "https://github.com/craftcms/commerce", "clone_url": "https://github.com/craftcms/commerce.git", "stars": 234, "issues": 36, "language": "PHP", "description": "Fully integrated ecommerce for Craft CMS.", "risk_score": 28, "health_score": 72, "security_score": 35, "development_score": 70, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 44.15, "risk_factors": "High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10)", "health_factors": "Recently active (last 30 days); Moderate open issues; Recent release", "security_factors": "No security policy found; No branch protection; Has CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-07-29T08:01:53Z", "created_at": "2015-04-23T21:02:38Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "Flute-CMS/cms", "repo_url": "https://github.com/Flute-CMS/cms", "clone_url": "https://github.com/Flute-CMS/cms.git", "stars": 106, "issues": 0, "language": "PHP", "description": "Web-based CMS for server games written on PHP", "risk_score": 28, "health_score": 77, "security_score": 30, "development_score": 65, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 43.65, "risk_factors": "High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10)", "health_factors": "Recently active (last 30 days); No open issues; Recent release", "security_factors": "No security policy found; No branch protection; Has CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-07-24T23:34:15Z", "created_at": "2024-03-03T07:11:11Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "getformwork/formwork", "repo_url": "https://github.com/getformwork/formwork", "clone_url": "https://github.com/getformwork/formwork.git", "stars": 61, "issues": 1, "language": "PHP", "description": "🏗 A flat file-based Content Management System (CMS) to build and manage sites with simplicity", "risk_score": 18, "health_score": 72, "security_score": 50, "development_score": 85, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 43.65, "risk_factors": "High-risk application type (+8); High-risk language: php (+10)", "health_factors": "Recently active (last 30 days); Few open issues; Recent release", "security_factors": "Has SECURITY.md policy; No branch protection; Has CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-07-26T13:52:31Z", "created_at": "2018-06-16T14:39:18Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "Ecodev/graphql-upload", "repo_url": "https://github.com/Ecodev/graphql-upload", "clone_url": "https://github.com/Ecodev/graphql-upload.git", "stars": 90, "issues": 1, "language": "PHP", "description": "A PSR-15 middleware to support file uploads in GraphQL", "risk_score": 25, "health_score": 67, "security_score": 30, "development_score": 85, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 43.15, "risk_factors": "High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3)", "health_factors": "Recently active (last 30 days); Few open issues; Old releases", "security_factors": "No security policy found; No branch protection; Has CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-07-08T10:19:54Z", "created_at": "2018-01-05T09:28:25Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "nystudio107/craft-seomatic", "repo_url": "https://github.com/nystudio107/craft-seomatic", "clone_url": "https://github.com/nystudio107/craft-seomatic.git", "stars": 171, "issues": 53, "language": "PHP", "description": "SEOmatic facilitates modern SEO best practices & implementation for Craft CMS 3. It is a turnkey SEO system that is comprehensive, powerful, and flexible.", "risk_score": 33, "health_score": 65, "security_score": 15, "development_score": 75, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 43.0, "risk_factors": "High-risk application type (+8); Optimal star range (+10); High issue count (+5)", "health_factors": "Recently active (last 30 days); Many open issues (53); Recent release", "security_factors": "No security policy found; No branch protection; Has CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-07-22T12:57:17Z", "created_at": "2017-02-18T19:18:55Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "nette/security", "repo_url": "https://github.com/nette/security", "clone_url": "https://github.com/nette/security.git", "stars": 366, "issues": 7, "language": "PHP", "description": "🔑 Provides authentication, authorization and a role-based access control management via ACL (Access Control List)", "risk_score": 27, "health_score": 72, "security_score": 35, "development_score": 65, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 42.9, "risk_factors": "Critical features (+4); Vulnerability keywords (+3); Optimal star range (+10)", "health_factors": "Moderately active (last 90 days); Few open issues; Release within year", "security_factors": "No security policy found; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-06-19T17:48:02Z", "created_at": "2014-03-13T03:47:30Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "a<PERSON><PERSON>/kirby-vite", "repo_url": "https://github.com/arnoson/kirby-vite", "clone_url": "https://github.com/arnoson/kirby-vite.git", "stars": 91, "issues": 1, "language": "PHP", "description": "Use Kirby CMS together with Vite", "risk_score": 23, "health_score": 85, "security_score": 30, "development_score": 65, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 42.75, "risk_factors": "High-risk application type (+8); High-risk language: php (+10); Vulnerable age: 4.6y (+5)", "health_factors": "Recently active (last 30 days); Few open issues; Recent release", "security_factors": "No security policy found; No branch protection; Has CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-07-02T08:34:49Z", "created_at": "2020-12-11T20:40:40Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "mattiverse/Laravel-Userstamps", "repo_url": "https://github.com/mattiverse/Laravel-Userstamps", "clone_url": "https://github.com/mattiverse/Laravel-Userstamps.git", "stars": 621, "issues": 2, "language": "PHP", "description": "Laravel Userstamps provides an Eloquent trait which automatically maintains created_by and updated_by columns on your model, populated by the currently authenticated user in your application.", "risk_score": 23, "health_score": 70, "security_score": 30, "development_score": 85, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 42.75, "risk_factors": "Vulnerability keywords (+3); Optimal star range (+10); High-risk language: php (+10)", "health_factors": "Moderately active (last 90 days); Few open issues; Recent release", "security_factors": "No security policy found; No branch protection; Has CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-06-09T07:06:36Z", "created_at": "2016-03-10T18:14:16Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "oveleon/contao-cookiebar", "repo_url": "https://github.com/oveleon/contao-cookiebar", "clone_url": "https://github.com/oveleon/contao-cookiebar.git", "stars": 59, "issues": 2, "language": "PHP", "description": "Cookie bar for the Contao Open Source CMS", "risk_score": 23, "health_score": 87, "security_score": 20, "development_score": 70, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 42.400000000000006, "risk_factors": "High-risk application type (+8); High-risk language: php (+10); Vulnerable age: 5.1y (+5)", "health_factors": "Recently active (last 30 days); Few open issues; Recent release", "security_factors": "No security policy found; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-07-09T13:36:22Z", "created_at": "2020-07-07T08:40:07Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "et-nik/gameap", "repo_url": "https://github.com/et-nik/gameap", "clone_url": "https://github.com/et-nik/gameap.git", "stars": 115, "issues": 40, "language": "PHP", "description": "Game Admin Panel (GameAP) is the opensource game servers control panel.", "risk_score": 40, "health_score": 52, "security_score": 20, "development_score": 60, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 42.4, "risk_factors": "High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3)", "health_factors": "Moderately active (last 90 days); Moderate open issues; Old releases", "security_factors": "No security policy found; No branch protection; No CI/CD workflows", "development_factors": "Some semantic versioning; Has testing infrastructure; Comprehensive README", "last_updated": "2025-05-15T07:09:45Z", "created_at": "2018-11-21T12:01:13Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "spicywebau/craft-embedded-assets", "repo_url": "https://github.com/spicywebau/craft-embedded-assets", "clone_url": "https://github.com/spicywebau/craft-embedded-assets.git", "stars": 171, "issues": 19, "language": "PHP", "description": "Manage YouTube videos, Instagram photos and more as first class assets in Craft CMS", "risk_score": 28, "health_score": 67, "security_score": 30, "development_score": 70, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 42.4, "risk_factors": "High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10)", "health_factors": "Moderately active (last 90 days); Moderate open issues; Release within year", "security_factors": "No security policy found; No branch protection; Has CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-05-26T11:44:04Z", "created_at": "2015-12-05T06:33:46Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "nextcloud/user_saml", "repo_url": "https://github.com/nextcloud/user_saml", "clone_url": "https://github.com/nextcloud/user_saml.git", "stars": 96, "issues": 146, "language": "PHP", "description": ":lock: App for authenticating Nextcloud users using SAML https://apps.nextcloud.com/apps/user_saml", "risk_score": 23, "health_score": 72, "security_score": 45, "development_score": 65, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 42.4, "risk_factors": "Vulnerability keywords (+3); High issue count (+10); High-risk language: php (+10)", "health_factors": "Recently active (last 30 days); Many open issues (144); Recent release", "security_factors": "No security policy found; No branch protection; Has CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-07-28T01:50:53Z", "created_at": "2016-06-28T22:02:50Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "mostafaznv/larupload", "repo_url": "https://github.com/mostafaznv/larupload", "clone_url": "https://github.com/mostafaznv/larupload.git", "stars": 67, "issues": 1, "language": "PHP", "description": "Larupload is an ORM based file uploader for laravel to upload image, video, audio and other known files.", "risk_score": 25, "health_score": 60, "security_score": 25, "development_score": 90, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 41.75, "risk_factors": "High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3)", "health_factors": "Low activity (last year); Few open issues; Release within year", "security_factors": "No security policy found; No branch protection; Has CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-04-27T08:20:07Z", "created_at": "2018-07-20T11:21:59Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "rainlab/builder-plugin", "repo_url": "https://github.com/rainlab/builder-plugin", "clone_url": "https://github.com/rainlab/builder-plugin.git", "stars": 171, "issues": 2, "language": "PHP", "description": "Visual development tool for October CMS", "risk_score": 28, "health_score": 75, "security_score": 0, "development_score": 85, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 41.75, "risk_factors": "High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10)", "health_factors": "Recently active (last 30 days); Few open issues; Old releases", "security_factors": "No security policy found; No branch protection; No CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-07-26T04:48:03Z", "created_at": "2016-02-10T05:23:43Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "putyourlightson/craft-blitz", "repo_url": "https://github.com/putyourlightson/craft-blitz", "clone_url": "https://github.com/putyourlightson/craft-blitz.git", "stars": 151, "issues": 6, "language": "PHP", "description": "Intelligent static page caching for creating lightning-fast sites with Craft CMS.", "risk_score": 28, "health_score": 77, "security_score": 15, "development_score": 65, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 41.4, "risk_factors": "High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10)", "health_factors": "Recently active (last 30 days); Few open issues; Recent release", "security_factors": "No security policy found; No branch protection; Has CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-07-28T12:27:41Z", "created_at": "2018-06-26T17:31:46Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "Cotonti/Cotonti", "repo_url": "https://github.com/Cotonti/<PERSON>tonti", "clone_url": "https://github.com/Cotonti/Cotonti.git", "stars": 99, "issues": 137, "language": "PHP", "description": "Fast, reliable and flexible PHP CMF/CMS", "risk_score": 28, "health_score": 57, "security_score": 40, "development_score": 65, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 41.15, "risk_factors": "High-risk application type (+8); High issue count (+10); High-risk language: php (+10)", "health_factors": "Recently active (last 30 days); Many open issues (137); Release within year", "security_factors": "No security policy found; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-07-11T16:01:36Z", "created_at": "2010-10-24T12:17:40Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "nukeviet/nukeviet", "repo_url": "https://github.com/nukeviet/nukeviet", "clone_url": "https://github.com/nukeviet/nukeviet.git", "stars": 158, "issues": 85, "language": "PHP", "description": "NukeViet CMS is multi Content Management System. NukeViet CMS is the 1st open source content management system in Vietnam. NukeViet was awarded the Vietnam Talent 2011, the Ministry of Education and Training Vietnam officially encouraged to use.", "risk_score": 36, "health_score": 50, "security_score": 10, "development_score": 75, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 40.75, "risk_factors": "High-risk application type (+8); Optimal star range (+10); High issue count (+8)", "health_factors": "Recently active (last 30 days); Many open issues (85); Old releases", "security_factors": "No security policy found; No branch protection; No CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-07-26T08:35:33Z", "created_at": "2012-11-24T15:39:39Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "liberu-cms/cms-laravel", "repo_url": "https://github.com/liberu-cms/cms-laravel", "clone_url": "https://github.com/liberu-cms/cms-laravel.git", "stars": 81, "issues": 5, "language": "PHP", "description": "CMS application written in Laravel 11 / PHP 8.3 using Filament 3. WordPress alternative.", "risk_score": 26, "health_score": 70, "security_score": 45, "development_score": 45, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 40.5, "risk_factors": "High-risk application type (+16); High-risk language: php (+10)", "health_factors": "Recently active (last 30 days); Few open issues; No releases", "security_factors": "No security policy found; No branch protection; Has CI/CD workflows", "development_factors": "No releases found; Has testing infrastructure; Comprehensive README", "last_updated": "2025-07-25T00:35:02Z", "created_at": "2024-02-04T14:43:16Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "ollieread/multiauth", "repo_url": "https://github.com/ollieread/multiauth", "clone_url": "https://github.com/ollieread/multiauth.git", "stars": 444, "issues": 0, "language": "PHP", "description": "Multiauth is a replacement for Laravels authentication system", "risk_score": 27, "health_score": 52, "security_score": 25, "development_score": 85, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 40.4, "risk_factors": "Critical features (+4); Vulnerability keywords (+3); Optimal star range (+10)", "health_factors": "Moderately active (last 90 days); No open issues; Old releases", "security_factors": "No security policy found; No branch protection; Has CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-06-21T09:06:55Z", "created_at": "2013-11-28T14:34:15Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "KABBOUCHI/nova-impersonate", "repo_url": "https://github.com/KABBOUCHI/nova-impersonate", "clone_url": "https://github.com/KABBOUCHI/nova-impersonate.git", "stars": 234, "issues": 18, "language": "PHP", "description": "A Laravel Nova field allows you to authenticate as your users.", "risk_score": 28, "health_score": 60, "security_score": 25, "development_score": 70, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 40.25, "risk_factors": "Vulnerability keywords (+3); Optimal star range (+10); High-risk language: php (+10)", "health_factors": "Low activity (last year); Moderate open issues; Release within year", "security_factors": "No security policy found; No branch protection; No CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-04-14T08:24:55Z", "created_at": "2018-09-09T16:31:05Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "FusionGen/FusionGEN", "repo_url": "https://github.com/FusionGen/FusionGEN", "clone_url": "https://github.com/FusionGen/FusionGEN.git", "stars": 110, "issues": 10, "language": "PHP", "description": "This is a Continuation of FusionCMS but now under the brand name FusionGEN.  This CMS are an Open-Source Project and anyone may use & contribute this.  The main goal is to get something long-term wise going. This CMS just needs love...!", "risk_score": 33, "health_score": 62, "security_score": 45, "development_score": 30, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 40.15, "risk_factors": "High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10)", "health_factors": "Recently active (last 30 days); Few open issues; No releases", "security_factors": "Has .github/SECURITY.md policy; No branch protection; Has CI/CD workflows", "development_factors": "No releases found; No testing infrastructure detected; Comprehensive README", "last_updated": "2025-07-11T21:41:16Z", "created_at": "2020-05-01T15:31:30Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "PHPAuth/PHPAuth", "repo_url": "https://github.com/PHPAuth/PHPAuth", "clone_url": "https://github.com/PHPAuth/PHPAuth.git", "stars": 886, "issues": 17, "language": "PHP", "description": "  PHPAuth is a secure PHP Authentication class that easily integrates into any site. ", "risk_score": 27, "health_score": 50, "security_score": 20, "development_score": 90, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 40.0, "risk_factors": "Critical features (+4); Vulnerability keywords (+3); Optimal star range (+10)", "health_factors": "Low activity (last year); Moderate open issues; Old releases", "security_factors": "No security policy found; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; Has testing infrastructure", "last_updated": "2025-04-08T15:31:54Z", "created_at": "2014-01-25T21:34:44Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "abdessa<PERSON>bettal/lara4", "repo_url": "https://github.com/abdessamadbettal/lara4", "clone_url": "https://github.com/abdessamadbettal/lara4.git", "stars": 54, "issues": 2, "language": "PHP", "description": "Simplify your Laravel development with Lara4! Build faster and smarter with tools like Inertia.js, Vue, multi-language support, SEO optimization, and Filament CMS - all ready to use.", "risk_score": 18, "health_score": 55, "security_score": 60, "development_score": 70, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 39.5, "risk_factors": "High-risk application type (+8); High-risk language: php (+10)", "health_factors": "Moderately active (last 90 days); Few open issues; No releases", "security_factors": "Has SECURITY.md policy; Security info in README; No branch protection", "development_factors": "No releases found; Has testing infrastructure; Comprehensive README", "last_updated": "2025-05-21T18:18:00Z", "created_at": "2024-11-07T12:51:41Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "fsi-open/admin-bundle", "repo_url": "https://github.com/fsi-open/admin-bundle", "clone_url": "https://github.com/fsi-open/admin-bundle.git", "stars": 58, "issues": 11, "language": "PHP", "description": "FSi Admin Bundle is complete solution that provides mechanisms to generate admin panel for any Symfony2 project.", "risk_score": 25, "health_score": 67, "security_score": 35, "development_score": 55, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 39.4, "risk_factors": "High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3)", "health_factors": "Moderately active (last 90 days); Moderate open issues; Release within year", "security_factors": "No security policy found; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; No testing infrastructure detected; Comprehensive README", "last_updated": "2025-06-10T13:18:54Z", "created_at": "2013-05-22T12:17:16Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "tomatophp/filament-cms", "repo_url": "https://github.com/tomatophp/filament-cms", "clone_url": "https://github.com/tomatophp/filament-cms.git", "stars": 102, "issues": 1, "language": "PHP", "description": "Full CMS System with easy to use page builder & theme manager for FilamentPHP", "risk_score": 28, "health_score": 60, "security_score": 15, "development_score": 70, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 38.75, "risk_factors": "High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10)", "health_factors": "Low activity (last year); Few open issues; Release within year", "security_factors": "Has SECURITY.md policy; No branch protection; No CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-04-16T04:48:30Z", "created_at": "2024-05-09T11:54:48Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "pluxml/PluXml", "repo_url": "https://github.com/pluxml/PluXml", "clone_url": "https://github.com/pluxml/PluXml.git", "stars": 229, "issues": 20, "language": "PHP", "description": "A CMS to create lightweight websites with ease and without database.", "risk_score": 28, "health_score": 67, "security_score": 20, "development_score": 55, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 38.65, "risk_factors": "High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10)", "health_factors": "Recently active (last 30 days); Moderate open issues; Release within year", "security_factors": "Has .github/SECURITY.md policy; No branch protection; No CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-07-27T17:44:28Z", "created_at": "2012-07-26T07:55:26Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "putyourlightson/craft-campaign", "repo_url": "https://github.com/putyourlightson/craft-campaign", "clone_url": "https://github.com/putyourlightson/craft-campaign.git", "stars": 63, "issues": 6, "language": "PHP", "description": "Send and manage email campaigns, contacts and mailing lists in Craft CMS.", "risk_score": 18, "health_score": 87, "security_score": 15, "development_score": 65, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 38.400000000000006, "risk_factors": "High-risk application type (+8); High-risk language: php (+10)", "health_factors": "Recently active (last 30 days); Few open issues; Recent release", "security_factors": "No security policy found; No branch protection; Has CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-07-22T09:08:32Z", "created_at": "2018-03-07T11:23:13Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "craftcms/aws-s3", "repo_url": "https://github.com/craftcms/aws-s3", "clone_url": "https://github.com/craftcms/aws-s3.git", "stars": 62, "issues": 18, "language": "PHP", "description": "Amazon S3 volume type for Craft CMS.", "risk_score": 18, "health_score": 72, "security_score": 30, "development_score": 70, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 38.4, "risk_factors": "High-risk application type (+8); High-risk language: php (+10)", "health_factors": "Moderately active (last 90 days); Moderate open issues; Recent release", "security_factors": "No security policy found; No branch protection; Has CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-06-17T02:45:17Z", "created_at": "2017-01-30T20:26:48Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "kleeja-official/kleeja", "repo_url": "https://github.com/kleeja-official/kleeja", "clone_url": "https://github.com/kleeja-official/kleeja.git", "stars": 185, "issues": 13, "language": "PHP", "description": "⬇️ File Upload/sharing application, used by thousands of webmasters since 2007. ", "risk_score": 35, "health_score": 55, "security_score": 10, "development_score": 55, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 38.25, "risk_factors": "High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3)", "health_factors": "Moderately active (last 90 days); Moderate open issues; Old releases", "security_factors": "No security policy found; No branch protection; No CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-05-07T19:19:54Z", "created_at": "2018-01-08T22:57:26Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "processwire/processwire", "repo_url": "https://github.com/processwire/processwire", "clone_url": "https://github.com/processwire/processwire.git", "stars": 1004, "issues": 71, "language": "PHP", "description": "ProcessWire 3.x is a friendly and powerful open source CMS with a strong API. ", "risk_score": 38, "health_score": 50, "security_score": 15, "development_score": 45, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 38.0, "risk_factors": "High-risk application type (+8); Vulnerability keywords (+3); Optimal star range (+10)", "health_factors": "Recently active (last 30 days); Many open issues (71); No releases", "security_factors": "No security policy found; Security info in README; No branch protection", "development_factors": "No releases found; No testing infrastructure detected; Comprehensive README", "last_updated": "2025-07-25T20:46:12Z", "created_at": "2016-08-29T09:59:35Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "aimeos/ai-cms-grapesjs", "repo_url": "https://github.com/aimeos/ai-cms-grapesjs", "clone_url": "https://github.com/aimeos/ai-cms-grapesjs.git", "stars": 608, "issues": 2, "language": "PHP", "description": "GrapesJS CMS integration into Aimeos", "risk_score": 33, "health_score": 67, "security_score": 15, "development_score": 30, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 36.65, "risk_factors": "High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10)", "health_factors": "Recently active (last 30 days); Few open issues; No releases", "security_factors": "No security policy found; Security info in README; No branch protection", "development_factors": "No releases found; No testing infrastructure detected; Comprehensive README", "last_updated": "2025-07-09T07:05:34Z", "created_at": "2021-01-24T12:59:28Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "KeyAuth/KeyAuth-Source-Code", "repo_url": "https://github.com/KeyAuth/KeyAuth-Source-Code", "clone_url": "https://github.com/KeyAuth/KeyAuth-Source-Code.git", "stars": 266, "issues": 0, "language": "PHP", "description": "KeyAuth is an open source authentication system with cloud-hosted solutions available as well.", "risk_score": 32, "health_score": 67, "security_score": 15, "development_score": 20, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 34.65, "risk_factors": "Critical features (+4); Vulnerability keywords (+3); Optimal star range (+10)", "health_factors": "Recently active (last 30 days); No open issues; No releases", "security_factors": "No security policy found; No branch protection; Has CI/CD workflows", "development_factors": "No releases found; No testing infrastructure detected; Comprehensive README", "last_updated": "2025-07-04T16:28:09Z", "created_at": "2021-06-23T21:51:36Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "PHPVibe/PHPVibe-Video-Sharing-CMS", "repo_url": "https://github.com/PHPVibe/PHPVibe-Video-Sharing-CMS", "clone_url": "https://github.com/PHPVibe/PHPVibe-Video-Sharing-CMS.git", "stars": 82, "issues": 17, "language": "PHP", "description": "PHPVibe - Video Sharing CMS", "risk_score": 23, "health_score": 55, "security_score": 25, "development_score": 55, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 34.5, "risk_factors": "High-risk application type (+8); High-risk language: php (+10); Vulnerable age: 2.6y (+5)", "health_factors": "Moderately active (last 90 days); Moderate open issues; Recent release", "security_factors": "No security policy found; Security info in README; No branch protection", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-05-20T11:47:48Z", "created_at": "2023-01-05T13:02:23Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "ashraf-kabir/personal-blog", "repo_url": "https://github.com/ashraf-kabir/personal-blog", "clone_url": "https://github.com/ashraf-kabir/personal-blog.git", "stars": 63, "issues": 1, "language": "PHP", "description": "Dynamic blogsite using PHP, JS & mysql where user can register, log in, post blog with images, comment, edit profile, change password, etc. There is a separate admin panel also where admin can approve user & theirs posts & comments and manipulate the whole site.", "risk_score": 36, "health_score": 65, "security_score": 5, "development_score": 15, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 34.0, "risk_factors": "High-risk application type (+8); Critical features (+4); Vulnerability keywords (+9)", "health_factors": "Recently active (last 30 days); Few open issues; No releases", "security_factors": "No security policy found; No branch protection; No CI/CD workflows", "development_factors": "No releases found; No testing infrastructure detected; Basic README", "last_updated": "2025-07-05T06:22:15Z", "created_at": "2019-08-26T17:23:48Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "delight-im/PHP-Auth", "repo_url": "https://github.com/delight-im/PHP-Auth", "clone_url": "https://github.com/delight-im/PHP-Auth.git", "stars": 1177, "issues": 34, "language": "PHP", "description": "Authentication for PHP. Simple, lightweight and secure.", "risk_score": 27, "health_score": 52, "security_score": 35, "development_score": 30, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 33.65, "risk_factors": "Critical features (+4); Vulnerability keywords (+3); Optimal star range (+10)", "health_factors": "Moderately active (last 90 days); Moderate open issues; No releases", "security_factors": "No security policy found; Security info in README; No branch protection", "development_factors": "No releases found; No testing infrastructure detected; Comprehensive README", "last_updated": "2025-06-04T17:48:29Z", "created_at": "2015-10-20T12:26:04Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "FriendsOfREDAXO/cke5", "repo_url": "https://github.com/FriendsOfREDAXO/cke5", "clone_url": "https://github.com/FriendsOfREDAXO/cke5.git", "stars": 54, "issues": 6, "language": "PHP", "description": "Integrates the CKEditor5 into REDAXO CMS", "risk_score": 18, "health_score": 70, "security_score": 10, "development_score": 60, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 33.5, "risk_factors": "High-risk application type (+8); High-risk language: php (+10)", "health_factors": "Moderately active (last 90 days); Few open issues; Recent release", "security_factors": "No security policy found; No branch protection; Has CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-05-16T14:50:57Z", "created_at": "2018-05-04T18:10:51Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "modxcms/fred", "repo_url": "https://github.com/modxcms/fred", "clone_url": "https://github.com/modxcms/fred.git", "stars": 62, "issues": 42, "language": "PHP", "description": "The friendly front-end editor for visual, drag-and-drop content building in MODX CMS", "risk_score": 18, "health_score": 47, "security_score": 35, "development_score": 65, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 33.4, "risk_factors": "High-risk application type (+8); High-risk language: php (+10)", "health_factors": "Moderately active (last 90 days); Moderate open issues; Old releases", "security_factors": "No security policy found; No branch protection; Has CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-04-30T21:52:32Z", "created_at": "2018-01-09T17:58:59Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "azerothcore/acore-cms", "repo_url": "https://github.com/azerothcore/acore-cms", "clone_url": "https://github.com/azerothcore/acore-cms.git", "stars": 62, "issues": 21, "language": "PHP", "description": "ACore CMS based on Wordpress", "risk_score": 31, "health_score": 57, "security_score": 15, "development_score": 25, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 32.9, "risk_factors": "High-risk application type (+16); High-risk language: php (+10); Vulnerable age: 5.2y (+5)", "health_factors": "Recently active (last 30 days); Moderate open issues; No releases", "security_factors": "No security policy found; No branch protection; Has CI/CD workflows", "development_factors": "No releases found; No testing infrastructure detected; Comprehensive README", "last_updated": "2025-07-21T07:10:29Z", "created_at": "2020-05-30T10:24:05Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "simplygoodwork/craft-donkeytail", "repo_url": "https://github.com/simplygoodwork/craft-donkeytail", "clone_url": "https://github.com/simplygoodwork/craft-donkeytail.git", "stars": 52, "issues": 13, "language": "PHP", "description": "A Craft CMS fieldtype for content managing points on images.", "risk_score": 18, "health_score": 52, "security_score": 20, "development_score": 70, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 32.9, "risk_factors": "High-risk application type (+8); High-risk language: php (+10)", "health_factors": "Low activity (last year); Moderate open issues; Release within year", "security_factors": "No security policy found; No branch protection; No CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-04-16T14:50:56Z", "created_at": "2016-06-29T16:08:27Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "OFFLINE-GmbH/oc-bootstrapper", "repo_url": "https://github.com/OFFLINE-GmbH/oc-bootstrapper", "clone_url": "https://github.com/OFFLINE-GmbH/oc-bootstrapper.git", "stars": 81, "issues": 7, "language": "PHP", "description": "Easily bootstrap a new October CMS project", "risk_score": 18, "health_score": 57, "security_score": 15, "development_score": 65, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 32.4, "risk_factors": "High-risk application type (+8); High-risk language: php (+10)", "health_factors": "Moderately active (last 90 days); Few open issues; Old releases", "security_factors": "No security policy found; No branch protection; Has CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-06-28T13:19:34Z", "created_at": "2016-02-24T15:17:35Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "lautaroangelico/WebEngine", "repo_url": "https://github.com/lautaroangelico/WebEngine", "clone_url": "https://github.com/lautaroangelico/WebEngine.git", "stars": 134, "issues": 2, "language": "PHP", "description": "WebEngine is an open-source, fast and secure CMS for private Mu Online game servers.", "risk_score": 28, "health_score": 50, "security_score": 0, "development_score": 50, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 31.5, "risk_factors": "High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10)", "health_factors": "Low activity (last year); Few open issues; Release within year", "security_factors": "No security policy found; No branch protection; No CI/CD workflows", "development_factors": "Follows semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-04-25T05:49:10Z", "created_at": "2017-05-14T11:34:27Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "justitems/midrub_cms", "repo_url": "https://github.com/justitems/midrub_cms", "clone_url": "https://github.com/justitems/midrub_cms.git", "stars": 56, "issues": 0, "language": "PHP", "description": "Midrub - the first CMS only for business", "risk_score": 23, "health_score": 55, "security_score": 15, "development_score": 45, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 31.5, "risk_factors": "High-risk application type (+8); High-risk language: php (+10); Vulnerable age: 5.5y (+5)", "health_factors": "Low activity (last year); No open issues; Old releases", "security_factors": "No security policy found; No branch protection; No CI/CD workflows", "development_factors": "Some semantic versioning; No testing infrastructure detected; Comprehensive README", "last_updated": "2025-04-22T10:35:36Z", "created_at": "2020-01-20T12:27:37Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "maxsite/cms", "repo_url": "https://github.com/maxsite/cms", "clone_url": "https://github.com/maxsite/cms.git", "stars": 146, "issues": 2, "language": "PHP", "description": "MaxSite CMS. Free CMS for your website. Smart alternative to WordPress", "risk_score": 36, "health_score": 52, "security_score": 0, "development_score": 20, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 31.4, "risk_factors": "High-risk application type (+16); Optimal star range (+10); High-risk language: php (+10)", "health_factors": "Recently active (last 30 days); Few open issues; No releases", "security_factors": "No security policy found; No branch protection; No CI/CD workflows", "development_factors": "No releases found; No testing infrastructure detected; Comprehensive README", "last_updated": "2025-07-15T07:21:00Z", "created_at": "2012-11-14T12:12:35Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "xpertbot/craft-wheelform", "repo_url": "https://github.com/xpertbot/craft-wheelform", "clone_url": "https://github.com/xpertbot/craft-wheelform.git", "stars": 66, "issues": 24, "language": "PHP", "description": "Craft CMS 4 Form with Database integration", "risk_score": 18, "health_score": 60, "security_score": 30, "development_score": 35, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 30.75, "risk_factors": "High-risk application type (+8); High-risk language: php (+10)", "health_factors": "Moderately active (last 90 days); Moderate open issues; No releases", "security_factors": "No security policy found; No branch protection; Has CI/CD workflows", "development_factors": "No releases found; No testing infrastructure detected; Comprehensive README", "last_updated": "2025-05-21T19:55:12Z", "created_at": "2018-02-04T22:16:50Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "corbado/passkeys-php-laravel", "repo_url": "https://github.com/corbado/passkeys-php-laravel", "clone_url": "https://github.com/corbado/passkeys-php-laravel.git", "stars": 81, "issues": 0, "language": "PHP", "description": "Add passkeys to your PHP laravel application using the Corbado passkey-first authentication solution.", "risk_score": 17, "health_score": 60, "security_score": 20, "development_score": 40, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 29.5, "risk_factors": "Critical features (+4); Vulnerability keywords (+3); High-risk language: php (+10)", "health_factors": "Moderately active (last 90 days); No open issues; No releases", "security_factors": "No security policy found; No branch protection; No CI/CD workflows", "development_factors": "No releases found; Has testing infrastructure; Comprehensive README", "last_updated": "2025-06-02T09:36:24Z", "created_at": "2024-12-30T09:42:35Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "plan2net/webp", "repo_url": "https://github.com/plan2net/webp", "clone_url": "https://github.com/plan2net/webp.git", "stars": 65, "issues": 13, "language": "PHP", "description": "Create a WebP copy for images (TYPO3 CMS)", "risk_score": 18, "health_score": 70, "security_score": 5, "development_score": 30, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 28.25, "risk_factors": "High-risk application type (+8); High-risk language: php (+10)", "health_factors": "Recently active (last 30 days); Moderate open issues; No releases", "security_factors": "No security policy found; No branch protection; No CI/CD workflows", "development_factors": "No releases found; No testing infrastructure detected; Comprehensive README", "last_updated": "2025-06-30T19:49:29Z", "created_at": "2018-03-13T19:45:34Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "TeamEasy/EasyCMS", "repo_url": "https://github.com/TeamEasy/EasyCMS", "clone_url": "https://github.com/TeamEasy/EasyCMS.git", "stars": 102, "issues": 0, "language": "PHP", "description": "cms by thinkphp3.1.3+dwz+bootstrap", "risk_score": 28, "health_score": 50, "security_score": 5, "development_score": 15, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 27.0, "risk_factors": "High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10)", "health_factors": "Moderately active (last 90 days); No open issues; No releases", "security_factors": "No security policy found; No branch protection; No CI/CD workflows", "development_factors": "No releases found; No testing infrastructure detected; Basic README", "last_updated": "2025-06-07T10:49:18Z", "created_at": "2014-04-14T00:13:44Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}, {"repo_name": "romadebrian/WEB-Sekolah", "repo_url": "https://github.com/romadebrian/WEB-Sekolah", "clone_url": "https://github.com/romadebrian/WEB-Sekolah.git", "stars": 65, "issues": 3, "language": "PHP", "description": "Source Code untuk Web sekolah (CMS)", "risk_score": 18, "health_score": 50, "security_score": 0, "development_score": 40, "semgrep_vulnerabilities": 0, "semgrep_severity_score": 0, "vulnerability_categories": "", "final_score": 25.0, "risk_factors": "High-risk application type (+8); High-risk language: php (+10)", "health_factors": "Moderately active (last 90 days); Few open issues; Old releases", "security_factors": "No security policy found; No branch protection; No CI/CD workflows", "development_factors": "Some semantic versioning; Has release notes; No testing infrastructure detected", "last_updated": "2025-06-03T23:22:18Z", "created_at": "2018-07-16T12:39:46Z", "semgrep_detailed_findings": [], "semgrep_raw_findings": []}], "all_repositories_summary": [{"repo_name": "lara-zeus/sky", "final_score": 64.9, "semgrep_vulnerabilities": 0}, {"repo_name": "uasoft-indonesia/badaso", "final_score": 63.5, "semgrep_vulnerabilities": 0}, {"repo_name": "moonshine-software/moonshine", "final_score": 61.25, "semgrep_vulnerabilities": 0}, {"repo_name": "stephenjude/filament-blog", "final_score": 61.25, "semgrep_vulnerabilities": 0}, {"repo_name": "WordPress/two-factor", "final_score": 60.0, "semgrep_vulnerabilities": 0}, {"repo_name": "saade/filament-laravel-log", "final_score": 59.5, "semgrep_vulnerabilities": 0}, {"repo_name": "JibayMcs/filament-tour", "final_score": 56.75, "semgrep_vulnerabilities": 0}, {"repo_name": "alperenersoy/filament-export", "final_score": 56.25, "semgrep_vulnerabilities": 0}, {"repo_name": "codeigniter4/shield", "final_score": 56.25, "semgrep_vulnerabilities": 0}, {"repo_name": "ClassicPress/ClassicPress", "final_score": 55.65, "semgrep_vulnerabilities": 0}, {"repo_name": "maize-tech/laravel-magic-login", "final_score": 54.400000000000006, "semgrep_vulnerabilities": 0}, {"repo_name": "qcod/laravel-imageup", "final_score": 54.15, "semgrep_vulnerabilities": 0}, {"repo_name": "simplesamlphp/simplesamlphp", "final_score": 54.0, "semgrep_vulnerabilities": 0}, {"repo_name": "scheb/2fa", "final_score": 53.9, "semgrep_vulnerabilities": 0}, {"repo_name": "stephen<PERSON>de/filament-debugger", "final_score": 53.5, "semgrep_vulnerabilities": 0}, {"repo_name": "renoki-co/php-k8s", "final_score": 53.5, "semgrep_vulnerabilities": 0}, {"repo_name": "spatie/livewire-filepond", "final_score": 53.4, "semgrep_vulnerabilities": 0}, {"repo_name": "e107inc/e107", "final_score": 53.0, "semgrep_vulnerabilities": 0}, {"repo_name": "silverstripe/silverstripe-elemental", "final_score": 52.75, "semgrep_vulnerabilities": 0}, {"repo_name": "rappasoft/laravel-authentication-log", "final_score": 52.65, "semgrep_vulnerabilities": 0}, {"repo_name": "Laragear/WebAuthn", "final_score": 51.900000000000006, "semgrep_vulnerabilities": 0}, {"repo_name": "usefulteam/jwt-auth", "final_score": 51.75, "semgrep_vulnerabilities": 0}, {"repo_name": "laravel/fortify", "final_score": 51.650000000000006, "semgrep_vulnerabilities": 0}, {"repo_name": "concretecms/concretecms", "final_score": 51.65, "semgrep_vulnerabilities": 0}, {"repo_name": "stephenjude/filament-two-factor-authentication", "final_score": 51.400000000000006, "semgrep_vulnerabilities": 0}, {"repo_name": "aymanalhattami/filament-context-menu", "final_score": 51.4, "semgrep_vulnerabilities": 0}, {"repo_name": "DirectoryTree/LdapRecord-Laravel", "final_score": 51.4, "semgrep_vulnerabilities": 0}, {"repo_name": "pantheon-systems/wp-saml-auth", "final_score": 51.25, "semgrep_vulnerabilities": 0}, {"repo_name": "spatie/laravel-one-time-passwords", "final_score": 50.650000000000006, "semgrep_vulnerabilities": 0}, {"repo_name": "coderello/laravel-passport-social-grant", "final_score": 49.9, "semgrep_vulnerabilities": 0}, {"repo_name": "TypiCMS/Base", "final_score": 48.65, "semgrep_vulnerabilities": 0}, {"repo_name": "cesargb/laravel-magiclink", "final_score": 48.65, "semgrep_vulnerabilities": 0}, {"repo_name": "redaxo/redaxo", "final_score": 48.5, "semgrep_vulnerabilities": 0}, {"repo_name": "10up/eight-day-week", "final_score": 48.25, "semgrep_vulnerabilities": 0}, {"repo_name": "andrewdwallo/filament-companies", "final_score": 47.75, "semgrep_vulnerabilities": 0}, {"repo_name": "haxtheweb/haxcms-php", "final_score": 47.4, "semgrep_vulnerabilities": 0}, {"repo_name": "concretecms-community-store/community_store", "final_score": 47.0, "semgrep_vulnerabilities": 0}, {"repo_name": "dustin10/VichUploaderBundle", "final_score": 46.65, "semgrep_vulnerabilities": 0}, {"repo_name": "oc-shopaholic/oc-shopaholic-plugin", "final_score": 46.4, "semgrep_vulnerabilities": 0}, {"repo_name": "s-cart/s-cart", "final_score": 46.25, "semgrep_vulnerabilities": 0}, {"repo_name": "solutionforest/filament-scaffold", "final_score": 45.650000000000006, "semgrep_vulnerabilities": 0}, {"repo_name": "danpros/htmly", "final_score": 45.400000000000006, "semgrep_vulnerabilities": 0}, {"repo_name": "WonderCMS/wondercms", "final_score": 45.25, "semgrep_vulnerabilities": 0}, {"repo_name": "nextcloud/cms_pico", "final_score": 44.75, "semgrep_vulnerabilities": 0}, {"repo_name": "DirectoryTree/<PERSON>ender", "final_score": 44.5, "semgrep_vulnerabilities": 0}, {"repo_name": "Really-Simple-Plugins/really-simple-ssl", "final_score": 44.5, "semgrep_vulnerabilities": 0}, {"repo_name": "zenphoto/zenphoto", "final_score": 44.4, "semgrep_vulnerabilities": 0}, {"repo_name": "enhavo/enhavo", "final_score": 44.4, "semgrep_vulnerabilities": 0}, {"repo_name": "codepress/admin-columns", "final_score": 44.25, "semgrep_vulnerabilities": 0}, {"repo_name": "webreinvent/vaahcms", "final_score": 44.25, "semgrep_vulnerabilities": 0}, {"repo_name": "FriendsOfTYPO3/content-blocks", "final_score": 44.25, "semgrep_vulnerabilities": 0}, {"repo_name": "spicywebau/craft-neo", "final_score": 44.15, "semgrep_vulnerabilities": 0}, {"repo_name": "craftcms/commerce", "final_score": 44.15, "semgrep_vulnerabilities": 0}, {"repo_name": "Flute-CMS/cms", "final_score": 43.65, "semgrep_vulnerabilities": 0}, {"repo_name": "getformwork/formwork", "final_score": 43.65, "semgrep_vulnerabilities": 0}, {"repo_name": "Ecodev/graphql-upload", "final_score": 43.15, "semgrep_vulnerabilities": 0}, {"repo_name": "nystudio107/craft-seomatic", "final_score": 43.0, "semgrep_vulnerabilities": 0}, {"repo_name": "nette/security", "final_score": 42.9, "semgrep_vulnerabilities": 0}, {"repo_name": "a<PERSON><PERSON>/kirby-vite", "final_score": 42.75, "semgrep_vulnerabilities": 0}, {"repo_name": "mattiverse/Laravel-Userstamps", "final_score": 42.75, "semgrep_vulnerabilities": 0}, {"repo_name": "oveleon/contao-cookiebar", "final_score": 42.400000000000006, "semgrep_vulnerabilities": 0}, {"repo_name": "et-nik/gameap", "final_score": 42.4, "semgrep_vulnerabilities": 0}, {"repo_name": "spicywebau/craft-embedded-assets", "final_score": 42.4, "semgrep_vulnerabilities": 0}, {"repo_name": "nextcloud/user_saml", "final_score": 42.4, "semgrep_vulnerabilities": 0}, {"repo_name": "mostafaznv/larupload", "final_score": 41.75, "semgrep_vulnerabilities": 0}, {"repo_name": "rainlab/builder-plugin", "final_score": 41.75, "semgrep_vulnerabilities": 0}, {"repo_name": "putyourlightson/craft-blitz", "final_score": 41.4, "semgrep_vulnerabilities": 0}, {"repo_name": "Cotonti/Cotonti", "final_score": 41.15, "semgrep_vulnerabilities": 0}, {"repo_name": "nukeviet/nukeviet", "final_score": 40.75, "semgrep_vulnerabilities": 0}, {"repo_name": "liberu-cms/cms-laravel", "final_score": 40.5, "semgrep_vulnerabilities": 0}, {"repo_name": "ollieread/multiauth", "final_score": 40.4, "semgrep_vulnerabilities": 0}, {"repo_name": "KABBOUCHI/nova-impersonate", "final_score": 40.25, "semgrep_vulnerabilities": 0}, {"repo_name": "FusionGen/FusionGEN", "final_score": 40.15, "semgrep_vulnerabilities": 0}, {"repo_name": "PHPAuth/PHPAuth", "final_score": 40.0, "semgrep_vulnerabilities": 0}, {"repo_name": "abdessa<PERSON>bettal/lara4", "final_score": 39.5, "semgrep_vulnerabilities": 0}, {"repo_name": "fsi-open/admin-bundle", "final_score": 39.4, "semgrep_vulnerabilities": 0}, {"repo_name": "tomatophp/filament-cms", "final_score": 38.75, "semgrep_vulnerabilities": 0}, {"repo_name": "pluxml/PluXml", "final_score": 38.65, "semgrep_vulnerabilities": 0}, {"repo_name": "putyourlightson/craft-campaign", "final_score": 38.400000000000006, "semgrep_vulnerabilities": 0}, {"repo_name": "craftcms/aws-s3", "final_score": 38.4, "semgrep_vulnerabilities": 0}, {"repo_name": "kleeja-official/kleeja", "final_score": 38.25, "semgrep_vulnerabilities": 0}, {"repo_name": "processwire/processwire", "final_score": 38.0, "semgrep_vulnerabilities": 0}, {"repo_name": "aimeos/ai-cms-grapesjs", "final_score": 36.65, "semgrep_vulnerabilities": 0}, {"repo_name": "KeyAuth/KeyAuth-Source-Code", "final_score": 34.65, "semgrep_vulnerabilities": 0}, {"repo_name": "PHPVibe/PHPVibe-Video-Sharing-CMS", "final_score": 34.5, "semgrep_vulnerabilities": 0}, {"repo_name": "ashraf-kabir/personal-blog", "final_score": 34.0, "semgrep_vulnerabilities": 0}, {"repo_name": "delight-im/PHP-Auth", "final_score": 33.65, "semgrep_vulnerabilities": 0}, {"repo_name": "FriendsOfREDAXO/cke5", "final_score": 33.5, "semgrep_vulnerabilities": 0}, {"repo_name": "modxcms/fred", "final_score": 33.4, "semgrep_vulnerabilities": 0}, {"repo_name": "azerothcore/acore-cms", "final_score": 32.9, "semgrep_vulnerabilities": 0}, {"repo_name": "simplygoodwork/craft-donkeytail", "final_score": 32.9, "semgrep_vulnerabilities": 0}, {"repo_name": "OFFLINE-GmbH/oc-bootstrapper", "final_score": 32.4, "semgrep_vulnerabilities": 0}, {"repo_name": "lautaroangelico/WebEngine", "final_score": 31.5, "semgrep_vulnerabilities": 0}, {"repo_name": "justitems/midrub_cms", "final_score": 31.5, "semgrep_vulnerabilities": 0}, {"repo_name": "maxsite/cms", "final_score": 31.4, "semgrep_vulnerabilities": 0}, {"repo_name": "xpertbot/craft-wheelform", "final_score": 30.75, "semgrep_vulnerabilities": 0}, {"repo_name": "corbado/passkeys-php-laravel", "final_score": 29.5, "semgrep_vulnerabilities": 0}, {"repo_name": "plan2net/webp", "final_score": 28.25, "semgrep_vulnerabilities": 0}, {"repo_name": "TeamEasy/EasyCMS", "final_score": 27.0, "semgrep_vulnerabilities": 0}, {"repo_name": "romadebrian/WEB-Sekolah", "final_score": 25.0, "semgrep_vulnerabilities": 0}]}