{"version": "1.130.0", "results": [], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/laravel_fortify/.github/workflows/tests.yml", "start": {"line": 45, "col": 105, "offset": 890}, "end": {"line": 45, "col": 121, "offset": 906}}]], "message": "Syntax error at line downloaded_repos/laravel_fortify/.github/workflows/tests.yml:45:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ matrix.laravel` was unexpected", "path": "downloaded_repos/laravel_fortify/.github/workflows/tests.yml", "spans": [{"file": "downloaded_repos/laravel_fortify/.github/workflows/tests.yml", "start": {"line": 45, "col": 105, "offset": 890}, "end": {"line": 45, "col": 121, "offset": 906}}]}], "paths": {"scanned": ["downloaded_repos/laravel_fortify/.editorconfig", "downloaded_repos/laravel_fortify/.gitattributes", "downloaded_repos/laravel_fortify/.github/CODE_OF_CONDUCT.md", "downloaded_repos/laravel_fortify/.github/CONTRIBUTING.md", "downloaded_repos/laravel_fortify/.github/ISSUE_TEMPLATE/1_Bug_report.yml", "downloaded_repos/laravel_fortify/.github/ISSUE_TEMPLATE/config.yml", "downloaded_repos/laravel_fortify/.github/PULL_REQUEST_TEMPLATE.md", "downloaded_repos/laravel_fortify/.github/SECURITY.md", "downloaded_repos/laravel_fortify/.github/SUPPORT.md", "downloaded_repos/laravel_fortify/.github/workflows/issues.yml", "downloaded_repos/laravel_fortify/.github/workflows/pull-requests.yml", "downloaded_repos/laravel_fortify/.github/workflows/static-analysis.yml", "downloaded_repos/laravel_fortify/.github/workflows/tests.yml", "downloaded_repos/laravel_fortify/.github/workflows/update-changelog.yml", "downloaded_repos/laravel_fortify/.gitignore", "downloaded_repos/laravel_fortify/.styleci.yml", "downloaded_repos/laravel_fortify/CHANGELOG.md", "downloaded_repos/laravel_fortify/LICENSE.md", "downloaded_repos/laravel_fortify/README.md", "downloaded_repos/laravel_fortify/UPGRADE.md", "downloaded_repos/laravel_fortify/art/logo.svg", "downloaded_repos/laravel_fortify/art/socialcard.png", "downloaded_repos/laravel_fortify/composer.json", "downloaded_repos/laravel_fortify/config/fortify.php", "downloaded_repos/laravel_fortify/database/migrations/2014_10_12_200000_add_two_factor_columns_to_users_table.php", "downloaded_repos/laravel_fortify/phpstan.neon.dist", "downloaded_repos/laravel_fortify/phpunit.xml.dist", "downloaded_repos/laravel_fortify/routes/routes.php", "downloaded_repos/laravel_fortify/src/Actions/AttemptToAuthenticate.php", "downloaded_repos/laravel_fortify/src/Actions/CanonicalizeUsername.php", "downloaded_repos/laravel_fortify/src/Actions/CompletePasswordReset.php", "downloaded_repos/laravel_fortify/src/Actions/ConfirmPassword.php", "downloaded_repos/laravel_fortify/src/Actions/ConfirmTwoFactorAuthentication.php", "downloaded_repos/laravel_fortify/src/Actions/DisableTwoFactorAuthentication.php", "downloaded_repos/laravel_fortify/src/Actions/EnableTwoFactorAuthentication.php", "downloaded_repos/laravel_fortify/src/Actions/EnsureLoginIsNotThrottled.php", "downloaded_repos/laravel_fortify/src/Actions/GenerateNewRecoveryCodes.php", "downloaded_repos/laravel_fortify/src/Actions/PrepareAuthenticatedSession.php", "downloaded_repos/laravel_fortify/src/Actions/RedirectIfTwoFactorAuthenticatable.php", "downloaded_repos/laravel_fortify/src/Console/InstallCommand.php", "downloaded_repos/laravel_fortify/src/Contracts/ConfirmPasswordViewResponse.php", "downloaded_repos/laravel_fortify/src/Contracts/CreatesNewUsers.php", "downloaded_repos/laravel_fortify/src/Contracts/EmailVerificationNotificationSentResponse.php", "downloaded_repos/laravel_fortify/src/Contracts/FailedPasswordConfirmationResponse.php", "downloaded_repos/laravel_fortify/src/Contracts/FailedPasswordResetLinkRequestResponse.php", "downloaded_repos/laravel_fortify/src/Contracts/FailedPasswordResetResponse.php", "downloaded_repos/laravel_fortify/src/Contracts/FailedTwoFactorLoginResponse.php", "downloaded_repos/laravel_fortify/src/Contracts/LockoutResponse.php", "downloaded_repos/laravel_fortify/src/Contracts/LoginResponse.php", "downloaded_repos/laravel_fortify/src/Contracts/LoginViewResponse.php", "downloaded_repos/laravel_fortify/src/Contracts/LogoutResponse.php", "downloaded_repos/laravel_fortify/src/Contracts/PasswordConfirmedResponse.php", "downloaded_repos/laravel_fortify/src/Contracts/PasswordResetResponse.php", "downloaded_repos/laravel_fortify/src/Contracts/PasswordUpdateResponse.php", "downloaded_repos/laravel_fortify/src/Contracts/ProfileInformationUpdatedResponse.php", "downloaded_repos/laravel_fortify/src/Contracts/RecoveryCodesGeneratedResponse.php", "downloaded_repos/laravel_fortify/src/Contracts/RedirectsIfTwoFactorAuthenticatable.php", "downloaded_repos/laravel_fortify/src/Contracts/RegisterResponse.php", "downloaded_repos/laravel_fortify/src/Contracts/RegisterViewResponse.php", "downloaded_repos/laravel_fortify/src/Contracts/RequestPasswordResetLinkViewResponse.php", "downloaded_repos/laravel_fortify/src/Contracts/ResetPasswordViewResponse.php", "downloaded_repos/laravel_fortify/src/Contracts/ResetsUserPasswords.php", "downloaded_repos/laravel_fortify/src/Contracts/SuccessfulPasswordResetLinkRequestResponse.php", "downloaded_repos/laravel_fortify/src/Contracts/TwoFactorAuthenticationProvider.php", "downloaded_repos/laravel_fortify/src/Contracts/TwoFactorChallengeViewResponse.php", "downloaded_repos/laravel_fortify/src/Contracts/TwoFactorConfirmedResponse.php", "downloaded_repos/laravel_fortify/src/Contracts/TwoFactorDisabledResponse.php", "downloaded_repos/laravel_fortify/src/Contracts/TwoFactorEnabledResponse.php", "downloaded_repos/laravel_fortify/src/Contracts/TwoFactorLoginResponse.php", "downloaded_repos/laravel_fortify/src/Contracts/UpdatesUserPasswords.php", "downloaded_repos/laravel_fortify/src/Contracts/UpdatesUserProfileInformation.php", "downloaded_repos/laravel_fortify/src/Contracts/VerifyEmailResponse.php", "downloaded_repos/laravel_fortify/src/Contracts/VerifyEmailViewResponse.php", "downloaded_repos/laravel_fortify/src/Events/PasswordUpdatedViaController.php", "downloaded_repos/laravel_fortify/src/Events/RecoveryCodeReplaced.php", "downloaded_repos/laravel_fortify/src/Events/RecoveryCodesGenerated.php", "downloaded_repos/laravel_fortify/src/Events/TwoFactorAuthenticationChallenged.php", "downloaded_repos/laravel_fortify/src/Events/TwoFactorAuthenticationConfirmed.php", "downloaded_repos/laravel_fortify/src/Events/TwoFactorAuthenticationDisabled.php", "downloaded_repos/laravel_fortify/src/Events/TwoFactorAuthenticationEnabled.php", "downloaded_repos/laravel_fortify/src/Events/TwoFactorAuthenticationEvent.php", "downloaded_repos/laravel_fortify/src/Events/TwoFactorAuthenticationFailed.php", "downloaded_repos/laravel_fortify/src/Events/ValidTwoFactorAuthenticationCodeProvided.php", "downloaded_repos/laravel_fortify/src/Features.php", "downloaded_repos/laravel_fortify/src/Fortify.php", "downloaded_repos/laravel_fortify/src/FortifyServiceProvider.php", "downloaded_repos/laravel_fortify/src/Http/Controllers/AuthenticatedSessionController.php", "downloaded_repos/laravel_fortify/src/Http/Controllers/ConfirmablePasswordController.php", "downloaded_repos/laravel_fortify/src/Http/Controllers/ConfirmedPasswordStatusController.php", "downloaded_repos/laravel_fortify/src/Http/Controllers/ConfirmedTwoFactorAuthenticationController.php", "downloaded_repos/laravel_fortify/src/Http/Controllers/EmailVerificationNotificationController.php", "downloaded_repos/laravel_fortify/src/Http/Controllers/EmailVerificationPromptController.php", "downloaded_repos/laravel_fortify/src/Http/Controllers/NewPasswordController.php", "downloaded_repos/laravel_fortify/src/Http/Controllers/PasswordController.php", "downloaded_repos/laravel_fortify/src/Http/Controllers/PasswordResetLinkController.php", "downloaded_repos/laravel_fortify/src/Http/Controllers/ProfileInformationController.php", "downloaded_repos/laravel_fortify/src/Http/Controllers/RecoveryCodeController.php", "downloaded_repos/laravel_fortify/src/Http/Controllers/RegisteredUserController.php", "downloaded_repos/laravel_fortify/src/Http/Controllers/TwoFactorAuthenticatedSessionController.php", "downloaded_repos/laravel_fortify/src/Http/Controllers/TwoFactorAuthenticationController.php", "downloaded_repos/laravel_fortify/src/Http/Controllers/TwoFactorQrCodeController.php", "downloaded_repos/laravel_fortify/src/Http/Controllers/TwoFactorSecretKeyController.php", "downloaded_repos/laravel_fortify/src/Http/Controllers/VerifyEmailController.php", "downloaded_repos/laravel_fortify/src/Http/Requests/LoginRequest.php", "downloaded_repos/laravel_fortify/src/Http/Requests/TwoFactorLoginRequest.php", "downloaded_repos/laravel_fortify/src/Http/Requests/VerifyEmailRequest.php", "downloaded_repos/laravel_fortify/src/Http/Responses/EmailVerificationNotificationSentResponse.php", "downloaded_repos/laravel_fortify/src/Http/Responses/FailedPasswordConfirmationResponse.php", "downloaded_repos/laravel_fortify/src/Http/Responses/FailedPasswordResetLinkRequestResponse.php", "downloaded_repos/laravel_fortify/src/Http/Responses/FailedPasswordResetResponse.php", "downloaded_repos/laravel_fortify/src/Http/Responses/FailedTwoFactorLoginResponse.php", "downloaded_repos/laravel_fortify/src/Http/Responses/LockoutResponse.php", "downloaded_repos/laravel_fortify/src/Http/Responses/LoginResponse.php", "downloaded_repos/laravel_fortify/src/Http/Responses/LogoutResponse.php", "downloaded_repos/laravel_fortify/src/Http/Responses/PasswordConfirmedResponse.php", "downloaded_repos/laravel_fortify/src/Http/Responses/PasswordResetResponse.php", "downloaded_repos/laravel_fortify/src/Http/Responses/PasswordUpdateResponse.php", "downloaded_repos/laravel_fortify/src/Http/Responses/ProfileInformationUpdatedResponse.php", "downloaded_repos/laravel_fortify/src/Http/Responses/RecoveryCodesGeneratedResponse.php", "downloaded_repos/laravel_fortify/src/Http/Responses/RedirectAsIntended.php", "downloaded_repos/laravel_fortify/src/Http/Responses/RegisterResponse.php", "downloaded_repos/laravel_fortify/src/Http/Responses/SimpleViewResponse.php", "downloaded_repos/laravel_fortify/src/Http/Responses/SuccessfulPasswordResetLinkRequestResponse.php", "downloaded_repos/laravel_fortify/src/Http/Responses/TwoFactorConfirmedResponse.php", "downloaded_repos/laravel_fortify/src/Http/Responses/TwoFactorDisabledResponse.php", "downloaded_repos/laravel_fortify/src/Http/Responses/TwoFactorEnabledResponse.php", "downloaded_repos/laravel_fortify/src/Http/Responses/TwoFactorLoginResponse.php", "downloaded_repos/laravel_fortify/src/Http/Responses/VerifyEmailResponse.php", "downloaded_repos/laravel_fortify/src/LoginRateLimiter.php", "downloaded_repos/laravel_fortify/src/RecoveryCode.php", "downloaded_repos/laravel_fortify/src/RoutePath.php", "downloaded_repos/laravel_fortify/src/Rules/Password.php", "downloaded_repos/laravel_fortify/src/TwoFactorAuthenticatable.php", "downloaded_repos/laravel_fortify/src/TwoFactorAuthenticationProvider.php", "downloaded_repos/laravel_fortify/stubs/CreateNewUser.php", "downloaded_repos/laravel_fortify/stubs/FortifyServiceProvider.php", "downloaded_repos/laravel_fortify/stubs/PasswordValidationRules.php", "downloaded_repos/laravel_fortify/stubs/ResetUserPassword.php", "downloaded_repos/laravel_fortify/stubs/UpdateUserPassword.php", "downloaded_repos/laravel_fortify/stubs/UpdateUserProfileInformation.php", "downloaded_repos/laravel_fortify/stubs/fortify.php", "downloaded_repos/laravel_fortify/testbench.yaml", "downloaded_repos/laravel_fortify/workbench/app/Models/User.php", "downloaded_repos/laravel_fortify/workbench/database/factories/UserFactory.php"], "skipped": [{"path": "downloaded_repos/laravel_fortify/.github/workflows/tests.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/laravel_fortify/tests/AuthenticatedSessionControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_fortify/tests/AuthenticatedSessionControllerWithTwoFactorTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_fortify/tests/ConfirmablePasswordControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_fortify/tests/EmailVerificationNotificationControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_fortify/tests/EmailVerificationPromptControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_fortify/tests/FortifyServiceProviderTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_fortify/tests/Models/UserWithTwoFactor.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_fortify/tests/NewPasswordControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_fortify/tests/OrchestraTestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_fortify/tests/PasswordControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_fortify/tests/PasswordResetLinkRequestControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_fortify/tests/PasswordRuleTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_fortify/tests/ProfileInformationControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_fortify/tests/RecoveryCodeControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_fortify/tests/RegisteredUserControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_fortify/tests/TwoFactorAuthenticationControllerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/laravel_fortify/tests/VerifyEmailControllerTest.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.6321680545806885, "profiling_times": {"config_time": 5.910632610321045, "core_time": 2.708923101425171, "ignores_time": 0.0017032623291015625, "total_time": 8.622815370559692}, "parsing_time": {"total_time": 0.5041337013244629, "per_file_time": {"mean": 0.0039385445415973655, "std_dev": 6.916163181168446e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 1.36576509475708, "per_file_time": {"mean": 0.003283089170089136, "std_dev": 8.846023156605777e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.23455262184143066, "per_file_and_rule_time": {"mean": 0.0005379647289941074, "std_dev": 3.8083558791989612e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.027761220932006836, "per_def_and_rule_time": {"mean": 0.0007503032684326172, "std_dev": 3.4509373317386897e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1090991936}, "engine_requested": "OSS", "skipped_rules": []}