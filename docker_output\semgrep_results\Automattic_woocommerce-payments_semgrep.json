{"version": "1.130.0", "results": [{"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/Automattic_woocommerce-payments/bin/combine-pot-files.php", "start": {"line": 163, "col": 1, "offset": 4861}, "end": {"line": 163, "col": 19, "offset": 4879}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "php.lang.security.unlink-use.unlink-use", "path": "downloaded_repos/Automattic_woocommerce-payments/bin/combine-pot-files.php", "start": {"line": 164, "col": 1, "offset": 4881}, "end": {"line": 164, "col": 19, "offset": 4899}, "extra": {"message": "Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to.", "metadata": {"references": ["https://www.php.net/manual/en/function.unlink", "https://owasp.org/www-project-top-ten/2017/A5_2017-Broken_Access_Control.html"], "category": "security", "technology": ["php"], "owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/php.lang.security.unlink-use.unlink-use", "shortlink": "https://sg.run/rYeR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "bash.lang.security.ifs-tampering.ifs-tampering", "path": "downloaded_repos/Automattic_woocommerce-payments/bin/run-ci-tests-check-coverage.bash", "start": {"line": 5, "col": 1, "offset": 58}, "end": {"line": 5, "col": 12, "offset": 69}, "extra": {"message": "The special variable IFS affects how splitting takes place when expanding unquoted variables. Don't set it globally. Prefer a dedicated utility such as 'cut' or 'awk' if you need to split input data. If you must use 'read', set IFS locally using e.g. 'IFS=\",\" read -a my_array'.", "metadata": {"cwe": ["CWE-20: Improper Input Validation"], "category": "security", "technology": ["bash"], "confidence": "LOW", "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/bash.lang.security.ifs-tampering.ifs-tampering", "shortlink": "https://sg.run/Q9pq"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "bash.lang.security.ifs-tampering.ifs-tampering", "path": "downloaded_repos/Automattic_woocommerce-payments/bin/run-ci-tests.bash", "start": {"line": 5, "col": 1, "offset": 58}, "end": {"line": 5, "col": 12, "offset": 69}, "extra": {"message": "The special variable IFS affects how splitting takes place when expanding unquoted variables. Don't set it globally. Prefer a dedicated utility such as 'cut' or 'awk' if you need to split input data. If you must use 'read', set IFS locally using e.g. 'IFS=\",\" read -a my_array'.", "metadata": {"cwe": ["CWE-20: Improper Input Validation"], "category": "security", "technology": ["bash"], "confidence": "LOW", "owasp": ["A03:2021 - Injection"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/bash.lang.security.ifs-tampering.ifs-tampering", "shortlink": "https://sg.run/Q9pq"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "typescript.react.security.audit.react-dangerouslysetinnerhtml.react-dangerouslysetinnerhtml", "path": "downloaded_repos/Automattic_woocommerce-payments/client/checkout/blocks/payment-processor.js", "start": {"line": 283, "col": 15, "offset": 7324}, "end": {"line": 283, "col": 34, "offset": 7343}, "extra": {"message": "Detection of dangerouslySetInnerHTML from non-constant definition. This can inadvertently expose users to cross-site scripting (XSS) attacks if this comes from user-provided input. If you have to use dangerouslySetInnerHTML, consider using a sanitization library such as DOMPurify to sanitize your HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://react.dev/reference/react-dom/components/common#dangerously-setting-the-inner-html"], "category": "security", "confidence": "MEDIUM", "technology": ["react"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/typescript.react.security.audit.react-dangerouslysetinnerhtml.react-dangerouslysetinnerhtml", "shortlink": "https://sg.run/rAx6"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/Automattic_woocommerce-payments/client/checkout/classic/payment-processing.js", "start": {"line": 485, "col": 3, "offset": 15443}, "end": {"line": 485, "col": 46, "offset": 15486}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insufficient-postmessage-origin-validation.insufficient-postmessage-origin-validation", "path": "downloaded_repos/Automattic_woocommerce-payments/client/checkout/woopay/email-input-iframe.js", "start": {"line": 459, "col": 2, "offset": 12836}, "end": {"line": 544, "col": 5, "offset": 15164}, "extra": {"message": "No validation of origin is done by the addEventListener API. It may be possible to exploit this flaw to perform Cross Origin attacks such as Cross-Site Scripting(XSS).", "metadata": {"owasp": ["A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-345: Insufficient Verification of Data Authenticity"], "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/javascript.browser.security.insufficient-postmessage-origin-validation.insufficient-postmessage-origin-validation", "shortlink": "https://sg.run/gL9x"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/Automattic_woocommerce-payments/client/checkout/woopay/express-button/utils.js", "start": {"line": 42, "col": 6, "offset": 1404}, "end": {"line": 42, "col": 47, "offset": 1445}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.open-redirect.js-open-redirect", "path": "downloaded_repos/Automattic_woocommerce-payments/client/connect-account-page/index.tsx", "start": {"line": 214, "col": 28, "offset": 7373}, "end": {"line": 217, "col": 8, "offset": 7451}, "extra": {"message": "The application accepts potentially user-controlled input `$PROP` which can control the location of the current window context. This can lead two types of vulnerabilities open-redirection and Cross-Site-Scripting (XSS) with JavaScript URIs. It is recommended to validate user-controllable input before allowing it to control the redirection.", "metadata": {"interfile": true, "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "owasp": ["A01:2021 - Broken Access Control"], "asvs": {"control_id": "5.5.1 Insecue Redirect", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v51-input-validation", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "confidence": "HIGH", "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "technology": ["browser"], "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/javascript.browser.security.open-redirect.js-open-redirect", "shortlink": "https://sg.run/3xRe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.open-redirect.js-open-redirect", "path": "downloaded_repos/Automattic_woocommerce-payments/client/connect-account-page/index.tsx", "start": {"line": 315, "col": 27, "offset": 10699}, "end": {"line": 317, "col": 7, "offset": 10846}, "extra": {"message": "The application accepts potentially user-controlled input `$PROP` which can control the location of the current window context. This can lead two types of vulnerabilities open-redirection and Cross-Site-Scripting (XSS) with JavaScript URIs. It is recommended to validate user-controllable input before allowing it to control the redirection.", "metadata": {"interfile": true, "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "owasp": ["A01:2021 - Broken Access Control"], "asvs": {"control_id": "5.5.1 Insecue Redirect", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v51-input-validation", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "confidence": "HIGH", "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "technology": ["browser"], "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/javascript.browser.security.open-redirect.js-open-redirect", "shortlink": "https://sg.run/3xRe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/utils/index.ts", "start": {"line": 125, "col": 2, "offset": 2672}, "end": {"line": 125, "col": 32, "offset": 2702}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.open-redirect.js-open-redirect", "path": "downloaded_repos/Automattic_woocommerce-payments/client/onboarding/index.tsx", "start": {"line": 25, "col": 26, "offset": 738}, "end": {"line": 32, "col": 6, "offset": 931}, "extra": {"message": "The application accepts potentially user-controlled input `$PROP` which can control the location of the current window context. This can lead two types of vulnerabilities open-redirection and Cross-Site-Scripting (XSS) with JavaScript URIs. It is recommended to validate user-controllable input before allowing it to control the redirection.", "metadata": {"interfile": true, "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "owasp": ["A01:2021 - Broken Access Control"], "asvs": {"control_id": "5.5.1 Insecue Redirect", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v51-input-validation", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "confidence": "HIGH", "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "technology": ["browser"], "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/javascript.browser.security.open-redirect.js-open-redirect", "shortlink": "https://sg.run/3xRe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.open-redirect.js-open-redirect", "path": "downloaded_repos/Automattic_woocommerce-payments/client/onboarding/kyc/index.tsx", "start": {"line": 27, "col": 26, "offset": 818}, "end": {"line": 34, "col": 4, "offset": 962}, "extra": {"message": "The application accepts potentially user-controlled input `$PROP` which can control the location of the current window context. This can lead two types of vulnerabilities open-redirection and Cross-Site-Scripting (XSS) with JavaScript URIs. It is recommended to validate user-controllable input before allowing it to control the redirection.", "metadata": {"interfile": true, "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "owasp": ["A01:2021 - Broken Access Control"], "asvs": {"control_id": "5.5.1 Insecue Redirect", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v51-input-validation", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "confidence": "HIGH", "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "technology": ["browser"], "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/javascript.browser.security.open-redirect.js-open-redirect", "shortlink": "https://sg.run/3xRe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.open-redirect.js-open-redirect", "path": "downloaded_repos/Automattic_woocommerce-payments/client/onboarding/steps/embedded-kyc.tsx", "start": {"line": 45, "col": 28, "offset": 1465}, "end": {"line": 51, "col": 6, "offset": 1597}, "extra": {"message": "The application accepts potentially user-controlled input `$PROP` which can control the location of the current window context. This can lead two types of vulnerabilities open-redirection and Cross-Site-Scripting (XSS) with JavaScript URIs. It is recommended to validate user-controllable input before allowing it to control the redirection.", "metadata": {"interfile": true, "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "owasp": ["A01:2021 - Broken Access Control"], "asvs": {"control_id": "5.5.1 Insecue Redirect", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v51-input-validation", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "confidence": "HIGH", "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "technology": ["browser"], "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/javascript.browser.security.open-redirect.js-open-redirect", "shortlink": "https://sg.run/3xRe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.open-redirect.js-open-redirect", "path": "downloaded_repos/Automattic_woocommerce-payments/client/onboarding/steps/embedded-kyc.tsx", "start": {"line": 53, "col": 28, "offset": 1638}, "end": {"line": 59, "col": 6, "offset": 1767}, "extra": {"message": "The application accepts potentially user-controlled input `$PROP` which can control the location of the current window context. This can lead two types of vulnerabilities open-redirection and Cross-Site-Scripting (XSS) with JavaScript URIs. It is recommended to validate user-controllable input before allowing it to control the redirection.", "metadata": {"interfile": true, "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "owasp": ["A01:2021 - Broken Access Control"], "asvs": {"control_id": "5.5.1 Insecue Redirect", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v51-input-validation", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "confidence": "HIGH", "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "technology": ["browser"], "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/javascript.browser.security.open-redirect.js-open-redirect", "shortlink": "https://sg.run/3xRe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.open-redirect.js-open-redirect", "path": "downloaded_repos/Automattic_woocommerce-payments/client/onboarding/steps/embedded-kyc.tsx", "start": {"line": 62, "col": 27, "offset": 1822}, "end": {"line": 68, "col": 5, "offset": 1944}, "extra": {"message": "The application accepts potentially user-controlled input `$PROP` which can control the location of the current window context. This can lead two types of vulnerabilities open-redirection and Cross-Site-Scripting (XSS) with JavaScript URIs. It is recommended to validate user-controllable input before allowing it to control the redirection.", "metadata": {"interfile": true, "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "owasp": ["A01:2021 - Broken Access Control"], "asvs": {"control_id": "5.5.1 Insecue Redirect", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v51-input-validation", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "confidence": "HIGH", "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "technology": ["browser"], "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/javascript.browser.security.open-redirect.js-open-redirect", "shortlink": "https://sg.run/3xRe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.open-redirect.js-open-redirect", "path": "downloaded_repos/Automattic_woocommerce-payments/client/onboarding/steps/loading.tsx", "start": {"line": 29, "col": 26, "offset": 750}, "end": {"line": 35, "col": 6, "offset": 950}, "extra": {"message": "The application accepts potentially user-controlled input `$PROP` which can control the location of the current window context. This can lead two types of vulnerabilities open-redirection and Cross-Site-Scripting (XSS) with JavaScript URIs. It is recommended to validate user-controllable input before allowing it to control the redirection.", "metadata": {"interfile": true, "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "owasp": ["A01:2021 - Broken Access Control"], "asvs": {"control_id": "5.5.1 Insecue Redirect", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v51-input-validation", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "confidence": "HIGH", "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "technology": ["browser"], "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/javascript.browser.security.open-redirect.js-open-redirect", "shortlink": "https://sg.run/3xRe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.open-redirect.js-open-redirect", "path": "downloaded_repos/Automattic_woocommerce-payments/client/overview/modal/progressive-onboarding-eligibility/index.tsx", "start": {"line": 41, "col": 26, "offset": 1551}, "end": {"line": 45, "col": 6, "offset": 1685}, "extra": {"message": "The application accepts potentially user-controlled input `$PROP` which can control the location of the current window context. This can lead two types of vulnerabilities open-redirection and Cross-Site-Scripting (XSS) with JavaScript URIs. It is recommended to validate user-controllable input before allowing it to control the redirection.", "metadata": {"interfile": true, "cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "owasp": ["A01:2021 - Broken Access Control"], "asvs": {"control_id": "5.5.1 Insecue Redirect", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v51-input-validation", "section": "V5 Validation, Sanitization and Encoding", "version": "4"}, "category": "security", "confidence": "HIGH", "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Unvalidated_Redirects_and_Forwards_Cheat_Sheet.html"], "technology": ["browser"], "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/javascript.browser.security.open-redirect.js-open-redirect", "shortlink": "https://sg.run/3xRe"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "typescript.react.security.audit.react-dangerouslysetinnerhtml.react-dangerouslysetinnerhtml", "path": "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/payment-method/base-payment-method-details/index.tsx", "start": {"line": 89, "col": 16, "offset": 2206}, "end": {"line": 89, "col": 41, "offset": 2231}, "extra": {"message": "Detection of dangerouslySetInnerHTML from non-constant definition. This can inadvertently expose users to cross-site scripting (XSS) attacks if this comes from user-provided input. If you have to use dangerouslySetInnerHTML, consider using a sanitization library such as DOMPurify to sanitize your HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://react.dev/reference/react-dom/components/common#dangerously-setting-the-inner-html"], "category": "security", "confidence": "MEDIUM", "technology": ["react"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/typescript.react.security.audit.react-dangerouslysetinnerhtml.react-dangerouslysetinnerhtml", "shortlink": "https://sg.run/rAx6"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "typescript.react.security.audit.react-dangerouslysetinnerhtml.react-dangerouslysetinnerhtml", "path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/express-checkout-settings/woopay-preview.js", "start": {"line": 108, "col": 13, "offset": 2526}, "end": {"line": 108, "col": 21, "offset": 2534}, "extra": {"message": "Detection of dangerouslySetInnerHTML from non-constant definition. This can inadvertently expose users to cross-site scripting (XSS) attacks if this comes from user-provided input. If you have to use dangerouslySetInnerHTML, consider using a sanitization library such as DOMPurify to sanitize your HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://react.dev/reference/react-dom/components/common#dangerously-setting-the-inner-html"], "category": "security", "confidence": "MEDIUM", "technology": ["react"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/typescript.react.security.audit.react-dangerouslysetinnerhtml.react-dangerouslysetinnerhtml", "shortlink": "https://sg.run/rAx6"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.highlight.js", "start": {"line": 123, "col": 11, "offset": 3667}, "end": {"line": 123, "col": 38, "offset": 3694}, "extra": {"message": "RegExp() called with a `words` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js", "start": {"line": 1430, "col": 7, "offset": 38294}, "end": {"line": 1437, "col": 49, "offset": 38512}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js", "start": {"line": 2004, "col": 22, "offset": 53838}, "end": {"line": 2012, "col": 9, "offset": 53984}, "extra": {"message": "RegExp() called with a `className` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js", "start": {"line": 2100, "col": 14, "offset": 56287}, "end": {"line": 2100, "col": 36, "offset": 56309}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js", "start": {"line": 2691, "col": 16, "offset": 71833}, "end": {"line": 2691, "col": 38, "offset": 71855}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js", "start": {"line": 2707, "col": 17, "offset": 72349}, "end": {"line": 2707, "col": 39, "offset": 72371}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js", "start": {"line": 2718, "col": 17, "offset": 72601}, "end": {"line": 2718, "col": 39, "offset": 72623}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js", "start": {"line": 2739, "col": 12, "offset": 73150}, "end": {"line": 2739, "col": 38, "offset": 73176}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js", "start": {"line": 3423, "col": 11, "offset": 91223}, "end": {"line": 3423, "col": 33, "offset": 91245}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js", "start": {"line": 3783, "col": 11, "offset": 100228}, "end": {"line": 3783, "col": 31, "offset": 100248}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js", "start": {"line": 5498, "col": 6, "offset": 144044}, "end": {"line": 5499, "col": 60, "offset": 144119}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js", "start": {"line": 6850, "col": 10, "offset": 177976}, "end": {"line": 6850, "col": 33, "offset": 177999}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_lunr.js", "start": {"line": 1825, "col": 4, "offset": 46843}, "end": {"line": 1825, "col": 29, "offset": 46868}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_lunr.js", "start": {"line": 1851, "col": 4, "offset": 47514}, "end": {"line": 1851, "col": 29, "offset": 47539}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "path": "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_lunr.js", "start": {"line": 1894, "col": 4, "offset": 48865}, "end": {"line": 1894, "col": 29, "offset": 48890}, "extra": {"message": "Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object.", "metadata": {"cwe": ["CWE-915: Improperly Controlled Modification of Dynamically-Determined Object Attributes"], "category": "security", "references": ["https://github.com/HoLyVieR/prototype-pollution-nsec18/blob/master/paper/JavaScript_prototype_pollution_attack_in_NodeJS.pdf"], "technology": ["typescript"], "owasp": ["A08:2021 - Software and Data Integrity Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mass Assignment"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop", "shortlink": "https://sg.run/w1DB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/setup/tasks/add-currencies-task/__tests__/index.test.js", "start": {"line": 233, "col": 13, "offset": 4510}, "end": {"line": 236, "col": 8, "offset": 4589}, "extra": {"message": "RegExp() called with a `currencyCode` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/setup/tasks/add-currencies-task/__tests__/index.test.js", "start": {"line": 242, "col": 13, "offset": 4696}, "end": {"line": 245, "col": 8, "offset": 4775}, "extra": {"message": "RegExp() called with a `currencyCode` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/setup/tasks/add-currencies-task/__tests__/index.test.js", "start": {"line": 332, "col": 13, "offset": 6879}, "end": {"line": 335, "col": 8, "offset": 6950}, "extra": {"message": "RegExp() called with a `code` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/setup/tasks/add-currencies-task/__tests__/index.test.js", "start": {"line": 355, "col": 13, "offset": 7393}, "end": {"line": 358, "col": 8, "offset": 7464}, "extra": {"message": "RegExp() called with a `code` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/setup/tasks/add-currencies-task/__tests__/index.test.js", "start": {"line": 442, "col": 13, "offset": 9410}, "end": {"line": 445, "col": 8, "offset": 9481}, "extra": {"message": "RegExp() called with a `code` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js:\n ", "path": "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js:\n ", "path": "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js"}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/Automattic_woocommerce-payments/.github/actions/create-tag/action.yml", "start": {"line": 45, "col": 25, "offset": 1101}, "end": {"line": 45, "col": 39, "offset": 1115}}, {"path": "downloaded_repos/Automattic_woocommerce-payments/.github/actions/create-tag/action.yml", "start": {"line": 46, "col": 26, "offset": 1101}, "end": {"line": 46, "col": 40, "offset": 1115}}]], "message": "Syntax error at line downloaded_repos/Automattic_woocommerce-payments/.github/actions/create-tag/action.yml:45:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ github.actor` was unexpected", "path": "downloaded_repos/Automattic_woocommerce-payments/.github/actions/create-tag/action.yml", "spans": [{"file": "downloaded_repos/Automattic_woocommerce-payments/.github/actions/create-tag/action.yml", "start": {"line": 45, "col": 25, "offset": 1101}, "end": {"line": 45, "col": 39, "offset": 1115}}, {"file": "downloaded_repos/Automattic_woocommerce-payments/.github/actions/create-tag/action.yml", "start": {"line": 46, "col": 26, "offset": 1101}, "end": {"line": 46, "col": 40, "offset": 1115}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/Automattic_woocommerce-payments/.github/actions/process-changelog/action.yml", "start": {"line": 52, "col": 13, "offset": 2232}, "end": {"line": 76, "col": 47, "offset": 3262}}]], "message": "Syntax error at line downloaded_repos/Automattic_woocommerce-payments/.github/actions/process-changelog/action.yml:52:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `if ${{ env.ACTION_TYPE == 'amend-version' }}; then\n  sed -i \"s/^= $CURRENT_VERSION - .* =$/= $RELEASE_VERSION - $RELEASE_DATE =/\" changelog.txt\nelse\n  # Install this dev package globally to gather changelog entries while not including it into the release package\n  composer global require automattic/jetpack-changelogger:^3.0.7\n\n  if ${{ env.ACTION_TYPE == 'generate' }}; then\n    CHANGELOG_FLAG=\"\"\n    echo \"Generating the changelog entries.\" >> $GITHUB_STEP_SUMMARY\n  else\n    CHANGELOG_FLAG=\"--amend\"\n    echo \"Amending the changelog entries.\" >> $GITHUB_STEP_SUMMARY\n  fi\n\n  ~/.composer/vendor/bin/changelogger write --use-version=\"$RELEASE_VERSION\" --release-date=\"$RELEASE_DATE\" $CHANGELOG_FLAG --no-interaction --yes\nfi\n\nCHANGELOG=$(awk '/^= / { if (p) { exit }; p=1; next } p && NF' changelog.txt)\n\n# Escape backslash, new line and ampersand characters. The order is important.\nCHANGELOG=${CHANGELOG//\\\\/\\\\\\\\}\nCHANGELOG=${CHANGELOG//$'\\n'/\\\\n} \nCHANGELOG=${CHANGELOG//&/\\\\&}\n\necho \"CHANGELOG=$CHANGELOG\" >> $GITHUB_OUTPUT\n` was unexpected", "path": "downloaded_repos/Automattic_woocommerce-payments/.github/actions/process-changelog/action.yml", "spans": [{"file": "downloaded_repos/Automattic_woocommerce-payments/.github/actions/process-changelog/action.yml", "start": {"line": 52, "col": 13, "offset": 2232}, "end": {"line": 76, "col": 47, "offset": 3262}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/Automattic_woocommerce-payments/.github/actions/process-changelog/action.yml", "start": {"line": 87, "col": 13, "offset": 3900}, "end": {"line": 95, "col": 4, "offset": 4325}}]], "message": "Syntax error at line downloaded_repos/Automattic_woocommerce-payments/.github/actions/process-changelog/action.yml:87:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `if ${{ env.ACTION_TYPE == 'amend-version' }}; then\n  sed -i \"s/^= $CURRENT_VERSION - .* =$/= $RELEASE_VERSION - $RELEASE_DATE =/\" readme.txt\nelse\n  if ${{ env.ACTION_TYPE == 'amend' }}; then\n    perl -i -p0e \"s/= $RELEASE_VERSION.*?(\\n){2}//s\" readme.txt # Delete the existing changelog for the release version first\n  fi\n\n  sed -ri \"s|(== Changelog ==)|\\1\\n\\n= $RELEASE_VERSION - $RELEASE_DATE =\\n$CHANGELOG|\" readme.txt\nfi\n` was unexpected", "path": "downloaded_repos/Automattic_woocommerce-payments/.github/actions/process-changelog/action.yml", "spans": [{"file": "downloaded_repos/Automattic_woocommerce-payments/.github/actions/process-changelog/action.yml", "start": {"line": 87, "col": 13, "offset": 3900}, "end": {"line": 95, "col": 4, "offset": 4325}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/Automattic_woocommerce-payments/.github/actions/version-check/action.yml", "start": {"line": 28, "col": 1, "offset": 787}, "end": {"line": 41, "col": 4, "offset": 1231}}]], "message": "Syntax error at line downloaded_repos/Automattic_woocommerce-payments/.github/actions/version-check/action.yml:28:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `TRIMMED_VERSION=$(echo \"$VERSION\" | xargs) \n\nif ${{ env.IS_PRERELEASE == 'true' }}; then\n  VERSION_FORMAT=\"^[0-9]+\\.[0-9]\\.[0-9]+-test-[1-9]$\"\nelse\n  VERSION_FORMAT=\"^[0-9]+\\.[0-9]\\.[0-9]+$\"\nfi\n\nif [[ $TRIMMED_VERSION =~ $VERSION_FORMAT ]]; then\n  echo \"VERSION=$TRIMMED_VERSION\" >> $GITHUB_OUTPUT\nelse\n  echo \"::error::The version provided doesn't respect the format expected (version: $TRIMMED_VERSION; format: $VERSION_FORMAT).\"\n  exit 1\nfi\n` was unexpected", "path": "downloaded_repos/Automattic_woocommerce-payments/.github/actions/version-check/action.yml", "spans": [{"file": "downloaded_repos/Automattic_woocommerce-payments/.github/actions/version-check/action.yml", "start": {"line": 28, "col": 1, "offset": 787}, "end": {"line": 41, "col": 4, "offset": 1231}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/i18n-weekly-release.yml", "start": {"line": 109, "col": 5, "offset": 1192}, "end": {"line": 109, "col": 8, "offset": 1195}}]], "message": "Syntax error at line downloaded_repos/Automattic_woocommerce-payments/.github/workflows/i18n-weekly-release.yml:109:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/i18n-weekly-release.yml", "spans": [{"file": "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/i18n-weekly-release.yml", "start": {"line": 109, "col": 5, "offset": 1192}, "end": {"line": 109, "col": 8, "offset": 1195}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/release-changelog.yml", "start": {"line": 58, "col": 15, "offset": 1779}, "end": {"line": 65, "col": 10, "offset": 2077}}]], "message": "Syntax error at line downloaded_repos/Automattic_woocommerce-payments/.github/workflows/release-changelog.yml:58:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `git config user.name \"botwoo\"\ngit config user.email \"<EMAIL>\"\nif ${{ env.CHANGELOG_ACTION == 'amend' }}; then\n  git commit -am \"Amend changelog entries for release $RELEASE_VERSION\"\nelse\n  git commit -am \"Generate changelog entries for release $RELEASE_VERSION\"\nfi\ngit push\n` was unexpected", "path": "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/release-changelog.yml", "spans": [{"file": "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/release-changelog.yml", "start": {"line": 58, "col": 15, "offset": 1779}, "end": {"line": 65, "col": 10, "offset": 2077}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/release-code-freeze.yml", "start": {"line": 60, "col": 41, "offset": 1857}, "end": {"line": 60, "col": 59, "offset": 1875}}, {"path": "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/release-code-freeze.yml", "start": {"line": 61, "col": 46, "offset": 1857}, "end": {"line": 61, "col": 64, "offset": 1875}}]], "message": "Syntax error at line downloaded_repos/Automattic_woocommerce-payments/.github/workflows/release-code-freeze.yml:60:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ env.NEXT_VERSION` was unexpected", "path": "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/release-code-freeze.yml", "spans": [{"file": "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/release-code-freeze.yml", "start": {"line": 60, "col": 41, "offset": 1857}, "end": {"line": 60, "col": 59, "offset": 1875}}, {"file": "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/release-code-freeze.yml", "start": {"line": 61, "col": 46, "offset": 1857}, "end": {"line": 61, "col": 64, "offset": 1875}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/Automattic_woocommerce-payments/client/deposits/declarations.d.ts", "start": {"line": 16, "col": 23, "offset": 0}, "end": {"line": 21, "col": 4, "offset": 80}}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/deposits/declarations.d.ts", "start": {"line": 29, "col": 6, "offset": 0}, "end": {"line": 29, "col": 20, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/Automattic_woocommerce-payments/client/deposits/declarations.d.ts:16:\n `{\n\t\torder: { status },\n\t\tclassName,\n\t\torderStatusMap,\n\t\tlabelPositionToLeft,\n\t}:` was unexpected", "path": "downloaded_repos/Automattic_woocommerce-payments/client/deposits/declarations.d.ts", "spans": [{"file": "downloaded_repos/Automattic_woocommerce-payments/client/deposits/declarations.d.ts", "start": {"line": 16, "col": 23, "offset": 0}, "end": {"line": 21, "col": 4, "offset": 80}}, {"file": "downloaded_repos/Automattic_woocommerce-payments/client/deposits/declarations.d.ts", "start": {"line": 29, "col": 6, "offset": 0}, "end": {"line": 29, "col": 20, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/payment-methods-list/delete-modal.tsx", "start": {"line": 73, "col": 43, "offset": 0}, "end": {"line": 73, "col": 85, "offset": 42}}]], "message": "Syntax error at line downloaded_repos/Automattic_woocommerce-payments/client/settings/payment-methods-list/delete-modal.tsx:73:\n `&tab=checkout&section=woocommerce_payments` was unexpected", "path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/payment-methods-list/delete-modal.tsx", "spans": [{"file": "downloaded_repos/Automattic_woocommerce-payments/client/settings/payment-methods-list/delete-modal.tsx", "start": {"line": 73, "col": 43, "offset": 0}, "end": {"line": 73, "col": 85, "offset": 42}}]}], "paths": {"scanned": ["downloaded_repos/Automattic_woocommerce-payments/.browserslistrc", "downloaded_repos/Automattic_woocommerce-payments/.editorconfig", "downloaded_repos/Automattic_woocommerce-payments/.eslintignore", "downloaded_repos/Automattic_woocommerce-payments/.eslintrc", "downloaded_repos/Automattic_woocommerce-payments/.git-ftp-include", "downloaded_repos/Automattic_woocommerce-payments/.github/ISSUE_TEMPLATE/bug_report.md", "downloaded_repos/Automattic_woocommerce-payments/.github/ISSUE_TEMPLATE/e2e_test_report.md", "downloaded_repos/Automattic_woocommerce-payments/.github/ISSUE_TEMPLATE/feature.md", "downloaded_repos/Automattic_woocommerce-payments/.github/PULL_REQUEST_TEMPLATE.md", "downloaded_repos/Automattic_woocommerce-payments/.github/actions/commit-push-as-bot/action.yml", "downloaded_repos/Automattic_woocommerce-payments/.github/actions/create-branch/action.yml", "downloaded_repos/Automattic_woocommerce-payments/.github/actions/create-tag/action.yml", "downloaded_repos/Automattic_woocommerce-payments/.github/actions/e2e/atomic-prepare-and-run/action.yml", "downloaded_repos/Automattic_woocommerce-payments/.github/actions/e2e/env-setup/action.yml", "downloaded_repos/Automattic_woocommerce-payments/.github/actions/e2e/run-log-tests/action.yml", "downloaded_repos/Automattic_woocommerce-payments/.github/actions/e2e-pw/run-log-tests/action.yml", "downloaded_repos/Automattic_woocommerce-payments/.github/actions/process-changelog/action.yml", "downloaded_repos/Automattic_woocommerce-payments/.github/actions/setup-php/action.yml", "downloaded_repos/Automattic_woocommerce-payments/.github/actions/setup-repo/action.yml", "downloaded_repos/Automattic_woocommerce-payments/.github/actions/trigger-translations/action.yml", "downloaded_repos/Automattic_woocommerce-payments/.github/actions/version-check/action.yml", "downloaded_repos/Automattic_woocommerce-payments/.github/dependabot.yml", "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/build-zip-and-run-smoke-tests.yml", "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/bundle-size.yml", "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/check-changelog.yml", "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/compatibility.yml", "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/coverage.yml", "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/create-pre-release.yml", "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/deploy-api-docs.yml", "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/e2e-pull-request.yml", "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/e2e-pw-pull-request.yml", "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/e2e-test.yml", "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/e2e-tests-atomic.yml", "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/i18n-weekly-release.yml", "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/js-lint-test.yml", "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/php-compatibility.yml", "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/php-lint-test.yml", "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/post-release-updates.yml", "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/pr-build-live-branch.yml", "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/qit.yml", "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/release-changelog.yml", "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/release-code-freeze.yml", "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/release-pr.yml", "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/scripts/get-next-version.php", "downloaded_repos/Automattic_woocommerce-payments/.gitignore", "downloaded_repos/Automattic_woocommerce-payments/.husky/post-checkout", "downloaded_repos/Automattic_woocommerce-payments/.husky/post-merge", "downloaded_repos/Automattic_woocommerce-payments/.husky/post-rewrite", "downloaded_repos/Automattic_woocommerce-payments/.husky/pre-commit", "downloaded_repos/Automattic_woocommerce-payments/.husky/pre-push", "downloaded_repos/Automattic_woocommerce-payments/.npmrc", "downloaded_repos/Automattic_woocommerce-payments/.nvmrc", "downloaded_repos/Automattic_woocommerce-payments/.phpunit-watcher.yml", "downloaded_repos/Automattic_woocommerce-payments/.prettierignore", "downloaded_repos/Automattic_woocommerce-payments/.prettierrc", "downloaded_repos/Automattic_woocommerce-payments/.stylelintignore", "downloaded_repos/Automattic_woocommerce-payments/.stylelintrc.json", "downloaded_repos/Automattic_woocommerce-payments/CONTRIBUTING.md", "downloaded_repos/Automattic_woocommerce-payments/README.md", "downloaded_repos/Automattic_woocommerce-payments/SECURITY.md", "downloaded_repos/Automattic_woocommerce-payments/assets/css/admin.css", "downloaded_repos/Automattic_woocommerce-payments/assets/css/admin.rtl.css", "downloaded_repos/Automattic_woocommerce-payments/assets/css/success.css", "downloaded_repos/Automattic_woocommerce-payments/assets/css/success.rtl.css", "downloaded_repos/Automattic_woocommerce-payments/assets/fonts/WCPay.eot", "downloaded_repos/Automattic_woocommerce-payments/assets/fonts/WCPay.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/fonts/WCPay.ttf", "downloaded_repos/Automattic_woocommerce-payments/assets/fonts/WCPay.woff", "downloaded_repos/Automattic_woocommerce-payments/assets/images/cards/amex.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/cards/apple-pay.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/cards/cartes_bancaires.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/cards/cb.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/cards/diners.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/cards/discover.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/cards/eftpos.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/cards/google-pay.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/cards/jcb.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/cards/mastercard.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/cards/sepa.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/cards/unionpay.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/cards/unknown.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/cards/visa.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/cards/woo-card.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/confetti.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/deposits-banner.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/dispute-evidence-submitted.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/fraud-protection/<EMAIL>", "downloaded_repos/Automattic_woocommerce-payments/assets/images/icons/check-green.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/icons/close.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/icons/copy.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/icons/send-money.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/icons/shield-stroke-orange.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/icons/shield-stroke-red.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/illustrations/connect-hero.png", "downloaded_repos/Automattic_woocommerce-payments/assets/images/illustrations/setup.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/illustrations/woopayments.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/logo.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-method-icons/affirm.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-method-icons/afterpay.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-method-icons/amex.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-method-icons/applepay.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-method-icons/discover.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-method-icons/gpay.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-method-icons/grabpay.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-method-icons/jcb.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-method-icons/klarna.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-method-icons/mastercard.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-method-icons/visa.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-method-icons/wechat-pay.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-method-icons/woopay.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/affirm-badge.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/affirm-icon.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/affirm-logo-dark.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/affirm-logo.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/afterpay-badge.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/afterpay-cashapp-badge.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/afterpay-cashapp-icon.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/afterpay-cashapp-logo-dark.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/afterpay-cashapp-logo.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/afterpay-icon.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/afterpay-logo.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/alipay-logo.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/all_local_payments.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/bancontact.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/bank-debit.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/becs.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/cc.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/clearpay.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/eps.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/generic-card-black.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/generic-card.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/giropay.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/grabpay.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/ideal.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/jcb.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/klarna-pill.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/klarna.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/link.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/local_payments.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/multibanco-icon.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/multibanco-instructions.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/multibanco-logo-dark.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/multibanco-logo.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/multibanco.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/p24.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/przelewy24.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/sepa-debit.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/sofort.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/taptopay.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/wechat-pay.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/woo-short.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/payment-methods/woo.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/subscriptions-empty-state-unconnected.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/subscriptions-onboarding-modal.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/transactions-banner.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/upe_preview_illustration.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/woo-logo.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/woopay.svg", "downloaded_repos/Automattic_woocommerce-payments/assets/images/woopayments.svg", "downloaded_repos/Automattic_woocommerce-payments/babel.config.js", "downloaded_repos/Automattic_woocommerce-payments/bin/check-changelog.sh", "downloaded_repos/Automattic_woocommerce-payments/bin/check-test-coverage.sh", "downloaded_repos/Automattic_woocommerce-payments/bin/class-wcpay-changelog-formatter.php", "downloaded_repos/Automattic_woocommerce-payments/bin/cli.sh", "downloaded_repos/Automattic_woocommerce-payments/bin/combine-pot-files.php", "downloaded_repos/Automattic_woocommerce-payments/bin/docker-setup.sh", "downloaded_repos/Automattic_woocommerce-payments/bin/install-wp-tests.sh", "downloaded_repos/Automattic_woocommerce-payments/bin/jurassic-tube-setup.sh", "downloaded_repos/Automattic_woocommerce-payments/bin/phpcs-compat.sh", "downloaded_repos/Automattic_woocommerce-payments/bin/phpcs.sh", "downloaded_repos/Automattic_woocommerce-payments/bin/phpunit.sh", "downloaded_repos/Automattic_woocommerce-payments/bin/run-ci-tests-check-coverage.bash", "downloaded_repos/Automattic_woocommerce-payments/bin/run-ci-tests.bash", "downloaded_repos/Automattic_woocommerce-payments/bin/run-psalm.sh", "downloaded_repos/Automattic_woocommerce-payments/bin/run-tests.sh", "downloaded_repos/Automattic_woocommerce-payments/bin/wcpay-live-branches/README.md", "downloaded_repos/Automattic_woocommerce-payments/bin/wcpay-live-branches/wcpay-live-branches.user.js", "downloaded_repos/Automattic_woocommerce-payments/bin/xdebug-toggle.sh", "downloaded_repos/Automattic_woocommerce-payments/changelog/.gitkeep", "downloaded_repos/Automattic_woocommerce-payments/changelog/chore-remove-config-and-qrcode-dependencies", "downloaded_repos/Automattic_woocommerce-payments/changelog/fix-icon-border-gray-200", "downloaded_repos/Automattic_woocommerce-payments/changelog/fix-suppress-renewal-order-completed-on-dispute-won", "downloaded_repos/Automattic_woocommerce-payments/changelog/fix-woopmnt-5191-3d-secure-verification-dialog-fails-to-appear-when-using", "downloaded_repos/Automattic_woocommerce-payments/changelog/fix-woopmnt-5198-spinner-in-the-proceed-to-checkout-button-is-misaligned", "downloaded_repos/Automattic_woocommerce-payments/changelog/loan-page-wp-components-update", "downloaded_repos/Automattic_woocommerce-payments/changelog/make-kyc-onboarding-use-wp-components-from-wp-installation", "downloaded_repos/Automattic_woocommerce-payments/changelog/onboading-pages-wp-components", "downloaded_repos/Automattic_woocommerce-payments/changelog/update-add-lt-address-no-state", "downloaded_repos/Automattic_woocommerce-payments/changelog/update-documents-wp-components", "downloaded_repos/Automattic_woocommerce-payments/changelog/update-woopmnt-5193-fix-link-to-disputes-documentation", "downloaded_repos/Automattic_woocommerce-payments/changelog/woopmnt-4836-non-specific-card-details-notice-when-updating-payment", "downloaded_repos/Automattic_woocommerce-payments/changelog.txt", "downloaded_repos/Automattic_woocommerce-payments/client/capital/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/capital/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/card-readers/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/card-readers/list/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/card-readers/list/list-item.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/card-readers/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/cart/blocks/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/cart/blocks/product-details.js", "downloaded_repos/Automattic_woocommerce-payments/client/cart/blocks/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/cart/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/api/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/blocks/confirm-card-payment.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/blocks/hooks.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/blocks/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/blocks/payment-elements.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/blocks/payment-method-label.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/blocks/payment-methods-logos/index.ts", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/blocks/payment-methods-logos/logo-popover.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/blocks/payment-methods-logos/payment-methods-logos.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/blocks/payment-methods-logos/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/blocks/payment-processor.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/blocks/saved-token-handler.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/blocks/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/blocks/utils.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/classic/3ds-flow-handling.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/classic/event-handlers.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/classic/payment-processing.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/classic/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/constants.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/express-checkout-buttons.scss", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/preview.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/stripe-link/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/upe-styles/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/upe-styles/upe-styles.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/upe-styles/utils.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/utils/copy-test-number.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/utils/fingerprint.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/utils/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/utils/request.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/utils/show-error-checkout.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/utils/upe.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/woopay/connect/connect-utils.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/woopay/connect/session-connect.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/woopay/connect/user-connect.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/woopay/connect/woopay-connect-iframe.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/woopay/connect/woopay-connect.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/woopay/direct-checkout/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/woopay/direct-checkout/utils.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/woopay/direct-checkout/woopay-direct-checkout.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/woopay/email-input-iframe.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/woopay/express-button/express-checkout-iframe.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/woopay/express-button/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/woopay/express-button/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/woopay/express-button/use-express-checkout-product-handler.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/woopay/express-button/utils.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/woopay/express-button/woopay-express-checkout-button.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/woopay/express-button/woopay-express-checkout-payment-method.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/woopay/express-button/woopay-first-party-auth.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/woopay/express-button/woopay-icon-light.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/woopay/express-button/woopay-icon.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/woopay/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/woopay/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/checkout/woopay/utils.js", "downloaded_repos/Automattic_woocommerce-payments/client/components/accordion/accordion.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/accordion/body.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/accordion/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/accordion/row.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/accordion/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/accordion/subtitle.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/accordion/title.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/accordion/types.ts", "downloaded_repos/Automattic_woocommerce-payments/client/components/account-balances/balance-block.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/account-balances/balance-tooltip.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/account-balances/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/account-balances/strings.ts", "downloaded_repos/Automattic_woocommerce-payments/client/components/account-balances/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/account-status/account-fees/expiration-bar.js", "downloaded_repos/Automattic_woocommerce-payments/client/components/account-status/account-fees/expiration-description.js", "downloaded_repos/Automattic_woocommerce-payments/client/components/account-status/account-fees/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/components/account-status/account-fees/styles.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/account-status/account-status-item.js", "downloaded_repos/Automattic_woocommerce-payments/client/components/account-status/account-tools/__tests__/__snapshots__/index.test.tsx.snap", "downloaded_repos/Automattic_woocommerce-payments/client/components/account-status/account-tools/__tests__/index.test.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/account-status/account-tools/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/account-status/account-tools/strings.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/account-status/account-tools/styles.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/account-status/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/components/account-status/shared.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/account-status/status-chip.js", "downloaded_repos/Automattic_woocommerce-payments/client/components/account-status/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/active-loan-summary/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/active-loan-summary/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/amount-input/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/amount-input/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/banner-notice/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/banner-notice/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/cancel-authorization-button/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/capture-authorization-button/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/card-notice/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/card-notice/styles.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/chip/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/chip/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/clickable-cell/__tests__/__snapshots__/index.js.snap", "downloaded_repos/Automattic_woocommerce-payments/client/components/clickable-cell/__tests__/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/components/clickable-cell/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/clickable-cell/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/confetti-animation/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/confirmation-modal/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/confirmation-modal/styles.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/copy-button/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/copy-button/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/custom-select-control/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/custom-select-control/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/customer-link/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/deposit-status-chip/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/deposits-overview/deposit-notices.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/deposits-overview/deposit-schedule.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/deposits-overview/hooks.ts", "downloaded_repos/Automattic_woocommerce-payments/client/components/deposits-overview/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/deposits-overview/recent-deposits-list.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/deposits-overview/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/deposits-status/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/details-link/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/dispute-status-chip/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/dispute-status-chip/mappings.ts", "downloaded_repos/Automattic_woocommerce-payments/client/components/disputed-order-notice/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/components/disputed-order-notice/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/download-button/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/download-button/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/duplicate-notice/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/error-boundary/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/components/file-upload/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/file-upload/preview.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/file-upload/upload-error.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/form/fields.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/form/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/grouped-select-control/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/grouped-select-control/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/horizontal-list/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/horizontal-list/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/icons/block-embed.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/icons/block-post-author.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/icons/lightbulb.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/icons/warning.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/inline-label-select/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/inline-label-select/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/inline-notice/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/inline-notice/styles.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/jetpack-idc-notice/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/load-bar/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/load-bar/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/loadable/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/loadable/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/order-link/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/page/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/page/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/paragraphs/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/payment-confirm-illustration/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/payment-confirm-illustration/styles.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/payment-delete-illustration/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/payment-delete-illustration/styles.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/payment-method-details/__tests__/__snapshots__/index.js.snap", "downloaded_repos/Automattic_woocommerce-payments/client/components/payment-method-details/__tests__/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/components/payment-method-details/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/payment-method-details/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/payment-method-logos/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/payment-method-logos/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/payment-status-chip/__tests__/__snapshots__/index.js.snap", "downloaded_repos/Automattic_woocommerce-payments/client/components/payment-status-chip/__tests__/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/components/payment-status-chip/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/payment-status-chip/mappings.ts", "downloaded_repos/Automattic_woocommerce-payments/client/components/payments-status/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/phone-number-control/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/phone-number-control/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/pill/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/pill/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/progress-bar/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/progress-bar/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/risk-level/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/risk-level/strings.ts", "downloaded_repos/Automattic_woocommerce-payments/client/components/sandbox-mode-switch-to-live-notice/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/sandbox-mode-switch-to-live-notice/modal/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/sandbox-mode-switch-to-live-notice/modal/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/sandbox-mode-switch-to-live-notice/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/stepper/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/stepper/stepper-context.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/stepper/stepper-panel.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/stepper/stepper-types.ts", "downloaded_repos/Automattic_woocommerce-payments/client/components/stepper/stepper.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/stepper/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/stripe-spinner/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/stripe-spinner/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/test-mode-notice/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/tip-box/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/tip-box/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/tooltip/README.md", "downloaded_repos/Automattic_woocommerce-payments/client/components/tooltip/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/tooltip/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/tooltip/tooltip-base.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/transaction-status-pill/__tests__/__snapshots__/index.test.js.snap", "downloaded_repos/Automattic_woocommerce-payments/client/components/transaction-status-pill/__tests__/index.test.js", "downloaded_repos/Automattic_woocommerce-payments/client/components/transaction-status-pill/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/transaction-status-pill/mappings.ts", "downloaded_repos/Automattic_woocommerce-payments/client/components/welcome/currency-select.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/welcome/hooks.ts", "downloaded_repos/Automattic_woocommerce-payments/client/components/welcome/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/welcome/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/wizard/README.md", "downloaded_repos/Automattic_woocommerce-payments/client/components/wizard/__tests__/wizard.test.js", "downloaded_repos/Automattic_woocommerce-payments/client/components/wizard/collapsible-body.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/wizard/collapsible-body.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wizard/task/context.ts", "downloaded_repos/Automattic_woocommerce-payments/client/components/wizard/task/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/components/wizard/task-item.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/wizard/task-item.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wizard/task-list.js", "downloaded_repos/Automattic_woocommerce-payments/client/components/wizard/wrapper/context.js", "downloaded_repos/Automattic_woocommerce-payments/client/components/wizard/wrapper/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/components/woopay/hooks/use-phone-number-validity.js", "downloaded_repos/Automattic_woocommerce-payments/client/components/woopay/hooks/use-selected-payment-method.js", "downloaded_repos/Automattic_woocommerce-payments/client/components/woopay/hooks/use-woopay-user.js", "downloaded_repos/Automattic_woocommerce-payments/client/components/woopay/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/woopay/save-user/additional-information.js", "downloaded_repos/Automattic_woocommerce-payments/client/components/woopay/save-user/agreement.js", "downloaded_repos/Automattic_woocommerce-payments/client/components/woopay/save-user/checkout-page-save-user.js", "downloaded_repos/Automattic_woocommerce-payments/client/components/woopay/save-user/container.js", "downloaded_repos/Automattic_woocommerce-payments/client/components/woopay/save-user/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/base-control.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/button.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/card-body.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/card-divider.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/card-footer.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/card-header.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/card-notice.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/card.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/checkbox-control.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/drop-zone.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/dropdown-menu.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/external-link.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/flex-block.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/flex-item.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/flex.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/form-file-upload.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/horizontal-rule.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/icon.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/menu-group.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/menu-item.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/modal.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/notice.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/number-control.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/panel-body.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/panel.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/popover.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/radio-control.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/range-control.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/search-control.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/select-control.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/snackbar-list.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/spinner.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/tab-panel.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/text-control.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/text.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/textarea-control.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/toggle-control.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/components/tooltip.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/components/wp-components-wrapped/make-wrapped-component.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/connect-account-page/incentive.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/connect-account-page/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/connect-account-page/info-notice-modal.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/connect-account-page/modal/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/connect-account-page/modal/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/connect-account-page/strings.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/connect-account-page/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/constants/payment-method.ts", "downloaded_repos/Automattic_woocommerce-payments/client/constants/time.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/authorizations/action-types.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/authorizations/actions.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/authorizations/hooks.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/authorizations/index.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/authorizations/reducer.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/authorizations/resolvers.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/authorizations/selectors.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/capital/action-types.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/capital/actions.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/capital/hooks.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/capital/index.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/capital/reducer.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/capital/resolvers.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/capital/selectors.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/capital/types.d.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/card-readers/action-types.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/card-readers/actions.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/card-readers/hooks.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/card-readers/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/card-readers/reducer.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/card-readers/resolvers.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/card-readers/selectors.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/charges/action-types.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/charges/actions.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/charges/hooks.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/charges/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/charges/reducer.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/charges/resolvers.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/charges/selectors.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/charges/types.d.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/constants.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/deposits/action-types.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/deposits/actions.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/deposits/hooks.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/deposits/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/deposits/reducer.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/deposits/resolvers.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/deposits/selectors.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/disputes/action-types.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/disputes/actions.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/disputes/hooks.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/disputes/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/disputes/reducer.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/disputes/resolvers.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/disputes/selectors.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/documents/action-types.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/documents/actions.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/documents/hooks.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/documents/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/documents/reducer.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/documents/resolvers.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/documents/selectors.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/files/action-types.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/files/actions.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/files/hooks.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/files/index.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/files/reducer.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/files/resolvers.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/files/selectors.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/files/types.d.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/index.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/payment-intents/action-types.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/payment-intents/actions.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/payment-intents/hooks.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/payment-intents/index.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/payment-intents/reducer.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/payment-intents/resolvers.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/payment-intents/selectors.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/payment-intents/types.d.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/settings/action-types.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/settings/actions.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/settings/hooks.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/settings/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/settings/reducer.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/settings/resolvers.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/settings/selectors.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/store.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/timeline/action-types.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/timeline/actions.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/timeline/hooks.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/timeline/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/timeline/reducer.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/timeline/resolvers.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/timeline/selectors.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/timeline/types.d.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/transactions/action-types.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/transactions/actions.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/transactions/hooks.ts", "downloaded_repos/Automattic_woocommerce-payments/client/data/transactions/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/transactions/reducer.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/transactions/resolvers.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/transactions/selectors.js", "downloaded_repos/Automattic_woocommerce-payments/client/data/types.d.ts", "downloaded_repos/Automattic_woocommerce-payments/client/declarations.d.ts", "downloaded_repos/Automattic_woocommerce-payments/client/deposits/declarations.d.ts", "downloaded_repos/Automattic_woocommerce-payments/client/deposits/details/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/deposits/details/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/deposits/filters/config.js", "downloaded_repos/Automattic_woocommerce-payments/client/deposits/filters/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/deposits/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/deposits/instant-payouts/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/deposits/instant-payouts/modal.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/deposits/instant-payouts/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/deposits/list/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/deposits/list/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/deposits/strings.ts", "downloaded_repos/Automattic_woocommerce-payments/client/deposits/utils/index.ts", "downloaded_repos/Automattic_woocommerce-payments/client/disable-confirmation-modal/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/disable-confirmation-modal/styles.scss", "downloaded_repos/Automattic_woocommerce-payments/client/disputes/filters/config.ts", "downloaded_repos/Automattic_woocommerce-payments/client/disputes/filters/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/disputes/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/disputes/info/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/disputes/info/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/disputes/new-evidence/confirmation-screen.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/disputes/new-evidence/cover-letter-generator.ts", "downloaded_repos/Automattic_woocommerce-payments/client/disputes/new-evidence/cover-letter.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/disputes/new-evidence/customer-details.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/disputes/new-evidence/duplicate-status.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/disputes/new-evidence/file-upload-control.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/disputes/new-evidence/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/disputes/new-evidence/product-details.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/disputes/new-evidence/recommended-document-fields.ts", "downloaded_repos/Automattic_woocommerce-payments/client/disputes/new-evidence/recommended-documents.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/disputes/new-evidence/refund-status.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/disputes/new-evidence/shipping-details.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/disputes/new-evidence/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/disputes/new-evidence/types.ts", "downloaded_repos/Automattic_woocommerce-payments/client/disputes/new-evidence/utils.ts", "downloaded_repos/Automattic_woocommerce-payments/client/disputes/redirect-to-transaction-details/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/disputes/redirect-to-transaction-details/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/disputes/strings.ts", "downloaded_repos/Automattic_woocommerce-payments/client/disputes/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/disputes/utils.ts", "downloaded_repos/Automattic_woocommerce-payments/client/documents/filters/config.ts", "downloaded_repos/Automattic_woocommerce-payments/client/documents/filters/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/documents/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/documents/list/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/documents/list/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/documents/strings.ts", "downloaded_repos/Automattic_woocommerce-payments/client/embedded-components/appearance.ts", "downloaded_repos/Automattic_woocommerce-payments/client/embedded-components/hooks.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/embedded-components/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/embedded-components/types.ts", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/__fixtures__/cart.js", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/__tests__/cart-api.test.js", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/__tests__/event-handlers.test.js", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/__tests__/order-api.js", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/__tests__/tokenized-express-checkout--order-pay-page.test.js", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/__tests__/tokenized-express-checkout--product-page.test.js", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/__tests__/tokenized-express-checkout--shortcode-checkout-page.test.js", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/blocks/components/express-checkout-button-preview.js", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/blocks/components/express-checkout-component.js", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/blocks/components/express-checkout-container.js", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/blocks/components/express-checkout-preview.js", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/blocks/express-checkout-element.scss", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/blocks/hooks/__tests__/use-express-checkout.test.js", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/blocks/hooks/use-express-checkout.js", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/blocks/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/button-ui.js", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/cart-api.js", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/compatibility/__tests__/wc-product-bundles.test.js", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/compatibility/__tests__/wc-product-page.test.js", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/compatibility/wc-deposits.js", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/compatibility/wc-order-attribution.js", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/compatibility/wc-product-bundles.js", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/compatibility/wc-product-page.js", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/constants.js", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/debounce.js", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/event-handlers.js", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/order-api.js", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/tracking.js", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/transformers/__tests__/wc-to-stripe.test.js", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/transformers/stripe-to-wc.js", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/transformers/wc-to-stripe.js", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/utils/__tests__/checkPaymentMethodIsAvailable.test.js", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/utils/__tests__/index.ts", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/utils/__tests__/normalize.js", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/utils/checkPaymentMethodIsAvailable.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/utils/index.ts", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/utils/normalize.js", "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/utils/shipping-fields.js", "downloaded_repos/Automattic_woocommerce-payments/client/external-declarations.d.ts", "downloaded_repos/Automattic_woocommerce-payments/client/fraud-scripts/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/fraud-scripts/sift.js", "downloaded_repos/Automattic_woocommerce-payments/client/fraud-scripts/stripe.js", "downloaded_repos/Automattic_woocommerce-payments/client/frontend-tracks/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/globals.d.ts", "downloaded_repos/Automattic_woocommerce-payments/client/hooks/use-persisted-table-column-visibility.ts", "downloaded_repos/Automattic_woocommerce-payments/client/hooks/use-report-export.ts", "downloaded_repos/Automattic_woocommerce-payments/client/hooks/use-stripe-async.js", "downloaded_repos/Automattic_woocommerce-payments/client/hooks/use-unique-id.ts", "downloaded_repos/Automattic_woocommerce-payments/client/icons/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/icons/shield-icon.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/icons/tip-icon.js", "downloaded_repos/Automattic_woocommerce-payments/client/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/merchant-feedback-prompt/hooks.ts", "downloaded_repos/Automattic_woocommerce-payments/client/merchant-feedback-prompt/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/merchant-feedback-prompt/negative-modal.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/merchant-feedback-prompt/positive-modal.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/merchant-feedback-prompt/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/onboarding/context.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/onboarding/form.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/onboarding/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/onboarding/kyc/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/onboarding/step.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/onboarding/steps/business-details.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/onboarding/steps/embedded-kyc.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/onboarding/steps/loading.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/onboarding/strings.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/onboarding/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/onboarding/tracking.ts", "downloaded_repos/Automattic_woocommerce-payments/client/onboarding/translations/descriptions.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/onboarding/types.ts", "downloaded_repos/Automattic_woocommerce-payments/client/onboarding/utils.ts", "downloaded_repos/Automattic_woocommerce-payments/client/onboarding/validation.ts", "downloaded_repos/Automattic_woocommerce-payments/client/order/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/order/order-status-change-strategies/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/order/order-status-confirmation-modal/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/order/refund-confirm-modal/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/order/refund-confirm-modal/styles.scss", "downloaded_repos/Automattic_woocommerce-payments/client/order/test-mode-notice/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/overview/hooks.ts", "downloaded_repos/Automattic_woocommerce-payments/client/overview/inbox-notifications/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/overview/inbox-notifications/index.scss", "downloaded_repos/Automattic_woocommerce-payments/client/overview/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/overview/modal/connection-success/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/overview/modal/connection-success/strings.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/overview/modal/connection-success/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/overview/modal/progressive-onboarding-eligibility/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/overview/modal/progressive-onboarding-eligibility/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/overview/modal/reset-account/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/overview/modal/reset-account/strings.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/overview/modal/reset-account/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/overview/modal/update-business-details/index.scss", "downloaded_repos/Automattic_woocommerce-payments/client/overview/modal/update-business-details/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/overview/modal/update-business-details/strings.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/overview/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/overview/task-list/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/overview/task-list/strings.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/overview/task-list/tasks/dispute-task.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/overview/task-list/tasks/go-live-task.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/overview/task-list/tasks/po-task.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/overview/task-list/tasks/reconnect-task.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/overview/task-list/tasks/update-business-details-task.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/overview/task-list/tasks.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/overview/task-list/types.d.ts", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/charge-details/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/dispute-details/dispute-awaiting-response-details.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/dispute-details/dispute-due-by-date.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/dispute-details/dispute-notice.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/dispute-details/dispute-resolution-footer.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/dispute-details/dispute-steps.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/dispute-details/dispute-summary-row.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/dispute-details/evidence-list.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/dispute-details/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/order-details/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/payment-details/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/payment-method/bancontact/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/payment-method/base-payment-method-details/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/payment-method/becs/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/payment-method/card/check.js", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/payment-method/card/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/payment-method/card-present/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/payment-method/detail.js", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/payment-method/eps/bank-list.js", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/payment-method/eps/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/payment-method/giropay/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/payment-method/ideal/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/payment-method/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/payment-method/klarna/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/payment-method/p24/bank-list.ts", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/payment-method/p24/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/payment-method/sepa/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/payment-method/sofort/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/readers/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/summary/__tests__/__snapshots__/index.test.js.snap", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/summary/__tests__/index.test.js", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/summary/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/summary/missing-order-notice/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/summary/missing-order-notice/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/summary/refund-modal/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/summary/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/timeline/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/timeline/map-events.js", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/timeline/mappings.ts", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/timeline/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/transaction-breakdown/fees-breakdown/fee-breakdown-components.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/transaction-breakdown/fees-breakdown/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/transaction-breakdown/hooks.ts", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/transaction-breakdown/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/transaction-breakdown/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/transaction-breakdown/types.ts", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/transaction-breakdown/utils.ts", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/types.ts", "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/utils/tax-descriptions.ts", "downloaded_repos/Automattic_woocommerce-payments/client/payment-methods-icons.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/payment-methods-map.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/plugins-page/deactivation-survey/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/plugins-page/deactivation-survey/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/plugins-page/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/product-details/bnpl-site-messaging/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/product-details/bnpl-site-messaging/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/product-details/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/public-path.js", "downloaded_repos/Automattic_woocommerce-payments/client/requirements-map.ts", "downloaded_repos/Automattic_woocommerce-payments/client/settings/__tests__/google-pay-test-mode-compatibility-notice.test.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/__tests__/settings-section.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/advanced-settings/debug-mode.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/advanced-settings/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/advanced-settings/multi-currency-toggle.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/advanced-settings/stripe-billing-notices/context.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/advanced-settings/stripe-billing-notices/migrate-automatically-notice.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/advanced-settings/stripe-billing-notices/migrate-completed-notice.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/advanced-settings/stripe-billing-notices/migrate-option-notice.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/advanced-settings/stripe-billing-notices/migration-progress-notice.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/advanced-settings/stripe-billing-notices/notices.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/advanced-settings/stripe-billing-notices/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/settings/advanced-settings/stripe-billing-section.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/advanced-settings/stripe-billing-toggle.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/advanced-settings/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/settings/advanced-settings/wcpay-subscriptions-toggle.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/buy-now-pay-later-section/__tests__/buy-now-pay-later-section.test.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/buy-now-pay-later-section/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/card-body/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/card-body/styles.scss", "downloaded_repos/Automattic_woocommerce-payments/client/settings/constants.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/deposits/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/deposits/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/settings/express-checkout/__tests__/index.test.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/express-checkout/apple-google-pay-item.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/express-checkout/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/express-checkout/link-item.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/express-checkout/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/settings/express-checkout/woopay-item.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/express-checkout-settings/__tests__/file-upload.test.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/express-checkout-settings/__tests__/index.test.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/express-checkout-settings/__tests__/payment-request-button-preview.test.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/express-checkout-settings/__tests__/payment-request-settings.test.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/express-checkout-settings/__tests__/woopay-settings.test.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/express-checkout-settings/file-upload.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/express-checkout-settings/general-payment-request-button-settings.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/express-checkout-settings/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/express-checkout-settings/index.scss", "downloaded_repos/Automattic_woocommerce-payments/client/settings/express-checkout-settings/payment-request-button-preview.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/express-checkout-settings/payment-request-settings.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/express-checkout-settings/woopay-preview.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/express-checkout-settings/woopay-settings.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/allow-countries-notice.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/cards/address-mismatch.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/cards/avs-mismatch.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/cards/cvc-verification.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/cards/international-ip-address.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/cards/ip-address-mismatch.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/cards/order-items-threshold.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/cards/purchase-price-threshold.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/constants.ts", "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/context.ts", "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/rule-card-notice.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/rule-card.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/rule-description.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/rule-toggle.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/utils.ts", "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/components/fp-help-text/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/components/fp-modal/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/components/index.ts", "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/components/protection-level-modal-notice/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/components/protection-levels/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/interfaces.ts", "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/tour/declarations.d.ts", "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/tour/index.test.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/tour/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/tour/steps.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/general-settings/enable-woopayments-checkbox.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/general-settings/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/general-settings/test-mode-confirm-modal.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/google-pay-test-mode-compatibility-notice.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/loadable-settings-section.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/payment-method-icon/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/payment-method-icon/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/settings/payment-methods-list/__tests__/__snapshots__/activation-modal.test.js.snap", "downloaded_repos/Automattic_woocommerce-payments/client/settings/payment-methods-list/__tests__/__snapshots__/delete-modal.test.js.snap", "downloaded_repos/Automattic_woocommerce-payments/client/settings/payment-methods-list/__tests__/activation-modal.test.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/payment-methods-list/__tests__/delete-modal.test.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/payment-methods-list/__tests__/payment-method.test.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/payment-methods-list/__tests__/use-payment-method-availability.test.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/payment-methods-list/activation-modal.scss", "downloaded_repos/Automattic_woocommerce-payments/client/settings/payment-methods-list/activation-modal.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/payment-methods-list/delete-modal.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/payment-methods-list/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/payment-methods-list/payment-method.scss", "downloaded_repos/Automattic_woocommerce-payments/client/settings/payment-methods-list/payment-method.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/payment-methods-list/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/settings/payment-methods-list/use-payment-method-availability.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/payment-methods-section/__tests__/payment-methods-section.test.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/payment-methods-section/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/phone-input/declarations.d.ts", "downloaded_repos/Automattic_woocommerce-payments/client/settings/phone-input/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/phone-input/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/settings/save-settings-section/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/save-settings-section/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/settings/settings-layout/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/settings-layout/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/settings/settings-manager/duplicated-payment-methods-context.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/settings-manager/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/settings-manager/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/settings/settings-section.scss", "downloaded_repos/Automattic_woocommerce-payments/client/settings/settings-section.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/settings-warnings/incompatibility-notice.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/support-email-input/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/support-phone-input/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/transactions/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/transactions/manual-capture-control.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/settings/transactions/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/settings/wcpay-settings-context.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/woopay-disable-feedback/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/settings/woopay-disable-feedback/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/stylesheets/abstracts/_breakpoints.scss", "downloaded_repos/Automattic_woocommerce-payments/client/stylesheets/abstracts/_colors.scss", "downloaded_repos/Automattic_woocommerce-payments/client/stylesheets/abstracts/_mixins.scss", "downloaded_repos/Automattic_woocommerce-payments/client/stylesheets/abstracts/_variables.scss", "downloaded_repos/Automattic_woocommerce-payments/client/subscription-edit-page.js", "downloaded_repos/Automattic_woocommerce-payments/client/subscription-product-onboarding/modal.js", "downloaded_repos/Automattic_woocommerce-payments/client/subscription-product-onboarding/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/subscription-product-onboarding/toast.js", "downloaded_repos/Automattic_woocommerce-payments/client/subscriptions-empty-state/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/subscriptions-empty-state/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/success/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/tos/disabled-notice.js", "downloaded_repos/Automattic_woocommerce-payments/client/tos/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/tos/modal/__tests__/__snapshots__/index.test.js.snap", "downloaded_repos/Automattic_woocommerce-payments/client/tos/modal/__tests__/index.test.js", "downloaded_repos/Automattic_woocommerce-payments/client/tos/modal/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/tos/modal/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/tos/request.ts", "downloaded_repos/Automattic_woocommerce-payments/client/tracks/event.d.ts", "downloaded_repos/Automattic_woocommerce-payments/client/tracks/index.ts", "downloaded_repos/Automattic_woocommerce-payments/client/transactions/autocompleter.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/transactions/blocked/columns.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/transactions/blocked/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/transactions/declarations.d.ts", "downloaded_repos/Automattic_woocommerce-payments/client/transactions/filters/config.ts", "downloaded_repos/Automattic_woocommerce-payments/client/transactions/filters/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/transactions/fraud-protection/autocompleter.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/transactions/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/transactions/list/converted-amount.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/transactions/list/deposit.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/transactions/list/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/transactions/list/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/transactions/risk-review/columns.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/transactions/risk-review/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/transactions/risk-review/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/transactions/strings.ts", "downloaded_repos/Automattic_woocommerce-payments/client/transactions/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/transactions/uncaptured/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/transactions/utils/getTransactionPaymentMethodTitle.ts", "downloaded_repos/Automattic_woocommerce-payments/client/types/account/account-status.d.ts", "downloaded_repos/Automattic_woocommerce-payments/client/types/account-overview.d.ts", "downloaded_repos/Automattic_woocommerce-payments/client/types/authorizations.d.ts", "downloaded_repos/Automattic_woocommerce-payments/client/types/balance-transactions.d.ts", "downloaded_repos/Automattic_woocommerce-payments/client/types/card-readers.d.ts", "downloaded_repos/Automattic_woocommerce-payments/client/types/charges.d.ts", "downloaded_repos/Automattic_woocommerce-payments/client/types/data.d.ts", "downloaded_repos/Automattic_woocommerce-payments/client/types/deposits.d.ts", "downloaded_repos/Automattic_woocommerce-payments/client/types/disputes.d.ts", "downloaded_repos/Automattic_woocommerce-payments/client/types/errors.d.ts", "downloaded_repos/Automattic_woocommerce-payments/client/types/fees.d.ts", "downloaded_repos/Automattic_woocommerce-payments/client/types/fraud-outcome.d.ts", "downloaded_repos/Automattic_woocommerce-payments/client/types/notices.d.ts", "downloaded_repos/Automattic_woocommerce-payments/client/types/orders.d.ts", "downloaded_repos/Automattic_woocommerce-payments/client/types/payment-intents.d.ts", "downloaded_repos/Automattic_woocommerce-payments/client/types/payment-methods.d.ts", "downloaded_repos/Automattic_woocommerce-payments/client/types/wcpay-data-settings-hooks.ts", "downloaded_repos/Automattic_woocommerce-payments/client/types/wp-components.d.ts", "downloaded_repos/Automattic_woocommerce-payments/client/utils/account-fees.scss", "downloaded_repos/Automattic_woocommerce-payments/client/utils/account-fees.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/utils/charge/index.ts", "downloaded_repos/Automattic_woocommerce-payments/client/utils/checkout.js", "downloaded_repos/Automattic_woocommerce-payments/client/utils/compute-suggestion-match.ts", "downloaded_repos/Automattic_woocommerce-payments/client/utils/data.js", "downloaded_repos/Automattic_woocommerce-payments/client/utils/date-time.ts", "downloaded_repos/Automattic_woocommerce-payments/client/utils/express-checkout/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/utils/fees/index.test.ts", "downloaded_repos/Automattic_woocommerce-payments/client/utils/fees/index.ts", "downloaded_repos/Automattic_woocommerce-payments/client/utils/format-list-of-items.ts", "downloaded_repos/Automattic_woocommerce-payments/client/utils/index.js", "downloaded_repos/Automattic_woocommerce-payments/client/utils/is-value-truthy.ts", "downloaded_repos/Automattic_woocommerce-payments/client/utils/order.js", "downloaded_repos/Automattic_woocommerce-payments/client/utils/sanitize.ts", "downloaded_repos/Automattic_woocommerce-payments/client/utils/update-woocommerce-user-meta.js", "downloaded_repos/Automattic_woocommerce-payments/client/utils/use-confirm-navigation.js", "downloaded_repos/Automattic_woocommerce-payments/client/utils/use-toggle.js", "downloaded_repos/Automattic_woocommerce-payments/client/vat/form/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/vat/form/style.scss", "downloaded_repos/Automattic_woocommerce-payments/client/vat/form/tasks/company-data-task.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/vat/form/tasks/vat-number-task.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/vat/form-modal/index.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/vat/types.d.ts", "downloaded_repos/Automattic_woocommerce-payments/client/wordpress-components-context/bundled-wp-components-provider.js", "downloaded_repos/Automattic_woocommerce-payments/client/wordpress-components-context/context.tsx", "downloaded_repos/Automattic_woocommerce-payments/client/wordpress-components-context/unbundled-wp-components-provider.js", "downloaded_repos/Automattic_woocommerce-payments/composer.json", "downloaded_repos/Automattic_woocommerce-payments/composer.lock", "downloaded_repos/Automattic_woocommerce-payments/default.env", "downloaded_repos/Automattic_woocommerce-payments/dev/phpcs/WCPay/Sniffs/Hooks/DisallowHooksInConstructorSniff.php", "downloaded_repos/Automattic_woocommerce-payments/dev/phpcs/WCPay/ruleset.xml", "downloaded_repos/Automattic_woocommerce-payments/docker/README.md", "downloaded_repos/Automattic_woocommerce-payments/docker/mu-plugins/.gitkeep", "downloaded_repos/Automattic_woocommerce-payments/docker/wc-payments-php.ini", "downloaded_repos/Automattic_woocommerce-payments/docker/wordpress_xdebug/Dockerfile", "downloaded_repos/Automattic_woocommerce-payments/docker-compose.yml", "downloaded_repos/Automattic_woocommerce-payments/docs/dependencies.md", "downloaded_repos/Automattic_woocommerce-payments/docs/metadata.md", "downloaded_repos/Automattic_woocommerce-payments/docs/renovate.md", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/README.md", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/build.sh", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/font-selection.json", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/fonts/slate.eot", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/fonts/slate.svg", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/fonts/slate.ttf", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/fonts/slate.woff", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/fonts/slate.woff2", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/images/favicon-180x180.png", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/images/favicon-192x192.png", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/images/favicon-270x270.png", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/images/favicon-32x32.png", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/images/logo.png", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/images/navbar.png", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/images/woocommerce-api-key-generated.png", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/images/woocommerce-api-keys-settings.png", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/images/woocommerce-creating-api-keys.png", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/includes/wp-api-v3/authentication.md", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/includes/wp-api-v3/authorization.md", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/includes/wp-api-v3/customer.md", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/includes/wp-api-v3/intent.md", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/includes/wp-api-v3/introduction.md", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/includes/wp-api-v3/order.md", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/includes/wp-api-v3/payouts.md", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/includes/wp-api-v3/reports.md", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/index.html.md", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/all.js", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/all_nosearch.js", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/app/_lang.js", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/app/_search.js", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/app/_toc.js", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_energize.js", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.highlight.js", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_lunr.js", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/layouts/layout.erb", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/stylesheets/_api-endpoint.scss", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/stylesheets/_content.scss", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/stylesheets/_icon-font.scss", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/stylesheets/_label.scss", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/stylesheets/_lang-selector.scss", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/stylesheets/_normalize.scss", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/stylesheets/_rtl.scss", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/stylesheets/_toc.scss", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/stylesheets/_variables.scss", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/stylesheets/_warning.scss", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/stylesheets/print.css.scss", "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/stylesheets/screen.css.scss", "downloaded_repos/Automattic_woocommerce-payments/docs/typescript/README.md", "downloaded_repos/Automattic_woocommerce-payments/docs/typescript/declaring-types.md", "downloaded_repos/Automattic_woocommerce-payments/docs/typescript/general-conventions.md", "downloaded_repos/Automattic_woocommerce-payments/docs/typescript/interface-vs-type.md", "downloaded_repos/Automattic_woocommerce-payments/docs/typescript/react-components.md", "downloaded_repos/Automattic_woocommerce-payments/docs/typescript/utility-and-manipulation.md", "downloaded_repos/Automattic_woocommerce-payments/docs/unbundling-wordpress-components.md", "downloaded_repos/Automattic_woocommerce-payments/docs/version-support-policy.md", "downloaded_repos/Automattic_woocommerce-payments/i18n/currency-info.php", "downloaded_repos/Automattic_woocommerce-payments/i18n/locale-info.php", "downloaded_repos/Automattic_woocommerce-payments/includes/admin/class-wc-payments-admin-settings.php", "downloaded_repos/Automattic_woocommerce-payments/includes/admin/class-wc-payments-admin.php", "downloaded_repos/Automattic_woocommerce-payments/includes/admin/class-wc-payments-rest-controller.php", "downloaded_repos/Automattic_woocommerce-payments/includes/admin/class-wc-rest-payments-accounts-controller.php", "downloaded_repos/Automattic_woocommerce-payments/includes/admin/class-wc-rest-payments-authorizations-controller.php", "downloaded_repos/Automattic_woocommerce-payments/includes/admin/class-wc-rest-payments-capital-controller.php", "downloaded_repos/Automattic_woocommerce-payments/includes/admin/class-wc-rest-payments-charges-controller.php", "downloaded_repos/Automattic_woocommerce-payments/includes/admin/class-wc-rest-payments-connection-tokens-controller.php", "downloaded_repos/Automattic_woocommerce-payments/includes/admin/class-wc-rest-payments-customer-controller.php", "downloaded_repos/Automattic_woocommerce-payments/includes/admin/class-wc-rest-payments-deposits-controller.php", "downloaded_repos/Automattic_woocommerce-payments/includes/admin/class-wc-rest-payments-disputes-controller.php", "downloaded_repos/Automattic_woocommerce-payments/includes/admin/class-wc-rest-payments-documents-controller.php", "downloaded_repos/Automattic_woocommerce-payments/includes/admin/class-wc-rest-payments-files-controller.php", "downloaded_repos/Automattic_woocommerce-payments/includes/admin/class-wc-rest-payments-fraud-outcomes-controller.php", "downloaded_repos/Automattic_woocommerce-payments/includes/admin/class-wc-rest-payments-onboarding-controller.php", "downloaded_repos/Automattic_woocommerce-payments/includes/admin/class-wc-rest-payments-orders-controller.php", "downloaded_repos/Automattic_woocommerce-payments/includes/admin/class-wc-rest-payments-payment-intents-controller.php", "downloaded_repos/Automattic_woocommerce-payments/includes/admin/class-wc-rest-payments-reader-controller.php", "downloaded_repos/Automattic_woocommerce-payments/includes/admin/class-wc-rest-payments-refunds-controller.php", "downloaded_repos/Automattic_woocommerce-payments/includes/admin/class-wc-rest-payments-settings-controller.php", "downloaded_repos/Automattic_woocommerce-payments/includes/admin/class-wc-rest-payments-settings-option-controller.php", "downloaded_repos/Automattic_woocommerce-payments/includes/admin/class-wc-rest-payments-terminal-locations-controller.php", "downloaded_repos/Automattic_woocommerce-payments/includes/admin/class-wc-rest-payments-timeline-controller.php", "downloaded_repos/Automattic_woocommerce-payments/includes/admin/class-wc-rest-payments-tos-controller.php", "downloaded_repos/Automattic_woocommerce-payments/includes/admin/class-wc-rest-payments-transactions-controller.php", "downloaded_repos/Automattic_woocommerce-payments/includes/admin/class-wc-rest-payments-vat-controller.php", "downloaded_repos/Automattic_woocommerce-payments/includes/admin/class-wc-rest-payments-webhook-controller.php", "downloaded_repos/Automattic_woocommerce-payments/includes/admin/class-wc-rest-upe-flag-toggle-controller.php", "downloaded_repos/Automattic_woocommerce-payments/includes/admin/class-wc-rest-woopay-session-controller.php", "downloaded_repos/Automattic_woocommerce-payments/includes/admin/tasks/class-wc-payments-task-disputes.php", "downloaded_repos/Automattic_woocommerce-payments/includes/admin/tracks/class-tracker.php", "downloaded_repos/Automattic_woocommerce-payments/includes/admin/tracks/tracks-loader.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-compatibility-service.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-database-cache.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-duplicate-payment-prevention-service.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-duplicates-detection-service.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-experimental-abtest.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-logger-context.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-logger.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-payment-information.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-session-rate-limiter.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payment-gateway-wcpay.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payment-token-wcpay-link.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payment-token-wcpay-sepa.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments-account.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments-action-scheduler-service.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments-apple-pay-registration.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments-blocks-payment-method.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments-captured-event-note.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments-checkout.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments-customer-service.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments-db.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments-dependency-service.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments-explicit-price-formatter.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments-features.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments-file-service.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments-fraud-service.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments-incentives-service.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments-localization-service.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments-onboarding-service.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments-order-service.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments-order-success-page.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments-payment-method-messaging-element.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments-payment-request-session-handler.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments-payment-request-session.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments-redirect-service.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments-session-service.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments-settings-service.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments-status.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments-tasks.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments-token-service.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments-utils.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments-vat-redirect-service.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments-webhook-processing-service.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments-webhook-reliability-service.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments-woopay-button-handler.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments-woopay-direct-checkout.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments.php", "downloaded_repos/Automattic_woocommerce-payments/includes/class-woopay-tracker.php", "downloaded_repos/Automattic_woocommerce-payments/includes/compat/blocks/class-blocks-data-extractor.php", "downloaded_repos/Automattic_woocommerce-payments/includes/compat/multi-currency/class-wc-payments-currency-manager.php", "downloaded_repos/Automattic_woocommerce-payments/includes/compat/multi-currency/wc-payments-multi-currency.php", "downloaded_repos/Automattic_woocommerce-payments/includes/compat/subscriptions/class-wc-payments-email-failed-authentication-retry.php", "downloaded_repos/Automattic_woocommerce-payments/includes/compat/subscriptions/class-wc-payments-email-failed-renewal-authentication.php", "downloaded_repos/Automattic_woocommerce-payments/includes/compat/subscriptions/emails/failed-renewal-authentication-requested.php", "downloaded_repos/Automattic_woocommerce-payments/includes/compat/subscriptions/emails/failed-renewal-authentication.php", "downloaded_repos/Automattic_woocommerce-payments/includes/compat/subscriptions/emails/plain/failed-renewal-authentication-requested.php", "downloaded_repos/Automattic_woocommerce-payments/includes/compat/subscriptions/emails/plain/failed-renewal-authentication.php", "downloaded_repos/Automattic_woocommerce-payments/includes/compat/subscriptions/trait-wc-payment-gateway-wcpay-subscriptions.php", "downloaded_repos/Automattic_woocommerce-payments/includes/compat/subscriptions/trait-wc-payments-subscriptions-utilities.php", "downloaded_repos/Automattic_woocommerce-payments/includes/constants/class-base-constant.php", "downloaded_repos/Automattic_woocommerce-payments/includes/constants/class-country-code.php", "downloaded_repos/Automattic_woocommerce-payments/includes/constants/class-country-test-cards.php", "downloaded_repos/Automattic_woocommerce-payments/includes/constants/class-currency-code.php", "downloaded_repos/Automattic_woocommerce-payments/includes/constants/class-express-checkout-element-states.php", "downloaded_repos/Automattic_woocommerce-payments/includes/constants/class-express-checkout-hong-kong-states.php", "downloaded_repos/Automattic_woocommerce-payments/includes/constants/class-fraud-meta-box-type.php", "downloaded_repos/Automattic_woocommerce-payments/includes/constants/class-intent-status.php", "downloaded_repos/Automattic_woocommerce-payments/includes/constants/class-order-mode.php", "downloaded_repos/Automattic_woocommerce-payments/includes/constants/class-order-status.php", "downloaded_repos/Automattic_woocommerce-payments/includes/constants/class-payment-capture-type.php", "downloaded_repos/Automattic_woocommerce-payments/includes/constants/class-payment-initiated-by.php", "downloaded_repos/Automattic_woocommerce-payments/includes/constants/class-payment-intent-status.php", "downloaded_repos/Automattic_woocommerce-payments/includes/constants/class-payment-method.php", "downloaded_repos/Automattic_woocommerce-payments/includes/constants/class-payment-type.php", "downloaded_repos/Automattic_woocommerce-payments/includes/constants/class-refund-failure-reason.php", "downloaded_repos/Automattic_woocommerce-payments/includes/constants/class-refund-status.php", "downloaded_repos/Automattic_woocommerce-payments/includes/constants/class-track-events.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/CONTRIBUTING.md", "downloaded_repos/Automattic_woocommerce-payments/includes/core/README.md", "downloaded_repos/Automattic_woocommerce-payments/includes/core/class-mode.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/exceptions/server/request/class-extend-request-exception.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/exceptions/server/request/class-immutable-parameter-exception.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/exceptions/server/request/class-invalid-request-parameter-exception.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/exceptions/server/request/class-server-request-exception.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/exceptions/server/response/class-server-response-exception.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/CONTRIBUTING.md", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/README.md", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/class-request.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/class-response.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-add-account-tos-agreement.md", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-add-account-tos-agreement.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-cancel-intention.md", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-cancel-intention.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-capture-intention.md", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-capture-intention.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-create-and-confirm-intention.md", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-create-and-confirm-intention.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-create-and-confirm-setup-intention.md", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-create-and-confirm-setup-intention.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-create-intention.md", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-create-intention.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-create-setup-intention.md", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-create-setup-intention.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-generic.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-get-account-capital-link.md", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-get-account-capital-link.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-get-account-login-data.md", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-get-account-login-data.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-get-account.md", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-get-account.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-get-charge.md", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-get-charge.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-get-intention.md", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-get-intention.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-get-request.md", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-get-request.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-get-setup-intention.md", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-get-setup-intention.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-list-authorizations.md", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-list-authorizations.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-list-charge-refunds.md", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-list-charge-refunds.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-list-deposits.md", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-list-deposits.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-list-disputes.md", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-list-disputes.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-list-documents.md", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-list-documents.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-list-fraud-outcome-transactions.md", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-list-fraud-outcome-transactions.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-list-transactions.md", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-list-transactions.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-paginated.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-refund-charge.md", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-refund-charge.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-request-utils.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-update-account.md", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-update-account.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-update-intention.md", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-update-intention.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-woopay-create-and-confirm-intention.md", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-woopay-create-and-confirm-intention.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-woopay-create-and-confirm-setup-intention.md", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-woopay-create-and-confirm-setup-intention.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-woopay-create-intent.md", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/class-woopay-create-intent.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/trait-date-parameters.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/trait-intention.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/trait-level3.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/trait-order-info.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/server/request/trait-use-test-mode-only-when-test-mode-onboarding.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/service/README.md", "downloaded_repos/Automattic_woocommerce-payments/includes/core/service/class-wc-payments-customer-service-api.php", "downloaded_repos/Automattic_woocommerce-payments/includes/core/service/customer-service.md", "downloaded_repos/Automattic_woocommerce-payments/includes/emails/class-wc-payments-email-ipp-receipt.php", "downloaded_repos/Automattic_woocommerce-payments/includes/exceptions/class-add-payment-method-exception.php", "downloaded_repos/Automattic_woocommerce-payments/includes/exceptions/class-amount-too-large-exception.php", "downloaded_repos/Automattic_woocommerce-payments/includes/exceptions/class-amount-too-small-exception.php", "downloaded_repos/Automattic_woocommerce-payments/includes/exceptions/class-api-exception.php", "downloaded_repos/Automattic_woocommerce-payments/includes/exceptions/class-api-merchant-exception.php", "downloaded_repos/Automattic_woocommerce-payments/includes/exceptions/class-base-exception.php", "downloaded_repos/Automattic_woocommerce-payments/includes/exceptions/class-cannot-combine-currencies-exception.php", "downloaded_repos/Automattic_woocommerce-payments/includes/exceptions/class-connection-exception.php", "downloaded_repos/Automattic_woocommerce-payments/includes/exceptions/class-fraud-prevention-enabled-exception.php", "downloaded_repos/Automattic_woocommerce-payments/includes/exceptions/class-fraud-ruleset-exception.php", "downloaded_repos/Automattic_woocommerce-payments/includes/exceptions/class-intent-authentication-exception.php", "downloaded_repos/Automattic_woocommerce-payments/includes/exceptions/class-invalid-address-exception.php", "downloaded_repos/Automattic_woocommerce-payments/includes/exceptions/class-invalid-payment-method-exception.php", "downloaded_repos/Automattic_woocommerce-payments/includes/exceptions/class-invalid-phone-number-exception.php", "downloaded_repos/Automattic_woocommerce-payments/includes/exceptions/class-invalid-price-exception.php", "downloaded_repos/Automattic_woocommerce-payments/includes/exceptions/class-invalid-webhook-data-exception.php", "downloaded_repos/Automattic_woocommerce-payments/includes/exceptions/class-order-id-mismatch-exception.php", "downloaded_repos/Automattic_woocommerce-payments/includes/exceptions/class-order-not-found-exception.php", "downloaded_repos/Automattic_woocommerce-payments/includes/exceptions/class-process-payment-exception.php", "downloaded_repos/Automattic_woocommerce-payments/includes/exceptions/class-rate-limiter-enabled-exception.php", "downloaded_repos/Automattic_woocommerce-payments/includes/exceptions/class-rest-request-exception.php", "downloaded_repos/Automattic_woocommerce-payments/includes/exceptions/class-subscription-mode-mismatch-exception.php", "downloaded_repos/Automattic_woocommerce-payments/includes/express-checkout/class-wc-payments-express-checkout-ajax-handler.php", "downloaded_repos/Automattic_woocommerce-payments/includes/express-checkout/class-wc-payments-express-checkout-button-display-handler.php", "downloaded_repos/Automattic_woocommerce-payments/includes/express-checkout/class-wc-payments-express-checkout-button-handler.php", "downloaded_repos/Automattic_woocommerce-payments/includes/express-checkout/class-wc-payments-express-checkout-button-helper.php", "downloaded_repos/Automattic_woocommerce-payments/includes/fraud-prevention/class-buyer-fingerprinting-service.php", "downloaded_repos/Automattic_woocommerce-payments/includes/fraud-prevention/class-fraud-prevention-service.php", "downloaded_repos/Automattic_woocommerce-payments/includes/fraud-prevention/class-fraud-risk-tools.php", "downloaded_repos/Automattic_woocommerce-payments/includes/fraud-prevention/class-order-fraud-and-risk-meta-box.php", "downloaded_repos/Automattic_woocommerce-payments/includes/fraud-prevention/models/class-check.php", "downloaded_repos/Automattic_woocommerce-payments/includes/fraud-prevention/models/class-rule.php", "downloaded_repos/Automattic_woocommerce-payments/includes/fraud-prevention/wc-payments-fraud-risk-tools.php", "downloaded_repos/Automattic_woocommerce-payments/includes/in-person-payments/class-wc-payments-in-person-payments-receipts-service.php", "downloaded_repos/Automattic_woocommerce-payments/includes/in-person-payments/class-wc-payments-printed-receipt-sample-order.php", "downloaded_repos/Automattic_woocommerce-payments/includes/in-person-payments/templates/html-in-person-payment-receipt.php", "downloaded_repos/Automattic_woocommerce-payments/includes/inline-script-payloads/class-woo-payments-payment-method-definitions.php", "downloaded_repos/Automattic_woocommerce-payments/includes/inline-script-payloads/class-woo-payments-payment-methods-config.php", "downloaded_repos/Automattic_woocommerce-payments/includes/migrations/class-additional-payment-methods-admin-notes-removal.php", "downloaded_repos/Automattic_woocommerce-payments/includes/migrations/class-allowed-payment-request-button-sizes-update.php", "downloaded_repos/Automattic_woocommerce-payments/includes/migrations/class-allowed-payment-request-button-types-update.php", "downloaded_repos/Automattic_woocommerce-payments/includes/migrations/class-delete-active-woopay-webhook.php", "downloaded_repos/Automattic_woocommerce-payments/includes/migrations/class-erase-bnpl-announcement-meta.php", "downloaded_repos/Automattic_woocommerce-payments/includes/migrations/class-erase-deprecated-flags-and-options.php", "downloaded_repos/Automattic_woocommerce-payments/includes/migrations/class-gateway-settings-sync.php", "downloaded_repos/Automattic_woocommerce-payments/includes/migrations/class-link-woopay-mutual-exclusion-handler.php", "downloaded_repos/Automattic_woocommerce-payments/includes/migrations/class-manual-capture-payment-method-settings-update.php", "downloaded_repos/Automattic_woocommerce-payments/includes/migrations/class-payment-method-deprecation-settings-update.php", "downloaded_repos/Automattic_woocommerce-payments/includes/migrations/class-update-service-data-from-server.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/AdminNotices.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/Analytics.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/Compatibility/BaseCompatibility.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/Compatibility/WooCommerceBookings.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/Compatibility/WooCommerceDeposits.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/Compatibility/WooCommerceFedEx.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/Compatibility/WooCommerceNameYourPrice.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/Compatibility/WooCommercePointsAndRewards.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/Compatibility/WooCommercePreOrders.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/Compatibility/WooCommerceProductAddOns.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/Compatibility/WooCommerceSubscriptions.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/Compatibility/WooCommerceUPS.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/Compatibility.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/CountryFlags.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/Currency.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/CurrencySwitcherBlock.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/CurrencySwitcherWidget.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/Exceptions/InvalidCurrencyException.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/Exceptions/InvalidCurrencyRateException.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/FrontendCurrencies.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/FrontendPrices.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/Geolocation.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/Interfaces/MultiCurrencyAccountInterface.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/Interfaces/MultiCurrencyApiClientInterface.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/Interfaces/MultiCurrencyCacheInterface.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/Interfaces/MultiCurrencyLocalizationInterface.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/Interfaces/MultiCurrencySettingsInterface.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/Logger.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/MultiCurrency.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/Notes/NoteMultiCurrencyAvailable.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/README.md", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/RestController.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/Settings.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/SettingsOnboardCta.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/StorefrontIntegration.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/Tracking.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/UserSettings.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/Utils.php", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/analytics/index.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/blocks/currency-switcher.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/components/currency-delete-illustration/index.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/components/currency-delete-illustration/styles.scss", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/components/preview-modal/index.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/components/preview-modal/index.scss", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/context.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/data/action-types.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/data/actions.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/data/constants.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/data/hooks.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/data/index.ts", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/data/reducer.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/data/resolvers.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/data/selectors.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/data/store.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/index.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/interface/assets.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/interface/components.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/interface/data.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/interface/functions.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/settings/multi-currency/__tests__/__snapshots__/index.test.js.snap", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/settings/multi-currency/__tests__/index.test.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/settings/multi-currency/enabled-currencies-list/__tests__/__snapshots__/index.test.js.snap", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/settings/multi-currency/enabled-currencies-list/__tests__/index.test.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/settings/multi-currency/enabled-currencies-list/delete-button.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/settings/multi-currency/enabled-currencies-list/index.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/settings/multi-currency/enabled-currencies-list/list-item-placeholder.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/settings/multi-currency/enabled-currencies-list/list-item.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/settings/multi-currency/enabled-currencies-list/list.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/settings/multi-currency/enabled-currencies-list/modal-checkbox-list.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/settings/multi-currency/enabled-currencies-list/modal-checkbox.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/settings/multi-currency/enabled-currencies-list/modal.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/settings/multi-currency/enabled-currencies-list/style.scss", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/settings/multi-currency/index.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/settings/multi-currency/store-settings/__tests__/__snapshots__/index.test.js.snap", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/settings/multi-currency/store-settings/__tests__/index.test.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/settings/multi-currency/store-settings/index.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/settings/multi-currency/style.scss", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/settings/single-currency/__tests__/__snapshots__/index.test.js.snap", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/settings/single-currency/__tests__/index.test.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/settings/single-currency/constants.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/settings/single-currency/currency-preview.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/settings/single-currency/index.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/settings/single-currency/style.scss", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/setup/__tests__/index.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/setup/index.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/setup/tasks/__tests__/__snapshots__/multi-currency-setup.test.js.snap", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/setup/tasks/__tests__/multi-currency-setup.test.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/setup/tasks/add-currencies-task/__tests__/__snapshots__/index.test.js.snap", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/setup/tasks/add-currencies-task/__tests__/index.test.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/setup/tasks/add-currencies-task/__tests__/utils.test.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/setup/tasks/add-currencies-task/constants.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/setup/tasks/add-currencies-task/index.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/setup/tasks/add-currencies-task/index.scss", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/setup/tasks/add-currencies-task/utils.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/setup/tasks/multi-currency-setup.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/setup/tasks/multi-currency-setup.scss", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/setup/tasks/setup-complete-task/__tests__/index.test.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/setup/tasks/setup-complete-task/index.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/setup/tasks/setup-complete-task/index.scss", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/setup/tasks/store-settings-task/__tests__/__snapshots__/index.test.js.snap", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/setup/tasks/store-settings-task/__tests__/index.test.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/setup/tasks/store-settings-task/index.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/setup/tasks/store-settings-task/index.scss", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/utils/currency/index.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/utils/missing-currencies-message/__tests__/index.test.js", "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/utils/missing-currencies-message/index.ts", "downloaded_repos/Automattic_woocommerce-payments/includes/notes/class-wc-payments-notes-additional-payment-methods.php", "downloaded_repos/Automattic_woocommerce-payments/includes/notes/class-wc-payments-notes-instant-deposits-eligible.php", "downloaded_repos/Automattic_woocommerce-payments/includes/notes/class-wc-payments-notes-loan-approved.php", "downloaded_repos/Automattic_woocommerce-payments/includes/notes/class-wc-payments-notes-qualitative-feedback.php", "downloaded_repos/Automattic_woocommerce-payments/includes/notes/class-wc-payments-notes-set-https-for-checkout.php", "downloaded_repos/Automattic_woocommerce-payments/includes/notes/class-wc-payments-notes-set-up-stripelink.php", "downloaded_repos/Automattic_woocommerce-payments/includes/notes/class-wc-payments-notes-stripe-billing-deprecation.php", "downloaded_repos/Automattic_woocommerce-payments/includes/notes/class-wc-payments-remote-note-service.php", "downloaded_repos/Automattic_woocommerce-payments/includes/payment-methods/Configs/Constants/PaymentMethodCapability.php", "downloaded_repos/Automattic_woocommerce-payments/includes/payment-methods/Configs/Definitions/AlipayDefinition.php", "downloaded_repos/Automattic_woocommerce-payments/includes/payment-methods/Configs/Definitions/WechatPayDefinition.php", "downloaded_repos/Automattic_woocommerce-payments/includes/payment-methods/Configs/Interfaces/PaymentMethodDefinitionInterface.php", "downloaded_repos/Automattic_woocommerce-payments/includes/payment-methods/Configs/Registry/PaymentMethodDefinitionRegistry.php", "downloaded_repos/Automattic_woocommerce-payments/includes/payment-methods/Configs/Utils/PaymentMethodUtils.php", "downloaded_repos/Automattic_woocommerce-payments/includes/payment-methods/class-affirm-payment-method.php", "downloaded_repos/Automattic_woocommerce-payments/includes/payment-methods/class-afterpay-payment-method.php", "downloaded_repos/Automattic_woocommerce-payments/includes/payment-methods/class-bancontact-payment-method.php", "downloaded_repos/Automattic_woocommerce-payments/includes/payment-methods/class-becs-payment-method.php", "downloaded_repos/Automattic_woocommerce-payments/includes/payment-methods/class-cc-payment-gateway.php", "downloaded_repos/Automattic_woocommerce-payments/includes/payment-methods/class-cc-payment-method.php", "downloaded_repos/Automattic_woocommerce-payments/includes/payment-methods/class-eps-payment-method.php", "downloaded_repos/Automattic_woocommerce-payments/includes/payment-methods/class-giropay-payment-method.php", "downloaded_repos/Automattic_woocommerce-payments/includes/payment-methods/class-grabpay-payment-method.php", "downloaded_repos/Automattic_woocommerce-payments/includes/payment-methods/class-ideal-payment-method.php", "downloaded_repos/Automattic_woocommerce-payments/includes/payment-methods/class-klarna-payment-method.php", "downloaded_repos/Automattic_woocommerce-payments/includes/payment-methods/class-link-payment-method.php", "downloaded_repos/Automattic_woocommerce-payments/includes/payment-methods/class-multibanco-payment-method.php", "downloaded_repos/Automattic_woocommerce-payments/includes/payment-methods/class-p24-payment-method.php", "downloaded_repos/Automattic_woocommerce-payments/includes/payment-methods/class-sepa-payment-method.php", "downloaded_repos/Automattic_woocommerce-payments/includes/payment-methods/class-sofort-payment-method.php", "downloaded_repos/Automattic_woocommerce-payments/includes/payment-methods/class-upe-payment-method.php", "downloaded_repos/Automattic_woocommerce-payments/includes/reports/class-wc-rest-payments-reports-authorizations-controller.php", "downloaded_repos/Automattic_woocommerce-payments/includes/reports/class-wc-rest-payments-reports-transactions-controller.php", "downloaded_repos/Automattic_woocommerce-payments/includes/subscriptions/assets/css/plugin-page.css", "downloaded_repos/Automattic_woocommerce-payments/includes/subscriptions/assets/js/plugin-page.js", "downloaded_repos/Automattic_woocommerce-payments/includes/subscriptions/class-wc-payments-invoice-service.php", "downloaded_repos/Automattic_woocommerce-payments/includes/subscriptions/class-wc-payments-product-service.php", "downloaded_repos/Automattic_woocommerce-payments/includes/subscriptions/class-wc-payments-subscription-change-payment-method-handler.php", "downloaded_repos/Automattic_woocommerce-payments/includes/subscriptions/class-wc-payments-subscription-migration-log-handler.php", "downloaded_repos/Automattic_woocommerce-payments/includes/subscriptions/class-wc-payments-subscription-minimum-amount-handler.php", "downloaded_repos/Automattic_woocommerce-payments/includes/subscriptions/class-wc-payments-subscription-service.php", "downloaded_repos/Automattic_woocommerce-payments/includes/subscriptions/class-wc-payments-subscriptions-admin-notices.php", "downloaded_repos/Automattic_woocommerce-payments/includes/subscriptions/class-wc-payments-subscriptions-empty-state-manager.php", "downloaded_repos/Automattic_woocommerce-payments/includes/subscriptions/class-wc-payments-subscriptions-event-handler.php", "downloaded_repos/Automattic_woocommerce-payments/includes/subscriptions/class-wc-payments-subscriptions-migrator.php", "downloaded_repos/Automattic_woocommerce-payments/includes/subscriptions/class-wc-payments-subscriptions-onboarding-handler.php", "downloaded_repos/Automattic_woocommerce-payments/includes/subscriptions/class-wc-payments-subscriptions-plugin-notice-manager.php", "downloaded_repos/Automattic_woocommerce-payments/includes/subscriptions/class-wc-payments-subscriptions.php", "downloaded_repos/Automattic_woocommerce-payments/includes/subscriptions/templates/html-subscriptions-plugin-notice.php", "downloaded_repos/Automattic_woocommerce-payments/includes/subscriptions/templates/html-wcpay-deactivate-warning.php", "downloaded_repos/Automattic_woocommerce-payments/includes/subscriptions/templates/html-woo-payments-deactivate-warning.php", "downloaded_repos/Automattic_woocommerce-payments/includes/wc-payment-api/class-wc-payments-api-client.php", "downloaded_repos/Automattic_woocommerce-payments/includes/wc-payment-api/class-wc-payments-http-interface.php", "downloaded_repos/Automattic_woocommerce-payments/includes/wc-payment-api/class-wc-payments-http.php", "downloaded_repos/Automattic_woocommerce-payments/includes/wc-payment-api/models/class-wc-payments-api-abstract-intention.php", "downloaded_repos/Automattic_woocommerce-payments/includes/wc-payment-api/models/class-wc-payments-api-charge.php", "downloaded_repos/Automattic_woocommerce-payments/includes/wc-payment-api/models/class-wc-payments-api-payment-intention.php", "downloaded_repos/Automattic_woocommerce-payments/includes/wc-payment-api/models/class-wc-payments-api-setup-intention.php", "downloaded_repos/Automattic_woocommerce-payments/includes/woopay/class-woopay-adapted-extensions.php", "downloaded_repos/Automattic_woocommerce-payments/includes/woopay/class-woopay-order-status-sync.php", "downloaded_repos/Automattic_woocommerce-payments/includes/woopay/class-woopay-scheduler.php", "downloaded_repos/Automattic_woocommerce-payments/includes/woopay/class-woopay-session.php", "downloaded_repos/Automattic_woocommerce-payments/includes/woopay/class-woopay-store-api-session-handler.php", "downloaded_repos/Automattic_woocommerce-payments/includes/woopay/class-woopay-store-api-token.php", "downloaded_repos/Automattic_woocommerce-payments/includes/woopay/class-woopay-utilities.php", "downloaded_repos/Automattic_woocommerce-payments/includes/woopay/services/class-checkout-service.php", "downloaded_repos/Automattic_woocommerce-payments/includes/woopay-user/class-woopay-save-user.php", "downloaded_repos/Automattic_woocommerce-payments/jsconfig.json", "downloaded_repos/Automattic_woocommerce-payments/languages/README.md", "downloaded_repos/Automattic_woocommerce-payments/lib/README.md", "downloaded_repos/Automattic_woocommerce-payments/lib/composer.json", "downloaded_repos/Automattic_woocommerce-payments/lib/composer.lock", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/Argument/ArgumentInterface.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/Argument/ArgumentResolverInterface.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/Argument/ArgumentResolverTrait.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/Argument/DefaultValueArgument.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/Argument/DefaultValueInterface.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/Argument/Literal/ArrayArgument.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/Argument/Literal/BooleanArgument.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/Argument/Literal/CallableArgument.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/Argument/Literal/FloatArgument.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/Argument/Literal/IntegerArgument.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/Argument/Literal/ObjectArgument.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/Argument/Literal/StringArgument.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/Argument/LiteralArgument.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/Argument/LiteralArgumentInterface.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/Argument/ResolvableArgument.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/Argument/ResolvableArgumentInterface.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/Container.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/ContainerAwareInterface.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/ContainerAwareTrait.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/Definition/Definition.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/Definition/DefinitionAggregate.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/Definition/DefinitionAggregateInterface.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/Definition/DefinitionInterface.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/DefinitionContainerInterface.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/Exception/ContainerException.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/Exception/NotFoundException.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/Inflector/Inflector.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/Inflector/InflectorAggregate.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/Inflector/InflectorAggregateInterface.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/Inflector/InflectorInterface.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/ReflectionContainer.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/ServiceProvider/AbstractServiceProvider.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/ServiceProvider/BootableServiceProviderInterface.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/ServiceProvider/ServiceProviderAggregate.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/ServiceProvider/ServiceProviderAggregateInterface.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/League/Container/ServiceProvider/ServiceProviderInterface.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/Psr/Container/ContainerExceptionInterface.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/Psr/Container/ContainerInterface.php", "downloaded_repos/Automattic_woocommerce-payments/lib/packages/Psr/Container/NotFoundExceptionInterface.php", "downloaded_repos/Automattic_woocommerce-payments/license.txt", "downloaded_repos/Automattic_woocommerce-payments/lint-staged.config.js", "downloaded_repos/Automattic_woocommerce-payments/package.json", "downloaded_repos/Automattic_woocommerce-payments/phpcs-compat.xml.dist", "downloaded_repos/Automattic_woocommerce-payments/phpcs.xml.dist", "downloaded_repos/Automattic_woocommerce-payments/phpunit-includes.xml.dist", "downloaded_repos/Automattic_woocommerce-payments/phpunit-src.xml.dist", "downloaded_repos/Automattic_woocommerce-payments/phpunit.xml.dist", "downloaded_repos/Automattic_woocommerce-payments/psalm-baseline.xml", "downloaded_repos/Automattic_woocommerce-payments/psalm-loader.php", "downloaded_repos/Automattic_woocommerce-payments/psalm.xml", "downloaded_repos/Automattic_woocommerce-payments/readme.txt", "downloaded_repos/Automattic_woocommerce-payments/renovate.json", "downloaded_repos/Automattic_woocommerce-payments/src/Container.php", "downloaded_repos/Automattic_woocommerce-payments/src/Internal/DependencyManagement/AbstractServiceProvider.php", "downloaded_repos/Automattic_woocommerce-payments/src/Internal/DependencyManagement/DelegateContainer/LegacyContainer.php", "downloaded_repos/Automattic_woocommerce-payments/src/Internal/DependencyManagement/DelegateContainer/REAMDE.md", "downloaded_repos/Automattic_woocommerce-payments/src/Internal/DependencyManagement/DelegateContainer/WooContainer.php", "downloaded_repos/Automattic_woocommerce-payments/src/Internal/DependencyManagement/ExtendedContainer.php", "downloaded_repos/Automattic_woocommerce-payments/src/Internal/DependencyManagement/ServiceProvider/GenericServiceProvider.php", "downloaded_repos/Automattic_woocommerce-payments/src/Internal/DependencyManagement/ServiceProvider/PaymentsServiceProvider.php", "downloaded_repos/Automattic_woocommerce-payments/src/Internal/DependencyManagement/ServiceProvider/ProxiesServiceProvider.php", "downloaded_repos/Automattic_woocommerce-payments/src/Internal/Logger.php", "downloaded_repos/Automattic_woocommerce-payments/src/Internal/LoggerContext.php", "downloaded_repos/Automattic_woocommerce-payments/src/Internal/PluginManagement/TranslationsLoader.php", "downloaded_repos/Automattic_woocommerce-payments/src/Internal/Proxy/HooksProxy.php", "downloaded_repos/Automattic_woocommerce-payments/src/Internal/Proxy/LegacyProxy.php", "downloaded_repos/Automattic_woocommerce-payments/src/Internal/Proxy/ProxyException.php", "downloaded_repos/Automattic_woocommerce-payments/src/Internal/Service/DuplicatePaymentPreventionService.php", "downloaded_repos/Automattic_woocommerce-payments/src/Internal/Service/ExampleService.php", "downloaded_repos/Automattic_woocommerce-payments/src/Internal/Service/ExampleServiceWithDependencies.php", "downloaded_repos/Automattic_woocommerce-payments/src/Internal/Service/Level3Service.php", "downloaded_repos/Automattic_woocommerce-payments/src/Internal/Service/OrderService.php", "downloaded_repos/Automattic_woocommerce-payments/src/Internal/Service/SessionService.php", "downloaded_repos/Automattic_woocommerce-payments/src/README.md", "downloaded_repos/Automattic_woocommerce-payments/src/index.php", "downloaded_repos/Automattic_woocommerce-payments/src/wcpay-get-container.php", "downloaded_repos/Automattic_woocommerce-payments/tasks/release.js", "downloaded_repos/Automattic_woocommerce-payments/templates/emails/customer-ipp-receipt.php", "downloaded_repos/Automattic_woocommerce-payments/templates/emails/email-ipp-receipt-compliance-details.php", "downloaded_repos/Automattic_woocommerce-payments/templates/emails/email-ipp-receipt-store-details.php", "downloaded_repos/Automattic_woocommerce-payments/templates/emails/plain/customer-ipp-receipt.php", "downloaded_repos/Automattic_woocommerce-payments/templates/emails/plain/email-ipp-receipt-compliance-details.php", "downloaded_repos/Automattic_woocommerce-payments/templates/emails/plain/email-ipp-receipt-store-details.php", "downloaded_repos/Automattic_woocommerce-payments/templates/plugins-page/plugins-page-wrapper.php", "downloaded_repos/Automattic_woocommerce-payments/tsconfig.json", "downloaded_repos/Automattic_woocommerce-payments/webpack/development.js", "downloaded_repos/Automattic_woocommerce-payments/webpack/hmr.js", "downloaded_repos/Automattic_woocommerce-payments/webpack/production.js", "downloaded_repos/Automattic_woocommerce-payments/webpack/shared.js", "downloaded_repos/Automattic_woocommerce-payments/webpack/webpack-rtl-plugin.js", "downloaded_repos/Automattic_woocommerce-payments/webpack.config.js", "downloaded_repos/Automattic_woocommerce-payments/woocommerce-payments.php", "downloaded_repos/Automattic_woocommerce-payments/wordpress-org-assets/banner-1544x500.png", "downloaded_repos/Automattic_woocommerce-payments/wordpress-org-assets/banner-772x250.png", "downloaded_repos/Automattic_woocommerce-payments/wordpress-org-assets/icon-128x128.png", "downloaded_repos/Automattic_woocommerce-payments/wordpress-org-assets/icon-256x256.png", "downloaded_repos/Automattic_woocommerce-payments/wordpress-org-assets/screenshot-1.jpg", "downloaded_repos/Automattic_woocommerce-payments/wordpress-org-assets/screenshot-2.jpg", "downloaded_repos/Automattic_woocommerce-payments/wordpress-org-assets/screenshot-3.jpg", "downloaded_repos/Automattic_woocommerce-payments/wordpress-org-assets/screenshot-4.jpg"], "skipped": [{"path": "downloaded_repos/Automattic_woocommerce-payments/.github/actions/build/action.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/.github/actions/create-tag/action.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/.github/actions/process-changelog/action.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/.github/actions/version-check/action.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/i18n-weekly-release.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/release-changelog.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/.github/workflows/release-code-freeze.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/bin/phpunit6", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/capital/test/__snapshots__/index.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/capital/test/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/card-readers/list/test/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/card-readers/test/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/checkout/api/test/index.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/checkout/blocks/test/hooks.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/checkout/blocks/test/payment-method-logos.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/checkout/blocks/test/payment-processor.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/checkout/classic/test/3ds-flow-handling.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/checkout/classic/test/payment-processing.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/checkout/stripe-link/test/index.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/checkout/upe-styles/test/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/checkout/upe-styles/test/utils.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/checkout/utils/test/upe.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/checkout/woopay/connect/tests/woopay-connect-iframe.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/checkout/woopay/direct-checkout/test/index.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/checkout/woopay/direct-checkout/test/woopay-direct-checkout.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/checkout/woopay/express-button/test/express-checkout-iframe.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/checkout/woopay/express-button/test/index.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/checkout/woopay/express-button/test/woopay-express-checkout-button.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/checkout/woopay/test/utils.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/accordion/test/__snapshots__/accordion.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/accordion/test/__snapshots__/body.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/accordion/test/__snapshots__/row.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/accordion/test/accordion.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/accordion/test/body.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/accordion/test/row.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/account-balances/test/__snapshots__/index.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/account-balances/test/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/account-status/account-fees/test/__snapshots__/index.js.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/account-status/account-fees/test/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/account-status/test/__snapshots__/index.js.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/account-status/test/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/active-loan-summary/test/__snapshots__/index.js.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/active-loan-summary/test/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/amount-input/test/__snapshots__/index.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/amount-input/test/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/banner-notice/tests/__snapshots__/index.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/banner-notice/tests/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/cancel-authorization-button/test/__snapshots__/index.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/cancel-authorization-button/test/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/capture-authorization-button/test/__snapshots__/index.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/capture-authorization-button/test/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/card-notice/test/__snapshots__/index.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/card-notice/test/index.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/chip/test/__snapshots__/index.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/chip/test/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/copy-button/test/__snapshots__/index.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/copy-button/test/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/custom-select-control/test/__snapshots__/index.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/custom-select-control/test/index.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/customer-link/test/__snapshots__/index.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/customer-link/test/index.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/deposit-status-chip/test/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/deposits-overview/test/__snapshots__/index.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/deposits-overview/test/index.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/deposits-status/test/__snapshots__/index.js.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/deposits-status/test/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/details-link/test/__snapshots__/index.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/details-link/test/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/dispute-status-chip/test/__snapshots__/index.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/dispute-status-chip/test/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/disputed-order-notice/test/__snapshots__/index.test.js.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/disputed-order-notice/test/index.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/download-button/test/__snapshots__/index.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/download-button/test/index.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/duplicate-notice/tests/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/file-upload/test/__snapshots__/index.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/file-upload/test/index.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/form/test/fields.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/grouped-select-control/test/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/horizontal-list/test/__snapshots__/index.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/horizontal-list/test/index.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/inline-label-select/test/__snapshots__/index.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/inline-label-select/test/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/inline-notice/tests/__snapshots__/index.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/inline-notice/tests/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/loadable/test/__snapshots__/index.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/loadable/test/index.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/order-link/test/__snapshots__/index.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/order-link/test/index.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/paragraphs/test/__snapshots__/index.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/paragraphs/test/index.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/payment-confirm-illustration/test/__snapshots__/index.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/payment-confirm-illustration/test/index.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/payment-delete-illustration/test/__snapshots__/index.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/payment-delete-illustration/test/index.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/payments-status/test/__snapshots__/index.js.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/payments-status/test/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/phone-number-control/test/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/pill/test/__snapshots__/index.test.js.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/pill/test/index.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/progress-bar/test/__snapshots__/index.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/progress-bar/test/index.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/risk-level/test/__snapshots__/index.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/risk-level/test/index.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/sandbox-mode-switch-to-live-notice/modal/test/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/stepper/test/stepper-panel.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/stepper/test/test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/test-mode-notice/test/__snapshots__/index.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/test-mode-notice/test/index.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/tip-box/test/__snapshots__/index.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/tip-box/test/index.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/tooltip/test/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/tooltip/test/tooltip-base.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/welcome/test/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/woopay/save-user/test/checkout-page-save-user.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/components/woopay/test/index.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/connect-account-page/modal/test/__snapshots__/index.js.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/connect-account-page/modal/test/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/connect-account-page/test/__snapshots__/index.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/connect-account-page/test/__snapshots__/info-notice-modal.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/connect-account-page/test/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/connect-account-page/test/info-notice-modal.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/authorizations/test/actions.test.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/authorizations/test/authorizations-summary.fixture.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/authorizations/test/authorizations.fixture.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/authorizations/test/reducer.test.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/authorizations/test/resolvers.test.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/authorizations/test/selectors.test.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/capital/test/reducer.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/capital/test/resolvers.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/charges/test/hooks.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/charges/test/reducer.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/charges/test/resolvers.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/charges/test/selectors.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/deposits/test/overviews.fixture.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/deposits/test/reducer.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/deposits/test/resolvers.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/deposits/test/selectors.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/disputes/test/actions.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/disputes/test/reducer.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/disputes/test/resolvers.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/disputes/test/selectors.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/documents/test/reducer.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/documents/test/resolvers.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/documents/test/selectors.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/payment-intents/test/hooks.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/payment-intents/test/resolvers.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/payment-intents/test/selectors.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/settings/test/actions.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/settings/test/hooks.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/settings/test/reducer.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/settings/test/resolvers.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/settings/test/selectors.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/timeline/test/reducer.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/timeline/test/selectors.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/transactions/test/reducer.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/transactions/test/resolvers.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/data/transactions/test/selectors.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/deposits/declarations.d.ts", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/deposits/details/test/__snapshots__/index.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/deposits/details/test/index.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/deposits/filters/test/__snapshots__/index.js.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/deposits/filters/test/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/deposits/instant-payouts/test/__snapshots__/index.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/deposits/instant-payouts/test/index.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/deposits/list/test/__snapshots__/index.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/deposits/list/test/index.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/deposits/utils/test/index.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/disable-confirmation-modal/test/disable-confirmation-modal.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/disputes/filters/test/__snapshots__/index.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/disputes/filters/test/index.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/disputes/info/test/__snapshots__/index.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/disputes/info/test/index.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/disputes/new-evidence/test/confirmation-screen.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/disputes/new-evidence/test/cover-letter-generator.test.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/disputes/new-evidence/test/cover-letter.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/disputes/new-evidence/test/customer-details.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/disputes/new-evidence/test/duplicate-status.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/disputes/new-evidence/test/file-upload-control.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/disputes/new-evidence/test/product-details.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/disputes/new-evidence/test/recommended-document-fields.test.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/disputes/new-evidence/test/recommended-documents.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/disputes/new-evidence/test/refund-status.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/disputes/new-evidence/test/shipping-details.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/disputes/new-evidence/test/utils.test.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/disputes/test/__snapshots__/index.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/disputes/test/index.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/disputes/test/utils.test.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/documents/filters/test/__snapshots__/index.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/documents/filters/test/index.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/documents/list/test/__snapshots__/index.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/documents/list/test/index.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/embedded-components/test/index.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/hooks/test/use-report-export.test.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/merchant-feedback-prompt/test/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/onboarding/steps/test/business-details.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/onboarding/steps/test/embedded-kyc.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/onboarding/steps/test/loading.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/onboarding/test/context.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/onboarding/test/form.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/onboarding/test/utils.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/onboarding/test/validation.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/overview/inbox-notifications/test/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/overview/modal/connection-success/test/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/overview/modal/progressive-onboarding-eligibility/test/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/overview/modal/reset-account/test/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/overview/modal/update-business-details/test/__snapshots__/index.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/overview/modal/update-business-details/test/index.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/overview/task-list/tasks/test/po-tasks.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/overview/task-list/test/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/overview/task-list/test/tasks.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/overview/test/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/order-details/test/__snapshots__/index.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/order-details/test/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/payment-method/card-present/test/__snapshots__/index.js.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/payment-method/card-present/test/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/readers/test/__snapshots__/index.js.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/readers/test/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/summary/missing-order-notice/test/__snapshots__/index.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/summary/missing-order-notice/test/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/summary/refund-modal/test/__snapshots__/index.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/summary/refund-modal/test/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/test/__snapshots__/index.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/test/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/timeline/test/__snapshots__/index.js.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/timeline/test/__snapshots__/map-events.js.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/timeline/test/captured-event-strings.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/timeline/test/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/timeline/test/map-events.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/timeline/test/tax-string.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/transaction-breakdown/fees-breakdown/tests/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/transaction-breakdown/tests/hooks.test.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/transaction-breakdown/tests/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/transaction-breakdown/tests/utils.test.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/utils/test/tax-descriptions.test.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/advanced-settings/test/debug-mode.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/advanced-settings/test/index.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/advanced-settings/test/multi-currency-toggle.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/deposits/test/index.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/cards/test/__snapshots__/address-mismatch.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/cards/test/__snapshots__/avs-mismatch.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/cards/test/__snapshots__/cvc-verification.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/cards/test/__snapshots__/international-ip-address.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/cards/test/__snapshots__/ip-address-mismatch.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/cards/test/__snapshots__/order-items-threshold.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/cards/test/__snapshots__/purchase-price-threshold.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/cards/test/address-mismatch.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/cards/test/avs-mismatch.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/cards/test/cvc-verification.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/cards/test/international-ip-address.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/cards/test/ip-address-mismatch.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/cards/test/order-items-threshold.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/cards/test/purchase-price-threshold.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/test/__snapshots__/allow-countries-notice.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/test/__snapshots__/index.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/test/__snapshots__/rule-card-notice.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/test/__snapshots__/rule-card.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/test/__snapshots__/rule-description.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/test/__snapshots__/rule-toggle.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/test/allow-countries-notice.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/test/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/test/rule-card-notice.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/test/rule-card.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/test/rule-description.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/test/rule-toggle.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/advanced-settings/test/utils.test.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/components/fp-help-text/test/__snapshots__/index.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/components/fp-help-text/test/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/components/protection-level-modal-notice/test/__snapshots__/index.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/components/protection-level-modal-notice/test/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/components/protection-levels/test/__snapshots__/index.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/fraud-protection/components/protection-levels/test/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/general-settings/test/general-settings.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/general-settings/test/test-mode-confirm-modal.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/payment-method-icon/test/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/payment-methods-list/delete-modal.tsx", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/phone-input/test/phone-input.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/save-settings-section/test/index.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/settings-layout/test/index.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/settings-manager/test/index.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/support-email-input/test/support-email-input.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/support-phone-input/test/support-phone-input.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/settings/transactions/test/index.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/transactions/blocked/test/__snapshots__/columns.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/transactions/blocked/test/columns.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/transactions/filters/test/__snapshots__/index.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/transactions/filters/test/index.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/transactions/list/test/__snapshots__/converted-amount.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/transactions/list/test/__snapshots__/deposit.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/transactions/list/test/__snapshots__/index.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/transactions/list/test/converted-amount.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/transactions/list/test/deposit.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/transactions/list/test/index.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/transactions/risk-review/test/__snapshots__/columns.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/transactions/risk-review/test/columns.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/transactions/test/index.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/transactions/uncaptured/test/__snapshots__/index.test.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/transactions/uncaptured/test/index.test.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/transactions/utils/test/getTransactionPaymentMethodTitle.test.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/utils/charge/test/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/utils/test/__snapshots__/account-fees.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/utils/test/account-fees.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/utils/test/data.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/utils/test/date-time.test.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/utils/test/format-list-of-items.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/utils/test/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/utils/test/sanitize.test.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/utils/test/update-woocommerce-user-meta.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/vat/form/test/index.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/vat/form-modal/test/__snapshots__/index.tsx.snap", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/client/vat/form-modal/test/index.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_imagesloaded.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/utils/currency/test/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/package-lock.json", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/WCPAY_UnitTestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/WP_UnitTestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/config/default.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/config/users.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/docker-compose.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/env/cleanup.sh", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/env/default.env", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/env/docker-compose.yml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/env/down.sh", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/env/setup.sh", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/env/shared.sh", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/env/up.sh", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/env/wc-subscription-products-atomic.csv", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/env/wc-subscription-products.xml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/env/wordpress-xdebug/Dockerfile", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/playwright.config.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/playwright.performance.config.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/reporters/slack-reporter.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/__snapshots__/wcpay/merchant/merchant-admin-analytics.spec.ts/Admin-order-analytics-should-load-without-any-errors-1.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/__snapshots__/wcpay/merchant/merchant-admin-deposits.spec.ts/Merchant-deposits-Select-deposits-list-advanced-filters-1.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/__snapshots__/wcpay/merchant/merchant-admin-transactions.spec.ts/Admin-transactions-page-should-load-without-errors-1.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/__snapshots__/wcpay/merchant/merchant-disputes-view-details-via-order-notice.spec.ts/Disputes-View-dispute-details-via-disputed-o-f9e9d-ils-when-disputed-order-notice-button-clicked-1.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/__snapshots__/wcpay/merchant/multi-currency-on-boarding.spec.ts/Multi-currency-on-boarding-Currency-selection--7b0d9-submit-button-when-no-currencies-are-selected-1.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/__snapshots__/wcpay/merchant/multi-currency-on-boarding.spec.ts/Multi-currency-on-boarding-Geolocation-feature-83665-tch-by-geolocation-correctly-with-USD-and-GBP-1.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/__snapshots__/wcpay/merchant/multi-currency-on-boarding.spec.ts/Multi-currency-on-boarding-Geolocation-feature-d8568-tch-by-geolocation-correctly-with-USD-and-GBP-2.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/__snapshots__/wcpay/merchant/multi-currency.spec.ts/Multi-currency-page-load-without-any-errors-1.png", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/auth.setup.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/basic.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/performance/payment-methods.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/subscriptions/merchant/merchant-subscriptions-renew-action-scheduler.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/subscriptions/merchant/merchant-subscriptions-renew.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/subscriptions/merchant/merchant-subscriptions-settings.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/subscriptions/shopper/shopper-subscriptions-manage-payments.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/subscriptions/shopper/shopper-subscriptions-purchase-free-trial.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/subscriptions/shopper/shopper-subscriptions-purchase-multiple-subscriptions.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/subscriptions/shopper/shopper-subscriptions-purchase-no-signup-fee.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/subscriptions/shopper/shopper-subscriptions-purchase-sign-up-fee.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/merchant/merchant-admin-account-balance.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/merchant/merchant-admin-analytics.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/merchant/merchant-admin-deposits.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/merchant/merchant-admin-disputes.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/merchant/merchant-admin-transactions.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/merchant/merchant-disputes-respond.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/merchant/merchant-disputes-view-details-via-order-notice.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/merchant/merchant-multi-currency-widget.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/merchant/merchant-orders-full-refund.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/merchant/merchant-orders-manual-capture.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/merchant/merchant-orders-partial-refund.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/merchant/merchant-orders-refund-failures.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/merchant/merchant-orders-status-change.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/merchant/merchant-payment-settings-manual-capture.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/merchant/merchant-progressive-onboarding.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/merchant/multi-currency-on-boarding.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/merchant/multi-currency-setup.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/merchant/multi-currency.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/merchant/non-admin-wp-admin-access.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/merchant/woopay-setup.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/shopper/alipay-checkout-purchase.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/shopper/klarna-checkout-purchase.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/shopper/multi-currency-checkout.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/shopper/shopper-bnpls-checkout.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/shopper/shopper-checkout-cart-coupon.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/shopper/shopper-checkout-failures.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/shopper/shopper-checkout-purchase-site-editor.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/shopper/shopper-checkout-purchase-with-upe-methods.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/shopper/shopper-checkout-purchase.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/shopper/shopper-checkout-save-card-and-purchase.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/shopper/shopper-multi-currency-widget.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/shopper/shopper-myaccount-payment-methods-add-fail.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/shopper/shopper-myaccount-renew-subscription.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/shopper/shopper-myaccount-saved-cards.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/shopper/shopper-pay-for-order.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/shopper/shopper-wc-blocks-checkout-failures.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/shopper/shopper-wc-blocks-checkout-purchase.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/specs/wcpay/shopper/shopper-wc-blocks-saved-card-checkout-and-usage.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/test-e2e-performance.sh", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/test-e2e-ui.sh", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/test-e2e.sh", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/utils/constants.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/utils/devtools.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/utils/helpers.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/utils/merchant-navigation.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/utils/merchant.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/utils/performance.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/utils/rest-api.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/utils/shopper-navigation.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/utils/shopper.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/e2e/utils/slack.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/fixtures/captured-payments/discount.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/fixtures/captured-payments/foreign-card.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/fixtures/captured-payments/fx-decimal.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/fixtures/captured-payments/fx-foreign-card.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/fixtures/captured-payments/fx-partial-capture.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/fixtures/captured-payments/fx-same-currency-symbol.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/fixtures/captured-payments/fx-with-capped-fee.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/fixtures/captured-payments/fx.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/fixtures/captured-payments/jpy-payment.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/fixtures/captured-payments/only-base-fee.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/fixtures/captured-payments/partial-capture.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/fixtures/captured-payments/subscription.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/fixtures/captured-payments/tax-fx.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/fixtures/captured-payments/tax.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/js/fileMock.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/js/jest-extensions-setup.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/js/jest-global-setup.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/js/jest-msw-setup.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/js/jest-test-file-setup.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/js/jest.config.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/js/utilities/msw-server.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/js/utilities/timezone.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/js/woocommerce-blocks-registry.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/phpunit-patches/globals-usage-compatibility.diff", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/qit/README.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/qit/common.sh", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/qit/config/default.env", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/qit/malware.sh", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/qit/phpstan.sh", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/qit/security.sh", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/admin/tasks/test-class-wc-payments-task-disputes.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/admin/test-class-wc-payments-admin-settings.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/admin/test-class-wc-payments-admin.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/admin/test-class-wc-rest-payments-accounts-controller.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/admin/test-class-wc-rest-payments-customer-controller.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/admin/test-class-wc-rest-payments-files-controller.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/admin/test-class-wc-rest-payments-onboarding-controller.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/admin/test-class-wc-rest-payments-orders-controller.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/admin/test-class-wc-rest-payments-payment-intents-controller.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/admin/test-class-wc-rest-payments-readers-controller.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/admin/test-class-wc-rest-payments-refunds-controller.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/admin/test-class-wc-rest-payments-settings-controller.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/admin/test-class-wc-rest-payments-settings-option-controller.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/admin/test-class-wc-rest-payments-terminal-locations-controller.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/admin/test-class-wc-rest-payments-tos-controller.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/admin/test-class-wc-rest-payments-webhook.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/bootstrap.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/contants/test-class-country-code.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/contants/test-class-refund-failure-reason.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/core/server/request/test-class-add-account-tos-agreement.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/core/server/request/test-class-core-cancel-intention-request.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/core/server/request/test-class-core-capture-intention-request.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/core/server/request/test-class-core-create-and-confirm-intention-request.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/core/server/request/test-class-core-create-and-confirm-setup-intention-request.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/core/server/request/test-class-core-create-intention-request.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/core/server/request/test-class-core-create-setup-intention-request.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/core/server/request/test-class-core-get-account-capital-link.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/core/server/request/test-class-core-get-account-login-data.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/core/server/request/test-class-core-get-account-request.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/core/server/request/test-class-core-get-charge-request.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/core/server/request/test-class-core-get-intention-request.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/core/server/request/test-class-core-get-setup-intention-request.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/core/server/request/test-class-core-list-charge-refunds-request.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/core/server/request/test-class-core-paginated-request.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/core/server/request/test-class-core-refund-charge-request.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/core/server/request/test-class-core-request-generic.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/core/server/request/test-class-core-request.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/core/server/request/test-class-core-update-account-request.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/core/server/request/test-class-core-update-intention-request.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/core/server/request/test-class-get-request.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/core/server/request/test-class-list-authorizations-request.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/core/server/request/test-class-list-deposits-request.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/core/server/request/test-class-list-disputes-request.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/core/server/request/test-class-list-documents-request.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/core/server/request/test-class-list-fraud-outcome-transactions-request.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/core/server/request/test-class-list-transactions-request.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/core/server/request/test-trait-order-info.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/core/service/test-class-wc-payments-customer-service-api.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/core/test-class-mode.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/duplicate-detection/class-test-gateway.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/duplicate-detection/test-class-duplicates-detection-service.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/express-checkout/test-class-wc-payments-express-checkout-ajax-handler.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/express-checkout/test-class-wc-payments-express-checkout-button-display-handler.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/express-checkout/test-class-wc-payments-express-checkout-button-handler.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/express-checkout/test-class-wc-payments-express-checkout-button-helper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/fraud-prevention/models/test-class-check.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/fraud-prevention/models/test-class-rule.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/fraud-prevention/test-class-buyer-fingerprinting-service.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/fraud-prevention/test-class-fraud-prevention-service.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/fraud-prevention/test-class-fraud-risk-tools.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/fraud-prevention/test-class-order-fraud-and-risk-meta-box.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/helpers/class-phpunit-utils.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/helpers/class-wc-blocks-rest-api-registration-preventer.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/helpers/class-wc-helper-deposit-product-manager.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/helpers/class-wc-helper-gift-cards.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/helpers/class-wc-helper-intention.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/helpers/class-wc-helper-name-your-price.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/helpers/class-wc-helper-order.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/helpers/class-wc-helper-points-and-rewards.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/helpers/class-wc-helper-product-add-ons-helper.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/helpers/class-wc-helper-product-add-ons.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/helpers/class-wc-helper-product.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/helpers/class-wc-helper-shipping.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/helpers/class-wc-helper-site-currency.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/helpers/class-wc-helper-subscription.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/helpers/class-wc-helper-subscriptions-cart.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/helpers/class-wc-helper-subscriptions-product.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/helpers/class-wc-helper-subscriptions-synchroniser.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/helpers/class-wc-helper-subscriptions.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/helpers/class-wc-helper-token.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/helpers/class-wc-mock-wc-data-store.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/helpers/class-wc-mock-wc-data.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/helpers/class-wcs-helper-background-repairer.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/in-person-payments/test-class-wc-payments-in-person-payments-receipts-service.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/migrations/test-class-allowed-payment-request-button-types-update.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/migrations/test-class-erase-bnpl-announcement-meta.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/migrations/test-class-erase-deprecated-flags-and-options.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/migrations/test-class-update-service-data-from-server.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/multi-currency/compatibility/test-class-woocommerce-bookings.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/multi-currency/compatibility/test-class-woocommerce-deposits.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/multi-currency/compatibility/test-class-woocommerce-fedex.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/multi-currency/compatibility/test-class-woocommerce-name-your-price.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/multi-currency/compatibility/test-class-woocommerce-points-and-rewards.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/multi-currency/compatibility/test-class-woocommerce-pre-orders.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/multi-currency/compatibility/test-class-woocommerce-product-add-ons.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/multi-currency/compatibility/test-class-woocommerce-subscriptions.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/multi-currency/compatibility/test-class-woocommerce-ups.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/multi-currency/notes/test-class-note-multi-currency-available-test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/multi-currency/test-class-admin-notices.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/multi-currency/test-class-analytics.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/multi-currency/test-class-compatibility.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/multi-currency/test-class-country-flags.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/multi-currency/test-class-currency-switcher-block.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/multi-currency/test-class-currency-switcher-widget.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/multi-currency/test-class-currency.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/multi-currency/test-class-frontend-currencies.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/multi-currency/test-class-frontend-prices.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/multi-currency/test-class-geolocation.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/multi-currency/test-class-multi-currency.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/multi-currency/test-class-rest-controller.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/multi-currency/test-class-settings.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/multi-currency/test-class-storefront-integration.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/multi-currency/test-class-tracking.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/multi-currency/test-class-user-settings.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/multi-currency/test-class-utils.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/notes/test-class-wc-payments-notes-instant-deposits-eligible.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/notes/test-class-wc-payments-notes-set-https-for-checkout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/notes/test-class-wc-payments-notes-set-up-stripelink.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/notes/test-class-wc-payments-notes-stripe-billing-deprecation.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/notes/test-class-wc-payments-remote-note-service.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/payment-methods/Configs/mocks/class-mock-payment-method-definition-invalid.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/payment-methods/Configs/mocks/class-mock-payment-method-definition-two.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/payment-methods/Configs/mocks/class-mock-payment-method-definition.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/payment-methods/Configs/test-class-payment-method-definition-registry.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/payment-methods/Configs/test-class-payment-method-utils.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/payment-methods/test-class-klarna-payment-method.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/payment-methods/test-class-upe-payment-gateway.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/payment-methods/test-class-upe-payment-method.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/payment-methods/test-class-upe-split-payment-gateway.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/reports/test-class-wc-rest-payments-reports-authorizations-controller.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/reports/test-class-wc-rest-payments-reports-transactions-controller.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/src/ContainerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/src/Internal/DependencyManagement/DelegateContainer/LegacyContainerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/src/Internal/DependencyManagement/DelegateContainer/WooContainerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/src/Internal/DependencyManagement/ExtendedContainerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/src/Internal/LoggerContextTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/src/Internal/LoggerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/src/Internal/Proxy/HooksProxyTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/src/Internal/Proxy/LegacyProxyTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/src/Internal/Service/DuplicatePaymentPreventionServiceTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/src/Internal/Service/ExampleServiceWithDependenciesTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/src/Internal/Service/Level3ServiceTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/src/Internal/Service/OrderServiceTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/src/Internal/Service/SessionServiceTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/subscriptions/test-class-wc-payments-invoice-service.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/subscriptions/test-class-wc-payments-product-service.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/subscriptions/test-class-wc-payments-subscription-change-payment-method.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/subscriptions/test-class-wc-payments-subscription-migration-log-handler.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/subscriptions/test-class-wc-payments-subscription-minimum-amount-handler.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/subscriptions/test-class-wc-payments-subscription-service.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/subscriptions/test-class-wc-payments-subscriptions-admin-notices.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/subscriptions/test-class-wc-payments-subscriptions-event-handler.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/subscriptions/test-class-wc-payments-subscriptions-migrator.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/subscriptions/test-class-wc-payments-subscriptions-onboarding-handler.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/subscriptions/test-class-wc-payments-subscriptions.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-base-constant.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-compatibility-service.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-database-cache.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-duplicate-payment-prevention-service.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-experimental-abtest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-payment-information.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-session-rate-limiter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payment-gateway-wcpay-payment-types.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payment-gateway-wcpay-process-payment.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payment-gateway-wcpay-process-refund.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payment-gateway-wcpay-subscriptions-payment-method-order-note.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payment-gateway-wcpay-subscriptions-process-payment.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payment-gateway-wcpay-subscriptions-trait.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payment-gateway-wcpay-subscriptions.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payment-gateway-wcpay.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payments-account-capital.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payments-account-link.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payments-account.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payments-action-scheduler-service.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payments-apple-pay-registration.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payments-captured-event-note.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payments-checkout.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payments-currency-manager.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payments-customer-service.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payments-db.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payments-dependency-service.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payments-explicit-price-formatter.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payments-features.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payments-fraud-service.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payments-incentives-service.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payments-localization-service.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payments-onboarding-service.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payments-order-service.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payments-order-success-page.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payments-payment-method-messaging-element.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payments-payment-request-session-handler.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payments-payment-request-session.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payments-redirect-service.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payments-session-service.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payments-token-service.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payments-utils.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payments-webhook-processing-service.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payments-webhook-reliability-service.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payments-woopay-button-handler.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-wc-payments.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-class-woopay-tracker.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/test-data/ip-geolocation.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/wc-payment-api/models/test-class-wc-payments-api-charge.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/wc-payment-api/test-class-wc-payments-api-client.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/woopay/class-woopay-scheduler-test.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/woopay/services/test-checkout-service.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/woopay/test-class-woopay-adapted-extensions.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/woopay/test-class-woopay-order-status-sync.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/woopay/test-class-woopay-session.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Automattic_woocommerce-payments/tests/unit/woopay/test-class-woopay-utilities.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.7622220516204834, "profiling_times": {"config_time": 6.679445743560791, "core_time": 39.8185760974884, "ignores_time": 0.001707315444946289, "total_time": 46.50077438354492}, "parsing_time": {"total_time": 24.953771829605103, "per_file_time": {"mean": 0.021183167936846423, "std_dev": 0.001978466075707409}, "very_slow_stats": {"time_ratio": 0.1130302429098989, "count_ratio": 0.005942275042444821}, "very_slow_files": [{"fpath": "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payment-gateway-wcpay.php", "ftime": 0.3290998935699463}, {"fpath": "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/summary/index.tsx", "ftime": 0.3494839668273926}, {"fpath": "downloaded_repos/Automattic_woocommerce-payments/client/transactions/list/index.tsx", "ftime": 0.36223411560058594}, {"fpath": "downloaded_repos/Automattic_woocommerce-payments/client/disputes/new-evidence/index.tsx", "ftime": 0.393388032913208}, {"fpath": "downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/transformers/__tests__/wc-to-stripe.test.js", "ftime": 0.39821791648864746}, {"fpath": "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_lunr.js", "ftime": 0.46834802627563477}, {"fpath": "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/timeline/map-events.js", "ftime": 0.519758939743042}]}, "scanning_time": {"total_time": 234.47688508033752, "per_file_time": {"mean": 0.0545168298257005, "std_dev": 0.3547077801346837}, "very_slow_stats": {"time_ratio": 0.5159030289177768, "count_ratio": 0.006045105789351314}, "very_slow_files": [{"fpath": "downloaded_repos/Automattic_woocommerce-payments/client/payment-details/timeline/map-events.js", "ftime": 3.***************}, {"fpath": "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments.php", "ftime": 3.****************}, {"fpath": "downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/MultiCurrency.php", "ftime": 3.****************}, {"fpath": "downloaded_repos/Automattic_woocommerce-payments/client/connect-account-page/index.tsx", "ftime": 4.***************}, {"fpath": "downloaded_repos/Automattic_woocommerce-payments/client/checkout/woopay/email-input-iframe.js", "ftime": 4.***************}, {"fpath": "downloaded_repos/Automattic_woocommerce-payments/client/transactions/list/index.tsx", "ftime": 5.**************}, {"fpath": "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_lunr.js", "ftime": 5.***************}, {"fpath": "downloaded_repos/Automattic_woocommerce-payments/client/disputes/new-evidence/index.tsx", "ftime": 10.***************}, {"fpath": "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payment-gateway-wcpay.php", "ftime": 11.*************}, {"fpath": "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js", "ftime": 31.***************}]}, "matching_time": {"total_time": 106.**************, "per_file_and_rule_time": {"mean": 0.018763802260683304, "std_dev": 0.0066236398912796115}, "very_slow_stats": {"time_ratio": 0.5814301113880546, "count_ratio": 0.03543859649122807}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payments.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 0.9035189151763916}, {"fpath": "downloaded_repos/Automattic_woocommerce-payments/client/transactions/list/index.tsx", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 1.0236480236053467}, {"fpath": "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js", "rule_id": "javascript.aws-lambda.security.tainted-eval.tainted-eval", "time": 1.1982958316802979}, {"fpath": "downloaded_repos/Automattic_woocommerce-payments/client/disputes/new-evidence/index.tsx", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 1.2460689544677734}, {"fpath": "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payment-gateway-wcpay.php", "rule_id": "php.lang.security.injection.tainted-filename.tainted-filename", "time": 1.4005990028381348}, {"fpath": "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payment-gateway-wcpay.php", "rule_id": "php.lang.security.injection.tainted-callable.tainted-callable", "time": 1.5328271389007568}, {"fpath": "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 1.5738410949707031}, {"fpath": "downloaded_repos/Automattic_woocommerce-payments/client/disputes/new-evidence/index.tsx", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 1.5945219993591309}, {"fpath": "downloaded_repos/Automattic_woocommerce-payments/includes/class-wc-payment-gateway-wcpay.php", "rule_id": "php.laravel.security.laravel-sql-injection.laravel-sql-injection", "time": 1.6585099697113037}, {"fpath": "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js", "rule_id": "javascript.aws-lambda.security.tainted-html-response.tainted-html-response", "time": 1.897237777709961}]}, "tainting_time": {"total_time": 44.38077163696289, "per_def_and_rule_time": {"mean": 0.003124306345439134, "std_dev": 0.00048188633919827873}, "very_slow_stats": {"time_ratio": 0.38831703090031566, "count_ratio": 0.009081309398099261}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js", "fline": 1, "rule_id": "javascript.express.security.audit.express-path-join-resolve-traversal.express-path-join-resolve-traversal", "time": 0.22579383850097656}, {"fpath": "downloaded_repos/Automattic_woocommerce-payments/client/checkout/utils/upe.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.26076698303222656}, {"fpath": "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js", "fline": 1, "rule_id": "javascript.express.security.express-data-exfiltration.express-data-exfiltration", "time": 0.2621290683746338}, {"fpath": "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.37862300872802734}, {"fpath": "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js", "fline": 1, "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 0.4794349670410156}, {"fpath": "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js", "fline": 1, "rule_id": "javascript.lang.security.audit.sqli.node-postgres-sqli.node-postgres-sqli", "time": 0.6000730991363525}, {"fpath": "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js", "fline": 1, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.6996750831604004}, {"fpath": "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js", "fline": 1, "rule_id": "typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method", "time": 0.8732640743255615}, {"fpath": "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js", "fline": 1, "rule_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "time": 0.8999199867248535}, {"fpath": "downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 1.5304248332977295}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1133477312}, "engine_requested": "OSS", "skipped_rules": []}