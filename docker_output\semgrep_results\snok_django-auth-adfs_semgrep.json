{"version": "1.130.0", "results": [{"check_id": "python.django.security.audit.django-rest-framework.missing-throttle-config.missing-throttle-config", "path": "downloaded_repos/snok_django-auth-adfs/demo/adfs/mysite/settings.py", "start": {"line": 172, "col": 1, "offset": 4738}, "end": {"line": 172, "col": 15, "offset": 4752}, "extra": {"message": "Django REST framework configuration is missing default rate- limiting options. This could inadvertently allow resource starvation or Denial of Service (DoS) attacks. Add 'DEFAULT_THROTTLE_CLASSES' and 'DEFAULT_THROTTLE_RATES' to add rate-limiting to your application.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-770: Allocation of Resources Without Limits or Throttling"], "references": ["https://www.django-rest-framework.org/api-guide/throttling/#setting-the-throttling-policy"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/python.django.security.audit.django-rest-framework.missing-throttle-config.missing-throttle-config", "shortlink": "https://sg.run/vzBY"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.django-rest-framework.missing-throttle-config.missing-throttle-config", "path": "downloaded_repos/snok_django-auth-adfs/demo/formsbased/mysite/settings.py", "start": {"line": 127, "col": 1, "offset": 3238}, "end": {"line": 127, "col": 15, "offset": 3252}, "extra": {"message": "Django REST framework configuration is missing default rate- limiting options. This could inadvertently allow resource starvation or Denial of Service (DoS) attacks. Add 'DEFAULT_THROTTLE_CLASSES' and 'DEFAULT_THROTTLE_RATES' to add rate-limiting to your application.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-770: Allocation of Resources Without Limits or Throttling"], "references": ["https://www.django-rest-framework.org/api-guide/throttling/#setting-the-throttling-policy"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/python.django.security.audit.django-rest-framework.missing-throttle-config.missing-throttle-config", "shortlink": "https://sg.run/vzBY"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/snok_django-auth-adfs/demo/formsbased/templates/registration/login.html", "start": {"line": 17, "col": 1, "offset": 416}, "end": {"line": 35, "col": 8, "offset": 1086}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/snok_django-auth-adfs/django_auth_adfs/backend.py", "start": {"line": 58, "col": 9, "offset": 2159}, "end": {"line": 58, "col": 84, "offset": 2234}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"Getting access token at: %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/snok_django-auth-adfs/django_auth_adfs/backend.py", "start": {"line": 73, "col": 9, "offset": 2798}, "end": {"line": 73, "col": 85, "offset": 2874}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"Getting OBO access token: %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/snok_django-auth-adfs/django_auth_adfs/backend.py", "start": {"line": 88, "col": 9, "offset": 3527}, "end": {"line": 88, "col": 72, "offset": 3590}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"Received OBO access token: %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/snok_django-auth-adfs/django_auth_adfs/backend.py", "start": {"line": 188, "col": 9, "offset": 7439}, "end": {"line": 188, "col": 64, "offset": 7494}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"Received access token: %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/snok_django-auth-adfs/django_auth_adfs/config.py", "start": {"line": 235, "col": 13, "offset": 9770}, "end": {"line": 235, "col": 75, "offset": 9832}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"token endpoint:         %s\" being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/snok_django-auth-adfs/.github/workflows/testing.yml", "start": {"line": 65, "col": 24, "offset": 1819}, "end": {"line": 65, "col": 47, "offset": 1842}}, {"path": "downloaded_repos/snok_django-auth-adfs/.github/workflows/testing.yml", "start": {"line": 66, "col": 37, "offset": 1819}, "end": {"line": 66, "col": 57, "offset": 1839}}]], "message": "Syntax error at line downloaded_repos/snok_django-auth-adfs/.github/workflows/testing.yml:65:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ matrix.django-version` was unexpected", "path": "downloaded_repos/snok_django-auth-adfs/.github/workflows/testing.yml", "spans": [{"file": "downloaded_repos/snok_django-auth-adfs/.github/workflows/testing.yml", "start": {"line": 65, "col": 24, "offset": 1819}, "end": {"line": 65, "col": 47, "offset": 1842}}, {"file": "downloaded_repos/snok_django-auth-adfs/.github/workflows/testing.yml", "start": {"line": 66, "col": 37, "offset": 1819}, "end": {"line": 66, "col": 57, "offset": 1839}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/templates/admin/base_site.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 21, "offset": 147}}, {"path": "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/templates/admin/base_site.html", "start": {"line": 7, "col": 1, "offset": 0}, "end": {"line": 9, "col": 37, "offset": 52}}]], "message": "Syntax error at line downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/templates/admin/base_site.html:1:\n `{% extends \"admin/base.html\" %}\n\n{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}\n\n{% block branding %}` was unexpected", "path": "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/templates/admin/base_site.html", "spans": [{"file": "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/templates/admin/base_site.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 21, "offset": 147}}, {"file": "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/templates/admin/base_site.html", "start": {"line": 7, "col": 1, "offset": 0}, "end": {"line": 9, "col": 37, "offset": 52}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/templates/polls/detail.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 20, "offset": 45}}, {"path": "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/templates/polls/detail.html", "start": {"line": 18, "col": 1, "offset": 0}, "end": {"line": 18, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/templates/polls/detail.html:1:\n `{% extends \"base.html\" %}\n{% block content %}` was unexpected", "path": "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/templates/polls/detail.html", "spans": [{"file": "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/templates/polls/detail.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 20, "offset": 45}}, {"file": "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/templates/polls/detail.html", "start": {"line": 18, "col": 1, "offset": 0}, "end": {"line": 18, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/templates/polls/index.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 20, "offset": 45}}, {"path": "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/templates/polls/index.html", "start": {"line": 4, "col": 3, "offset": 0}, "end": {"line": 4, "col": 32, "offset": 29}}, {"path": "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/templates/polls/index.html", "start": {"line": 10, "col": 3, "offset": 0}, "end": {"line": 10, "col": 13, "offset": 10}}, {"path": "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/templates/polls/index.html", "start": {"line": 12, "col": 3, "offset": 0}, "end": {"line": 13, "col": 15, "offset": 26}}]], "message": "Syntax error at line downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/templates/polls/index.html:1:\n `{% extends \"base.html\" %}\n{% block content %}` was unexpected", "path": "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/templates/polls/index.html", "spans": [{"file": "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/templates/polls/index.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 20, "offset": 45}}, {"file": "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/templates/polls/index.html", "start": {"line": 4, "col": 3, "offset": 0}, "end": {"line": 4, "col": 32, "offset": 29}}, {"file": "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/templates/polls/index.html", "start": {"line": 10, "col": 3, "offset": 0}, "end": {"line": 10, "col": 13, "offset": 10}}, {"file": "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/templates/polls/index.html", "start": {"line": 12, "col": 3, "offset": 0}, "end": {"line": 13, "col": 15, "offset": 26}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/snok_django-auth-adfs/demo/adfs/templates/base.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 18, "offset": 17}}]], "message": "Syntax error at line downloaded_repos/snok_django-auth-adfs/demo/adfs/templates/base.html:1:\n `{% load static %}` was unexpected", "path": "downloaded_repos/snok_django-auth-adfs/demo/adfs/templates/base.html", "spans": [{"file": "downloaded_repos/snok_django-auth-adfs/demo/adfs/templates/base.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 18, "offset": 17}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/templates/admin/base_site.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 21, "offset": 147}}, {"path": "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/templates/admin/base_site.html", "start": {"line": 7, "col": 1, "offset": 0}, "end": {"line": 9, "col": 37, "offset": 52}}]], "message": "Syntax error at line downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/templates/admin/base_site.html:1:\n `{% extends \"admin/base.html\" %}\n\n{% block title %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}\n\n{% block branding %}` was unexpected", "path": "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/templates/admin/base_site.html", "spans": [{"file": "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/templates/admin/base_site.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 21, "offset": 147}}, {"file": "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/templates/admin/base_site.html", "start": {"line": 7, "col": 1, "offset": 0}, "end": {"line": 9, "col": 37, "offset": 52}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/templates/polls/detail.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 20, "offset": 45}}, {"path": "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/templates/polls/detail.html", "start": {"line": 18, "col": 1, "offset": 0}, "end": {"line": 18, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/templates/polls/detail.html:1:\n `{% extends \"base.html\" %}\n{% block content %}` was unexpected", "path": "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/templates/polls/detail.html", "spans": [{"file": "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/templates/polls/detail.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 20, "offset": 45}}, {"file": "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/templates/polls/detail.html", "start": {"line": 18, "col": 1, "offset": 0}, "end": {"line": 18, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/templates/polls/index.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 20, "offset": 45}}, {"path": "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/templates/polls/index.html", "start": {"line": 4, "col": 3, "offset": 0}, "end": {"line": 4, "col": 32, "offset": 29}}, {"path": "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/templates/polls/index.html", "start": {"line": 10, "col": 3, "offset": 0}, "end": {"line": 10, "col": 13, "offset": 10}}, {"path": "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/templates/polls/index.html", "start": {"line": 12, "col": 3, "offset": 0}, "end": {"line": 13, "col": 15, "offset": 26}}]], "message": "Syntax error at line downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/templates/polls/index.html:1:\n `{% extends \"base.html\" %}\n{% block content %}` was unexpected", "path": "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/templates/polls/index.html", "spans": [{"file": "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/templates/polls/index.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 2, "col": 20, "offset": 45}}, {"file": "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/templates/polls/index.html", "start": {"line": 4, "col": 3, "offset": 0}, "end": {"line": 4, "col": 32, "offset": 29}}, {"file": "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/templates/polls/index.html", "start": {"line": 10, "col": 3, "offset": 0}, "end": {"line": 10, "col": 13, "offset": 10}}, {"file": "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/templates/polls/index.html", "start": {"line": 12, "col": 3, "offset": 0}, "end": {"line": 13, "col": 15, "offset": 26}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/snok_django-auth-adfs/demo/formsbased/templates/base.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 18, "offset": 17}}]], "message": "Syntax error at line downloaded_repos/snok_django-auth-adfs/demo/formsbased/templates/base.html:1:\n `{% load static %}` was unexpected", "path": "downloaded_repos/snok_django-auth-adfs/demo/formsbased/templates/base.html", "spans": [{"file": "downloaded_repos/snok_django-auth-adfs/demo/formsbased/templates/base.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 18, "offset": 17}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/snok_django-auth-adfs/demo/formsbased/templates/registration/logged_out.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 20, "offset": 87}}, {"path": "downloaded_repos/snok_django-auth-adfs/demo/formsbased/templates/registration/logged_out.html", "start": {"line": 9, "col": 1, "offset": 0}, "end": {"line": 9, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/snok_django-auth-adfs/demo/formsbased/templates/registration/logged_out.html:1:\n `{% extends 'base.html' %}\n\n{% block title %}See you!{% endblock %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/snok_django-auth-adfs/demo/formsbased/templates/registration/logged_out.html", "spans": [{"file": "downloaded_repos/snok_django-auth-adfs/demo/formsbased/templates/registration/logged_out.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 20, "offset": 87}}, {"file": "downloaded_repos/snok_django-auth-adfs/demo/formsbased/templates/registration/logged_out.html", "start": {"line": 9, "col": 1, "offset": 0}, "end": {"line": 9, "col": 15, "offset": 14}}]}], "paths": {"scanned": ["downloaded_repos/snok_django-auth-adfs/.codecov.yml", "downloaded_repos/snok_django-auth-adfs/.coveragerc", "downloaded_repos/snok_django-auth-adfs/.editorconfig", "downloaded_repos/snok_django-auth-adfs/.github/FUNDING.yml", "downloaded_repos/snok_django-auth-adfs/.github/workflows/codecov.yml", "downloaded_repos/snok_django-auth-adfs/.github/workflows/publish_to_pypi.yml", "downloaded_repos/snok_django-auth-adfs/.github/workflows/testing.yml", "downloaded_repos/snok_django-auth-adfs/.gitignore", "downloaded_repos/snok_django-auth-adfs/.readthedocs.yaml", "downloaded_repos/snok_django-auth-adfs/CONTRIBUTING.rst", "downloaded_repos/snok_django-auth-adfs/LICENSE", "downloaded_repos/snok_django-auth-adfs/MANIFEST.in", "downloaded_repos/snok_django-auth-adfs/README.rst", "downloaded_repos/snok_django-auth-adfs/Vagrantfile", "downloaded_repos/snok_django-auth-adfs/demo/adfs/manage.py", "downloaded_repos/snok_django-auth-adfs/demo/adfs/mysite/__init__.py", "downloaded_repos/snok_django-auth-adfs/demo/adfs/mysite/settings.py", "downloaded_repos/snok_django-auth-adfs/demo/adfs/mysite/urls.py", "downloaded_repos/snok_django-auth-adfs/demo/adfs/mysite/wsgi.py", "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/__init__.py", "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/admin.py", "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/api/__init__.py", "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/api/filters.py", "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/api/serializers.py", "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/api/urls.py", "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/api/views.py", "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/apps.py", "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/migrations/0001_initial.py", "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/migrations/__init__.py", "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/models.py", "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/static/bootstrap.min.css", "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/templates/admin/base_site.html", "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/templates/polls/detail.html", "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/templates/polls/index.html", "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/templates/polls/vote.html", "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/urls.py", "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/views.py", "downloaded_repos/snok_django-auth-adfs/demo/adfs/templates/base.html", "downloaded_repos/snok_django-auth-adfs/demo/adfs/templates/home.html", "downloaded_repos/snok_django-auth-adfs/demo/formsbased/manage.py", "downloaded_repos/snok_django-auth-adfs/demo/formsbased/mysite/__init__.py", "downloaded_repos/snok_django-auth-adfs/demo/formsbased/mysite/settings.py", "downloaded_repos/snok_django-auth-adfs/demo/formsbased/mysite/urls.py", "downloaded_repos/snok_django-auth-adfs/demo/formsbased/mysite/wsgi.py", "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/__init__.py", "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/admin.py", "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/api/__init__.py", "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/api/filters.py", "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/api/serializers.py", "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/api/urls.py", "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/api/views.py", "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/apps.py", "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/migrations/0001_initial.py", "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/migrations/__init__.py", "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/models.py", "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/static/bootstrap.min.css", "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/templates/admin/base_site.html", "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/templates/polls/detail.html", "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/templates/polls/index.html", "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/templates/polls/vote.html", "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/urls.py", "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/views.py", "downloaded_repos/snok_django-auth-adfs/demo/formsbased/templates/base.html", "downloaded_repos/snok_django-auth-adfs/demo/formsbased/templates/home.html", "downloaded_repos/snok_django-auth-adfs/demo/formsbased/templates/registration/logged_out.html", "downloaded_repos/snok_django-auth-adfs/demo/formsbased/templates/registration/login.html", "downloaded_repos/snok_django-auth-adfs/django_auth_adfs/__init__.py", "downloaded_repos/snok_django-auth-adfs/django_auth_adfs/backend.py", "downloaded_repos/snok_django-auth-adfs/django_auth_adfs/config.py", "downloaded_repos/snok_django-auth-adfs/django_auth_adfs/drf-urls.py", "downloaded_repos/snok_django-auth-adfs/django_auth_adfs/drf_urls.py", "downloaded_repos/snok_django-auth-adfs/django_auth_adfs/exceptions.py", "downloaded_repos/snok_django-auth-adfs/django_auth_adfs/middleware.py", "downloaded_repos/snok_django-auth-adfs/django_auth_adfs/rest_framework.py", "downloaded_repos/snok_django-auth-adfs/django_auth_adfs/signals.py", "downloaded_repos/snok_django-auth-adfs/django_auth_adfs/templates/django_auth_adfs/login_failed.html", "downloaded_repos/snok_django-auth-adfs/django_auth_adfs/urls.py", "downloaded_repos/snok_django-auth-adfs/django_auth_adfs/views.py", "downloaded_repos/snok_django-auth-adfs/docs/Makefile", "downloaded_repos/snok_django-auth-adfs/docs/_static/2012/01_add_relying_party.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/2012/02_add_relying_party_wizard_page1.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/2012/03_add_relying_party_wizard_page2.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/2012/04_add_relying_party_wizard_page3.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/2012/05_add_relying_party_wizard_page4.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/2012/06_add_relying_party_wizard_page5.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/2012/07_add_relying_party_wizard_page6.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/2012/08_relying_party_id.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/2012/09_add_relying_party_wizard_page8.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/2012/10_add_relying_party_wizard_page9.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/2012/11_add_relying_party_wizard_review.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/2012/12_add_relying_party_wizard_page11.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/2012/13_configure_claims_page1.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/2012/14_configure_claims_page2.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/2012/15_configure_claims_page3.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/2012/16_configure_claims_page4.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/2016/01_add_app_group.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/2016/02_add_app_group_wizard_page1.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/2016/03_add_native_app.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/2016/04_native_app_access_policy.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/2016/05_review_settings.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/2016/06_wizard_end.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/2016/07_app_group_settings.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/2016/08_add_claim_rules.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/2016/08_add_ldap_attributes_part1.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/2016/08_add_ldap_attributes_part2.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/AzureAD/01-azure_active_directory.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/AzureAD/02-azure_dashboard.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/AzureAD/03-new_registrations.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/AzureAD/04-app_registrations_specs.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/AzureAD/05-application_overview.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/AzureAD/06-add_Secret.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/AzureAD/07-add_Secret_name.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/AzureAD/08-copy_Secret.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/AzureAD/09_register_frontend_app.PNG", "downloaded_repos/snok_django-auth-adfs/docs/_static/AzureAD/10_copy-frontend-client_id.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/AzureAD/11-navigate_to_expose_an_api.PNG", "downloaded_repos/snok_django-auth-adfs/docs/_static/AzureAD/13_set_app_id.PNG", "downloaded_repos/snok_django-auth-adfs/docs/_static/AzureAD/14_add_a_scope.PNG", "downloaded_repos/snok_django-auth-adfs/docs/_static/AzureAD/15_add_authorized_app_1.png", "downloaded_repos/snok_django-auth-adfs/docs/_static/AzureAD/16_add_authorized_app_2.PNG", "downloaded_repos/snok_django-auth-adfs/docs/_static/AzureAD/17_navigate_to_api_permissions.PNG", "downloaded_repos/snok_django-auth-adfs/docs/_static/AzureAD/18_add_permission.PNG", "downloaded_repos/snok_django-auth-adfs/docs/_static/AzureAD/19_add-permission-2.PNG", "downloaded_repos/snok_django-auth-adfs/docs/_static/AzureAD/20_add-permission-3.png", "downloaded_repos/snok_django-auth-adfs/docs/_templates/.gitkeep", "downloaded_repos/snok_django-auth-adfs/docs/adfs_3.0_config_guide.rst", "downloaded_repos/snok_django-auth-adfs/docs/adfs_4.0_config_guide.rst", "downloaded_repos/snok_django-auth-adfs/docs/azure_ad_config_guide.rst", "downloaded_repos/snok_django-auth-adfs/docs/conf.py", "downloaded_repos/snok_django-auth-adfs/docs/config_guides.rst", "downloaded_repos/snok_django-auth-adfs/docs/contributing.rst", "downloaded_repos/snok_django-auth-adfs/docs/demo.rst", "downloaded_repos/snok_django-auth-adfs/docs/faq.rst", "downloaded_repos/snok_django-auth-adfs/docs/index.rst", "downloaded_repos/snok_django-auth-adfs/docs/install.rst", "downloaded_repos/snok_django-auth-adfs/docs/make.bat", "downloaded_repos/snok_django-auth-adfs/docs/middleware.rst", "downloaded_repos/snok_django-auth-adfs/docs/oauth2_explained.rst", "downloaded_repos/snok_django-auth-adfs/docs/requirements.txt", "downloaded_repos/snok_django-auth-adfs/docs/rest_framework.rst", "downloaded_repos/snok_django-auth-adfs/docs/settings_ref.rst", "downloaded_repos/snok_django-auth-adfs/docs/signals.rst", "downloaded_repos/snok_django-auth-adfs/docs/troubleshooting.rst", "downloaded_repos/snok_django-auth-adfs/manage.py", "downloaded_repos/snok_django-auth-adfs/poetry.lock", "downloaded_repos/snok_django-auth-adfs/pyproject.toml", "downloaded_repos/snok_django-auth-adfs/setup.cfg", "downloaded_repos/snok_django-auth-adfs/vagrant/01-setup-domain.ps1", "downloaded_repos/snok_django-auth-adfs/vagrant/02-setup-vagrant-user.ps1", "downloaded_repos/snok_django-auth-adfs/vagrant/03-setup-adfs.ps1", "downloaded_repos/snok_django-auth-adfs/vagrant/04-example-adfs-config.ps1", "downloaded_repos/snok_django-auth-adfs/vagrant/New-SelfSignedCertificateEx.ps1", "downloaded_repos/snok_django-auth-adfs/vagrant/README.rst"], "skipped": [{"path": "downloaded_repos/snok_django-auth-adfs/.github/workflows/testing.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/templates/admin/base_site.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/templates/polls/detail.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/snok_django-auth-adfs/demo/adfs/polls/templates/polls/index.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/snok_django-auth-adfs/demo/adfs/templates/base.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/templates/admin/base_site.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/templates/polls/detail.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/snok_django-auth-adfs/demo/formsbased/polls/templates/polls/index.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/snok_django-auth-adfs/demo/formsbased/templates/base.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/snok_django-auth-adfs/demo/formsbased/templates/registration/logged_out.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/snok_django-auth-adfs/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/snok_django-auth-adfs/tests/custom_config.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/snok_django-auth-adfs/tests/mock_files/FederationMetadata.xml", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/snok_django-auth-adfs/tests/mock_files/adfs-openid-configuration.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/snok_django-auth-adfs/tests/mock_files/azure-openid-configuration-v2.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/snok_django-auth-adfs/tests/mock_files/azure-openid-configuration.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/snok_django-auth-adfs/tests/models.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/snok_django-auth-adfs/tests/settings.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/snok_django-auth-adfs/tests/test_authentication.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/snok_django-auth-adfs/tests/test_drf_integration.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/snok_django-auth-adfs/tests/test_settings.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/snok_django-auth-adfs/tests/urls.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/snok_django-auth-adfs/tests/utils.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/snok_django-auth-adfs/tests/views.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.8687071800231934, "profiling_times": {"config_time": 6.717122316360474, "core_time": 3.9380943775177, "ignores_time": 0.001958608627319336, "total_time": 10.658454895019531}, "parsing_time": {"total_time": 0.5183556079864502, "per_file_time": {"mean": 0.00809930637478828, "std_dev": 0.00018891285984677658}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 4.518252611160278, "per_file_time": {"mean": 0.012016629285000741, "std_dev": 0.002340835422815936}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 1.167100191116333, "per_file_and_rule_time": {"mean": 0.00211048859153044, "std_dev": 5.2156807472747405e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.28501152992248535, "per_def_and_rule_time": {"mean": 0.00043380750368719235, "std_dev": 1.0825311085532506e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089476480}, "engine_requested": "OSS", "skipped_rules": []}