{"version": "1.130.0", "results": [{"check_id": "dockerfile.security.missing-user-entrypoint.missing-user-entrypoint", "path": "downloaded_repos/iGeeky_wolf/agent/Dockerfile", "start": {"line": 8, "col": 1, "offset": 157}, "end": {"line": 8, "col": 37, "offset": 193}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nENTRYPOINT [\"sh\", \"./entrypoint.sh\"]", "metadata": {"cwe": ["CWE-269: Improper Privilege Management"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user-entrypoint.missing-user-entrypoint", "shortlink": "https://sg.run/k281"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/iGeeky_wolf/console/src/utils/index.js", "start": {"line": 181, "col": 3, "offset": 3959}, "end": {"line": 181, "col": 22, "offset": 3978}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/iGeeky_wolf/console/src/utils/index.js", "start": {"line": 328, "col": 32, "offset": 7562}, "end": {"line": 328, "col": 71, "offset": 7601}, "extra": {"message": "RegExp() called with a `cls` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/iGeeky_wolf/console/src/utils/index.js", "start": {"line": 347, "col": 17, "offset": 7955}, "end": {"line": 347, "col": 56, "offset": 7994}, "extra": {"message": "RegExp() called with a `cls` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/iGeeky_wolf/console/src/views/login/index.vue", "start": {"line": 58, "col": 13, "offset": 2042}, "end": {"line": 58, "col": 46, "offset": 2075}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/iGeeky_wolf/console/src/views/resource/index.vue", "start": {"line": 91, "col": 15, "offset": 4478}, "end": {"line": 91, "col": 28, "offset": 4491}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/iGeeky_wolf/console/src/views/resource/index.vue", "start": {"line": 95, "col": 15, "offset": 4709}, "end": {"line": 95, "col": 28, "offset": 4722}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/iGeeky_wolf/console/vue.config.js", "start": {"line": 8, "col": 31, "offset": 215}, "end": {"line": 8, "col": 34, "offset": 218}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-generic-secret.detected-generic-secret", "path": "downloaded_repos/iGeeky_wolf/docs/admin-api-oauth2.0-cn.md", "start": {"line": 138, "col": 57, "offset": 5029}, "end": {"line": 138, "col": 104, "offset": 5076}, "extra": {"message": "Generic Secret detected", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "source-rule-url": "https://github.com/dxa4481/truffleHogRegexes/blob/master/truffleHogRegexes/regexes.json", "category": "security", "technology": ["secrets"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-generic-secret.detected-generic-secret", "shortlink": "https://sg.run/l2o5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-generic-secret.detected-generic-secret", "path": "downloaded_repos/iGeeky_wolf/docs/admin-api-oauth2.0-cn.md", "start": {"line": 206, "col": 47, "offset": 6917}, "end": {"line": 206, "col": 94, "offset": 6964}, "extra": {"message": "Generic Secret detected", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "source-rule-url": "https://github.com/dxa4481/truffleHogRegexes/blob/master/truffleHogRegexes/regexes.json", "category": "security", "technology": ["secrets"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-generic-secret.detected-generic-secret", "shortlink": "https://sg.run/l2o5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-generic-secret.detected-generic-secret", "path": "downloaded_repos/iGeeky_wolf/docs/admin-api-oauth2.0-cn.md", "start": {"line": 271, "col": 57, "offset": 8700}, "end": {"line": 271, "col": 105, "offset": 8748}, "extra": {"message": "Generic Secret detected", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "source-rule-url": "https://github.com/dxa4481/truffleHogRegexes/blob/master/truffleHogRegexes/regexes.json", "category": "security", "technology": ["secrets"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-generic-secret.detected-generic-secret", "shortlink": "https://sg.run/l2o5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-generic-secret.detected-generic-secret", "path": "downloaded_repos/iGeeky_wolf/docs/admin-api-oauth2.0-cn.md", "start": {"line": 336, "col": 52, "offset": 10487}, "end": {"line": 336, "col": 99, "offset": 10534}, "extra": {"message": "Generic Secret detected", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "source-rule-url": "https://github.com/dxa4481/truffleHogRegexes/blob/master/truffleHogRegexes/regexes.json", "category": "security", "technology": ["secrets"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-generic-secret.detected-generic-secret", "shortlink": "https://sg.run/l2o5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-generic-secret.detected-generic-secret", "path": "downloaded_repos/iGeeky_wolf/docs/admin-api-oauth2.0.md", "start": {"line": 137, "col": 57, "offset": 5435}, "end": {"line": 137, "col": 104, "offset": 5482}, "extra": {"message": "Generic Secret detected", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "source-rule-url": "https://github.com/dxa4481/truffleHogRegexes/blob/master/truffleHogRegexes/regexes.json", "category": "security", "technology": ["secrets"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-generic-secret.detected-generic-secret", "shortlink": "https://sg.run/l2o5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-generic-secret.detected-generic-secret", "path": "downloaded_repos/iGeeky_wolf/docs/admin-api-oauth2.0.md", "start": {"line": 205, "col": 47, "offset": 7413}, "end": {"line": 205, "col": 94, "offset": 7460}, "extra": {"message": "Generic Secret detected", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "source-rule-url": "https://github.com/dxa4481/truffleHogRegexes/blob/master/truffleHogRegexes/regexes.json", "category": "security", "technology": ["secrets"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-generic-secret.detected-generic-secret", "shortlink": "https://sg.run/l2o5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-generic-secret.detected-generic-secret", "path": "downloaded_repos/iGeeky_wolf/docs/admin-api-oauth2.0.md", "start": {"line": 271, "col": 57, "offset": 9255}, "end": {"line": 271, "col": 105, "offset": 9303}, "extra": {"message": "Generic Secret detected", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "source-rule-url": "https://github.com/dxa4481/truffleHogRegexes/blob/master/truffleHogRegexes/regexes.json", "category": "security", "technology": ["secrets"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-generic-secret.detected-generic-secret", "shortlink": "https://sg.run/l2o5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-generic-secret.detected-generic-secret", "path": "downloaded_repos/iGeeky_wolf/docs/admin-api-oauth2.0.md", "start": {"line": 336, "col": 52, "offset": 11163}, "end": {"line": 336, "col": 99, "offset": 11210}, "extra": {"message": "Generic Secret detected", "metadata": {"cwe": ["CWE-798: Use of Hard-coded Credentials"], "source-rule-url": "https://github.com/dxa4481/truffleHogRegexes/blob/master/truffleHogRegexes/regexes.json", "category": "security", "technology": ["secrets"], "confidence": "LOW", "owasp": ["A07:2021 - Identification and Authentication Failures"], "references": ["https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Hard-coded Secrets"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-generic-secret.detected-generic-secret", "shortlink": "https://sg.run/l2o5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/iGeeky_wolf/quick-start-with-docker/docker-compose.yaml", "start": {"line": 3, "col": 3, "offset": 25}, "end": {"line": 3, "col": 11, "offset": 33}, "extra": {"message": "Service 'database' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/iGeeky_wolf/quick-start-with-docker/docker-compose.yaml", "start": {"line": 3, "col": 3, "offset": 25}, "end": {"line": 3, "col": 11, "offset": 33}, "extra": {"message": "Service 'database' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/iGeeky_wolf/quick-start-with-docker/docker-compose.yaml", "start": {"line": 13, "col": 3, "offset": 305}, "end": {"line": 13, "col": 8, "offset": 310}, "extra": {"message": "Service 'cache' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/iGeeky_wolf/quick-start-with-docker/docker-compose.yaml", "start": {"line": 13, "col": 3, "offset": 305}, "end": {"line": 13, "col": 8, "offset": 310}, "extra": {"message": "Service 'cache' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/iGeeky_wolf/quick-start-with-docker/docker-compose.yaml", "start": {"line": 17, "col": 3, "offset": 386}, "end": {"line": 17, "col": 9, "offset": 392}, "extra": {"message": "Service 'server' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/iGeeky_wolf/quick-start-with-docker/docker-compose.yaml", "start": {"line": 17, "col": 3, "offset": 386}, "end": {"line": 17, "col": 9, "offset": 392}, "extra": {"message": "Service 'server' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/iGeeky_wolf/quick-start-with-docker/docker-compose.yaml", "start": {"line": 34, "col": 3, "offset": 925}, "end": {"line": 34, "col": 11, "offset": 933}, "extra": {"message": "Service 'agent-or' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/iGeeky_wolf/quick-start-with-docker/docker-compose.yaml", "start": {"line": 34, "col": 3, "offset": 925}, "end": {"line": 34, "col": 11, "offset": 933}, "extra": {"message": "Service 'agent-or' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/iGeeky_wolf/quick-start-with-docker/docker-compose.yaml", "start": {"line": 46, "col": 3, "offset": 1212}, "end": {"line": 46, "col": 15, "offset": 1224}, "extra": {"message": "Service 'restful-demo' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/iGeeky_wolf/quick-start-with-docker/docker-compose.yaml", "start": {"line": 46, "col": 3, "offset": 1212}, "end": {"line": 46, "col": 15, "offset": 1224}, "extra": {"message": "Service 'restful-demo' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/iGeeky_wolf/quick-start-with-docker/docker-compose.yaml", "start": {"line": 52, "col": 3, "offset": 1351}, "end": {"line": 52, "col": 13, "offset": 1361}, "extra": {"message": "Service 'agent-demo' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/iGeeky_wolf/quick-start-with-docker/docker-compose.yaml", "start": {"line": 52, "col": 3, "offset": 1351}, "end": {"line": 52, "col": 13, "offset": 1361}, "extra": {"message": "Service 'agent-demo' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.kubernetes.security.run-as-non-root.run-as-non-root", "path": "downloaded_repos/iGeeky_wolf/quick-start-with-docker/k8s/wolf-cache.yaml", "start": {"line": 18, "col": 5, "offset": 257}, "end": {"line": 18, "col": 9, "offset": 261}, "extra": {"message": "When running containers in Kubernetes, it's important to ensure that they  are properly secured to prevent privilege escalation attacks.  One potential vulnerability is when a container is allowed to run  applications as the root user, which could allow an attacker to gain  access to sensitive resources. To mitigate this risk, it's recommended to  add a `securityContext` to the container, with the parameter `runAsNonRoot`  set to `true`. This will ensure that the container runs as a non-root user,  limiting the damage that could be caused by any potential attacks. By  adding a `securityContext` to the container in your Kubernetes pod, you can  help to ensure that your containerized applications are more secure and  less vulnerable to privilege escalation attacks.", "fix": "spec:\n      securityContext:\n        runAsNonRoot: true #", "metadata": {"references": ["https://kubernetes.io/blog/2016/08/security-best-practices-kubernetes-deployment/", "https://kubernetes.io/docs/concepts/policy/pod-security-policy/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-2-set-a-user"], "category": "security", "cwe": ["CWE-250: Execution with Unnecessary Privileges"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "technology": ["kubernetes"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.kubernetes.security.run-as-non-root.run-as-non-root", "shortlink": "https://sg.run/dgP5"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.kubernetes.security.allow-privilege-escalation-no-securitycontext.allow-privilege-escalation-no-securitycontext", "path": "downloaded_repos/iGeeky_wolf/quick-start-with-docker/k8s/wolf-cache.yaml", "start": {"line": 20, "col": 11, "offset": 291}, "end": {"line": 20, "col": 15, "offset": 295}, "extra": {"message": "In Kubernetes, each pod runs in its own isolated environment with its own set of security policies. However, certain container images may contain `setuid` or `setgid` binaries that could allow an attacker to perform privilege escalation and gain access to sensitive resources. To mitigate this risk, it's recommended to add a `securityContext` to the container in the pod, with the parameter `allowPrivilegeEscalation` set to `false`. This will prevent the container from running any privileged processes and limit the impact of any potential attacks. By adding a `securityContext` to your Kubernetes pod, you can help to ensure that your containerized applications are more secure and less vulnerable to privilege escalation attacks.", "fix": "securityContext:\n            allowPrivilegeEscalation: false\n          name", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://kubernetes.io/docs/concepts/policy/pod-security-policy/#privilege-escalation", "https://kubernetes.io/docs/tasks/configure-pod-container/security-context/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["kubernetes"], "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.kubernetes.security.allow-privilege-escalation-no-securitycontext.allow-privilege-escalation-no-securitycontext", "shortlink": "https://sg.run/eleR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.kubernetes.security.run-as-non-root.run-as-non-root", "path": "downloaded_repos/iGeeky_wolf/quick-start-with-docker/k8s/wolf-database.yaml", "start": {"line": 18, "col": 5, "offset": 269}, "end": {"line": 18, "col": 9, "offset": 273}, "extra": {"message": "When running containers in Kubernetes, it's important to ensure that they  are properly secured to prevent privilege escalation attacks.  One potential vulnerability is when a container is allowed to run  applications as the root user, which could allow an attacker to gain  access to sensitive resources. To mitigate this risk, it's recommended to  add a `securityContext` to the container, with the parameter `runAsNonRoot`  set to `true`. This will ensure that the container runs as a non-root user,  limiting the damage that could be caused by any potential attacks. By  adding a `securityContext` to the container in your Kubernetes pod, you can  help to ensure that your containerized applications are more secure and  less vulnerable to privilege escalation attacks.", "fix": "spec:\n      securityContext:\n        runAsNonRoot: true #", "metadata": {"references": ["https://kubernetes.io/blog/2016/08/security-best-practices-kubernetes-deployment/", "https://kubernetes.io/docs/concepts/policy/pod-security-policy/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-2-set-a-user"], "category": "security", "cwe": ["CWE-250: Execution with Unnecessary Privileges"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "technology": ["kubernetes"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.kubernetes.security.run-as-non-root.run-as-non-root", "shortlink": "https://sg.run/dgP5"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.kubernetes.security.allow-privilege-escalation-no-securitycontext.allow-privilege-escalation-no-securitycontext", "path": "downloaded_repos/iGeeky_wolf/quick-start-with-docker/k8s/wolf-database.yaml", "start": {"line": 20, "col": 11, "offset": 303}, "end": {"line": 20, "col": 15, "offset": 307}, "extra": {"message": "In Kubernetes, each pod runs in its own isolated environment with its own set of security policies. However, certain container images may contain `setuid` or `setgid` binaries that could allow an attacker to perform privilege escalation and gain access to sensitive resources. To mitigate this risk, it's recommended to add a `securityContext` to the container in the pod, with the parameter `allowPrivilegeEscalation` set to `false`. This will prevent the container from running any privileged processes and limit the impact of any potential attacks. By adding a `securityContext` to your Kubernetes pod, you can help to ensure that your containerized applications are more secure and less vulnerable to privilege escalation attacks.", "fix": "securityContext:\n            allowPrivilegeEscalation: false\n          name", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://kubernetes.io/docs/concepts/policy/pod-security-policy/#privilege-escalation", "https://kubernetes.io/docs/tasks/configure-pod-container/security-context/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["kubernetes"], "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.kubernetes.security.allow-privilege-escalation-no-securitycontext.allow-privilege-escalation-no-securitycontext", "shortlink": "https://sg.run/eleR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.kubernetes.security.run-as-non-root.run-as-non-root", "path": "downloaded_repos/iGeeky_wolf/quick-start-with-docker/k8s/wolf-server.yaml", "start": {"line": 39, "col": 5, "offset": 1041}, "end": {"line": 39, "col": 9, "offset": 1045}, "extra": {"message": "When running containers in Kubernetes, it's important to ensure that they  are properly secured to prevent privilege escalation attacks.  One potential vulnerability is when a container is allowed to run  applications as the root user, which could allow an attacker to gain  access to sensitive resources. To mitigate this risk, it's recommended to  add a `securityContext` to the container, with the parameter `runAsNonRoot`  set to `true`. This will ensure that the container runs as a non-root user,  limiting the damage that could be caused by any potential attacks. By  adding a `securityContext` to the container in your Kubernetes pod, you can  help to ensure that your containerized applications are more secure and  less vulnerable to privilege escalation attacks.", "fix": "spec:\n      securityContext:\n        runAsNonRoot: true #", "metadata": {"references": ["https://kubernetes.io/blog/2016/08/security-best-practices-kubernetes-deployment/", "https://kubernetes.io/docs/concepts/policy/pod-security-policy/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-2-set-a-user"], "category": "security", "cwe": ["CWE-250: Execution with Unnecessary Privileges"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "technology": ["kubernetes"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.kubernetes.security.run-as-non-root.run-as-non-root", "shortlink": "https://sg.run/dgP5"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.kubernetes.security.allow-privilege-escalation-no-securitycontext.allow-privilege-escalation-no-securitycontext", "path": "downloaded_repos/iGeeky_wolf/quick-start-with-docker/k8s/wolf-server.yaml", "start": {"line": 41, "col": 11, "offset": 1075}, "end": {"line": 41, "col": 15, "offset": 1079}, "extra": {"message": "In Kubernetes, each pod runs in its own isolated environment with its own set of security policies. However, certain container images may contain `setuid` or `setgid` binaries that could allow an attacker to perform privilege escalation and gain access to sensitive resources. To mitigate this risk, it's recommended to add a `securityContext` to the container in the pod, with the parameter `allowPrivilegeEscalation` set to `false`. This will prevent the container from running any privileged processes and limit the impact of any potential attacks. By adding a `securityContext` to your Kubernetes pod, you can help to ensure that your containerized applications are more secure and less vulnerable to privilege escalation attacks.", "fix": "securityContext:\n            allowPrivilegeEscalation: false\n          name", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://kubernetes.io/docs/concepts/policy/pod-security-policy/#privilege-escalation", "https://kubernetes.io/docs/tasks/configure-pod-container/security-context/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["kubernetes"], "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.kubernetes.security.allow-privilege-escalation-no-securitycontext.allow-privilege-escalation-no-securitycontext", "shortlink": "https://sg.run/eleR"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "dockerfile.security.missing-user-entrypoint.missing-user-entrypoint", "path": "downloaded_repos/iGeeky_wolf/server/Dockerfile", "start": {"line": 9, "col": 1, "offset": 102}, "end": {"line": 9, "col": 37, "offset": 138}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nENTRYPOINT [\"sh\", \"./entrypoint.sh\"]", "metadata": {"cwe": ["CWE-269: Improper Privilege Management"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user-entrypoint.missing-user-entrypoint", "shortlink": "https://sg.run/k281"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "html.security.plaintext-http-link.plaintext-http-link", "path": "downloaded_repos/iGeeky_wolf/server/tmpl/oauth2/clientAuthenticate.html", "start": {"line": 19, "col": 3, "offset": 784}, "end": {"line": 19, "col": 258, "offset": 1039}, "extra": {"message": "This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible.", "metadata": {"category": "security", "technology": ["html"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "confidence": "HIGH", "subcategory": ["vuln"], "references": ["https://cwe.mitre.org/data/definitions/319.html"], "likelihood": "LOW", "impact": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/html.security.plaintext-http-link.plaintext-http-link", "shortlink": "https://sg.run/RA5q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/iGeeky_wolf/console/public/index.html", "start": {"line": 9, "col": 12, "offset": 0}, "end": {"line": 9, "col": 37, "offset": 25}}]], "message": "Syntax error at line downloaded_repos/iGeeky_wolf/console/public/index.html:9:\n `<%= webpackConfig.name %>` was unexpected", "path": "downloaded_repos/iGeeky_wolf/console/public/index.html", "spans": [{"file": "downloaded_repos/iGeeky_wolf/console/public/index.html", "start": {"line": 9, "col": 12, "offset": 0}, "end": {"line": 9, "col": 37, "offset": 25}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/iGeeky_wolf/server/tmpl/login_status.html", "start": {"line": 94, "col": 6, "offset": 0}, "end": {"line": 98, "col": 13, "offset": 121}}, {"path": "downloaded_repos/iGeeky_wolf/server/tmpl/login_status.html", "start": {"line": 104, "col": 6, "offset": 0}, "end": {"line": 104, "col": 27, "offset": 21}}, {"path": "downloaded_repos/iGeeky_wolf/server/tmpl/login_status.html", "start": {"line": 106, "col": 6, "offset": 0}, "end": {"line": 106, "col": 20, "offset": 14}}, {"path": "downloaded_repos/iGeeky_wolf/server/tmpl/login_status.html", "start": {"line": 108, "col": 6, "offset": 0}, "end": {"line": 108, "col": 13, "offset": 7}}]], "message": "Syntax error at line downloaded_repos/iGeeky_wolf/server/tmpl/login_status.html:94:\n `<% if (userInfo) { %>\n\t\t\t\t\t\t<%= userInfo.username %>/<%= userInfo.nickname %>\n\t\t\t\t\t<% } else { %>\n\t\t\t\t\t\tNone\n\t\t\t\t\t<% } %>` was unexpected", "path": "downloaded_repos/iGeeky_wolf/server/tmpl/login_status.html", "spans": [{"file": "downloaded_repos/iGeeky_wolf/server/tmpl/login_status.html", "start": {"line": 94, "col": 6, "offset": 0}, "end": {"line": 98, "col": 13, "offset": 121}}, {"file": "downloaded_repos/iGeeky_wolf/server/tmpl/login_status.html", "start": {"line": 104, "col": 6, "offset": 0}, "end": {"line": 104, "col": 27, "offset": 21}}, {"file": "downloaded_repos/iGeeky_wolf/server/tmpl/login_status.html", "start": {"line": 106, "col": 6, "offset": 0}, "end": {"line": 106, "col": 20, "offset": 14}}, {"file": "downloaded_repos/iGeeky_wolf/server/tmpl/login_status.html", "start": {"line": 108, "col": 6, "offset": 0}, "end": {"line": 108, "col": 13, "offset": 7}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/iGeeky_wolf/server/tmpl/no_permission.html", "start": {"line": 84, "col": 28, "offset": 0}, "end": {"line": 84, "col": 45, "offset": 17}}, {"path": "downloaded_repos/iGeeky_wolf/server/tmpl/no_permission.html", "start": {"line": 89, "col": 31, "offset": 0}, "end": {"line": 89, "col": 44, "offset": 13}}]], "message": "Syntax error at line downloaded_repos/iGeeky_wolf/server/tmpl/no_permission.html:84:\n `<%= username %> [` was unexpected", "path": "downloaded_repos/iGeeky_wolf/server/tmpl/no_permission.html", "spans": [{"file": "downloaded_repos/iGeeky_wolf/server/tmpl/no_permission.html", "start": {"line": 84, "col": 28, "offset": 0}, "end": {"line": 84, "col": 45, "offset": 17}}, {"file": "downloaded_repos/iGeeky_wolf/server/tmpl/no_permission.html", "start": {"line": 89, "col": 31, "offset": 0}, "end": {"line": 89, "col": 44, "offset": 13}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/iGeeky_wolf/server/tmpl/oauth2/clientAuthenticate.html", "start": {"line": 13, "col": 20, "offset": 0}, "end": {"line": 13, "col": 36, "offset": 16}}]], "message": "Syntax error at line downloaded_repos/iGeeky_wolf/server/tmpl/oauth2/clientAuthenticate.html:13:\n `<%= client_id %>` was unexpected", "path": "downloaded_repos/iGeeky_wolf/server/tmpl/oauth2/clientAuthenticate.html", "spans": [{"file": "downloaded_repos/iGeeky_wolf/server/tmpl/oauth2/clientAuthenticate.html", "start": {"line": 13, "col": 20, "offset": 0}, "end": {"line": 13, "col": 36, "offset": 16}}]}], "paths": {"scanned": ["downloaded_repos/iGeeky_wolf/.gitignore", "downloaded_repos/iGeeky_wolf/.vscode/settings.json", "downloaded_repos/iGeeky_wolf/ChangeLog.md", "downloaded_repos/iGeeky_wolf/LICENSE", "downloaded_repos/iGeeky_wolf/README-CN.md", "downloaded_repos/iGeeky_wolf/README-JA.md", "downloaded_repos/iGeeky_wolf/README.md", "downloaded_repos/iGeeky_wolf/agent/Dockerfile", "downloaded_repos/iGeeky_wolf/agent/README.md", "downloaded_repos/iGeeky_wolf/agent/conf/no-permission-demo.conf", "downloaded_repos/iGeeky_wolf/agent/conf/server-demo.conf", "downloaded_repos/iGeeky_wolf/agent/entrypoint.sh", "downloaded_repos/iGeeky_wolf/agent/lua/access_check.lua", "downloaded_repos/iGeeky_wolf/agent/lua/agent_pub.lua", "downloaded_repos/iGeeky_wolf/agent/lua/basic_auth_access_check.lua", "downloaded_repos/iGeeky_wolf/agent/lua/body_filter.lua", "downloaded_repos/iGeeky_wolf/agent/lua/config.lua", "downloaded_repos/iGeeky_wolf/agent/lua/header_filter.lua", "downloaded_repos/iGeeky_wolf/agent/lua/json.lua", "downloaded_repos/iGeeky_wolf/agent/lua/resty/cookie.lua", "downloaded_repos/iGeeky_wolf/agent/lua/util.lua", "downloaded_repos/iGeeky_wolf/bin/build-all.sh", "downloaded_repos/iGeeky_wolf/bin/build-docker-img-agent.sh", "downloaded_repos/iGeeky_wolf/bin/build-docker-img-server-quickly.sh", "downloaded_repos/iGeeky_wolf/bin/build-docker-img-server-use-cnpm.sh", "downloaded_repos/iGeeky_wolf/bin/build-docker-img-server.sh", "downloaded_repos/iGeeky_wolf/bin/push-docker-imgs.sh", "downloaded_repos/iGeeky_wolf/console/.editorconfig", "downloaded_repos/iGeeky_wolf/console/.env.development", "downloaded_repos/iGeeky_wolf/console/.env.production", "downloaded_repos/iGeeky_wolf/console/.eslintignore", "downloaded_repos/iGeeky_wolf/console/.eslintrc.js", "downloaded_repos/iGeeky_wolf/console/.gitignore", "downloaded_repos/iGeeky_wolf/console/.travis.yml", "downloaded_repos/iGeeky_wolf/console/babel.config.js", "downloaded_repos/iGeeky_wolf/console/jsconfig.json", "downloaded_repos/iGeeky_wolf/console/package.json", "downloaded_repos/iGeeky_wolf/console/plopfile.js", "downloaded_repos/iGeeky_wolf/console/postcss.config.js", "downloaded_repos/iGeeky_wolf/console/public/favicon.ico", "downloaded_repos/iGeeky_wolf/console/public/index.html", "downloaded_repos/iGeeky_wolf/console/public/wolf-logo.png", "downloaded_repos/iGeeky_wolf/console/src/App.vue", "downloaded_repos/iGeeky_wolf/console/src/api/access-log.js", "downloaded_repos/iGeeky_wolf/console/src/api/application.js", "downloaded_repos/iGeeky_wolf/console/src/api/category.js", "downloaded_repos/iGeeky_wolf/console/src/api/common.js", "downloaded_repos/iGeeky_wolf/console/src/api/permission.js", "downloaded_repos/iGeeky_wolf/console/src/api/resource.js", "downloaded_repos/iGeeky_wolf/console/src/api/role.js", "downloaded_repos/iGeeky_wolf/console/src/api/user-role.js", "downloaded_repos/iGeeky_wolf/console/src/api/user.js", "downloaded_repos/iGeeky_wolf/console/src/assets/401_images/401.gif", "downloaded_repos/iGeeky_wolf/console/src/assets/404_images/404.png", "downloaded_repos/iGeeky_wolf/console/src/assets/404_images/404_cloud.png", "downloaded_repos/iGeeky_wolf/console/src/assets/custom-theme/fonts/element-icons.ttf", "downloaded_repos/iGeeky_wolf/console/src/assets/custom-theme/fonts/element-icons.woff", "downloaded_repos/iGeeky_wolf/console/src/assets/custom-theme/index.css", "downloaded_repos/iGeeky_wolf/console/src/components/Breadcrumb/index.vue", "downloaded_repos/iGeeky_wolf/console/src/components/CurrentApp/index.vue", "downloaded_repos/iGeeky_wolf/console/src/components/DatetimePicker/index.vue", "downloaded_repos/iGeeky_wolf/console/src/components/GithubCorner/index.vue", "downloaded_repos/iGeeky_wolf/console/src/components/Hamburger/index.vue", "downloaded_repos/iGeeky_wolf/console/src/components/HeaderSearch/index.vue", "downloaded_repos/iGeeky_wolf/console/src/components/LangChanger/index.vue", "downloaded_repos/iGeeky_wolf/console/src/components/Pagination/index.vue", "downloaded_repos/iGeeky_wolf/console/src/components/PanThumb/index.vue", "downloaded_repos/iGeeky_wolf/console/src/components/PermissionSelect/index.vue", "downloaded_repos/iGeeky_wolf/console/src/components/PermissionTransfer/index.vue", "downloaded_repos/iGeeky_wolf/console/src/components/RbacDiagram/index.vue", "downloaded_repos/iGeeky_wolf/console/src/components/RightPanel/index.vue", "downloaded_repos/iGeeky_wolf/console/src/components/RoleSelect/index.vue", "downloaded_repos/iGeeky_wolf/console/src/components/Screenfull/index.vue", "downloaded_repos/iGeeky_wolf/console/src/components/SvgIcon/index.vue", "downloaded_repos/iGeeky_wolf/console/src/components/ThemePicker/index.vue", "downloaded_repos/iGeeky_wolf/console/src/directive/clipboard/clipboard.js", "downloaded_repos/iGeeky_wolf/console/src/directive/clipboard/index.js", "downloaded_repos/iGeeky_wolf/console/src/directive/el-drag-dialog/drag.js", "downloaded_repos/iGeeky_wolf/console/src/directive/el-drag-dialog/index.js", "downloaded_repos/iGeeky_wolf/console/src/directive/el-table/adaptive.js", "downloaded_repos/iGeeky_wolf/console/src/directive/el-table/index.js", "downloaded_repos/iGeeky_wolf/console/src/directive/permission/index.js", "downloaded_repos/iGeeky_wolf/console/src/directive/permission/permission.js", "downloaded_repos/iGeeky_wolf/console/src/directive/sticky.js", "downloaded_repos/iGeeky_wolf/console/src/directive/waves/index.js", "downloaded_repos/iGeeky_wolf/console/src/directive/waves/waves.css", "downloaded_repos/iGeeky_wolf/console/src/directive/waves/waves.js", "downloaded_repos/iGeeky_wolf/console/src/filters/index.js", "downloaded_repos/iGeeky_wolf/console/src/i18n/i18n.js", "downloaded_repos/iGeeky_wolf/console/src/i18n/langs/en.js", "downloaded_repos/iGeeky_wolf/console/src/i18n/langs/index.js", "downloaded_repos/iGeeky_wolf/console/src/i18n/langs/zh-cn.js", "downloaded_repos/iGeeky_wolf/console/src/icons/index.js", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/404.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/audit.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/bug.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/chart.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/clipboard.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/component.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/dashboard.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/documentation.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/drag.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/edit.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/education.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/email.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/example.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/excel.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/exit-fullscreen.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/eye-open.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/eye.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/form.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/fullscreen.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/guide.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/icon.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/international.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/language.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/link.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/list.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/lock.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/message.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/money.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/nested.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/password.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/pdf.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/people.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/peoples.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/permission.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/qq.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/refresh.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/resource.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/role.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/search.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/shopping.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/size.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/skill.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/star.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/tab.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/table.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/theme.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/tree-table.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/tree.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/user.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/wechat.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svg/zip.svg", "downloaded_repos/iGeeky_wolf/console/src/icons/svgo.yml", "downloaded_repos/iGeeky_wolf/console/src/layout/components/AppMain.vue", "downloaded_repos/iGeeky_wolf/console/src/layout/components/Navbar.vue", "downloaded_repos/iGeeky_wolf/console/src/layout/components/Settings/index.vue", "downloaded_repos/iGeeky_wolf/console/src/layout/components/Sidebar/FixiOSBug.js", "downloaded_repos/iGeeky_wolf/console/src/layout/components/Sidebar/Item.vue", "downloaded_repos/iGeeky_wolf/console/src/layout/components/Sidebar/Link.vue", "downloaded_repos/iGeeky_wolf/console/src/layout/components/Sidebar/Logo.vue", "downloaded_repos/iGeeky_wolf/console/src/layout/components/Sidebar/SidebarItem.vue", "downloaded_repos/iGeeky_wolf/console/src/layout/components/Sidebar/index.vue", "downloaded_repos/iGeeky_wolf/console/src/layout/components/TagsView/ScrollPane.vue", "downloaded_repos/iGeeky_wolf/console/src/layout/components/TagsView/index.vue", "downloaded_repos/iGeeky_wolf/console/src/layout/components/index.js", "downloaded_repos/iGeeky_wolf/console/src/layout/index.vue", "downloaded_repos/iGeeky_wolf/console/src/layout/mixin/ResizeHandler.js", "downloaded_repos/iGeeky_wolf/console/src/main.js", "downloaded_repos/iGeeky_wolf/console/src/permission.js", "downloaded_repos/iGeeky_wolf/console/src/router/index.js", "downloaded_repos/iGeeky_wolf/console/src/settings.js", "downloaded_repos/iGeeky_wolf/console/src/store/getters.js", "downloaded_repos/iGeeky_wolf/console/src/store/index.js", "downloaded_repos/iGeeky_wolf/console/src/store/modules/app.js", "downloaded_repos/iGeeky_wolf/console/src/store/modules/currentApp.js", "downloaded_repos/iGeeky_wolf/console/src/store/modules/permission.js", "downloaded_repos/iGeeky_wolf/console/src/store/modules/settings.js", "downloaded_repos/iGeeky_wolf/console/src/store/modules/tagsView.js", "downloaded_repos/iGeeky_wolf/console/src/store/modules/user.js", "downloaded_repos/iGeeky_wolf/console/src/styles/btn.scss", "downloaded_repos/iGeeky_wolf/console/src/styles/custom.scss", "downloaded_repos/iGeeky_wolf/console/src/styles/element-ui.scss", "downloaded_repos/iGeeky_wolf/console/src/styles/element-variables.scss", "downloaded_repos/iGeeky_wolf/console/src/styles/index.scss", "downloaded_repos/iGeeky_wolf/console/src/styles/mixin.scss", "downloaded_repos/iGeeky_wolf/console/src/styles/sidebar.scss", "downloaded_repos/iGeeky_wolf/console/src/styles/transition.scss", "downloaded_repos/iGeeky_wolf/console/src/styles/variables.scss", "downloaded_repos/iGeeky_wolf/console/src/utils/auth.js", "downloaded_repos/iGeeky_wolf/console/src/utils/clipboard.js", "downloaded_repos/iGeeky_wolf/console/src/utils/formatter.js", "downloaded_repos/iGeeky_wolf/console/src/utils/get-page-title.js", "downloaded_repos/iGeeky_wolf/console/src/utils/index.js", "downloaded_repos/iGeeky_wolf/console/src/utils/permission.js", "downloaded_repos/iGeeky_wolf/console/src/utils/request.js", "downloaded_repos/iGeeky_wolf/console/src/utils/scroll-to.js", "downloaded_repos/iGeeky_wolf/console/src/utils/validate.js", "downloaded_repos/iGeeky_wolf/console/src/views/access-log/index.vue", "downloaded_repos/iGeeky_wolf/console/src/views/application/index.vue", "downloaded_repos/iGeeky_wolf/console/src/views/category/index.vue", "downloaded_repos/iGeeky_wolf/console/src/views/dashboard/admin/index.vue", "downloaded_repos/iGeeky_wolf/console/src/views/dashboard/editor/index.vue", "downloaded_repos/iGeeky_wolf/console/src/views/dashboard/index.vue", "downloaded_repos/iGeeky_wolf/console/src/views/error-page/401.vue", "downloaded_repos/iGeeky_wolf/console/src/views/error-page/404.vue", "downloaded_repos/iGeeky_wolf/console/src/views/login/auth-redirect.vue", "downloaded_repos/iGeeky_wolf/console/src/views/login/components/SocialSignin.vue", "downloaded_repos/iGeeky_wolf/console/src/views/login/index.vue", "downloaded_repos/iGeeky_wolf/console/src/views/permission/index.vue", "downloaded_repos/iGeeky_wolf/console/src/views/profile/components/Account.vue", "downloaded_repos/iGeeky_wolf/console/src/views/profile/components/Activity.vue", "downloaded_repos/iGeeky_wolf/console/src/views/profile/components/Timeline.vue", "downloaded_repos/iGeeky_wolf/console/src/views/profile/components/UserCard.vue", "downloaded_repos/iGeeky_wolf/console/src/views/profile/index.vue", "downloaded_repos/iGeeky_wolf/console/src/views/redirect/index.vue", "downloaded_repos/iGeeky_wolf/console/src/views/resource/index.vue", "downloaded_repos/iGeeky_wolf/console/src/views/role/index.vue", "downloaded_repos/iGeeky_wolf/console/src/views/theme/index.vue", "downloaded_repos/iGeeky_wolf/console/src/views/user/index.vue", "downloaded_repos/iGeeky_wolf/console/src/views/user/roleDetail.vue", "downloaded_repos/iGeeky_wolf/console/vue.config.js", "downloaded_repos/iGeeky_wolf/docs/admin-api-cn.md", "downloaded_repos/iGeeky_wolf/docs/admin-api-oauth2.0-cn.md", "downloaded_repos/iGeeky_wolf/docs/admin-api-oauth2.0.md", "downloaded_repos/iGeeky_wolf/docs/admin-api.md", "downloaded_repos/iGeeky_wolf/docs/deploy-cn.md", "downloaded_repos/iGeeky_wolf/docs/deploy.md", "downloaded_repos/iGeeky_wolf/docs/imgs/architecture.png", "downloaded_repos/iGeeky_wolf/docs/imgs/data-model.png", "downloaded_repos/iGeeky_wolf/docs/imgs/screenshot/client/login-status.png", "downloaded_repos/iGeeky_wolf/docs/imgs/screenshot/client/login.png", "downloaded_repos/iGeeky_wolf/docs/imgs/screenshot/client/main.png", "downloaded_repos/iGeeky_wolf/docs/imgs/screenshot/client/no-permission.png", "downloaded_repos/iGeeky_wolf/docs/imgs/screenshot/console/application-diagram.png", "downloaded_repos/iGeeky_wolf/docs/imgs/screenshot/console/application.png", "downloaded_repos/iGeeky_wolf/docs/imgs/screenshot/console/audit-log.png", "downloaded_repos/iGeeky_wolf/docs/imgs/screenshot/console/login.png", "downloaded_repos/iGeeky_wolf/docs/imgs/screenshot/console/permission-detail.png", "downloaded_repos/iGeeky_wolf/docs/imgs/screenshot/console/permission.png", "downloaded_repos/iGeeky_wolf/docs/imgs/screenshot/console/resource-edit.png", "downloaded_repos/iGeeky_wolf/docs/imgs/screenshot/console/resource.png", "downloaded_repos/iGeeky_wolf/docs/imgs/screenshot/console/role.png", "downloaded_repos/iGeeky_wolf/docs/imgs/screenshot/console/user.png", "downloaded_repos/iGeeky_wolf/docs/imgs/screenshot/coverage-detail.png", "downloaded_repos/iGeeky_wolf/docs/imgs/screenshot/coverage-overview.png", "downloaded_repos/iGeeky_wolf/docs/imgs/wolf-logo.jpg", "downloaded_repos/iGeeky_wolf/docs/imgs/wolf-logo.png", "downloaded_repos/iGeeky_wolf/docs/ldap-config-cn.md", "downloaded_repos/iGeeky_wolf/docs/ldap-config.md", "downloaded_repos/iGeeky_wolf/docs/perf-cn.md", "downloaded_repos/iGeeky_wolf/docs/perf.md", "downloaded_repos/iGeeky_wolf/docs/unittest-cn.md", "downloaded_repos/iGeeky_wolf/docs/unittest.md", "downloaded_repos/iGeeky_wolf/docs/usage-cn.md", "downloaded_repos/iGeeky_wolf/docs/usage.md", "downloaded_repos/iGeeky_wolf/quick-start-with-docker/.env", "downloaded_repos/iGeeky_wolf/quick-start-with-docker/README-CN.md", "downloaded_repos/iGeeky_wolf/quick-start-with-docker/README-K8S-CN.md", "downloaded_repos/iGeeky_wolf/quick-start-with-docker/README-K8S.md", "downloaded_repos/iGeeky_wolf/quick-start-with-docker/README.md", "downloaded_repos/iGeeky_wolf/quick-start-with-docker/docker-compose.yaml", "downloaded_repos/iGeeky_wolf/quick-start-with-docker/k8s/wolf-cache.yaml", "downloaded_repos/iGeeky_wolf/quick-start-with-docker/k8s/wolf-database.yaml", "downloaded_repos/iGeeky_wolf/quick-start-with-docker/k8s/wolf-server.yaml", "downloaded_repos/iGeeky_wolf/quick-start-with-docker/wolf-demo-init.sh", "downloaded_repos/iGeeky_wolf/server/.babelrc", "downloaded_repos/iGeeky_wolf/server/.dockerignore", "downloaded_repos/iGeeky_wolf/server/.eslintrc.js", "downloaded_repos/iGeeky_wolf/server/Dockerfile", "downloaded_repos/iGeeky_wolf/server/DockerfileQuickly", "downloaded_repos/iGeeky_wolf/server/DockerfileTB", "downloaded_repos/iGeeky_wolf/server/README.md", "downloaded_repos/iGeeky_wolf/server/app.js", "downloaded_repos/iGeeky_wolf/server/conf/config.js", "downloaded_repos/iGeeky_wolf/server/conf/langs/en.js", "downloaded_repos/iGeeky_wolf/server/conf/langs/zh-CN.js", "downloaded_repos/iGeeky_wolf/server/conf/log4js.json", "downloaded_repos/iGeeky_wolf/server/entrypoint.sh", "downloaded_repos/iGeeky_wolf/server/jsconfig.json", "downloaded_repos/iGeeky_wolf/server/package.json", "downloaded_repos/iGeeky_wolf/server/script/db-mysql-upgrade.sql", "downloaded_repos/iGeeky_wolf/server/script/db-mysql.sql", "downloaded_repos/iGeeky_wolf/server/script/db-psql-upgrade.sql", "downloaded_repos/iGeeky_wolf/server/script/db-psql.sql", "downloaded_repos/iGeeky_wolf/server/src/controllers/access-log.js", "downloaded_repos/iGeeky_wolf/server/src/controllers/application.js", "downloaded_repos/iGeeky_wolf/server/src/controllers/basic-service.js", "downloaded_repos/iGeeky_wolf/server/src/controllers/captcha.js", "downloaded_repos/iGeeky_wolf/server/src/controllers/category.js", "downloaded_repos/iGeeky_wolf/server/src/controllers/error-test.js", "downloaded_repos/iGeeky_wolf/server/src/controllers/helper.js", "downloaded_repos/iGeeky_wolf/server/src/controllers/oauth2.js", "downloaded_repos/iGeeky_wolf/server/src/controllers/permission.js", "downloaded_repos/iGeeky_wolf/server/src/controllers/rbac-pub.js", "downloaded_repos/iGeeky_wolf/server/src/controllers/rbac.js", "downloaded_repos/iGeeky_wolf/server/src/controllers/resource.js", "downloaded_repos/iGeeky_wolf/server/src/controllers/role.js", "downloaded_repos/iGeeky_wolf/server/src/controllers/user-role.js", "downloaded_repos/iGeeky_wolf/server/src/controllers/user.js", "downloaded_repos/iGeeky_wolf/server/src/errors/access-deny-error.js", "downloaded_repos/iGeeky_wolf/server/src/errors/args-error.js", "downloaded_repos/iGeeky_wolf/server/src/errors/backend-error.js", "downloaded_repos/iGeeky_wolf/server/src/errors/data-exist-error.js", "downloaded_repos/iGeeky_wolf/server/src/errors/data-not-found-error.js", "downloaded_repos/iGeeky_wolf/server/src/errors/errors.js", "downloaded_repos/iGeeky_wolf/server/src/errors/method-invalid-error.js", "downloaded_repos/iGeeky_wolf/server/src/errors/rbac-token-error.js", "downloaded_repos/iGeeky_wolf/server/src/errors/token-error.js", "downloaded_repos/iGeeky_wolf/server/src/ldap/LDAPClient.js", "downloaded_repos/iGeeky_wolf/server/src/ldap/LDAPMockServer.js", "downloaded_repos/iGeeky_wolf/server/src/middlewares/access-log.js", "downloaded_repos/iGeeky_wolf/server/src/middlewares/error-catch.js", "downloaded_repos/iGeeky_wolf/server/src/middlewares/rbac-token-check.js", "downloaded_repos/iGeeky_wolf/server/src/middlewares/token-check.js", "downloaded_repos/iGeeky_wolf/server/src/model/access-log.js", "downloaded_repos/iGeeky_wolf/server/src/model/application.js", "downloaded_repos/iGeeky_wolf/server/src/model/category.js", "downloaded_repos/iGeeky_wolf/server/src/model/oauth2.js", "downloaded_repos/iGeeky_wolf/server/src/model/permission.js", "downloaded_repos/iGeeky_wolf/server/src/model/resource.js", "downloaded_repos/iGeeky_wolf/server/src/model/role.js", "downloaded_repos/iGeeky_wolf/server/src/model/user-role.js", "downloaded_repos/iGeeky_wolf/server/src/model/user.js", "downloaded_repos/iGeeky_wolf/server/src/routes/router.js", "downloaded_repos/iGeeky_wolf/server/src/service/resource-cache.js", "downloaded_repos/iGeeky_wolf/server/src/service/service.js", "downloaded_repos/iGeeky_wolf/server/src/service/user-cache.js", "downloaded_repos/iGeeky_wolf/server/src/util/args-util.js", "downloaded_repos/iGeeky_wolf/server/src/util/captcha-util.js", "downloaded_repos/iGeeky_wolf/server/src/util/chksum.js", "downloaded_repos/iGeeky_wolf/server/src/util/constant.js", "downloaded_repos/iGeeky_wolf/server/src/util/cryptor.js", "downloaded_repos/iGeeky_wolf/server/src/util/init-root-user.js", "downloaded_repos/iGeeky_wolf/server/src/util/log4js.js", "downloaded_repos/iGeeky_wolf/server/src/util/oauth-util.js", "downloaded_repos/iGeeky_wolf/server/src/util/ok-json.js", "downloaded_repos/iGeeky_wolf/server/src/util/op-util.js", "downloaded_repos/iGeeky_wolf/server/src/util/query-util.js", "downloaded_repos/iGeeky_wolf/server/src/util/radixtree.js", "downloaded_repos/iGeeky_wolf/server/src/util/redis-util.js", "downloaded_repos/iGeeky_wolf/server/src/util/sequelize.js", "downloaded_repos/iGeeky_wolf/server/src/util/token-util.js", "downloaded_repos/iGeeky_wolf/server/src/util/type-util.js", "downloaded_repos/iGeeky_wolf/server/src/util/util.js", "downloaded_repos/iGeeky_wolf/server/src/util/wolf-cache.js", "downloaded_repos/iGeeky_wolf/server/tmpl/change_pwd.html", "downloaded_repos/iGeeky_wolf/server/tmpl/login.html", "downloaded_repos/iGeeky_wolf/server/tmpl/login_status.html", "downloaded_repos/iGeeky_wolf/server/tmpl/no_permission.html", "downloaded_repos/iGeeky_wolf/server/tmpl/oauth2/clientApp.html", "downloaded_repos/iGeeky_wolf/server/tmpl/oauth2/clientAuthenticate.html", "downloaded_repos/iGeeky_wolf/server/tools/password.js", "downloaded_repos/iGeeky_wolf/vetur.config.js"], "skipped": [{"path": "downloaded_repos/iGeeky_wolf/console/build/index.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/iGeeky_wolf/console/public/index.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/iGeeky_wolf/server/test/00.framework.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/iGeeky_wolf/server/test/01.util.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/iGeeky_wolf/server/test/02.method-invalid-error.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/iGeeky_wolf/server/test/10.application.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/iGeeky_wolf/server/test/20.user.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/iGeeky_wolf/server/test/22.user-ldap.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/iGeeky_wolf/server/test/24.captcha.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/iGeeky_wolf/server/test/30.category.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/iGeeky_wolf/server/test/40.permission.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/iGeeky_wolf/server/test/50.resource.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/iGeeky_wolf/server/test/60.role.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/iGeeky_wolf/server/test/70.user-role.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/iGeeky_wolf/server/test/80.access-log.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/iGeeky_wolf/server/test/90.rbac-agent.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/iGeeky_wolf/server/test/95.oauth2.test.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/iGeeky_wolf/server/test/init/0-rbac-data-demo.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/iGeeky_wolf/server/test/init/0-rbac-data-or.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/iGeeky_wolf/server/test/init/0-rbac-data-restful.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/iGeeky_wolf/server/test/init/0-rbac-data-unittest.md", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/iGeeky_wolf/server/test/init/0-rbac-destroy.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/iGeeky_wolf/server/test/init/0-rbac-init.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/iGeeky_wolf/server/test/init/0-rbac-util.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/iGeeky_wolf/server/test/init/md2json.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/iGeeky_wolf/server/test/init/rbac-data-parser.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/iGeeky_wolf/server/test/init/user-default.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/iGeeky_wolf/server/test/monitor/api-monitor.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/iGeeky_wolf/server/test/perf.lua", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/iGeeky_wolf/server/test/util/json.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/iGeeky_wolf/server/test/util/ldap-mock-server.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/iGeeky_wolf/server/test/util/mocha.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/iGeeky_wolf/server/test/util/schema-gen.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/iGeeky_wolf/server/test/util/util.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/iGeeky_wolf/server/tmpl/login_status.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/iGeeky_wolf/server/tmpl/no_permission.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/iGeeky_wolf/server/tmpl/oauth2/clientAuthenticate.html", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 2.0723118782043457, "profiling_times": {"config_time": 9.958216905593872, "core_time": 4.806398391723633, "ignores_time": 0.0020351409912109375, "total_time": 14.767699003219604}, "parsing_time": {"total_time": 2.5054142475128174, "per_file_time": {"mean": 0.01659214733452197, "std_dev": 0.000645458782430228}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 15.201123714447021, "per_file_time": {"mean": 0.01779991067265461, "std_dev": 0.005807347024868493}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 7.105112075805664, "per_file_and_rule_time": {"mean": 0.009062642953833753, "std_dev": 0.0005581028206401228}, "very_slow_stats": {"time_ratio": 0.2659139290627831, "count_ratio": 0.015306122448979591}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/iGeeky_wolf/server/app.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.12886500358581543}, {"fpath": "downloaded_repos/iGeeky_wolf/server/src/controllers/rbac.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.13227605819702148}, {"fpath": "downloaded_repos/iGeeky_wolf/server/src/controllers/rbac.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.13288307189941406}, {"fpath": "downloaded_repos/iGeeky_wolf/server/src/controllers/user.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.1348741054534912}, {"fpath": "downloaded_repos/iGeeky_wolf/server/src/controllers/oauth2.js", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.14998698234558105}, {"fpath": "downloaded_repos/iGeeky_wolf/server/src/controllers/user.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.15362215042114258}, {"fpath": "downloaded_repos/iGeeky_wolf/server/src/controllers/oauth2.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.17452192306518555}, {"fpath": "downloaded_repos/iGeeky_wolf/server/src/controllers/basic-service.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.1776740550994873}, {"fpath": "downloaded_repos/iGeeky_wolf/server/src/model/oauth2.js", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.2401878833770752}, {"fpath": "downloaded_repos/iGeeky_wolf/console/src/utils/index.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.24675607681274414}]}, "tainting_time": {"total_time": 1.8210043907165527, "per_def_and_rule_time": {"mean": 0.0009645150374558015, "std_dev": 4.396701374839276e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089476480}, "engine_requested": "OSS", "skipped_rules": []}