{"version": "1.130.0", "results": [{"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/always-open_laravel-totem/docker-compose.yml", "start": {"line": 3, "col": 3, "offset": 27}, "end": {"line": 3, "col": 6, "offset": 30}, "extra": {"message": "Service 'app' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/always-open_laravel-totem/docker-compose.yml", "start": {"line": 3, "col": 3, "offset": 27}, "end": {"line": 3, "col": 6, "offset": 30}, "extra": {"message": "Service 'app' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.no-new-privileges.no-new-privileges", "path": "downloaded_repos/always-open_laravel-totem/docker-compose.yml", "start": {"line": 16, "col": 3, "offset": 275}, "end": {"line": 16, "col": 5, "offset": 277}, "extra": {"message": "Service 'db' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://raesene.github.io/blog/2019/06/01/docker-capabilities-and-no-new-privs/", "https://www.kernel.org/doc/Documentation/prctl/no_new_privs.txt", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-4-add-no-new-privileges-flag"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.no-new-privileges.no-new-privileges", "shortlink": "https://sg.run/0n8q"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "path": "downloaded_repos/always-open_laravel-totem/docker-compose.yml", "start": {"line": 16, "col": 3, "offset": 275}, "end": {"line": 16, "col": 5, "offset": 277}, "extra": {"message": "Service 'db' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this.", "metadata": {"cwe": ["CWE-732: Incorrect Permission Assignment for Critical Resource"], "owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "references": ["https://docs.docker.com/compose/compose-file/compose-file-v3/#domainname-hostname-ipc-mac_address-privileged-read_only-shm_size-stdin_open-tty-user-working_dir", "https://blog.atomist.com/security-of-docker-kubernetes/", "https://cheatsheetseries.owasp.org/cheatsheets/Docker_Security_Cheat_Sheet.html#rule-8-set-filesystem-and-volumes-to-read-only"], "category": "security", "technology": ["docker-compose"], "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service", "shortlink": "https://sg.run/e4JE"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "path": "downloaded_repos/always-open_laravel-totem/resources/assets/js/tasks/components/TaskOutput.vue", "start": {"line": 11, "col": 17, "offset": 416}, "end": {"line": 11, "col": 29, "offset": 428}, "extra": {"message": "Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content.", "metadata": {"references": ["https://vuejs.org/v2/guide/syntax.html#Raw-HTML"], "category": "security", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "technology": ["vue"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html", "shortlink": "https://sg.run/0QEw"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.aws-lambda.security.tainted-html-string.tainted-html-string", "message": "Timeout when running javascript.aws-lambda.security.tainted-html-string.tainted-html-string on downloaded_repos/always-open_laravel-totem/public/js/app.js:\n ", "path": "downloaded_repos/always-open_laravel-totem/public/js/app.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "message": "Timeout when running javascript.express.security.audit.express-ssrf.express-ssrf on downloaded_repos/always-open_laravel-totem/public/js/app.js:\n ", "path": "downloaded_repos/always-open_laravel-totem/public/js/app.js"}, {"code": 2, "level": "warn", "type": "Timeout", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "message": "Timeout when running javascript.express.security.express-insecure-template-usage.express-insecure-template-usage on downloaded_repos/always-open_laravel-totem/public/js/app.js:\n ", "path": "downloaded_repos/always-open_laravel-totem/public/js/app.js"}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/always-open_laravel-totem/.github/workflows/laravel.yml", "start": {"line": 33, "col": 51, "offset": 1026}, "end": {"line": 33, "col": 67, "offset": 1042}}, {"path": "downloaded_repos/always-open_laravel-totem/.github/workflows/laravel.yml", "start": {"line": 34, "col": 19, "offset": 1026}, "end": {"line": 34, "col": 22, "offset": 1029}}]], "message": "Syntax error at line downloaded_repos/always-open_laravel-totem/.github/workflows/laravel.yml:33:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ matrix.laravel` was unexpected", "path": "downloaded_repos/always-open_laravel-totem/.github/workflows/laravel.yml", "spans": [{"file": "downloaded_repos/always-open_laravel-totem/.github/workflows/laravel.yml", "start": {"line": 33, "col": 51, "offset": 1026}, "end": {"line": 33, "col": 67, "offset": 1042}}, {"file": "downloaded_repos/always-open_laravel-totem/.github/workflows/laravel.yml", "start": {"line": 34, "col": 19, "offset": 1026}, "end": {"line": 34, "col": 22, "offset": 1029}}]}], "paths": {"scanned": ["downloaded_repos/always-open_laravel-totem/.editorconfig", "downloaded_repos/always-open_laravel-totem/.gitattributes", "downloaded_repos/always-open_laravel-totem/.github/stale.yml", "downloaded_repos/always-open_laravel-totem/.github/workflows/laravel.yml", "downloaded_repos/always-open_laravel-totem/.gitignore", "downloaded_repos/always-open_laravel-totem/.styleci.yml", "downloaded_repos/always-open_laravel-totem/.travis.yml", "downloaded_repos/always-open_laravel-totem/CHANGELOG.md", "downloaded_repos/always-open_laravel-totem/CODE_OF_CONDUCT.md", "downloaded_repos/always-open_laravel-totem/CONTRIBUTING.md", "downloaded_repos/always-open_laravel-totem/LICENSE", "downloaded_repos/always-open_laravel-totem/README.md", "downloaded_repos/always-open_laravel-totem/composer.json", "downloaded_repos/always-open_laravel-totem/config/totem.php", "downloaded_repos/always-open_laravel-totem/database/factories/TotemResultFactory.php", "downloaded_repos/always-open_laravel-totem/database/factories/TotemTaskFactory.php", "downloaded_repos/always-open_laravel-totem/database/factories/TotemUserFactory.php", "downloaded_repos/always-open_laravel-totem/database/migrations/2017_08_05_194349_create_tasks_table.php", "downloaded_repos/always-open_laravel-totem/database/migrations/2017_08_05_195539_create_task_frequencies_table.php", "downloaded_repos/always-open_laravel-totem/database/migrations/2017_08_05_201914_create_task_results_table.php", "downloaded_repos/always-open_laravel-totem/database/migrations/2017_08_24_085132_create_frequency_parameters_table.php", "downloaded_repos/always-open_laravel-totem/database/migrations/2017_08_26_083622_alter_tasks_table_add_notifications_fields.php", "downloaded_repos/always-open_laravel-totem/database/migrations/2018_01_02_121533_alter_tasks_table_add_auto_cleanup_num_and_type_fields.php", "downloaded_repos/always-open_laravel-totem/database/migrations/2018_07_03_120000_alter_tasks_table_add_run_on_one_server_support.php", "downloaded_repos/always-open_laravel-totem/database/migrations/2018_07_06_165603_add_indexes_for_tasks.php", "downloaded_repos/always-open_laravel-totem/database/migrations/2019_09_25_103421_update_task_results_duration_type.php", "downloaded_repos/always-open_laravel-totem/database/migrations/2020_12_10_120000_alter_tasks_table_add_run_in_background_support.php", "downloaded_repos/always-open_laravel-totem/database/migrations/2022_03_14_120000_alter_task_results_table_add_index_on_created_at.php", "downloaded_repos/always-open_laravel-totem/docker/Dockerfile", "downloaded_repos/always-open_laravel-totem/docker/mysql/init_db.sql", "downloaded_repos/always-open_laravel-totem/docker-compose.yml", "downloaded_repos/always-open_laravel-totem/gulpfile.js", "downloaded_repos/always-open_laravel-totem/package.json", "downloaded_repos/always-open_laravel-totem/phpunit.xml", "downloaded_repos/always-open_laravel-totem/public/css/app.css", "downloaded_repos/always-open_laravel-totem/public/css/app.css.map", "downloaded_repos/always-open_laravel-totem/public/img/button-text-arrow.svg", "downloaded_repos/always-open_laravel-totem/public/img/divider-icon.svg", "downloaded_repos/always-open_laravel-totem/public/img/form-select.svg", "downloaded_repos/always-open_laravel-totem/public/img/funnel.svg", "downloaded_repos/always-open_laravel-totem/public/img/icons/clock.svg", "downloaded_repos/always-open_laravel-totem/public/img/icons/close.svg", "downloaded_repos/always-open_laravel-totem/public/img/icons/cog.svg", "downloaded_repos/always-open_laravel-totem/public/img/icons/mask.svg", "downloaded_repos/always-open_laravel-totem/public/img/icons/more.svg", "downloaded_repos/always-open_laravel-totem/public/img/icons/play-circle.svg", "downloaded_repos/always-open_laravel-totem/public/img/icons/play.svg", "downloaded_repos/always-open_laravel-totem/public/img/icons/search.svg", "downloaded_repos/always-open_laravel-totem/public/img/icons/spinner.svg", "downloaded_repos/always-open_laravel-totem/public/img/icons/test.svg", "downloaded_repos/always-open_laravel-totem/public/img/list-bullet.svg", "downloaded_repos/always-open_laravel-totem/public/img/mask.svg", "downloaded_repos/always-open_laravel-totem/public/img/totem.png", "downloaded_repos/always-open_laravel-totem/public/img/totem.svg", "downloaded_repos/always-open_laravel-totem/public/js/app.js", "downloaded_repos/always-open_laravel-totem/resources/assets/img/funnel.svg", "downloaded_repos/always-open_laravel-totem/resources/assets/img/icons/clock.svg", "downloaded_repos/always-open_laravel-totem/resources/assets/img/icons/close.svg", "downloaded_repos/always-open_laravel-totem/resources/assets/img/icons/cog.svg", "downloaded_repos/always-open_laravel-totem/resources/assets/img/icons/mask.svg", "downloaded_repos/always-open_laravel-totem/resources/assets/img/icons/more.svg", "downloaded_repos/always-open_laravel-totem/resources/assets/img/icons/play.svg", "downloaded_repos/always-open_laravel-totem/resources/assets/img/icons/search.svg", "downloaded_repos/always-open_laravel-totem/resources/assets/img/icons/spinner.svg", "downloaded_repos/always-open_laravel-totem/resources/assets/img/mask.svg", "downloaded_repos/always-open_laravel-totem/resources/assets/img/new-logos/inverted-logo.svg", "downloaded_repos/always-open_laravel-totem/resources/assets/img/new-logos/inverted-variations.svg", "downloaded_repos/always-open_laravel-totem/resources/assets/img/new-logos/logo.svg", "downloaded_repos/always-open_laravel-totem/resources/assets/img/new-logos/variations.svg", "downloaded_repos/always-open_laravel-totem/resources/assets/img/totem.png", "downloaded_repos/always-open_laravel-totem/resources/assets/img/totem.svg", "downloaded_repos/always-open_laravel-totem/resources/assets/js/app.js", "downloaded_repos/always-open_laravel-totem/resources/assets/js/bootstrap.js", "downloaded_repos/always-open_laravel-totem/resources/assets/js/components/ClickToClose.vue", "downloaded_repos/always-open_laravel-totem/resources/assets/js/components/UIKitAlert.vue", "downloaded_repos/always-open_laravel-totem/resources/assets/js/components/UIKitModal.vue", "downloaded_repos/always-open_laravel-totem/resources/assets/js/tasks/components/CommandList.vue", "downloaded_repos/always-open_laravel-totem/resources/assets/js/tasks/components/ExecuteButton.vue", "downloaded_repos/always-open_laravel-totem/resources/assets/js/tasks/components/ImportButton.vue", "downloaded_repos/always-open_laravel-totem/resources/assets/js/tasks/components/StatusButton.vue", "downloaded_repos/always-open_laravel-totem/resources/assets/js/tasks/components/TaskOutput.vue", "downloaded_repos/always-open_laravel-totem/resources/assets/js/tasks/components/TaskRow.vue", "downloaded_repos/always-open_laravel-totem/resources/assets/js/tasks/components/TaskType.vue", "downloaded_repos/always-open_laravel-totem/resources/assets/less/img/button-text-arrow.svg", "downloaded_repos/always-open_laravel-totem/resources/assets/less/img/divider-icon.svg", "downloaded_repos/always-open_laravel-totem/resources/assets/less/img/form-select.svg", "downloaded_repos/always-open_laravel-totem/resources/assets/less/img/list-bullet.svg", "downloaded_repos/always-open_laravel-totem/resources/assets/less/totem/_import.less", "downloaded_repos/always-open_laravel-totem/resources/assets/less/totem/alert.less", "downloaded_repos/always-open_laravel-totem/resources/assets/less/totem/base.less", "downloaded_repos/always-open_laravel-totem/resources/assets/less/totem/button.less", "downloaded_repos/always-open_laravel-totem/resources/assets/less/totem/card.less", "downloaded_repos/always-open_laravel-totem/resources/assets/less/totem/close.less", "downloaded_repos/always-open_laravel-totem/resources/assets/less/totem/container.less", "downloaded_repos/always-open_laravel-totem/resources/assets/less/totem/divider.less", "downloaded_repos/always-open_laravel-totem/resources/assets/less/totem/form.less", "downloaded_repos/always-open_laravel-totem/resources/assets/less/totem/heading.less", "downloaded_repos/always-open_laravel-totem/resources/assets/less/totem/list.less", "downloaded_repos/always-open_laravel-totem/resources/assets/less/totem/nav.less", "downloaded_repos/always-open_laravel-totem/resources/assets/less/totem/pagination.less", "downloaded_repos/always-open_laravel-totem/resources/assets/less/totem/search.less", "downloaded_repos/always-open_laravel-totem/resources/assets/less/totem/section.less", "downloaded_repos/always-open_laravel-totem/resources/assets/less/totem/table.less", "downloaded_repos/always-open_laravel-totem/resources/assets/less/totem/text.less", "downloaded_repos/always-open_laravel-totem/resources/assets/less/totem/variables.less", "downloaded_repos/always-open_laravel-totem/resources/assets/less/totem.less", "downloaded_repos/always-open_laravel-totem/resources/assets/less/utilities/align.less", "downloaded_repos/always-open_laravel-totem/resources/lang/en/messages.php", "downloaded_repos/always-open_laravel-totem/resources/views/dialogs/frequencies/add.blade.php", "downloaded_repos/always-open_laravel-totem/resources/views/layout.blade.php", "downloaded_repos/always-open_laravel-totem/resources/views/partials/alerts.blade.php", "downloaded_repos/always-open_laravel-totem/resources/views/partials/footer.blade.php", "downloaded_repos/always-open_laravel-totem/resources/views/partials/pagination.blade.php", "downloaded_repos/always-open_laravel-totem/resources/views/partials/sidebar.blade.php", "downloaded_repos/always-open_laravel-totem/resources/views/tasks/form.blade.php", "downloaded_repos/always-open_laravel-totem/resources/views/tasks/index.blade.php", "downloaded_repos/always-open_laravel-totem/resources/views/tasks/view.blade.php", "downloaded_repos/always-open_laravel-totem/routes/api.php", "downloaded_repos/always-open_laravel-totem/routes/web.php", "downloaded_repos/always-open_laravel-totem/src/Console/Commands/ListSchedule.php", "downloaded_repos/always-open_laravel-totem/src/Console/Commands/PublishAssets.php", "downloaded_repos/always-open_laravel-totem/src/Contracts/TaskInterface.php", "downloaded_repos/always-open_laravel-totem/src/Database/TotemMigration.php", "downloaded_repos/always-open_laravel-totem/src/Events/Activated.php", "downloaded_repos/always-open_laravel-totem/src/Events/BroadcastingEvent.php", "downloaded_repos/always-open_laravel-totem/src/Events/Created.php", "downloaded_repos/always-open_laravel-totem/src/Events/Creating.php", "downloaded_repos/always-open_laravel-totem/src/Events/Deactivated.php", "downloaded_repos/always-open_laravel-totem/src/Events/Deleted.php", "downloaded_repos/always-open_laravel-totem/src/Events/Deleting.php", "downloaded_repos/always-open_laravel-totem/src/Events/Event.php", "downloaded_repos/always-open_laravel-totem/src/Events/Executed.php", "downloaded_repos/always-open_laravel-totem/src/Events/Executing.php", "downloaded_repos/always-open_laravel-totem/src/Events/TaskEvent.php", "downloaded_repos/always-open_laravel-totem/src/Events/Updated.php", "downloaded_repos/always-open_laravel-totem/src/Events/Updating.php", "downloaded_repos/always-open_laravel-totem/src/Frequency.php", "downloaded_repos/always-open_laravel-totem/src/Http/Controllers/ActiveTasksController.php", "downloaded_repos/always-open_laravel-totem/src/Http/Controllers/Controller.php", "downloaded_repos/always-open_laravel-totem/src/Http/Controllers/DashboardController.php", "downloaded_repos/always-open_laravel-totem/src/Http/Controllers/ExecuteTasksController.php", "downloaded_repos/always-open_laravel-totem/src/Http/Controllers/ExportTasksController.php", "downloaded_repos/always-open_laravel-totem/src/Http/Controllers/ImportTasksController.php", "downloaded_repos/always-open_laravel-totem/src/Http/Controllers/TasksController.php", "downloaded_repos/always-open_laravel-totem/src/Http/Middleware/Authenticate.php", "downloaded_repos/always-open_laravel-totem/src/Http/Requests/ImportRequest.php", "downloaded_repos/always-open_laravel-totem/src/Http/Requests/TaskRequest.php", "downloaded_repos/always-open_laravel-totem/src/Listeners/BuildCache.php", "downloaded_repos/always-open_laravel-totem/src/Listeners/BustCache.php", "downloaded_repos/always-open_laravel-totem/src/Listeners/BustCacheImmediately.php", "downloaded_repos/always-open_laravel-totem/src/Listeners/Listener.php", "downloaded_repos/always-open_laravel-totem/src/Notifications/TaskCompleted.php", "downloaded_repos/always-open_laravel-totem/src/Parameter.php", "downloaded_repos/always-open_laravel-totem/src/Providers/ConsoleServiceProvider.php", "downloaded_repos/always-open_laravel-totem/src/Providers/TotemEventServiceProvider.php", "downloaded_repos/always-open_laravel-totem/src/Providers/TotemRouteServiceProvider.php", "downloaded_repos/always-open_laravel-totem/src/Providers/TotemServiceProvider.php", "downloaded_repos/always-open_laravel-totem/src/Repositories/EloquentTaskRepository.php", "downloaded_repos/always-open_laravel-totem/src/Result.php", "downloaded_repos/always-open_laravel-totem/src/Task.php", "downloaded_repos/always-open_laravel-totem/src/Totem.php", "downloaded_repos/always-open_laravel-totem/src/TotemModel.php", "downloaded_repos/always-open_laravel-totem/src/Traits/FrontendSortable.php", "downloaded_repos/always-open_laravel-totem/src/Traits/HasFrequencies.php", "downloaded_repos/always-open_laravel-totem/src/Traits/HasParameters.php", "downloaded_repos/always-open_laravel-totem/src/User.php", "downloaded_repos/always-open_laravel-totem/src/helpers.php"], "skipped": [{"path": "downloaded_repos/always-open_laravel-totem/.github/workflows/laravel.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/always-open_laravel-totem/public/js/app.js", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/always-open_laravel-totem/tests/Feature/AuthTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/always-open_laravel-totem/tests/Feature/CompileParametersTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/always-open_laravel-totem/tests/Feature/CreateTaskTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/always-open_laravel-totem/tests/Feature/EditTaskTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/always-open_laravel-totem/tests/Feature/ExportTasksTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/always-open_laravel-totem/tests/Feature/ImportTasksTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/always-open_laravel-totem/tests/Feature/TaskExecutionTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/always-open_laravel-totem/tests/Feature/ViewDashboardTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/always-open_laravel-totem/tests/Feature/ViewTaskTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/always-open_laravel-totem/tests/Fixtures/tasks.json", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/always-open_laravel-totem/tests/TestCase.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.7092549800872803, "profiling_times": {"config_time": 5.998380661010742, "core_time": 8.243064880371094, "ignores_time": 0.001615285873413086, "total_time": 14.244622945785522}, "parsing_time": {"total_time": 0.38559627532958984, "per_file_time": {"mean": 0.004483677620111509, "std_dev": 5.63885374131607e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 7.483205556869507, "per_file_time": {"mean": 0.017774835051946585, "std_dev": 0.0695642690706016}, "very_slow_stats": {"time_ratio": 0.7228612285550641, "count_ratio": 0.0023752969121140144}, "very_slow_files": [{"fpath": "downloaded_repos/always-open_laravel-totem/public/js/app.js", "ftime": 5.409319162368774}]}, "matching_time": {"total_time": 0.1405656337738037, "per_file_and_rule_time": {"mean": 0.0005304363538634103, "std_dev": 2.1861860789471785e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.0019350051879882812, "per_def_and_rule_time": {"mean": 0.00027642931256975444, "std_dev": 2.254227861065337e-08}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}