#!/usr/bin/env python3

"""
Fix and Run Container - Quick fix for CSV field error
"""

import os
import sys
import subprocess
import time

def main():
    print("🔧 FIXING AND RUNNING CONTAINER")
    print("=" * 40)
    print("Fixing CSV field error and rebuilding container")
    print()
    
    # Check GitHub token
    if not os.getenv("GITHUB_TOKEN"):
        print("❌ Please set GITHUB_TOKEN environment variable")
        print("   export GITHUB_TOKEN='your_token_here'")
        sys.exit(1)
    
    print("✅ GitHub token is set")
    
    # Rebuild container with fixed code
    print("\n🔨 Rebuilding Docker container with fixes...")
    try:
        cmd = ["docker", "build", "-t", "unified-vuln-scanner", "--no-cache", "."]
        result = subprocess.run(cmd, check=True)
        print("✅ Container rebuilt successfully")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to rebuild container: {e}")
        sys.exit(1)
    
    # Create output directory
    os.makedirs("docker_output", exist_ok=True)
    print("📁 Output directory ready")
    
    # Run container with test parameters
    print("\n🚀 Running fixed container with test scan...")
    try:
        cmd = [
            "docker", "run", "--rm",
            "-e", f"GITHUB_TOKEN={os.getenv('GITHUB_TOKEN')}",
            "-v", f"{os.getcwd()}/docker_output:/app/output",
            "unified-vuln-scanner",
            "--max-repos", "20",  # Small test
            "--pages", "2",
            "--concurrent", "1"
        ]
        
        print(f"Command: {' '.join(cmd[:8])}...")
        start_time = time.time()
        
        result = subprocess.run(cmd, check=True)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n✅ Container scan completed successfully in {duration:.1f} seconds")
        
        # Check results
        results_file = "docker_output/unified_vulnerability_results.csv"
        if os.path.exists(results_file):
            with open(results_file, 'r') as f:
                lines = f.readlines()
                print(f"📄 Results: {len(lines) - 1} repositories in CSV")
                
                # Show first few results
                if len(lines) > 1:
                    print("\n🏆 TOP RESULTS:")
                    for i, line in enumerate(lines[1:4], 1):
                        parts = line.strip().split(',')
                        if len(parts) >= 3:
                            rank = parts[0]
                            repo_name = parts[1]
                            final_score = parts[12] if len(parts) > 12 else "N/A"
                            print(f"  {rank}. {repo_name} - Score: {final_score}")
        else:
            print("❌ No results file generated")
        
        print(f"\n📁 Check docker_output/ directory for all results")
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Container scan failed: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print(f"\n⚠️ Scan interrupted")
        sys.exit(1)

if __name__ == "__main__":
    main()
