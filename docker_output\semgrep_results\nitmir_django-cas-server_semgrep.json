{"version": "1.130.0", "results": [{"check_id": "python.lang.security.audit.dynamic-urllib-use-detected.dynamic-urllib-use-detected", "path": "downloaded_repos/nitmir_django-cas-server/cas_server/cas.py", "start": {"line": 118, "col": 20, "offset": 4278}, "end": {"line": 118, "col": 67, "offset": 4325}, "extra": {"message": "Detected a dynamic value being used with urllib. urllib supports 'file://' schemes, so a dynamic value controlled by a malicious actor may allow them to read arbitrary files. Audit uses of urllib calls to ensure user data cannot control the URLs, or consider using the 'requests' library instead.", "metadata": {"cwe": ["CWE-939: Improper Authorization in Handler for Custom URL Scheme"], "owasp": "A01:2017 - Injection", "source-rule-url": "https://github.com/PyCQA/bandit/blob/b1411bfb43795d3ffd268bef17a839dee954c2b1/bandit/blacklists/calls.py#L163", "bandit-code": "B310", "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://cwe.mitre.org/data/definitions/939.html"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.dynamic-urllib-use-detected.dynamic-urllib-use-detected", "shortlink": "https://sg.run/dKZZ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.dynamic-urllib-use-detected.dynamic-urllib-use-detected", "path": "downloaded_repos/nitmir_django-cas-server/cas_server/cas.py", "start": {"line": 160, "col": 16, "offset": 5821}, "end": {"line": 160, "col": 43, "offset": 5848}, "extra": {"message": "Detected a dynamic value being used with urllib. urllib supports 'file://' schemes, so a dynamic value controlled by a malicious actor may allow them to read arbitrary files. Audit uses of urllib calls to ensure user data cannot control the URLs, or consider using the 'requests' library instead.", "metadata": {"cwe": ["CWE-939: Improper Authorization in Handler for Custom URL Scheme"], "owasp": "A01:2017 - Injection", "source-rule-url": "https://github.com/PyCQA/bandit/blob/b1411bfb43795d3ffd268bef17a839dee954c2b1/bandit/blacklists/calls.py#L163", "bandit-code": "B310", "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://cwe.mitre.org/data/definitions/939.html"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.dynamic-urllib-use-detected.dynamic-urllib-use-detected", "shortlink": "https://sg.run/dKZZ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.dynamic-urllib-use-detected.dynamic-urllib-use-detected", "path": "downloaded_repos/nitmir_django-cas-server/cas_server/cas.py", "start": {"line": 197, "col": 16, "offset": 7305}, "end": {"line": 197, "col": 43, "offset": 7332}, "extra": {"message": "Detected a dynamic value being used with urllib. urllib supports 'file://' schemes, so a dynamic value controlled by a malicious actor may allow them to read arbitrary files. Audit uses of urllib calls to ensure user data cannot control the URLs, or consider using the 'requests' library instead.", "metadata": {"cwe": ["CWE-939: Improper Authorization in Handler for Custom URL Scheme"], "owasp": "A01:2017 - Injection", "source-rule-url": "https://github.com/PyCQA/bandit/blob/b1411bfb43795d3ffd268bef17a839dee954c2b1/bandit/blacklists/calls.py#L163", "bandit-code": "B310", "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://cwe.mitre.org/data/definitions/939.html"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.dynamic-urllib-use-detected.dynamic-urllib-use-detected", "shortlink": "https://sg.run/dKZZ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.dynamic-urllib-use-detected.dynamic-urllib-use-detected", "path": "downloaded_repos/nitmir_django-cas-server/cas_server/cas.py", "start": {"line": 367, "col": 16, "offset": 13815}, "end": {"line": 367, "col": 47, "offset": 13846}, "extra": {"message": "Detected a dynamic value being used with urllib. urllib supports 'file://' schemes, so a dynamic value controlled by a malicious actor may allow them to read arbitrary files. Audit uses of urllib calls to ensure user data cannot control the URLs, or consider using the 'requests' library instead.", "metadata": {"cwe": ["CWE-939: Improper Authorization in Handler for Custom URL Scheme"], "owasp": "A01:2017 - Injection", "source-rule-url": "https://github.com/PyCQA/bandit/blob/b1411bfb43795d3ffd268bef17a839dee954c2b1/bandit/blacklists/calls.py#L163", "bandit-code": "B310", "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://cwe.mitre.org/data/definitions/939.html"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.dynamic-urllib-use-detected.dynamic-urllib-use-detected", "shortlink": "https://sg.run/dKZZ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/nitmir_django-cas-server/cas_server/default_settings.py", "start": {"line": 289, "col": 16, "offset": 13442}, "end": {"line": 289, "col": 54, "offset": 13480}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs3/base.html", "start": {"line": 43, "col": 20, "offset": 2566}, "end": {"line": 43, "col": 36, "offset": 2582}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs3/logged.html", "start": {"line": 4, "col": 47, "offset": 123}, "end": {"line": 4, "col": 63, "offset": 139}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs4/base.html", "start": {"line": 28, "col": 18, "offset": 1804}, "end": {"line": 28, "col": 34, "offset": 1820}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs4/logged.html", "start": {"line": 4, "col": 47, "offset": 123}, "end": {"line": 4, "col": 63, "offset": 139}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.non-literal-import.non-literal-import", "path": "downloaded_repos/nitmir_django-cas-server/cas_server/utils.py", "start": {"line": 140, "col": 24, "offset": 5196}, "end": {"line": 140, "col": 45, "offset": 5217}, "extra": {"message": "Untrusted user input in `importlib.import_module()` function allows an attacker to load arbitrary code. Avoid dynamic values in `importlib.import_module()` or use a whitelist to prevent running untrusted code.", "metadata": {"owasp": ["A01:2021 - Broken Access Control"], "cwe": ["CWE-706: Use of Incorrectly-Resolved Name or Reference"], "category": "security", "technology": ["python"], "references": ["https://owasp.org/Top10/A01_2021-Broken_Access_Control"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.non-literal-import.non-literal-import", "shortlink": "https://sg.run/y6Jk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.secure-cookies.django-secure-set-cookie", "path": "downloaded_repos/nitmir_django-cas-server/cas_server/utils.py", "start": {"line": 214, "col": 5, "offset": 7966}, "end": {"line": 221, "col": 6, "offset": 8171}, "extra": {"message": "Django cookies should be handled securely by setting secure=True, httponly=True, and samesite='Lax' in response.set_cookie(...). If your situation calls for different settings, explicitly disable the setting. If you want to send the cookie over http, set secure=False. If you want to let client-side JavaScript read the cookie, set httponly=False. If you want to attach cookies to requests for external sites, set samesite=None.", "metadata": {"cwe": ["CWE-614: <PERSON><PERSON> in HTTPS Session Without 'Secure' Attribute"], "owasp": ["A05:2021 - Security Misconfiguration"], "asvs": {"control_id": "3.4 Missing <PERSON><PERSON>", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x12-V3-Session-management.md#v34-cookie-based-session-management", "section": "V3: Session Management Verification Requirements", "version": "4"}, "references": ["https://docs.djangoproject.com/en/3.0/ref/request-response/#django.http.HttpResponse.set_cookie", "https://semgrep.dev/blog/2020/bento-check-keeping-cookies-safe-in-flask/", "https://bento.dev/checks/flask/secure-set-cookie/"], "category": "security", "technology": ["django"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["<PERSON><PERSON>"], "source": "https://semgrep.dev/r/python.django.security.audit.secure-cookies.django-secure-set-cookie", "shortlink": "https://sg.run/x1WL"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.injection.tainted-url-host.tainted-url-host", "path": "downloaded_repos/nitmir_django-cas-server/cas_server/utils.py", "start": {"line": 237, "col": 19, "offset": 8792}, "end": {"line": 237, "col": 78, "offset": 8851}, "extra": {"message": "User data flows into the host portion of this manually-constructed URL. This could allow an attacker to send data to their own server, potentially exposing sensitive data such as cookies or authorization information sent with this request. They could also probe internal servers or other resources that the server running this code can access. (This is called server-side request forgery, or SSRF.) Do not allow arbitrary hosts. Instead, create an allowlist for approved hosts, or hardcode the correct host.", "metadata": {"cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://cheatsheetseries.owasp.org/cheatsheets/Server_Side_Request_Forgery_Prevention_Cheat_Sheet.html"], "category": "security", "technology": ["flask"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "impact": "MEDIUM", "likelihood": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/python.django.security.injection.tainted-url-host.tainted-url-host", "shortlink": "https://sg.run/oYz6"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.audit.directly-returned-format-string.directly-returned-format-string", "path": "downloaded_repos/nitmir_django-cas-server/cas_server/utils.py", "start": {"line": 242, "col": 5, "offset": 9006}, "end": {"line": 242, "col": 23, "offset": 9024}, "extra": {"message": "Detected Flask route directly returning a formatted string. This is subject to cross-site scripting if user input can reach the string. Consider using the template engine instead and rendering pages with 'render_template()'.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["flask"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.audit.directly-returned-format-string.directly-returned-format-string", "shortlink": "https://sg.run/Zv6o"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/nitmir_django-cas-server/cas_server/views.py", "start": {"line": 212, "col": 30, "offset": 8147}, "end": {"line": 216, "col": 19, "offset": 8392}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/nitmir_django-cas-server/cas_server/views.py", "start": {"line": 218, "col": 30, "offset": 8455}, "end": {"line": 223, "col": 32, "offset": 8751}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/nitmir_django-cas-server/cas_server/views.py", "start": {"line": 225, "col": 30, "offset": 8799}, "end": {"line": 229, "col": 19, "offset": 9039}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "path": "downloaded_repos/nitmir_django-cas-server/cas_server/views.py", "start": {"line": 661, "col": 21, "offset": 28082}, "end": {"line": 661, "col": 93, "offset": 28154}, "extra": {"message": "Detected a python logger call with a potential hardcoded secret \"Fail to create GSSAPI credentials objects: %s\" % (error,) being logged. This may lead to secret credentials being exposed. Make sure that the logger is not logging  sensitive information.", "metadata": {"cwe": ["CWE-532: Insertion of Sensitive Information into Log File"], "category": "security", "technology": ["python"], "owasp": ["A09:2021 - Security Logging and Monitoring Failures"], "references": ["https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.logging.logger-credential-leak.python-logger-credential-disclosure", "shortlink": "https://sg.run/ydNx"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml", "start": {"line": 76, "col": 47, "offset": 2241}, "end": {"line": 76, "col": 50, "offset": 2244}}, {"path": "downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml", "start": {"line": 76, "col": 71, "offset": 2241}, "end": {"line": 76, "col": 74, "offset": 2244}}, {"path": "downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml", "start": {"line": 76, "col": 97, "offset": 2241}, "end": {"line": 76, "col": 100, "offset": 2244}}]], "message": "Syntax error at line downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml:76:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml", "spans": [{"file": "downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml", "start": {"line": 76, "col": 47, "offset": 2241}, "end": {"line": 76, "col": 50, "offset": 2244}}, {"file": "downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml", "start": {"line": 76, "col": 71, "offset": 2241}, "end": {"line": 76, "col": 74, "offset": 2244}}, {"file": "downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml", "start": {"line": 76, "col": 97, "offset": 2241}, "end": {"line": 76, "col": 100, "offset": 2244}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml", "start": {"line": 78, "col": 47, "offset": 2472}, "end": {"line": 78, "col": 50, "offset": 2475}}, {"path": "downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml", "start": {"line": 78, "col": 88, "offset": 2472}, "end": {"line": 78, "col": 91, "offset": 2475}}, {"path": "downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml", "start": {"line": 78, "col": 114, "offset": 2472}, "end": {"line": 78, "col": 117, "offset": 2475}}, {"path": "downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml", "start": {"line": 78, "col": 200, "offset": 2472}, "end": {"line": 78, "col": 216, "offset": 2488}}]], "message": "Syntax error at line downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml:78:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml", "spans": [{"file": "downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml", "start": {"line": 78, "col": 47, "offset": 2472}, "end": {"line": 78, "col": 50, "offset": 2475}}, {"file": "downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml", "start": {"line": 78, "col": 88, "offset": 2472}, "end": {"line": 78, "col": 91, "offset": 2475}}, {"file": "downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml", "start": {"line": 78, "col": 114, "offset": 2472}, "end": {"line": 78, "col": 117, "offset": 2475}}, {"file": "downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml", "start": {"line": 78, "col": 200, "offset": 2472}, "end": {"line": 78, "col": 216, "offset": 2488}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml", "start": {"line": 100, "col": 47, "offset": 3405}, "end": {"line": 100, "col": 50, "offset": 3408}}, {"path": "downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml", "start": {"line": 100, "col": 71, "offset": 3405}, "end": {"line": 100, "col": 74, "offset": 3408}}, {"path": "downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml", "start": {"line": 100, "col": 97, "offset": 3405}, "end": {"line": 100, "col": 100, "offset": 3408}}]], "message": "Syntax error at line downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml:100:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml", "spans": [{"file": "downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml", "start": {"line": 100, "col": 47, "offset": 3405}, "end": {"line": 100, "col": 50, "offset": 3408}}, {"file": "downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml", "start": {"line": 100, "col": 71, "offset": 3405}, "end": {"line": 100, "col": 74, "offset": 3408}}, {"file": "downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml", "start": {"line": 100, "col": 97, "offset": 3405}, "end": {"line": 100, "col": 100, "offset": 3408}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml", "start": {"line": 102, "col": 47, "offset": 3636}, "end": {"line": 102, "col": 50, "offset": 3639}}, {"path": "downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml", "start": {"line": 102, "col": 88, "offset": 3636}, "end": {"line": 102, "col": 91, "offset": 3639}}, {"path": "downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml", "start": {"line": 102, "col": 114, "offset": 3636}, "end": {"line": 102, "col": 117, "offset": 3639}}, {"path": "downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml", "start": {"line": 102, "col": 200, "offset": 3636}, "end": {"line": 102, "col": 216, "offset": 3652}}]], "message": "Syntax error at line downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml:102:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `${{` was unexpected", "path": "downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml", "spans": [{"file": "downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml", "start": {"line": 102, "col": 47, "offset": 3636}, "end": {"line": 102, "col": 50, "offset": 3639}}, {"file": "downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml", "start": {"line": 102, "col": 88, "offset": 3636}, "end": {"line": 102, "col": 91, "offset": 3639}}, {"file": "downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml", "start": {"line": 102, "col": 114, "offset": 3636}, "end": {"line": 102, "col": 117, "offset": 3639}}, {"file": "downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml", "start": {"line": 102, "col": 200, "offset": 3636}, "end": {"line": 102, "col": 216, "offset": 3652}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs3/base.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 76, "offset": 75}}, {"path": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs3/base.html", "start": {"line": 16, "col": 42, "offset": 0}, "end": {"line": 16, "col": 66, "offset": 24}}, {"path": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs3/base.html", "start": {"line": 49, "col": 41, "offset": 0}, "end": {"line": 49, "col": 42, "offset": 1}}, {"path": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs3/base.html", "start": {"line": 51, "col": 43, "offset": 0}, "end": {"line": 51, "col": 44, "offset": 1}}, {"path": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs3/base.html", "start": {"line": 53, "col": 43, "offset": 0}, "end": {"line": 53, "col": 44, "offset": 1}}, {"path": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs3/base.html", "start": {"line": 55, "col": 43, "offset": 0}, "end": {"line": 55, "col": 44, "offset": 1}}, {"path": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs3/base.html", "start": {"line": 79, "col": 41, "offset": 0}, "end": {"line": 79, "col": 59, "offset": 18}}]], "message": "Syntax error at line downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs3/base.html:1:\n `{% load i18n %}{% load static %}{% get_current_language as LANGUAGE_CODE %}` was unexpected", "path": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs3/base.html", "spans": [{"file": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs3/base.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 76, "offset": 75}}, {"file": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs3/base.html", "start": {"line": 16, "col": 42, "offset": 0}, "end": {"line": 16, "col": 66, "offset": 24}}, {"file": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs3/base.html", "start": {"line": 49, "col": 41, "offset": 0}, "end": {"line": 49, "col": 42, "offset": 1}}, {"file": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs3/base.html", "start": {"line": 51, "col": 43, "offset": 0}, "end": {"line": 51, "col": 44, "offset": 1}}, {"file": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs3/base.html", "start": {"line": 53, "col": 43, "offset": 0}, "end": {"line": 53, "col": 44, "offset": 1}}, {"file": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs3/base.html", "start": {"line": 55, "col": 43, "offset": 0}, "end": {"line": 55, "col": 44, "offset": 1}}, {"file": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs3/base.html", "start": {"line": 79, "col": 41, "offset": 0}, "end": {"line": 79, "col": 59, "offset": 18}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs4/base.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 76, "offset": 75}}, {"path": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs4/base.html", "start": {"line": 9, "col": 42, "offset": 0}, "end": {"line": 9, "col": 66, "offset": 24}}, {"path": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs4/base.html", "start": {"line": 39, "col": 39, "offset": 0}, "end": {"line": 39, "col": 40, "offset": 1}}, {"path": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs4/base.html", "start": {"line": 41, "col": 41, "offset": 0}, "end": {"line": 41, "col": 42, "offset": 1}}, {"path": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs4/base.html", "start": {"line": 43, "col": 41, "offset": 0}, "end": {"line": 43, "col": 42, "offset": 1}}, {"path": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs4/base.html", "start": {"line": 45, "col": 41, "offset": 0}, "end": {"line": 45, "col": 42, "offset": 1}}, {"path": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs4/base.html", "start": {"line": 71, "col": 41, "offset": 0}, "end": {"line": 71, "col": 59, "offset": 18}}]], "message": "Syntax error at line downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs4/base.html:1:\n `{% load i18n %}{% load static %}{% get_current_language as LANGUAGE_CODE %}` was unexpected", "path": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs4/base.html", "spans": [{"file": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs4/base.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 76, "offset": 75}}, {"file": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs4/base.html", "start": {"line": 9, "col": 42, "offset": 0}, "end": {"line": 9, "col": 66, "offset": 24}}, {"file": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs4/base.html", "start": {"line": 39, "col": 39, "offset": 0}, "end": {"line": 39, "col": 40, "offset": 1}}, {"file": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs4/base.html", "start": {"line": 41, "col": 41, "offset": 0}, "end": {"line": 41, "col": 42, "offset": 1}}, {"file": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs4/base.html", "start": {"line": 43, "col": 41, "offset": 0}, "end": {"line": 43, "col": 42, "offset": 1}}, {"file": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs4/base.html", "start": {"line": 45, "col": 41, "offset": 0}, "end": {"line": 45, "col": 42, "offset": 1}}, {"file": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs4/base.html", "start": {"line": 71, "col": 41, "offset": 0}, "end": {"line": 71, "col": 59, "offset": 18}}]}], "paths": {"scanned": ["downloaded_repos/nitmir_django-cas-server/.coveragerc", "downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml", "downloaded_repos/nitmir_django-cas-server/.gitignore", "downloaded_repos/nitmir_django-cas-server/.gitlab-ci.yml", "downloaded_repos/nitmir_django-cas-server/.readthedocs.yaml", "downloaded_repos/nitmir_django-cas-server/.update_coverage", "downloaded_repos/nitmir_django-cas-server/CHANGELOG.rst", "downloaded_repos/nitmir_django-cas-server/LICENSE", "downloaded_repos/nitmir_django-cas-server/MANIFEST.in", "downloaded_repos/nitmir_django-cas-server/Makefile", "downloaded_repos/nitmir_django-cas-server/README.rst", "downloaded_repos/nitmir_django-cas-server/cas_server/__init__.py", "downloaded_repos/nitmir_django-cas-server/cas_server/admin.py", "downloaded_repos/nitmir_django-cas-server/cas_server/apps.py", "downloaded_repos/nitmir_django-cas-server/cas_server/auth.py", "downloaded_repos/nitmir_django-cas-server/cas_server/cas.py", "downloaded_repos/nitmir_django-cas-server/cas_server/default_settings.py", "downloaded_repos/nitmir_django-cas-server/cas_server/federate.py", "downloaded_repos/nitmir_django-cas-server/cas_server/forms.py", "downloaded_repos/nitmir_django-cas-server/cas_server/locale/fr/LC_MESSAGES/django.mo", "downloaded_repos/nitmir_django-cas-server/cas_server/locale/fr/LC_MESSAGES/django.po", "downloaded_repos/nitmir_django-cas-server/cas_server/locale/nl/LC_MESSAGES/django.mo", "downloaded_repos/nitmir_django-cas-server/cas_server/locale/nl/LC_MESSAGES/django.po", "downloaded_repos/nitmir_django-cas-server/cas_server/locale/pt_BR/django.mo", "downloaded_repos/nitmir_django-cas-server/cas_server/locale/pt_BR/django.po", "downloaded_repos/nitmir_django-cas-server/cas_server/locale/zh_Hans/LC_MESSAGES/django.mo", "downloaded_repos/nitmir_django-cas-server/cas_server/locale/zh_Hans/LC_MESSAGES/django.po", "downloaded_repos/nitmir_django-cas-server/cas_server/management/__init__.py", "downloaded_repos/nitmir_django-cas-server/cas_server/management/commands/__init__.py", "downloaded_repos/nitmir_django-cas-server/cas_server/management/commands/cas_clean_federate.py", "downloaded_repos/nitmir_django-cas-server/cas_server/management/commands/cas_clean_sessions.py", "downloaded_repos/nitmir_django-cas-server/cas_server/management/commands/cas_clean_tickets.py", "downloaded_repos/nitmir_django-cas-server/cas_server/migrations/0001_squashed_0013_auto_20170329_1748.py", "downloaded_repos/nitmir_django-cas-server/cas_server/migrations/__init__.py", "downloaded_repos/nitmir_django-cas-server/cas_server/models.py", "downloaded_repos/nitmir_django-cas-server/cas_server/static/cas_server/bs3/styles.css", "downloaded_repos/nitmir_django-cas-server/cas_server/static/cas_server/bs4/fa-lock.svg", "downloaded_repos/nitmir_django-cas-server/cas_server/static/cas_server/bs4/fa-user.svg", "downloaded_repos/nitmir_django-cas-server/cas_server/static/cas_server/bs4/styles.css", "downloaded_repos/nitmir_django-cas-server/cas_server/static/cas_server/cas.js", "downloaded_repos/nitmir_django-cas-server/cas_server/static/cas_server/favicon.ico", "downloaded_repos/nitmir_django-cas-server/cas_server/static/cas_server/functions.js", "downloaded_repos/nitmir_django-cas-server/cas_server/static/cas_server/logo.png", "downloaded_repos/nitmir_django-cas-server/cas_server/static/cas_server/logo.svg", "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs3/base.html", "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs3/form.html", "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs3/logged.html", "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs3/login.html", "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs3/logout.html", "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs3/warn.html", "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs4/base.html", "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs4/form.html", "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs4/logged.html", "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs4/login.html", "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs4/logout.html", "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs4/warn.html", "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/proxy.xml", "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/samlValidate.xml", "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/samlValidateError.xml", "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/serviceValidate.xml", "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/serviceValidateError.xml", "downloaded_repos/nitmir_django-cas-server/cas_server/templatetags/__init__.py", "downloaded_repos/nitmir_django-cas-server/cas_server/templatetags/cas_server.py", "downloaded_repos/nitmir_django-cas-server/cas_server/urls.py", "downloaded_repos/nitmir_django-cas-server/cas_server/utils.py", "downloaded_repos/nitmir_django-cas-server/cas_server/views.py", "downloaded_repos/nitmir_django-cas-server/docs/CHANGELOG.rst", "downloaded_repos/nitmir_django-cas-server/docs/Makefile", "downloaded_repos/nitmir_django-cas-server/docs/README.rst", "downloaded_repos/nitmir_django-cas-server/docs/_ext/djangodocs.py", "downloaded_repos/nitmir_django-cas-server/docs/conf.py", "downloaded_repos/nitmir_django-cas-server/docs/index.rst", "downloaded_repos/nitmir_django-cas-server/docs/make.bat", "downloaded_repos/nitmir_django-cas-server/docs/package/cas_server.admin.rst", "downloaded_repos/nitmir_django-cas-server/docs/package/cas_server.apps.rst", "downloaded_repos/nitmir_django-cas-server/docs/package/cas_server.auth.rst", "downloaded_repos/nitmir_django-cas-server/docs/package/cas_server.cas.rst", "downloaded_repos/nitmir_django-cas-server/docs/package/cas_server.default_settings.rst", "downloaded_repos/nitmir_django-cas-server/docs/package/cas_server.federate.rst", "downloaded_repos/nitmir_django-cas-server/docs/package/cas_server.forms.rst", "downloaded_repos/nitmir_django-cas-server/docs/package/cas_server.models.rst", "downloaded_repos/nitmir_django-cas-server/docs/package/cas_server.rst", "downloaded_repos/nitmir_django-cas-server/docs/package/cas_server.templatetags.cas_server.rst", "downloaded_repos/nitmir_django-cas-server/docs/package/cas_server.templatetags.rst", "downloaded_repos/nitmir_django-cas-server/docs/package/cas_server.urls.rst", "downloaded_repos/nitmir_django-cas-server/docs/package/cas_server.utils.rst", "downloaded_repos/nitmir_django-cas-server/docs/package/cas_server.views.rst", "downloaded_repos/nitmir_django-cas-server/docs/package/modules.rst", "downloaded_repos/nitmir_django-cas-server/docs/requirements.txt", "downloaded_repos/nitmir_django-cas-server/pytest.ini", "downloaded_repos/nitmir_django-cas-server/requirements-dev.txt", "downloaded_repos/nitmir_django-cas-server/requirements.txt", "downloaded_repos/nitmir_django-cas-server/setup.cfg", "downloaded_repos/nitmir_django-cas-server/setup.py", "downloaded_repos/nitmir_django-cas-server/tox.ini"], "skipped": [{"path": "downloaded_repos/nitmir_django-cas-server/.github/workflows/github-actions.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs3/base.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nitmir_django-cas-server/cas_server/templates/cas_server/bs4/base.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/nitmir_django-cas-server/cas_server/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nitmir_django-cas-server/cas_server/tests/auth.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nitmir_django-cas-server/cas_server/tests/mixin.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nitmir_django-cas-server/cas_server/tests/settings.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nitmir_django-cas-server/cas_server/tests/test_federate.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nitmir_django-cas-server/cas_server/tests/test_models.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nitmir_django-cas-server/cas_server/tests/test_templatetags.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nitmir_django-cas-server/cas_server/tests/test_utils.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nitmir_django-cas-server/cas_server/tests/test_view.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nitmir_django-cas-server/cas_server/tests/urls.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/nitmir_django-cas-server/cas_server/tests/utils.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.9852359294891357, "profiling_times": {"config_time": 6.8839991092681885, "core_time": 5.53566575050354, "ignores_time": 0.12987184524536133, "total_time": 12.55049204826355}, "parsing_time": {"total_time": 0.8795762062072754, "per_file_time": {"mean": 0.02748675644397735, "std_dev": 0.000992535787590176}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 12.120436429977417, "per_file_time": {"mean": 0.05224326047404062, "std_dev": 0.057645934992713185}, "very_slow_stats": {"time_ratio": 0.20749352904721866, "count_ratio": 0.004310344827586207}, "very_slow_files": [{"fpath": "downloaded_repos/nitmir_django-cas-server/cas_server/views.py", "ftime": 2.5149121284484863}]}, "matching_time": {"total_time": 6.858721017837524, "per_file_and_rule_time": {"mean": 0.014624138630783637, "std_dev": 0.0020097412656473845}, "very_slow_stats": {"time_ratio": 0.48725402893905395, "count_ratio": 0.031982942430703626}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/nitmir_django-cas-server/cas_server/utils.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.15466713905334473}, {"fpath": "downloaded_repos/nitmir_django-cas-server/cas_server/utils.py", "rule_id": "python.django.security.injection.open-redirect.open-redirect", "time": 0.1585390567779541}, {"fpath": "downloaded_repos/nitmir_django-cas-server/cas_server/models.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.18999004364013672}, {"fpath": "downloaded_repos/nitmir_django-cas-server/docs/conf.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.20048308372497559}, {"fpath": "downloaded_repos/nitmir_django-cas-server/cas_server/default_settings.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.2460188865661621}, {"fpath": "downloaded_repos/nitmir_django-cas-server/setup.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.28262996673583984}, {"fpath": "downloaded_repos/nitmir_django-cas-server/cas_server/views.py", "rule_id": "python.django.security.injection.open-redirect.open-redirect", "time": 0.3066549301147461}, {"fpath": "downloaded_repos/nitmir_django-cas-server/cas_server/migrations/0001_squashed_0013_auto_20170329_1748.py", "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.31984901428222656}, {"fpath": "downloaded_repos/nitmir_django-cas-server/cas_server/migrations/0001_squashed_0013_auto_20170329_1748.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.4353160858154297}, {"fpath": "downloaded_repos/nitmir_django-cas-server/cas_server/views.py", "rule_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "time": 0.4510970115661621}]}, "tainting_time": {"total_time": 1.429274082183838, "per_def_and_rule_time": {"mean": 0.0006944966385732936, "std_dev": 1.4266821680355731e-05}, "very_slow_stats": {"time_ratio": 0.10950722374854249, "count_ratio": 0.00048590864917395527}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/nitmir_django-cas-server/cas_server/cas.py", "fline": 297, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.15651583671569824}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089364160}, "engine_requested": "OSS", "skipped_rules": []}