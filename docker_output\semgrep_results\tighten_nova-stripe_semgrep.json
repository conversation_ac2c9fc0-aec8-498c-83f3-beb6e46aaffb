{"version": "1.130.0", "results": [], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/tighten_nova-stripe/.github/workflows/test.yml", "start": {"line": 30, "col": 62, "offset": 554}, "end": {"line": 30, "col": 85, "offset": 577}}, {"path": "downloaded_repos/tighten_nova-stripe/.github/workflows/test.yml", "start": {"line": 30, "col": 93, "offset": 554}, "end": {"line": 30, "col": 119, "offset": 580}}]], "message": "Syntax error at line downloaded_repos/tighten_nova-stripe/.github/workflows/test.yml:30:\n When parsing a snippet as Bash for metavariable-pattern in rule 'yaml.github-actions.security.curl-eval.curl-eval', `{ secrets.NOVA_USERNAME` was unexpected", "path": "downloaded_repos/tighten_nova-stripe/.github/workflows/test.yml", "spans": [{"file": "downloaded_repos/tighten_nova-stripe/.github/workflows/test.yml", "start": {"line": 30, "col": 62, "offset": 554}, "end": {"line": 30, "col": 85, "offset": 577}}, {"file": "downloaded_repos/tighten_nova-stripe/.github/workflows/test.yml", "start": {"line": 30, "col": 93, "offset": 554}, "end": {"line": 30, "col": 119, "offset": 580}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/tighten_nova-stripe/src/Http/Middleware/Authorize.php", "start": {"line": 15, "col": 76, "offset": 0}, "end": {"line": 15, "col": 79, "offset": 3}}]], "message": "Syntax error at line downloaded_repos/tighten_nova-stripe/src/Http/Middleware/Authorize.php:15:\n `...` was unexpected", "path": "downloaded_repos/tighten_nova-stripe/src/Http/Middleware/Authorize.php", "spans": [{"file": "downloaded_repos/tighten_nova-stripe/src/Http/Middleware/Authorize.php", "start": {"line": 15, "col": 76, "offset": 0}, "end": {"line": 15, "col": 79, "offset": 3}}]}], "paths": {"scanned": ["downloaded_repos/tighten_nova-stripe/.editorconfig", "downloaded_repos/tighten_nova-stripe/.env.testing.example", "downloaded_repos/tighten_nova-stripe/.gitattributes", "downloaded_repos/tighten_nova-stripe/.github/workflows/deprecated/lint.yml.txt", "downloaded_repos/tighten_nova-stripe/.github/workflows/deprecated/test.yml.txt", "downloaded_repos/tighten_nova-stripe/.github/workflows/test.yml", "downloaded_repos/tighten_nova-stripe/.gitignore", "downloaded_repos/tighten_nova-stripe/.phpcs.xml.dist", "downloaded_repos/tighten_nova-stripe/CONTRIBUTING.md", "downloaded_repos/tighten_nova-stripe/LICENSE.md", "downloaded_repos/tighten_nova-stripe/composer.json", "downloaded_repos/tighten_nova-stripe/nova-stripe-banner.png", "downloaded_repos/tighten_nova-stripe/phpunit.xml", "downloaded_repos/tighten_nova-stripe/readme.md", "downloaded_repos/tighten_nova-stripe/rector.php", "downloaded_repos/tighten_nova-stripe/resources/js/index.js", "downloaded_repos/tighten_nova-stripe/screenshots/charges-details.png", "downloaded_repos/tighten_nova-stripe/screenshots/charges-index.png", "downloaded_repos/tighten_nova-stripe/screenshots/customers-details.png", "downloaded_repos/tighten_nova-stripe/screenshots/customers-index.png", "downloaded_repos/tighten_nova-stripe/screenshots/products-details.png", "downloaded_repos/tighten_nova-stripe/screenshots/products-index.png", "downloaded_repos/tighten_nova-stripe/screenshots/subscriptions-details.png", "downloaded_repos/tighten_nova-stripe/screenshots/subscriptions-index.png", "downloaded_repos/tighten_nova-stripe/src/Actions/Sync.php", "downloaded_repos/tighten_nova-stripe/src/Http/Middleware/Authorize.php", "downloaded_repos/tighten_nova-stripe/src/Jobs/SyncWithStripe.php", "downloaded_repos/tighten_nova-stripe/src/Models/BaseModel.php", "downloaded_repos/tighten_nova-stripe/src/Models/Charge.php", "downloaded_repos/tighten_nova-stripe/src/Models/Customer.php", "downloaded_repos/tighten_nova-stripe/src/Models/Product.php", "downloaded_repos/tighten_nova-stripe/src/Models/Subscription.php", "downloaded_repos/tighten_nova-stripe/src/NovaStripe.php", "downloaded_repos/tighten_nova-stripe/src/Resources/BaseResource.php", "downloaded_repos/tighten_nova-stripe/src/Resources/Charge.php", "downloaded_repos/tighten_nova-stripe/src/Resources/Customer.php", "downloaded_repos/tighten_nova-stripe/src/Resources/Product.php", "downloaded_repos/tighten_nova-stripe/src/Resources/Subscription.php", "downloaded_repos/tighten_nova-stripe/src/Services/StripeClientService.php", "downloaded_repos/tighten_nova-stripe/src/ToolServiceProvider.php"], "skipped": [{"path": "downloaded_repos/tighten_nova-stripe/.github/workflows/test.yml", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/tighten_nova-stripe/src/Http/Middleware/Authorize.php", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/tighten_nova-stripe/tests/Feature/SyncWithStripeTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/tighten_nova-stripe/tests/Pest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/tighten_nova-stripe/tests/TestCase.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/tighten_nova-stripe/tests/TestUser.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/tighten_nova-stripe/tests/Unit/ChargeTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/tighten_nova-stripe/tests/Unit/CustomerTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/tighten_nova-stripe/tests/Unit/ProductTest.php", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/tighten_nova-stripe/tests/Unit/SubscriptionTest.php", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.786350965499878, "profiling_times": {"config_time": 6.848435401916504, "core_time": 2.4859726428985596, "ignores_time": 0.0021750926971435547, "total_time": 9.337440013885498}, "parsing_time": {"total_time": 0.43935155868530273, "per_file_time": {"mean": 0.02196757793426514, "std_dev": 0.0001841140853008483}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 0.8872089385986328, "per_file_time": {"mean": 0.008872089385986328, "std_dev": 0.0003108900486223775}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.15791940689086914, "per_file_and_rule_time": {"mean": 0.0025888427359158877, "std_dev": 1.8334908601346573e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.004705905914306641, "per_def_and_rule_time": {"mean": 0.00039215882619222003, "std_dev": 1.0442502526883698e-07}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}