rank,repo_name,repo_url,clone_url,stars,issues,language,description,risk_score,semgrep_vulnerabilities,semgrep_severity_score,vulnerability_categories,final_score,risk_factors,last_updated,created_at,semgrep_findings_summary,detailed_vulnerabilities
1,uasoft-indonesia/badaso,https://github.com/uasoft-indonesia/badaso,https://github.com/uasoft-indonesia/badaso.git,1237,1,PHP,"Laravel Vue headless CMS / admin panel / dashboard / builder / API CRUD generator, anything !",59,88,100,Other; XSS,100,High-risk application type (+24); Critical features (+4); Vulnerability keywords (+6),2025-06-13T09:54:58Z,2021-03-15T04:40:50Z,WARNING: 88,"[WARNING] dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile: Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities. (File: downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile, Line: 3) | [WARNING] dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile: Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities. (File: downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile, Line: 6) | [WARNING] dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile: Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities. (File: downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile, Line: 7) | [WARNING] dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile: Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities. (File: downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile, Line: 8) | [WARNING] dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile: Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities. (File: downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile, Line: 14) | [WARNING] dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile: Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities. (File: downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile, Line: 15) | [WARNING] dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile: Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities. (File: downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile, Line: 18) | [WARNING] dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile: Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities. (File: downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile, Line: 19) | [WARNING] dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile: Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities. (File: downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile, Line: 20) | [WARNING] dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile: Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities. (File: downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile, Line: 21) | [WARNING] dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile: Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities. (File: downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile, Line: 24) | [WARNING] dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile: Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities. (File: downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile, Line: 26) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/uasoft-indonesia_badaso/src/Controllers/BadasoDatabaseController.php, Line: 364) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/uasoft-indonesia_badaso/src/Helpers/Redis/ConfigurationRedis.php, Line: 30) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoCheckbox.vue, Line: 22) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoCheckbox.vue, Line: 34) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoCodeEditor.vue, Line: 19) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoCodeEditor.vue, Line: 30) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoCodeEditor.vue, Line: 45) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoCodeEditor.vue, Line: 57) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoColorPicker.vue, Line: 26) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoColorPicker.vue, Line: 29) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoColorPicker.vue, Line: 37) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoDate.vue, Line: 18) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoDate.vue, Line: 30) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoDatetime.vue, Line: 24) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoDatetime.vue, Line: 36) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoEditor.vue, Line: 12) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoEditor.vue, Line: 15) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoEditor.vue, Line: 23) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoEmail.vue, Line: 15) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoEmail.vue, Line: 27) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoNotificationMessage.vue, Line: 43) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoNotificationMessage.vue, Line: 85) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoNumber.vue, Line: 13) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoNumber.vue, Line: 25) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoPassword.vue, Line: 10) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoPassword.vue, Line: 22) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoRadio.vue, Line: 21) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoRadio.vue, Line: 33) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSearch.vue, Line: 12) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSearch.vue, Line: 24) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSelect.vue, Line: 18) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSelect.vue, Line: 30) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSelectMultiple.vue, Line: 19) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSelectMultiple.vue, Line: 31) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSlider.vue, Line: 13) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSlider.vue, Line: 16) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSlider.vue, Line: 24) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSwitch.vue, Line: 51) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSwitch.vue, Line: 63) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoTags.vue, Line: 12) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoTags.vue, Line: 24) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoText.vue, Line: 18) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoText.vue, Line: 30) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoTextarea.vue, Line: 7) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoTextarea.vue, Line: 19) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoTime.vue, Line: 21) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoTime.vue, Line: 33) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUploadFile.vue, Line: 20) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUploadFile.vue, Line: 32) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUploadFileMultiple.vue, Line: 25) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUploadFileMultiple.vue, Line: 37) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUploadImage.vue, Line: 20) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUploadImage.vue, Line: 23) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUploadImage.vue, Line: 31) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUploadImageMultiple.vue, Line: 25) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUploadImageMultiple.vue, Line: 37) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUrl.vue, Line: 12) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUrl.vue, Line: 23) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/forgot-password.vue, Line: 37) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/forgot-password.vue, Line: 77) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/login.vue, Line: 28) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/login.vue, Line: 52) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/register.vue, Line: 28) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/register.vue, Line: 50) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/register.vue, Line: 75) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/register.vue, Line: 97) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/register.vue, Line: 120) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/register.vue, Line: 154) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/secret-login.vue, Line: 28) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/secret-login.vue, Line: 52) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/verify.vue, Line: 36) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/crud-generated/browse-bin.vue, Line: 146) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/crud-generated/browse-bin.vue, Line: 445) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/crud-generated/browse.vue, Line: 193) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/crud-generated/browse.vue, Line: 482) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/crud-generated/read.vue, Line: 71)"
2,moonshine-software/moonshine,https://github.com/moonshine-software/moonshine,https://github.com/moonshine-software/moonshine.git,1089,8,PHP,"Laravel Admin panel and more. Simple for beginners and powerful for experts. Using Blade, Alpine.js and Tailwind CSS.",56,16,100,Other; Hardcoded Secrets,100,High-risk application type (+24); Critical features (+4); Vulnerability keywords (+3),2025-07-27T18:55:38Z,2022-05-12T15:31:35Z,ERROR: 14; WARNING: 2,"[WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/moonshine-software_moonshine/src/Core/src/Storage/FileStorage.php, Line: 35) | [ERROR] generic.secrets.security.detected-bcrypt-hash.detected-bcrypt-hash: bcrypt hash detected (File: downloaded_repos/moonshine-software_moonshine/src/Laravel/database/factories/MoonshineUserFactory.php, Line: 27) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/ActionButton.js, Line: 29) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/BelongsToMany.js, Line: 34) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/FormBuilder.js, Line: 44) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/FormBuilder.js, Line: 176) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/FormBuilder.js, Line: 296) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/Fragment.js, Line: 67) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/TableBuilder.js, Line: 217) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Request/Sets.js, Line: 94) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Request/Sets.js, Line: 96) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Support/AsyncLoadContent.js, Line: 12) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Support/DOMUpdater.js, Line: 71) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Support/DOMUpdater.js, Line: 73) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Support/Iterable.js, Line: 82) | [WARNING] javascript.lang.security.audit.incomplete-sanitization.incomplete-sanitization: `inputName.replace('slide[', '').replace` method will only replace the first occurrence when used with a string argument (']'). If this method is used for escaping of dangerous data then there is a possibility for a bypass. Try to use sanitization library instead or use a Regex with a global flag. (File: downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Support/ShowWhen.js, Line: 243)"
3,et-nik/gameap,https://github.com/et-nik/gameap,https://github.com/et-nik/gameap.git,115,40,PHP,Game Admin Panel (GameAP) is the opensource game servers control panel.,40,24,100,Other; Command Injection; XSS; Hardcoded Secrets,90.0,High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3),2025-05-15T07:09:45Z,2018-11-21T12:01:13Z,ERROR: 5; WARNING: 19,"[ERROR] dockerfile.security.missing-user.missing-user: By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'. (File: downloaded_repos/et-nik_gameap/.dev/docker/gdaemon/Dockerfile, Line: 20) | [WARNING] bash.curl.security.curl-pipe-bash.curl-pipe-bash: Data is being piped into `bash` from a `curl` command. An attacker with control of the server in the `curl` command could inject malicious code into the pipe, resulting in a system compromise. Avoid piping untrusted data into `bash` or any other shell if you can. If you must do this, consider checking the SHA sum of the content returned by the server to verify its integrity. (File: downloaded_repos/et-nik_gameap/.dev/docker/gdaemon/entrypoint, Line: 8) | [ERROR] php.lang.security.exec-use.exec-use: Executing non-constant commands. This can lead to command injection. (File: downloaded_repos/et-nik_gameap/.dev/docker/php/entrypoint, Line: 15) | [ERROR] php.lang.security.exec-use.exec-use: Executing non-constant commands. This can lead to command injection. (File: downloaded_repos/et-nik_gameap/.dev/docker/php/entrypoint, Line: 79) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/et-nik_gameap/app/Repositories/ClientCertificateRepository.php, Line: 121) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/et-nik_gameap/app/Repositories/ClientCertificateRepository.php, Line: 130) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/et-nik_gameap/app/Repositories/NodeRepository.php, Line: 131) | [ERROR] generic.secrets.security.detected-bcrypt-hash.detected-bcrypt-hash: bcrypt hash detected (File: downloaded_repos/et-nik_gameap/database/seeds/UsersTableSeeder.php, Line: 20) | [WARNING] yaml.docker-compose.security.no-new-privileges.no-new-privileges: Service 'mysql' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this. (File: downloaded_repos/et-nik_gameap/docker-compose.yml, Line: 3) | [WARNING] yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service: Service 'mysql' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this. (File: downloaded_repos/et-nik_gameap/docker-compose.yml, Line: 3) | [WARNING] yaml.docker-compose.security.no-new-privileges.no-new-privileges: Service 'nginx' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this. (File: downloaded_repos/et-nik_gameap/docker-compose.yml, Line: 15) | [WARNING] yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service: Service 'nginx' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this. (File: downloaded_repos/et-nik_gameap/docker-compose.yml, Line: 15) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/et-nik_gameap/resources/assets/js/components/blocks/CreateNodeModal.vue, Line: 23) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/et-nik_gameap/resources/assets/js/components/blocks/CreateNodeModal.vue, Line: 40) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/et-nik_gameap/resources/assets/js/parts/form.js, Line: 4) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/AdminServersCreate.vue, Line: 63) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/AdminServersEdit.vue, Line: 84) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/forms/UpdateModForm.vue, Line: 150) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/forms/UpdateModForm.vue, Line: 160) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/forms/UpdateModForm.vue, Line: 170) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/forms/UpdateModForm.vue, Line: 187) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/forms/UpdateModForm.vue, Line: 197) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/forms/UpdateModForm.vue, Line: 207) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/et-nik_gameap/server.php, Line: 17)"
4,kleeja-official/kleeja,https://github.com/kleeja-official/kleeja,https://github.com/kleeja-official/kleeja.git,185,13,PHP,"⬇️ File Upload/sharing application, used by thousands of webmasters since 2007. ",35,83,100,Other; Command Injection,85.0,High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3),2025-05-07T19:19:54Z,2018-01-08T22:57:26Z,ERROR: 16; WARNING: 67,"[WARNING] python.django.security.django-no-csrf-token.django-no-csrf-token: Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks. (File: downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_configs.html, Line: 2) | [WARNING] python.django.security.django-no-csrf-token.django-no-csrf-token: Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks. (File: downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_plugins.html, Line: 118) | [WARNING] python.django.security.django-no-csrf-token.django-no-csrf-token: Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks. (File: downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_rules.html, Line: 18) | [WARNING] html.security.audit.missing-integrity.missing-integrity: This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files. (File: downloaded_repos/kleeja-official_kleeja/admin/Masmak/admin_start.html, Line: 483) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/kleeja-official_kleeja/admin/Masmak/css/index.html, Line: 51) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/kleeja-official_kleeja/admin/Masmak/fonts/index.html, Line: 51) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/kleeja-official_kleeja/admin/Masmak/images/index.html, Line: 51) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `configTypes` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/bootstrap.js, Line: 168) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/index.html, Line: 51) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `decimalPoint` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js, Line: 1401) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js, Line: 2649) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js, Line: 2685) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js, Line: 2795) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js, Line: 2910) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js, Line: 3126) | [WARNING] javascript.lang.security.audit.incomplete-sanitization.incomplete-sanitization: `word.replace` method will only replace the first occurrence when used with a string argument ('""'). If this method is used for escaping of dangerous data then there is a possibility for a bypass. Try to use sanitization library instead or use a Regex with a global flag. (File: downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js, Line: 4471) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `search` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js, Line: 4477) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js, Line: 4536) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js, Line: 5380) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/kleeja-official_kleeja/admin/Masmak/js/jquery.dataTables.js, Line: 5387) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/kleeja-official_kleeja/admin/index.php, Line: 41) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/kleeja-official_kleeja/images/filegroups/index.html, Line: 1) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/kleeja-official_kleeja/images/filegroups/index.html, Line: 1) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/kleeja-official_kleeja/images/filegroups/index.html, Line: 1) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/kleeja-official_kleeja/images/filegroups/index.html, Line: 1) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/kleeja-official_kleeja/images/filetypes/index.html, Line: 1) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/kleeja-official_kleeja/images/filetypes/index.html, Line: 1) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/kleeja-official_kleeja/images/filetypes/index.html, Line: 1) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/kleeja-official_kleeja/images/filetypes/index.html, Line: 1) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/kleeja-official_kleeja/images/index.html, Line: 51) | [ERROR] php.lang.security.curl-ssl-verifypeer-off.curl-ssl-verifypeer-off: SSL verification is disabled but should not be (currently CURLOPT_SSL_VERIFYPEER= false) (File: downloaded_repos/kleeja-official_kleeja/includes/FetchFile.php, Line: 105) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/kleeja-official_kleeja/includes/adm/c_files.php, Line: 80) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/kleeja-official_kleeja/includes/adm/c_files.php, Line: 157) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/kleeja-official_kleeja/includes/adm/c_files.php, Line: 257) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/kleeja-official_kleeja/includes/adm/c_files.php, Line: 366) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/kleeja-official_kleeja/includes/adm/d_img_ctrl.php, Line: 67) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/kleeja-official_kleeja/includes/adm/g_users.php, Line: 1084) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/kleeja-official_kleeja/includes/adm/j_plugins.php, Line: 281) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/kleeja-official_kleeja/includes/adm/k_ban.php, Line: 59) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/kleeja-official_kleeja/includes/adm/m_styles.php, Line: 299) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/kleeja-official_kleeja/includes/adm/p_check_update.php, Line: 19) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/kleeja-official_kleeja/includes/adm/p_check_update.php, Line: 271) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/kleeja-official_kleeja/includes/adm/start.php, Line: 87) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/kleeja-official_kleeja/includes/common.php, Line: 286) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/kleeja-official_kleeja/includes/functions.php, Line: 369) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/kleeja-official_kleeja/includes/functions.php, Line: 376) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/kleeja-official_kleeja/includes/functions_display.php, Line: 432) | [ERROR] php.lang.security.eval-use.eval-use: Evaluating non-constant commands. This can lead to command injection. (File: downloaded_repos/kleeja-official_kleeja/includes/style.php, Line: 368) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/kleeja-official_kleeja/includes/up_methods/defaultUploader.php, Line: 386) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/kleeja-official_kleeja/includes/usr.php, Line: 102) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/kleeja-official_kleeja/includes/usr.php, Line: 282) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/kleeja-official_kleeja/includes/usr.php, Line: 426) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/kleeja-official_kleeja/includes/usr.php, Line: 436) | [ERROR] php.lang.security.eval-use.eval-use: Evaluating non-constant commands. This can lead to command injection. (File: downloaded_repos/kleeja-official_kleeja/install/includes/functions_install.php, Line: 80) | [WARNING] python.django.security.django-no-csrf-token.django-no-csrf-token: Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks. (File: downloaded_repos/kleeja-official_kleeja/install/style/check.html, Line: 69) | [WARNING] python.django.security.django-no-csrf-token.django-no-csrf-token: Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks. (File: downloaded_repos/kleeja-official_kleeja/install/style/check.html, Line: 74) | [WARNING] python.django.security.django-no-csrf-token.django-no-csrf-token: Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks. (File: downloaded_repos/kleeja-official_kleeja/install/style/check_all.html, Line: 55) | [WARNING] python.django.security.django-no-csrf-token.django-no-csrf-token: Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks. (File: downloaded_repos/kleeja-official_kleeja/install/style/check_all.html, Line: 60) | [WARNING] python.django.security.django-no-csrf-token.django-no-csrf-token: Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks. (File: downloaded_repos/kleeja-official_kleeja/install/style/configs.html, Line: 63) | [WARNING] python.django.security.django-no-csrf-token.django-no-csrf-token: Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks. (File: downloaded_repos/kleeja-official_kleeja/install/style/configs.html, Line: 77) | [WARNING] python.django.security.django-no-csrf-token.django-no-csrf-token: Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks. (File: downloaded_repos/kleeja-official_kleeja/install/style/header.html, Line: 24) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/kleeja-official_kleeja/install/style/images/index.html, Line: 1) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/kleeja-official_kleeja/install/style/images/index.html, Line: 1) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/kleeja-official_kleeja/install/style/images/index.html, Line: 1) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/kleeja-official_kleeja/install/style/images/index.html, Line: 1) | [WARNING] python.django.security.django-no-csrf-token.django-no-csrf-token: Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks. (File: downloaded_repos/kleeja-official_kleeja/install/style/official.html, Line: 2) | [WARNING] python.django.security.django-no-csrf-token.django-no-csrf-token: Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks. (File: downloaded_repos/kleeja-official_kleeja/install/style/sqls_done.html, Line: 12) | [WARNING] python.django.security.django-no-csrf-token.django-no-csrf-token: Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks. (File: downloaded_repos/kleeja-official_kleeja/install/style/update_list.html, Line: 7) | [WARNING] python.django.security.django-no-csrf-token.django-no-csrf-token: Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks. (File: downloaded_repos/kleeja-official_kleeja/install/style/what_is_kleeja.html, Line: 10) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/kleeja-official_kleeja/styles/bootstrap/css/index.html, Line: 1) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/kleeja-official_kleeja/styles/bootstrap/css/index.html, Line: 1) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/kleeja-official_kleeja/styles/bootstrap/css/index.html, Line: 1) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/kleeja-official_kleeja/styles/bootstrap/css/index.html, Line: 1) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/kleeja-official_kleeja/styles/default/css/index.html, Line: 1) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/kleeja-official_kleeja/styles/default/css/index.html, Line: 1) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/kleeja-official_kleeja/styles/default/css/index.html, Line: 1) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/kleeja-official_kleeja/styles/default/css/index.html, Line: 1) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/kleeja-official_kleeja/styles/default/images/index.html, Line: 1) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/kleeja-official_kleeja/styles/default/images/index.html, Line: 1) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/kleeja-official_kleeja/styles/default/images/index.html, Line: 1) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/kleeja-official_kleeja/styles/default/images/index.html, Line: 1) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/kleeja-official_kleeja/ucp.php, Line: 673) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/kleeja-official_kleeja/uploads/thumbs/index.html, Line: 51)"
5,codepress/admin-columns,https://github.com/codepress/admin-columns,https://github.com/codepress/admin-columns.git,67,6,PHP,"Admin Columns allows you to manage and organize columns in the posts, users, comments, and media lists tables in the WordPress admin panel. Transform the WordPress admin screens into beautiful, clear overviews.",33,36,100,Other; Path Traversal,83.0,High-risk application type (+16); Critical features (+4); Vulnerability keywords (+3),2025-07-23T12:00:23Z,2013-03-13T11:40:07Z,ERROR: 27; WARNING: 9,"[WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/codepress_admin-columns/assets/external/qtip2/jquery.qtip.js, Line: 496) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/codepress_admin-columns/assets/js/admin-general.js, Line: 2) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/codepress_admin-columns/assets/js/admin-general.js, Line: 2) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/codepress_admin-columns/assets/js/admin-page-addons.js, Line: 122) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `name` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/codepress_admin-columns/assets/js/admin-page-addons.js, Line: 2568) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `e` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/codepress_admin-columns/assets/js/admin-page-columns.js, Line: 1) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/codepress_admin-columns/assets/js/admin-page-columns.js, Line: 1) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/codepress_admin-columns/assets/js/admin-page-columns.js, Line: 1) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/codepress_admin-columns/assets/js/admin-page-columns.js, Line: 1) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/codepress_admin-columns/assets/js/admin-page-columns.js, Line: 1) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/codepress_admin-columns/assets/js/admin-page-columns.js, Line: 1) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/codepress_admin-columns/assets/js/admin-page-settings.js, Line: 1) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `e` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/codepress_admin-columns/assets/js/admin-page-settings.js, Line: 1) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/codepress_admin-columns/assets/js/select2.js, Line: 149) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/codepress_admin-columns/assets/js/table.js, Line: 1) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/codepress_admin-columns/assets/js/table.js, Line: 1) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/codepress_admin-columns/assets/js/table.js, Line: 1) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/codepress_admin-columns/assets/js/table.js, Line: 1) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/codepress_admin-columns/assets/js/table.js, Line: 1) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `e` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/codepress_admin-columns/assets/js/table.js, Line: 1) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/codepress_admin-columns/classes/ListScreenRepository/Database.php, Line: 170) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/codepress_admin-columns/classes/ListScreenRepository/Database.php, Line: 197) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/codepress_admin-columns/src/js/admin/columns/column.ts, Line: 144) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/codepress_admin-columns/src/js/admin/columns/events/label.ts, Line: 49) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/codepress_admin-columns/src/js/admin/columns/settings/date.ts, Line: 110) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/codepress_admin-columns/src/js/admin/columns/settings/date.ts, Line: 158) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/codepress_admin-columns/src/js/admin/columns/settings/label.ts, Line: 137) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/codepress_admin-columns/src/js/admin/columns/settings/width.ts, Line: 49) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/codepress_admin-columns/src/js/helpers/html-element.ts, Line: 75) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/codepress_admin-columns/src/js/modules/addon-download.ts, Line: 88) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/codepress_admin-columns/src/js/modules/toggle-box-link.ts, Line: 69) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/codepress_admin-columns/src/js/modules/toggle-box-link.ts, Line: 88) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/codepress_admin-columns/src/js/plugin/show-more.ts, Line: 58) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/codepress_admin-columns/src/js/plugin/tooltip.ts, Line: 56) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/codepress_admin-columns/src/js/table/cell.ts, Line: 62) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/codepress_admin-columns/src/scripts/webfont.js, Line: 45)"
6,danpros/htmly,https://github.com/danpros/htmly,https://github.com/danpros/htmly.git,1211,24,PHP,"Simple and fast databaseless PHP blogging platform, and Flat-File CMS",28,93,100,Other; Command Injection,78.0,High-risk application type (+8); Optimal star range (+10); High-risk language: php (+10),2025-07-19T00:42:40Z,2013-12-25T01:35:51Z,ERROR: 13; WARNING: 80,"[WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/install.php, Line: 19) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/install.php, Line: 187) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/danpros_htmly/install.php, Line: 202) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 135) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 299) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 337) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 457) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 492) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 663) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 759) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 840) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 850) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 1149) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 1193) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 1201) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 1590) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 1684) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 1710) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 1716) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 1722) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 1725) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 1731) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 1734) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 1740) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 1743) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 1749) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 1752) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 1759) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 1762) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 1768) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 1774) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 1777) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 1783) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 1786) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 1795) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 1798) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 1807) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/admin.php, Line: 1814) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `list_type` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/danpros_htmly/system/admin/editor/js/Markdown.Converter.js, Line: 940) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `charsToEscape` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/danpros_htmly/system/admin/editor/js/Markdown.Converter.js, Line: 1414) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/danpros_htmly/system/admin/editor/js/Markdown.Editor.js, Line: 991) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/danpros_htmly/system/admin/editor/js/Markdown.Editor.js, Line: 999) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/danpros_htmly/system/admin/editor/js/Markdown.Editor.js, Line: 1163) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/danpros_htmly/system/admin/views/add-content.html.php, Line: 16) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/views/backup.html.php, Line: 8) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/danpros_htmly/system/admin/views/backup.html.php, Line: 8) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/views/clear-cache.html.php, Line: 5) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/admin/views/clear-cache.html.php, Line: 11) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/danpros_htmly/system/admin/views/edit-content.html.php, Line: 61) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/htmly.php, Line: 1921) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/danpros_htmly/system/htmly.php, Line: 1921) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/htmly.php, Line: 2014) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/htmly.php, Line: 2299) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/htmly.php, Line: 2373) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/danpros_htmly/system/htmly.php, Line: 2915) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/danpros_htmly/system/htmly.php, Line: 2917) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/htmly.php, Line: 2981) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/danpros_htmly/system/htmly.php, Line: 2981) | [ERROR] php.lang.security.mcrypt-use.mcrypt-use: Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL. (File: downloaded_repos/danpros_htmly/system/includes/dispatch.php, Line: 169) | [ERROR] php.lang.security.mcrypt-use.mcrypt-use: Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL. (File: downloaded_repos/danpros_htmly/system/includes/dispatch.php, Line: 170) | [ERROR] php.lang.security.mcrypt-use.mcrypt-use: Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL. (File: downloaded_repos/danpros_htmly/system/includes/dispatch.php, Line: 171) | [ERROR] php.lang.security.mcrypt-use.mcrypt-use: Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL. (File: downloaded_repos/danpros_htmly/system/includes/dispatch.php, Line: 172) | [ERROR] php.lang.security.mcrypt-use.mcrypt-use: Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL. (File: downloaded_repos/danpros_htmly/system/includes/dispatch.php, Line: 182) | [ERROR] php.lang.security.mcrypt-use.mcrypt-use: Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL. (File: downloaded_repos/danpros_htmly/system/includes/dispatch.php, Line: 186) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/danpros_htmly/system/includes/dispatch.php, Line: 428) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/danpros_htmly/system/includes/functions.php, Line: 18) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/danpros_htmly/system/includes/functions.php, Line: 31) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/danpros_htmly/system/includes/functions.php, Line: 44) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/danpros_htmly/system/includes/functions.php, Line: 74) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/danpros_htmly/system/includes/functions.php, Line: 88) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/danpros_htmly/system/includes/functions.php, Line: 152) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/danpros_htmly/system/includes/functions.php, Line: 166) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/danpros_htmly/system/includes/functions.php, Line: 196) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/includes/functions.php, Line: 367) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/danpros_htmly/system/includes/functions.php, Line: 1050) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/danpros_htmly/system/includes/functions.php, Line: 1656) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/danpros_htmly/system/includes/functions.php, Line: 1773) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/danpros_htmly/system/includes/functions.php, Line: 1871) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/danpros_htmly/system/includes/functions.php, Line: 1884) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/danpros_htmly/system/includes/functions.php, Line: 1929) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/danpros_htmly/system/includes/functions.php, Line: 1948) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/danpros_htmly/system/includes/functions.php, Line: 2002) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/danpros_htmly/system/includes/functions.php, Line: 2009) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/danpros_htmly/system/includes/functions.php, Line: 3680) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/danpros_htmly/system/includes/functions.php, Line: 3775) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/danpros_htmly/system/includes/functions.php, Line: 3851) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/danpros_htmly/system/resources/js/form.builder.js, Line: 190) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/danpros_htmly/system/resources/js/form.builder.js, Line: 207) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/danpros_htmly/system/resources/js/form.builder.js, Line: 250) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `a` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/danpros_htmly/themes/twentyfifteen/js/jquery.js, Line: 2) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/danpros_htmly/themes/twentyfifteen/js/jquery.js, Line: 2) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/danpros_htmly/themes/twentyfifteen/js/jquery.js, Line: 2) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/danpros_htmly/themes/twentysixteen/js/html5.js, Line: 71)"
7,ashraf-kabir/personal-blog,https://github.com/ashraf-kabir/personal-blog,https://github.com/ashraf-kabir/personal-blog.git,63,1,PHP,"Dynamic blogsite using PHP, JS & mysql where user can register, log in, post blog with images, comment, edit profile, change password, etc. There is a separate admin panel also where admin can approve user & theirs posts & comments and manipulate the whole site.",36,6,45,Other; SQL Injection; Command Injection,58.5,High-risk application type (+8); Critical features (+4); Vulnerability keywords (+9),2025-07-05T06:22:15Z,2019-08-26T17:23:48Z,ERROR: 3; WARNING: 3,"[WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ashraf-kabir_personal-blog/admin/info.php, Line: 1) | [ERROR] php.lang.security.phpinfo-use.phpinfo-use: The 'phpinfo' function may reveal sensitive information about your environment. (File: downloaded_repos/ashraf-kabir_personal-blog/admin/info.php, Line: 1) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ashraf-kabir_personal-blog/info.php, Line: 1) | [ERROR] php.lang.security.phpinfo-use.phpinfo-use: The 'phpinfo' function may reveal sensitive information about your environment. (File: downloaded_repos/ashraf-kabir_personal-blog/info.php, Line: 1) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ashraf-kabir_personal-blog/view-posts.php, Line: 65) | [WARNING] php.lang.security.injection.tainted-callable.tainted-callable: Callable based on user input risks remote code execution. (File: downloaded_repos/ashraf-kabir_personal-blog/view-posts.php, Line: 66)"
8,JibayMcs/filament-tour,https://github.com/JibayMcs/filament-tour,https://github.com/JibayMcs/filament-tour.git,118,6,PHP,"Let's embed the power of DriverJS to your filament admin panel, and guide peoples through your app",35,4,31,Other,50.5,High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3),2025-06-19T13:47:52Z,2023-08-17T07:25:01Z,ERROR: 3; INFO: 1,"[INFO] javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring: Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string. (File: downloaded_repos/JibayMcs_filament-tour/bin/build.js, Line: 37) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/JibayMcs_filament-tour/resources/js/index.js, Line: 69) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/JibayMcs_filament-tour/resources/js/index.js, Line: 117) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/JibayMcs_filament-tour/resources/js/index.js, Line: 234)"
9,fsi-open/admin-bundle,https://github.com/fsi-open/admin-bundle,https://github.com/fsi-open/admin-bundle.git,58,11,PHP,FSi Admin Bundle is complete solution that provides mechanisms to generate admin panel for any Symfony2 project.,25,8,40,Other,45.0,High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3),2025-06-10T13:18:54Z,2013-05-22T12:17:16Z,WARNING: 8,"[WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/fsi-open_admin-bundle/Behat/Context/DataContext.php, Line: 82) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `prototypeName` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/fsi-open_admin-bundle/Resources/public/js/collection.js, Line: 15) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `prototypeName` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/fsi-open_admin-bundle/Resources/public/js/collection.js, Line: 16) | [WARNING] javascript.browser.security.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/fsi-open_admin-bundle/Resources/public/js/require.js, Line: 36) | [WARNING] yaml.docker-compose.security.no-new-privileges.no-new-privileges: Service 'web' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this. (File: downloaded_repos/fsi-open_admin-bundle/docker-compose.yml, Line: 5) | [WARNING] yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service: Service 'web' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this. (File: downloaded_repos/fsi-open_admin-bundle/docker-compose.yml, Line: 5) | [WARNING] yaml.docker-compose.security.no-new-privileges.no-new-privileges: Service 'selenium' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this. (File: downloaded_repos/fsi-open_admin-bundle/docker-compose.yml, Line: 23) | [WARNING] yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service: Service 'selenium' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this. (File: downloaded_repos/fsi-open_admin-bundle/docker-compose.yml, Line: 23)"
10,alperenersoy/filament-export,https://github.com/alperenersoy/filament-export,https://github.com/alperenersoy/filament-export.git,257,17,PHP,Customizable export and print functionality for Filament Admin Panel,44,0,0,,44.0,High-risk application type (+8); Critical features (+8); Vulnerability keywords (+3),2025-06-30T18:46:38Z,2022-05-06T19:54:09Z,No vulnerabilities found,None
