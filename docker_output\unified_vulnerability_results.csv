rank,repo_name,repo_url,clone_url,stars,issues,language,description,risk_score,semgrep_vulnerabilities,semgrep_severity_score,vulnerability_categories,final_score,risk_factors,last_updated,created_at,semgrep_findings_summary,detailed_vulnerabilities
1,uasoft-indonesia/badaso,https://github.com/uasoft-indonesia/badaso,https://github.com/uasoft-indonesia/badaso.git,1237,1,PHP,"Laravel Vue headless CMS / admin panel / dashboard / builder / API CRUD generator, anything !",59,88,100,XSS; Other,100,High-risk application type (+24); Critical features (+4); Vulnerability keywords (+6),2025-06-13T09:54:58Z,2021-03-15T04:40:50Z,WARNING: 88,"[WARNING] dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile: Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities. (File: downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile, Line: 3) | [WARNING] dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile: Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities. (File: downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile, Line: 6) | [WARNING] dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile: Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities. (File: downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile, Line: 7) | [WARNING] dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile: Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities. (File: downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile, Line: 8) | [WARNING] dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile: Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities. (File: downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile, Line: 14) | [WARNING] dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile: Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities. (File: downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile, Line: 15) | [WARNING] dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile: Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities. (File: downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile, Line: 18) | [WARNING] dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile: Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities. (File: downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile, Line: 19) | [WARNING] dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile: Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities. (File: downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile, Line: 20) | [WARNING] dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile: Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities. (File: downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile, Line: 21) | [WARNING] dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile: Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities. (File: downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile, Line: 24) | [WARNING] dockerfile.security.no-sudo-in-dockerfile.no-sudo-in-dockerfile: Avoid using sudo in Dockerfiles. Running processes as a non-root user can help  reduce the potential impact of configuration errors and security vulnerabilities. (File: downloaded_repos/uasoft-indonesia_badaso/.gitpod.Dockerfile, Line: 26) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/uasoft-indonesia_badaso/src/Controllers/BadasoDatabaseController.php, Line: 364) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/uasoft-indonesia_badaso/src/Helpers/Redis/ConfigurationRedis.php, Line: 30) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoCheckbox.vue, Line: 22) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoCheckbox.vue, Line: 34) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoCodeEditor.vue, Line: 19) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoCodeEditor.vue, Line: 30) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoCodeEditor.vue, Line: 45) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoCodeEditor.vue, Line: 57) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoColorPicker.vue, Line: 26) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoColorPicker.vue, Line: 29) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoColorPicker.vue, Line: 37) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoDate.vue, Line: 18) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoDate.vue, Line: 30) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoDatetime.vue, Line: 24) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoDatetime.vue, Line: 36) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoEditor.vue, Line: 12) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoEditor.vue, Line: 15) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoEditor.vue, Line: 23) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoEmail.vue, Line: 15) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoEmail.vue, Line: 27) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoNotificationMessage.vue, Line: 43) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoNotificationMessage.vue, Line: 85) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoNumber.vue, Line: 13) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoNumber.vue, Line: 25) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoPassword.vue, Line: 10) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoPassword.vue, Line: 22) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoRadio.vue, Line: 21) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoRadio.vue, Line: 33) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSearch.vue, Line: 12) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSearch.vue, Line: 24) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSelect.vue, Line: 18) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSelect.vue, Line: 30) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSelectMultiple.vue, Line: 19) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSelectMultiple.vue, Line: 31) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSlider.vue, Line: 13) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSlider.vue, Line: 16) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSlider.vue, Line: 24) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSwitch.vue, Line: 51) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoSwitch.vue, Line: 63) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoTags.vue, Line: 12) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoTags.vue, Line: 24) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoText.vue, Line: 18) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoText.vue, Line: 30) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoTextarea.vue, Line: 7) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoTextarea.vue, Line: 19) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoTime.vue, Line: 21) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoTime.vue, Line: 33) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUploadFile.vue, Line: 20) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUploadFile.vue, Line: 32) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUploadFileMultiple.vue, Line: 25) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUploadFileMultiple.vue, Line: 37) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUploadImage.vue, Line: 20) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUploadImage.vue, Line: 23) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUploadImage.vue, Line: 31) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUploadImageMultiple.vue, Line: 25) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUploadImageMultiple.vue, Line: 37) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUrl.vue, Line: 12) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/components/BadasoUrl.vue, Line: 23) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/forgot-password.vue, Line: 37) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/forgot-password.vue, Line: 77) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/login.vue, Line: 28) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/login.vue, Line: 52) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/register.vue, Line: 28) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/register.vue, Line: 50) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/register.vue, Line: 75) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/register.vue, Line: 97) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/register.vue, Line: 120) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/register.vue, Line: 154) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/secret-login.vue, Line: 28) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/secret-login.vue, Line: 52) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/auth/verify.vue, Line: 36) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/crud-generated/browse-bin.vue, Line: 146) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/crud-generated/browse-bin.vue, Line: 445) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/crud-generated/browse.vue, Line: 193) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/crud-generated/browse.vue, Line: 482) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/uasoft-indonesia_badaso/src/resources/badaso/pages/crud-generated/read.vue, Line: 71)"
2,moonshine-software/moonshine,https://github.com/moonshine-software/moonshine,https://github.com/moonshine-software/moonshine.git,1090,7,PHP,"Laravel Admin panel and more. Simple for beginners and powerful for experts. Using Blade, Alpine.js and Tailwind CSS.",56,16,100,Hardcoded Secrets; Other,100,High-risk application type (+24); Critical features (+4); Vulnerability keywords (+3),2025-07-27T18:55:38Z,2022-05-12T15:31:35Z,ERROR: 14; WARNING: 2,"[WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/moonshine-software_moonshine/src/Core/src/Storage/FileStorage.php, Line: 35) | [ERROR] generic.secrets.security.detected-bcrypt-hash.detected-bcrypt-hash: bcrypt hash detected (File: downloaded_repos/moonshine-software_moonshine/src/Laravel/database/factories/MoonshineUserFactory.php, Line: 27) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/ActionButton.js, Line: 29) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/BelongsToMany.js, Line: 34) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/FormBuilder.js, Line: 44) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/FormBuilder.js, Line: 176) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/FormBuilder.js, Line: 296) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/Fragment.js, Line: 67) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Components/TableBuilder.js, Line: 217) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Request/Sets.js, Line: 94) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Request/Sets.js, Line: 96) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Support/AsyncLoadContent.js, Line: 12) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Support/DOMUpdater.js, Line: 71) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Support/DOMUpdater.js, Line: 73) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Support/Iterable.js, Line: 82) | [WARNING] javascript.lang.security.audit.incomplete-sanitization.incomplete-sanitization: `inputName.replace('slide[', '').replace` method will only replace the first occurrence when used with a string argument (']'). If this method is used for escaping of dangerous data then there is a possibility for a bypass. Try to use sanitization library instead or use a Regex with a global flag. (File: downloaded_repos/moonshine-software_moonshine/src/UI/resources/js/Support/ShowWhen.js, Line: 243)"
3,Automattic/woocommerce-payments,https://github.com/Automattic/woocommerce-payments,https://github.com/Automattic/woocommerce-payments.git,181,525,PHP,"Securely accept major credit and debit cards, and allow customers to pay you directly without leaving your WooCommerce store. View and manage transactions from one convenient place - your WordPress dashboard.",51,40,100,Other,100,High-risk application type (+16); Optimal star range (+10); High issue count (+10),2025-07-28T22:51:57Z,2019-03-02T00:15:56Z,ERROR: 6; WARNING: 34,"[WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/Automattic_woocommerce-payments/bin/combine-pot-files.php, Line: 163) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/Automattic_woocommerce-payments/bin/combine-pot-files.php, Line: 164) | [WARNING] bash.lang.security.ifs-tampering.ifs-tampering: The special variable IFS affects how splitting takes place when expanding unquoted variables. Don't set it globally. Prefer a dedicated utility such as 'cut' or 'awk' if you need to split input data. If you must use 'read', set IFS locally using e.g. 'IFS="","" read -a my_array'. (File: downloaded_repos/Automattic_woocommerce-payments/bin/run-ci-tests-check-coverage.bash, Line: 5) | [WARNING] bash.lang.security.ifs-tampering.ifs-tampering: The special variable IFS affects how splitting takes place when expanding unquoted variables. Don't set it globally. Prefer a dedicated utility such as 'cut' or 'awk' if you need to split input data. If you must use 'read', set IFS locally using e.g. 'IFS="","" read -a my_array'. (File: downloaded_repos/Automattic_woocommerce-payments/bin/run-ci-tests.bash, Line: 5) | [WARNING] typescript.react.security.audit.react-dangerouslysetinnerhtml.react-dangerouslysetinnerhtml: Detection of dangerouslySetInnerHTML from non-constant definition. This can inadvertently expose users to cross-site scripting (XSS) attacks if this comes from user-provided input. If you have to use dangerouslySetInnerHTML, consider using a sanitization library such as DOMPurify to sanitize your HTML. (File: downloaded_repos/Automattic_woocommerce-payments/client/checkout/blocks/payment-processor.js, Line: 283) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/Automattic_woocommerce-payments/client/checkout/classic/payment-processing.js, Line: 485) | [WARNING] javascript.browser.security.insufficient-postmessage-origin-validation.insufficient-postmessage-origin-validation: No validation of origin is done by the addEventListener API. It may be possible to exploit this flaw to perform Cross Origin attacks such as Cross-Site Scripting(XSS). (File: downloaded_repos/Automattic_woocommerce-payments/client/checkout/woopay/email-input-iframe.js, Line: 459) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/Automattic_woocommerce-payments/client/checkout/woopay/express-button/utils.js, Line: 42) | [WARNING] javascript.browser.security.open-redirect.js-open-redirect: The application accepts potentially user-controlled input `$PROP` which can control the location of the current window context. This can lead two types of vulnerabilities open-redirection and Cross-Site-Scripting (XSS) with JavaScript URIs. It is recommended to validate user-controllable input before allowing it to control the redirection. (File: downloaded_repos/Automattic_woocommerce-payments/client/connect-account-page/index.tsx, Line: 214) | [WARNING] javascript.browser.security.open-redirect.js-open-redirect: The application accepts potentially user-controlled input `$PROP` which can control the location of the current window context. This can lead two types of vulnerabilities open-redirection and Cross-Site-Scripting (XSS) with JavaScript URIs. It is recommended to validate user-controllable input before allowing it to control the redirection. (File: downloaded_repos/Automattic_woocommerce-payments/client/connect-account-page/index.tsx, Line: 315) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/Automattic_woocommerce-payments/client/express-checkout/utils/index.ts, Line: 125) | [WARNING] javascript.browser.security.open-redirect.js-open-redirect: The application accepts potentially user-controlled input `$PROP` which can control the location of the current window context. This can lead two types of vulnerabilities open-redirection and Cross-Site-Scripting (XSS) with JavaScript URIs. It is recommended to validate user-controllable input before allowing it to control the redirection. (File: downloaded_repos/Automattic_woocommerce-payments/client/onboarding/index.tsx, Line: 25) | [WARNING] javascript.browser.security.open-redirect.js-open-redirect: The application accepts potentially user-controlled input `$PROP` which can control the location of the current window context. This can lead two types of vulnerabilities open-redirection and Cross-Site-Scripting (XSS) with JavaScript URIs. It is recommended to validate user-controllable input before allowing it to control the redirection. (File: downloaded_repos/Automattic_woocommerce-payments/client/onboarding/kyc/index.tsx, Line: 27) | [WARNING] javascript.browser.security.open-redirect.js-open-redirect: The application accepts potentially user-controlled input `$PROP` which can control the location of the current window context. This can lead two types of vulnerabilities open-redirection and Cross-Site-Scripting (XSS) with JavaScript URIs. It is recommended to validate user-controllable input before allowing it to control the redirection. (File: downloaded_repos/Automattic_woocommerce-payments/client/onboarding/steps/embedded-kyc.tsx, Line: 45) | [WARNING] javascript.browser.security.open-redirect.js-open-redirect: The application accepts potentially user-controlled input `$PROP` which can control the location of the current window context. This can lead two types of vulnerabilities open-redirection and Cross-Site-Scripting (XSS) with JavaScript URIs. It is recommended to validate user-controllable input before allowing it to control the redirection. (File: downloaded_repos/Automattic_woocommerce-payments/client/onboarding/steps/embedded-kyc.tsx, Line: 53) | [WARNING] javascript.browser.security.open-redirect.js-open-redirect: The application accepts potentially user-controlled input `$PROP` which can control the location of the current window context. This can lead two types of vulnerabilities open-redirection and Cross-Site-Scripting (XSS) with JavaScript URIs. It is recommended to validate user-controllable input before allowing it to control the redirection. (File: downloaded_repos/Automattic_woocommerce-payments/client/onboarding/steps/embedded-kyc.tsx, Line: 62) | [WARNING] javascript.browser.security.open-redirect.js-open-redirect: The application accepts potentially user-controlled input `$PROP` which can control the location of the current window context. This can lead two types of vulnerabilities open-redirection and Cross-Site-Scripting (XSS) with JavaScript URIs. It is recommended to validate user-controllable input before allowing it to control the redirection. (File: downloaded_repos/Automattic_woocommerce-payments/client/onboarding/steps/loading.tsx, Line: 29) | [WARNING] javascript.browser.security.open-redirect.js-open-redirect: The application accepts potentially user-controlled input `$PROP` which can control the location of the current window context. This can lead two types of vulnerabilities open-redirection and Cross-Site-Scripting (XSS) with JavaScript URIs. It is recommended to validate user-controllable input before allowing it to control the redirection. (File: downloaded_repos/Automattic_woocommerce-payments/client/overview/modal/progressive-onboarding-eligibility/index.tsx, Line: 41) | [WARNING] typescript.react.security.audit.react-dangerouslysetinnerhtml.react-dangerouslysetinnerhtml: Detection of dangerouslySetInnerHTML from non-constant definition. This can inadvertently expose users to cross-site scripting (XSS) attacks if this comes from user-provided input. If you have to use dangerouslySetInnerHTML, consider using a sanitization library such as DOMPurify to sanitize your HTML. (File: downloaded_repos/Automattic_woocommerce-payments/client/payment-details/payment-method/base-payment-method-details/index.tsx, Line: 89) | [WARNING] typescript.react.security.audit.react-dangerouslysetinnerhtml.react-dangerouslysetinnerhtml: Detection of dangerouslySetInnerHTML from non-constant definition. This can inadvertently expose users to cross-site scripting (XSS) attacks if this comes from user-provided input. If you have to use dangerouslySetInnerHTML, consider using a sanitization library such as DOMPurify to sanitize your HTML. (File: downloaded_repos/Automattic_woocommerce-payments/client/settings/express-checkout-settings/woopay-preview.js, Line: 108) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `words` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.highlight.js, Line: 123) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js, Line: 1430) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `className` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js, Line: 2004) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js, Line: 2100) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js, Line: 2691) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js, Line: 2707) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js, Line: 2718) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js, Line: 2739) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js, Line: 3423) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js, Line: 3783) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js, Line: 5498) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_jquery.js, Line: 6850) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_lunr.js, Line: 1825) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_lunr.js, Line: 1851) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/Automattic_woocommerce-payments/docs/rest-api/source/javascripts/lib/_lunr.js, Line: 1894) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `currencyCode` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/setup/tasks/add-currencies-task/__tests__/index.test.js, Line: 233) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `currencyCode` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/setup/tasks/add-currencies-task/__tests__/index.test.js, Line: 242) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `code` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/setup/tasks/add-currencies-task/__tests__/index.test.js, Line: 332) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `code` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/setup/tasks/add-currencies-task/__tests__/index.test.js, Line: 355) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `code` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/Automattic_woocommerce-payments/includes/multi-currency/client/setup/tasks/add-currencies-task/__tests__/index.test.js, Line: 442)"
4,AeonOrg/Aeon-MLTB,https://github.com/AeonOrg/Aeon-MLTB,https://github.com/AeonOrg/Aeon-MLTB.git,233,1,Python,"Fork of the official mltb repo: Telegram bot to download direct links, torrents, NZB, Google Drive, Telegram files, rclone clouds, yt-dlp and JDownloader sites, and upload to Google Drive, Telegram cloud, or rclone clouds. Includes two branches: main (same as official, mirror/leech bot) and extended (multi-purpose bot with extra features).",51,28,100,Deserialization; Other,100,High-risk application type (+24); Vulnerability keywords (+6); Optimal star range (+10),2025-07-25T15:50:19Z,2023-02-20T06:30:39Z,ERROR: 2; WARNING: 26,"[ERROR] dockerfile.security.missing-user.missing-user: By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'. (File: downloaded_repos/AeonOrg_Aeon-MLTB/Dockerfile, Line: 11) | [WARNING] python.lang.security.audit.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/ext_utils/bot_utils.py, Line: 215) | [WARNING] python.lang.security.audit.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/ext_utils/media_utils.py, Line: 53) | [WARNING] python.lang.security.audit.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/ext_utils/media_utils.py, Line: 105) | [WARNING] python.lang.security.audit.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/gdrive_utils/clone.py, Line: 153) | [WARNING] python.lang.security.audit.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/gdrive_utils/download.py, Line: 143) | [WARNING] python.lang.security.deserialization.pickle.avoid-pickle: Avoid using `pickle`, which is known to lead to code execution vulnerabilities. When unpickling, the serialized data could be manipulated to run arbitrary code. Instead, consider serializing the relevant data as JSON or a similar text-based serialization format. (File: downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/gdrive_utils/helper.py, Line: 94) | [WARNING] python.lang.security.audit.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/gdrive_utils/upload.py, Line: 212) | [WARNING] python.lang.security.deserialization.pickle.avoid-pickle: Avoid using `pickle`, which is known to lead to code execution vulnerabilities. When unpickling, the serialized data could be manipulated to run arbitrary code. Instead, consider serializing the relevant data as JSON or a similar text-based serialization format. (File: downloaded_repos/AeonOrg_Aeon-MLTB/bot/helper/mirror_leech_utils/youtube_utils/youtube_helper.py, Line: 75) | [WARNING] python.lang.security.audit.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/bot_settings.py, Line: 248) | [WARNING] python.lang.security.audit.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/bot_settings.py, Line: 279) | [WARNING] python.lang.security.audit.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/bot_settings.py, Line: 298) | [WARNING] python.lang.security.audit.exec-detected.exec-detected: Detected the use of exec(). exec() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/exec.py, Line: 77) | [WARNING] python.lang.security.audit.exec-detected.exec-detected: Detected the use of exec(). exec() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/exec.py, Line: 79) | [WARNING] python.lang.security.audit.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/gd_search.py, Line: 74) | [WARNING] python.lang.security.audit.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/gd_search.py, Line: 75) | [WARNING] python.lang.security.audit.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/gd_search.py, Line: 79) | [WARNING] python.lang.security.audit.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/gd_search.py, Line: 80) | [WARNING] python.lang.security.audit.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/gd_search.py, Line: 87) | [WARNING] python.lang.security.audit.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/gd_search.py, Line: 88) | [WARNING] python.lang.security.audit.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/users_settings.py, Line: 433) | [WARNING] python.lang.security.audit.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/users_settings.py, Line: 479) | [WARNING] python.lang.security.audit.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/AeonOrg_Aeon-MLTB/bot/modules/ytdlp.py, Line: 381) | [WARNING] python.lang.security.deserialization.pickle.avoid-pickle: Avoid using `pickle`, which is known to lead to code execution vulnerabilities. When unpickling, the serialized data could be manipulated to run arbitrary code. Instead, consider serializing the relevant data as JSON or a similar text-based serialization format. (File: downloaded_repos/AeonOrg_Aeon-MLTB/dev/token.py, Line: 17) | [WARNING] python.lang.security.deserialization.pickle.avoid-pickle: Avoid using `pickle`, which is known to lead to code execution vulnerabilities. When unpickling, the serialized data could be manipulated to run arbitrary code. Instead, consider serializing the relevant data as JSON or a similar text-based serialization format. (File: downloaded_repos/AeonOrg_Aeon-MLTB/dev/token.py, Line: 32) | [ERROR] python.lang.security.audit.subprocess-shell-true.subprocess-shell-true: Found 'subprocess' function 'srun' with 'shell=True'. This is dangerous because this call will spawn the command using a shell process. Doing so propagates current shell settings and variables, which makes it much easier for a malicious actor to execute commands. Use 'shell=False' instead. (File: downloaded_repos/AeonOrg_Aeon-MLTB/update.py, Line: 133) | [WARNING] html.security.audit.missing-integrity.missing-integrity: This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files. (File: downloaded_repos/AeonOrg_Aeon-MLTB/web/templates/page.html, Line: 8) | [WARNING] html.security.audit.missing-integrity.missing-integrity: This tag is missing an 'integrity' subresource integrity attribute. The 'integrity' attribute allows for the browser to verify that externally hosted files (for example from a CDN) are delivered without unexpected manipulation. Without this attribute, if an attacker can modify the externally hosted resource, this could lead to XSS and other types of attacks. To prevent this, include the base64-encoded cryptographic hash of the resource (file) you’re telling the browser to fetch in the 'integrity' attribute for all externally hosted files. (File: downloaded_repos/AeonOrg_Aeon-MLTB/web/templates/page.html, Line: 9)"
5,e107inc/e107,https://github.com/e107inc/e107,https://github.com/e107inc/e107.git,329,691,PHP,"e107 Bootstrap CMS (Content Management System) v2 with PHP, MySQL, HTML5, jQuery and Twitter Bootstrap. Issue Discussion Room: https://gitter.im/e107inc/e107 ",44,289,100,Command Injection; SQL Injection; Hardcoded Secrets; Cryptography; Other,94.0,High-risk application type (+8); Vulnerability keywords (+6); Optimal star range (+10),2025-07-20T18:33:58Z,2012-11-16T19:49:01Z,ERROR: 57; WARNING: 232,"[ERROR] php.lang.security.exec-use.exec-use: Executing non-constant commands. This can lead to command injection. (File: downloaded_repos/e107inc_e107/.github/workflows/build-release/OsHelper.php, Line: 29) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/.github/workflows/build-release/e107_make.php, Line: 312) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/.github/workflows/build-release/e107_make.php, Line: 439) | [ERROR] php.lang.security.exec-use.exec-use: Executing non-constant commands. This can lead to command injection. (File: downloaded_repos/e107inc_e107/class2.php, Line: 974) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/e107inc_e107/contact.php, Line: 197) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_admin/admin.php, Line: 694) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_admin/admin.php, Line: 711) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/e107inc_e107/e107_admin/db.php, Line: 496) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/e107inc_e107/e107_admin/db.php, Line: 1461) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_admin/emoticon.php, Line: 397) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/e107inc_e107/e107_admin/fileinspector.php, Line: 141) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_admin/fileinspector.php, Line: 1287) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_admin/fileinspector.php, Line: 1317) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_admin/image.php, Line: 2861) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/e107inc_e107/e107_admin/image.php, Line: 2861) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/e107inc_e107/e107_admin/image.php, Line: 2868) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_admin/image.php, Line: 3559) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/e107inc_e107/e107_admin/image.php, Line: 3577) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_admin/image.php, Line: 3579) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/e107inc_e107/e107_admin/image.php, Line: 3579) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_admin/image.php, Line: 3845) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_admin/image.php, Line: 3886) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/e107inc_e107/e107_admin/image.php, Line: 3886) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_admin/lancheck.php, Line: 675) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_admin/lancheck.php, Line: 693) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/e107inc_e107/e107_admin/language.php, Line: 517) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/e107inc_e107/e107_admin/language.php, Line: 577) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/e107inc_e107/e107_admin/language.php, Line: 907) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/e107inc_e107/e107_admin/language.php, Line: 910) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/e107inc_e107/e107_admin/language.php, Line: 959) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/e107inc_e107/e107_admin/mailout.php, Line: 432) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_admin/mailout.php, Line: 816) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_admin/mailout.php, Line: 1575) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_admin/mailout.php, Line: 1603) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_admin/mailout.php, Line: 1633) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_admin/mailout.php, Line: 1639) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_admin/mailout.php, Line: 1689) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_admin/mailout.php, Line: 1696) | [ERROR] php.lang.security.phpinfo-use.phpinfo-use: The 'phpinfo' function may reveal sensitive information about your environment. (File: downloaded_repos/e107inc_e107/e107_admin/phpinfo.php, Line: 25) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/e107inc_e107/e107_admin/plugin.php, Line: 320) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/e107inc_e107/e107_admin/prefs.php, Line: 405) | [ERROR] php.lang.security.exec-use.exec-use: Executing non-constant commands. This can lead to command injection. (File: downloaded_repos/e107inc_e107/e107_admin/prefs.php, Line: 837) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_admin/theme.php, Line: 1942) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_admin/update_routines.php, Line: 2151) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/e107inc_e107/e107_admin/updateadmin.php, Line: 30) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_admin/upload.php, Line: 260) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_admin/upload.php, Line: 261) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_admin/users.php, Line: 428) | [WARNING] php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect: The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs. (File: downloaded_repos/e107inc_e107/e107_admin/users.php, Line: 875) | [WARNING] php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect: The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs. (File: downloaded_repos/e107inc_e107/e107_admin/users.php, Line: 905) | [WARNING] php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect: The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs. (File: downloaded_repos/e107inc_e107/e107_admin/users.php, Line: 919) | [WARNING] php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect: The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs. (File: downloaded_repos/e107inc_e107/e107_admin/users.php, Line: 948) | [WARNING] php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect: The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs. (File: downloaded_repos/e107inc_e107/e107_admin/users.php, Line: 978) | [WARNING] php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect: The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs. (File: downloaded_repos/e107inc_e107/e107_admin/users.php, Line: 1010) | [WARNING] php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect: The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs. (File: downloaded_repos/e107inc_e107/e107_admin/users.php, Line: 1039) | [WARNING] php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect: The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs. (File: downloaded_repos/e107inc_e107/e107_admin/users.php, Line: 1045) | [WARNING] php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect: The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs. (File: downloaded_repos/e107inc_e107/e107_admin/users.php, Line: 1052) | [WARNING] php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect: The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs. (File: downloaded_repos/e107inc_e107/e107_admin/users.php, Line: 1218) | [WARNING] php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect: The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs. (File: downloaded_repos/e107inc_e107/e107_admin/users.php, Line: 1383) | [WARNING] php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect: The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs. (File: downloaded_repos/e107inc_e107/e107_admin/users.php, Line: 1395) | [WARNING] php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect: The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs. (File: downloaded_repos/e107inc_e107/e107_admin/users.php, Line: 1402) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/e107inc_e107/e107_admin/users.php, Line: 1517) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/e107inc_e107/e107_admin/users_extended.php, Line: 647) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/e107inc_e107/e107_admin/users_extended.php, Line: 687) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/e107inc_e107/e107_admin/users_extended.php, Line: 689) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_core/shortcodes/batch/admin_shortcodes.php, Line: 1131) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_core/shortcodes/batch/admin_shortcodes.php, Line: 1952) | [ERROR] php.lang.security.exec-use.exec-use: Executing non-constant commands. This can lead to command injection. (File: downloaded_repos/e107inc_e107/e107_core/sql/extended_timezones.php, Line: 73) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/e107inc_e107/e107_docs/README.html, Line: 23) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/e107inc_e107/e107_docs/README.html, Line: 23) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/e107inc_e107/e107_docs/README.html, Line: 23) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/e107inc_e107/e107_docs/README.html, Line: 23) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/e107inc_e107/e107_docs/README.html, Line: 34) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/e107inc_e107/e107_docs/README.html, Line: 34) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/e107inc_e107/e107_docs/README.html, Line: 51) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/e107inc_e107/e107_docs/README.html, Line: 161) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/e107inc_e107/e107_docs/README.html, Line: 163) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/e107inc_e107/e107_docs/README.html, Line: 168) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/e107inc_e107/e107_docs/README.html, Line: 171) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/e107inc_e107/e107_docs/README.html, Line: 192) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/e107inc_e107/e107_docs/README.html, Line: 195) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/e107inc_e107/e107_docs/README.html, Line: 196) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/e107inc_e107/e107_docs/README.html, Line: 197) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/e107inc_e107/e107_docs/README.html, Line: 198) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/e107inc_e107/e107_docs/README.html, Line: 199) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/e107inc_e107/e107_docs/README.html, Line: 200) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/e107inc_e107/e107_docs/README.html, Line: 201) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/e107inc_e107/e107_docs/README.html, Line: 202) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/e107inc_e107/e107_docs/README.html, Line: 203) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/e107inc_e107/e107_docs/README.html, Line: 204) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/e107inc_e107/e107_docs/README_UPGRADE.html, Line: 18) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/e107inc_e107/e107_docs/README_UPGRADE.html, Line: 23) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/e107inc_e107/e107_docs/README_UPGRADE.html, Line: 23) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/e107inc_e107/e107_docs/README_UPGRADE.html, Line: 23) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/e107inc_e107/e107_docs/README_UPGRADE.html, Line: 23) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/e107inc_e107/e107_docs/README_UPGRADE.html, Line: 34) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/application.php, Line: 1178) | [ERROR] php.lang.security.eval-use.eval-use: Evaluating non-constant commands. This can lead to command injection. (File: downloaded_repos/e107inc_e107/e107_handlers/bbcode_handler.php, Line: 397) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/e107inc_e107/e107_handlers/cache_handler.php, Line: 92) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/cache_handler.php, Line: 150) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/cache_handler.php, Line: 338) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/cache_handler.php, Line: 425) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_handlers/core_functions.php, Line: 585) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_handlers/core_functions.php, Line: 589) | [ERROR] php.lang.security.eval-use.eval-use: Evaluating non-constant commands. This can lead to command injection. (File: downloaded_repos/e107inc_e107/e107_handlers/core_functions.php, Line: 657) | [ERROR] php.lang.security.eval-use.eval-use: Evaluating non-constant commands. This can lead to command injection. (File: downloaded_repos/e107inc_e107/e107_handlers/core_functions.php, Line: 684) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/cron_class.php, Line: 60) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/cron_class.php, Line: 69) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/cron_class.php, Line: 70) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/e_marketplace.php, Line: 498) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/e107inc_e107/e107_handlers/e_pluginbuilder_class.php, Line: 213) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/e107inc_e107/e107_handlers/e_pluginbuilder_class.php, Line: 220) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/e107inc_e107/e107_handlers/e_pluginbuilder_class.php, Line: 222) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/e107inc_e107/e107_handlers/e_pluginbuilder_class.php, Line: 1637) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/e107inc_e107/e107_handlers/e_pluginbuilder_class.php, Line: 1665) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/e107inc_e107/e107_handlers/e_pluginbuilder_class.php, Line: 1672) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_handlers/e_ranks_class.php, Line: 38) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/e107inc_e107/e107_handlers/e_signup_class.php, Line: 151) | [ERROR] php.lang.security.curl-ssl-verifypeer-off.curl-ssl-verifypeer-off: SSL verification is disabled but should not be (currently CURLOPT_SSL_VERIFYPEER= false) (File: downloaded_repos/e107inc_e107/e107_handlers/file_class.php, Line: 627) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/file_class.php, Line: 892) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/file_class.php, Line: 1423) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/file_class.php, Line: 1452) | [ERROR] php.lang.security.backticks-use.backticks-use: Backticks use may lead to command injection vulnerabilities. (File: downloaded_repos/e107inc_e107/e107_handlers/file_class.php, Line: 1631) | [ERROR] php.lang.security.backticks-use.backticks-use: Backticks use may lead to command injection vulnerabilities. (File: downloaded_repos/e107inc_e107/e107_handlers/file_class.php, Line: 1632) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/file_class.php, Line: 1763) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/file_class.php, Line: 1775) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/file_class.php, Line: 1786) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/file_class.php, Line: 1795) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/file_class.php, Line: 1876) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/e107inc_e107/e107_handlers/jslib_handler.php, Line: 191) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/e107inc_e107/e107_handlers/jslib_handler.php, Line: 191) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/library_manager.php, Line: 2291) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/library_manager.php, Line: 2303) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/e107inc_e107/e107_handlers/login.php, Line: 476) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/e107inc_e107/e107_handlers/mail_manager_class.php, Line: 1546) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_handlers/media_class.php, Line: 1110) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/media_class.php, Line: 2098) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/e107inc_e107/e107_handlers/media_class.php, Line: 2146) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/e107inc_e107/e107_handlers/media_class.php, Line: 2153) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/media_class.php, Line: 2180) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/media_class.php, Line: 2233) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/media_class.php, Line: 2329) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/media_class.php, Line: 2336) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_handlers/menu_class.php, Line: 185) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_handlers/menu_class.php, Line: 299) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_handlers/menu_class.php, Line: 411) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_handlers/menu_class.php, Line: 644) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_handlers/menumanager_class.php, Line: 755) | [ERROR] php.lang.security.eval-use.eval-use: Evaluating non-constant commands. This can lead to command injection. (File: downloaded_repos/e107inc_e107/e107_handlers/menumanager_class.php, Line: 2567) | [ERROR] php.lang.security.eval-use.eval-use: Evaluating non-constant commands. This can lead to command injection. (File: downloaded_repos/e107inc_e107/e107_handlers/menumanager_class.php, Line: 2581) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/pcltar.lib.php, Line: 1065) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/pcltar.lib.php, Line: 1320) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/pcltar.lib.php, Line: 2898) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/pcltar.lib.php, Line: 3020) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/pcltar.lib.php, Line: 3199) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/pcltar.lib.php, Line: 3318) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/pcltar.lib.php, Line: 3371) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/pcltar.lib.php, Line: 3407) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/pclzip.lib.php, Line: 2281) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/pclzip.lib.php, Line: 2307) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/pclzip.lib.php, Line: 2361) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/pclzip.lib.php, Line: 2942) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/pclzip.lib.php, Line: 4019) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/pclzip.lib.php, Line: 4832) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/pclzip.lib.php, Line: 4847) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/pclzip.lib.php, Line: 4864) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/pclzip.lib.php, Line: 4875) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/pclzip.lib.php, Line: 4891) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/pclzip.lib.php, Line: 4916) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/pclzip.lib.php, Line: 4928) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/pclzip.lib.php, Line: 5182) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/pclzip.lib.php, Line: 5611) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_handlers/pref_class.php, Line: 485) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_handlers/pref_class.php, Line: 490) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_handlers/pref_class.php, Line: 563) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_handlers/pref_class.php, Line: 1204) | [WARNING] php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect: The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs. (File: downloaded_repos/e107inc_e107/e107_handlers/redirection_class.php, Line: 257) | [WARNING] php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect: The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs. (File: downloaded_repos/e107inc_e107/e107_handlers/redirection_class.php, Line: 320) | [WARNING] php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect: The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs. (File: downloaded_repos/e107inc_e107/e107_handlers/redirection_class.php, Line: 350) | [WARNING] php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect: The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs. (File: downloaded_repos/e107inc_e107/e107_handlers/redirection_class.php, Line: 361) | [ERROR] php.lang.security.exec-use.exec-use: Executing non-constant commands. This can lead to command injection. (File: downloaded_repos/e107inc_e107/e107_handlers/resize_handler.php, Line: 162) | [ERROR] php.lang.security.exec-use.exec-use: Executing non-constant commands. This can lead to command injection. (File: downloaded_repos/e107inc_e107/e107_handlers/resize_handler.php, Line: 167) | [ERROR] php.lang.security.eval-use.eval-use: Evaluating non-constant commands. This can lead to command injection. (File: downloaded_repos/e107inc_e107/e107_handlers/shortcode_handler.php, Line: 1362) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/e107inc_e107/e107_handlers/shortcode_handler.php, Line: 1689) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_handlers/upload_handler.php, Line: 412) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_handlers/user_extended_class.php, Line: 339) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_handlers/user_extended_class.php, Line: 1234) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_handlers/user_extended_class.php, Line: 1313) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_handlers/user_extended_class.php, Line: 1628) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_handlers/user_extended_class.php, Line: 1852) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/e107inc_e107/e107_handlers/user_handler.php, Line: 463) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_handlers/userclass_class.php, Line: 163) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/e107inc_e107/e107_plugins/alt_auth/extended_password_handler.php, Line: 403) | [WARNING] php.lang.security.ldap-bind-without-password.ldap-bind-without-password: Detected anonymous LDAP bind. This permits anonymous users to execute LDAP statements. Consider enforcing authentication for LDAP. (File: downloaded_repos/e107inc_e107/e107_plugins/alt_auth/ldap_auth.php, Line: 178) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/e107inc_e107/e107_plugins/chatbox_menu/chat.php, Line: 79) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/e107inc_e107/e107_plugins/chatbox_menu/chat.php, Line: 92) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_plugins/download/download_setup.php, Line: 87) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_plugins/download/download_setup.php, Line: 88) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_plugins/download/includes/admin.php, Line: 600) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_plugins/download/includes/admin.php, Line: 607) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/e107inc_e107/e107_plugins/download/includes/admin.php, Line: 1745) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/e107inc_e107/e107_plugins/download/includes/admin.php, Line: 1749) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/e107inc_e107/e107_plugins/download/includes/admin.php, Line: 1753) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/e107inc_e107/e107_plugins/download/request.php, Line: 141) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/e107inc_e107/e107_plugins/forum/forum_admin.php, Line: 1147) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_plugins/forum/forum_admin.php, Line: 1172) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_plugins/forum/forum_admin.php, Line: 1204) | [WARNING] php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect: The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs. (File: downloaded_repos/e107inc_e107/e107_plugins/forum/forum_post.php, Line: 132) | [WARNING] php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect: The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs. (File: downloaded_repos/e107inc_e107/e107_plugins/forum/forum_post.php, Line: 187) | [WARNING] php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect: The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs. (File: downloaded_repos/e107inc_e107/e107_plugins/forum/forum_post.php, Line: 1300) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_plugins/forum/forum_post.php, Line: 1484) | [WARNING] php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect: The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs. (File: downloaded_repos/e107inc_e107/e107_plugins/forum/forum_post.php, Line: 1523) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_plugins/forum/forum_post.php, Line: 1564) | [WARNING] php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect: The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs. (File: downloaded_repos/e107inc_e107/e107_plugins/forum/forum_post.php, Line: 1599) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_plugins/forum/forum_post.php, Line: 1753) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_plugins/forum/forum_setup.php, Line: 118) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_plugins/forum/forum_setup.php, Line: 119) | [WARNING] php.lang.security.injection.tainted-callable.tainted-callable: Callable based on user input risks remote code execution. (File: downloaded_repos/e107inc_e107/e107_plugins/forum/forum_update.php, Line: 69) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_plugins/forum/forum_update.php, Line: 806) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_plugins/forum/forum_update.php, Line: 1188) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_plugins/forum/forum_update.php, Line: 1343) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_plugins/forum/forum_uploads.php, Line: 37) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/e107inc_e107/e107_plugins/forum/forum_uploads.php, Line: 37) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_plugins/forum/forum_viewforum.php, Line: 372) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_plugins/forum/forum_viewtopic.php, Line: 295) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_plugins/forum/shortcodes/batch/view_shortcodes.php, Line: 403) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_plugins/import/providers/drupal_import_class.php, Line: 347) | [WARNING] php.lang.security.injection.tainted-url-host.tainted-url-host: User data flows into the host portion of this manually-constructed URL. This could allow an attacker to send data to their own server, potentially exposing sensitive data such as cookies or authorization information sent with this request. They could also probe internal servers or other resources that the server running this code can access. (This is called server-side request forgery, or SSRF.) Do not allow arbitrary hosts. Instead, create an allowlist for approved hosts, or hardcode the correct host. (File: downloaded_repos/e107inc_e107/e107_plugins/import/providers/livejournal_import_class.php, Line: 47) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_plugins/import/providers/wordpress_import_class.php, Line: 166) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_plugins/linkwords/e_parse.php, Line: 126) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_plugins/linkwords/e_parse.php, Line: 403) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_plugins/news/news.php, Line: 857) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/e107inc_e107/e107_plugins/news/news.php, Line: 1013) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/e107inc_e107/e107_plugins/news/news.php, Line: 1035) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_plugins/newsfeed/newsfeed_functions.php, Line: 82) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_plugins/newsfeed/newsfeed_functions.php, Line: 263) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_plugins/pm/admin_config.php, Line: 723) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_plugins/pm/e_cron.php, Line: 141) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/e107inc_e107/e107_plugins/pm/pm.php, Line: 733) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_plugins/pm/pm_class.php, Line: 340) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_plugins/pm/pm_conf.php, Line: 779) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_plugins/siteinfo/counter_menu.php, Line: 75) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/e107inc_e107/e107_plugins/social/ie7/ie7.js, Line: 13) | [WARNING] javascript.browser.security.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/e107inc_e107/e107_plugins/tinymce4/plugins/compat3x/tiny_mce_popup.js, Line: 192) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/e107inc_e107/e107_plugins/tinymce4/plugins/compat3x/tiny_mce_popup.js, Line: 237) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/e107inc_e107/e107_plugins/tinymce4/plugins/compat3x/tiny_mce_popup.js, Line: 377) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `p` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/e107inc_e107/e107_plugins/tinymce4/plugins/compat3x/utils/validate.js, Line: 73) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `c` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/e107inc_e107/e107_plugins/tinymce4/plugins/compat3x/utils/validate.js, Line: 221) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `c` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/e107inc_e107/e107_plugins/tinymce4/plugins/compat3x/utils/validate.js, Line: 225) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `c` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/e107inc_e107/e107_plugins/tinymce4/plugins/compat3x/utils/validate.js, Line: 237) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `smiley` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/e107inc_e107/e107_plugins/tinymce4/plugins/smileys/plugin.js, Line: 262) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `item` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/e107inc_e107/e107_plugins/tinymce4/plugins/smileys/plugin.js, Line: 269) | [WARNING] go.lang.security.audit.crypto.missing-ssl-minversion.missing-ssl-minversion: `MinVersion` is missing from this TLS configuration.  By default, as of Go 1.22, TLS 1.2 is currently used as the minimum. General purpose web applications should default to TLS 1.3 with all other protocols disabled.  Only where it is known that a web server must support legacy clients with unsupported an insecure browsers (such as Internet Explorer 10), it may be necessary to enable TLS 1.0 to provide support. Add `MinVersion: tls.VersionTLS13' to the TLS configuration to bump the minimum version to TLS 1.3. (File: downloaded_repos/e107inc_e107/e107_tests/lib/ci/salt/salt-bootstrap.go, Line: 22) | [WARNING] problem-based-packs.insecure-transport.go-stdlib.bypass-tls-verification.bypass-tls-verification: Checks for disabling of TLS/SSL certificate verification. This should only be used for debugging purposes because it leads to vulnerability to MTM attacks. (File: downloaded_repos/e107inc_e107/e107_tests/lib/ci/salt/salt-bootstrap.go, Line: 22) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/e107_tests/lib/deployers/DeployerFactory.php, Line: 25) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_tests/lib/deployers/LocalDeployer.php, Line: 10) | [ERROR] generic.secrets.security.detected-ssh-password.detected-ssh-password: SSH Password detected (File: downloaded_repos/e107inc_e107/e107_tests/lib/deployers/SFTPDeployer.php, Line: 25) | [ERROR] php.lang.security.exec-use.exec-use: Executing non-constant commands. This can lead to command injection. (File: downloaded_repos/e107inc_e107/e107_tests/lib/deployers/SFTPDeployer.php, Line: 54) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_tests/lib/preparers/E107Preparer.php, Line: 53) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_tests/lib/preparers/GitPreparer.php, Line: 57) | [ERROR] php.lang.security.exec-use.exec-use: Executing non-constant commands. This can lead to command injection. (File: downloaded_repos/e107inc_e107/e107_tests/lib/preparers/GitPreparer.php, Line: 98) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/e107_tests/lib/preparers/GitPreparer.php, Line: 132) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `c` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/e107inc_e107/e107_web/js/bootstrap-jasny/js/jasny-bootstrap.js, Line: 584) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `$` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/e107inc_e107/e107_web/js/bootstrap3-editable/js/bootstrap-editable.js, Line: 3284) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `v` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/e107inc_e107/e107_web/js/bootstrap3-editable/js/bootstrap-editable.js, Line: 4026) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `prefix` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/e107inc_e107/e107_web/js/bootstrap3-editable/js/bootstrap-editable.js, Line: 5852) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `prefix` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/e107inc_e107/e107_web/js/bootstrap3-editable/js/bootstrap-editable.js, Line: 5853) | [WARNING] javascript.browser.security.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/e107inc_e107/e107_web/js/chart/mathFunctions.js, Line: 75) | [WARNING] javascript.browser.security.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/e107inc_e107/e107_web/js/chart/mathFunctions.js, Line: 112) | [WARNING] javascript.lang.security.audit.incomplete-sanitization.incomplete-sanitization: `text.replace` method will only replace the first occurrence when used with a string argument ('*'). If this method is used for escaping of dangerous data then there is a possibility for a bypass. Try to use sanitization library instead or use a Regex with a global flag. (File: downloaded_repos/e107inc_e107/e107_web/js/core/all.jquery.js, Line: 1540) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/e107inc_e107/e107_web/js/core/all.jquery.js, Line: 1557) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/e107inc_e107/e107_web/js/core/all.jquery.js, Line: 1818) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `baseid` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/e107inc_e107/e107_web/js/core/all.jquery.js, Line: 1818) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/e107inc_e107/e107_web/js/e_jslib.php, Line: 138) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/e107inc_e107/e107_web/js/e_jslib.php, Line: 138) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `pattern` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/e107inc_e107/e107_web/js/selectize/js/selectize.js, Line: 40) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `query` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/e107inc_e107/e107_web/js/selectize/js/selectize.js, Line: 322) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/e107inc_e107/e107_web/js/selectize/js/selectize.js, Line: 682) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/e107inc_e107/e107_web/lib/bootstrap/3/js/bootstrap.js, Line: 1401) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `e` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/e107inc_e107/e107_web/lib/jquery.prettyPhoto/js/jquery.prettyPhoto.js, Line: 7) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/e107inc_e107/e107_web/lib/jquery.prettyPhoto/js/jquery.prettyPhoto.js, Line: 7) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/install.php, Line: 268) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/e107inc_e107/install.php, Line: 404) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/e107inc_e107/submitnews.php, Line: 165) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/upload.php, Line: 175) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/upload.php, Line: 176) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/e107inc_e107/user.php, Line: 97) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/e107inc_e107/user.php, Line: 239) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/e107inc_e107/userposts.php, Line: 186)"
6,ClassicPress/ClassicPress,https://github.com/ClassicPress/ClassicPress,https://github.com/ClassicPress/ClassicPress.git,808,43,PHP,The CMS for Creators. Stable. Lightweight. Instantly Familiar. Forked from WordPress.,41,340,100,Command Injection; SQL Injection; Other,91.0,High-risk application type (+16); Optimal star range (+10); High-risk language: php (+10),2025-07-28T17:37:02Z,2018-08-29T10:02:51Z,ERROR: 190; WARNING: 150,"[WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/admin.php, Line: 265) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/admin.php, Line: 265) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/admin.php, Line: 266) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/admin.php, Line: 266) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/admin.php, Line: 290) | [WARNING] php.lang.security.injection.tainted-callable.tainted-callable: Callable based on user input risks remote code execution. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/admin.php, Line: 364) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/comment.php, Line: 284) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/comment.php, Line: 359) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/edit-tags.php, Line: 112) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/edit-tags.php, Line: 168) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/ajax-actions.php, Line: 661) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/ajax-actions.php, Line: 720) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/ajax-actions.php, Line: 748) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/ajax-actions.php, Line: 774) | [WARNING] php.lang.security.injection.tainted-object-instantiation.tainted-object-instantiation: <- A new object is created where the class name is based on user input. This could lead to remote code execution, as it allows to instantiate any class in the application. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/ajax-actions.php, Line: 1521) | [WARNING] php.lang.security.injection.tainted-callable.tainted-callable: Callable based on user input risks remote code execution. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/ajax-actions.php, Line: 2362) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/ajax-actions.php, Line: 2689) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/ajax-actions.php, Line: 2719) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/ajax-actions.php, Line: 2824) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/ajax-actions.php, Line: 2870) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/ajax-actions.php, Line: 3087) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/ajax-actions.php, Line: 3179) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/ajax-actions.php, Line: 3235) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/ajax-actions.php, Line: 3398) | [WARNING] php.lang.security.injection.tainted-url-host.tainted-url-host: User data flows into the host portion of this manually-constructed URL. This could allow an attacker to send data to their own server, potentially exposing sensitive data such as cookies or authorization information sent with this request. They could also probe internal servers or other resources that the server running this code can access. (This is called server-side request forgery, or SSRF.) Do not allow arbitrary hosts. Instead, create an allowlist for approved hosts, or hardcode the correct host. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/ajax-actions.php, Line: 3563) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/ajax-actions.php, Line: 4362) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/ajax-actions.php, Line: 4826) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/ajax-actions.php, Line: 4826) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/ajax-actions.php, Line: 5122) | [WARNING] php.lang.security.injection.tainted-callable.tainted-callable: Callable based on user input risks remote code execution. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/ajax-actions.php, Line: 5414) | [WARNING] php.lang.security.injection.tainted-callable.tainted-callable: Callable based on user input risks remote code execution. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/ajax-actions.php, Line: 5604) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-custom-image-header.php, Line: 843) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-custom-image-header.php, Line: 872) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-custom-image-header.php, Line: 1084) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-file-upload-upgrader.php, Line: 153) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-pclzip.php, Line: 2243) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-pclzip.php, Line: 2271) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-pclzip.php, Line: 2326) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-pclzip.php, Line: 2903) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-pclzip.php, Line: 4040) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-pclzip.php, Line: 4880) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-pclzip.php, Line: 4895) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-pclzip.php, Line: 4913) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-pclzip.php, Line: 4924) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-pclzip.php, Line: 4940) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-pclzip.php, Line: 4966) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-pclzip.php, Line: 4978) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-pclzip.php, Line: 5242) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-pclzip.php, Line: 5665) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-direct.php, Line: 382) | [ERROR] php.lang.security.ftp-use.ftp-use: FTP allows for unencrypted file transfers. Consider using an encrypted alternative. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpext.php, Line: 87) | [ERROR] php.lang.security.ftp-use.ftp-use: FTP allows for unencrypted file transfers. Consider using an encrypted alternative. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpext.php, Line: 89) | [ERROR] php.lang.security.ftp-use.ftp-use: FTP allows for unencrypted file transfers. Consider using an encrypted alternative. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpext.php, Line: 119) | [ERROR] php.lang.security.ftp-use.ftp-use: FTP allows for unencrypted file transfers. Consider using an encrypted alternative. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpext.php, Line: 122) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpext.php, Line: 142) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpext.php, Line: 148) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpext.php, Line: 160) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpext.php, Line: 193) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpext.php, Line: 206) | [ERROR] php.lang.security.ftp-use.ftp-use: FTP allows for unencrypted file transfers. Consider using an encrypted alternative. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpext.php, Line: 212) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpext.php, Line: 215) | [ERROR] php.lang.security.ftp-use.ftp-use: FTP allows for unencrypted file transfers. Consider using an encrypted alternative. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpext.php, Line: 230) | [ERROR] php.lang.security.ftp-use.ftp-use: FTP allows for unencrypted file transfers. Consider using an encrypted alternative. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpext.php, Line: 248) | [ERROR] php.lang.security.ftp-use.ftp-use: FTP allows for unencrypted file transfers. Consider using an encrypted alternative. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpext.php, Line: 285) | [ERROR] php.lang.security.ftp-use.ftp-use: FTP allows for unencrypted file transfers. Consider using an encrypted alternative. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpext.php, Line: 288) | [ERROR] php.lang.security.ftp-use.ftp-use: FTP allows for unencrypted file transfers. Consider using an encrypted alternative. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpext.php, Line: 379) | [ERROR] php.lang.security.ftp-use.ftp-use: FTP allows for unencrypted file transfers. Consider using an encrypted alternative. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpext.php, Line: 400) | [ERROR] php.lang.security.ftp-use.ftp-use: FTP allows for unencrypted file transfers. Consider using an encrypted alternative. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpext.php, Line: 404) | [ERROR] php.lang.security.ftp-use.ftp-use: FTP allows for unencrypted file transfers. Consider using an encrypted alternative. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpext.php, Line: 415) | [ERROR] php.lang.security.ftp-use.ftp-use: FTP allows for unencrypted file transfers. Consider using an encrypted alternative. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpext.php, Line: 427) | [ERROR] php.lang.security.ftp-use.ftp-use: FTP allows for unencrypted file transfers. Consider using an encrypted alternative. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpext.php, Line: 458) | [ERROR] php.lang.security.ftp-use.ftp-use: FTP allows for unencrypted file transfers. Consider using an encrypted alternative. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpext.php, Line: 461) | [ERROR] php.lang.security.ftp-use.ftp-use: FTP allows for unencrypted file transfers. Consider using an encrypted alternative. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpext.php, Line: 513) | [ERROR] php.lang.security.ftp-use.ftp-use: FTP allows for unencrypted file transfers. Consider using an encrypted alternative. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpext.php, Line: 525) | [ERROR] php.lang.security.ftp-use.ftp-use: FTP allows for unencrypted file transfers. Consider using an encrypted alternative. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpext.php, Line: 600) | [ERROR] php.lang.security.ftp-use.ftp-use: FTP allows for unencrypted file transfers. Consider using an encrypted alternative. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpext.php, Line: 726) | [ERROR] php.lang.security.ftp-use.ftp-use: FTP allows for unencrypted file transfers. Consider using an encrypted alternative. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpext.php, Line: 732) | [ERROR] php.lang.security.ftp-use.ftp-use: FTP allows for unencrypted file transfers. Consider using an encrypted alternative. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpext.php, Line: 734) | [ERROR] php.lang.security.ftp-use.ftp-use: FTP allows for unencrypted file transfers. Consider using an encrypted alternative. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpext.php, Line: 789) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpsockets.php, Line: 146) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpsockets.php, Line: 154) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpsockets.php, Line: 171) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpsockets.php, Line: 204) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpsockets.php, Line: 215) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-filesystem-ftpsockets.php, Line: 229) | [WARNING] php.lang.security.injection.tainted-callable.tainted-callable: Callable based on user input risks remote code execution. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-list-table.php, Line: 701) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-list-table.php, Line: 701) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/class-wp-upgrader.php, Line: 385) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/file.php, Line: 995) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/file.php, Line: 1158) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/file.php, Line: 1188) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/file.php, Line: 1215) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/file.php, Line: 1226) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/file.php, Line: 1304) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/file.php, Line: 2188) | [WARNING] php.lang.security.injection.tainted-url-host.tainted-url-host: User data flows into the host portion of this manually-constructed URL. This could allow an attacker to send data to their own server, potentially exposing sensitive data such as cookies or authorization information sent with this request. They could also probe internal servers or other resources that the server running this code can access. (This is called server-side request forgery, or SSRF.) Do not allow arbitrary hosts. Instead, create an allowlist for approved hosts, or hardcode the correct host. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/media.php, Line: 906) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/media.php, Line: 1077) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/ms.php, Line: 937) | [WARNING] php.lang.security.injection.tainted-callable.tainted-callable: Callable based on user input risks remote code execution. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/post.php, Line: 430) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/privacy-tools.php, Line: 570) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/privacy-tools.php, Line: 573) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/schema.php, Line: 1134) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/upgrade.php, Line: 2379) | [WARNING] php.lang.security.injection.tainted-url-host.tainted-url-host: User data flows into the host portion of this manually-constructed URL. This could allow an attacker to send data to their own server, potentially exposing sensitive data such as cookies or authorization information sent with this request. They could also probe internal servers or other resources that the server running this code can access. (This is called server-side request forgery, or SSRF.) Do not allow arbitrary hosts. Instead, create an allowlist for approved hosts, or hardcode the correct host. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/includes/user.php, Line: 89) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/customize-nav-menus.js, Line: 69) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `term` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/customize-widgets.js, Line: 98) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/customize-widgets.js, Line: 1290) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/customize-widgets.js, Line: 1894) | [WARNING] javascript.lang.security.audit.incomplete-sanitization.incomplete-sanitization: `stops[first].replace` method will only replace the first occurrence when used with a string argument ('%'). If this method is used for escaping of dangerous data then there is a possibility for a bypass. Try to use sanitization library instead or use a Regex with a global flag. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/iris.js, Line: 199) | [WARNING] javascript.lang.security.audit.incomplete-sanitization.incomplete-sanitization: `stops[last].replace` method will only replace the first occurrence when used with a string argument ('%'). If this method is used for escaping of dangerous data then there is a possibility for a bypass. Try to use sanitization library instead or use a Regex with a global flag. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/iris.js, Line: 200) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/media.js, Line: 276) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/media.js, Line: 332) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/media.js, Line: 644) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/media.js, Line: 821) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/media.js, Line: 827) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/media.js, Line: 845) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/nav-menu.js, Line: 491) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/nav-menu.js, Line: 494) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/nav-menu.js, Line: 519) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `item` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/nav-menu.js, Line: 519) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/nav-menu.js, Line: 1014) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/nav-menu.js, Line: 1048) | [WARNING] typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method: Detection of insertAdjacentHTML from non-constant definition. This can inadvertently expose users to cross-site scripting (XSS) attacks if this comes from user-provided input. If you have to use insertAdjacentHTML, consider using a sanitization library such as DOMPurify to sanitize your HTML. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/plugin-install.js, Line: 46) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/revisions.js, Line: 150) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/revisions.js, Line: 213) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/revisions.js, Line: 214) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/revisions.js, Line: 215) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/revisions.js, Line: 276) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/revisions.js, Line: 277) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/revisions.js, Line: 278) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/theme.js, Line: 132) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/theme.js, Line: 171) | [WARNING] typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method: Detection of insertAdjacentHTML from non-constant definition. This can inadvertently expose users to cross-site scripting (XSS) attacks if this comes from user-provided input. If you have to use insertAdjacentHTML, consider using a sanitization library such as DOMPurify to sanitize your HTML. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/theme.js, Line: 216) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/theme.js, Line: 334) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/theme.js, Line: 346) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/theme.js, Line: 349) | [WARNING] typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method: Detection of insertAdjacentHTML from non-constant definition. This can inadvertently expose users to cross-site scripting (XSS) attacks if this comes from user-provided input. If you have to use insertAdjacentHTML, consider using a sanitization library such as DOMPurify to sanitize your HTML. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/theme.js, Line: 574) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets.js, Line: 272) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets.js, Line: 407) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets.js, Line: 443) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets.js, Line: 572) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets.js, Line: 578) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets.js, Line: 707) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets/media-audio-widget.js, Line: 21) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets/media-audio-widget.js, Line: 45) | [WARNING] javascript.lang.security.audit.incomplete-sanitization.incomplete-sanitization: `input.getAttribute( 'name' ).replace( 'attachments[' + id + '][' , '' ).replace` method will only replace the first occurrence when used with a string argument (']'). If this method is used for escaping of dangerous data then there is a possibility for a bypass. Try to use sanitization library instead or use a Regex with a global flag. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets/media-audio-widget.js, Line: 142) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets/media-audio-widget.js, Line: 455) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets/media-audio-widget.js, Line: 756) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets/media-audio-widget.js, Line: 774) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets/media-audio-widget.js, Line: 861) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets/media-gallery-widget.js, Line: 22) | [WARNING] javascript.lang.security.audit.incomplete-sanitization.incomplete-sanitization: `input.getAttribute( 'name' ).replace( 'attachments[' + id + '][' , '' ).replace` method will only replace the first occurrence when used with a string argument (']'). If this method is used for escaping of dangerous data then there is a possibility for a bypass. Try to use sanitization library instead or use a Regex with a global flag. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets/media-gallery-widget.js, Line: 123) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets/media-gallery-widget.js, Line: 383) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets/media-gallery-widget.js, Line: 479) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets/media-gallery-widget.js, Line: 1361) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets/media-gallery-widget.js, Line: 1375) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets/media-image-widget.js, Line: 21) | [WARNING] javascript.lang.security.audit.incomplete-sanitization.incomplete-sanitization: `input.getAttribute( 'name' ).replace( 'attachments[' + id + '][' , '' ).replace` method will only replace the first occurrence when used with a string argument (']'). If this method is used for escaping of dangerous data then there is a possibility for a bypass. Try to use sanitization library instead or use a Regex with a global flag. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets/media-image-widget.js, Line: 122) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets/media-image-widget.js, Line: 383) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets/media-image-widget.js, Line: 483) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets/media-image-widget.js, Line: 769) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets/media-image-widget.js, Line: 803) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets/media-image-widget.js, Line: 969) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets/media-image-widget.js, Line: 974) | [WARNING] typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method: Detection of insertAdjacentHTML from non-constant definition. This can inadvertently expose users to cross-site scripting (XSS) attacks if this comes from user-provided input. If you have to use insertAdjacentHTML, consider using a sanitization library such as DOMPurify to sanitize your HTML. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets/media-image-widget.js, Line: 1051) | [WARNING] typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method: Detection of insertAdjacentHTML from non-constant definition. This can inadvertently expose users to cross-site scripting (XSS) attacks if this comes from user-provided input. If you have to use insertAdjacentHTML, consider using a sanitization library such as DOMPurify to sanitize your HTML. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets/media-image-widget.js, Line: 1233) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets/media-video-widget.js, Line: 21) | [WARNING] javascript.lang.security.audit.incomplete-sanitization.incomplete-sanitization: `input.getAttribute( 'name' ).replace( 'attachments[' + id + '][' , '' ).replace` method will only replace the first occurrence when used with a string argument (']'). If this method is used for escaping of dangerous data then there is a possibility for a bypass. Try to use sanitization library instead or use a Regex with a global flag. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets/media-video-widget.js, Line: 118) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets/media-video-widget.js, Line: 426) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets/media-video-widget.js, Line: 727) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets/media-video-widget.js, Line: 745) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/widgets/media-video-widget.js, Line: 832) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `settings` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/js/word-count.js, Line: 66) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/link.php, Line: 85) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/link.php, Line: 94) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/nav-menus.php, Line: 281) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/nav-menus.php, Line: 297) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/nav-menus.php, Line: 1228) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/network/site-settings.php, Line: 124) | [WARNING] php.lang.security.injection.tainted-callable.tainted-callable: Callable based on user input risks remote code execution. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/network/site-settings.php, Line: 128) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/network/sites.php, Line: 166) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/network/themes.php, Line: 93) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/options.php, Line: 406) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/plugin-editor.php, Line: 109) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/plugin-editor.php, Line: 162) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/plugin-editor.php, Line: 214) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/plugin-editor.php, Line: 222) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/plugin-editor.php, Line: 294) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/plugins.php, Line: 166) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/plugins.php, Line: 361) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/post.php, Line: 206) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/post.php, Line: 220) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/post.php, Line: 308) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/post.php, Line: 337) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/setup-config.php, Line: 325) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/themes.php, Line: 57) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/update-core.php, Line: 983) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/update-core.php, Line: 1024) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/update-core.php, Line: 1087) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/update.php, Line: 44) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/update.php, Line: 71) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/update.php, Line: 85) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/update.php, Line: 87) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/update.php, Line: 161) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/update.php, Line: 250) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/update.php, Line: 273) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/update.php, Line: 317) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/update.php, Line: 394) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/users.php, Line: 185) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/widgets-form.php, Line: 127) | [WARNING] php.lang.security.injection.tainted-callable.tainted-callable: Callable based on user input risks remote code execution. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/widgets-form.php, Line: 291) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-admin/widgets-form.php, Line: 349) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-content/themes/twentyseventeen/assets/js/html5.js, Line: 71) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/ID3/getid3.lib.php, Line: 1534) | [ERROR] php.lang.security.backticks-use.backticks-use: Backticks use may lead to command injection vulnerabilities. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/ID3/getid3.lib.php, Line: 1791) | [ERROR] php.lang.security.backticks-use.backticks-use: Backticks use may lead to command injection vulnerabilities. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/ID3/getid3.php, Line: 488) | [ERROR] php.lang.security.backticks-use.backticks-use: Backticks use may lead to command injection vulnerabilities. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/ID3/getid3.php, Line: 1817) | [ERROR] php.lang.security.backticks-use.backticks-use: Backticks use may lead to command injection vulnerabilities. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/ID3/getid3.php, Line: 1828) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/ID3/getid3.php, Line: 1852) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/ID3/getid3.php, Line: 1853) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/ID3/getid3.php, Line: 2478) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/ID3/module.audio-video.matroska.php, Line: 1390) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/ID3/module.audio-video.riff.php, Line: 1980) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/ID3/module.audio.flac.php, Line: 199) | [ERROR] php.lang.security.exec-use.exec-use: Executing non-constant commands. This can lead to command injection. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/PHPMailer/PHPMailer.php, Line: 1831) | [ERROR] php.lang.security.exec-use.exec-use: Executing non-constant commands. This can lead to command injection. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/PHPMailer/PHPMailer.php, Line: 1857) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/PHPMailer/PHPMailer.php, Line: 3189) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/PHPMailer/PHPMailer.php, Line: 3192) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/PHPMailer/PHPMailer.php, Line: 3198) | [ERROR] php.lang.security.curl-ssl-verifypeer-off.curl-ssl-verifypeer-off: SSL verification is disabled but should not be (currently CURLOPT_SSL_VERIFYPEER= 0) (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/Requests/src/Transport/Curl.php, Line: 194) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/SimplePie/src/Cache/File.php, Line: 127) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/SimplePie/src/Cache/File.php, Line: 160) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/SimplePie/src/Cache/Memcache.php, Line: 135) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/SimplePie/src/Cache/Memcached.php, Line: 131) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/SimplePie/src/Cache/MySQL.php, Line: 282) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/SimplePie/src/Cache/MySQL.php, Line: 313) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/SimplePie/src/Cache/Redis.php, Line: 156) | [ERROR] php.lang.security.exec-use.exec-use: Executing non-constant commands. This can lead to command injection. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/Text/Diff/Engine/shell.php, Line: 50) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/Text/Diff/Engine/shell.php, Line: 51) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/Text/Diff/Engine/shell.php, Line: 52) | [ERROR] php.lang.security.exec-use.exec-use: Executing non-constant commands. This can lead to command injection. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/class-snoopy.php, Line: 1018) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/class-snoopy.php, Line: 1084) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/class-wp-customize-widgets.php, Line: 1382) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/class-wp-query.php, Line: 1903) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/class-wp.php, Line: 516) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/class-wp.php, Line: 517) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/functions.php, Line: 649) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/functions.php, Line: 2278) | [ERROR] php.lang.security.phpinfo-use.phpinfo-use: The 'phpinfo' function may reveal sensitive information about your environment. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/functions.php, Line: 6056) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/functions.php, Line: 7485) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/functions.php, Line: 8093) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/general-template.php, Line: 2115) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/general-template.php, Line: 2253) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/general-template.php, Line: 2271) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/general-template.php, Line: 2279) | [ERROR] php.lang.security.injection.tainted-sql-string.tainted-sql-string: User data flows into this manually-constructed SQL string. User data can be safely inserted into SQL strings using prepared statements or an object-relational mapper (ORM). Manually-constructed SQL strings is a possible indicator of SQL injection, which could let an attacker steal or manipulate data from the database. Instead, use prepared statements (`$mysqli->prepare(""INSERT INTO test(id, label) VALUES (?, ?)"");`) or a safe library. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/general-template.php, Line: 2320) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/colorpicker.js, Line: 256) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/colorpicker.js, Line: 259) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/colorpicker.js, Line: 566) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/filepond/filepond-plugin-image-preview.js, Line: 2733) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/jquery/jquery-migrate.js, Line: 886) | [WARNING] javascript.browser.security.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/jquery/jquery.schedule.js, Line: 30) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `q` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/jquery/suggest.js, Line: 212) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `$` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/jquery/ui/autocomplete.js, Line: 637) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `term` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/jquery/ui/autocomplete.js, Line: 637) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/jquery/ui/core.js, Line: 1434) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `character` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/jquery/ui/menu.js, Line: 698) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/jquery/ui/widget.js, Line: 404) | [WARNING] javascript.browser.security.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/json2.js, Line: 504) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `window` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/mce-view.js, Line: 514) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/media-grid.js, Line: 34) | [WARNING] javascript.lang.security.audit.incomplete-sanitization.incomplete-sanitization: `input.getAttribute( 'name' ).replace( 'attachments[' + id + '][' , '' ).replace` method will only replace the first occurrence when used with a string argument (']'). If this method is used for escaping of dangerous data then there is a possibility for a bypass. Try to use sanitization library instead or use a Regex with a global flag. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/media-grid.js, Line: 131) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/media-grid.js, Line: 516) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/media-grid.js, Line: 750) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/media-grid.js, Line: 756) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/media-grid.js, Line: 774) | [WARNING] typescript.react.security.audit.react-unsanitized-method.react-unsanitized-method: Detection of insertAdjacentHTML from non-constant definition. This can inadvertently expose users to cross-site scripting (XSS) attacks if this comes from user-provided input. If you have to use insertAdjacentHTML, consider using a sanitization library such as DOMPurify to sanitize your HTML. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/media-grid.js, Line: 996) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/mediaelement/wp-playlist.js, Line: 28) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/mediaelement/wp-playlist.js, Line: 53) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/plupload/handlers.js, Line: 62) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/plupload/handlers.js, Line: 80) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/plupload/handlers.js, Line: 102) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/plupload/moxie.js, Line: 77) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/plupload/moxie.js, Line: 94) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `mime` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/plupload/moxie.js, Line: 1409) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `name` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/plupload/moxie.js, Line: 1503) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `name` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/plupload/moxie.js, Line: 1531) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/plupload/moxie.js, Line: 5926) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/plupload/moxie.js, Line: 6515) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/plupload/moxie.js, Line: 9670) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/plupload/wp-plupload.js, Line: 411) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/plupload/wp-plupload.js, Line: 431) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/plupload/wp-plupload.js, Line: 460) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/quicktags.js, Line: 307) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `tag` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/shortcode.js, Line: 121) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/tinymce/plugins/image/plugin.js, Line: 71) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/tinymce/plugins/lists/plugin.js, Line: 458) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/tinymce/plugins/lists/plugin.js, Line: 570) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `name` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/tinymce/plugins/media/plugin.js, Line: 1158) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `name` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/tinymce/plugins/wpgallery/plugin.js, Line: 18) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `p` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/tinymce/plugins/wptextpattern/plugin.js, Line: 139) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `p` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/tinymce/plugins/wptextpattern/plugin.js, Line: 162) | [WARNING] javascript.browser.security.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/tinymce/tiny_mce_popup.js, Line: 192) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/tinymce/tiny_mce_popup.js, Line: 237) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/tinymce/tiny_mce_popup.js, Line: 377) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `p` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/tinymce/utils/validate.js, Line: 73) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `c` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/tinymce/utils/validate.js, Line: 221) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `c` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/tinymce/utils/validate.js, Line: 225) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `c` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/tinymce/utils/validate.js, Line: 237) | [WARNING] javascript.browser.security.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/tw-sack.js, Line: 119) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/tw-sack.js, Line: 172) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/underscore.js, Line: 661) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/underscore.js, Line: 684) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `map` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/underscore.js, Line: 814) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `map` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/underscore.js, Line: 815) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `oldSettings` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/underscore.js, Line: 887) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `settings` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/underscore.js, Line: 887) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/wp-custom-header.js, Line: 124) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/wp-custom-header.js, Line: 138) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/wp-custom-header.js, Line: 146) | [WARNING] javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration: The target origin of the window.postMessage() API is set to ""*"". This could allow for information disclosure due to the possibility of any origin allowed to receive the message. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/wp-embed-template.js, Line: 14) | [WARNING] javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration: The target origin of the window.postMessage() API is set to ""*"". This could allow for information disclosure due to the possibility of any origin allowed to receive the message. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/wp-embed.js, Line: 139) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `type` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/js/wp-lists.js, Line: 247) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/ms-files.php, Line: 27) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/ms-files.php, Line: 34) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/ms-files.php, Line: 45) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/ms-files.php, Line: 57) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/ms-files.php, Line: 78) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/ms-files.php, Line: 79) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/ms-files.php, Line: 86) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/ms-functions.php, Line: 342) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/ms-site.php, Line: 870) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/rest-api/endpoints/class-wp-rest-attachments-controller.php, Line: 1070) | [WARNING] php.lang.security.unserialize-use.unserialize-use: Calling `unserialize()` with user input in the pattern can lead to arbitrary code execution. Consider using JSON or structured data approaches (e.g. Google Protocol Buffers). (File: downloaded_repos/ClassicPress_ClassicPress/src/wp-includes/rss.php, Line: 802) | [ERROR] php.lang.security.eval-use.eval-use: Evaluating non-constant commands. This can lead to command injection. (File: downloaded_repos/ClassicPress_ClassicPress/tools/i18n/extract.php, Line: 215) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/ClassicPress_ClassicPress/tools/i18n/makepot.php, Line: 92) | [ERROR] php.lang.security.exec-use.exec-use: Executing non-constant commands. This can lead to command injection. (File: downloaded_repos/ClassicPress_ClassicPress/tools/i18n/makepot.php, Line: 264) | [ERROR] php.lang.security.exec-use.exec-use: Executing non-constant commands. This can lead to command injection. (File: downloaded_repos/ClassicPress_ClassicPress/tools/i18n/makepot.php, Line: 342) | [ERROR] php.lang.security.exec-use.exec-use: Executing non-constant commands. This can lead to command injection. (File: downloaded_repos/ClassicPress_ClassicPress/tools/i18n/makepot.php, Line: 397) | [ERROR] php.lang.security.eval-use.eval-use: Evaluating non-constant commands. This can lead to command injection. (File: downloaded_repos/ClassicPress_ClassicPress/tools/i18n/not-gettexted.php, Line: 110)"
7,error311/FileRise,https://github.com/error311/FileRise,https://github.com/error311/FileRise.git,347,7,JavaScript,"🗂️ Lightweight, self-hosted web-based file manager with multi-file upload, editing, and batch operations – built with PHP & JavaScript for seamless file and folder management (Docker & Unraid ready).",41,58,100,Command Injection; Other,91.0,High-risk application type (+16); Critical features (+4); Vulnerability keywords (+3),2025-05-28T03:51:45Z,2025-02-21T08:02:37Z,ERROR: 41; WARNING: 15; INFO: 2,"[ERROR] dockerfile.security.missing-user.missing-user: By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'. (File: downloaded_repos/error311_FileRise/Dockerfile, Line: 145) | [WARNING] php.lang.security.audit.openssl-decrypt-validate.openssl-decrypt-validate: The function `openssl_decrypt` returns either a string of the decrypted data on success or `false` on failure. If the failure case is not handled, this could lead to undefined behavior in your application. Please handle the case where `openssl_decrypt` returns `false`. (File: downloaded_repos/error311_FileRise/config/config.php, Line: 56) | [WARNING] python.django.security.django-no-csrf-token.django-no-csrf-token: Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks. (File: downloaded_repos/error311_FileRise/public/index.html, Line: 193) | [WARNING] python.django.security.django-no-csrf-token.django-no-csrf-token: Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks. (File: downloaded_repos/error311_FileRise/public/index.html, Line: 238) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/adminPanel.js, Line: 214) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/adminPanel.js, Line: 247) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/adminPanel.js, Line: 325) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/adminPanel.js, Line: 371) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/adminPanel.js, Line: 390) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/adminPanel.js, Line: 398) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/adminPanel.js, Line: 418) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/adminPanel.js, Line: 427) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/adminPanel.js, Line: 583) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/adminPanel.js, Line: 655) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/adminPanel.js, Line: 681) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/adminPanel.js, Line: 704) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/auth.js, Line: 325) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/auth.js, Line: 335) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/auth.js, Line: 392) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/authModals.js, Line: 30) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/authModals.js, Line: 437) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/authModals.js, Line: 468) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/fileEditor.js, Line: 75) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/fileListView.js, Line: 267) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/fileListView.js, Line: 317) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/fileListView.js, Line: 344) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/fileListView.js, Line: 369) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/fileListView.js, Line: 397) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/fileListView.js, Line: 546) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/fileListView.js, Line: 907) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/filePreview.js, Line: 15) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/fileTags.js, Line: 15) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/fileTags.js, Line: 94) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/fileTags.js, Line: 170) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/fileTags.js, Line: 205) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/fileTags.js, Line: 258) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/error311_FileRise/public/js/folderManager.js, Line: 40) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/folderManager.js, Line: 444) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/folderShareModal.js, Line: 14) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/upload.js, Line: 54) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/upload.js, Line: 97) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/upload.js, Line: 104) | [INFO] javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring: Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string. (File: downloaded_repos/error311_FileRise/public/js/upload.js, Line: 139) | [INFO] javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring: Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string. (File: downloaded_repos/error311_FileRise/public/js/upload.js, Line: 142) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/upload.js, Line: 291) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/upload.js, Line: 298) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/error311_FileRise/public/js/upload.js, Line: 356) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/error311_FileRise/public/webdav.php, Line: 56) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/error311_FileRise/src/controllers/FileController.php, Line: 606) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/error311_FileRise/src/controllers/FileController.php, Line: 607) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/error311_FileRise/src/controllers/FileController.php, Line: 730) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/error311_FileRise/src/controllers/UserController.php, Line: 1090) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/error311_FileRise/src/models/AdminModel.php, Line: 107) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/error311_FileRise/src/models/FileModel.php, Line: 578) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/error311_FileRise/src/models/FileModel.php, Line: 1034) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/error311_FileRise/src/models/FolderModel.php, Line: 108) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/error311_FileRise/src/models/UploadModel.php, Line: 129) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/error311_FileRise/src/models/UploadModel.php, Line: 231)"
8,quiqr/quiqr-desktop,https://github.com/quiqr/quiqr-desktop,https://github.com/quiqr/quiqr-desktop.git,148,112,JavaScript,Hugo CMS and Publishing Application,41,176,100,Other; Path Traversal,91.0,High-risk application type (+8); Optimal star range (+10); High issue count (+10),2025-04-15T19:52:05Z,2022-03-04T15:01:40Z,ERROR: 4; WARNING: 171; INFO: 1,"[WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/app-prefs-state/configuration-data-provider.js, Line: 100) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/app-prefs-state/configuration-data-provider.js, Line: 100) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/app-prefs-state/configuration-data-provider.js, Line: 109) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/app-prefs-state/configuration-data-provider.js, Line: 110) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/app-prefs-state/configuration-data-provider.js, Line: 111) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/app-prefs-state/quiqr-app-config.js, Line: 15) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/app-prefs-state/quiqr-app-config.js, Line: 15) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/bridge/api-main.js, Line: 173) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/bridge/api-main.js, Line: 193) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/bridge/api-main.js, Line: 337) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/bridge/api-main.js, Line: 898) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/bridge/api-main.js, Line: 898) | [INFO] javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring: Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/bridge/ipc-main-binder.js, Line: 31) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `varItem` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/build-actions/document-build-action.js, Line: 30) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `repl` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/build-actions/document-build-action.js, Line: 63) | [ERROR] javascript.lang.security.detect-child-process.detect-child-process: Detected calls to child_process from a function argument `def_variables`. This could lead to a command injection if the input is user controllable. Try to avoid calls to child_process, and if it is needed ensure user input is correctly sanitized or sandboxed.  (File: downloaded_repos/quiqr_quiqr-desktop/src-main/build-actions/document-build-action.js, Line: 93) | [ERROR] javascript.lang.security.detect-child-process.detect-child-process: Detected calls to child_process from a function argument `docPath`. This could lead to a command injection if the input is user controllable. Try to avoid calls to child_process, and if it is needed ensure user input is correctly sanitized or sandboxed.  (File: downloaded_repos/quiqr_quiqr-desktop/src-main/build-actions/document-build-action.js, Line: 93) | [ERROR] javascript.lang.security.detect-child-process.detect-child-process: Detected calls to child_process from a function argument `execution_dict`. This could lead to a command injection if the input is user controllable. Try to avoid calls to child_process, and if it is needed ensure user input is correctly sanitized or sandboxed.  (File: downloaded_repos/quiqr_quiqr-desktop/src-main/build-actions/document-build-action.js, Line: 93) | [ERROR] javascript.lang.security.detect-child-process.detect-child-process: Detected calls to child_process from a function argument `sitePath`. This could lead to a command injection if the input is user controllable. Try to avoid calls to child_process, and if it is needed ensure user input is correctly sanitized or sandboxed.  (File: downloaded_repos/quiqr_quiqr-desktop/src-main/build-actions/document-build-action.js, Line: 93) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/hugo/hugo-downloader.js, Line: 188) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/hugo/hugo-utils.js, Line: 13) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/hugo/hugo-utils.js, Line: 13) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/import/folder-importer.js, Line: 23) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/import/folder-importer.js, Line: 24) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/import/folder-importer.js, Line: 25) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/import/folder-importer.js, Line: 26) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/import/folder-importer.js, Line: 27) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/import/folder-importer.js, Line: 28) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/import/folder-importer.js, Line: 29) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/library/library-service.js, Line: 74) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/library/library-service.js, Line: 127) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/folder-helper.js, Line: 35) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/folder-helper.js, Line: 35) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/initial-workspace-config-builder.js, Line: 24) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-config-provider.js, Line: 66) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-config-provider.js, Line: 72) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-config-provider.js, Line: 112) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-config-provider.js, Line: 115) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-config-provider.js, Line: 118) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-config-provider.js, Line: 121) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-config-provider.js, Line: 170) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-config-provider.js, Line: 233) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-config-provider.js, Line: 233) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-config-provider.js, Line: 264) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-config-provider.js, Line: 272) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-config-provider.js, Line: 272) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 141) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 241) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 283) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 309) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 310) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 314) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 426) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 427) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 428) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 432) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 433) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 434) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 481) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 484) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 485) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 489) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 490) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 525) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 526) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 527) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 531) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 532) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 533) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 559) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 562) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 586) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 589) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 591) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 604) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 623) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 623) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 649) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 685) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 689) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 689) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 697) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 698) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 706) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 706) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 709) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 709) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 749) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 749) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 751) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 782) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 798) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 803) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 803) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 817) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 831) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 831) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/services/workspace/workspace-service.js, Line: 831) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/folder/folder-sync.js, Line: 94) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/folder/folder-sync.js, Line: 101) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/folder/folder-sync.js, Line: 102) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/folder/folder-sync.js, Line: 103) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/folder/folder-sync.js, Line: 104) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/folder/folder-sync.js, Line: 105) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/folder/folder-sync.js, Line: 106) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/github/github-sync.js, Line: 407) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/github/github-sync.js, Line: 428) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/github/github-sync.js, Line: 470) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/github/github-sync.js, Line: 471) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/github/github-sync.js, Line: 472) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/github/github-sync.js, Line: 476) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/github/github-sync.js, Line: 477) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/github/github-sync.js, Line: 478) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/github/github-sync.js, Line: 479) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/github/github-sync.js, Line: 480) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/github/github-sync.js, Line: 481) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/github/github-sync.js, Line: 488) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/github/github-sync.js, Line: 489) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/github/github-sync.js, Line: 490) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/github/github-sync.js, Line: 490) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/github/github-sync.js, Line: 491) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/github/github-sync.js, Line: 491) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/github/github-sync.js, Line: 513) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/sync-factory.js, Line: 11) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/sync-factory.js, Line: 11) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/sysgit/sysgit-sync.js, Line: 407) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/sysgit/sysgit-sync.js, Line: 428) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/sysgit/sysgit-sync.js, Line: 470) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/sysgit/sysgit-sync.js, Line: 471) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/sysgit/sysgit-sync.js, Line: 472) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/sysgit/sysgit-sync.js, Line: 476) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/sysgit/sysgit-sync.js, Line: 477) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/sysgit/sysgit-sync.js, Line: 478) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/sysgit/sysgit-sync.js, Line: 479) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/sysgit/sysgit-sync.js, Line: 480) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/sysgit/sysgit-sync.js, Line: 481) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/sysgit/sysgit-sync.js, Line: 488) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/sysgit/sysgit-sync.js, Line: 489) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/sysgit/sysgit-sync.js, Line: 490) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/sysgit/sysgit-sync.js, Line: 490) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/sysgit/sysgit-sync.js, Line: 491) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/sysgit/sysgit-sync.js, Line: 491) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/sync/sysgit/sysgit-sync.js, Line: 513) | [WARNING] javascript.lang.security.audit.incomplete-sanitization.incomplete-sanitization: `fssimple.readFileSync(datePath, {encoding:'utf8', flag:'r'}).replace` method will only replace the first occurrence when used with a string argument (""\n""). If this method is used for escaping of dangerous data then there is a possibility for a bypass. Try to use sanitization library instead or use a Regex with a global flag. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/ui-managers/menu-manager.js, Line: 58) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/ui-managers/screenshot-window-manager.js, Line: 12) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/ui-managers/screenshot-window-manager.js, Line: 12) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/ui-managers/screenshot-window-manager.js, Line: 14) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/ui-managers/screenshot-window-manager.js, Line: 51) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/ui-managers/screenshot-window-manager.js, Line: 58) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/ui-managers/screenshot-window-manager.js, Line: 58) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/ui-managers/screenshot-window-manager.js, Line: 66) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/ui-managers/screenshot-window-manager.js, Line: 66) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/utils/path-helper.js, Line: 42) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/utils/path-helper.js, Line: 51) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/utils/path-helper.js, Line: 51) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/utils/path-helper.js, Line: 63) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/utils/path-helper.js, Line: 104) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/utils/path-helper.js, Line: 104) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/utils/path-helper.js, Line: 112) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/utils/path-helper.js, Line: 117) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/utils/path-helper.js, Line: 139) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/utils/path-helper.js, Line: 142) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/utils/path-helper.js, Line: 164) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src-main/utils/path-helper.js, Line: 165) | [WARNING] javascript.lang.security.audit.incomplete-sanitization.incomplete-sanitization: `buildResult.stdoutContent.replace` method will only replace the first occurrence when used with a string argument (""\n""). If this method is used for escaping of dangerous data then there is a possibility for a bypass. Try to use sanitization library instead or use a Regex with a global flag. (File: downloaded_repos/quiqr_quiqr-desktop/src/components/HoForm/Form.js, Line: 361) | [WARNING] javascript.browser.security.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/quiqr_quiqr-desktop/src/components/SukohForm/components/EisenhouwerDynamic.js, Line: 99) | [WARNING] javascript.browser.security.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/quiqr_quiqr-desktop/src/components/SukohForm/components/EisenhouwerDynamic.js, Line: 101) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/quiqr_quiqr-desktop/src/components/SukohForm/components/EisenhouwerDynamic.js, Line: 109) | [WARNING] javascript.browser.security.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/quiqr_quiqr-desktop/src/components/SukohForm/components/EisenhouwerDynamic.js, Line: 116) | [WARNING] javascript.browser.security.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/quiqr_quiqr-desktop/src/components/SukohForm/components/EisenhouwerDynamic.js, Line: 122) | [WARNING] javascript.browser.security.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/quiqr_quiqr-desktop/src/components/SukohForm/components/EisenhouwerDynamic.js, Line: 129) | [WARNING] javascript.browser.security.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/quiqr_quiqr-desktop/src/components/SukohForm/components/EisenhouwerDynamic.js, Line: 157) | [WARNING] javascript.browser.security.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/quiqr_quiqr-desktop/src/components/SukohForm/components/EisenhouwerDynamic.js, Line: 161) | [WARNING] javascript.browser.security.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/quiqr_quiqr-desktop/src/components/SukohForm/components/EisenhouwerDynamic.js, Line: 372) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src/components/SukohForm/components/ImageSelectDynamic.js, Line: 51) | [WARNING] javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal: Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first. (File: downloaded_repos/quiqr_quiqr-desktop/src/components/SukohForm/components/ImageSelectDynamic.js, Line: 66)"
9,et-nik/gameap,https://github.com/et-nik/gameap,https://github.com/et-nik/gameap.git,115,40,PHP,Game Admin Panel (GameAP) is the opensource game servers control panel.,40,24,100,XSS; Command Injection; Hardcoded Secrets; Other,90.0,High-risk application type (+8); Critical features (+4); Vulnerability keywords (+3),2025-05-15T07:09:45Z,2018-11-21T12:01:13Z,ERROR: 5; WARNING: 19,"[ERROR] dockerfile.security.missing-user.missing-user: By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'. (File: downloaded_repos/et-nik_gameap/.dev/docker/gdaemon/Dockerfile, Line: 20) | [WARNING] bash.curl.security.curl-pipe-bash.curl-pipe-bash: Data is being piped into `bash` from a `curl` command. An attacker with control of the server in the `curl` command could inject malicious code into the pipe, resulting in a system compromise. Avoid piping untrusted data into `bash` or any other shell if you can. If you must do this, consider checking the SHA sum of the content returned by the server to verify its integrity. (File: downloaded_repos/et-nik_gameap/.dev/docker/gdaemon/entrypoint, Line: 8) | [ERROR] php.lang.security.exec-use.exec-use: Executing non-constant commands. This can lead to command injection. (File: downloaded_repos/et-nik_gameap/.dev/docker/php/entrypoint, Line: 15) | [ERROR] php.lang.security.exec-use.exec-use: Executing non-constant commands. This can lead to command injection. (File: downloaded_repos/et-nik_gameap/.dev/docker/php/entrypoint, Line: 79) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/et-nik_gameap/app/Repositories/ClientCertificateRepository.php, Line: 121) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/et-nik_gameap/app/Repositories/ClientCertificateRepository.php, Line: 130) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/et-nik_gameap/app/Repositories/NodeRepository.php, Line: 131) | [ERROR] generic.secrets.security.detected-bcrypt-hash.detected-bcrypt-hash: bcrypt hash detected (File: downloaded_repos/et-nik_gameap/database/seeds/UsersTableSeeder.php, Line: 20) | [WARNING] yaml.docker-compose.security.no-new-privileges.no-new-privileges: Service 'mysql' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this. (File: downloaded_repos/et-nik_gameap/docker-compose.yml, Line: 3) | [WARNING] yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service: Service 'mysql' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this. (File: downloaded_repos/et-nik_gameap/docker-compose.yml, Line: 3) | [WARNING] yaml.docker-compose.security.no-new-privileges.no-new-privileges: Service 'nginx' allows for privilege escalation via setuid or setgid binaries. Add 'no-new-privileges:true' in 'security_opt' to prevent this. (File: downloaded_repos/et-nik_gameap/docker-compose.yml, Line: 15) | [WARNING] yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service: Service 'nginx' is running with a writable root filesystem. This may allow malicious applications to download and run additional payloads, or modify container files. If an application inside a container has to save something temporarily consider using a tmpfs. Add 'read_only: true' to this service to prevent this. (File: downloaded_repos/et-nik_gameap/docker-compose.yml, Line: 15) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/et-nik_gameap/resources/assets/js/components/blocks/CreateNodeModal.vue, Line: 23) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/et-nik_gameap/resources/assets/js/components/blocks/CreateNodeModal.vue, Line: 40) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/et-nik_gameap/resources/assets/js/parts/form.js, Line: 4) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/AdminServersCreate.vue, Line: 63) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/AdminServersEdit.vue, Line: 84) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/forms/UpdateModForm.vue, Line: 150) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/forms/UpdateModForm.vue, Line: 160) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/forms/UpdateModForm.vue, Line: 170) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/forms/UpdateModForm.vue, Line: 187) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/forms/UpdateModForm.vue, Line: 197) | [WARNING] javascript.vue.security.audit.xss.templates.avoid-v-html.avoid-v-html: Dynamically rendering arbitrary HTML on your website can be very dangerous because it can easily lead to XSS vulnerabilities. Only use HTML interpolation on trusted content and never on user-provided content. (File: downloaded_repos/et-nik_gameap/resources/assets/js/views/adminviews/forms/UpdateModForm.vue, Line: 207) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/et-nik_gameap/server.php, Line: 17)"
10,processwire/processwire,https://github.com/processwire/processwire,https://github.com/processwire/processwire.git,1004,71,PHP,ProcessWire 3.x is a friendly and powerful open source CMS with a strong API. ,38,101,100,Command Injection; Other,88.0,High-risk application type (+8); Vulnerability keywords (+3); Optimal star range (+10),2025-07-25T20:46:12Z,2016-08-29T09:59:35Z,ERROR: 23; WARNING: 77; INFO: 1,"[WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/processwire_processwire/install.php, Line: 1287) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/processwire_processwire/wire/core/FileCompiler.php, Line: 377) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/processwire_processwire/wire/core/FileCompiler.php, Line: 377) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/processwire_processwire/wire/core/FileCompiler.php, Line: 444) | [ERROR] php.lang.security.md5-loose-equality.md5-loose-equality: Make sure comparisons involving md5 values are strict (use `===` not `==`) to avoid type juggling issues (File: downloaded_repos/processwire_processwire/wire/core/FileCompiler.php, Line: 469) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/processwire_processwire/wire/core/ImageSizerEngine.php, Line: 658) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/processwire_processwire/wire/core/ProcessWire.php, Line: 1110) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/processwire_processwire/wire/core/ProcessWire.php, Line: 1268) | [WARNING] php.lang.security.injection.tainted-filename.tainted-filename: File name based on user input risks server-side request forgery. (File: downloaded_repos/processwire_processwire/wire/core/ProcessWire.php, Line: 1271) | [WARNING] php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect: The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs. (File: downloaded_repos/processwire_processwire/wire/core/Session.php, Line: 1428) | [WARNING] php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect: The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs. (File: downloaded_repos/processwire_processwire/wire/core/Tfa.php, Line: 363) | [WARNING] php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect: The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs. (File: downloaded_repos/processwire_processwire/wire/core/Tfa.php, Line: 366) | [WARNING] php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect: The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs. (File: downloaded_repos/processwire_processwire/wire/core/Tfa.php, Line: 759) | [WARNING] php.symfony.security.audit.symfony-non-literal-redirect.symfony-non-literal-redirect: The `redirect()` method does not check its destination in any way. If you redirect to a URL provided by end-users, your application may be open to the unvalidated redirects security vulnerability. Consider using literal values or an allowlist to validate URLs. (File: downloaded_repos/processwire_processwire/wire/core/Tfa.php, Line: 899) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/processwire_processwire/wire/core/WireDatabaseBackup.php, Line: 704) | [ERROR] php.lang.security.exec-use.exec-use: Executing non-constant commands. This can lead to command injection. (File: downloaded_repos/processwire_processwire/wire/core/WireDatabaseBackup.php, Line: 938) | [ERROR] php.lang.security.exec-use.exec-use: Executing non-constant commands. This can lead to command injection. (File: downloaded_repos/processwire_processwire/wire/core/WireDatabaseBackup.php, Line: 1123) | [ERROR] php.lang.security.exec-use.exec-use: Executing non-constant commands. This can lead to command injection. (File: downloaded_repos/processwire_processwire/wire/core/WireDatabaseBackup.php, Line: 1447) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/processwire_processwire/wire/core/WireFileTools.php, Line: 360) | [ERROR] php.lang.security.mcrypt-use.mcrypt-use: Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL. (File: downloaded_repos/processwire_processwire/wire/core/WireRandom.php, Line: 351) | [ERROR] php.lang.security.mcrypt-use.mcrypt-use: Mcrypt functionality has been deprecated and/or removed in recent PHP versions. Consider using Sodium or OpenSSL. (File: downloaded_repos/processwire_processwire/wire/core/WireRandom.php, Line: 836) | [WARNING] javascript.browser.security.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout.js, Line: 54) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout.js, Line: 914) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout.js, Line: 986) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout.js, Line: 992) | [WARNING] javascript.browser.security.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout.js, Line: 1151) | [WARNING] javascript.browser.security.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout.js, Line: 1154) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `oPane` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout.js, Line: 4820) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `pane` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout.js, Line: 4820) | [WARNING] javascript.browser.security.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout_and_plugins.js, Line: 54) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout_and_plugins.js, Line: 914) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout_and_plugins.js, Line: 986) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout_and_plugins.js, Line: 992) | [WARNING] javascript.browser.security.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout_and_plugins.js, Line: 1151) | [WARNING] javascript.browser.security.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout_and_plugins.js, Line: 1154) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `oPane` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout_and_plugins.js, Line: 4820) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `pane` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/AdminTheme/AdminThemeUikit/layout/source/stable/jquery.layout_and_plugins.js, Line: 4820) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `name` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeComments/comments.js, Line: 31) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeComments/comments.js, Line: 46) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/processwire_processwire/wire/modules/Fieldtype/FieldtypeRepeater/InputfieldRepeater.js, Line: 1517) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `b` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/pastetools/filter/common.js, Line: 23) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `b` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/ckeditor-4.19.0/plugins/pastetools/filter/image.js, Line: 5) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/samples/sourcedialog.html, Line: 75) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/samples/sourcedialog.html, Line: 109) | [WARNING] html.security.plaintext-http-link.plaintext-http-link: This link points to a plaintext HTTP URL. Prefer an encrypted HTTPS URL if possible. (File: downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldCKEditor/plugins/sourcedialog/samples/sourcedialog.html, Line: 113) | [WARNING] javascript.browser.security.eval-detected.eval-detected: Detected the use of eval(). eval() can be dangerous if used to evaluate dynamic content. If this content can be input from outside the program, this may be a code injection vulnerability. Ensure evaluated content is not definable by external sources. (File: downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/jquery-ui-timepicker-addon.js, Line: 180) | [WARNING] javascript.lang.security.audit.incomplete-sanitization.incomplete-sanitization: `$g.css(rtl ? 'marginRight' : 'marginLeft').toString().replace` method will only replace the first occurrence when used with a string argument ('%'). If this method is used for escaping of dangerous data then there is a possibility for a bypass. Try to use sanitization library instead or use a Regex with a global flag. (File: downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/jquery-ui-timepicker-addon.js, Line: 565) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `f` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/jquery-ui-timepicker-addon.js, Line: 1256) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `o` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldDatetime/timepicker/jquery-ui-timepicker-addon.js, Line: 1256) | [INFO] javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring: Detected string concatenation with a non-literal variable in a util.format / console.log function. If an attacker injects a format specifier in the string, it will forge the log message. Try to use constant values for the format string. (File: downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/InputfieldTinyMCE.js, Line: 138) | [WARNING] generic.unicode.security.bidi.contains-bidirectional-characters: This code contains bidirectional (bidi) characters. While this is useful for support of right-to-left languages such as Arabic or Hebrew, it can also be used to trick language parsers into executing code in a manner that is different from how it is displayed in code editing and review tools. If this is not what you were expecting, please review this code in an editor that can reveal hidden Unicode characters. (File: downloaded_repos/processwire_processwire/wire/modules/Inputfield/InputfieldTinyMCE/tinymce-6.8.2/plugins/help/js/i18n/keynav/ar.js, Line: 41) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/dev/jquery-migrate-debug-3.4.0.js, Line: 850) | [WARNING] javascript.browser.security.wildcard-postmessage-configuration.wildcard-postmessage-configuration: The target origin of the window.postMessage() API is set to ""*"". This could allow for information disclosure due to the possibility of any origin allowed to receive the message. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/iframe-resizer.js, Line: 452) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `XRegExp` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/xregexp.js, Line: 288) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `flags` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/xregexp.js, Line: 288) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `left` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/xregexp.js, Line: 288) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `options` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/xregexp.js, Line: 288) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `right` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/xregexp.js, Line: 288) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `regex` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/xregexp.js, Line: 2813) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `flags` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/xregexp.js, Line: 3207) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `pattern` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/xregexp.js, Line: 3207) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `search` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/xregexp.js, Line: 3737) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `regex` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryCore/xregexp.js, Line: 4028) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryMagnific/JqueryMagnific.js, Line: 67) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/JqueryTableSorter.js, Line: 1064) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `table` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/JqueryTableSorter.js, Line: 2020) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `key` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/JqueryTableSorter.js, Line: 2416) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `c` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js, Line: 540) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `data` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js, Line: 540) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `c` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js, Line: 574) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `data` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js, Line: 574) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `data` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js, Line: 602) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `c` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js, Line: 726) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `data` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js, Line: 726) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `table` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js, Line: 784) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `table` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js, Line: 785) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `$` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js, Line: 786) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `$` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js, Line: 787) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `$` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js, Line: 788) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `$` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js, Line: 789) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `$` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js, Line: 790) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `$` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js, Line: 791) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `$` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js, Line: 792) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `attr` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryTableSorter/widgets.js, Line: 1179) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `pattern` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/selectize/js/selectize.js, Line: 33) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `query` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/selectize/js/standalone/selectize.js, Line: 68) | [WARNING] javascript.lang.security.audit.prototype-pollution.prototype-pollution-loop.prototype-pollution-loop: Possibility of prototype polluting function detected. By adding or modifying attributes of an object prototype, it is possible to create attributes that exist on every object, or replace critical attributes with malicious ones. This can be problematic if the software depends on existence or non-existence of certain attributes, or uses pre-defined attributes of object prototype (such as hasOwnProperty, toString or valueOf). Possible mitigations might be: freezing the object prototype, using an object without prototypes (via Object.create(null) ), blocking modifications of attributes that resolve to object prototype, using Map instead of object. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/selectize/js/standalone/selectize.js, Line: 428) | [WARNING] javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp: RegExp() called with a `pattern` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS. (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/selectize/js/standalone/selectize.js, Line: 670) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/scripts/vex.combined.js, Line: 330) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/scripts/vex.combined.js, Line: 340) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/scripts/vex.combined.js, Line: 756) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/scripts/vex.combined.js, Line: 766) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/scripts/vex.js, Line: 330) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/processwire_processwire/wire/modules/Jquery/JqueryUI/vex/scripts/vex.js, Line: 340) | [ERROR] php.lang.security.eval-use.eval-use: Evaluating non-constant commands. This can lead to command injection. (File: downloaded_repos/processwire_processwire/wire/modules/Markup/MarkupHTMLPurifier/htmlpurifier/standalone/HTMLPurifier/ConfigSchema/InterchangeBuilder.php, Line: 182) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/processwire_processwire/wire/modules/Page/PageFrontEdit/PageFrontEdit.js, Line: 271) | [WARNING] javascript.lang.security.audit.incomplete-sanitization.incomplete-sanitization: `tpl.replace` method will only replace the first occurrence when used with a string argument ('%'). If this method is used for escaping of dangerous data then there is a possibility for a bypass. Try to use sanitization library instead or use a Regex with a global flag. (File: downloaded_repos/processwire_processwire/wire/modules/Process/ProcessField/ProcessField.js, Line: 27) | [WARNING] php.lang.security.unlink-use.unlink-use: Using user input when deleting files with `unlink()` is potentially dangerous. A malicious actor could use this to modify or access files they have no right to. (File: downloaded_repos/processwire_processwire/wire/modules/Process/ProcessModule/ProcessModuleInstall.php, Line: 385) | [WARNING] javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag: Cannot determine what 'js' is and it is used with a '<script>' tag. This could be susceptible to cross-site scripting (XSS). Ensure 'js' is not externally controlled, or sanitize this data. (File: downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageLister/ProcessPageLister.js, Line: 320) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/processwire_processwire/wire/modules/Process/ProcessPageLister/ProcessPageLister.js, Line: 326) | [ERROR] javascript.browser.security.insecure-document-method.insecure-document-method: User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities (File: downloaded_repos/processwire_processwire/wire/templates-admin/scripts/main.js, Line: 514)"
