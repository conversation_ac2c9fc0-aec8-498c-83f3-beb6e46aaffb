{"version": "1.130.0", "results": [{"check_id": "python.django.security.injection.ssrf.ssrf-injection-urllib.ssrf-injection-urllib", "path": "downloaded_repos/Voyz_ibeam/ibeam/src/handlers/http_handler.py", "start": {"line": 129, "col": 9, "offset": 4329}, "end": {"line": 130, "col": 99, "offset": 4469}, "extra": {"message": "Data from request object is passed to a new server-side request. This could lead to a server-side request forgery (SSRF), which could result in attackers gaining access to private organization data. To mitigate, ensure that schemes and hosts are validated against an allowlist, do not forward the response to the user, and ensure proper authentication and transport-layer security in the proxied request.", "metadata": {"cwe": ["CWE-918: Server-Side Request Forgery (SSRF)"], "owasp": ["A10:2021 - Server-Side Request Forgery (SSRF)"], "references": ["https://owasp.org/www-community/attacks/Server_Side_Request_Forgery"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "HIGH", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Server-Side Request Forgery (SSRF)"], "source": "https://semgrep.dev/r/python.django.security.injection.ssrf.ssrf-injection-urllib.ssrf-injection-urllib", "shortlink": "https://sg.run/6n2B"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.dynamic-urllib-use-detected.dynamic-urllib-use-detected", "path": "downloaded_repos/Voyz_ibeam/ibeam/src/handlers/http_handler.py", "start": {"line": 130, "col": 16, "offset": 4386}, "end": {"line": 130, "col": 99, "offset": 4469}, "extra": {"message": "Detected a dynamic value being used with urllib. urllib supports 'file://' schemes, so a dynamic value controlled by a malicious actor may allow them to read arbitrary files. Audit uses of urllib calls to ensure user data cannot control the URLs, or consider using the 'requests' library instead.", "metadata": {"cwe": ["CWE-939: Improper Authorization in Handler for Custom URL Scheme"], "owasp": "A01:2017 - Injection", "source-rule-url": "https://github.com/PyCQA/bandit/blob/b1411bfb43795d3ffd268bef17a839dee954c2b1/bandit/blacklists/calls.py#L163", "bandit-code": "B310", "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://cwe.mitre.org/data/definitions/939.html"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.dynamic-urllib-use-detected.dynamic-urllib-use-detected", "shortlink": "https://sg.run/dKZZ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "path": "downloaded_repos/Voyz_ibeam/ibeam/src/handlers/secrets_handler.py", "start": {"line": 138, "col": 37, "offset": 4639}, "end": {"line": 138, "col": 120, "offset": 4722}, "extra": {"message": "Detected a request using 'http://'. This request will be unencrypted, and attackers could listen into traffic on the network and be able to obtain sensitive information. Use 'https://' instead.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "asvs": {"control_id": "9.1.1 Weak TLS", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x17-V9-Communications.md#v92-server-communications-security-requirements", "section": "V9 Communications Verification Requirements", "version": "4"}, "category": "security", "technology": ["requests"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/python.lang.security.audit.insecure-transport.requests.request-with-http.request-with-http", "shortlink": "https://sg.run/W8J4"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.audit.dynamic-urllib-use-detected.dynamic-urllib-use-detected", "path": "downloaded_repos/Voyz_ibeam/support/verify_connection.py", "start": {"line": 25, "col": 5, "offset": 699}, "end": {"line": 25, "col": 61, "offset": 755}, "extra": {"message": "Detected a dynamic value being used with urllib. urllib supports 'file://' schemes, so a dynamic value controlled by a malicious actor may allow them to read arbitrary files. Audit uses of urllib calls to ensure user data cannot control the URLs, or consider using the 'requests' library instead.", "metadata": {"cwe": ["CWE-939: Improper Authorization in Handler for Custom URL Scheme"], "owasp": "A01:2017 - Injection", "source-rule-url": "https://github.com/PyCQA/bandit/blob/b1411bfb43795d3ffd268bef17a839dee954c2b1/bandit/blacklists/calls.py#L163", "bandit-code": "B310", "asvs": {"control_id": "5.2.4 Dynamic Code Execution Features", "control_url": "https://github.com/OWASP/ASVS/blob/master/4.0/en/0x13-V5-Validation-Sanitization-Encoding.md#v52-sanitization-and-sandboxing-requirements", "section": "V5: Validation, Sanitization and Encoding Verification Requirements", "version": "4"}, "category": "security", "technology": ["python"], "references": ["https://cwe.mitre.org/data/definitions/939.html"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/python.lang.security.audit.dynamic-urllib-use-detected.dynamic-urllib-use-detected", "shortlink": "https://sg.run/dKZZ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/bin/run.sh", "start": {"line": 33, "col": 1, "offset": 0}, "end": {"line": 33, "col": 1, "offset": 0}}]], "message": "Syntax error at line downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/bin/run.sh:33:\n missing element", "path": "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/bin/run.sh", "spans": [{"file": "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/bin/run.sh", "start": {"line": 33, "col": 1, "offset": 0}, "end": {"line": 33, "col": 1, "offset": 0}}]}], "paths": {"scanned": ["downloaded_repos/Voyz_ibeam/.dockerignore", "downloaded_repos/Voyz_ibeam/.gitattributes", "downloaded_repos/Voyz_ibeam/.github/ISSUE_TEMPLATE/bug_report.md", "downloaded_repos/Voyz_ibeam/.github/ISSUE_TEMPLATE/enhancement.md", "downloaded_repos/Voyz_ibeam/.gitignore", "downloaded_repos/Voyz_ibeam/CONTRIBUTING.md", "downloaded_repos/Voyz_ibeam/Dockerfile", "downloaded_repos/Voyz_ibeam/Dockerfile_armv7", "downloaded_repos/Voyz_ibeam/LICENSE", "downloaded_repos/Voyz_ibeam/MANIFEST.in", "downloaded_repos/Voyz_ibeam/README.md", "downloaded_repos/Voyz_ibeam/build_multiplatform.sh", "downloaded_repos/Voyz_ibeam/build_multiplatform_armv7.sh", "downloaded_repos/Voyz_ibeam/compose.yaml", "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/bin/run.bat", "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/bin/run.sh", "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/doc/GettingStarted.md", "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/doc/RealtimeSubscription.md", "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/root/conf.beta.yaml", "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/root/conf.yaml", "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/root/demo.zip", "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/root/logback.xml", "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/root/vertx.jks", "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/root/webapps/demo/index.html", "downloaded_repos/Voyz_ibeam/dev-requirements.txt", "downloaded_repos/Voyz_ibeam/ibeam/__init__.py", "downloaded_repos/Voyz_ibeam/ibeam/authenticate.sh", "downloaded_repos/Voyz_ibeam/ibeam/config.py", "downloaded_repos/Voyz_ibeam/ibeam/ibeam_starter.py", "downloaded_repos/Voyz_ibeam/ibeam/maintain.sh", "downloaded_repos/Voyz_ibeam/ibeam/run.sh", "downloaded_repos/Voyz_ibeam/ibeam/src/__init__.py", "downloaded_repos/Voyz_ibeam/ibeam/src/gateway_client.py", "downloaded_repos/Voyz_ibeam/ibeam/src/handlers/__init__.py", "downloaded_repos/Voyz_ibeam/ibeam/src/handlers/http_handler.py", "downloaded_repos/Voyz_ibeam/ibeam/src/handlers/inputs_handler.py", "downloaded_repos/Voyz_ibeam/ibeam/src/handlers/login_handler.py", "downloaded_repos/Voyz_ibeam/ibeam/src/handlers/process_handler.py", "downloaded_repos/Voyz_ibeam/ibeam/src/handlers/secrets_handler.py", "downloaded_repos/Voyz_ibeam/ibeam/src/handlers/strategy_handler.py", "downloaded_repos/Voyz_ibeam/ibeam/src/health_server.py", "downloaded_repos/Voyz_ibeam/ibeam/src/login/__init__.py", "downloaded_repos/Voyz_ibeam/ibeam/src/login/driver.py", "downloaded_repos/Voyz_ibeam/ibeam/src/login/targets.py", "downloaded_repos/Voyz_ibeam/ibeam/src/logs.py", "downloaded_repos/Voyz_ibeam/ibeam/src/two_fa_handlers/__init__.py", "downloaded_repos/Voyz_ibeam/ibeam/src/two_fa_handlers/external_request_handler.py", "downloaded_repos/Voyz_ibeam/ibeam/src/two_fa_handlers/google_msg_handler.py", "downloaded_repos/Voyz_ibeam/ibeam/src/two_fa_handlers/notification_resend_handler.py", "downloaded_repos/Voyz_ibeam/ibeam/src/two_fa_handlers/two_fa_handler.py", "downloaded_repos/Voyz_ibeam/ibeam/src/two_fa_selector.py", "downloaded_repos/Voyz_ibeam/ibeam/src/utils/__init__.py", "downloaded_repos/Voyz_ibeam/ibeam/src/utils/py_utils.py", "downloaded_repos/Voyz_ibeam/ibeam/src/utils/selenium_utils.py", "downloaded_repos/Voyz_ibeam/ibeam/src/var.py", "downloaded_repos/Voyz_ibeam/ibeam/test_request.sh", "downloaded_repos/Voyz_ibeam/media/ib_logo.png", "downloaded_repos/Voyz_ibeam/media/ibeam_logo.png", "downloaded_repos/Voyz_ibeam/media/ibeam_logo.psd", "downloaded_repos/Voyz_ibeam/media/ibeam_logo_01.png", "downloaded_repos/Voyz_ibeam/media/ibeam_logo_B01.png", "downloaded_repos/Voyz_ibeam/media/ibeam_logo_B01.psd", "downloaded_repos/Voyz_ibeam/media/ibeam_logo_transparent.png", "downloaded_repos/Voyz_ibeam/requirements.txt", "downloaded_repos/Voyz_ibeam/setup.cfg", "downloaded_repos/Voyz_ibeam/setup.py", "downloaded_repos/Voyz_ibeam/support/san.cnf", "downloaded_repos/Voyz_ibeam/support/test_two_fa_server.py", "downloaded_repos/Voyz_ibeam/support/verify_connection.py"], "skipped": [{"path": "downloaded_repos/Voyz_ibeam/build/docs/parse_vars.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/bin/run.sh", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/build/lib/runtime/commons-cli-1.2.jar", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/build/lib/runtime/commons-lang-2.6.jar", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/build/lib/runtime/ibgroup.security.auth.client.lib-20210528111740.jar", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/build/lib/runtime/jackson-annotations-2.9.8.jar", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/build/lib/runtime/jackson-core-2.9.9.jar", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/build/lib/runtime/jackson-databind-2.9.9.3.jar", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/build/lib/runtime/logback-classic-1.2.11.jar", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/build/lib/runtime/logback-core-1.2.11.jar", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/build/lib/runtime/netty-buffer-4.1.15.Final.jar", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/build/lib/runtime/netty-codec-4.1.15.Final.jar", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/build/lib/runtime/netty-codec-dns-4.1.15.Final.jar", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/build/lib/runtime/netty-codec-http-4.1.15.Final.jar", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/build/lib/runtime/netty-codec-http2-4.1.15.Final.jar", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/build/lib/runtime/netty-codec-socks-4.1.15.Final.jar", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/build/lib/runtime/netty-common-4.1.15.Final.jar", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/build/lib/runtime/netty-handler-4.1.15.Final.jar", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/build/lib/runtime/netty-handler-proxy-4.1.15.Final.jar", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/build/lib/runtime/netty-resolver-4.1.15.Final.jar", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/build/lib/runtime/netty-resolver-dns-4.1.15.Final.jar", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/build/lib/runtime/netty-tcnative-boringssl-static-2.0.6.Final.jar", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/build/lib/runtime/netty-transport-4.1.15.Final.jar", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/build/lib/runtime/netty-transport-native-epoll-4.1.15.Final.jar", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/build/lib/runtime/netty-transport-native-kqueue-4.1.15.Final.jar", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/build/lib/runtime/netty-transport-native-unix-common-4.1.15.Final.jar", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/build/lib/runtime/slf4j-api-1.7.36.jar", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/build/lib/runtime/snakeyaml-1.17.jar", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/build/lib/runtime/vertx-core-3.5.0.jar", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/build/lib/runtime/vertx-web-3.5.0.jar", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/copy_cache/clientportal.gw/dist/ibgroup.web.core.iblink.router.clientportal.gw.jar", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/dist/ibeam-0.1.0.tar.gz", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/dist/ibeam-0.2.0.tar.gz", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/dist/ibeam-0.2.1.tar.gz", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/dist/ibeam-0.3.0.tar.gz", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/dist/ibeam-0.4.0.tar.gz", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/dist/ibeam-0.4.1.tar.gz", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/dist/ibeam-0.4.2.tar.gz", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/dist/ibeam-0.4.3.tar.gz", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/dist/ibeam-0.4.4.tar.gz", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/dist/ibeam-0.4.5.tar.gz", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/dist/ibeam-0.4.6.tar.gz", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/dist/ibeam-0.5.0.tar.gz", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/dist/ibeam-0.5.1.tar.gz", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/dist/ibeam-0.5.3.tar.gz", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/dist/ibeam-0.5.4-py3-none-any.whl", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/dist/ibeam-0.5.4.tar.gz", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/dist/ibeam-0.5.5-py3-none-any.whl", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/dist/ibeam-0.5.5.tar.gz", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/dist/ibeam-0.5.6-py3-none-any.whl", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/dist/ibeam-0.5.6.tar.gz", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/dist/ibeam-0.5.7-py3-none-any.whl", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/dist/ibeam-0.5.7.tar.gz", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/dist/ibeam-0.5.8-py3-none-any.whl", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/dist/ibeam-0.5.8.tar.gz", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/Voyz_ibeam/tests/ibeam/src/test_gateway_client.py", "reason": "semgrepignore_patterns_match"}]}, "time": {"rules": [], "rules_parse_time": 1.8708970546722412, "profiling_times": {"config_time": 7.365688800811768, "core_time": 3.500891923904419, "ignores_time": 0.002292633056640625, "total_time": 10.869792699813843}, "parsing_time": {"total_time": 0.6645512580871582, "per_file_time": {"mean": 0.016208567270418497, "std_dev": 0.0003570258176141183}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 5.314464330673218, "per_file_time": {"mean": 0.029524801837073435, "std_dev": 0.007611141087549653}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 1.950321912765503, "per_file_and_rule_time": {"mean": 0.004588992735918832, "std_dev": 9.698396447367301e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.6800055503845215, "per_def_and_rule_time": {"mean": 0.0007709813496423147, "std_dev": 2.2729954212392287e-06}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_defs": []}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}