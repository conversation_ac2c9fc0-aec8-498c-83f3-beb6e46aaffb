{"version": "1.130.0", "results": [{"check_id": "dockerfile.security.missing-user-entrypoint.missing-user-entrypoint", "path": "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/Dockerfile", "start": {"line": 81, "col": 1, "offset": 2159}, "end": {"line": 81, "col": 43, "offset": 2201}, "extra": {"message": "By not specifying a USER, a program in the container may run as 'root'. This is a security hazard. If an attacker can control a process running as root, they may have control over the container. Ensure that the last USER in a Dockerfile is a USER other than 'root'.", "fix": "USER non-root\nENTRYPOINT [ \"/usr/local/bin/entrypoint\" ]", "metadata": {"cwe": ["CWE-269: Improper Privilege Management"], "category": "security", "technology": ["dockerfile"], "confidence": "MEDIUM", "owasp": ["A04:2021 - Insecure Design"], "references": ["https://owasp.org/Top10/A04_2021-Insecure_Design"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authorization"], "source": "https://semgrep.dev/r/dockerfile.security.missing-user-entrypoint.missing-user-entrypoint", "shortlink": "https://sg.run/k281"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "terraform.aws.security.aws-cloudfront-insecure-tls.aws-insecure-cloudfront-distribution-tls-version", "path": "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/aws/cloudfront.tf", "start": {"line": 14, "col": 1, "offset": 576}, "end": {"line": 101, "col": 2, "offset": 2944}, "extra": {"message": "Detected an AWS CloudFront Distribution with an insecure TLS version. TLS versions less than 1.2 are considered insecure because they can be broken. To fix this, set your `minimum_protocol_version` to `\"TLSv1.2_2018\", \"TLSv1.2_2019\" or \"TLSv1.2_2021\"`.", "metadata": {"category": "security", "technology": ["terraform", "aws"], "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-326: Inadequate Encryption Strength"], "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/terraform.aws.security.aws-cloudfront-insecure-tls.aws-insecure-cloudfront-distribution-tls-version", "shortlink": "https://sg.run/Q6o4"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "terraform.aws.security.aws-dynamodb-table-unencrypted.aws-dynamodb-table-unencrypted", "path": "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/aws/create_state_bucket/dynamodb.tf", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 11, "col": 2, "offset": 274}, "extra": {"message": "By default, AWS DynamoDB Table is encrypted using AWS-managed keys. However, for added security, it's recommended to configure your own AWS KMS encryption key to protect your data in the DynamoDB table. You can either create a new aws_kms_key resource or use the ARN of an existing key in your AWS account to do so.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-326: Inadequate Encryption Strength"], "technology": ["aws", "terraform"], "category": "security", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/terraform.aws.security.aws-dynamodb-table-unencrypted.aws-dynamodb-table-unencrypted", "shortlink": "https://sg.run/Ay4p"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "terraform.aws.security.aws-kms-no-rotation.aws-kms-no-rotation", "path": "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/aws/create_state_bucket/s3.tf", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 2, "offset": 121}, "extra": {"message": "The AWS KMS has no rotation. Missing rotation can cause leaked key to be used by attackers. To fix this, set a `enable_key_rotation`.", "metadata": {"owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-326: Inadequate Encryption Strength"], "technology": ["aws", "terraform"], "category": "security", "references": ["https://owasp.org/Top10/A02_2021-Cryptographic_Failures"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/terraform.aws.security.aws-kms-no-rotation.aws-kms-no-rotation", "shortlink": "https://sg.run/kz47"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "terraform.lang.security.s3-cors-all-origins.all-origins-allowed", "path": "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/aws/s3.tf", "start": {"line": 9, "col": 5, "offset": 266}, "end": {"line": 9, "col": 28, "offset": 289}, "extra": {"message": "CORS rule on bucket permits any origin", "metadata": {"references": ["https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/s3_bucket#using-cors"], "cwe": ["CWE-942: Permissive Cross-domain Policy with Untrusted Domains"], "category": "security", "technology": ["terraform", "aws"], "owasp": ["A05:2021 - Security Misconfiguration"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Validation"], "source": "https://semgrep.dev/r/terraform.lang.security.s3-cors-all-origins.all-origins-allowed", "shortlink": "https://sg.run/DJb2"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.django-rest-framework.missing-throttle-config.missing-throttle-config", "path": "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/src/backend/{{cookiecutter.site}}/settings.py", "start": {"line": 110, "col": 5, "offset": 3005}, "end": {"line": 110, "col": 19, "offset": 3019}, "extra": {"message": "Django REST framework configuration is missing default rate- limiting options. This could inadvertently allow resource starvation or Denial of Service (DoS) attacks. Add 'DEFAULT_THROTTLE_CLASSES' and 'DEFAULT_THROTTLE_RATES' to add rate-limiting to your application.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-770: Allocation of Resources Without Limits or Throttling"], "references": ["https://www.django-rest-framework.org/api-guide/throttling/#setting-the-throttling-policy"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/python.django.security.audit.django-rest-framework.missing-throttle-config.missing-throttle-config", "shortlink": "https://sg.run/vzBY"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.django-rest-framework.missing-throttle-config.missing-throttle-config", "path": "downloaded_repos/openfun_richie/sandbox/settings.py", "start": {"line": 106, "col": 5, "offset": 2814}, "end": {"line": 106, "col": 19, "offset": 2828}, "extra": {"message": "Django REST framework configuration is missing default rate- limiting options. This could inadvertently allow resource starvation or Denial of Service (DoS) attacks. Add 'DEFAULT_THROTTLE_CLASSES' and 'DEFAULT_THROTTLE_RATES' to add rate-limiting to your application.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-770: Allocation of Resources Without Limits or Throttling"], "references": ["https://www.django-rest-framework.org/api-guide/throttling/#setting-the-throttling-policy"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "HIGH", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Other"], "source": "https://semgrep.dev/r/python.django.security.audit.django-rest-framework.missing-throttle-config.missing-throttle-config", "shortlink": "https://sg.run/vzBY"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/openfun_richie/src/frontend/.storybook/main.js", "start": {"line": 38, "col": 39, "offset": 1063}, "end": {"line": 38, "col": 44, "offset": 1068}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/openfun_richie/src/frontend/i18n/compile-translations.js", "start": {"line": 41, "col": 63, "offset": 1376}, "end": {"line": 41, "col": 71, "offset": 1384}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/openfun_richie/src/frontend/i18n/compile-translations.js", "start": {"line": 67, "col": 61, "offset": 2188}, "end": {"line": 67, "col": 69, "offset": 2196}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/openfun_richie/src/frontend/i18n/compile-translations.js", "start": {"line": 79, "col": 26, "offset": 2580}, "end": {"line": 79, "col": 43, "offset": 2597}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/openfun_richie/src/frontend/js/api/configuration.ts", "start": {"line": 8, "col": 37, "offset": 335}, "end": {"line": 8, "col": 66, "offset": 364}, "extra": {"message": "RegExp() called with a `lms` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "typescript.react.security.react-insecure-request.react-insecure-request", "path": "downloaded_repos/openfun_richie/src/frontend/js/api/joanie.spec.ts", "start": {"line": 10, "col": 23, "offset": 421}, "end": {"line": 10, "col": 50, "offset": 448}, "extra": {"message": "Unencrypted request over HTTP detected.", "metadata": {"vulnerability": "Insecure Transport", "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.npmjs.com/package/axios"], "category": "security", "technology": ["react"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/typescript.react.security.react-insecure-request.react-insecure-request", "shortlink": "https://sg.run/1n0b"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/openfun_richie/src/frontend/js/api/lms/dummy.ts", "start": {"line": 31, "col": 6, "offset": 914}, "end": {"line": 31, "col": 299, "offset": 1207}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/openfun_richie/src/frontend/js/api/lms/dummy.ts", "start": {"line": 33, "col": 6, "offset": 1267}, "end": {"line": 33, "col": 306, "offset": 1567}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/openfun_richie/src/frontend/js/api/lms/dummy.ts", "start": {"line": 35, "col": 6, "offset": 1627}, "end": {"line": 35, "col": 304, "offset": 1925}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/openfun_richie/src/frontend/js/api/lms/dummy.ts", "start": {"line": 37, "col": 6, "offset": 1985}, "end": {"line": 37, "col": 308, "offset": 2287}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/openfun_richie/src/frontend/js/api/lms/dummy.ts", "start": {"line": 39, "col": 6, "offset": 2347}, "end": {"line": 39, "col": 308, "offset": 2649}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/openfun_richie/src/frontend/js/api/lms/dummy.ts", "start": {"line": 41, "col": 6, "offset": 2722}, "end": {"line": 41, "col": 344, "offset": 3060}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/openfun_richie/src/frontend/js/api/lms/dummy.ts", "start": {"line": 43, "col": 6, "offset": 3127}, "end": {"line": 43, "col": 332, "offset": 3453}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/openfun_richie/src/frontend/js/api/lms/dummy.ts", "start": {"line": 45, "col": 6, "offset": 3527}, "end": {"line": 45, "col": 356, "offset": 3877}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "generic.secrets.security.detected-jwt-token.detected-jwt-token", "path": "downloaded_repos/openfun_richie/src/frontend/js/api/lms/dummy.ts", "start": {"line": 47, "col": 6, "offset": 3937}, "end": {"line": 47, "col": 283, "offset": 4214}, "extra": {"message": "JWT token detected", "metadata": {"source-rule-url": "https://github.com/Yelp/detect-secrets/blob/master/detect_secrets/plugins/jwt.py", "category": "security", "technology": ["secrets", "jwt"], "confidence": "LOW", "references": ["https://semgrep.dev/blog/2020/hardcoded-secrets-unverified-tokens-and-other-common-jwt-mistakes/"], "cwe": ["CWE-321: Use of Hard-coded Cryptographic Key"], "owasp": ["A02:2021 - Cryptographic Failures"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cryptographic Issues"], "source": "https://semgrep.dev/r/generic.secrets.security.detected-jwt-token.detected-jwt-token", "shortlink": "https://sg.run/05N5"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insufficient-postmessage-origin-validation.insufficient-postmessage-origin-validation", "path": "downloaded_repos/openfun_richie/src/frontend/js/components/PaymentInterfaces/PayplugLightbox.tsx", "start": {"line": 56, "col": 5, "offset": 1806}, "end": {"line": 56, "col": 67, "offset": 1868}, "extra": {"message": "No validation of origin is done by the addEventListener API. It may be possible to exploit this flaw to perform Cross Origin attacks such as Cross-Site Scripting(XSS).", "metadata": {"owasp": ["A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-345: Insufficient Verification of Data Authenticity"], "category": "security", "technology": ["browser"], "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Improper Authentication"], "source": "https://semgrep.dev/r/javascript.browser.security.insufficient-postmessage-origin-validation.insufficient-postmessage-origin-validation", "shortlink": "https://sg.run/gL9x"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/index.full-process.spec.tsx", "start": {"line": 264, "col": 42, "offset": 9640}, "end": {"line": 264, "col": 76, "offset": 9674}, "extra": {"message": "RegExp() called with a `installment` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "typescript.react.security.react-insecure-request.react-insecure-request", "path": "downloaded_repos/openfun_richie/src/frontend/js/utils/react-query/useSessionMutation/index.spec.tsx", "start": {"line": 44, "col": 11, "offset": 1692}, "end": {"line": 44, "col": 73, "offset": 1754}, "extra": {"message": "Unencrypted request over HTTP detected.", "metadata": {"vulnerability": "Insecure Transport", "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.npmjs.com/package/axios"], "category": "security", "technology": ["react"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/typescript.react.security.react-insecure-request.react-insecure-request", "shortlink": "https://sg.run/1n0b"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "typescript.react.security.react-insecure-request.react-insecure-request", "path": "downloaded_repos/openfun_richie/src/frontend/js/utils/react-query/useSessionQuery/index.spec.tsx", "start": {"line": 46, "col": 41, "offset": 1720}, "end": {"line": 46, "col": 77, "offset": 1756}, "extra": {"message": "Unencrypted request over HTTP detected.", "metadata": {"vulnerability": "Insecure Transport", "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.npmjs.com/package/axios"], "category": "security", "technology": ["react"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/typescript.react.security.react-insecure-request.react-insecure-request", "shortlink": "https://sg.run/1n0b"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "typescript.react.security.react-insecure-request.react-insecure-request", "path": "downloaded_repos/openfun_richie/src/frontend/js/utils/react-query/useSessionQuery/index.spec.tsx", "start": {"line": 76, "col": 41, "offset": 2925}, "end": {"line": 76, "col": 77, "offset": 2961}, "extra": {"message": "Unencrypted request over HTTP detected.", "metadata": {"vulnerability": "Insecure Transport", "owasp": ["A03:2017 - Sensitive Data Exposure", "A02:2021 - Cryptographic Failures"], "cwe": ["CWE-319: Cleartext Transmission of Sensitive Information"], "references": ["https://www.npmjs.com/package/axios"], "category": "security", "technology": ["react"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Mishandled Sensitive Information"], "source": "https://semgrep.dev/r/typescript.react.security.react-insecure-request.react-insecure-request", "shortlink": "https://sg.run/1n0b"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/openfun_richie/src/frontend/js/widgets/Search/components/SearchFilterValueParent/index.tsx", "start": {"line": 47, "col": 35, "offset": 2315}, "end": {"line": 47, "col": 64, "offset": 2344}, "extra": {"message": "RegExp() called with a `{ filter, value }: SearchFilterValueParentProps` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "typescript.react.security.audit.react-dangerouslysetinnerhtml.react-dangerouslysetinnerhtml", "path": "downloaded_repos/openfun_richie/src/frontend/js/widgets/Slider/components/SlidePanel.tsx", "start": {"line": 62, "col": 48, "offset": 1959}, "end": {"line": 62, "col": 80, "offset": 1991}, "extra": {"message": "Detection of dangerouslySetInnerHTML from non-constant definition. This can inadvertently expose users to cross-site scripting (XSS) attacks if this comes from user-provided input. If you have to use dangerouslySetInnerHTML, consider using a sanitization library such as DOMPurify to sanitize your HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://react.dev/reference/react-dom/components/common#dangerously-setting-the-inner-html"], "category": "security", "confidence": "MEDIUM", "technology": ["react"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/typescript.react.security.audit.react-dangerouslysetinnerhtml.react-dangerouslysetinnerhtml", "shortlink": "https://sg.run/rAx6"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "path": "downloaded_repos/openfun_richie/src/frontend/webpack.config.js", "start": {"line": 21, "col": 40, "offset": 796}, "end": {"line": 21, "col": 54, "offset": 810}, "extra": {"message": "Detected possible user input going into a `path.join` or `path.resolve` function. This could possibly lead to a path traversal vulnerability,  where the attacker can access arbitrary files stored in the file system. Instead, be sure to sanitize or validate user input first.", "metadata": {"owasp": ["A05:2017 - Broken Access Control", "A01:2021 - Broken Access Control"], "cwe": ["CWE-22: Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal')"], "category": "security", "references": ["https://owasp.org/www-community/attacks/Path_Traversal"], "technology": ["javascript", "node.js"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Path Traversal"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "shortlink": "https://sg.run/OPqk"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/openfun_richie/src/frontend/webpack.config.js", "start": {"line": 118, "col": 62, "offset": 4043}, "end": {"line": 118, "col": 82, "offset": 4063}, "extra": {"message": "RegExp() called with a `entry` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-translate-as-no-escape.template-translate-as-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/dashboard.html", "start": {"line": 9, "col": 5, "offset": 180}, "end": {"line": 10, "col": 74, "offset": 291}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-translate-as-no-escape.template-translate-as-no-escape", "shortlink": "https://sg.run/PJDz"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/pagination_inner.html", "start": {"line": 15, "col": 17, "offset": 505}, "end": {"line": 15, "col": 33, "offset": 521}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/pagination_inner.html", "start": {"line": 17, "col": 17, "offset": 620}, "end": {"line": 17, "col": 33, "offset": 636}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/pagination_inner.html", "start": {"line": 27, "col": 17, "offset": 1140}, "end": {"line": 27, "col": 33, "offset": 1156}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/pagination_inner.html", "start": {"line": 35, "col": 21, "offset": 1543}, "end": {"line": 35, "col": 37, "offset": 1559}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/pagination_inner.html", "start": {"line": 37, "col": 21, "offset": 1665}, "end": {"line": 37, "col": 37, "offset": 1681}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/pagination_inner.html", "start": {"line": 39, "col": 21, "offset": 1804}, "end": {"line": 39, "col": 37, "offset": 1820}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/pagination_inner.html", "start": {"line": 41, "col": 21, "offset": 1909}, "end": {"line": 41, "col": 37, "offset": 1925}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/blogpost-badges.html", "start": {"line": 3, "col": 5, "offset": 93}, "end": {"line": 3, "col": 64, "offset": 152}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/blogpost-badges.html", "start": {"line": 4, "col": 5, "offset": 200}, "end": {"line": 4, "col": 88, "offset": 283}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/course-badges.html", "start": {"line": 3, "col": 5, "offset": 93}, "end": {"line": 3, "col": 42, "offset": 130}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/course-badges.html", "start": {"line": 4, "col": 5, "offset": 188}, "end": {"line": 4, "col": 88, "offset": 271}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.direct-use-of-httpresponse.direct-use-of-httpresponse", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/admin.py", "start": {"line": 291, "col": 20, "offset": 10197}, "end": {"line": 291, "col": 86, "offset": 10263}, "extra": {"message": "Detected data rendered directly to the end user via 'HttpResponse' or a similar object. This bypasses Django's built-in cross-site scripting (XSS) defenses and could result in an XSS vulnerability. Use Django's template engine to safely render HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.1/intro/tutorial03/#a-shortcut-render", "https://docs.djangoproject.com/en/3.1/topics/http/shortcuts/#render"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.direct-use-of-httpresponse.direct-use-of-httpresponse", "shortlink": "https://sg.run/EknN"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.direct-use-of-httpresponse.direct-use-of-httpresponse", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/admin.py", "start": {"line": 296, "col": 20, "offset": 10421}, "end": {"line": 296, "col": 61, "offset": 10462}, "extra": {"message": "Detected data rendered directly to the end user via 'HttpResponse' or a similar object. This bypasses Django's built-in cross-site scripting (XSS) defenses and could result in an XSS vulnerability. Use Django's template engine to safely render HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.1/intro/tutorial03/#a-shortcut-render", "https://docs.djangoproject.com/en/3.1/topics/http/shortcuts/#render"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.direct-use-of-httpresponse.direct-use-of-httpresponse", "shortlink": "https://sg.run/EknN"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/category_detail.html", "start": {"line": 6, "col": 5, "offset": 184}, "end": {"line": 6, "col": 56, "offset": 235}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/category_detail.html", "start": {"line": 137, "col": 41, "offset": 8588}, "end": {"line": 137, "col": 112, "offset": 8659}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 6, "col": 5, "offset": 219}, "end": {"line": 6, "col": 52, "offset": 266}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 132, "col": 53, "offset": 8294}, "end": {"line": 132, "col": 106, "offset": 8347}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 220, "col": 21, "offset": 13184}, "end": {"line": 220, "col": 116, "offset": 13279}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 261, "col": 78, "offset": 16282}, "end": {"line": 261, "col": 125, "offset": 16329}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 273, "col": 62, "offset": 17058}, "end": {"line": 273, "col": 109, "offset": 17105}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 284, "col": 82, "offset": 17780}, "end": {"line": 284, "col": 129, "offset": 17827}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 297, "col": 66, "offset": 18656}, "end": {"line": 297, "col": 113, "offset": 18703}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 310, "col": 82, "offset": 19648}, "end": {"line": 310, "col": 129, "offset": 19695}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 323, "col": 62, "offset": 20538}, "end": {"line": 323, "col": 109, "offset": 20585}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 338, "col": 62, "offset": 21575}, "end": {"line": 338, "col": 109, "offset": 21622}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 356, "col": 70, "offset": 22628}, "end": {"line": 356, "col": 117, "offset": 22675}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 405, "col": 41, "offset": 25632}, "end": {"line": 405, "col": 87, "offset": 25678}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 434, "col": 25, "offset": 27316}, "end": {"line": 434, "col": 72, "offset": 27363}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 453, "col": 25, "offset": 28310}, "end": {"line": 453, "col": 72, "offset": 28357}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 496, "col": 54, "offset": 30616}, "end": {"line": 496, "col": 101, "offset": 30663}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_error_detail_template_banner.html", "start": {"line": 7, "col": 9, "offset": 219}, "end": {"line": 7, "col": 25, "offset": 235}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_organization_glimpse.html", "start": {"line": 7, "col": 63, "offset": 543}, "end": {"line": 7, "col": 122, "offset": 602}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_detail.html", "start": {"line": 119, "col": 21, "offset": 6898}, "end": {"line": 119, "col": 74, "offset": 6951}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_detail.html", "start": {"line": 152, "col": 41, "offset": 8886}, "end": {"line": 152, "col": 120, "offset": 8965}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/person_detail.html", "start": {"line": 81, "col": 133, "offset": 4819}, "end": {"line": 81, "col": 183, "offset": 4869}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/person_detail.html", "start": {"line": 140, "col": 41, "offset": 7819}, "end": {"line": 140, "col": 108, "offset": 7886}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/program_detail.html", "start": {"line": 6, "col": 5, "offset": 167}, "end": {"line": 6, "col": 54, "offset": 216}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/program_detail.html", "start": {"line": 86, "col": 25, "offset": 4427}, "end": {"line": 86, "col": 69, "offset": 4471}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/program_detail.html", "start": {"line": 184, "col": 63, "offset": 9858}, "end": {"line": 184, "col": 115, "offset": 9910}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/program_detail.html", "start": {"line": 220, "col": 25, "offset": 11717}, "end": {"line": 220, "col": 72, "offset": 11764}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.direct-use-of-httpresponse.direct-use-of-httpresponse", "path": "downloaded_repos/openfun_richie/src/richie/apps/search/views.py", "start": {"line": 23, "col": 16, "offset": 760}, "end": {"line": 26, "col": 10, "offset": 926}, "extra": {"message": "Detected data rendered directly to the end user via 'HttpResponse' or a similar object. This bypasses Django's built-in cross-site scripting (XSS) defenses and could result in an XSS vulnerability. Use Django's template engine to safely render HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.1/intro/tutorial03/#a-shortcut-render", "https://docs.djangoproject.com/en/3.1/topics/http/shortcuts/#render"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.direct-use-of-httpresponse.direct-use-of-httpresponse", "shortlink": "https://sg.run/EknN"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/category_detail.html:1:\n Failure: not a program", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/category_detail.html"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_blogpost_glimpse.html:1:\n Failure: not a program", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_blogpost_glimpse.html"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_category_glimpse.html:1:\n Failure: not a program", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_category_glimpse.html"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_organization_main_logo.html:1:\n Failure: not a program", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_organization_main_logo.html"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_person_glimpse.html:1:\n Failure: not a program", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_person_glimpse.html"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_program_glimpse.html:1:\n Failure: not a program", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_program_glimpse.html"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/plugins/person.html:1:\n Failure: not a program", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/plugins/person.html"}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/bin/release", "start": {"line": 49, "col": 26, "offset": 0}, "end": {"line": 49, "col": 52, "offset": 26}}, {"path": "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/bin/release", "start": {"line": 59, "col": 30, "offset": 0}, "end": {"line": 59, "col": 56, "offset": 26}}, {"path": "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/bin/release", "start": {"line": 63, "col": 33, "offset": 0}, "end": {"line": 63, "col": 59, "offset": 26}}]], "message": "Syntax error at line downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/bin/release:49:\n `current_version_array[0] +` was unexpected", "path": "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/bin/release", "spans": [{"file": "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/bin/release", "start": {"line": 49, "col": 26, "offset": 0}, "end": {"line": 49, "col": 52, "offset": 26}}, {"file": "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/bin/release", "start": {"line": 59, "col": 30, "offset": 0}, "end": {"line": 59, "col": 56, "offset": 26}}, {"file": "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/bin/release", "start": {"line": 63, "col": 33, "offset": 0}, "end": {"line": 63, "col": 59, "offset": 26}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/djangocms_link/button-caesura/link.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 19, "offset": 111}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/djangocms_link/button-caesura/link.html", "start": {"line": 4, "col": 332, "offset": 0}, "end": {"line": 6, "col": 19, "offset": 35}}]], "message": "Syntax error at line downloaded_repos/openfun_richie/src/richie/apps/core/templates/djangocms_link/button-caesura/link.html:1:\n `{% load cms_tags %}{% spaceless %}\n\n{# this needs to be in one line for rendering purpose #}\n{% endspaceless %}` was unexpected", "path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/djangocms_link/button-caesura/link.html", "spans": [{"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/djangocms_link/button-caesura/link.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 19, "offset": 111}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/djangocms_link/button-caesura/link.html", "start": {"line": 4, "col": 332, "offset": 0}, "end": {"line": 6, "col": 19, "offset": 35}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/djangocms_video/default/video_player.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 96, "col": 17, "offset": 5547}}]], "message": "Syntax error at line downloaded_repos/openfun_richie/src/richie/apps/core/templates/djangocms_video/default/video_player.html:1:\n `{% load i18n cms_tags extra_tags thumbnail static %}\n{% comment %}\nThis is a copy of original template from plugin just to clean <iframe> from\nobsolete attribute \"frameborder\" and invalid \"allowfullscreen\" attribute value.\n\nFor performance reasons instead of loading the video iframe directly, \nit changes the default template with an hidden iframe that is only visible\nwhen the user clicks on the image with a big play icon '▶', the image comes \nfrom the video poster or the course cover.\nOnly after the user clicks on the play icon '▶', the browser loads the external\nvideo player iframe.\nAdditionaly, it tries to autoplay the external video player, this feature depends\non the browser and external video platform implementations.\n{% endcomment %}\n\n{% if instance.embed_link %}\n    {# show iframe if embed_link is provided #}\n    <div class=\"aspect-ratio\">\n        {% if RICHIE_VIDEO_PLUGIN_LAZY_LOADING %}\n            <a class=\"video-player-image\" onclick=\"this.style.display='none'; this.parentNode.getElementsByTagName('iframe')[0].style.display='block'; this.parentNode.getElementsByTagName('iframe')[0].src=this.parentNode.getElementsByTagName('iframe')[0].getAttribute('data-src');\" href=\"javascript:void(0)\">\n                {% if instance.poster %}\n                    <img\n                        src='{% thumbnail instance.poster.url 300x170 replace_alpha='#FFFFFF' crop upscale subject_location=instance.poster.subject_location %}'\n                        srcset='\n                        {% thumbnail instance.poster 300x170 replace_alpha='#FFFFFF' crop upscale subject_location=instance.poster.subject_location %} 300w\n                        {% if instance.poster.width >= 600 %},{% thumbnail instance.poster 600x340 replace_alpha='#FFFFFF' crop upscale subject_location=instance.poster.subject_location %} 600w{% endif %}\n                        {% if instance.poster.width >= 900 %},{% thumbnail instance.poster 900x510 replace_alpha='#FFFFFF' crop upscale subject_location=instance.poster.subject_location %} 900w{% endif %}... (truncated 3516 more characters)", "path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/djangocms_video/default/video_player.html", "spans": [{"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/djangocms_video/default/video_player.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 96, "col": 17, "offset": 5547}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/menu/breadcrumb_item.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 32, "offset": 31}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/menu/breadcrumb_item.html", "start": {"line": 9, "col": 1, "offset": 0}, "end": {"line": 9, "col": 13, "offset": 12}}]], "message": "Syntax error at line downloaded_repos/openfun_richie/src/richie/apps/core/templates/menu/breadcrumb_item.html:1:\n `{% for ancestor in ancestors %}` was unexpected", "path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/menu/breadcrumb_item.html", "spans": [{"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/menu/breadcrumb_item.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 32, "offset": 31}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/menu/breadcrumb_item.html", "start": {"line": 9, "col": 1, "offset": 0}, "end": {"line": 9, "col": 13, "offset": 12}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/menu/header_menu.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 18, "col": 71, "offset": 1177}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/menu/header_menu.html", "start": {"line": 39, "col": 17, "offset": 0}, "end": {"line": 39, "col": 27, "offset": 10}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/menu/header_menu.html", "start": {"line": 43, "col": 17, "offset": 0}, "end": {"line": 49, "col": 19, "offset": 102}}]], "message": "Syntax error at line downloaded_repos/openfun_richie/src/richie/apps/core/templates/menu/header_menu.html:1:\n `{% load cms_tags menu_tags %}{% spaceless %}\n\n{% for child in children %}\n    {% with children_slug=child.get_menu_title|slugify menu_options=child.menu_extension %}\n        {% with page_url=current_page.get_absolute_url redirect_url=child.attr.redirect_url %}\n            <li class=\"topbar__item\n                {% if menu_options.allow_submenu and child.children %}dropdown{% endif %}\n                {% if menu_options.allow_submenu and menu_options.menu_color %} topbar__item--{{ menu_options.menu_color }}{% endif %}\n                {% if child.ancestor %} topbar__item--ancestor{% endif %}\n                {% if child.sibling %} topbar__item--sibling{% endif %}\n                {% if child.descendant %} topbar__item--descendant{% endif %}\n                {% if child.selected %} topbar__item--selected\n                {% elif redirect_url and page_url == redirect_url %} topbar__item--selected\n                {% endif %}\"\n            >\n                {% comment %}Dropdown menu for children are only for page with index page\n                extension with a specific option enabled{% endcomment %}\n                {% if menu_options.allow_submenu and child.children %}` was unexpected", "path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/menu/header_menu.html", "spans": [{"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/menu/header_menu.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 18, "col": 71, "offset": 1177}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/menu/header_menu.html", "start": {"line": 39, "col": 17, "offset": 0}, "end": {"line": 39, "col": 27, "offset": 10}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/menu/header_menu.html", "start": {"line": 43, "col": 17, "offset": 0}, "end": {"line": 49, "col": 19, "offset": 102}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/menu/menu.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 11, "col": 36, "offset": 638}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/menu/menu.html", "start": {"line": 15, "col": 13, "offset": 0}, "end": {"line": 20, "col": 19, "offset": 76}}]], "message": "Syntax error at line downloaded_repos/openfun_richie/src/richie/apps/core/templates/menu/menu.html:1:\n `{% load cms_tags %}{% spaceless %}\n\n{% for child in children %}\n    {% with children_slug=child.get_menu_title|slugify %}\n        <li class=\"menu__list__item\n            {% if child.selected %} menu__list__item--selected{% endif %}\n            {% if child.ancestor %} menu__list__item--ancestor{% endif %}\n            {% if child.sibling %} menu__list__item--sibling{% endif %}\n            {% if child.descendant %} menu__list__item--descendant{% endif %}\">\n            <a class=\"menu__list__item__link\" href=\"{{ child.attr.redirect_url|default:child.get_absolute_url }}\">{{ child.get_menu_title }}</a>\n            {% if child.children %}` was unexpected", "path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/menu/menu.html", "spans": [{"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/menu/menu.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 11, "col": 36, "offset": 638}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/menu/menu.html", "start": {"line": 15, "col": 13, "offset": 0}, "end": {"line": 20, "col": 19, "offset": 76}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/base.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 88, "offset": 87}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/base.html", "start": {"line": 50, "col": 74, "offset": 0}, "end": {"line": 50, "col": 95, "offset": 21}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/base.html", "start": {"line": 105, "col": 76, "offset": 0}, "end": {"line": 105, "col": 85, "offset": 9}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/base.html", "start": {"line": 107, "col": 64, "offset": 0}, "end": {"line": 107, "col": 91, "offset": 27}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/base.html", "start": {"line": 115, "col": 63, "offset": 0}, "end": {"line": 116, "col": 50, "offset": 55}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/base.html", "start": {"line": 122, "col": 94, "offset": 0}, "end": {"line": 122, "col": 101, "offset": 7}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/base.html", "start": {"line": 173, "col": 64, "offset": 0}, "end": {"line": 173, "col": 93, "offset": 29}}]], "message": "Syntax error at line downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/base.html:1:\n `{% load i18n cms_tags menu_tags rfc_5646_locale static sekizai_tags full_static_tags %}` was unexpected", "path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/base.html", "spans": [{"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/base.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 88, "offset": 87}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/base.html", "start": {"line": 50, "col": 74, "offset": 0}, "end": {"line": 50, "col": 95, "offset": 21}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/base.html", "start": {"line": 105, "col": 76, "offset": 0}, "end": {"line": 105, "col": 85, "offset": 9}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/base.html", "start": {"line": 107, "col": 64, "offset": 0}, "end": {"line": 107, "col": 91, "offset": 27}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/base.html", "start": {"line": 115, "col": 63, "offset": 0}, "end": {"line": 116, "col": 50, "offset": 55}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/base.html", "start": {"line": 122, "col": 94, "offset": 0}, "end": {"line": 122, "col": 101, "offset": 7}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/base.html", "start": {"line": 173, "col": 64, "offset": 0}, "end": {"line": 173, "col": 93, "offset": 29}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/child_pages_list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 20, "offset": 162}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/child_pages_list.html", "start": {"line": 25, "col": 1, "offset": 0}, "end": {"line": 25, "col": 23, "offset": 22}}]], "message": "Syntax error at line downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/child_pages_list.html:1:\n `{% extends \"richie/fullwidth.html\" %}\n{% load cms_tags i18n %}\n\n{# This is a generic page template that will list and link its child pages #}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/child_pages_list.html", "spans": [{"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/child_pages_list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 20, "offset": 162}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/child_pages_list.html", "start": {"line": 25, "col": 1, "offset": 0}, "end": {"line": 25, "col": 23, "offset": 22}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/error.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 11, "col": 20, "offset": 209}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/error.html", "start": {"line": 20, "col": 1, "offset": 0}, "end": {"line": 20, "col": 23, "offset": 22}}]], "message": "Syntax error at line downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/error.html:1:\n `{% extends \"richie/base.html\" %}\n{% load i18n cms_tags %}\n\n{% block head_title %}\n    {{status}} - {{ title }}\n{% endblock head_title %}\n\n{% block breadcrumbs %}{% endblock breadcrumbs %}\n\n\n{% block content %}` was unexpected", "path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/error.html", "spans": [{"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/error.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 11, "col": 20, "offset": 209}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/error.html", "start": {"line": 20, "col": 1, "offset": 0}, "end": {"line": 20, "col": 23, "offset": 22}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/hreflang.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 38, "offset": 102}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/hreflang.html", "start": {"line": 5, "col": 5, "offset": 0}, "end": {"line": 6, "col": 12, "offset": 24}}]], "message": "Syntax error at line downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/hreflang.html:1:\n `{% load i18n menu_tags cms_tags %}\n{% if languages|length > 1 %}\n    {% for code, name in languages %}` was unexpected", "path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/hreflang.html", "spans": [{"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/hreflang.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 38, "offset": 102}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/hreflang.html", "start": {"line": 5, "col": 5, "offset": 0}, "end": {"line": 6, "col": 12, "offset": 24}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/pagination_inner.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 31, "offset": 176}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/pagination_inner.html", "start": {"line": 26, "col": 79, "offset": 0}, "end": {"line": 26, "col": 123, "offset": 44}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/pagination_inner.html", "start": {"line": 54, "col": 1, "offset": 0}, "end": {"line": 54, "col": 19, "offset": 18}}]], "message": "Syntax error at line downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/pagination_inner.html:1:\n `{% comment %}\nthis is not meant to be included as is in a template,\nbecause wrapper tags are missing. See richie/pagination.html\n{% endcomment %}\n{% load i18n %}{% spaceless %}` was unexpected", "path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/pagination_inner.html", "spans": [{"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/pagination_inner.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 31, "offset": 176}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/pagination_inner.html", "start": {"line": 26, "col": 79, "offset": 0}, "end": {"line": 26, "col": 123, "offset": 44}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/pagination_inner.html", "start": {"line": 54, "col": 1, "offset": 0}, "end": {"line": 54, "col": 19, "offset": 18}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/styleguide/fragment_inline_markup.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 12, "offset": 11}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/styleguide/fragment_inline_markup.html", "start": {"line": 1, "col": 25, "offset": 0}, "end": {"line": 1, "col": 39, "offset": 14}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/styleguide/fragment_inline_markup.html", "start": {"line": 1, "col": 64, "offset": 0}, "end": {"line": 1, "col": 75, "offset": 11}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/styleguide/fragment_inline_markup.html", "start": {"line": 1, "col": 90, "offset": 0}, "end": {"line": 1, "col": 109, "offset": 19}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/styleguide/fragment_inline_markup.html", "start": {"line": 1, "col": 128, "offset": 0}, "end": {"line": 1, "col": 140, "offset": 12}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/styleguide/fragment_inline_markup.html", "start": {"line": 1, "col": 162, "offset": 0}, "end": {"line": 1, "col": 181, "offset": 19}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/styleguide/fragment_inline_markup.html", "start": {"line": 1, "col": 204, "offset": 0}, "end": {"line": 1, "col": 231, "offset": 27}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/styleguide/fragment_inline_markup.html", "start": {"line": 1, "col": 255, "offset": 0}, "end": {"line": 1, "col": 265, "offset": 10}}]], "message": "Syntax error at line downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/styleguide/fragment_inline_markup.html:1:\n `Suspendisse` was unexpected", "path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/styleguide/fragment_inline_markup.html", "spans": [{"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/styleguide/fragment_inline_markup.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 12, "offset": 11}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/styleguide/fragment_inline_markup.html", "start": {"line": 1, "col": 25, "offset": 0}, "end": {"line": 1, "col": 39, "offset": 14}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/styleguide/fragment_inline_markup.html", "start": {"line": 1, "col": 64, "offset": 0}, "end": {"line": 1, "col": 75, "offset": 11}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/styleguide/fragment_inline_markup.html", "start": {"line": 1, "col": 90, "offset": 0}, "end": {"line": 1, "col": 109, "offset": 19}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/styleguide/fragment_inline_markup.html", "start": {"line": 1, "col": 128, "offset": 0}, "end": {"line": 1, "col": 140, "offset": 12}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/styleguide/fragment_inline_markup.html", "start": {"line": 1, "col": 162, "offset": 0}, "end": {"line": 1, "col": 181, "offset": 19}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/styleguide/fragment_inline_markup.html", "start": {"line": 1, "col": 204, "offset": 0}, "end": {"line": 1, "col": 231, "offset": 27}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/styleguide/fragment_inline_markup.html", "start": {"line": 1, "col": 255, "offset": 0}, "end": {"line": 1, "col": 265, "offset": 10}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/styleguide/index.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 8, "col": 20, "offset": 181}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/styleguide/index.html", "start": {"line": 78, "col": 73, "offset": 0}, "end": {"line": 78, "col": 80, "offset": 7}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/styleguide/index.html", "start": {"line": 514, "col": 1, "offset": 0}, "end": {"line": 514, "col": 23, "offset": 22}}]], "message": "Syntax error at line downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/styleguide/index.html:1:\n `{% extends \"richie/base.html\" %}\n{% load static %}\n\n{% block breadcrumbs %}{% endblock breadcrumbs %}\n\n{% block head_title %}Styleguide{% endblock head_title %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/styleguide/index.html", "spans": [{"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/styleguide/index.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 8, "col": 20, "offset": 181}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/styleguide/index.html", "start": {"line": 78, "col": 73, "offset": 0}, "end": {"line": 78, "col": 80, "offset": 7}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/styleguide/index.html", "start": {"line": 514, "col": 1, "offset": 0}, "end": {"line": 514, "col": 23, "offset": 22}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/blogpost-badges.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 11, "col": 74, "offset": 649}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/blogpost-badges.html", "start": {"line": 14, "col": 13, "offset": 0}, "end": {"line": 15, "col": 13, "offset": 19}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/blogpost-badges.html", "start": {"line": 21, "col": 67, "offset": 0}, "end": {"line": 21, "col": 72, "offset": 5}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/blogpost-badges.html", "start": {"line": 31, "col": 68, "offset": 0}, "end": {"line": 31, "col": 73, "offset": 5}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/blogpost-badges.html", "start": {"line": 41, "col": 65, "offset": 0}, "end": {"line": 41, "col": 70, "offset": 5}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/blogpost-badges.html", "start": {"line": 46, "col": 5, "offset": 0}, "end": {"line": 48, "col": 19, "offset": 39}}]], "message": "Syntax error at line downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/blogpost-badges.html:1:\n `{% load i18n static %}{% spaceless %}\n{% with full_page_url=SITE.web_url|add:page_url %}\n    {% blocktrans with title=page_title asvar mailto_subject %}Richie news: {{ title }}{% endblocktrans %}\n    {% blocktrans with title=page_title|safe url=full_page_url asvar shared_sentence %}Richie news: {{ title }} {{ url }}{% endblocktrans %}\n    <div class=\"social-network-badges\">\n        <a\n            href=\"https://www.facebook.com/share.php?u={{ full_page_url }}\"\n            class=\"social-network-badges__item\"\n            target=\"_blank\" rel=\"noopener noreferrer\"\n        >\n            <svg role=\"img\" aria-label=\"{% trans \"Share on Facebook\" %}\">` was unexpected", "path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/blogpost-badges.html", "spans": [{"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/blogpost-badges.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 11, "col": 74, "offset": 649}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/blogpost-badges.html", "start": {"line": 14, "col": 13, "offset": 0}, "end": {"line": 15, "col": 13, "offset": 19}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/blogpost-badges.html", "start": {"line": 21, "col": 67, "offset": 0}, "end": {"line": 21, "col": 72, "offset": 5}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/blogpost-badges.html", "start": {"line": 31, "col": 68, "offset": 0}, "end": {"line": 31, "col": 73, "offset": 5}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/blogpost-badges.html", "start": {"line": 41, "col": 65, "offset": 0}, "end": {"line": 41, "col": 70, "offset": 5}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/blogpost-badges.html", "start": {"line": 46, "col": 5, "offset": 0}, "end": {"line": 48, "col": 19, "offset": 39}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/course-badges.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 11, "col": 74, "offset": 667}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/course-badges.html", "start": {"line": 14, "col": 13, "offset": 0}, "end": {"line": 15, "col": 13, "offset": 19}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/course-badges.html", "start": {"line": 21, "col": 67, "offset": 0}, "end": {"line": 21, "col": 72, "offset": 5}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/course-badges.html", "start": {"line": 31, "col": 68, "offset": 0}, "end": {"line": 31, "col": 73, "offset": 5}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/course-badges.html", "start": {"line": 41, "col": 65, "offset": 0}, "end": {"line": 41, "col": 70, "offset": 5}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/course-badges.html", "start": {"line": 46, "col": 3, "offset": 0}, "end": {"line": 48, "col": 19, "offset": 39}}]], "message": "Syntax error at line downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/course-badges.html:1:\n `{% load i18n static %}{% spaceless %}\n{% with full_page_url=SITE.web_url|add:page_url %}\n    {% blocktrans asvar shared_subject %}Follow a course online with <PERSON>{% endblocktrans %}\n    {% blocktrans with title=page_title|safe url=full_page_url asvar shared_sentence %}I just enrolled to the course \"{{ title }}\" on Richie: {{ url }}{% endblocktrans %}\n    <div class=\"social-network-badges\">\n        <a\n            href=\"https://www.facebook.com/share.php?u={{ full_page_url }}\"\n            class=\"social-network-badges__item\"\n            target=\"_blank\" rel=\"noopener noreferrer\"\n        >\n            <svg role=\"img\" aria-label=\"{% trans \"Share on Facebook\" %}\">` was unexpected", "path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/course-badges.html", "spans": [{"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/course-badges.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 11, "col": 74, "offset": 667}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/course-badges.html", "start": {"line": 14, "col": 13, "offset": 0}, "end": {"line": 15, "col": 13, "offset": 19}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/course-badges.html", "start": {"line": 21, "col": 67, "offset": 0}, "end": {"line": 21, "col": 72, "offset": 5}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/course-badges.html", "start": {"line": 31, "col": 68, "offset": 0}, "end": {"line": 31, "col": 73, "offset": 5}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/course-badges.html", "start": {"line": 41, "col": 65, "offset": 0}, "end": {"line": 41, "col": 70, "offset": 5}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/course-badges.html", "start": {"line": 46, "col": 3, "offset": 0}, "end": {"line": 48, "col": 19, "offset": 39}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/footer-badges.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 38, "offset": 37}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/footer-badges.html", "start": {"line": 7, "col": 55, "offset": 0}, "end": {"line": 7, "col": 60, "offset": 5}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/footer-badges.html", "start": {"line": 17, "col": 59, "offset": 0}, "end": {"line": 17, "col": 64, "offset": 5}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/footer-badges.html", "start": {"line": 27, "col": 60, "offset": 0}, "end": {"line": 27, "col": 65, "offset": 5}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/footer-badges.html", "start": {"line": 32, "col": 1, "offset": 0}, "end": {"line": 32, "col": 19, "offset": 18}}]], "message": "Syntax error at line downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/footer-badges.html:1:\n `{% load i18n static %}{% spaceless %}` was unexpected", "path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/footer-badges.html", "spans": [{"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/footer-badges.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 38, "offset": 37}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/footer-badges.html", "start": {"line": 7, "col": 55, "offset": 0}, "end": {"line": 7, "col": 60, "offset": 5}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/footer-badges.html", "start": {"line": 17, "col": 59, "offset": 0}, "end": {"line": 17, "col": 64, "offset": 5}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/footer-badges.html", "start": {"line": 27, "col": 60, "offset": 0}, "end": {"line": 27, "col": 65, "offset": 5}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/footer-badges.html", "start": {"line": 32, "col": 1, "offset": 0}, "end": {"line": 32, "col": 19, "offset": 18}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 11, "col": 45, "offset": 489}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 20, "col": 64, "offset": 0}, "end": {"line": 20, "col": 141, "offset": 77}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 21, "col": 5, "offset": 0}, "end": {"line": 24, "col": 30, "offset": 75}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 27, "col": 5, "offset": 0}, "end": {"line": 31, "col": 149, "offset": 293}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 35, "col": 5, "offset": 0}, "end": {"line": 36, "col": 73, "offset": 84}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 37, "col": 71, "offset": 0}, "end": {"line": 37, "col": 148, "offset": 77}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 38, "col": 5, "offset": 0}, "end": {"line": 43, "col": 22, "offset": 158}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 126, "col": 64, "offset": 0}, "end": {"line": 126, "col": 190, "offset": 126}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 203, "col": 1, "offset": 0}, "end": {"line": 208, "col": 53, "offset": 115}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 418, "col": 63, "offset": 0}, "end": {"line": 420, "col": 48, "offset": 133}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 531, "col": 1, "offset": 0}, "end": {"line": 532, "col": 41, "offset": 54}}]], "message": "Syntax error at line downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html:1:\n `{% extends \"richie/fullwidth.html\" %}\n{% load cms_tags i18n extra_tags pagination_tags static thumbnail humanize joanie feature_flags %}\n\n{% block head_title %}\n    {% page_attribute \"page_title\" as course_title %}\n    {% blocktrans with course_title=course_title %}{{ course_title }} - Course{% endblocktrans %}\n{% endblock head_title %}\n\n{% block meta_index_rules %}\n    {# Make sure course snapshot pages are not indexed by search engines #}\n    {% if current_page.parent_page.course %}` was unexpected", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "spans": [{"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 11, "col": 45, "offset": 489}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 20, "col": 64, "offset": 0}, "end": {"line": 20, "col": 141, "offset": 77}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 21, "col": 5, "offset": 0}, "end": {"line": 24, "col": 30, "offset": 75}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 27, "col": 5, "offset": 0}, "end": {"line": 31, "col": 149, "offset": 293}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 35, "col": 5, "offset": 0}, "end": {"line": 36, "col": 73, "offset": 84}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 37, "col": 71, "offset": 0}, "end": {"line": 37, "col": 148, "offset": 77}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 38, "col": 5, "offset": 0}, "end": {"line": 43, "col": 22, "offset": 158}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 126, "col": 64, "offset": 0}, "end": {"line": 126, "col": 190, "offset": 126}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 203, "col": 1, "offset": 0}, "end": {"line": 208, "col": 53, "offset": 115}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 418, "col": 63, "offset": 0}, "end": {"line": 420, "col": 48, "offset": 133}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "start": {"line": 531, "col": 1, "offset": 0}, "end": {"line": 532, "col": 41, "offset": 54}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_course_glimpse.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 43, "col": 76, "offset": 3267}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_course_glimpse.html", "start": {"line": 54, "col": 29, "offset": 0}, "end": {"line": 58, "col": 49, "offset": 163}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_course_glimpse.html", "start": {"line": 60, "col": 71, "offset": 0}, "end": {"line": 60, "col": 82, "offset": 11}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_course_glimpse.html", "start": {"line": 66, "col": 17, "offset": 0}, "end": {"line": 66, "col": 28, "offset": 11}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_course_glimpse.html", "start": {"line": 68, "col": 70, "offset": 0}, "end": {"line": 68, "col": 81, "offset": 11}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_course_glimpse.html", "start": {"line": 74, "col": 13, "offset": 0}, "end": {"line": 77, "col": 34, "offset": 126}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_course_glimpse.html", "start": {"line": 83, "col": 13, "offset": 0}, "end": {"line": 86, "col": 38, "offset": 75}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_course_glimpse.html", "start": {"line": 89, "col": 66, "offset": 0}, "end": {"line": 89, "col": 77, "offset": 11}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_course_glimpse.html", "start": {"line": 131, "col": 5, "offset": 0}, "end": {"line": 134, "col": 19, "offset": 76}}]], "message": "Syntax error at line downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_course_glimpse.html:1:\n `{% load i18n cms_tags extra_tags static thumbnail %}{% spaceless %}\n{% comment %}Obviously, the context template variable \"course\" is required and must be a Course page extension{% endcomment %}\n\n{% with course_page=course.extended_object course_state=course.state main_organization_title=course.get_main_organization.extended_object.get_menu_title main_organization=course.get_main_organization course_variant=course_variant|default:'glimpse' %}\n<div class=\"\n    course-{{ course_variant }}\n    course-{{ course_variant }}--offer-{{course.offer|default:'free'}}\n    {% if course_page.publisher_is_draft is True %} course-{{ course_variant }}--draft{% endif %}\n    {% if course.certificate_offer %} course-{{ course_variant }}--offer-certificate {% endif %}\n    \">\n    <div class=\"course-{{ course_variant }}__body\">\n        <div aria-hidden=\"true\" class=\"course-{{ course_variant }}__media\">\n            <a tabindex=\"-1\" href=\"{{ course_page.get_absolute_url }}\">\n                {% get_placeholder_plugins \"course_cover\" course_page as cover_plugins or %}\n                    <p class=\"course-{{ course_variant }}--empty\">{% trans \"Cover\" %}</p>\n                {% endget_placeholder_plugins %}\n                {% blockplugin cover_plugins.0 %}\n                    <img src=\"{% thumbnail instance.picture 300x170 replace_alpha='#FFFFFF' crop upscale subject_location=instance.picture.subject_location %}\"\n                        srcset=\"\n                            {% thumbnail instance.picture 800x457 replace_alpha='#FFFFFF' crop upscale subject_location=instance.picture.subject_location %} 300w\n                            {% if instance.picture.width >= 1600 %},{% thumbnail instance.picture 1600x914 replace_alpha='#FFFFFF' crop upscale subject_location=instance.picture.subject_location %} 600w{% endif %}\n                            {% if instance.picture.width >= 2400 %},{% thumbnail instance.picture 2400x1371 replace_alpha='#FFFFFF' crop upscale subject_location=instance.picture.subject_location %} 900w{% endif %}\n                ... (truncated 1236 more characters)", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_course_glimpse.html", "spans": [{"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_course_glimpse.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 43, "col": 76, "offset": 3267}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_course_glimpse.html", "start": {"line": 54, "col": 29, "offset": 0}, "end": {"line": 58, "col": 49, "offset": 163}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_course_glimpse.html", "start": {"line": 60, "col": 71, "offset": 0}, "end": {"line": 60, "col": 82, "offset": 11}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_course_glimpse.html", "start": {"line": 66, "col": 17, "offset": 0}, "end": {"line": 66, "col": 28, "offset": 11}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_course_glimpse.html", "start": {"line": 68, "col": 70, "offset": 0}, "end": {"line": 68, "col": 81, "offset": 11}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_course_glimpse.html", "start": {"line": 74, "col": 13, "offset": 0}, "end": {"line": 77, "col": 34, "offset": 126}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_course_glimpse.html", "start": {"line": 83, "col": 13, "offset": 0}, "end": {"line": 86, "col": 38, "offset": 75}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_course_glimpse.html", "start": {"line": 89, "col": 66, "offset": 0}, "end": {"line": 89, "col": 77, "offset": 11}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_course_glimpse.html", "start": {"line": 131, "col": 5, "offset": 0}, "end": {"line": 134, "col": 19, "offset": 76}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_error_detail_template_banner.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 16, "offset": 32}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_error_detail_template_banner.html", "start": {"line": 14, "col": 1, "offset": 0}, "end": {"line": 14, "col": 19, "offset": 18}}]], "message": "Syntax error at line downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_error_detail_template_banner.html:1:\n `{% load i18n %}\n\n{% spaceless %}` was unexpected", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_error_detail_template_banner.html", "spans": [{"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_error_detail_template_banner.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 16, "offset": 32}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_error_detail_template_banner.html", "start": {"line": 14, "col": 1, "offset": 0}, "end": {"line": 14, "col": 19, "offset": 18}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_organization_glimpse.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 48, "col": 19, "offset": 3737}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_organization_glimpse.html", "start": {"line": 70, "col": 155, "offset": 0}, "end": {"line": 70, "col": 183, "offset": 28}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_organization_glimpse.html", "start": {"line": 72, "col": 9, "offset": 0}, "end": {"line": 75, "col": 14, "offset": 41}}]], "message": "Syntax error at line downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_organization_glimpse.html:1:\n `{% load i18n cms_tags extra_tags static thumbnail %}\n{% comment %}Obviously, the context template variable \"organization\" is required and must be an Organization page extension{% endcomment %}\n{% with organization_page=organization.extended_object organization_variant=organization_variant|default:\"glimpse\" %}\n<div\n    class=\"organization-{{ organization_variant }}{% if organization_page.publisher_is_draft is True %} organization-{{ organization_variant }}--draft{% endif %}\"\n>\n    <a href=\"{{ organization_page.get_absolute_url }}\" title=\"{% blocktranslate with title=organization_page.get_title %}Link to the organization page of {{ title }}{% endblocktranslate %}\" property=\"{{ organization_property|default:'author' }}\" typeof=\"CollegeOrUniversity\">\n        <meta property=\"url\" content=\"{{ SITE.web_url }}{{ organization_page.get_absolute_url }}\" />\n        {% if organization_variant == \"row\" %}\n            <div class=\"organization-{{ organization_variant }}__logo\">\n                {% get_placeholder_plugins \"logo\" organization_page as plugins or %}\n                    <img src=\"{% static 'richie/images/empty/organization_logo.png' %}\"\n                         class=\"organization-{{ organization_variant }}__logo--empty\"\n                         alt=\"\" />\n                {% endget_placeholder_plugins %}\n                {% blockplugin plugins.0 %}\n                    <img src=\"{% thumbnail instance.picture 200x113 replace_alpha='#FFFFFF' upscale subject_location=instance.picture.subject_location %}\"\n                        srcset=\"\n                            {% thumbnail instance.picture 200x113 replace_alpha='#FFFFFF' upscale subject_location=instance.picture.subject_location %} 200w\n                            {% if instance.picture.width >= 400 %},{% thumbnail instance.picture 400x225 replace_alpha='#FFFFFF' upscale subject_location=instance.picture.subject_location %} 400w{% endif %}\n                            {% if instance.picture.width >= 600 %},{% thumbnail instance.picture 600x338 replace_alpha='#FFFFFF' up... (truncated 1706 more characters)", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_organization_glimpse.html", "spans": [{"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_organization_glimpse.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 48, "col": 19, "offset": 3737}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_organization_glimpse.html", "start": {"line": 70, "col": 155, "offset": 0}, "end": {"line": 70, "col": 183, "offset": 28}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_organization_glimpse.html", "start": {"line": 72, "col": 9, "offset": 0}, "end": {"line": 75, "col": 14, "offset": 41}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_detail.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 61, "offset": 198}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_detail.html", "start": {"line": 6, "col": 69, "offset": 0}, "end": {"line": 6, "col": 134, "offset": 65}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_detail.html", "start": {"line": 7, "col": 5, "offset": 0}, "end": {"line": 7, "col": 67, "offset": 62}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_detail.html", "start": {"line": 8, "col": 79, "offset": 0}, "end": {"line": 8, "col": 148, "offset": 69}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_detail.html", "start": {"line": 9, "col": 5, "offset": 0}, "end": {"line": 12, "col": 30, "offset": 75}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_detail.html", "start": {"line": 15, "col": 5, "offset": 0}, "end": {"line": 17, "col": 100, "offset": 188}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_detail.html", "start": {"line": 21, "col": 5, "offset": 0}, "end": {"line": 22, "col": 61, "offset": 72}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_detail.html", "start": {"line": 23, "col": 76, "offset": 0}, "end": {"line": 23, "col": 141, "offset": 65}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_detail.html", "start": {"line": 24, "col": 5, "offset": 0}, "end": {"line": 24, "col": 67, "offset": 62}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_detail.html", "start": {"line": 25, "col": 86, "offset": 0}, "end": {"line": 25, "col": 155, "offset": 69}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_detail.html", "start": {"line": 26, "col": 5, "offset": 0}, "end": {"line": 33, "col": 22, "offset": 264}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_detail.html", "start": {"line": 148, "col": 55, "offset": 0}, "end": {"line": 149, "col": 114, "offset": 120}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_detail.html", "start": {"line": 177, "col": 51, "offset": 0}, "end": {"line": 179, "col": 36, "offset": 152}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_detail.html", "start": {"line": 187, "col": 1, "offset": 0}, "end": {"line": 191, "col": 41, "offset": 178}}]], "message": "Syntax error at line downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_detail.html:1:\n `{% extends \"richie/fullwidth.html\" %}\n{% load cms_tags extra_tags i18n pagination_tags static thumbnail %}\n\n{% block meta_html_default %}\n    {% if not current_page|is_empty_placeholder:\"excerpt\" %}` was unexpected", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_detail.html", "spans": [{"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_detail.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 61, "offset": 198}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_detail.html", "start": {"line": 6, "col": 69, "offset": 0}, "end": {"line": 6, "col": 134, "offset": 65}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_detail.html", "start": {"line": 7, "col": 5, "offset": 0}, "end": {"line": 7, "col": 67, "offset": 62}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_detail.html", "start": {"line": 8, "col": 79, "offset": 0}, "end": {"line": 8, "col": 148, "offset": 69}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_detail.html", "start": {"line": 9, "col": 5, "offset": 0}, "end": {"line": 12, "col": 30, "offset": 75}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_detail.html", "start": {"line": 15, "col": 5, "offset": 0}, "end": {"line": 17, "col": 100, "offset": 188}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_detail.html", "start": {"line": 21, "col": 5, "offset": 0}, "end": {"line": 22, "col": 61, "offset": 72}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_detail.html", "start": {"line": 23, "col": 76, "offset": 0}, "end": {"line": 23, "col": 141, "offset": 65}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_detail.html", "start": {"line": 24, "col": 5, "offset": 0}, "end": {"line": 24, "col": 67, "offset": 62}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_detail.html", "start": {"line": 25, "col": 86, "offset": 0}, "end": {"line": 25, "col": 155, "offset": 69}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_detail.html", "start": {"line": 26, "col": 5, "offset": 0}, "end": {"line": 33, "col": 22, "offset": 264}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_detail.html", "start": {"line": 148, "col": 55, "offset": 0}, "end": {"line": 149, "col": 114, "offset": 120}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_detail.html", "start": {"line": 177, "col": 51, "offset": 0}, "end": {"line": 179, "col": 36, "offset": 152}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_detail.html", "start": {"line": 187, "col": 1, "offset": 0}, "end": {"line": 191, "col": 41, "offset": 178}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/person_detail.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 57, "offset": 194}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/person_detail.html", "start": {"line": 6, "col": 64, "offset": 0}, "end": {"line": 6, "col": 125, "offset": 61}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/person_detail.html", "start": {"line": 7, "col": 5, "offset": 0}, "end": {"line": 10, "col": 30, "offset": 75}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/person_detail.html", "start": {"line": 13, "col": 5, "offset": 0}, "end": {"line": 15, "col": 166, "offset": 258}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/person_detail.html", "start": {"line": 19, "col": 5, "offset": 0}, "end": {"line": 20, "col": 57, "offset": 68}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/person_detail.html", "start": {"line": 21, "col": 71, "offset": 0}, "end": {"line": 21, "col": 132, "offset": 61}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/person_detail.html", "start": {"line": 22, "col": 5, "offset": 0}, "end": {"line": 27, "col": 22, "offset": 158}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/person_detail.html", "start": {"line": 66, "col": 60, "offset": 0}, "end": {"line": 67, "col": 46, "offset": 84}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/person_detail.html", "start": {"line": 93, "col": 1, "offset": 0}, "end": {"line": 98, "col": 20, "offset": 134}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/person_detail.html", "start": {"line": 136, "col": 59, "offset": 0}, "end": {"line": 137, "col": 110, "offset": 116}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/person_detail.html", "start": {"line": 163, "col": 55, "offset": 0}, "end": {"line": 165, "col": 40, "offset": 154}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/person_detail.html", "start": {"line": 172, "col": 5, "offset": 0}, "end": {"line": 176, "col": 41, "offset": 180}}]], "message": "Syntax error at line downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/person_detail.html:1:\n `{% extends \"richie/fullwidth.html\" %}\n{% load cms_tags extra_tags i18n pagination_tags static thumbnail %}\n\n{% block meta_html_default %}\n    {% if not current_page|is_empty_placeholder:\"bio\" %}` was unexpected", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/person_detail.html", "spans": [{"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/person_detail.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 57, "offset": 194}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/person_detail.html", "start": {"line": 6, "col": 64, "offset": 0}, "end": {"line": 6, "col": 125, "offset": 61}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/person_detail.html", "start": {"line": 7, "col": 5, "offset": 0}, "end": {"line": 10, "col": 30, "offset": 75}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/person_detail.html", "start": {"line": 13, "col": 5, "offset": 0}, "end": {"line": 15, "col": 166, "offset": 258}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/person_detail.html", "start": {"line": 19, "col": 5, "offset": 0}, "end": {"line": 20, "col": 57, "offset": 68}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/person_detail.html", "start": {"line": 21, "col": 71, "offset": 0}, "end": {"line": 21, "col": 132, "offset": 61}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/person_detail.html", "start": {"line": 22, "col": 5, "offset": 0}, "end": {"line": 27, "col": 22, "offset": 158}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/person_detail.html", "start": {"line": 66, "col": 60, "offset": 0}, "end": {"line": 67, "col": 46, "offset": 84}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/person_detail.html", "start": {"line": 93, "col": 1, "offset": 0}, "end": {"line": 98, "col": 20, "offset": 134}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/person_detail.html", "start": {"line": 136, "col": 59, "offset": 0}, "end": {"line": 137, "col": 110, "offset": 116}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/person_detail.html", "start": {"line": 163, "col": 55, "offset": 0}, "end": {"line": 165, "col": 40, "offset": 154}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/person_detail.html", "start": {"line": 172, "col": 5, "offset": 0}, "end": {"line": 176, "col": 41, "offset": 180}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/program_detail.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 53, "col": 47, "offset": 2623}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/program_detail.html", "start": {"line": 55, "col": 9, "offset": 0}, "end": {"line": 59, "col": 48, "offset": 124}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/program_detail.html", "start": {"line": 80, "col": 41, "offset": 0}, "end": {"line": 80, "col": 47, "offset": 6}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/program_detail.html", "start": {"line": 100, "col": 5, "offset": 0}, "end": {"line": 102, "col": 164, "offset": 193}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/program_detail.html", "start": {"line": 124, "col": 9, "offset": 0}, "end": {"line": 127, "col": 110, "offset": 133}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/program_detail.html", "start": {"line": 135, "col": 5, "offset": 0}, "end": {"line": 141, "col": 35, "offset": 99}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/program_detail.html", "start": {"line": 194, "col": 145, "offset": 0}, "end": {"line": 194, "col": 151, "offset": 6}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/program_detail.html", "start": {"line": 247, "col": 1, "offset": 0}, "end": {"line": 247, "col": 41, "offset": 40}}]], "message": "Syntax error at line downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/program_detail.html:1:\n `{% extends \"richie/fullwidth.html\" %}\n{% load cms_tags extra_tags i18n thumbnail %}\n\n{% block head_title %}\n    {% page_attribute \"page_title\" as program_title %}\n    {% blocktrans with program_title=program_title %}{{ program_title }} - Program{% endblocktrans %}\n{% endblock head_title %}\n\n{% block meta_html_default %}\n    {% if not current_page|is_empty_placeholder:\"program_excerpt\" %}\n        <meta name=\"description\" content=\"{% filter slice:\":160\" %}{% show_placeholder 'program_excerpt' current_page %}{% endfilter %}\" />\n    {% endif %}\n{% endblock meta_html_default %}\n\n{% block meta_rdfa_context %}\n    <meta property=\"og:type\" content=\"website\" />\n\n    {% get_placeholder_plugins \"program_cover\" as og_image_plugins %}\n    {% if og_image_plugins.0 %}\n        {% thumbnail og_image_plugins.0.picture 1200x630 replace_alpha='#FFFFFF' crop upscale subject_location=og_image_plugins.0.picture.subject_location as thumb %}\n        <meta property=\"og:image\" content=\"{{ MEDIA_URL_PREFIX }}{{ thumb.url }}\" />\n        <meta property=\"og:image:width\" content=\"{{ thumb.width }}\" />\n        <meta property=\"og:image:height\" content=\"{{ thumb.height }}\" />  \n    {% endif %}\n    {% if not current_page|is_empty_placeholder:\"program_excerpt\" %}\n        <meta property=\"og:description\" content=\"{% filter slice:\":200\" %}{% show_placeholder 'program_excerpt' current_page %}{% endfilter %}\" />\n    {% endif %}\n{% endblock meta_rdfa_context %}\n\n{% block body_rdfa %} vocab=\"https://schema.org/\" typeof=\"EducationalOccupationalProgram\"{% endblock body_rdfa %}\n\n{% block subheader_content %}\n<div class=\"subheader__container\">\n    <div class=\"subheader__intro\">\n    <div class=\"program-detail__main\">\n    {% block categories %}\n        {% if current_page.publisher_is_draft or not current_page|is_empty_placeholder:\"program_categories\" %}\n            <div class=\"category-badge-list subheader__badges\">\n                <div class=\"category-badge-list__container\">\n                    {% with category_variant=\"badge\" is_keywords_property=True %}\n    ... (truncated 592 more characters)", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/program_detail.html", "spans": [{"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/program_detail.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 53, "col": 47, "offset": 2623}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/program_detail.html", "start": {"line": 55, "col": 9, "offset": 0}, "end": {"line": 59, "col": 48, "offset": 124}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/program_detail.html", "start": {"line": 80, "col": 41, "offset": 0}, "end": {"line": 80, "col": 47, "offset": 6}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/program_detail.html", "start": {"line": 100, "col": 5, "offset": 0}, "end": {"line": 102, "col": 164, "offset": 193}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/program_detail.html", "start": {"line": 124, "col": 9, "offset": 0}, "end": {"line": 127, "col": 110, "offset": 133}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/program_detail.html", "start": {"line": 135, "col": 5, "offset": 0}, "end": {"line": 141, "col": 35, "offset": 99}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/program_detail.html", "start": {"line": 194, "col": 145, "offset": 0}, "end": {"line": 194, "col": 151, "offset": 6}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/program_detail.html", "start": {"line": 247, "col": 1, "offset": 0}, "end": {"line": 247, "col": 41, "offset": 40}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/plugins/licence_plugin.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 16, "offset": 37}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/plugins/licence_plugin.html", "start": {"line": 20, "col": 12, "offset": 0}, "end": {"line": 20, "col": 40, "offset": 28}}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/plugins/licence_plugin.html", "start": {"line": 31, "col": 1, "offset": 0}, "end": {"line": 31, "col": 19, "offset": 18}}]], "message": "Syntax error at line downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/plugins/licence_plugin.html:1:\n `{% load thumbnail %}\n\n{% spaceless %}` was unexpected", "path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/plugins/licence_plugin.html", "spans": [{"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/plugins/licence_plugin.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 16, "offset": 37}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/plugins/licence_plugin.html", "start": {"line": 20, "col": 12, "offset": 0}, "end": {"line": 20, "col": 40, "offset": 28}}, {"file": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/plugins/licence_plugin.html", "start": {"line": 31, "col": 1, "offset": 0}, "end": {"line": 31, "col": 19, "offset": 18}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/templates/richie/glimpse/glimpse.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 31, "offset": 198}}, {"path": "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/templates/richie/glimpse/glimpse.html", "start": {"line": 38, "col": 16, "offset": 0}, "end": {"line": 38, "col": 44, "offset": 28}}, {"path": "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/templates/richie/glimpse/glimpse.html", "start": {"line": 50, "col": 5, "offset": 0}, "end": {"line": 55, "col": 19, "offset": 73}}]], "message": "Syntax error at line downloaded_repos/openfun_richie/src/richie/plugins/glimpse/templates/richie/glimpse/glimpse.html:1:\n `{% load thumbnail %}{% spaceless %}\n\n{% if glimpse_variant in \"quote,person\" %}\n    {% include \"richie/glimpse/glimpse_\"|add:glimpse_variant|add:\".html\" %}\n{% else %}\n\n    {% if instance.get_link %}` was unexpected", "path": "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/templates/richie/glimpse/glimpse.html", "spans": [{"file": "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/templates/richie/glimpse/glimpse.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 31, "offset": 198}}, {"file": "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/templates/richie/glimpse/glimpse.html", "start": {"line": 38, "col": 16, "offset": 0}, "end": {"line": 38, "col": 44, "offset": 28}}, {"file": "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/templates/richie/glimpse/glimpse.html", "start": {"line": 50, "col": 5, "offset": 0}, "end": {"line": 55, "col": 19, "offset": 73}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/templates/richie/glimpse/glimpse_person.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 36, "offset": 35}}, {"path": "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/templates/richie/glimpse/glimpse_person.html", "start": {"line": 19, "col": 5, "offset": 0}, "end": {"line": 19, "col": 16, "offset": 11}}, {"path": "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/templates/richie/glimpse/glimpse_person.html", "start": {"line": 37, "col": 1, "offset": 0}, "end": {"line": 38, "col": 19, "offset": 25}}]], "message": "Syntax error at line downloaded_repos/openfun_richie/src/richie/plugins/glimpse/templates/richie/glimpse/glimpse_person.html:1:\n `{% load thumbnail %}{% spaceless %}` was unexpected", "path": "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/templates/richie/glimpse/glimpse_person.html", "spans": [{"file": "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/templates/richie/glimpse/glimpse_person.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 36, "offset": 35}}, {"file": "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/templates/richie/glimpse/glimpse_person.html", "start": {"line": 19, "col": 5, "offset": 0}, "end": {"line": 19, "col": 16, "offset": 11}}, {"file": "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/templates/richie/glimpse/glimpse_person.html", "start": {"line": 37, "col": 1, "offset": 0}, "end": {"line": 38, "col": 19, "offset": 25}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/templates/richie/glimpse/glimpse_quote.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 27, "offset": 63}}, {"path": "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/templates/richie/glimpse/glimpse_quote.html", "start": {"line": 25, "col": 12, "offset": 0}, "end": {"line": 25, "col": 40, "offset": 28}}, {"path": "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/templates/richie/glimpse/glimpse_quote.html", "start": {"line": 40, "col": 1, "offset": 0}, "end": {"line": 44, "col": 19, "offset": 53}}]], "message": "Syntax error at line downloaded_repos/openfun_richie/src/richie/plugins/glimpse/templates/richie/glimpse/glimpse_quote.html:1:\n `{% load thumbnail %}{% spaceless %}\n\n{% if instance.get_link %}` was unexpected", "path": "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/templates/richie/glimpse/glimpse_quote.html", "spans": [{"file": "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/templates/richie/glimpse/glimpse_quote.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 27, "offset": 63}}, {"file": "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/templates/richie/glimpse/glimpse_quote.html", "start": {"line": 25, "col": 12, "offset": 0}, "end": {"line": 25, "col": 40, "offset": 28}}, {"file": "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/templates/richie/glimpse/glimpse_quote.html", "start": {"line": 40, "col": 1, "offset": 0}, "end": {"line": 44, "col": 19, "offset": 53}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openfun_richie/src/richie/plugins/html_sitemap/templates/richie/html_sitemap/html_sitemap_item.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 37, "offset": 125}}, {"path": "downloaded_repos/openfun_richie/src/richie/plugins/html_sitemap/templates/richie/html_sitemap/html_sitemap_item.html", "start": {"line": 5, "col": 43, "offset": 0}, "end": {"line": 7, "col": 12, "offset": 44}}]], "message": "Syntax error at line downloaded_repos/openfun_richie/src/richie/plugins/html_sitemap/templates/richie/html_sitemap/html_sitemap_item.html:1:\n `{% if annotated_pages %}\n  {% for page, nb_open, nb_close, is_leaf in annotated_pages %}\n    {% for _ in ''|center:nb_open %}` was unexpected", "path": "downloaded_repos/openfun_richie/src/richie/plugins/html_sitemap/templates/richie/html_sitemap/html_sitemap_item.html", "spans": [{"file": "downloaded_repos/openfun_richie/src/richie/plugins/html_sitemap/templates/richie/html_sitemap/html_sitemap_item.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 37, "offset": 125}}, {"file": "downloaded_repos/openfun_richie/src/richie/plugins/html_sitemap/templates/richie/html_sitemap/html_sitemap_item.html", "start": {"line": 5, "col": 43, "offset": 0}, "end": {"line": 7, "col": 12, "offset": 44}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openfun_richie/src/richie/plugins/large_banner/templates/richie/large_banner/compacted.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 48, "offset": 47}}, {"path": "downloaded_repos/openfun_richie/src/richie/plugins/large_banner/templates/richie/large_banner/compacted.html", "start": {"line": 11, "col": 78, "offset": 0}, "end": {"line": 11, "col": 85, "offset": 7}}]], "message": "Syntax error at line downloaded_repos/openfun_richie/src/richie/plugins/large_banner/templates/richie/large_banner/compacted.html:1:\n `{% load static i18n cms_tags rfc_5646_locale %}` was unexpected", "path": "downloaded_repos/openfun_richie/src/richie/plugins/large_banner/templates/richie/large_banner/compacted.html", "spans": [{"file": "downloaded_repos/openfun_richie/src/richie/plugins/large_banner/templates/richie/large_banner/compacted.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 48, "offset": 47}}, {"file": "downloaded_repos/openfun_richie/src/richie/plugins/large_banner/templates/richie/large_banner/compacted.html", "start": {"line": 11, "col": 78, "offset": 0}, "end": {"line": 11, "col": 85, "offset": 7}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/openfun_richie/src/richie/plugins/large_banner/templates/richie/large_banner/hero-intro.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 58, "offset": 57}}, {"path": "downloaded_repos/openfun_richie/src/richie/plugins/large_banner/templates/richie/large_banner/hero-intro.html", "start": {"line": 21, "col": 78, "offset": 0}, "end": {"line": 21, "col": 85, "offset": 7}}]], "message": "Syntax error at line downloaded_repos/openfun_richie/src/richie/plugins/large_banner/templates/richie/large_banner/hero-intro.html:1:\n `{% load static i18n cms_tags rfc_5646_locale thumbnail %}` was unexpected", "path": "downloaded_repos/openfun_richie/src/richie/plugins/large_banner/templates/richie/large_banner/hero-intro.html", "spans": [{"file": "downloaded_repos/openfun_richie/src/richie/plugins/large_banner/templates/richie/large_banner/hero-intro.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 58, "offset": 57}}, {"file": "downloaded_repos/openfun_richie/src/richie/plugins/large_banner/templates/richie/large_banner/hero-intro.html", "start": {"line": 21, "col": 78, "offset": 0}, "end": {"line": 21, "col": 85, "offset": 7}}]}], "paths": {"scanned": ["downloaded_repos/openfun_richie/.circleci/config.yml", "downloaded_repos/openfun_richie/.dockerignore", "downloaded_repos/openfun_richie/.github/ISSUE_TEMPLATE/Bug_report.md", "downloaded_repos/openfun_richie/.github/ISSUE_TEMPLATE/Feature_request.md", "downloaded_repos/openfun_richie/.github/ISSUE_TEMPLATE/Support_question.md", "downloaded_repos/openfun_richie/.github/ISSUE_TEMPLATE.md", "downloaded_repos/openfun_richie/.github/PULL_REQUEST_TEMPLATE.md", "downloaded_repos/openfun_richie/.gitignore", "downloaded_repos/openfun_richie/.gitlint", "downloaded_repos/openfun_richie/.prettierignore", "downloaded_repos/openfun_richie/.prettierrc", "downloaded_repos/openfun_richie/.pylintrc", "downloaded_repos/openfun_richie/CHANGELOG.md", "downloaded_repos/openfun_richie/CONTRIBUTING.md", "downloaded_repos/openfun_richie/Dockerfile", "downloaded_repos/openfun_richie/LICENSE", "downloaded_repos/openfun_richie/MANIFEST.in", "downloaded_repos/openfun_richie/Makefile", "downloaded_repos/openfun_richie/README.md", "downloaded_repos/openfun_richie/UPGRADE.md", "downloaded_repos/openfun_richie/bin/_config.sh", "downloaded_repos/openfun_richie/bin/compose", "downloaded_repos/openfun_richie/bin/exec", "downloaded_repos/openfun_richie/bin/lint-back-diff", "downloaded_repos/openfun_richie/bin/pylint", "downloaded_repos/openfun_richie/bin/pytest", "downloaded_repos/openfun_richie/bin/run", "downloaded_repos/openfun_richie/bin/setup-ssl", "downloaded_repos/openfun_richie/cookiecutter/cookiecutter.json", "downloaded_repos/openfun_richie/cookiecutter/hooks/pre_gen_project.sh", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/.circleci/src/@root.yml", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/.circleci/src/commands/generate-version-file.yml", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/.circleci/src/jobs/@backend.yml", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/.circleci/src/jobs/@docker.yml", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/.circleci/src/jobs/@frontend.yml", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/.circleci/src/jobs/@project.yml", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/.circleci/src/workflows/site-factory.yml.tpl", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/.circleci/src/workflows/site_jobs.yml.tpl", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/.circleci/src/workflows/site_no_change.yml.tpl", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/.cookiecutter_default.yml", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/.cookiecutter_replay/.gitkeep", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/.dockerignore", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/.gitignore", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/.prettierrc", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/Dockerfile", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/LICENSE", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/Makefile", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/README.md", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/aws/cloudfront.tf", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/aws/create_state_bucket/dynamodb.tf", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/aws/create_state_bucket/output.tf", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/aws/create_state_bucket/s3.tf", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/aws/create_state_bucket/state.tf", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/aws/create_state_bucket/variables.tf", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/aws/output.tf", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/aws/s3.tf", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/aws/state.tf", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/aws/variables.tf", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/bin/_config.sh", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/bin/activate", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/bin/ci", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/bin/manage", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/bin/pytest", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/bin/release", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/bin/state", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/bin/tag", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/bin/terraform", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/bin/upgrade", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/docker/files/etc/nginx/conf.d/default.conf", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/docker/files/usr/local/bin/entrypoint", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/docker/files/usr/local/etc/gunicorn/app.py", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/docker-compose-mysql.yml", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/docker-compose-postgresql.yml", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/docker-compose-sqlite.yml", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/docker-compose.yml", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/docs/aws.md", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/env.d/aws.dist", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/env.d/development.dist", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/env.d/mysql", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/env.d/postgresql", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/env.d/sqlite", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/cookiecutter.json", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/CHANGELOG.md", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/aws/config.tfvars", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/requirements/base.txt", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/requirements/dev.txt", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/src/backend/__init__.py", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/src/backend/base/static/richie/css/.keep", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/src/backend/locale/.gitkeep", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/src/backend/locale/ar_SA/LC_MESSAGES/django.po", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/src/backend/locale/es_ES/LC_MESSAGES/django.po", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/src/backend/locale/fr_CA/LC_MESSAGES/django.po", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/src/backend/locale/fr_FR/LC_MESSAGES/django.po", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/src/backend/locale/ko_KR/LC_MESSAGES/django.po", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/src/backend/locale/pt_PT/LC_MESSAGES/django.po", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/src/backend/locale/ru_RU/LC_MESSAGES/django.po", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/src/backend/locale/vi_VN/LC_MESSAGES/django.po", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/src/backend/manage.py", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/src/backend/pyproject.toml", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/src/backend/{{cookiecutter.site}}/__init__.py", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/src/backend/{{cookiecutter.site}}/settings.py", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/src/backend/{{cookiecutter.site}}/urls.py", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/src/backend/{{cookiecutter.site}}/wsgi.py", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/src/frontend/cunningham.cjs", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/src/frontend/i18n/locales/.gitkeep", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/src/frontend/i18n/overrides/.gitkeep", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/src/frontend/overrides.json", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/src/frontend/package.json", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/src/frontend/scss/_main.scss", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/src/frontend/scss/extras/colors/_palette.scss", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/src/frontend/scss/extras/colors/_theme.scss", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/src/frontend/scss/extras/settings/_variables.scss", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/src/frontend/scss/vendors/css/cunningham-tokens.css", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/src/frontend/scss/vendors/cunningham-tokens.scss", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/src/frontend/tsconfig.json", "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/src/frontend/yarn.lock", "downloaded_repos/openfun_richie/crowdin/config.yml", "downloaded_repos/openfun_richie/docker/files/etc/nginx/conf.d/default.conf", "downloaded_repos/openfun_richie/docker/files/etc/nginx/ssl/ssl.conf.tpl", "downloaded_repos/openfun_richie/docker/files/usr/local/bin/entrypoint", "downloaded_repos/openfun_richie/docker/files/usr/local/etc/gunicorn/richie.py", "downloaded_repos/openfun_richie/docker-compose-mysql.yml", "downloaded_repos/openfun_richie/docker-compose-postgresql.yml", "downloaded_repos/openfun_richie/docker-compose-sqlite.yml", "downloaded_repos/openfun_richie/docker-compose.yml", "downloaded_repos/openfun_richie/docs/accessibility-testing.md", "downloaded_repos/openfun_richie/docs/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/docs/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/docs/building-the-frontend.md", "downloaded_repos/openfun_richie/docs/contributing.md", "downloaded_repos/openfun_richie/docs/cookiecutter.md", "downloaded_repos/openfun_richie/docs/css-guidelines.md", "downloaded_repos/openfun_richie/docs/discover.md", "downloaded_repos/openfun_richie/docs/displaying-connection-status.md", "downloaded_repos/openfun_richie/docs/django-react-interop.md", "downloaded_repos/openfun_richie/docs/docker-development.md", "downloaded_repos/openfun_richie/docs/filters-customization.md", "downloaded_repos/openfun_richie/docs/frontend-overrides.md", "downloaded_repos/openfun_richie/docs/installation.md", "downloaded_repos/openfun_richie/docs/internationalization.md", "downloaded_repos/openfun_richie/docs/joanie-connection.md", "downloaded_repos/openfun_richie/docs/lms-backends.md", "downloaded_repos/openfun_richie/docs/lms-connection.md", "downloaded_repos/openfun_richie/docs/native-installation.md", "downloaded_repos/openfun_richie/docs/plugins-extensions/menuentry.md", "downloaded_repos/openfun_richie/docs/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/docs/tls-connection.md", "downloaded_repos/openfun_richie/docs/web-analytics.md", "downloaded_repos/openfun_richie/env.d/development/common.dist", "downloaded_repos/openfun_richie/env.d/development/crowdin.dist", "downloaded_repos/openfun_richie/env.d/development/dev-ssl.dist", "downloaded_repos/openfun_richie/env.d/development/dev.dist", "downloaded_repos/openfun_richie/env.d/development/localtunnel.dist", "downloaded_repos/openfun_richie/env.d/development/mysql", "downloaded_repos/openfun_richie/env.d/development/postgresql", "downloaded_repos/openfun_richie/env.d/development/sqlite", "downloaded_repos/openfun_richie/gitlint/gitlint_emoji.py", "downloaded_repos/openfun_richie/pyproject.toml", "downloaded_repos/openfun_richie/renovate.json", "downloaded_repos/openfun_richie/sandbox/__init__.py", "downloaded_repos/openfun_richie/sandbox/manage.py", "downloaded_repos/openfun_richie/sandbox/settings.py", "downloaded_repos/openfun_richie/sandbox/urls.py", "downloaded_repos/openfun_richie/sandbox/wsgi.py", "downloaded_repos/openfun_richie/setup.py", "downloaded_repos/openfun_richie/src/frontend/.eslintignore", "downloaded_repos/openfun_richie/src/frontend/.eslintrc.json", "downloaded_repos/openfun_richie/src/frontend/.nvmrc", "downloaded_repos/openfun_richie/src/frontend/.prettierignore", "downloaded_repos/openfun_richie/src/frontend/.storybook/__mocks__/utils/context.ts", "downloaded_repos/openfun_richie/src/frontend/.storybook/main.js", "downloaded_repos/openfun_richie/src/frontend/.storybook/preview-body.html", "downloaded_repos/openfun_richie/src/frontend/.storybook/preview-head.html", "downloaded_repos/openfun_richie/src/frontend/.storybook/preview.tsx", "downloaded_repos/openfun_richie/src/frontend/.storybook/public/course_cover_image.jpg", "downloaded_repos/openfun_richie/src/frontend/.storybook/public/course_icon.png", "downloaded_repos/openfun_richie/src/frontend/.storybook/public/organization_cover_image.png", "downloaded_repos/openfun_richie/src/frontend/babel.config.js", "downloaded_repos/openfun_richie/src/frontend/cunningham.cjs", "downloaded_repos/openfun_richie/src/frontend/i18n/compile-translations.js", "downloaded_repos/openfun_richie/src/frontend/i18n/locales/ar-SA.json", "downloaded_repos/openfun_richie/src/frontend/i18n/locales/es-ES.json", "downloaded_repos/openfun_richie/src/frontend/i18n/locales/fa-IR.json", "downloaded_repos/openfun_richie/src/frontend/i18n/locales/fr-CA.json", "downloaded_repos/openfun_richie/src/frontend/i18n/locales/fr-FR.json", "downloaded_repos/openfun_richie/src/frontend/i18n/locales/ko-KR.json", "downloaded_repos/openfun_richie/src/frontend/i18n/locales/pt-PT.json", "downloaded_repos/openfun_richie/src/frontend/i18n/locales/ru-RU.json", "downloaded_repos/openfun_richie/src/frontend/i18n/locales/vi-VN.json", "downloaded_repos/openfun_richie/src/frontend/jest/resolver.js", "downloaded_repos/openfun_richie/src/frontend/jest/setup.ts", "downloaded_repos/openfun_richie/src/frontend/jest.config.js", "downloaded_repos/openfun_richie/src/frontend/js/api/authentication.ts", "downloaded_repos/openfun_richie/src/frontend/js/api/configuration.ts", "downloaded_repos/openfun_richie/src/frontend/js/api/enrollment.ts", "downloaded_repos/openfun_richie/src/frontend/js/api/joanie.spec.ts", "downloaded_repos/openfun_richie/src/frontend/js/api/joanie.ts", "downloaded_repos/openfun_richie/src/frontend/js/api/lms/dummy.spec.ts", "downloaded_repos/openfun_richie/src/frontend/js/api/lms/dummy.ts", "downloaded_repos/openfun_richie/src/frontend/js/api/lms/index.spec.ts", "downloaded_repos/openfun_richie/src/frontend/js/api/lms/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/api/lms/joanie.spec.ts", "downloaded_repos/openfun_richie/src/frontend/js/api/lms/joanie.ts", "downloaded_repos/openfun_richie/src/frontend/js/api/lms/openedx-dogwood.spec.ts", "downloaded_repos/openfun_richie/src/frontend/js/api/lms/openedx-dogwood.ts", "downloaded_repos/openfun_richie/src/frontend/js/api/lms/openedx-fonzie.spec.ts", "downloaded_repos/openfun_richie/src/frontend/js/api/lms/openedx-fonzie.ts", "downloaded_repos/openfun_richie/src/frontend/js/api/lms/openedx-hawthorn.spec.ts", "downloaded_repos/openfun_richie/src/frontend/js/api/lms/openedx-hawthorn.ts", "downloaded_repos/openfun_richie/src/frontend/js/api/mocks/joanie/assets/course_cover_001.jpg", "downloaded_repos/openfun_richie/src/frontend/js/api/mocks/joanie/assets/course_icon_001.png", "downloaded_repos/openfun_richie/src/frontend/js/api/mocks/joanie/assets/organization_cover_001.jpg", "downloaded_repos/openfun_richie/src/frontend/js/api/mocks/joanie/assets/organization_cover_002.jpg", "downloaded_repos/openfun_richie/src/frontend/js/api/utils.ts", "downloaded_repos/openfun_richie/src/frontend/js/api/web-analytics/base.ts", "downloaded_repos/openfun_richie/src/frontend/js/api/web-analytics/google_tag.spec.ts", "downloaded_repos/openfun_richie/src/frontend/js/api/web-analytics/google_tag.ts", "downloaded_repos/openfun_richie/src/frontend/js/api/web-analytics/google_tag_manager.spec.ts", "downloaded_repos/openfun_richie/src/frontend/js/api/web-analytics/google_tag_manager.ts", "downloaded_repos/openfun_richie/src/frontend/js/api/web-analytics/google_universal_analytics.spec.ts", "downloaded_repos/openfun_richie/src/frontend/js/api/web-analytics/google_universal_analytics.ts", "downloaded_repos/openfun_richie/src/frontend/js/api/web-analytics/google_universal_analytics_and_tag_manager.spec.ts", "downloaded_repos/openfun_richie/src/frontend/js/api/web-analytics/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/api/web-analytics/no_provider.spec.ts", "downloaded_repos/openfun_richie/src/frontend/js/api/web-analytics/unknown_provider.spec.ts", "downloaded_repos/openfun_richie/src/frontend/js/components/Address/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/AddressesManagement/AddressForm/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/AddressesManagement/AddressForm/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/AddressesManagement/AddressForm/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/AddressesManagement/AddressForm/validationSchema.spec.ts", "downloaded_repos/openfun_richie/src/frontend/js/components/AddressesManagement/AddressForm/validationSchema.ts", "downloaded_repos/openfun_richie/src/frontend/js/components/AddressesManagement/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/components/AddressesManagement/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/AddressesManagement/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/AddressesManagement/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/Badge/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/Badge/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/Badge/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/Banner/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/Banner/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/Banner/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/ContractFrame/AbstractContractFrame.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/ContractFrame/AbstractContractFrame.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/ContractFrame/LearnerContractFrame.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/ContractFrame/LearnerContractFrame.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/ContractFrame/OrganizationContractFrame.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/ContractFrame/OrganizationContractFrame.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/ContractFrame/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/components/ContractFrame/iframe-manager.js", "downloaded_repos/openfun_richie/src/frontend/js/components/ContractFrame/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/components/ContractStatus/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/ContractStatus/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/CourseGlimpse/CourseGlimpseFooter.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/CourseGlimpse/CourseLink.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/CourseGlimpse/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/CourseGlimpse/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/CourseGlimpse/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/CourseGlimpse/utils.ts", "downloaded_repos/openfun_richie/src/frontend/js/components/CourseGlimpseList/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/CourseGlimpseList/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/CourseGlimpseList/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/CourseGlimpseList/utils.ts", "downloaded_repos/openfun_richie/src/frontend/js/components/CreditCardSelector/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/components/CreditCardSelector/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/CreditCardSelector/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/DjangoCMSTemplate/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/DjangoCMSTemplate/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/DownloadCertificateButton/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/DownloadContractButton/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/DownloadContractButton/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/EnrollmentDate/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/EnrollmentDate/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/Form/CountrySelectField.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/Form/Form/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/Form/Input/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/Form/Input/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/Form/Select/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/Form/Select/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/Form/ValidationErrors.ts", "downloaded_repos/openfun_richie/src/frontend/js/components/Form/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/components/Form/messages.ts", "downloaded_repos/openfun_richie/src/frontend/js/components/Form/test-utils.ts", "downloaded_repos/openfun_richie/src/frontend/js/components/Form/utils.spec.ts", "downloaded_repos/openfun_richie/src/frontend/js/components/Form/utils.ts", "downloaded_repos/openfun_richie/src/frontend/js/components/Icon/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/Icon/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/Icon/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/Modal/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/components/Modal/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/Modal/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/Modal/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/OpenEdxFullNameForm/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/OpenEdxFullNameForm/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/OpenEdxFullNameForm/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/Pagination/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/Pagination/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/PaymentInterfaces/Dummy.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/PaymentInterfaces/LyraPopIn.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/PaymentInterfaces/LyraPopIn.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/PaymentInterfaces/PayplugLightbox.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/PaymentInterfaces/__mocks__/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/PaymentInterfaces/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/PaymentInterfaces/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/PaymentInterfaces/types.ts", "downloaded_repos/openfun_richie/src/frontend/js/components/PaymentScheduleGrid/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/components/PaymentScheduleGrid/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/ProtectedRoute/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/ProtectedRoute/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/PurchaseButton/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/PurchaseButton/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/PurchaseButton/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/PurchaseButton/styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/components/RegisteredAddress/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/components/RegisteredAddress/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/RegisteredAddress/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/RegisteredAddress/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/AddressSelector/CreateAddressFormModal.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/AddressSelector/EditAddressFormModal.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/AddressSelector/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/AddressSelector/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/AddressSelector/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/CertificateSaleTunnel/CertificateProductPath.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/CertificateSaleTunnel/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/CredentialSaleTunnel/CredentialProductPath.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/CredentialSaleTunnel/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/GenericSaleTunnel.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/ProductPath/CourseRunsList.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/ProductPath/ProductPathCertificateDefinition.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/ProductPath/ProductPathInstructions.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/ProductPath/TargetCourseDetail.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/ProductPath/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/SaleTunnelInformation/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/SaleTunnelSavePaymentMethod/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/SaleTunnelSavePaymentMethod/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/SaleTunnelSuccess/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/Sponsors/SaleTunnelSponsors.scss", "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/Sponsors/SaleTunnelSponsors.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/SubscriptionButton/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/SubscriptionButton/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/WithdrawRightCheckbox/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/hooks/useTerms.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/index.credential.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/index.full-process.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/SearchInput/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/components/SearchInput/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/SearchInput/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/SignContractButton/index.omniscientOrders.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/SignContractButton/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/SignContractButton/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/Spinner/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/components/Spinner/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/Spinner/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/SuccessIcon/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/components/SuccessIcon/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/Tabs/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/components/Tabs/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/Tabs/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/TeacherDashboardCourseList/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/components/TeacherDashboardCourseList/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/TeacherDashboardCourseList/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/components/WarningIcon/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/components/WarningIcon/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/contexts/JoanieApiContext/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/contexts/JoanieApiContext/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/contexts/SessionContext/BaseSessionProvider.tsx", "downloaded_repos/openfun_richie/src/frontend/js/contexts/SessionContext/JoanieSessionProvider.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/contexts/SessionContext/JoanieSessionProvider.tsx", "downloaded_repos/openfun_richie/src/frontend/js/contexts/SessionContext/SessionContext.tsx", "downloaded_repos/openfun_richie/src/frontend/js/contexts/SessionContext/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/contexts/SessionContext/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/contexts/SessionContext/no-authentication.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useAddresses.ts", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useAddressesManagement.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useAsyncEffect.ts", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useBreadcrumbsPlaceholders.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useCache.ts", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useCertificates/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useContractAbilities/index.spec.ts", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useContractAbilities/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useContractArchive/index.download.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useContractArchive/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useContractArchive/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useContracts/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useCourseOrders/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useCourseProductUnion/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useCourseProductUnion/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useCourseProducts.ts", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useCourseRunOrder/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useCourseRunOrder/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useCourseRuns/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useCourseSearchParams/computeNewFilterValue.ts", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useCourseSearchParams/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useCourseSearchParams/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useCourses/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useCourses/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useCreditCards/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useCreditCards/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useCreditCardsManagement.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useDashboardAddressForm.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useDateFormat/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useDateFormat/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useDateRelative.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useDefaultOrganizationId/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useDefaultOrganizationId/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useDownloadCertificate/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useDownloadCertificate/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useEnrollments.ts", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useHistory/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useHistory/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useIntersectionObserver.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useJoanieUserAbilities/index.not.isJoanieEnabled.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useJoanieUserAbilities/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useJoanieUserAbilities/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useJoanieUserProfile.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useLearnerCoursesSearch/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useMatchMedia.ts", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useOffering/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useOpenEdxProfile/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useOpenEdxProfile/utils/index.spec.ts", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useOpenEdxProfile/utils/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useOrders/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useOrders/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useOrganizations/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/hooks/usePaymentSchedule.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/usePrevious.ts", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useProductOrder/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useProductOrder/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useQueryKeyInvalidateListener.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useResources/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useResources/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useResources/useResourcesOmniscient.ts", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useResources/useResourcesRoot.ts", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useStaticFilters/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useStaticFilters/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useStepManager/index.spec.ts", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useStepManager/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useTeacherCoursesSearch/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useTeacherPendingContractsCount/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useUnionResource/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useUnionResource/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useUnionResource/utils/fetchEntities.ts", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useUnionResource/utils/fetchEntity.ts", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useUnionResource/utils/hasIntegrity.spec.ts", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useUnionResource/utils/hasIntegrity.ts", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useUnionResource/utils/log.ts", "downloaded_repos/openfun_richie/src/frontend/js/hooks/useUnionResource/utils/syncIntegrityCount.ts", "downloaded_repos/openfun_richie/src/frontend/js/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/mockServiceWorker.js", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardAddressesManagement/DashboardAddressBox.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardAddressesManagement/DashboardCreateAddress.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardAddressesManagement/DashboardCreateAddress.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardAddressesManagement/DashboardCreateAddress.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardAddressesManagement/DashboardCreateAddressLoader.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardAddressesManagement/DashboardEditAddress.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardAddressesManagement/DashboardEditAddress.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardAddressesManagement/DashboardEditAddress.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardAddressesManagement/DashboardEditAddressLoader.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardAddressesManagement/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardAddressesManagement/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardAddressesManagement/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardAddressesManagement/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardCertificates/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardCertificates/components/CertificateList/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardCertificates/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardCertificates/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardContracts/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardContracts/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardContracts/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardCourses/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardCourses/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardCourses/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardCourses/useOrdersEnrollments.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardCreditCardsManagement/CreditCardBrandLogo.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardCreditCardsManagement/CreditCardBrandLogo.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardCreditCardsManagement/CreditCardBrandLogo.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardCreditCardsManagement/DashboardCreditCardBox.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardCreditCardsManagement/DashboardEditCreditCard.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardCreditCardsManagement/DashboardEditCreditCard.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardCreditCardsManagement/DashboardEditCreditCard.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardCreditCardsManagement/DashboardEditCreditCardLoader.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardCreditCardsManagement/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardCreditCardsManagement/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardCreditCardsManagement/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardCreditCardsManagement/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardOpenEdxProfile/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardOpenEdxProfile/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardOpenEdxProfile/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardOrderLayout/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardOrderLayout/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardOrderLayout/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardPageNotFound/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardPreferences/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/pages/DashboardPreferences/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardContractsLayout/TeacherDashboardContracts/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardContractsLayout/TeacherDashboardContracts/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardContractsLayout/TeacherDashboardCourseContractsLayout/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardContractsLayout/TeacherDashboardOrganizationContractsLayout/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardContractsLayout/components/BulkDownloadContractButton/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardContractsLayout/components/BulkDownloadContractButton/index.timer.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardContractsLayout/components/BulkDownloadContractButton/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardContractsLayout/components/ContractActionsBar/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardContractsLayout/components/ContractActionsBar/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardContractsLayout/components/ContractFiltersBar/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardContractsLayout/components/ContractFiltersBar/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardContractsLayout/components/SignOrganizationContractButton/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardContractsLayout/components/SignOrganizationContractButton/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardContractsLayout/hooks/useCheckContractArchiveExists/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardContractsLayout/hooks/useCheckContractArchiveExists/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardContractsLayout/hooks/useDownloadContractArchive/contractArchiveLocalStorage.spec.ts", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardContractsLayout/hooks/useDownloadContractArchive/contractArchiveLocalStorage.ts", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardContractsLayout/hooks/useDownloadContractArchive/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardContractsLayout/hooks/useDownloadContractArchive/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardContractsLayout/hooks/useHasContractToDownload/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardContractsLayout/hooks/useHasContractToDownload/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardContractsLayout/hooks/useTeacherContractFilters/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardContractsLayout/hooks/useTeacherContractFilters/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardContractsLayout/hooks/useTeacherContractsToSign.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardContractsLayout/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardContractsLayout/styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardCourseLearnersLayout/components/CourseLearnerDataGrid/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardCourseLearnersLayout/components/CourseLearnerDataGrid/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardCourseLearnersLayout/components/CourseLearnersFiltersBar/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardCourseLearnersLayout/components/CourseLearnersFiltersBar/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardCourseLearnersLayout/hooks/useCourseLearnersFilters/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardCourseLearnersLayout/hooks/useCourseLearnersFilters/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardCourseLearnersLayout/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardCourseLearnersLayout/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardCourseLoader/CourseRunList/CourseRunListCell/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardCourseLoader/CourseRunList/CourseRunListCell/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardCourseLoader/CourseRunList/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardCourseLoader/CourseRunList/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardCourseLoader/CourseRunList/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardCourseLoader/CourseRunList/utils.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardCourseLoader/CourseRunList/utils.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardCourseLoader/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardCoursesLoader/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardCoursesLoader/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardOrganizationCourseLoader/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardOrganizationCourseLoader/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardTraining/TeacherDashboardTrainingLoader.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardTraining/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardTraining/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/pages/TeacherDashboardTraining/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/settings/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/settings/settings.dev.dist.ts", "downloaded_repos/openfun_richie/src/frontend/js/settings/settings.prod.ts", "downloaded_repos/openfun_richie/src/frontend/js/settings/settings.test.ts", "downloaded_repos/openfun_richie/src/frontend/js/translations/ar-SA.json", "downloaded_repos/openfun_richie/src/frontend/js/translations/es-ES.json", "downloaded_repos/openfun_richie/src/frontend/js/translations/fa-IR.json", "downloaded_repos/openfun_richie/src/frontend/js/translations/fr-CA.json", "downloaded_repos/openfun_richie/src/frontend/js/translations/fr-FR.json", "downloaded_repos/openfun_richie/src/frontend/js/translations/ko-KR.json", "downloaded_repos/openfun_richie/src/frontend/js/translations/pt-PT.json", "downloaded_repos/openfun_richie/src/frontend/js/translations/ru-RU.json", "downloaded_repos/openfun_richie/src/frontend/js/translations/vi-VN.json", "downloaded_repos/openfun_richie/src/frontend/js/types/Course.ts", "downloaded_repos/openfun_richie/src/frontend/js/types/<PERSON><PERSON>.ts", "downloaded_repos/openfun_richie/src/frontend/js/types/Resource.ts", "downloaded_repos/openfun_richie/src/frontend/js/types/Suggestion.ts", "downloaded_repos/openfun_richie/src/frontend/js/types/User.ts", "downloaded_repos/openfun_richie/src/frontend/js/types/api.ts", "downloaded_repos/openfun_richie/src/frontend/js/types/commonDataProps.ts", "downloaded_repos/openfun_richie/src/frontend/js/types/filters.ts", "downloaded_repos/openfun_richie/src/frontend/js/types/globals.d.ts", "downloaded_repos/openfun_richie/src/frontend/js/types/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/types/libs/iframe-resizer/index.d.ts", "downloaded_repos/openfun_richie/src/frontend/js/types/libs/intl-pluralrules/index.d.ts", "downloaded_repos/openfun_richie/src/frontend/js/types/libs/mdn-polyfills/index.d.ts", "downloaded_repos/openfun_richie/src/frontend/js/types/libs/whatwg-fetch/index.d.ts", "downloaded_repos/openfun_richie/src/frontend/js/types/openEdx.ts", "downloaded_repos/openfun_richie/src/frontend/js/types/payments/lyra.d.ts", "downloaded_repos/openfun_richie/src/frontend/js/types/payments/payplug.d.ts", "downloaded_repos/openfun_richie/src/frontend/js/types/utils.ts", "downloaded_repos/openfun_richie/src/frontend/js/types/web-analytics/google_analytics.d.ts", "downloaded_repos/openfun_richie/src/frontend/js/types/web-analytics/google_tag_manager.d.ts", "downloaded_repos/openfun_richie/src/frontend/js/types/web-analytics/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/AbilitiesHelper/contractAbilities.spec.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/AbilitiesHelper/contractAbilities.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/AbilitiesHelper/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/AbilitiesHelper/joanieUserProfileAbilities.spec.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/AbilitiesHelper/joanieUserProfileAbilities.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/AbilitiesHelper/types.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/CertificateHelper/index.spec.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/CertificateHelper/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/ContractHelper/index.spec.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/ContractHelper/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/CourseRunHelper/index.spec.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/CourseRunHelper/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/CourseRuns/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/utils/CourseRuns/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/CoursesHelper/index.spec.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/CoursesHelper/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/CreditCardHelper/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/utils/CreditCardHelper/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/utils/IntlHelper/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/utils/IntlHelper/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/JoinAnd/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/utils/JoinAnd/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/ObjectHelper/index.spec.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/ObjectHelper/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/OrderHelper/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/PaymentScheduleHelper/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/utils/PaymentScheduleHelper/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/ProductHelper/index.spec.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/ProductHelper/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/StorybookHelper/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/utils/StringHelper/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/utils/StringHelper/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/UserHelper/index.spec.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/UserHelper/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/base64Parser.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/context.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/cunningham-tokens.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/download.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/errors/ErrorBoundary.tsx", "downloaded_repos/openfun_richie/src/frontend/js/utils/errors/HttpError.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/errors/handle.spec.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/errors/handle.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/utils/indirection/window.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/mptt/index.spec.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/mptt/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/react-query/createQueryClient.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/react-query/useLocalizedQueryKey.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/react-query/useSessionKey.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/react-query/useSessionMutation/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/utils/react-query/useSessionMutation/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/react-query/useSessionQuery/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/utils/react-query/useSessionQuery/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/relativeDate.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/resolveAll.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/search/getSuggestionsSection/index.spec.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/search/getSuggestionsSection/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/utils/search/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/utils/sleep.ts", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardAvatar/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardAvatar/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardAvatar/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardAvatar/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardBox/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardBox/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardBox/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardBox/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardBreadcrumbs/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardBreadcrumbs/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardBreadcrumbs/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardCard/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardCard/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardCard/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardCard/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Certificate/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Certificate/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Certificate/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Certificate/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/CertificateStatus/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/CertificateStatus/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Contract/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Contract/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Contract/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Contract/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/CourseEnrolling/hooks/useCourseRunPeriodMessage.ts", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/CourseEnrolling/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/CourseEnrolling/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/CourseEnrolling/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/DashboardSubItem.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/DashboardSubItemsList.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Enrollment/DashboardItemEnrollment.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Enrollment/DashboardItemEnrollment.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Enrollment/DashboardItemEnrollment.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Enrollment/ProductCertificateFooter/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Enrollment/ProductCertificateFooter/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Order/CertificateItem/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Order/ContractItem/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Order/DashboardItemNotResumable.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Order/DashboardItemOrder.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Order/DashboardItemOrder.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Order/DashboardItemOrderContract.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Order/DashboardItemOrderContract.useUnionResource.cache.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Order/DashboardItemOrderReadonly.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Order/DashboardItemOrderWritable.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Order/DashboardItemSavePaymentMethod.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Order/Installment/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Order/OrderPaymentDetailsModal/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Order/OrderPaymentDetailsModal/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Order/OrderPaymentRetryModal/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Order/OrderStateLearnerMessage/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Order/OrderStateLearnerMessage/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Order/OrderStateMessage/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Order/OrderStateTeacherMessage/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Order/OrderStateTeacherMessage/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Order/OrganizationBlock/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Order/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/stories.mock.ts", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardLayout/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardLayout/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardLayoutRoute/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardLayoutRoute/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardListAvatar/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardListAvatar/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardListAvatar/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardOrderLoader/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardOrderLoader/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardSidebar/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardSidebar/components/ContractNavLink/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardSidebar/components/ContractNavLink/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardSidebar/components/MenuNavLink/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardSidebar/components/MenuNavLink/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardSidebar/components/NavigationSelect.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardSidebar/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardSidebar/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardSidebar/utils.ts", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardTest/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/FilterOrganization/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/FiltersBar/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/LearnerDashboardSidebar/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/LearnerDashboardSidebar/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/NavigateWithParams/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/NavigateWithParams/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/ProtectedOutlet/AuthenticatedOutlet.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/ProtectedOutlet/AuthenticatedOutlet.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/ProtectedOutlet/ProtectedOutlet.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/ProtectedOutlet/ProtectedOutlet.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/ProtectedOutlet/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/RouteInfo/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/RouterButton/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/RouterButton/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/SearchBar/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/SearchBar/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/SearchBar/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/SearchResultsCount/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/SearchResultsCount/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/Signature/DummyContractPlaceholder.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/Signature/SignatureDummy.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/Signature/SignatureLexPersona.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/TeacherDashboardCourseSidebar/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/TeacherDashboardCourseSidebar/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/TeacherDashboardCourseSidebar/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/TeacherDashboardCourseSidebar/utils.ts", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/TeacherDashboardOrganizationSidebar/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/TeacherDashboardOrganizationSidebar/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/TeacherDashboardOrganizationSidebar/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/TeacherDashboardProfileSidebar/components/OrganizationLinks/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/TeacherDashboardProfileSidebar/components/OrganizationLinks/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/TeacherDashboardProfileSidebar/components/OrganizationLinks/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/TeacherDashboardProfileSidebar/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/TeacherDashboardProfileSidebar/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/TeacherDashboardProfileSidebar/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/contexts/DashboardBreadcrumbsContext.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/hooks/useDashboardRouter/getDashboardBasename.ts", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/hooks/useDashboardRouter/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/hooks/useDashboardRouter/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/hooks/useEnroll/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/hooks/useEnroll/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/hooks/useRouteInfo/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/hooks/useRouteInfo/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/utils/dashboardRoutes.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/utils/learnerRoutes.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/utils/learnerRoutesPaths.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/utils/teacherDashboardPaths.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/utils/teacherRoutes.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/LanguageSelector/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/LanguageSelector/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/LtiConsumer/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/widgets/LtiConsumer/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/LtiConsumer/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/LtiConsumer/types/LtiConsumer.ts", "downloaded_repos/openfun_richie/src/frontend/js/widgets/RootSearchSuggestField/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/RootSearchSuggestField/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Search/components/FiltersPaneCloseButton.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Search/components/PaginateCourseSearch/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Search/components/PaginateCourseSearch/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Search/components/SearchFilterGroup/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Search/components/SearchFilterGroup/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Search/components/SearchFilterGroup/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Search/components/SearchFilterGroupModal/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Search/components/SearchFilterGroupModal/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Search/components/SearchFilterGroupModal/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Search/components/SearchFilterValueLeaf/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Search/components/SearchFilterValueLeaf/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Search/components/SearchFilterValueParent/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Search/components/SearchFilterValueParent/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Search/components/SearchFilterValueParent/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Search/components/SearchFiltersPane/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Search/components/SearchFiltersPane/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Search/components/SearchFiltersPane/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Search/hooks/useCourseSearch/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Search/hooks/useCourseSearch/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Search/hooks/useFilterValue/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Search/hooks/useFilterValue/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Search/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Search/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Search/types/api.ts", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Search/utils/getResourceList/index.spec.ts", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Search/utils/getResourceList/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SearchSuggestField/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SearchSuggestField/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Slider/components/Slide.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Slider/components/SlidePanel.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Slider/components/Slideshow.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Slider/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Slider/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Slider/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/Slider/types/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseProductItem/CourseProductItemFooter/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseProductItem/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseProductItem/components/CourseProductCertificateItem/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseProductItem/components/CourseProductCertificateItem/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseProductItem/components/CourseProductCertificateItem/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseProductItem/components/CourseProductCertificateItem/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseProductItem/components/CourseProductCourseRuns/CourseRunList.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseProductItem/components/CourseProductCourseRuns/CourseRunSection.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseProductItem/components/CourseProductCourseRuns/EnrollableCourseRunList.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseProductItem/components/CourseProductCourseRuns/EnrolledCourseRun.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseProductItem/components/CourseProductCourseRuns/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseProductItem/components/CourseProductCourseRuns/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseProductItem/components/CourseProductCourseRuns/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseProductItem/components/CourseRunItem/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseProductItem/components/CourseRunItem/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseProductItem/components/CourseRunItem/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseProductItem/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseProductItem/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseProductItem/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseRunEnrollment/CourseRunUnenrollmentButton/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseRunEnrollment/CourseRunUnenrollmentButton/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseRunEnrollment/CourseRunUnenrollmentButton/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseRunEnrollment/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseRunEnrollment/index.joanie.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseRunEnrollment/index.openedx.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseRunEnrollment/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseRunItem/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseRunItem/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseRunItemWithEnrollment/index.product.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseRunItemWithEnrollment/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseRunItemWithEnrollment/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseWishButton/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseWishButton/hooks/useCourseWish/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseWishButton/hooks/useCourseWish/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseWishButton/index.login.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseWishButton/index.logout.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseWishButton/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/SyllabusAsideList/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/SyllabusCourseRun/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/SyllabusCourseRun/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/SyllabusCourseRunCompacted/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/SyllabusSimpleCourseRunsList/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/SyllabusSimpleCourseRunsList/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/hooks/useCourseEnrollment/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/hooks/useCourseEnrollment/index.ts", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/UserLogin/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/widgets/UserLogin/components/UserMenu/DesktopUserMenu.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/UserLogin/components/UserMenu/MobileUserMenu.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/UserLogin/components/UserMenu/_styles.scss", "downloaded_repos/openfun_richie/src/frontend/js/widgets/UserLogin/components/UserMenu/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/UserLogin/components/UserMenu/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/UserLogin/index.not.isJoanieEnabled.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/UserLogin/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/UserLogin/index.stories.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/UserLogin/index.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/index.spec.tsx", "downloaded_repos/openfun_richie/src/frontend/js/widgets/index.tsx", "downloaded_repos/openfun_richie/src/frontend/mocks/browser.ts", "downloaded_repos/openfun_richie/src/frontend/mocks/handlers/contracts.ts", "downloaded_repos/openfun_richie/src/frontend/mocks/handlers.ts", "downloaded_repos/openfun_richie/src/frontend/package.json", "downloaded_repos/openfun_richie/src/frontend/public-path.js", "downloaded_repos/openfun_richie/src/frontend/scss/_main.scss", "downloaded_repos/openfun_richie/src/frontend/scss/colors/_gradients.scss", "downloaded_repos/openfun_richie/src/frontend/scss/colors/_palette.scss", "downloaded_repos/openfun_richie/src/frontend/scss/colors/_schemes.scss", "downloaded_repos/openfun_richie/src/frontend/scss/colors/_theme.scss", "downloaded_repos/openfun_richie/src/frontend/scss/components/_content.scss", "downloaded_repos/openfun_richie/src/frontend/scss/components/_error.scss", "downloaded_repos/openfun_richie/src/frontend/scss/components/_footer.scss", "downloaded_repos/openfun_richie/src/frontend/scss/components/_header.scss", "downloaded_repos/openfun_richie/src/frontend/scss/components/_index.scss", "downloaded_repos/openfun_richie/src/frontend/scss/components/_styleguide.scss", "downloaded_repos/openfun_richie/src/frontend/scss/components/_subheader.scss", "downloaded_repos/openfun_richie/src/frontend/scss/components/templates/courses/cms/_blogpost_detail.scss", "downloaded_repos/openfun_richie/src/frontend/scss/components/templates/courses/cms/_blogpost_list.scss", "downloaded_repos/openfun_richie/src/frontend/scss/components/templates/courses/cms/_category_detail.scss", "downloaded_repos/openfun_richie/src/frontend/scss/components/templates/courses/cms/_category_list.scss", "downloaded_repos/openfun_richie/src/frontend/scss/components/templates/courses/cms/_course_detail.scss", "downloaded_repos/openfun_richie/src/frontend/scss/components/templates/courses/cms/_homepage.scss", "downloaded_repos/openfun_richie/src/frontend/scss/components/templates/courses/cms/_organization_detail.scss", "downloaded_repos/openfun_richie/src/frontend/scss/components/templates/courses/cms/_organization_list.scss", "downloaded_repos/openfun_richie/src/frontend/scss/components/templates/courses/cms/_person_detail.scss", "downloaded_repos/openfun_richie/src/frontend/scss/components/templates/courses/cms/_person_list.scss", "downloaded_repos/openfun_richie/src/frontend/scss/components/templates/courses/cms/_program_detail.scss", "downloaded_repos/openfun_richie/src/frontend/scss/components/templates/courses/cms/_program_list.scss", "downloaded_repos/openfun_richie/src/frontend/scss/components/templates/courses/plugins/_category_plugin.scss", "downloaded_repos/openfun_richie/src/frontend/scss/components/templates/courses/plugins/_licence_plugin.scss", "downloaded_repos/openfun_richie/src/frontend/scss/components/templates/richie/_multiple-columns.scss", "downloaded_repos/openfun_richie/src/frontend/scss/components/templates/richie/_single-column.scss", "downloaded_repos/openfun_richie/src/frontend/scss/components/templates/richie/glimpse/_glimpse.scss", "downloaded_repos/openfun_richie/src/frontend/scss/components/templates/richie/large_banner/_compacted_banner.scss", "downloaded_repos/openfun_richie/src/frontend/scss/components/templates/richie/large_banner/_large_banner.scss", "downloaded_repos/openfun_richie/src/frontend/scss/components/templates/richie/nesteditem/_nesteditem.scss", "downloaded_repos/openfun_richie/src/frontend/scss/components/templates/richie/section/_section.scss", "downloaded_repos/openfun_richie/src/frontend/scss/components/templates/richie/simpletext/_simpletext.scss", "downloaded_repos/openfun_richie/src/frontend/scss/components/templates/richie/slider/_slider.scss", "downloaded_repos/openfun_richie/src/frontend/scss/components/templates/search/_search.scss", "downloaded_repos/openfun_richie/src/frontend/scss/generic/_accessibility.scss", "downloaded_repos/openfun_richie/src/frontend/scss/generic/_background.scss", "downloaded_repos/openfun_richie/src/frontend/scss/generic/_icons.scss", "downloaded_repos/openfun_richie/src/frontend/scss/generic/_images.scss", "downloaded_repos/openfun_richie/src/frontend/scss/generic/_index.scss", "downloaded_repos/openfun_richie/src/frontend/scss/generic/_type.scss", "downloaded_repos/openfun_richie/src/frontend/scss/objects/_banner.scss", "downloaded_repos/openfun_richie/src/frontend/scss/objects/_blogpost_glimpses.scss", "downloaded_repos/openfun_richie/src/frontend/scss/objects/_breadcrumbs.scss", "downloaded_repos/openfun_richie/src/frontend/scss/objects/_buttons.scss", "downloaded_repos/openfun_richie/src/frontend/scss/objects/_category_glimpses.scss", "downloaded_repos/openfun_richie/src/frontend/scss/objects/_characteristics.scss", "downloaded_repos/openfun_richie/src/frontend/scss/objects/_course_glimpses.scss", "downloaded_repos/openfun_richie/src/frontend/scss/objects/_dashboard.scss", "downloaded_repos/openfun_richie/src/frontend/scss/objects/_form.scss", "downloaded_repos/openfun_richie/src/frontend/scss/objects/_index.scss", "downloaded_repos/openfun_richie/src/frontend/scss/objects/_list.scss", "downloaded_repos/openfun_richie/src/frontend/scss/objects/_organization_glimpses.scss", "downloaded_repos/openfun_richie/src/frontend/scss/objects/_pagination.scss", "downloaded_repos/openfun_richie/src/frontend/scss/objects/_person_glimpses.scss", "downloaded_repos/openfun_richie/src/frontend/scss/objects/_program_glimpses.scss", "downloaded_repos/openfun_richie/src/frontend/scss/objects/_react-autosuggest.scss", "downloaded_repos/openfun_richie/src/frontend/scss/objects/_search-filter-value.scss", "downloaded_repos/openfun_richie/src/frontend/scss/objects/_selector.scss", "downloaded_repos/openfun_richie/src/frontend/scss/objects/_social-networks.scss", "downloaded_repos/openfun_richie/src/frontend/scss/settings/_bootstrap.scss", "downloaded_repos/openfun_richie/src/frontend/scss/settings/_fonts.scss", "downloaded_repos/openfun_richie/src/frontend/scss/settings/_variables.scss", "downloaded_repos/openfun_richie/src/frontend/scss/tools/_accordion.scss", "downloaded_repos/openfun_richie/src/frontend/scss/tools/_background.scss", "downloaded_repos/openfun_richie/src/frontend/scss/tools/_buttons.scss", "downloaded_repos/openfun_richie/src/frontend/scss/tools/_cards.scss", "downloaded_repos/openfun_richie/src/frontend/scss/tools/_colors.scss", "downloaded_repos/openfun_richie/src/frontend/scss/tools/_content.scss", "downloaded_repos/openfun_richie/src/frontend/scss/tools/_detail.scss", "downloaded_repos/openfun_richie/src/frontend/scss/tools/_empty.scss", "downloaded_repos/openfun_richie/src/frontend/scss/tools/_features.scss", "downloaded_repos/openfun_richie/src/frontend/scss/tools/_flexbox.scss", "downloaded_repos/openfun_richie/src/frontend/scss/tools/_generic.scss", "downloaded_repos/openfun_richie/src/frontend/scss/tools/_grids.scss", "downloaded_repos/openfun_richie/src/frontend/scss/tools/_hero.scss", "downloaded_repos/openfun_richie/src/frontend/scss/tools/_index.scss", "downloaded_repos/openfun_richie/src/frontend/scss/tools/_keyframes.scss", "downloaded_repos/openfun_richie/src/frontend/scss/tools/_list-group.scss", "downloaded_repos/openfun_richie/src/frontend/scss/tools/_rem.scss", "downloaded_repos/openfun_richie/src/frontend/scss/tools/_shapes.scss", "downloaded_repos/openfun_richie/src/frontend/scss/tools/_spacing.scss", "downloaded_repos/openfun_richie/src/frontend/scss/tools/_utils.scss", "downloaded_repos/openfun_richie/src/frontend/scss/trumps/_bootstrap.scss", "downloaded_repos/openfun_richie/src/frontend/scss/trumps/_cms.scss", "downloaded_repos/openfun_richie/src/frontend/scss/trumps/_ie11-fixes.scss", "downloaded_repos/openfun_richie/src/frontend/scss/vendors/css/cunningham-tokens.css", "downloaded_repos/openfun_richie/src/frontend/scss/vendors/cunningham-tokens.scss", "downloaded_repos/openfun_richie/src/frontend/stories/Introduction.mdx", "downloaded_repos/openfun_richie/src/frontend/tsconfig.json", "downloaded_repos/openfun_richie/src/frontend/webpack.config.js", "downloaded_repos/openfun_richie/src/frontend/yarn.lock", "downloaded_repos/openfun_richie/src/richie/__init__.py", "downloaded_repos/openfun_richie/src/richie/apps/__init__.py", "downloaded_repos/openfun_richie/src/richie/apps/core/__init__.py", "downloaded_repos/openfun_richie/src/richie/apps/core/admin.py", "downloaded_repos/openfun_richie/src/richie/apps/core/cache.py", "downloaded_repos/openfun_richie/src/richie/apps/core/context_processors.py", "downloaded_repos/openfun_richie/src/richie/apps/core/defaults.py", "downloaded_repos/openfun_richie/src/richie/apps/core/factories.py", "downloaded_repos/openfun_richie/src/richie/apps/core/fields/__init__.py", "downloaded_repos/openfun_richie/src/richie/apps/core/fields/duration.py", "downloaded_repos/openfun_richie/src/richie/apps/core/fields/effort.py", "downloaded_repos/openfun_richie/src/richie/apps/core/fields/multiselect.py", "downloaded_repos/openfun_richie/src/richie/apps/core/helpers.py", "downloaded_repos/openfun_richie/src/richie/apps/core/models.py", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/favicon/android-chrome-96x96.png", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/favicon/apple-touch-icon.png", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/favicon/browserconfig.xml", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/favicon/favicon-16x16.png", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/favicon/favicon-32x32.png", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/favicon/favicon.ico", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/favicon/mstile-150x150.png", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/favicon/safari-pinned-tab.svg", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/favicon/site.webmanifest", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/fonts/hind/hind-regular.woff", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/fonts/hind/hind-regular.woff2", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/fonts/hind/hind-semibold.woff", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/fonts/hind/hind-semibold.woff2", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/fonts/montserrat/montserrat-bold.woff", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/fonts/montserrat/montserrat-bold.woff2", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/fonts/montserrat/montserrat-extrabold.woff", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/fonts/montserrat/montserrat-extrabold.woff2", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/fonts/montserrat/montserrat-light.woff", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/fonts/montserrat/montserrat-light.woff2", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/fonts/montserrat/montserrat-medium.woff", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/fonts/montserrat/montserrat-medium.woff2", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/images/components/DashboardCreditCardsManagement/logo_cb.svg", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/images/components/DashboardCreditCardsManagement/logo_maestro.svg", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/images/components/DashboardCreditCardsManagement/logo_mastercard.svg", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/images/components/DashboardCreditCardsManagement/logo_visa.svg", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/images/components/add-outline.svg", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/images/components/arc-white.svg", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/images/components/checkmark.svg", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/images/components/clouds.jpg", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/images/components/curve-white.svg", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/images/components/minus-outline.svg", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/images/components/wave-dark.svg", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/images/components/wave-white.svg", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/images/components/waves.svg", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/images/empty/organization_banner.jpg", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/images/empty/organization_logo.png", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/images/empty/person_portrait.png", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/images/logo-alt.png", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/images/logo.png", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/images/prototypes/132x80.png", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/images/prototypes/135x135.png", "downloaded_repos/openfun_richie/src/richie/apps/core/static/richie/images/prototypes/330x200.png", "downloaded_repos/openfun_richie/src/richie/apps/core/storage.py", "downloaded_repos/openfun_richie/src/richie/apps/core/templates/cms/plugins/text.html", "downloaded_repos/openfun_richie/src/richie/apps/core/templates/djangocms_link/button-caesura/link.html", "downloaded_repos/openfun_richie/src/richie/apps/core/templates/djangocms_video/default/video_player.html", "downloaded_repos/openfun_richie/src/richie/apps/core/templates/djangocms_video/full-width/video_player.html", "downloaded_repos/openfun_richie/src/richie/apps/core/templates/menu/breadcrumb_item.html", "downloaded_repos/openfun_richie/src/richie/apps/core/templates/menu/breadcrumbs.html", "downloaded_repos/openfun_richie/src/richie/apps/core/templates/menu/header_menu.html", "downloaded_repos/openfun_richie/src/richie/apps/core/templates/menu/language_menu.html", "downloaded_repos/openfun_richie/src/richie/apps/core/templates/menu/menu.html", "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/base.html", "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/child_pages_list.html", "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/dashboard.html", "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/error.html", "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/forms/widgets/composite_widget.html", "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/fullwidth.html", "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/homepage.html", "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/hreflang.html", "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/icons.html", "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/multiple_columns.html", "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/pagination.html", "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/pagination_inner.html", "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/robots.html", "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/single_column.html", "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/styleguide/fragment_inline_markup.html", "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/styleguide/index.html", "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/three_columns_33.html", "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/two_columns_25_75.html", "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/two_columns_50.html", "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/two_columns_75_25.html", "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/web_analytics.html", "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/web_analytics_body_begin.html", "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/blogpost-badges.html", "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/course-badges.html", "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/footer-badges.html", "downloaded_repos/openfun_richie/src/richie/apps/core/templatetags/__init__.py", "downloaded_repos/openfun_richie/src/richie/apps/core/templatetags/feature_flags.py", "downloaded_repos/openfun_richie/src/richie/apps/core/templatetags/full_static_tags.py", "downloaded_repos/openfun_richie/src/richie/apps/core/templatetags/joanie.py", "downloaded_repos/openfun_richie/src/richie/apps/core/templatetags/language_name.py", "downloaded_repos/openfun_richie/src/richie/apps/core/templatetags/rfc_5646_locale.py", "downloaded_repos/openfun_richie/src/richie/apps/core/utils.py", "downloaded_repos/openfun_richie/src/richie/apps/core/views/__init__.py", "downloaded_repos/openfun_richie/src/richie/apps/core/views/error.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/__init__.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/admin.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/api.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/cms_menus.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/cms_plugins.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/cms_toolbars.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/cms_wizards.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/defaults.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/exceptions.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/factories.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/fields.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/forms.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/helpers.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/lms/__init__.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/lms/base.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/lms/edx.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/lms/joanie.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/management/__init__.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/management/commands/__init__.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/management/commands/migrate_course_run_resource_link.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/management/commands/richie_init.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0001_initial.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0002_auto_20190429_0901.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0003_auto_20190612_1743.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0004_auto_20190619_1630.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0005_auto_20190717_0827.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0006_add_program.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0007_auto_20190930_2245.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0008_auto_20191001_1212.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0009_auto_20191014_1801.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0010_auto_footer_to_static_placeholder.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0011_deprecate_untranslated_licence_fields.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0012_add_translation_model_for_licence_fields.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0013_migrate_data_translated_licence_fields.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0014_auto_20200309_2343.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0015_add_category_plugin_variant.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0016_auto_20200417_1237.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0017_auto_20200827_1011.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0018_auto_20201102_1912.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0019_auto_20201117_1004.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0020_auto_20201118_1153.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0021_auto_20201202_1146.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0022_auto_20201202_convert_course_runs_to_simple_models.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0023_auto_20201202_1146.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0024_auto_20201216_0740.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0025_auto_20201216_0917.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0026_courserun_sync_mode.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0027_auto_20210119_1411.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0028_auto_20210126_2200.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0029_auto_20210225_1429.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0030_auto_20210225_1435.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0031_auto_20210811_1234.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0032_auto_20211004_1733.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0033_auto_20211207_2251.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0034_auto_20230817_1736.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0035_add_menuentry.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0036_courserun_certificate_offer_and_more.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0037_alter_blogpostpluginmodel_cmsplugin_ptr_and_more.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0038_alter_mainmenuentry_menu_color.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0039_program_duration_program_effort_program_price.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/0040_courserun_certificate_discount_and_more.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/migrations/__init__.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/models/__init__.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/models/blog.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/models/category.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/models/course.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/models/menuentry.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/models/organization.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/models/person.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/models/program.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/models/role.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/serializers.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/settings/__init__.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/settings/mixins.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/static/richie/images/catalog_visibility/course_only.svg", "downloaded_repos/openfun_richie/src/richie/apps/courses/static/richie/images/catalog_visibility/hidden.svg", "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/blogpost_detail.html", "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/blogpost_list.html", "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/category_detail.html", "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/category_list.html", "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_blogpost_glimpse.html", "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_category_glimpse.html", "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_course_glimpse.html", "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_course_products.html", "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_course_relations.html", "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_error_detail_template_banner.html", "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_organization_glimpse.html", "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_organization_main_logo.html", "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_person_glimpse.html", "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_program_glimpse.html", "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_detail.html", "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_list.html", "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/person_detail.html", "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/person_list.html", "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/program_detail.html", "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/program_list.html", "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/plugins/blogpost.html", "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/plugins/category_plugin.html", "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/plugins/course_plugin.html", "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/plugins/licence_plugin.html", "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/plugins/organization.html", "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/plugins/organizations_by_category.html", "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/plugins/person.html", "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/plugins/program.html", "downloaded_repos/openfun_richie/src/richie/apps/courses/templatetags/__init__.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/templatetags/category_tags.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/templatetags/extra_tags.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/urls.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/utils.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/views.py", "downloaded_repos/openfun_richie/src/richie/apps/courses/widgets.py", "downloaded_repos/openfun_richie/src/richie/apps/demo/__init__.py", "downloaded_repos/openfun_richie/src/richie/apps/demo/defaults.py", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/banner/banner1.jpg", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/banner/banner2.jpg", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/banner/banner3.jpg", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/banner/banner4.jpg", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/category_logo/logo1.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/category_logo/logo10.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/category_logo/logo11.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/category_logo/logo12.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/category_logo/logo13.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/category_logo/logo14.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/category_logo/logo2.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/category_logo/logo3.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/category_logo/logo4.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/category_logo/logo5.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/category_logo/logo6.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/category_logo/logo7.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/category_logo/logo8.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/category_logo/logo9.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/cover/cover1.jpg", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/cover/cover2.jpg", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/cover/cover3.jpg", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/cover/cover4.jpg", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/cover/cover5.jpg", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/cover/cover6.jpg", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/cover/cover7.jpg", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/cover/cover8.jpg", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/icons/academic.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/icons/accessible.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/icons/cc.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/icons/certificate.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/icons/subtitles.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/licence/cc_by_nc.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/licence/cc_by_nc_nd.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/licence/cc_by_nc_sa.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/licence/cc_by_nd.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/licence/cc_by_sa.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/logo/logo1.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/logo/logo2.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/logo/logo3.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/logo/logo4.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/logo/logo5.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/logo/logo6.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/logo/logo7.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/logo/logo8.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/portrait/portrait-1.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/portrait/portrait-2.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/portrait/portrait-3.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/portrait/portrait-4.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/portrait/portrait-5.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/portrait/portrait-6.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/portrait/portrait-7.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/fixtures/portrait/portrait-8.png", "downloaded_repos/openfun_richie/src/richie/apps/demo/helpers.py", "downloaded_repos/openfun_richie/src/richie/apps/demo/management/__init__.py", "downloaded_repos/openfun_richie/src/richie/apps/demo/management/commands/__init__.py", "downloaded_repos/openfun_richie/src/richie/apps/demo/management/commands/create_demo_site.py", "downloaded_repos/openfun_richie/src/richie/apps/demo/management/commands/create_dev_data.py", "downloaded_repos/openfun_richie/src/richie/apps/demo/utils.py", "downloaded_repos/openfun_richie/src/richie/apps/search/__init__.py", "downloaded_repos/openfun_richie/src/richie/apps/search/apps.py", "downloaded_repos/openfun_richie/src/richie/apps/search/cms_toolbars.py", "downloaded_repos/openfun_richie/src/richie/apps/search/defaults.py", "downloaded_repos/openfun_richie/src/richie/apps/search/description.apib", "downloaded_repos/openfun_richie/src/richie/apps/search/elasticsearch.py", "downloaded_repos/openfun_richie/src/richie/apps/search/exceptions.py", "downloaded_repos/openfun_richie/src/richie/apps/search/fields/__init__.py", "downloaded_repos/openfun_richie/src/richie/apps/search/fields/array.py", "downloaded_repos/openfun_richie/src/richie/apps/search/fields/datetimerange.py", "downloaded_repos/openfun_richie/src/richie/apps/search/filter_definitions/__init__.py", "downloaded_repos/openfun_richie/src/richie/apps/search/filter_definitions/base.py", "downloaded_repos/openfun_richie/src/richie/apps/search/filter_definitions/courses.py", "downloaded_repos/openfun_richie/src/richie/apps/search/filter_definitions/helpers.py", "downloaded_repos/openfun_richie/src/richie/apps/search/filter_definitions/mixins.py", "downloaded_repos/openfun_richie/src/richie/apps/search/forms.py", "downloaded_repos/openfun_richie/src/richie/apps/search/index_manager.py", "downloaded_repos/openfun_richie/src/richie/apps/search/indexers/__init__.py", "downloaded_repos/openfun_richie/src/richie/apps/search/indexers/categories.py", "downloaded_repos/openfun_richie/src/richie/apps/search/indexers/courses.py", "downloaded_repos/openfun_richie/src/richie/apps/search/indexers/licences.py", "downloaded_repos/openfun_richie/src/richie/apps/search/indexers/organizations.py", "downloaded_repos/openfun_richie/src/richie/apps/search/indexers/persons.py", "downloaded_repos/openfun_richie/src/richie/apps/search/management/__init__.py", "downloaded_repos/openfun_richie/src/richie/apps/search/management/commands/__init__.py", "downloaded_repos/openfun_richie/src/richie/apps/search/management/commands/bootstrap_elasticsearch.py", "downloaded_repos/openfun_richie/src/richie/apps/search/models.py", "downloaded_repos/openfun_richie/src/richie/apps/search/signals.py", "downloaded_repos/openfun_richie/src/richie/apps/search/templates/search/search.html", "downloaded_repos/openfun_richie/src/richie/apps/search/text_indexing.py", "downloaded_repos/openfun_richie/src/richie/apps/search/urls.py", "downloaded_repos/openfun_richie/src/richie/apps/search/utils/__init__.py", "downloaded_repos/openfun_richie/src/richie/apps/search/utils/i18n.py", "downloaded_repos/openfun_richie/src/richie/apps/search/utils/indexers.py", "downloaded_repos/openfun_richie/src/richie/apps/search/utils/viewsets.py", "downloaded_repos/openfun_richie/src/richie/apps/search/views.py", "downloaded_repos/openfun_richie/src/richie/apps/search/viewsets/__init__.py", "downloaded_repos/openfun_richie/src/richie/apps/search/viewsets/categories.py", "downloaded_repos/openfun_richie/src/richie/apps/search/viewsets/courses.py", "downloaded_repos/openfun_richie/src/richie/apps/search/viewsets/licences.py", "downloaded_repos/openfun_richie/src/richie/apps/search/viewsets/organizations.py", "downloaded_repos/openfun_richie/src/richie/apps/search/viewsets/persons.py", "downloaded_repos/openfun_richie/src/richie/locale/.gitkeep", "downloaded_repos/openfun_richie/src/richie/locale/ar_SA/LC_MESSAGES/django.mo", "downloaded_repos/openfun_richie/src/richie/locale/ar_SA/LC_MESSAGES/django.po", "downloaded_repos/openfun_richie/src/richie/locale/es_ES/LC_MESSAGES/django.mo", "downloaded_repos/openfun_richie/src/richie/locale/es_ES/LC_MESSAGES/django.po", "downloaded_repos/openfun_richie/src/richie/locale/fr_CA/LC_MESSAGES/django.mo", "downloaded_repos/openfun_richie/src/richie/locale/fr_CA/LC_MESSAGES/django.po", "downloaded_repos/openfun_richie/src/richie/locale/fr_FR/LC_MESSAGES/django.mo", "downloaded_repos/openfun_richie/src/richie/locale/fr_FR/LC_MESSAGES/django.po", "downloaded_repos/openfun_richie/src/richie/locale/ko_KR/LC_MESSAGES/django.mo", "downloaded_repos/openfun_richie/src/richie/locale/ko_KR/LC_MESSAGES/django.po", "downloaded_repos/openfun_richie/src/richie/locale/pt_PT/LC_MESSAGES/django.mo", "downloaded_repos/openfun_richie/src/richie/locale/pt_PT/LC_MESSAGES/django.po", "downloaded_repos/openfun_richie/src/richie/locale/ru_RU/LC_MESSAGES/django.mo", "downloaded_repos/openfun_richie/src/richie/locale/ru_RU/LC_MESSAGES/django.po", "downloaded_repos/openfun_richie/src/richie/locale/vi_VN/LC_MESSAGES/django.mo", "downloaded_repos/openfun_richie/src/richie/locale/vi_VN/LC_MESSAGES/django.po", "downloaded_repos/openfun_richie/src/richie/plugins/README.md", "downloaded_repos/openfun_richie/src/richie/plugins/__init__.py", "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/__init__.py", "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/cms_plugins.py", "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/defaults.py", "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/factories.py", "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/migrations/0001_initial.py", "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/migrations/0002_auto_20200915_1312.py", "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/migrations/0003_auto_20201118_1153.py", "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/migrations/0004_alter_glimpse_cmsplugin_ptr_alter_glimpse_variant.py", "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/migrations/__init__.py", "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/models.py", "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/templates/richie/glimpse/glimpse.html", "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/templates/richie/glimpse/glimpse_person.html", "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/templates/richie/glimpse/glimpse_quote.html", "downloaded_repos/openfun_richie/src/richie/plugins/html_sitemap/__init__.py", "downloaded_repos/openfun_richie/src/richie/plugins/html_sitemap/cms_plugins.py", "downloaded_repos/openfun_richie/src/richie/plugins/html_sitemap/migrations/0001_initial.py", "downloaded_repos/openfun_richie/src/richie/plugins/html_sitemap/migrations/0002_alter_htmlsitemappage_cmsplugin_ptr.py", "downloaded_repos/openfun_richie/src/richie/plugins/html_sitemap/migrations/__init__.py", "downloaded_repos/openfun_richie/src/richie/plugins/html_sitemap/models.py", "downloaded_repos/openfun_richie/src/richie/plugins/html_sitemap/templates/richie/html_sitemap/html_sitemap.html", "downloaded_repos/openfun_richie/src/richie/plugins/html_sitemap/templates/richie/html_sitemap/html_sitemap_item.html", "downloaded_repos/openfun_richie/src/richie/plugins/large_banner/__init__.py", "downloaded_repos/openfun_richie/src/richie/plugins/large_banner/cms_plugins.py", "downloaded_repos/openfun_richie/src/richie/plugins/large_banner/defaults.py", "downloaded_repos/openfun_richie/src/richie/plugins/large_banner/factories.py", "downloaded_repos/openfun_richie/src/richie/plugins/large_banner/forms.py", "downloaded_repos/openfun_richie/src/richie/plugins/large_banner/migrations/0001_initial.py", "downloaded_repos/openfun_richie/src/richie/plugins/large_banner/migrations/0002_add_template_and_content_fields.py", "downloaded_repos/openfun_richie/src/richie/plugins/large_banner/migrations/0003_make_logo_optional.py", "downloaded_repos/openfun_richie/src/richie/plugins/large_banner/migrations/0004_alter_largebanner_cmsplugin_ptr.py", "downloaded_repos/openfun_richie/src/richie/plugins/large_banner/migrations/__init__.py", "downloaded_repos/openfun_richie/src/richie/plugins/large_banner/models.py", "downloaded_repos/openfun_richie/src/richie/plugins/large_banner/templates/richie/large_banner/compacted.html", "downloaded_repos/openfun_richie/src/richie/plugins/large_banner/templates/richie/large_banner/hero-intro.html", "downloaded_repos/openfun_richie/src/richie/plugins/large_banner/templates/richie/large_banner/large_banner.html", "downloaded_repos/openfun_richie/src/richie/plugins/lti_consumer/__init__.py", "downloaded_repos/openfun_richie/src/richie/plugins/lti_consumer/api.py", "downloaded_repos/openfun_richie/src/richie/plugins/lti_consumer/cms_plugins.py", "downloaded_repos/openfun_richie/src/richie/plugins/lti_consumer/defaults.py", "downloaded_repos/openfun_richie/src/richie/plugins/lti_consumer/factories.py", "downloaded_repos/openfun_richie/src/richie/plugins/lti_consumer/forms.py", "downloaded_repos/openfun_richie/src/richie/plugins/lti_consumer/migrations/0001_initial.py", "downloaded_repos/openfun_richie/src/richie/plugins/lti_consumer/migrations/0002_auto_20210603_1817.py", "downloaded_repos/openfun_richie/src/richie/plugins/lti_consumer/migrations/0003_auto_20221005_0931.py", "downloaded_repos/openfun_richie/src/richie/plugins/lti_consumer/migrations/0004_alter_lticonsumer_cmsplugin_ptr.py", "downloaded_repos/openfun_richie/src/richie/plugins/lti_consumer/migrations/__init__.py", "downloaded_repos/openfun_richie/src/richie/plugins/lti_consumer/models.py", "downloaded_repos/openfun_richie/src/richie/plugins/lti_consumer/static/lti_consumer/js/change_form.js", "downloaded_repos/openfun_richie/src/richie/plugins/lti_consumer/templates/richie/lti_consumer/lti_consumer.html", "downloaded_repos/openfun_richie/src/richie/plugins/lti_consumer/urls.py", "downloaded_repos/openfun_richie/src/richie/plugins/nesteditem/__init__.py", "downloaded_repos/openfun_richie/src/richie/plugins/nesteditem/cms_plugins.py", "downloaded_repos/openfun_richie/src/richie/plugins/nesteditem/defaults.py", "downloaded_repos/openfun_richie/src/richie/plugins/nesteditem/factories.py", "downloaded_repos/openfun_richie/src/richie/plugins/nesteditem/migrations/0001_initial.py", "downloaded_repos/openfun_richie/src/richie/plugins/nesteditem/migrations/0002_auto_20200417_1237.py", "downloaded_repos/openfun_richie/src/richie/plugins/nesteditem/migrations/0003_auto_20200511_2258.py", "downloaded_repos/openfun_richie/src/richie/plugins/nesteditem/migrations/0004_alter_nesteditem_cmsplugin_ptr.py", "downloaded_repos/openfun_richie/src/richie/plugins/nesteditem/migrations/__init__.py", "downloaded_repos/openfun_richie/src/richie/plugins/nesteditem/models.py", "downloaded_repos/openfun_richie/src/richie/plugins/nesteditem/templates/richie/nesteditem/nesteditem.html", "downloaded_repos/openfun_richie/src/richie/plugins/plain_text/__init__.py", "downloaded_repos/openfun_richie/src/richie/plugins/plain_text/cms_plugins.py", "downloaded_repos/openfun_richie/src/richie/plugins/plain_text/factories.py", "downloaded_repos/openfun_richie/src/richie/plugins/plain_text/migrations/0001_initial.py", "downloaded_repos/openfun_richie/src/richie/plugins/plain_text/migrations/0002_alter_plaintext_cmsplugin_ptr.py", "downloaded_repos/openfun_richie/src/richie/plugins/plain_text/migrations/__init__.py", "downloaded_repos/openfun_richie/src/richie/plugins/plain_text/models.py", "downloaded_repos/openfun_richie/src/richie/plugins/plain_text/templates/richie/plain_text/plain_text.html", "downloaded_repos/openfun_richie/src/richie/plugins/section/__init__.py", "downloaded_repos/openfun_richie/src/richie/plugins/section/cms_plugins.py", "downloaded_repos/openfun_richie/src/richie/plugins/section/defaults.py", "downloaded_repos/openfun_richie/src/richie/plugins/section/factories.py", "downloaded_repos/openfun_richie/src/richie/plugins/section/forms.py", "downloaded_repos/openfun_richie/src/richie/plugins/section/migrations/0001_initial.py", "downloaded_repos/openfun_richie/src/richie/plugins/section/migrations/0002_add_template_field.py", "downloaded_repos/openfun_richie/src/richie/plugins/section/migrations/0003_auto_20191119_1650.py", "downloaded_repos/openfun_richie/src/richie/plugins/section/migrations/0004_remove_section_cadenced.py", "downloaded_repos/openfun_richie/src/richie/plugins/section/migrations/0005_migrate_sectionlist_to_nesteditem.py", "downloaded_repos/openfun_richie/src/richie/plugins/section/migrations/0006_add_attributes_field.py", "downloaded_repos/openfun_richie/src/richie/plugins/section/migrations/0007_add_section_grid_columns_and_more.py", "downloaded_repos/openfun_richie/src/richie/plugins/section/migrations/__init__.py", "downloaded_repos/openfun_richie/src/richie/plugins/section/models.py", "downloaded_repos/openfun_richie/src/richie/plugins/section/templates/richie/section/section.html", "downloaded_repos/openfun_richie/src/richie/plugins/section/templates/richie/section/section_primary.html", "downloaded_repos/openfun_richie/src/richie/plugins/section/templates/richie/section/section_quaternary.html", "downloaded_repos/openfun_richie/src/richie/plugins/section/templates/richie/section/section_quinary.html", "downloaded_repos/openfun_richie/src/richie/plugins/section/templates/richie/section/section_secondary.html", "downloaded_repos/openfun_richie/src/richie/plugins/section/templates/richie/section/section_senary.html", "downloaded_repos/openfun_richie/src/richie/plugins/section/templates/richie/section/section_septenary.html", "downloaded_repos/openfun_richie/src/richie/plugins/section/templates/richie/section/section_tertiary.html", "downloaded_repos/openfun_richie/src/richie/plugins/section/templates/richie/section/section_tiles.html", "downloaded_repos/openfun_richie/src/richie/plugins/simple_picture/__init__.py", "downloaded_repos/openfun_richie/src/richie/plugins/simple_picture/cms_plugins.py", "downloaded_repos/openfun_richie/src/richie/plugins/simple_picture/defaults.py", "downloaded_repos/openfun_richie/src/richie/plugins/simple_picture/factories.py", "downloaded_repos/openfun_richie/src/richie/plugins/simple_picture/forms.py", "downloaded_repos/openfun_richie/src/richie/plugins/simple_picture/helpers.py", "downloaded_repos/openfun_richie/src/richie/plugins/simple_picture/templates/richie/simple_picture/picture.html", "downloaded_repos/openfun_richie/src/richie/plugins/simple_text_ckeditor/__init__.py", "downloaded_repos/openfun_richie/src/richie/plugins/simple_text_ckeditor/cms_plugins.py", "downloaded_repos/openfun_richie/src/richie/plugins/simple_text_ckeditor/defaults.py", "downloaded_repos/openfun_richie/src/richie/plugins/simple_text_ckeditor/factories.py", "downloaded_repos/openfun_richie/src/richie/plugins/simple_text_ckeditor/forms.py", "downloaded_repos/openfun_richie/src/richie/plugins/simple_text_ckeditor/migrations/0001_initial.py", "downloaded_repos/openfun_richie/src/richie/plugins/simple_text_ckeditor/migrations/0002_add_variant_and_cmsplugin_ptr.py", "downloaded_repos/openfun_richie/src/richie/plugins/simple_text_ckeditor/migrations/__init__.py", "downloaded_repos/openfun_richie/src/richie/plugins/simple_text_ckeditor/models.py", "downloaded_repos/openfun_richie/src/richie/plugins/simple_text_ckeditor/templates/richie/simple_text_ckeditor/simple_text.html", "downloaded_repos/openfun_richie/src/richie/plugins/simple_text_ckeditor/validators.py", "downloaded_repos/openfun_richie/src/richie/plugins/slider/__init__.py", "downloaded_repos/openfun_richie/src/richie/plugins/slider/cms_plugins.py", "downloaded_repos/openfun_richie/src/richie/plugins/slider/factories.py", "downloaded_repos/openfun_richie/src/richie/plugins/slider/forms.py", "downloaded_repos/openfun_richie/src/richie/plugins/slider/migrations/0001_initial.py", "downloaded_repos/openfun_richie/src/richie/plugins/slider/migrations/__init__.py", "downloaded_repos/openfun_richie/src/richie/plugins/slider/models.py", "downloaded_repos/openfun_richie/src/richie/plugins/slider/templates/richie/slider/slider.html", "downloaded_repos/openfun_richie/src/richie/plugins/urls.py", "downloaded_repos/openfun_richie/src/richie/static/richie/README.md", "downloaded_repos/openfun_richie/src/richie/static/richie/css/.gitkeep", "downloaded_repos/openfun_richie/src/richie/static/richie/css/ckeditor.css", "downloaded_repos/openfun_richie/src/richie/static/richie/js/.gitkeep", "downloaded_repos/openfun_richie/tests_e2e/.gitignore", "downloaded_repos/openfun_richie/tests_e2e/cypress/integration/categories_meta_list.spec.js", "downloaded_repos/openfun_richie/tests_e2e/cypress/integration/category_detail.spec.js", "downloaded_repos/openfun_richie/tests_e2e/cypress/integration/course_detail.spec.js", "downloaded_repos/openfun_richie/tests_e2e/cypress/integration/course_run.spec.js", "downloaded_repos/openfun_richie/tests_e2e/cypress/integration/courses_list.spec.js", "downloaded_repos/openfun_richie/tests_e2e/cypress/integration/homepage.spec.js", "downloaded_repos/openfun_richie/tests_e2e/cypress/integration/organization_detail.spec.js", "downloaded_repos/openfun_richie/tests_e2e/cypress/integration/organizations_list.spec.js", "downloaded_repos/openfun_richie/tests_e2e/cypress/integration/person_detail.spec.js", "downloaded_repos/openfun_richie/tests_e2e/cypress/integration/persons_list.spec.js", "downloaded_repos/openfun_richie/tests_e2e/cypress/integration/program_detail.spec.js", "downloaded_repos/openfun_richie/tests_e2e/cypress/integration/programs_list.spec.js", "downloaded_repos/openfun_richie/tests_e2e/cypress/support/index.js", "downloaded_repos/openfun_richie/tests_e2e/cypress.json", "downloaded_repos/openfun_richie/tests_e2e/package.json", "downloaded_repos/openfun_richie/tests_e2e/yarn.lock", "downloaded_repos/openfun_richie/website/.gitignore", "downloaded_repos/openfun_richie/website/docusaurus.config.js", "downloaded_repos/openfun_richie/website/package.json", "downloaded_repos/openfun_richie/website/sidebars.json", "downloaded_repos/openfun_richie/website/src/css/customTheme.css", "downloaded_repos/openfun_richie/website/src/pages/help.js", "downloaded_repos/openfun_richie/website/src/pages/index.js", "downloaded_repos/openfun_richie/website/src/pages/users.js", "downloaded_repos/openfun_richie/website/src/pages/versions.js", "downloaded_repos/openfun_richie/website/static/.circleci/config.yml", "downloaded_repos/openfun_richie/website/static/CNAME", "downloaded_repos/openfun_richie/website/static/css/custom.css", "downloaded_repos/openfun_richie/website/static/img/undraw_around_the_world.svg", "downloaded_repos/openfun_richie/website/static/img/undraw_experience_design.svg", "downloaded_repos/openfun_richie/website/static/img/undraw_open_source.svg", "downloaded_repos/openfun_richie/website/static/img/undraw_professor.svg", "downloaded_repos/openfun_richie/website/static/img/undraw_search.svg", "downloaded_repos/openfun_richie/website/static/img/undraw_video_call.svg", "downloaded_repos/openfun_richie/website/static/img/users/edulib.png", "downloaded_repos/openfun_richie/website/static/img/users/fun.svg", "downloaded_repos/openfun_richie/website/static/img/users/nau.svg", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.12/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.12/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.12/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.12/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.12/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.12/quick-start.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.13/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.13/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.13/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.13/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.13/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.13/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.13/quick-start.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.14/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.14/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.14/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.14/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.14/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.14/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.14/quick-start.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.15/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.15/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.15/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.15/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.15/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.15/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.15/quick-start.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.16/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.16/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.16/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.16/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.16/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.16/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.16/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.16/quick-start.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.17/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.17/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.17/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.17/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.17/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.17/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.17/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-1.17/quick-start.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.0.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.0.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.0.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.0.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.0.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.0.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.0.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.0.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.0.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.0.0/quick-start.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.0.1/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.0.1/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.0.1/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.0.1/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.0.1/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.0.1/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.0.1/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.0.1/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.0.1/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.0.1/quick-start.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.1.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.1.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.1.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.1.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.1.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.1.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.1.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.1.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.1.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.1.0/quick-start.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.10.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.10.0/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.10.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.10.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.10.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.10.0/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.10.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.10.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.10.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.10.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.10.0/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.10.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.10.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.10.0/quick-start.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.10.0/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.10.0/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.10.0/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.11.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.11.0/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.11.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.11.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.11.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.11.0/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.11.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.11.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.11.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.11.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.11.0/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.11.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.11.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.11.0/quick-start.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.11.0/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.11.0/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.11.0/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.12.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.12.0/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.12.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.12.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.12.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.12.0/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.12.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.12.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.12.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.12.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.12.0/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.12.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.12.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.12.0/quick-start.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.12.0/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.12.0/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.12.0/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.13.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.13.0/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.13.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.13.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.13.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.13.0/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.13.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.13.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.13.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.13.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.13.0/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.13.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.13.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.13.0/quick-start.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.13.0/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.13.0/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.13.0/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.0/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.0/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.0/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.0/quick-start.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.0/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.0/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.0/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.1/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.1/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.1/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.1/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.1/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.1/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.1/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.1/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.1/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.1/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.1/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.1/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.1/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.1/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.1/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.1/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.1/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.1/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.1/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.1/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.1/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.0/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.0/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.0/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.0/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.0/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.0/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.0/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.0/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.0/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.0/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.0/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.1/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.1/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.1/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.1/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.1/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.1/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.1/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.1/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.1/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.1/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.1/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.1/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.1/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.1/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.1/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.1/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.1/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.1/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.1/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.1/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.1/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.16.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.16.0/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.16.0/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.16.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.16.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.16.0/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.16.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.16.0/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.16.0/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.16.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.16.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.16.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.16.0/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.16.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.16.0/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.16.0/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.16.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.16.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.16.0/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.16.0/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.16.0/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.17.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.17.0/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.17.0/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.17.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.17.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.17.0/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.17.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.17.0/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.17.0/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.17.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.17.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.17.0/filters-customization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.17.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.17.0/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.17.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.17.0/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.17.0/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.17.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.17.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.17.0/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.17.0/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.17.0/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.18.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.18.0/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.18.0/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.18.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.18.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.18.0/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.18.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.18.0/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.18.0/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.18.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.18.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.18.0/filters-customization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.18.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.18.0/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.18.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.18.0/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.18.0/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.18.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.18.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.18.0/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.18.0/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.18.0/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.18.0/yarn.lock", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.19.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.19.0/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.19.0/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.19.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.19.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.19.0/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.19.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.19.0/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.19.0/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.19.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.19.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.19.0/filters-customization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.19.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.19.0/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.19.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.19.0/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.19.0/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.19.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.19.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.19.0/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.19.0/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.19.0/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.2.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.2.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.2.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.2.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.2.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.2.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.2.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.2.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.2.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.2.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.2.0/quick-start.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.0/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.0/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.0/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.0/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.0/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.0/filters-customization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.0/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.0/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.0/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.0/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.0/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.0/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.1/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.1/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.1/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.1/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.1/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.1/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.1/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.1/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.1/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.1/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.1/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.1/filters-customization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.1/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.1/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.1/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.1/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.1/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.1/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.1/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.1/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.1/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.1/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.0/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.0/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.0/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.0/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.0/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.0/filters-customization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.0/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.0/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.0/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.0/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.0/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.0/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.1/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.1/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.1/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.1/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.1/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.1/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.1/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.1/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.1/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.1/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.1/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.1/filters-customization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.1/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.1/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.1/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.1/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.1/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.1/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.1/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.1/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.1/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.1/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.22.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.22.0/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.22.0/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.22.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.22.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.22.0/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.22.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.22.0/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.22.0/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.22.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.22.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.22.0/filters-customization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.22.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.22.0/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.22.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.22.0/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.22.0/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.22.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.22.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.22.0/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.22.0/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.22.0/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.23.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.23.0/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.23.0/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.23.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.23.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.23.0/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.23.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.23.0/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.23.0/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.23.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.23.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.23.0/filters-customization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.23.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.23.0/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.23.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.23.0/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.23.0/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.23.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.23.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.23.0/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.23.0/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.23.0/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.0/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.0/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.0/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.0/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.0/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.0/filters-customization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.0/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.0/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.0/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.0/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.0/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.0/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.0/yarn.lock", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.1/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.1/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.1/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.1/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.1/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.1/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.1/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.1/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.1/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.1/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.1/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.1/filters-customization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.1/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.1/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.1/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.1/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.1/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.1/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.1/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.1/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.1/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.1/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0/filters-customization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.0/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.0/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.0/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.0/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.0/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.0/filters-customization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.0/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.0/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.0/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.0/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.0/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.0/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.1/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.1/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.1/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.1/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.1/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.1/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.1/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.1/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.1/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.1/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.1/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.1/filters-customization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.1/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.1/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.1/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.1/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.1/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.1/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.1/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.1/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.1/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.1/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.1/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.1/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.1/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.1/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.1/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.1/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.1/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.1/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.1/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.1/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.1/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.1/filters-customization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.1/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.1/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.1/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.1/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.1/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.1/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.1/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.1/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.1/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.1/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.26.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.26.0/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.26.0/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.26.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.26.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.26.0/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.26.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.26.0/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.26.0/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.26.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.26.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.26.0/filters-customization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.26.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.26.0/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.26.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.26.0/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.26.0/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.26.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.26.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.26.0/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.26.0/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.26.0/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.27.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.27.0/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.27.0/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.27.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.27.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.27.0/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.27.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.27.0/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.27.0/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.27.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.27.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.27.0/filters-customization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.27.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.27.0/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.27.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.27.0/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.27.0/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.27.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.27.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.27.0/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.27.0/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.27.0/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.0/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.0/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.0/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.0/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.0/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.0/filters-customization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.0/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.0/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.0/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.0/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.0/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.0/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.1/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.1/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.1/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.1/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.1/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.1/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.1/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.1/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.1/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.1/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.1/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.1/filters-customization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.1/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.1/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.1/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.1/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.1/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.1/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.1/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.1/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.1/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.1/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.0/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.0/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.0/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.0/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.0/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.0/filters-customization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.0/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.0/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.0/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.0/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.0/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.0/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.1/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.1/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.1/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.1/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.1/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.1/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.1/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.1/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.1/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.1/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.1/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.1/filters-customization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.1/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.1/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.1/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.1/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.1/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.1/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.1/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.1/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.1/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.1/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.2/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.2/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.2/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.2/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.2/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.2/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.2/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.2/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.2/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.2/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.2/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.2/filters-customization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.2/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.2/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.2/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.2/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.2/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.2/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.2/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.2/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.2/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.2/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.0/quick-start.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.1/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.1/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.1/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.1/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.1/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.1/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.1/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.1/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.1/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.1/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.1/quick-start.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.2/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.2/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.2/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.2/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.2/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.2/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.2/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.2/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.2/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.2/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.2/quick-start.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.3/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.3/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.3/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.3/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.3/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.3/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.3/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.3/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.3/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.3/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.3/quick-start.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.30.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.30.0/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.30.0/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.30.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.30.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.30.0/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.30.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.30.0/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.30.0/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.30.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.30.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.30.0/filters-customization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.30.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.30.0/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.30.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.30.0/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.30.0/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.30.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.30.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.30.0/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.30.0/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.30.0/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.31.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.31.0/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.31.0/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.31.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.31.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.31.0/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.31.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.31.0/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.31.0/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.31.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.31.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.31.0/filters-customization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.31.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.31.0/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.31.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.31.0/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.31.0/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.31.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.31.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.31.0/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.31.0/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.31.0/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.32.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.32.0/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.32.0/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.32.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.32.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.32.0/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.32.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.32.0/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.32.0/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.32.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.32.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.32.0/filters-customization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.32.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.32.0/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.32.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.32.0/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.32.0/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.32.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.32.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.32.0/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.32.0/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.32.0/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.33.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.33.0/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.33.0/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.33.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.33.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.33.0/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.33.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.33.0/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.33.0/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.33.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.33.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.33.0/filters-customization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.33.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.33.0/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.33.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.33.0/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.33.0/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.33.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.33.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.33.0/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.33.0/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.33.0/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.34.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.34.0/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.34.0/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.34.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.34.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.34.0/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.34.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.34.0/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.34.0/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.34.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.34.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.34.0/filters-customization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.34.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.34.0/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.34.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.34.0/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.34.0/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.34.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.34.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.34.0/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.34.0/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.34.0/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.4.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.4.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.4.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.4.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.4.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.4.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.4.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.4.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.4.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.4.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.4.0/quick-start.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.5.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.5.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.5.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.5.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.5.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.5.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.5.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.5.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.5.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.5.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.5.0/quick-start.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.6.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.6.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.6.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.6.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.6.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.6.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.6.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.6.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.6.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.6.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.6.0/quick-start.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.7.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.7.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.7.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.7.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.7.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.7.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.7.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.7.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.7.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.7.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.7.0/quick-start.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.7.1/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.7.1/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.7.1/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.7.1/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.7.1/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.7.1/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.7.1/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.7.1/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.7.1/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.7.1/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.7.1/quick-start.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.0/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.0/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.0/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.0/quick-start.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.0/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.0/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.0/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.1/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.1/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.1/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.1/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.1/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.1/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.1/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.1/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.1/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.1/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.1/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.1/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.1/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.1/quick-start.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.1/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.1/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.1/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.2/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.2/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.2/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.2/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.2/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.2/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.2/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.2/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.2/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.2/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.2/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.2/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.2/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.2/quick-start.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.2/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.2/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.2/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.0/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.0/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.0/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.0/quick-start.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.0/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.0/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.0/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.1/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.1/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.1/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.1/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.1/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.1/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.1/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.1/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.1/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.1/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.1/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.1/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.1/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.1/quick-start.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.1/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.1/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.1/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.0.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.0.0/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.0.0/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.0.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.0.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.0.0/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.0.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.0.0/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.0.0/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.0.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.0.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.0.0/filters-customization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.0.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.0.0/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.0.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.0.0/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.0.0/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.0.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.0.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.0.0/plugins-extensions/menuentry.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.0.0/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.0.0/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.0.0/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.0/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.0/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.0/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.0/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.0/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.0/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.0/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.0/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.0/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.0/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.0/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.0/filters-customization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.0/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.0/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.0/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.0/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.0/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.0/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.0/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.0/plugins-extensions/menuentry.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.0/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.0/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.0/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.1/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.1/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.1/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.1/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.1/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.1/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.1/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.1/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.1/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.1/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.1/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.1/filters-customization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.1/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.1/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.1/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.1/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.1/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.1/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.1/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.1/plugins-extensions/menuentry.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.1/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.1/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.1/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.2/accessibility-testing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.2/api/course-run-synchronization-api.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.2/assets/images/demo-screenshot.jpg", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.2/building-the-frontend.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.2/contributing.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.2/cookiecutter.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.2/css-guidelines.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.2/discover.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.2/displaying-connection-status.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.2/django-react-interop.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.2/docker-development.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.2/filters-customization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.2/frontend-overrides.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.2/installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.2/internationalization.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.2/joanie-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.2/lms-backends.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.2/lms-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.2/native-installation.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.2/plugins-extensions/menuentry.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.2/synchronizing-course-runs.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.2/tls-connection.md", "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.2/web-analytics.md", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-1.12-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-1.13-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-1.14-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-1.15-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-1.16-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-1.17-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.0.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.0.1-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.1.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.10.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.11.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.12.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.13.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.14.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.14.1-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.15.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.15.1-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.16.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.17.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.18.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.19.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.2.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.20.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.20.1-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.21.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.21.1-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.22.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.23.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.24.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.24.1-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.25.0-beta.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.25.0-beta.1-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.25.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.25.1-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.26.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.27.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.28.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.28.1-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.29.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.29.1-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.29.2-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.3.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.3.1-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.3.2-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.3.3-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.30.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.31.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.32.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.33.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.34.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.4.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.5.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.6.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.7.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.7.1-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.8.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.8.1-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.8.2-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.9.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-2.9.1-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-3.0.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-3.1.0-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-3.1.1-sidebars.json", "downloaded_repos/openfun_richie/website/versioned_sidebars/version-3.1.2-sidebars.json", "downloaded_repos/openfun_richie/website/versions.json", "downloaded_repos/openfun_richie/website/yarn.lock"], "skipped": [{"path": "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/bin/release", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/src/backend/base/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/cookiecutter/{{cookiecutter.organization}}-richie-site-factory/template/{{cookiecutter.site}}/src/backend/base/tests/test_views.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/docs/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/src/frontend/js/utils/test/Cunningham.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/frontend/js/utils/test/LocationDisplay.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/frontend/js/utils/test/createTestQueryClient.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/frontend/js/utils/test/deferred.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/frontend/js/utils/test/expectAlert.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/frontend/js/utils/test/expectBanner.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/frontend/js/utils/test/expectBreadcrumbsToEqualParts.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/frontend/js/utils/test/expectFetchCall.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/frontend/js/utils/test/expectSpinner.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/frontend/js/utils/test/expectUrlMatchLocationDisplayed.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/frontend/js/utils/test/factories/cunningham.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/frontend/js/utils/test/factories/factories.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/frontend/js/utils/test/factories/factories.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/frontend/js/utils/test/factories/helper.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/frontend/js/utils/test/factories/helper.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/frontend/js/utils/test/factories/joanie.spec.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/frontend/js/utils/test/factories/joanie.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/frontend/js/utils/test/factories/openEdx.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/frontend/js/utils/test/factories/reactQuery.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/frontend/js/utils/test/factories/richie.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/frontend/js/utils/test/isTestEnv.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/frontend/js/utils/test/makeHistoryOf.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/frontend/js/utils/test/mockCourseProductWithOrder.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/frontend/js/utils/test/mockPaginatedResponse.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/frontend/js/utils/test/render.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/frontend/js/utils/test/wrappers/AppWrapper.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/frontend/js/utils/test/wrappers/BaseAppWrapper.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/frontend/js/utils/test/wrappers/BaseJoanieAppWrapper.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/frontend/js/utils/test/wrappers/IntlWrapper.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/frontend/js/utils/test/wrappers/JoanieAppWrapper.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/frontend/js/utils/test/wrappers/PresentationalAppWrapper.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/frontend/js/utils/test/wrappers/ReactQueryWrapper.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/frontend/js/utils/test/wrappers/RouterWrapper.tsx", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/frontend/js/utils/test/wrappers/types.ts", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/djangocms_link/button-caesura/link.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/djangocms_video/default/video_player.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/menu/breadcrumb_item.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/menu/header_menu.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/menu/menu.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/base.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/child_pages_list.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/error.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/hreflang.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/pagination_inner.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/styleguide/fragment_inline_markup.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/richie/styleguide/index.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/blogpost-badges.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/course-badges.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/templates/social-networks/footer-badges.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/tests/test_cache.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/tests/test_settings.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/core/tests/utils.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/category_detail.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/course_detail.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_blogpost_glimpse.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_category_glimpse.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_course_glimpse.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_error_detail_template_banner.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_organization_glimpse.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_organization_main_logo.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_person_glimpse.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/fragment_program_glimpse.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/organization_detail.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/person_detail.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/cms/program_detail.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/plugins/licence_plugin.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/apps/courses/templates/courses/plugins/person.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/templates/richie/glimpse/glimpse.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/templates/richie/glimpse/glimpse_person.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/plugins/glimpse/templates/richie/glimpse/glimpse_quote.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/plugins/html_sitemap/templates/richie/html_sitemap/html_sitemap_item.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/plugins/large_banner/templates/richie/large_banner/compacted.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/src/richie/plugins/large_banner/templates/richie/large_banner/hero-intro.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/openfun_richie/tests/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/core/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/core/test_admin.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/core/test_authentication_delegation.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/core/test_cache.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/core/test_error_view.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/core/test_fields_duration.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/core/test_fields_effort.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/core/test_helpers.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/core/test_language_chooser.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/core/test_pages.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/core/test_robots.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/core/test_templates_cdn.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/core/test_templates_dns_prefetch.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/core/test_templates_richie_dashboard.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/core/test_templates_richie_homepage.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/core/test_templatetags_feature_flags.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/core/test_templatetags_full_static_tags.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/core/test_templatetags_joanie.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/core/test_templatetags_rfc_5646_locale.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/core/test_videoplayer.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/core/test_web_analytics.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/core/utils.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/lms/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/lms/test_get_lms_classes.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/lms/test_lms_select.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_admin_category.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_admin_course.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_admin_course_run.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_admin_form_course_run.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_admin_licence.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_admin_organization.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_admin_page_snapshot.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_api_course_run.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_api_course_run_sync.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_api_course_run_sync_edx.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_api_course_run_sync_joanie.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_cms_plugins_blogpost.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_cms_plugins_category.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_cms_plugins_course.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_cms_plugins_organization.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_cms_plugins_organizations_by_category.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_cms_plugins_person.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_cms_plugins_program.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_cms_toolbars.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_cms_views.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_cms_wizards_blogpost.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_cms_wizards_category.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_cms_wizards_course.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_cms_wizards_organization.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_cms_wizards_person.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_cms_wizards_program.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_command_migrate_course_run_resource_link.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_factories_blogpost.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_factories_category.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_factories_course_run.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_factories_organization.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_factories_person.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_factories_program.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_licence_plugin.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_models_blogpost.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_models_category.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_models_course.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_models_course_run.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_models_course_state.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_models_licence.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_models_organization.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_models_page_role.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_models_person.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_models_program.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_settings_mixins.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_templates_blogpost_detail.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_templates_blogpost_list.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_templates_category_detail.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_templates_category_list.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_templates_course_detail_opengraph.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_templates_course_detail_rdfa.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_templates_course_detail_rendering.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_templates_organization_detail.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_templates_organization_list.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_templates_person_detail.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_templates_person_list.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_templates_program_detail.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_templates_program_list.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_templatetags_category_tags_get_related_categories.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_templatetags_extra_tags_block_plugin.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_templatetags_extra_tags_course_programs_count.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_templatetags_extra_tags_course_runs_list_widget_props.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_templatetags_extra_tags_currency.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_templatetags_extra_tags_get_categories_pages_additional_information.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_templatetags_extra_tags_get_placeholder_plugins.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_templatetags_extra_tags_has_connected_lms.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_templatetags_extra_tags_order_by.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_templatetags_extra_tags_placeholder_as_plugins.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_templatetags_extra_tags_string_filters.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/courses/test_views_course_code_redirect.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/demo/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/demo/test_commands_create_demo_site.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/demo/test_helpers.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_autocomplete_categories.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_autocomplete_courses.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_autocomplete_licences.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_autocomplete_organizations.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_autocomplete_persons.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_cms_toolbars.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_commands_bootstrap_elasticsearch.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_elasticsearch_compat_layer.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_fields_array.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_fields_datetimerange.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_filter_definitions.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_forms_search_courses.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_forms_search_items.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_index_client.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_index_manager.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_indexers_categories.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_indexers_courses.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_indexers_licences.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_indexers_organizations.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_indexers_persons.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_partial_mappings.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_query_categories.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_query_courses.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_query_courses_edge_cases.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_query_courses_facets.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_query_courses_i18n.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_query_licences.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_query_organizations.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_query_persons.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_signals.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_templates_search.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_utils_i18n.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_utils_indexers.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_views_bootstrap_elasticsearch.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_views_filter_definitions.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_viewsets_categories.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_viewsets_courses.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_viewsets_licences.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_viewsets_organizations.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/apps/search/test_viewsets_persons.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/plugins/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/plugins/glimpse/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/plugins/glimpse/test_cms_plugins.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/plugins/html_sitemap/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/plugins/html_sitemap/test_cms_plugins.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/plugins/large_banner/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/plugins/large_banner/test_cms_plugins.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/plugins/lti_consumer/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/plugins/lti_consumer/test_api.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/plugins/lti_consumer/test_cms_plugins.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/plugins/lti_consumer/test_factories.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/plugins/lti_consumer/test_forms.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/plugins/lti_consumer/test_models.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/plugins/nesteditem/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/plugins/nesteditem/test_cms_plugins.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/plugins/plain_text/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/plugins/plain_text/test_cms_plugins.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/plugins/plain_text/test_factories.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/plugins/section/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/plugins/section/test_cms_plugins.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/plugins/section/test_templates_section_default.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/plugins/simple_picture/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/plugins/simple_picture/test_cms_plugins.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/plugins/simple_picture/test_factories.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/plugins/simple_picture/test_helpers.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/plugins/simple_text_ckeditor/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/plugins/simple_text_ckeditor/test_cms_plugins.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/plugins/simple_text_ckeditor/test_factories.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/plugins/simple_text_ckeditor/test_forms.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/plugins/simple_text_ckeditor/test_models.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/plugins/simple_text_ckeditor/test_validators.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/plugins/slider/test_cms_plugins.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/sandbox/__init__.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/tests/sandbox/test_settings.py", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.10.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.11.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.12.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.13.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.14.1/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.15.1/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.16.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.17.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.18.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.19.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.2.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.20.1/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.21.1/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.22.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.23.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.24.1/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.0-beta.1/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.25.1/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.26.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.27.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.28.1/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.1/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.29.2/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.1/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.2/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.3.3/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.30.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.31.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.32.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.33.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.34.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.4.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.5.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.6.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.7.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.7.1/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.1/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.8.2/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-2.9.1/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-3.0.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.0/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.1/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/openfun_richie/website/versioned_docs/version-3.1.2/assets/images/crowdin-join-richie.gif", "reason": "exceeded_size_limit"}]}, "time": {"rules": [], "rules_parse_time": 2.0040619373321533, "profiling_times": {"config_time": 6.155474662780762, "core_time": 24.30200505256653, "ignores_time": 0.0024912357330322266, "total_time": 30.461333513259888}, "parsing_time": {"total_time": 27.62870144844055, "per_file_time": {"mean": 0.0253474325215051, "std_dev": 0.0042371020820613555}, "very_slow_stats": {"time_ratio": 0.20536532081322972, "count_ratio": 0.009174311926605505}, "very_slow_files": [{"fpath": "downloaded_repos/openfun_richie/src/frontend/js/components/PurchaseButton/index.spec.tsx", "ftime": 0.3023200035095215}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Order/DashboardItemOrder.spec.tsx", "ftime": 0.3790140151977539}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseProductItem/components/CourseProductCourseRuns/index.spec.tsx", "ftime": 0.5202748775482178}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/index.spec.tsx", "ftime": 0.551609992980957}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/hooks/useCourseSearchParams/index.spec.tsx", "ftime": 0.5714249610900879}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/hooks/useUnionResource/index.spec.tsx", "ftime": 0.5955920219421387}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/index.spec.tsx", "ftime": 0.6443049907684326}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/index.full-process.spec.tsx", "ftime": 0.6533141136169434}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/widgets/SearchSuggestField/index.spec.tsx", "ftime": 0.6881270408630371}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseProductItem/index.spec.tsx", "ftime": 0.7679951190948486}]}, "scanning_time": {"total_time": 194.59683513641357, "per_file_time": {"mean": 0.03009539671147754, "std_dev": 0.04639841127057077}, "very_slow_stats": {"time_ratio": 0.3056158079352198, "count_ratio": 0.002938447262604392}, "very_slow_files": [{"fpath": "downloaded_repos/openfun_richie/src/frontend/js/widgets/Search/components/SearchFilterGroupModal/index.spec.tsx", "ftime": 2.168463945388794}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/widgets/SearchSuggestField/index.spec.tsx", "ftime": 2.1803460121154785}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/index.full-process.spec.tsx", "ftime": 2.620181083679199}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/components/SaleTunnel/index.spec.tsx", "ftime": 3.2910149097442627}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/hooks/useUnionResource/index.spec.tsx", "ftime": 3.295297861099243}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseProductItem/components/CourseProductCourseRuns/index.spec.tsx", "ftime": 4.409198999404907}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/hooks/useCourseSearchParams/index.spec.tsx", "ftime": 4.769751071929932}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseProductItem/index.spec.tsx", "ftime": 5.18264102935791}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Order/DashboardItemOrder.spec.tsx", "ftime": 6.709496974945068}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/index.spec.tsx", "ftime": 7.942867040634155}]}, "matching_time": {"total_time": 79.70857405662537, "per_file_and_rule_time": {"mean": 0.01137556358735912, "std_dev": 0.0015443848383252911}, "very_slow_stats": {"time_ratio": 0.38059529562923666, "count_ratio": 0.02069359212216355}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Order/DashboardItemOrder.spec.tsx", "rule_id": "javascript.express.security.audit.express-open-redirect.express-open-redirect", "time": 0.48679590225219727}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseProductItem/index.spec.tsx", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 0.48899197578430176}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Order/DashboardItemOrder.spec.tsx", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.5097401142120361}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/api/joanie.ts", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.5214638710021973}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseProductItem/components/CourseProductCourseRuns/index.spec.tsx", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.5574150085449219}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/hooks/useUnionResource/index.spec.tsx", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.6394751071929932}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/index.spec.tsx", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 0.6413037776947021}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/index.spec.tsx", "rule_id": "javascript.lang.security.audit.unknown-value-with-script-tag.unknown-value-with-script-tag", "time": 0.7630901336669922}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Order/DashboardItemOrder.spec.tsx", "rule_id": "javascript.express.security.audit.xss.direct-response-write.direct-response-write", "time": 0.9740791320800781}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/index.spec.tsx", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 1.2206230163574219}]}, "tainting_time": {"total_time": 38.482874631881714, "per_def_and_rule_time": {"mean": 0.005858254625039077, "std_dev": 0.000490611867234039}, "very_slow_stats": {"time_ratio": 0.4978067903639128, "count_ratio": 0.02542243872735576}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/index.spec.tsx", "fline": 1, "rule_id": "javascript.express.security.x-frame-options-misconfiguration.x-frame-options-misconfiguration", "time": 0.2592809200286865}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/index.spec.tsx", "fline": 1, "rule_id": "javascript.express.security.cors-misconfiguration.cors-misconfiguration", "time": 0.2655479907989502}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/hooks/useCourseSearchParams/index.spec.tsx", "fline": 1, "rule_id": "javascript.express.security.audit.res-render-injection.res-render-injection", "time": 0.27666211128234863}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/hooks/useCourseSearchParams/index.spec.tsx", "fline": 1, "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.27945613861083984}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseProductItem/components/CourseProductCourseRuns/index.spec.tsx", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.27980494499206543}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseProductItem/components/CourseProductCourseRuns/index.spec.tsx", "fline": 1, "rule_id": "javascript.express.security.require-request.require-request", "time": 0.2854011058807373}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseProductItem/components/CourseProductCourseRuns/index.spec.tsx", "fline": 1, "rule_id": "javascript.express.security.audit.res-render-injection.res-render-injection", "time": 0.31571292877197266}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/components/CourseProductItem/index.spec.tsx", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.32544994354248047}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/widgets/SyllabusCourseRunsList/index.spec.tsx", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.33908510208129883}, {"fpath": "downloaded_repos/openfun_richie/src/frontend/js/widgets/Dashboard/components/DashboardItem/Order/DashboardItemOrder.spec.tsx", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.385936975479126}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1158824768}, "engine_requested": "OSS", "skipped_rules": []}