{"version": "1.130.0", "results": [], "errors": [], "paths": {"scanned": ["downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/LICENSE", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/check-usb-support.py", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_<PERSON>rduboy-Python-Utilities/eeprom-backup.py", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_<PERSON>rduboy-Python-Utilities/eeprom-erase.py", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/eeprom-restore.py", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/example-flashcarts/example/Action/Sansan.hex", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/example-flashcarts/example/Action/Sansan.png", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_<PERSON>rduboy-Python-Utilities/example-flashcarts/example/Arcade/Ardu-Whack.hex", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_<PERSON>rduboy-Python-Utilities/example-flashcarts/example/Arcade/Ardu-Whack.png", "downloaded_repos/<PERSON><PERSON><PERSON><PERSON>_<PERSON>rduboy-Python-Utilities/example-flashcarts/example/Arcade/Nineteen43.hex", "downloaded_repos/<PERSON><PERSON><PERSON><PERSON>_<PERSON>rduboy-Python-Utilities/example-flashcarts/example/Arcade/Nineteen43.png", "downloaded_repos/<PERSON><PERSON><PERSON><PERSON>_<PERSON>rduboy-Python-Utilities/example-flashcarts/example/Platformer/CastleBoy.hex", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/example-flashcarts/example/Platformer/CastleBoy.png", "downloaded_repos/<PERSON><PERSON><PERSON><PERSON>_<PERSON>rduboy-Python-Utilities/example-flashcarts/example/Puzzle/Hangman.hex", "downloaded_repos/<PERSON><PERSON><PERSON><PERSON>_<PERSON>rduboy-Python-Utilities/example-flashcarts/example/Puzzle/Hangman.png", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/example-flashcarts/example/Puzzle/LATE.hex", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/example-flashcarts/example/Puzzle/LATE.png", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/example-flashcarts/example/Puzzle/Minesweeper.hex", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/example-flashcarts/example/Puzzle/Minesweeper.png", "downloaded_repos/<PERSON><PERSON><PERSON><PERSON>_<PERSON>rduboy-Python-Utilities/example-flashcarts/example/RPG/Rick-and-Morty.hex", "downloaded_repos/<PERSON><PERSON><PERSON><PERSON>_<PERSON>rduboy-Python-Utilities/example-flashcarts/example/RPG/Rick-and-Morty.png", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/example-flashcarts/example/Racing/Ard-Drivin.hex", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/example-flashcarts/example/Racing/Ard-Drivin.png", "downloaded_repos/<PERSON><PERSON><PERSON><PERSON>_<PERSON>rduboy-Python-Utilities/example-flashcarts/example/Shooter/Night-Raid.hex", "downloaded_repos/<PERSON><PERSON><PERSON><PERSON>_<PERSON>rduboy-Python-Utilities/example-flashcarts/example/Shooter/Night-Raid.png", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/example-flashcarts/example/arduboy_loader.png", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/example-flashcarts/example/category-screens/Action.png", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_<PERSON>rduboy-Python-Utilities/example-flashcarts/example/category-screens/Arcade.png", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/example-flashcarts/example/category-screens/Platformer.png", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/example-flashcarts/example/category-screens/Puzzle.png", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/example-flashcarts/example/category-screens/RPG.png", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/example-flashcarts/example/category-screens/Racing.png", "downloaded_repos/<PERSON><PERSON><PERSON><PERSON>_<PERSON>rduboy-Python-Utilities/example-flashcarts/example/category-screens/Shooter.png", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/example-flashcarts/example/category-screens/Sports.png", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/example-flashcarts/example/category-screens/Template.xcf", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/example-flashcarts/example/flashcart-image.bin", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/example-flashcarts/example/flashcart-index.csv", "downloaded_repos/Mr<PERSON><PERSON>y_Arduboy-Python-Utilities/example-flashcarts/loader-screen/arduboyloader.png", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_<PERSON>rduboy-Python-Utilities/example-flashcarts/loader-screen/loader-screen-only-image.bin", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_<PERSON>rduboy-Python-Utilities/example-flashcarts/loader-screen/loader-screen-only.csv", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/example-fxdata/assets/ball.png", "downloaded_repos/Mr<PERSON><PERSON>y_Arduboy-Python-Utilities/example-fxdata/assets/tiles_16x16.png", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/example-fxdata/fxdata.txt", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_<PERSON>rduboy-Python-Utilities/flashcart-backup.py", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/flashcart-builder.py", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/flashcart-decompiler.py", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/flashcart-trimmer.py", "downloaded_repos/<PERSON><PERSON><PERSON><PERSON>_<PERSON>rduboy-Python-Utilities/flashcart-writer.py", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/fxdata-build.py", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/fxdata-upload.py", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/image-converter.py", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/image-viewer.py", "downloaded_repos/<PERSON><PERSON><PERSON><PERSON>_<PERSON>rduboy-Python-Utilities/readme.md", "downloaded_repos/<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>uboy-Python-Utilities/sketch-backup.py", "downloaded_repos/<PERSON><PERSON><PERSON><PERSON>_<PERSON>rduboy-Python-Utilities/sketch-erase.py", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/starduino-alt-wiring-patch.py", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_<PERSON>rduboy-Python-Utilities/uploader-create-send-to-shortcut.vbs", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/uploader-gui.py", "downloaded_repos/Mr<PERSON><PERSON><PERSON>_<PERSON>rduboy-Python-Utilities/uploader.py"], "skipped": []}, "time": {"rules": [], "rules_parse_time": 1.7201929092407227, "profiling_times": {"config_time": 6.053776025772095, "core_time": 2.740403413772583, "ignores_time": 0.0016748905181884766, "total_time": 8.79667353630066}, "parsing_time": {"total_time": 0.6954939365386963, "per_file_time": {"mean": 0.038638552029927574, "std_dev": 0.0006278185276960989}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 2.970396041870117, "per_file_time": {"mean": 0.021841147366692033, "std_dev": 0.003032578324098327}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "matching_time": {"total_time": 0.4164700508117676, "per_file_and_rule_time": {"mean": 0.003718482596533639, "std_dev": 3.1998120178237195e-05}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_rules_on_files": []}, "tainting_time": {"total_time": 0.47804999351501465, "per_def_and_rule_time": {"mean": 0.003343006947657445, "std_dev": 0.00015951206103208032}, "very_slow_stats": {"time_ratio": 0.407842153482617, "count_ratio": 0.013986013986013986}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/fxdata-build.py", "fline": 1, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.059883832931518555}, {"fpath": "downloaded_repos/Mr<PERSON><PERSON><PERSON>_Arduboy-Python-Utilities/image-converter.py", "fline": 1, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.1350851058959961}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1089214336}, "engine_requested": "OSS", "skipped_rules": []}