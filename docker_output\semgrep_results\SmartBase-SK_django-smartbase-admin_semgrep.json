{"version": "1.130.0", "results": [{"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/admin/admin_base.py", "start": {"line": 661, "col": 16, "offset": 27301}, "end": {"line": 661, "col": 33, "offset": 27318}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/admin/widgets.py", "start": {"line": 550, "col": 16, "offset": 20405}, "end": {"line": 550, "col": 31, "offset": 20420}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.direct-use-of-httpresponse.direct-use-of-httpresponse", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/engine/admin_base_view.py", "start": {"line": 371, "col": 16, "offset": 14452}, "end": {"line": 371, "col": 79, "offset": 14515}, "extra": {"message": "Detected data rendered directly to the end user via 'HttpResponse' or a similar object. This bypasses Django's built-in cross-site scripting (XSS) defenses and could result in an XSS vulnerability. Use Django's template engine to safely render HTML.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.1/intro/tutorial03/#a-shortcut-render", "https://docs.djangoproject.com/en/3.1/topics/http/shortcuts/#render"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.direct-use-of-httpresponse.direct-use-of-httpresponse", "shortlink": "https://sg.run/EknN"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.audit.directly-returned-format-string.directly-returned-format-string", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/engine/admin_view.py", "start": {"line": 97, "col": 9, "offset": 2964}, "end": {"line": 97, "col": 100, "offset": 3055}, "extra": {"message": "Detected Flask route directly returning a formatted string. This is subject to cross-site scripting if user input can reach the string. Consider using the template engine instead and rendering pages with 'render_template()'.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["flask"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.audit.directly-returned-format-string.directly-returned-format-string", "shortlink": "https://sg.run/Zv6o"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.flask.security.audit.directly-returned-format-string.directly-returned-format-string", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/engine/admin_view.py", "start": {"line": 101, "col": 9, "offset": 3190}, "end": {"line": 101, "col": 80, "offset": 3261}, "extra": {"message": "Detected Flask route directly returning a formatted string. This is subject to cross-site scripting if user input can reach the string. Consider using the template engine instead and rendering pages with 'render_template()'.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["flask"], "references": ["https://owasp.org/Top10/A03_2021-Injection"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["vuln"], "likelihood": "HIGH", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.flask.security.audit.directly-returned-format-string.directly-returned-format-string", "shortlink": "https://sg.run/Zv6o"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/engine/field_formatter.py", "start": {"line": 42, "col": 16, "offset": 1079}, "end": {"line": 44, "col": 10, "offset": 1180}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/engine/field_formatter.py", "start": {"line": 45, "col": 12, "offset": 1192}, "end": {"line": 45, "col": 89, "offset": 1269}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/engine/field_formatter.py", "start": {"line": 56, "col": 12, "offset": 1612}, "end": {"line": 56, "col": 29, "offset": 1629}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/engine/field_formatter.py", "start": {"line": 68, "col": 12, "offset": 1898}, "end": {"line": 70, "col": 6, "offset": 1991}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/engine/field_formatter.py", "start": {"line": 74, "col": 12, "offset": 2043}, "end": {"line": 74, "col": 55, "offset": 2086}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/monkeypatch/admin_readonly_field_monkeypatch.py", "start": {"line": 19, "col": 16, "offset": 814}, "end": {"line": 28, "col": 10, "offset": 1140}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.deserialization.pickle.avoid-pickle", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/services/views.py", "start": {"line": 183, "col": 42, "offset": 6515}, "end": {"line": 183, "col": 82, "offset": 6555}, "extra": {"message": "Avoid using `pickle`, which is known to lead to code execution vulnerabilities. When unpickling, the serialized data could be manipulated to run arbitrary code. Instead, consider serializing the relevant data as JSON or a similar text-based serialization format.", "metadata": {"owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "references": ["https://docs.python.org/3/library/pickle.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/python.lang.security.deserialization.pickle.avoid-pickle", "shortlink": "https://sg.run/OPwB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.deserialization.pickle.avoid-pickle", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/services/views.py", "start": {"line": 183, "col": 85, "offset": 6558}, "end": {"line": 183, "col": 123, "offset": 6596}, "extra": {"message": "Avoid using `pickle`, which is known to lead to code execution vulnerabilities. When unpickling, the serialized data could be manipulated to run arbitrary code. Instead, consider serializing the relevant data as JSON or a similar text-based serialization format.", "metadata": {"owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "references": ["https://docs.python.org/3/library/pickle.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/python.lang.security.deserialization.pickle.avoid-pickle", "shortlink": "https://sg.run/OPwB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.lang.security.deserialization.pickle.avoid-pickle", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/services/views.py", "start": {"line": 183, "col": 126, "offset": 6599}, "end": {"line": 183, "col": 165, "offset": 6638}, "extra": {"message": "Avoid using `pickle`, which is known to lead to code execution vulnerabilities. When unpickling, the serialized data could be manipulated to run arbitrary code. Instead, consider serializing the relevant data as JSON or a similar text-based serialization format.", "metadata": {"owasp": ["A08:2017 - Insecure Deserialization", "A08:2021 - Software and Data Integrity Failures"], "cwe": ["CWE-502: Deserialization of Untrusted Data"], "references": ["https://docs.python.org/3/library/pickle.html"], "category": "security", "technology": ["python"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Insecure Deserialization "], "source": "https://semgrep.dev/r/python.lang.security.deserialization.pickle.avoid-pickle", "shortlink": "https://sg.run/OPwB"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/autocomplete.js", "start": {"line": 220, "col": 66, "offset": 9726}, "end": {"line": 220, "col": 111, "offset": 9771}, "extra": {"message": "RegExp() called with a `autocompleteData` function argument, this might allow an attacker to cause a Regular Expression Denial-of-Service (ReDoS) within your application as RegExP blocks the main thread. For this reason, it is recommended to use hardcoded regexes instead. If your regex is run on user-controlled input, consider performing input validation or use a regex checking/sanitization library such as https://www.npmjs.com/package/recheck to verify that the regex does not appear vulnerable to ReDoS.", "metadata": {"owasp": ["A05:2021 - Security Misconfiguration", "A06:2017 - Security Misconfiguration"], "cwe": ["CWE-1333: Inefficient Regular Expression Complexity"], "references": ["https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS"], "source-rule-url": "https://github.com/nodesecurity/eslint-plugin-security/blob/master/rules/detect-non-literal-regexp.js", "category": "security", "technology": ["javascript"], "subcategory": ["vuln"], "likelihood": "MEDIUM", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Denial-of-Service (DoS)"], "source": "https://semgrep.dev/r/javascript.lang.security.audit.detect-non-literal-regexp.detect-non-literal-regexp", "shortlink": "https://sg.run/gr65"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/chart.js", "start": {"line": 80, "col": 29, "offset": 3073}, "end": {"line": 80, "col": 93, "offset": 3137}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/chart.js", "start": {"line": 93, "col": 33, "offset": 3913}, "end": {"line": 93, "col": 69, "offset": 3949}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/choices.js", "start": {"line": 41, "col": 17, "offset": 1660}, "end": {"line": 41, "col": 47, "offset": 1690}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/confirmation_modal.js", "start": {"line": 73, "col": 13, "offset": 2758}, "end": {"line": 73, "col": 100, "offset": 2845}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/confirmation_modal.js", "start": {"line": 76, "col": 13, "offset": 2910}, "end": {"line": 76, "col": 105, "offset": 3002}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/confirmation_modal.js", "start": {"line": 79, "col": 13, "offset": 3066}, "end": {"line": 79, "col": 96, "offset": 3149}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/confirmation_modal.js", "start": {"line": 82, "col": 13, "offset": 3213}, "end": {"line": 82, "col": 96, "offset": 3296}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/confirmation_modal.js", "start": {"line": 85, "col": 13, "offset": 3362}, "end": {"line": 85, "col": 100, "offset": 3449}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/datepicker_plugins.js", "start": {"line": 108, "col": 9, "offset": 3669}, "end": {"line": 108, "col": 42, "offset": 3702}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/datepicker_plugins.js", "start": {"line": 173, "col": 9, "offset": 6004}, "end": {"line": 173, "col": 42, "offset": 6037}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/main.js", "start": {"line": 204, "col": 17, "offset": 7478}, "end": {"line": 204, "col": 75, "offset": 7536}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/main.js", "start": {"line": 289, "col": 21, "offset": 11108}, "end": {"line": 289, "col": 106, "offset": 11193}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/multiselect.js", "start": {"line": 90, "col": 9, "offset": 3751}, "end": {"line": 90, "col": 45, "offset": 3787}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/table_modules/column_display_module.js", "start": {"line": 116, "col": 13, "offset": 4522}, "end": {"line": 116, "col": 40, "offset": 4549}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/table_modules/selection_module.js", "start": {"line": 22, "col": 13, "offset": 910}, "end": {"line": 22, "col": 121, "offset": 1018}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/table_modules/selection_module.js", "start": {"line": 25, "col": 9, "offset": 1056}, "end": {"line": 25, "col": 129, "offset": 1176}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/table_modules/table_params_module.js", "start": {"line": 88, "col": 13, "offset": 3746}, "end": {"line": 88, "col": 44, "offset": 3777}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/table_modules/table_params_module.js", "start": {"line": 134, "col": 13, "offset": 5379}, "end": {"line": 134, "col": 207, "offset": 5573}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/table_modules/table_params_module.js", "start": {"line": 137, "col": 13, "offset": 5611}, "end": {"line": 137, "col": 89, "offset": 5687}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/table_modules/table_params_module.js", "start": {"line": 261, "col": 13, "offset": 10223}, "end": {"line": 261, "col": 44, "offset": 10254}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/table_modules/table_params_module.js", "start": {"line": 282, "col": 9, "offset": 11164}, "end": {"line": 282, "col": 47, "offset": 11202}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/tree_widget.js", "start": {"line": 200, "col": 25, "offset": 9166}, "end": {"line": 200, "col": 62, "offset": 9203}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/tree_widget.js", "start": {"line": 326, "col": 25, "offset": 15194}, "end": {"line": 326, "col": 98, "offset": 15267}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/utils.js", "start": {"line": 78, "col": 9, "offset": 2510}, "end": {"line": 78, "col": 36, "offset": 2537}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/utils.js", "start": {"line": 84, "col": 13, "offset": 2732}, "end": {"line": 84, "col": 59, "offset": 2778}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/utils.js", "start": {"line": 113, "col": 9, "offset": 3918}, "end": {"line": 113, "col": 42, "offset": 3951}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/utils.js", "start": {"line": 117, "col": 13, "offset": 4013}, "end": {"line": 117, "col": 74, "offset": 4074}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/utils.js", "start": {"line": 121, "col": 17, "offset": 4212}, "end": {"line": 121, "col": 54, "offset": 4249}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/utils.js", "start": {"line": 131, "col": 21, "offset": 4617}, "end": {"line": 131, "col": 63, "offset": 4659}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "javascript.browser.security.insecure-document-method.insecure-document-method", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/utils.js", "start": {"line": 133, "col": 21, "offset": 4705}, "end": {"line": 133, "col": 56, "offset": 4740}, "extra": {"message": "User controlled data in methods like `innerHTML`, `outerHTML` or `document.write` is an anti-pattern that can lead to XSS vulnerabilities", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "category": "security", "technology": ["browser"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "LOW", "confidence": "LOW", "references": ["https://owasp.org/Top10/A03_2021-Injection"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/javascript.browser.security.insecure-document-method.insecure-document-method", "shortlink": "https://sg.run/LwA9"}, "severity": "ERROR", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_form.html", "start": {"line": 136, "col": 29, "offset": 7064}, "end": {"line": 136, "col": 76, "offset": 7111}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_form.html", "start": {"line": 265, "col": 62, "offset": 15334}, "end": {"line": 265, "col": 78, "offset": 15350}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_password.html", "start": {"line": 23, "col": 29, "offset": 1195}, "end": {"line": 23, "col": 87, "offset": 1253}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_password.html", "start": {"line": 32, "col": 49, "offset": 1625}, "end": {"line": 32, "col": 92, "offset": 1668}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_confirmation.html", "start": {"line": 15, "col": 13, "offset": 541}, "end": {"line": 15, "col": 29, "offset": 557}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_confirmation.html", "start": {"line": 18, "col": 29, "offset": 669}, "end": {"line": 18, "col": 72, "offset": 712}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_confirmation.html", "start": {"line": 20, "col": 29, "offset": 971}, "end": {"line": 20, "col": 72, "offset": 1014}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_confirmation.html", "start": {"line": 22, "col": 29, "offset": 1202}, "end": {"line": 22, "col": 72, "offset": 1245}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_selected_confirmation.html", "start": {"line": 16, "col": 13, "offset": 576}, "end": {"line": 16, "col": 29, "offset": 592}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_selected_confirmation.html", "start": {"line": 19, "col": 29, "offset": 704}, "end": {"line": 19, "col": 49, "offset": 724}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_selected_confirmation.html", "start": {"line": 21, "col": 29, "offset": 974}, "end": {"line": 21, "col": 49, "offset": 994}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_selected_confirmation.html", "start": {"line": 23, "col": 29, "offset": 1173}, "end": {"line": 23, "col": 49, "offset": 1193}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/translations-detail.html", "start": {"line": 82, "col": 98, "offset": 4356}, "end": {"line": 82, "col": 114, "offset": 4372}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/login.html", "start": {"line": 8, "col": 17, "offset": 235}, "end": {"line": 8, "col": 76, "offset": 294}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/login.html", "start": {"line": 26, "col": 21, "offset": 818}, "end": {"line": 26, "col": 49, "offset": 846}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/login.html", "start": {"line": 36, "col": 9, "offset": 1343}, "end": {"line": 55, "col": 16, "offset": 2370}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-translate-as-no-escape.template-translate-as-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/login.html", "start": {"line": 47, "col": 66, "offset": 1946}, "end": {"line": 52, "col": 54, "offset": 2248}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-translate-as-no-escape.template-translate-as-no-escape", "shortlink": "https://sg.run/PJDz"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/password_change_form.html", "start": {"line": 9, "col": 21, "offset": 293}, "end": {"line": 9, "col": 80, "offset": 352}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.django-no-csrf-token.django-no-csrf-token", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/password_change_form.html", "start": {"line": 24, "col": 9, "offset": 1090}, "end": {"line": 39, "col": 16, "offset": 1878}, "extra": {"message": "Manually-created forms in django templates should specify a csrf_token to prevent CSRF attacks.", "metadata": {"category": "security", "cwe": "CWE-352: Cross-Site Request Forgery (CSRF)", "references": ["https://docs.djangoproject.com/en/4.2/howto/csrf/"], "confidence": "MEDIUM", "likelihood": "MEDIUM", "impact": "MEDIUM", "subcategory": ["audit"], "technology": ["django"], "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site Request Forgery (CSRF)"], "source": "https://semgrep.dev/r/python.django.security.django-no-csrf-token.django-no-csrf-token", "shortlink": "https://sg.run/N0Bp"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-autoescape-off.template-autoescape-off", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/password_reset_email.html", "start": {"line": 1, "col": 16, "offset": 15}, "end": {"line": 1, "col": 36, "offset": 35}, "extra": {"message": "Detected a template block where autoescaping is explicitly disabled with '{% autoescape off %}'. This allows rendering of raw HTML in this segment. Turn autoescaping on to prevent cross-site scripting (XSS). If you must do this, consider instead, using `mark_safe` in Python code.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.1/ref/templates/builtins/#autoescape"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-autoescape-off.template-autoescape-off", "shortlink": "https://sg.run/Q5WZ"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/password_reset_email.html", "start": {"line": 2, "col": 1, "offset": 36}, "end": {"line": 2, "col": 21, "offset": 56}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/password_reset_email.html", "start": {"line": 12, "col": 1, "offset": 532}, "end": {"line": 12, "col": 21, "offset": 552}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/password_reset_form.html", "start": {"line": 8, "col": 17, "offset": 235}, "end": {"line": 8, "col": 76, "offset": 294}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/components/inputs.html", "start": {"line": 181, "col": 29, "offset": 9128}, "end": {"line": 181, "col": 45, "offset": 9144}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/change_form_title.html", "start": {"line": 4, "col": 5, "offset": 34}, "end": {"line": 4, "col": 49, "offset": 78}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/stacked_inline.html", "start": {"line": 26, "col": 21, "offset": 1364}, "end": {"line": 26, "col": 146, "offset": 1489}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/page_list.html", "start": {"line": 99, "col": 45, "offset": 6686}, "end": {"line": 99, "col": 106, "offset": 6747}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/page_list.html", "start": {"line": 121, "col": 29, "offset": 7934}, "end": {"line": 121, "col": 82, "offset": 7987}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/page_list.html", "start": {"line": 272, "col": 25, "offset": 17637}, "end": {"line": 272, "col": 77, "offset": 17689}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/filer_change_form.html", "start": {"line": 54, "col": 5, "offset": 1951}, "end": {"line": 54, "col": 53, "offset": 1999}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-translate-as-no-escape.template-translate-as-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/folder_list.html", "start": {"line": 89, "col": 112, "offset": 4858}, "end": {"line": 97, "col": 114, "offset": 5497}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-translate-as-no-escape.template-translate-as-no-escape", "shortlink": "https://sg.run/PJDz"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/folder_list.html", "start": {"line": 97, "col": 28, "offset": 5411}, "end": {"line": 97, "col": 85, "offset": 5468}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/folder_list.html", "start": {"line": 104, "col": 28, "offset": 5902}, "end": {"line": 104, "col": 78, "offset": 5952}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/modal/modal_content.html", "start": {"line": 27, "col": 45, "offset": 1710}, "end": {"line": 27, "col": 103, "offset": 1768}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/sb_admin_js_trans.html", "start": {"line": 9, "col": 52, "offset": 551}, "end": {"line": 9, "col": 68, "offset": 567}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/sb_admin_js_trans.html", "start": {"line": 10, "col": 58, "offset": 747}, "end": {"line": 10, "col": 74, "offset": 763}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/sb_admin_js_trans.html", "start": {"line": 11, "col": 56, "offset": 863}, "end": {"line": 11, "col": 72, "offset": 879}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-blocktranslate-no-escape.template-blocktranslate-no-escape", "shortlink": "https://sg.run/3xpK"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.xss.template-translate-as-no-escape.template-translate-as-no-escape", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/tree_base.html", "start": {"line": 6, "col": 13, "offset": 205}, "end": {"line": 7, "col": 134, "offset": 376}, "extra": {"message": "Translated strings will not be escaped when rendered in a template. This leads to a vulnerability where translators could include malicious script tags in their translations. Consider using `force_escape` to explicitly escape a translated text.", "metadata": {"cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://edx.readthedocs.io/projects/edx-developer-guide/en/latest/preventing_xss/preventing_xss_in_django_templates.html#html-escaping-translations-in-django-templates", "https://docs.djangoproject.com/en/3.1/topics/i18n/translation/#internationalization-in-template-code"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.xss.template-translate-as-no-escape.template-translate-as-no-escape", "shortlink": "https://sg.run/PJDz"}, "severity": "INFO", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templatetags/sb_admin_tags.py", "start": {"line": 205, "col": 26, "offset": 7232}, "end": {"line": 207, "col": 10, "offset": 7323}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.injection.open-redirect.open-redirect", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/views/global_filter_view.py", "start": {"line": 16, "col": 9, "offset": 395}, "end": {"line": 16, "col": 64, "offset": 450}, "extra": {"message": "Data from request ($DATA) is passed to redirect(). This is an open redirect and could be exploited. Ensure you are redirecting to safe URLs by using django.utils.http.is_safe_url(). See https://cwe.mitre.org/data/definitions/601.html for more information.", "metadata": {"cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "owasp": ["A01:2021 - Broken Access Control"], "references": ["https://www.djm.org.uk/posts/djangos-little-protections-word-redirect-dangers/", "https://github.com/django/django/blob/d1b7bd030b1db111e1a3505b1fc029ab964382cc/django/utils/http.py#L231"], "category": "security", "technology": ["django"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/python.django.security.injection.open-redirect.open-redirect", "shortlink": "https://sg.run/Ave2"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.injection.open-redirect.open-redirect", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/views/global_filter_view.py", "start": {"line": 16, "col": 20, "offset": 406}, "end": {"line": 16, "col": 64, "offset": 450}, "extra": {"message": "Data from request ($DATA) is passed to redirect(). This is an open redirect and could be exploited. Ensure you are redirecting to safe URLs by using django.utils.http.is_safe_url(). See https://cwe.mitre.org/data/definitions/601.html for more information.", "metadata": {"cwe": ["CWE-601: URL Redirection to Untrusted Site ('Open Redirect')"], "owasp": ["A01:2021 - Broken Access Control"], "references": ["https://www.djm.org.uk/posts/djangos-little-protections-word-redirect-dangers/", "https://github.com/django/django/blob/d1b7bd030b1db111e1a3505b1fc029ab964382cc/django/utils/http.py#L231"], "category": "security", "technology": ["django"], "subcategory": ["vuln"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "MEDIUM", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Open Redirect"], "source": "https://semgrep.dev/r/python.django.security.injection.open-redirect.open-redirect", "shortlink": "https://sg.run/Ave2"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/views/translations_view.py", "start": {"line": 211, "col": 20, "offset": 8911}, "end": {"line": 213, "col": 14, "offset": 9112}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/views/translations_view.py", "start": {"line": 215, "col": 20, "offset": 9181}, "end": {"line": 217, "col": 14, "offset": 9378}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}, {"check_id": "python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/views/translations_view.py", "start": {"line": 219, "col": 20, "offset": 9447}, "end": {"line": 221, "col": 14, "offset": 9641}, "extra": {"message": "'mark_safe()' is used to mark a string as \"safe\" for HTML output. This disables escaping and could therefore subject the content to XSS attacks. Use 'django.utils.html.format_html()' to build HTML for rendering instead.", "metadata": {"source-rule-url": "https://bandit.readthedocs.io/en/latest/plugins/b703_django_mark_safe.html", "cwe": ["CWE-79: Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting')"], "owasp": ["A07:2017 - Cross-Site Scripting (XSS)", "A03:2021 - Injection"], "references": ["https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.safestring.mark_safe", "https://docs.djangoproject.com/en/3.0/ref/utils/#django.utils.html.format_html"], "category": "security", "technology": ["django"], "cwe2022-top25": true, "cwe2021-top25": true, "subcategory": ["audit"], "likelihood": "LOW", "impact": "MEDIUM", "confidence": "LOW", "license": "Semgrep Rules License v1.0. For more details, visit semgrep.dev/legal/rules-license", "vulnerability_class": ["Cross-Site-Scripting (XSS)"], "source": "https://semgrep.dev/r/python.django.security.audit.avoid-mark-safe.avoid-mark-safe", "shortlink": "https://sg.run/yd0P"}, "severity": "WARNING", "fingerprint": "requires login", "lines": "requires login", "validation_state": "NO_VALIDATOR", "engine_kind": "OSS"}}], "errors": [{"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/filter_widgets/advanced_filters/autocomplete_field.html:1:\n Failure: not a program", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/filter_widgets/advanced_filters/autocomplete_field.html"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/navigation.html:1:\n Failure: not a program", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/navigation.html"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/messages/alert_success.html:1:\n Failure: not a program", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/messages/alert_success.html"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/messages/alert_warning.html:1:\n Failure: not a program", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/messages/alert_warning.html"}, {"code": 2, "level": "warn", "type": "Other syntax error", "message": "Other syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/autocomplete.html:1:\n Failure: not a program", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/autocomplete.html"}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_form.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 22, "offset": 147}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_form.html", "start": {"line": 7, "col": 5, "offset": 0}, "end": {"line": 15, "col": 16, "offset": 167}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_form.html", "start": {"line": 23, "col": 5, "offset": 0}, "end": {"line": 31, "col": 34, "offset": 261}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_form.html", "start": {"line": 41, "col": 9, "offset": 0}, "end": {"line": 41, "col": 19, "offset": 10}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_form.html", "start": {"line": 73, "col": 96, "offset": 0}, "end": {"line": 73, "col": 164, "offset": 68}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_form.html", "start": {"line": 99, "col": 84, "offset": 0}, "end": {"line": 99, "col": 152, "offset": 68}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_form.html", "start": {"line": 121, "col": 9, "offset": 0}, "end": {"line": 124, "col": 21, "offset": 52}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_form.html", "start": {"line": 146, "col": 92, "offset": 0}, "end": {"line": 146, "col": 98, "offset": 6}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_form.html", "start": {"line": 260, "col": 37, "offset": 0}, "end": {"line": 260, "col": 186, "offset": 149}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_form.html", "start": {"line": 264, "col": 41, "offset": 0}, "end": {"line": 264, "col": 93, "offset": 52}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_form.html", "start": {"line": 266, "col": 41, "offset": 0}, "end": {"line": 266, "col": 182, "offset": 141}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_form.html", "start": {"line": 270, "col": 45, "offset": 0}, "end": {"line": 270, "col": 93, "offset": 48}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_form.html", "start": {"line": 286, "col": 5, "offset": 0}, "end": {"line": 287, "col": 15, "offset": 29}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_form.html:1:\n `{% extends \"sb_admin/sb_admin_base.html\" %}\n{% load i18n admin_urls static admin_modify sb_admin_tags %}\n\n{% block js_init %}\n    {{ block.super }}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_form.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_form.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 22, "offset": 147}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_form.html", "start": {"line": 7, "col": 5, "offset": 0}, "end": {"line": 15, "col": 16, "offset": 167}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_form.html", "start": {"line": 23, "col": 5, "offset": 0}, "end": {"line": 31, "col": 34, "offset": 261}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_form.html", "start": {"line": 41, "col": 9, "offset": 0}, "end": {"line": 41, "col": 19, "offset": 10}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_form.html", "start": {"line": 73, "col": 96, "offset": 0}, "end": {"line": 73, "col": 164, "offset": 68}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_form.html", "start": {"line": 99, "col": 84, "offset": 0}, "end": {"line": 99, "col": 152, "offset": 68}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_form.html", "start": {"line": 121, "col": 9, "offset": 0}, "end": {"line": 124, "col": 21, "offset": 52}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_form.html", "start": {"line": 146, "col": 92, "offset": 0}, "end": {"line": 146, "col": 98, "offset": 6}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_form.html", "start": {"line": 260, "col": 37, "offset": 0}, "end": {"line": 260, "col": 186, "offset": 149}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_form.html", "start": {"line": 264, "col": 41, "offset": 0}, "end": {"line": 264, "col": 93, "offset": 52}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_form.html", "start": {"line": 266, "col": 41, "offset": 0}, "end": {"line": 266, "col": 182, "offset": 141}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_form.html", "start": {"line": 270, "col": 45, "offset": 0}, "end": {"line": 270, "col": 93, "offset": 48}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_form.html", "start": {"line": 286, "col": 5, "offset": 0}, "end": {"line": 287, "col": 15, "offset": 29}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_password.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 20, "offset": 109}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_password.html", "start": {"line": 68, "col": 1, "offset": 0}, "end": {"line": 68, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_password.html:1:\n `{% extends \"sb_admin/sb_admin_base.html\" %}\n{% load i18n static %}\n{% load admin_urls %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_password.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_password.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 20, "offset": 109}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_password.html", "start": {"line": 68, "col": 1, "offset": 0}, "end": {"line": 68, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_confirmation.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 16, "offset": 138}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_confirmation.html", "start": {"line": 8, "col": 1, "offset": 0}, "end": {"line": 12, "col": 20, "offset": 162}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_confirmation.html", "start": {"line": 25, "col": 5, "offset": 0}, "end": {"line": 25, "col": 27, "offset": 22}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_confirmation.html", "start": {"line": 29, "col": 5, "offset": 0}, "end": {"line": 29, "col": 25, "offset": 20}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_confirmation.html", "start": {"line": 33, "col": 5, "offset": 0}, "end": {"line": 33, "col": 15, "offset": 10}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_confirmation.html", "start": {"line": 66, "col": 5, "offset": 0}, "end": {"line": 68, "col": 15, "offset": 27}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_confirmation.html:1:\n `{% extends \"sb_admin/sb_admin_base.html\" %}\n{% load i18n admin_urls static %}\n\n{% block extrahead %}\n    {{ block.super }}\n    {{ media }}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_confirmation.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_confirmation.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 16, "offset": 138}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_confirmation.html", "start": {"line": 8, "col": 1, "offset": 0}, "end": {"line": 12, "col": 20, "offset": 162}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_confirmation.html", "start": {"line": 25, "col": 5, "offset": 0}, "end": {"line": 25, "col": 27, "offset": 22}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_confirmation.html", "start": {"line": 29, "col": 5, "offset": 0}, "end": {"line": 29, "col": 25, "offset": 20}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_confirmation.html", "start": {"line": 33, "col": 5, "offset": 0}, "end": {"line": 33, "col": 15, "offset": 10}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_confirmation.html", "start": {"line": 66, "col": 5, "offset": 0}, "end": {"line": 68, "col": 15, "offset": 27}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_selected_confirmation.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 16, "offset": 143}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_selected_confirmation.html", "start": {"line": 8, "col": 1, "offset": 0}, "end": {"line": 13, "col": 20, "offset": 192}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_selected_confirmation.html", "start": {"line": 26, "col": 5, "offset": 0}, "end": {"line": 26, "col": 27, "offset": 22}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_selected_confirmation.html", "start": {"line": 30, "col": 5, "offset": 0}, "end": {"line": 30, "col": 25, "offset": 20}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_selected_confirmation.html", "start": {"line": 34, "col": 5, "offset": 0}, "end": {"line": 34, "col": 15, "offset": 10}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_selected_confirmation.html", "start": {"line": 70, "col": 5, "offset": 0}, "end": {"line": 71, "col": 15, "offset": 26}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_selected_confirmation.html:1:\n `{% extends \"sb_admin/sb_admin_base.html\" %}\n{% load i18n l10n admin_urls static %}\n\n{% block extrahead %}\n    {{ block.super }}\n    {{ media }}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_selected_confirmation.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_selected_confirmation.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 16, "offset": 143}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_selected_confirmation.html", "start": {"line": 8, "col": 1, "offset": 0}, "end": {"line": 13, "col": 20, "offset": 192}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_selected_confirmation.html", "start": {"line": 26, "col": 5, "offset": 0}, "end": {"line": 26, "col": 27, "offset": 22}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_selected_confirmation.html", "start": {"line": 30, "col": 5, "offset": 0}, "end": {"line": 30, "col": 25, "offset": 20}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_selected_confirmation.html", "start": {"line": 34, "col": 5, "offset": 0}, "end": {"line": 34, "col": 15, "offset": 10}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_selected_confirmation.html", "start": {"line": 70, "col": 5, "offset": 0}, "end": {"line": 71, "col": 15, "offset": 26}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 28, "offset": 205}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/list.html", "start": {"line": 97, "col": 5, "offset": 0}, "end": {"line": 98, "col": 31, "offset": 45}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/list.html", "start": {"line": 117, "col": 5, "offset": 0}, "end": {"line": 119, "col": 26, "offset": 55}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/list.html", "start": {"line": 121, "col": 5, "offset": 0}, "end": {"line": 122, "col": 86, "offset": 130}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/list.html", "start": {"line": 130, "col": 1, "offset": 0}, "end": {"line": 130, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/list.html:1:\n `{% extends list_base_template|default:\"sb_admin/sb_admin_base.html\" %}\n{% load sb_admin_tags i18n static %}\n\n{% block view_class %}change-list{% endblock %}\n\n{% block content %}\n    {% block page_header %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/list.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 28, "offset": 205}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/list.html", "start": {"line": 97, "col": 5, "offset": 0}, "end": {"line": 98, "col": 31, "offset": 45}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/list.html", "start": {"line": 117, "col": 5, "offset": 0}, "end": {"line": 119, "col": 26, "offset": 55}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/list.html", "start": {"line": 121, "col": 5, "offset": 0}, "end": {"line": 122, "col": 86, "offset": 130}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/list.html", "start": {"line": 130, "col": 1, "offset": 0}, "end": {"line": 130, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/object_history.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 125}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/object_history.html", "start": {"line": 45, "col": 1, "offset": 0}, "end": {"line": 45, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/object_history.html:1:\n `{% extends \"sb_admin/sb_admin_base.html\" %}\n{% load i18n admin_urls static admin_modify sb_admin_tags %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/object_history.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/object_history.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 125}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/object_history.html", "start": {"line": 45, "col": 1, "offset": 0}, "end": {"line": 45, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/action_link.html", "start": {"line": 2, "col": 16, "offset": 0}, "end": {"line": 2, "col": 77, "offset": 61}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/action_link.html:2:\n `'sb_admin/actions/partials/open_modal_attrs.html' with action` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/action_link.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/action_link.html", "start": {"line": 2, "col": 16, "offset": 0}, "end": {"line": 2, "col": 77, "offset": 61}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/selected_rows_actions.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 16, "offset": 15}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/selected_rows_actions.html", "start": {"line": 35, "col": 91, "offset": 0}, "end": {"line": 35, "col": 152, "offset": 61}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/selected_rows_actions.html", "start": {"line": 45, "col": 84, "offset": 0}, "end": {"line": 45, "col": 145, "offset": 61}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/selected_rows_actions.html:1:\n `{% load i18n %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/selected_rows_actions.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/selected_rows_actions.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 16, "offset": 15}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/selected_rows_actions.html", "start": {"line": 35, "col": 91, "offset": 0}, "end": {"line": 35, "col": 152, "offset": 61}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/selected_rows_actions.html", "start": {"line": 45, "col": 84, "offset": 0}, "end": {"line": 45, "col": 145, "offset": 61}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/tabulator_header_v1.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 37, "offset": 36}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/tabulator_header_v1.html:1:\n `{% load sb_admin_tags i18n static %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/tabulator_header_v1.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/tabulator_header_v1.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 37, "offset": 36}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/tabulator_header_v2.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 37, "offset": 36}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/tabulator_header_v2.html:1:\n `{% load sb_admin_tags i18n static %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/tabulator_header_v2.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/tabulator_header_v2.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 37, "offset": 36}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/translations_status_row.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 18, "offset": 17}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/translations_status_row.html:1:\n `{% load static %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/translations_status_row.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/translations_status_row.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 18, "offset": 17}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/translations-detail.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 22, "offset": 115}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/translations-detail.html", "start": {"line": 7, "col": 1, "offset": 0}, "end": {"line": 9, "col": 20, "offset": 35}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/translations-detail.html", "start": {"line": 88, "col": 1, "offset": 0}, "end": {"line": 88, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/translations-detail.html:1:\n `{% extends \"sb_admin/sb_admin_base.html\" %}\n{% load i18n static %}\n\n{% block additional_js %}\n    {{ block.super }}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/translations-detail.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/translations-detail.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 22, "offset": 115}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/translations-detail.html", "start": {"line": 7, "col": 1, "offset": 0}, "end": {"line": 9, "col": 20, "offset": 35}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/translations-detail.html", "start": {"line": 88, "col": 1, "offset": 0}, "end": {"line": 88, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/translations.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 80}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/translations.html", "start": {"line": 28, "col": 1, "offset": 0}, "end": {"line": 28, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/translations.html:1:\n `{% extends \"sb_admin/sb_admin_base.html\" %}\n{% load i18n %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/translations.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/translations.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 80}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/translations.html", "start": {"line": 28, "col": 1, "offset": 0}, "end": {"line": 28, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/login.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 55, "offset": 170}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/login.html", "start": {"line": 12, "col": 5, "offset": 0}, "end": {"line": 13, "col": 35, "offset": 46}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/login.html", "start": {"line": 19, "col": 5, "offset": 0}, "end": {"line": 19, "col": 16, "offset": 11}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/login.html", "start": {"line": 58, "col": 1, "offset": 0}, "end": {"line": 58, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/login.html:1:\n `{% extends \"sb_admin/authentification/login_base.html\" %}\n{% load i18n static sb_admin_tags %}\n\n{% block content %}\n    {% if form.errors and not form.non_field_errors %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/login.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/login.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 55, "offset": 170}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/login.html", "start": {"line": 12, "col": 5, "offset": 0}, "end": {"line": 13, "col": 35, "offset": 46}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/login.html", "start": {"line": 19, "col": 5, "offset": 0}, "end": {"line": 19, "col": 16, "offset": 11}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/login.html", "start": {"line": 58, "col": 1, "offset": 0}, "end": {"line": 58, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/logout.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 101}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/logout.html", "start": {"line": 10, "col": 1, "offset": 0}, "end": {"line": 10, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/logout.html:1:\n `{% extends \"sb_admin/authentification/login_base.html\" %}\n{% load i18n static %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/logout.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/logout.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 101}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/logout.html", "start": {"line": 10, "col": 1, "offset": 0}, "end": {"line": 10, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/password_change_done.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 25, "offset": 110}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/password_change_done.html", "start": {"line": 10, "col": 1, "offset": 0}, "end": {"line": 10, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/password_change_done.html:1:\n `{% extends \"sb_admin/sb_admin_base_no_sidebar.html\" %}\n{% load sb_admin_tags i18n %}\n\n{% block main_wrapper %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/password_change_done.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/password_change_done.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 25, "offset": 110}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/password_change_done.html", "start": {"line": 10, "col": 1, "offset": 0}, "end": {"line": 10, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/password_reset_complete.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 115}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/password_reset_complete.html", "start": {"line": 10, "col": 1, "offset": 0}, "end": {"line": 10, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/password_reset_complete.html:1:\n `{% extends \"sb_admin/authentification/login_base.html\" %}\n{% load i18n static sb_admin_tags %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/password_reset_complete.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/password_reset_complete.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 20, "offset": 115}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/password_reset_complete.html", "start": {"line": 10, "col": 1, "offset": 0}, "end": {"line": 10, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/components/columns.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 16, "offset": 15}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/components/columns.html:1:\n `{% load i18n %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/components/columns.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/components/columns.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 16, "offset": 15}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/components/filters.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 16, "offset": 15}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/components/filters.html:1:\n `{% load i18n %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/components/filters.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/components/filters.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 16, "offset": 15}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/components/filters_v2.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 20, "offset": 57}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/components/filters_v2.html", "start": {"line": 16, "col": 5, "offset": 0}, "end": {"line": 16, "col": 19, "offset": 14}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/components/filters_v2.html", "start": {"line": 124, "col": 1, "offset": 0}, "end": {"line": 124, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/components/filters_v2.html:1:\n `{% load i18n static sb_admin_tags %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/components/filters_v2.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/components/filters_v2.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 20, "offset": 57}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/components/filters_v2.html", "start": {"line": 16, "col": 5, "offset": 0}, "end": {"line": 16, "col": 19, "offset": 14}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/components/filters_v2.html", "start": {"line": 124, "col": 1, "offset": 0}, "end": {"line": 124, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/components/inputs.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 23, "offset": 22}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/components/inputs.html:1:\n `{% load static i18n %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/components/inputs.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/components/inputs.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 23, "offset": 22}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/config/view.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 16, "offset": 15}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/config/view.html:1:\n `{% load i18n %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/config/view.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/config/view.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 16, "offset": 15}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/dashboard/calendar_widget.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 26, "offset": 150}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/dashboard/calendar_widget.html", "start": {"line": 35, "col": 1, "offset": 0}, "end": {"line": 35, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/dashboard/calendar_widget.html:1:\n `{% extends \"sb_admin/dashboard/widget_base.html\" %}\n{% load sb_admin_tags static i18n %}\n\n{% block filters %}{% endblock %}\n\n{% block content_inner %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/dashboard/calendar_widget.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/dashboard/calendar_widget.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 26, "offset": 150}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/dashboard/calendar_widget.html", "start": {"line": 35, "col": 1, "offset": 0}, "end": {"line": 35, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/components.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 25, "offset": 95}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/components.html", "start": {"line": 446, "col": 1, "offset": 0}, "end": {"line": 446, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/components.html:1:\n `{% extends 'sb_admin/sb_admin_base_no_sidebar.html' %}\n{% load i18n %}\n{% block main_wrapper %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/components.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/components.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 25, "offset": 95}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/components.html", "start": {"line": 446, "col": 1, "offset": 0}, "end": {"line": 446, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/confirmation.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 33, "offset": 56}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/confirmation.html", "start": {"line": 35, "col": 1, "offset": 0}, "end": {"line": 35, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/confirmation.html:1:\n `{% load i18n static %}\n\n{% block confirmation_content %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/confirmation.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/confirmation.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 33, "offset": 56}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/confirmation.html", "start": {"line": 35, "col": 1, "offset": 0}, "end": {"line": 35, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/inline_fieldset.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 25, "offset": 90}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/inline_fieldset.html", "start": {"line": 25, "col": 9, "offset": 0}, "end": {"line": 29, "col": 35, "offset": 85}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/inline_fieldset.html", "start": {"line": 31, "col": 42, "offset": 0}, "end": {"line": 31, "col": 48, "offset": 6}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/inline_fieldset.html", "start": {"line": 50, "col": 42, "offset": 0}, "end": {"line": 50, "col": 48, "offset": 6}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/inline_fieldset.html", "start": {"line": 52, "col": 9, "offset": 0}, "end": {"line": 58, "col": 15, "offset": 101}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/inline_fieldset.html:1:\n `{% load sb_admin_tags %}\n\n{% block content %}\n\t{% block header %}\n\t\t{% if fieldset.name %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/inline_fieldset.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/inline_fieldset.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 25, "offset": 90}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/inline_fieldset.html", "start": {"line": 25, "col": 9, "offset": 0}, "end": {"line": 29, "col": 35, "offset": 85}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/inline_fieldset.html", "start": {"line": 31, "col": 42, "offset": 0}, "end": {"line": 31, "col": 48, "offset": 6}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/inline_fieldset.html", "start": {"line": 50, "col": 42, "offset": 0}, "end": {"line": 50, "col": 48, "offset": 6}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/inline_fieldset.html", "start": {"line": 52, "col": 9, "offset": 0}, "end": {"line": 58, "col": 15, "offset": 101}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/table_inline_delete_button.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 40, "offset": 113}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/table_inline_delete_button.html", "start": {"line": 5, "col": 134, "offset": 0}, "end": {"line": 5, "col": 139, "offset": 5}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/table_inline_delete_button.html", "start": {"line": 14, "col": 5, "offset": 0}, "end": {"line": 14, "col": 15, "offset": 10}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/table_inline_delete_button.html", "start": {"line": 16, "col": 124, "offset": 0}, "end": {"line": 17, "col": 41, "offset": 46}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/table_inline_delete_button.html", "start": {"line": 23, "col": 5, "offset": 0}, "end": {"line": 24, "col": 12, "offset": 23}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/table_inline_delete_button.html:1:\n `{% load widget_tweaks %}\n{% if inline_admin_formset.formset.can_delete %}\n    {% if inline_admin_form.original %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/table_inline_delete_button.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/table_inline_delete_button.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 40, "offset": 113}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/table_inline_delete_button.html", "start": {"line": 5, "col": 134, "offset": 0}, "end": {"line": 5, "col": 139, "offset": 5}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/table_inline_delete_button.html", "start": {"line": 14, "col": 5, "offset": 0}, "end": {"line": 14, "col": 15, "offset": 10}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/table_inline_delete_button.html", "start": {"line": 16, "col": 124, "offset": 0}, "end": {"line": 17, "col": 41, "offset": 46}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/table_inline_delete_button.html", "start": {"line": 23, "col": 5, "offset": 0}, "end": {"line": 24, "col": 12, "offset": 23}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/stacked_inline.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 52, "offset": 208}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/stacked_inline.html", "start": {"line": 21, "col": 140, "offset": 0}, "end": {"line": 21, "col": 145, "offset": 5}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/stacked_inline.html", "start": {"line": 65, "col": 130, "offset": 0}, "end": {"line": 65, "col": 135, "offset": 5}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/stacked_inline.html", "start": {"line": 74, "col": 145, "offset": 0}, "end": {"line": 74, "col": 176, "offset": 31}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/stacked_inline.html", "start": {"line": 114, "col": 1, "offset": 0}, "end": {"line": 116, "col": 87, "offset": 157}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/stacked_inline.html:1:\n `{% load i18n nested_admin admin_urls widget_tweaks %}\n{% block extra_style %}{% endblock %}\n\n{% with inline_admin_formset.formset.is_nested as is_nested %}\n\n{% with inline_admin_formset.opts as inline_opts %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/stacked_inline.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/stacked_inline.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 52, "offset": 208}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/stacked_inline.html", "start": {"line": 21, "col": 140, "offset": 0}, "end": {"line": 21, "col": 145, "offset": 5}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/stacked_inline.html", "start": {"line": 65, "col": 130, "offset": 0}, "end": {"line": 65, "col": 135, "offset": 5}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/stacked_inline.html", "start": {"line": 74, "col": 145, "offset": 0}, "end": {"line": 74, "col": 176, "offset": 31}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/stacked_inline.html", "start": {"line": 114, "col": 1, "offset": 0}, "end": {"line": 116, "col": 87, "offset": 157}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 56, "offset": 327}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline.html", "start": {"line": 23, "col": 77, "offset": 0}, "end": {"line": 23, "col": 138, "offset": 61}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline.html", "start": {"line": 28, "col": 33, "offset": 0}, "end": {"line": 32, "col": 84, "offset": 343}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline.html", "start": {"line": 36, "col": 33, "offset": 0}, "end": {"line": 36, "col": 34, "offset": 1}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline.html", "start": {"line": 43, "col": 160, "offset": 0}, "end": {"line": 43, "col": 165, "offset": 5}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline.html", "start": {"line": 103, "col": 128, "offset": 0}, "end": {"line": 112, "col": 247, "offset": 1172}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline.html", "start": {"line": 224, "col": 13, "offset": 0}, "end": {"line": 228, "col": 87, "offset": 191}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline.html:1:\n `{% load admin_urlname admin_urlquote from admin_urls %}\n{% load i18n nested_admin static widget_tweaks sb_admin_tags %}\n\n{% call_method inline_admin_formset.opts \"get_context_data\" request as context_data %}\n{% with inline_admin_formset.formset.is_nested as is_nested %}\n\n    {% with inline_admin_formset.opts as inline_opts %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 56, "offset": 327}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline.html", "start": {"line": 23, "col": 77, "offset": 0}, "end": {"line": 23, "col": 138, "offset": 61}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline.html", "start": {"line": 28, "col": 33, "offset": 0}, "end": {"line": 32, "col": 84, "offset": 343}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline.html", "start": {"line": 36, "col": 33, "offset": 0}, "end": {"line": 36, "col": 34, "offset": 1}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline.html", "start": {"line": 43, "col": 160, "offset": 0}, "end": {"line": 43, "col": 165, "offset": 5}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline.html", "start": {"line": 103, "col": 128, "offset": 0}, "end": {"line": 112, "col": 247, "offset": 1172}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline.html", "start": {"line": 224, "col": 13, "offset": 0}, "end": {"line": 228, "col": 87, "offset": 191}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline_paginated.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 61, "offset": 193}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline_paginated.html", "start": {"line": 13, "col": 21, "offset": 0}, "end": {"line": 13, "col": 260, "offset": 239}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline_paginated.html", "start": {"line": 15, "col": 21, "offset": 0}, "end": {"line": 17, "col": 58, "offset": 120}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline_paginated.html", "start": {"line": 22, "col": 52, "offset": 0}, "end": {"line": 22, "col": 58, "offset": 6}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline_paginated.html", "start": {"line": 30, "col": 44, "offset": 0}, "end": {"line": 30, "col": 112, "offset": 68}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline_paginated.html", "start": {"line": 47, "col": 21, "offset": 0}, "end": {"line": 47, "col": 252, "offset": 231}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline_paginated.html", "start": {"line": 49, "col": 21, "offset": 0}, "end": {"line": 49, "col": 78, "offset": 57}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline_paginated.html", "start": {"line": 52, "col": 9, "offset": 0}, "end": {"line": 54, "col": 15, "offset": 39}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline_paginated.html:1:\n `{% extends 'sb_admin/inlines/table_inline.html' %}\n{% load i18n paginated_inline %}\n\n{% block card_content %}\n    {{ block.super }}\n\n    {% with inline_admin_formset.formset.page as page_obj %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline_paginated.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline_paginated.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 61, "offset": 193}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline_paginated.html", "start": {"line": 13, "col": 21, "offset": 0}, "end": {"line": 13, "col": 260, "offset": 239}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline_paginated.html", "start": {"line": 15, "col": 21, "offset": 0}, "end": {"line": 17, "col": 58, "offset": 120}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline_paginated.html", "start": {"line": 22, "col": 52, "offset": 0}, "end": {"line": 22, "col": 58, "offset": 6}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline_paginated.html", "start": {"line": 30, "col": 44, "offset": 0}, "end": {"line": 30, "col": 112, "offset": 68}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline_paginated.html", "start": {"line": 47, "col": 21, "offset": 0}, "end": {"line": 47, "col": 252, "offset": 231}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline_paginated.html", "start": {"line": 49, "col": 21, "offset": 0}, "end": {"line": 49, "col": 78, "offset": 57}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline_paginated.html", "start": {"line": 52, "col": 9, "offset": 0}, "end": {"line": 54, "col": 15, "offset": 39}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/page_list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 8, "col": 19, "offset": 297}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/page_list.html", "start": {"line": 13, "col": 1, "offset": 0}, "end": {"line": 18, "col": 20, "offset": 142}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/page_list.html", "start": {"line": 41, "col": 150, "offset": 0}, "end": {"line": 41, "col": 155, "offset": 5}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/page_list.html", "start": {"line": 68, "col": 86, "offset": 0}, "end": {"line": 68, "col": 110, "offset": 24}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/page_list.html", "start": {"line": 91, "col": 54, "offset": 0}, "end": {"line": 91, "col": 55, "offset": 1}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/page_list.html", "start": {"line": 157, "col": 25, "offset": 0}, "end": {"line": 185, "col": 43, "offset": 2183}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/page_list.html", "start": {"line": 196, "col": 52, "offset": 0}, "end": {"line": 196, "col": 226, "offset": 174}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/page_list.html", "start": {"line": 208, "col": 100, "offset": 0}, "end": {"line": 208, "col": 101, "offset": 1}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/page_list.html", "start": {"line": 220, "col": 21, "offset": 0}, "end": {"line": 249, "col": 39, "offset": 2122}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/page_list.html", "start": {"line": 260, "col": 48, "offset": 0}, "end": {"line": 260, "col": 222, "offset": 174}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/page_list.html", "start": {"line": 270, "col": 96, "offset": 0}, "end": {"line": 272, "col": 77, "offset": 148}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/page_list.html", "start": {"line": 281, "col": 13, "offset": 0}, "end": {"line": 281, "col": 48, "offset": 35}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/page_list.html", "start": {"line": 283, "col": 9, "offset": 0}, "end": {"line": 286, "col": 15, "offset": 55}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/page_list.html:1:\n `{% extends list_base_template|default:\"sb_admin/sb_admin_base.html\" %}\n{% load i18n l10n admin_list static admin_urls admin_urls cms_admin cms_js_tags cms_static cms_tags %}\n\n{% block title %}{% trans \"List of pages\" %}{% endblock %}\n\n{% block extrahead %}\n    {{ block.super }}\n    {{ media.js }}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/page_list.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/page_list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 8, "col": 19, "offset": 297}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/page_list.html", "start": {"line": 13, "col": 1, "offset": 0}, "end": {"line": 18, "col": 20, "offset": 142}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/page_list.html", "start": {"line": 41, "col": 150, "offset": 0}, "end": {"line": 41, "col": 155, "offset": 5}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/page_list.html", "start": {"line": 68, "col": 86, "offset": 0}, "end": {"line": 68, "col": 110, "offset": 24}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/page_list.html", "start": {"line": 91, "col": 54, "offset": 0}, "end": {"line": 91, "col": 55, "offset": 1}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/page_list.html", "start": {"line": 157, "col": 25, "offset": 0}, "end": {"line": 185, "col": 43, "offset": 2183}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/page_list.html", "start": {"line": 196, "col": 52, "offset": 0}, "end": {"line": 196, "col": 226, "offset": 174}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/page_list.html", "start": {"line": 208, "col": 100, "offset": 0}, "end": {"line": 208, "col": 101, "offset": 1}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/page_list.html", "start": {"line": 220, "col": 21, "offset": 0}, "end": {"line": 249, "col": 39, "offset": 2122}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/page_list.html", "start": {"line": 260, "col": 48, "offset": 0}, "end": {"line": 260, "col": 222, "offset": 174}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/page_list.html", "start": {"line": 270, "col": 96, "offset": 0}, "end": {"line": 272, "col": 77, "offset": 148}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/page_list.html", "start": {"line": 281, "col": 13, "offset": 0}, "end": {"line": 281, "col": 48, "offset": 35}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/page_list.html", "start": {"line": 283, "col": 9, "offset": 0}, "end": {"line": 286, "col": 15, "offset": 55}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/translations_status_row.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 22, "offset": 111}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/translations_status_row.html", "start": {"line": 10, "col": 1, "offset": 0}, "end": {"line": 10, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/translations_status_row.html:1:\n `{% extends \"sb_admin/actions/partials/translations_status_row.html\" %}\n{% load static %}\n\n{% block edit_link %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/translations_status_row.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/translations_status_row.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 22, "offset": 111}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/translations_status_row.html", "start": {"line": 10, "col": 1, "offset": 0}, "end": {"line": 10, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/filer_change_form.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 11, "col": 22, "offset": 316}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/filer_change_form.html", "start": {"line": 33, "col": 13, "offset": 0}, "end": {"line": 37, "col": 20, "offset": 87}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/filer_change_form.html", "start": {"line": 91, "col": 1, "offset": 0}, "end": {"line": 91, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/filer_change_form.html:1:\n `{% extends list_base_template|default:\"sb_admin/sb_admin_base.html\" %}\n{% load i18n admin_modify static filer_admin_tags %}\n\n{% block breadcrumbs %}\n    {% with original as instance %}\n        {% include \"admin/filer/breadcrumbs.html\" %}\n    {% endwith %}\n{% endblock %}\n\n{% block extrastyle %}\n    {{ block.super }}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/filer_change_form.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/filer_change_form.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 11, "col": 22, "offset": 316}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/filer_change_form.html", "start": {"line": 33, "col": 13, "offset": 0}, "end": {"line": 37, "col": 20, "offset": 87}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/filer_change_form.html", "start": {"line": 91, "col": 1, "offset": 0}, "end": {"line": 91, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/folder_list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 19, "offset": 174}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/folder_list.html", "start": {"line": 34, "col": 5, "offset": 0}, "end": {"line": 34, "col": 65, "offset": 60}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/folder_list.html", "start": {"line": 42, "col": 5, "offset": 0}, "end": {"line": 48, "col": 22, "offset": 132}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/folder_list.html", "start": {"line": 54, "col": 5, "offset": 0}, "end": {"line": 55, "col": 23, "offset": 34}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/folder_list.html", "start": {"line": 61, "col": 5, "offset": 0}, "end": {"line": 61, "col": 16, "offset": 11}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/folder_list.html", "start": {"line": 80, "col": 1, "offset": 0}, "end": {"line": 83, "col": 26, "offset": 61}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/folder_list.html", "start": {"line": 124, "col": 5, "offset": 0}, "end": {"line": 124, "col": 16, "offset": 11}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/folder_list.html", "start": {"line": 345, "col": 1, "offset": 0}, "end": {"line": 345, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/folder_list.html:1:\n `{% extends list_base_template|default:\"sb_admin/sb_admin_base.html\" %}\n{% load i18n static filer_admin_tags %}\n\n{% block extrahead %}\n    {{ block.super }}\n    {{ media.js }}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/folder_list.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/folder_list.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 6, "col": 19, "offset": 174}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/folder_list.html", "start": {"line": 34, "col": 5, "offset": 0}, "end": {"line": 34, "col": 65, "offset": 60}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/folder_list.html", "start": {"line": 42, "col": 5, "offset": 0}, "end": {"line": 48, "col": 22, "offset": 132}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/folder_list.html", "start": {"line": 54, "col": 5, "offset": 0}, "end": {"line": 55, "col": 23, "offset": 34}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/folder_list.html", "start": {"line": 61, "col": 5, "offset": 0}, "end": {"line": 61, "col": 16, "offset": 11}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/folder_list.html", "start": {"line": 80, "col": 1, "offset": 0}, "end": {"line": 83, "col": 26, "offset": 61}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/folder_list.html", "start": {"line": 124, "col": 5, "offset": 0}, "end": {"line": 124, "col": 16, "offset": 11}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/folder_list.html", "start": {"line": 345, "col": 1, "offset": 0}, "end": {"line": 345, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/image_change_form.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 23, "offset": 198}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/image_change_form.html", "start": {"line": 30, "col": 1, "offset": 0}, "end": {"line": 33, "col": 37, "offset": 77}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/image_change_form.html", "start": {"line": 40, "col": 5, "offset": 0}, "end": {"line": 42, "col": 15, "offset": 81}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/image_change_form.html:1:\n `{% extends list_base_template|default:\"sb_admin/integrations/filer/filer_change_form.html\" %}\n{% load i18n admin_modify static %}\n\n{% block extrahead %}\n    {{ block.super }}\n\n    {# upload stuff #}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/image_change_form.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/image_change_form.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 7, "col": 23, "offset": 198}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/image_change_form.html", "start": {"line": 30, "col": 1, "offset": 0}, "end": {"line": 33, "col": 37, "offset": 77}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/image_change_form.html", "start": {"line": 40, "col": 5, "offset": 0}, "end": {"line": 42, "col": 15, "offset": 81}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/messages/alert_base.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 20, "offset": 19}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/messages/alert_base.html", "start": {"line": 19, "col": 1, "offset": 0}, "end": {"line": 19, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/messages/alert_base.html:1:\n `{% block content %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/messages/alert_base.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/messages/alert_base.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 20, "offset": 19}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/messages/alert_base.html", "start": {"line": 19, "col": 1, "offset": 0}, "end": {"line": 19, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/messages/alert_error.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 17, "offset": 162}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/messages/alert_error.html", "start": {"line": 9, "col": 1, "offset": 0}, "end": {"line": 9, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/messages/alert_error.html:1:\n `{% extends 'sb_admin/partials/messages/alert_base.html' %}\n\n{% block colors %}bg-negative-50 border-negative-100 text-negative-900{% endblock %}\n\n{% block icon %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/messages/alert_error.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/messages/alert_error.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 17, "offset": 162}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/messages/alert_error.html", "start": {"line": 9, "col": 1, "offset": 0}, "end": {"line": 9, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/messages/alert_info.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 17, "offset": 156}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/messages/alert_info.html", "start": {"line": 9, "col": 1, "offset": 0}, "end": {"line": 9, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/messages/alert_info.html:1:\n `{% extends 'sb_admin/partials/messages/alert_base.html' %}\n\n{% block colors %}bg-notice-50 border-notice-100 text-notice-900{% endblock %}\n\n{% block icon %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/messages/alert_info.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/messages/alert_info.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 17, "offset": 156}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/messages/alert_info.html", "start": {"line": 9, "col": 1, "offset": 0}, "end": {"line": 9, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/modal/modal_content.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 20, "offset": 36}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/modal/modal_content.html", "start": {"line": 57, "col": 1, "offset": 0}, "end": {"line": 57, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/modal/modal_content.html:1:\n `{% load i18n %}\n\n{% block content %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/modal/modal_content.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/modal/modal_content.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 20, "offset": 36}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/modal/modal_content.html", "start": {"line": 57, "col": 1, "offset": 0}, "end": {"line": 57, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/sb_admin_base.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 72, "offset": 177}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/sb_admin_base.html", "start": {"line": 49, "col": 1, "offset": 0}, "end": {"line": 49, "col": 15, "offset": 14}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/sb_admin_base.html:1:\n `{% extends 'sb_admin/sb_admin_base_no_sidebar.html' %}\n{% load sb_admin_tags %}\n\n{% block main_wrapper %}\n    {% get_item request.META 'HTTP_SEC_FETCH_SITE' as sec_fetch_site %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/sb_admin_base.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/sb_admin_base.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 5, "col": 72, "offset": 177}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/sb_admin_base.html", "start": {"line": 49, "col": 1, "offset": 0}, "end": {"line": 49, "col": 15, "offset": 14}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/sb_admin_base_no_sidebar.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 18, "offset": 17}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/sb_admin_base_no_sidebar.html:1:\n `{% load static %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/sb_admin_base_no_sidebar.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/sb_admin_base_no_sidebar.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 18, "offset": 17}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/submit_line.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 22, "offset": 70}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/submit_line.html", "start": {"line": 7, "col": 1, "offset": 0}, "end": {"line": 9, "col": 26, "offset": 38}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/submit_line.html", "start": {"line": 16, "col": 1, "offset": 0}, "end": {"line": 18, "col": 26, "offset": 38}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/submit_line.html", "start": {"line": 18, "col": 128, "offset": 0}, "end": {"line": 19, "col": 35, "offset": 44}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/submit_line.html", "start": {"line": 19, "col": 147, "offset": 0}, "end": {"line": 20, "col": 32, "offset": 43}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/submit_line.html", "start": {"line": 20, "col": 146, "offset": 0}, "end": {"line": 22, "col": 26, "offset": 38}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/submit_line.html", "start": {"line": 59, "col": 1, "offset": 0}, "end": {"line": 60, "col": 19, "offset": 59}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/submit_line.html", "start": {"line": 60, "col": 108, "offset": 0}, "end": {"line": 60, "col": 119, "offset": 11}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/submit_line.html:1:\n `{% load i18n admin_urls %}\n\n{# desktop button #}\n{% if show_buttons %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/submit_line.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/submit_line.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 4, "col": 22, "offset": 70}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/submit_line.html", "start": {"line": 7, "col": 1, "offset": 0}, "end": {"line": 9, "col": 26, "offset": 38}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/submit_line.html", "start": {"line": 16, "col": 1, "offset": 0}, "end": {"line": 18, "col": 26, "offset": 38}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/submit_line.html", "start": {"line": 18, "col": 128, "offset": 0}, "end": {"line": 19, "col": 35, "offset": 44}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/submit_line.html", "start": {"line": 19, "col": 147, "offset": 0}, "end": {"line": 20, "col": 32, "offset": 43}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/submit_line.html", "start": {"line": 20, "col": 146, "offset": 0}, "end": {"line": 22, "col": 26, "offset": 38}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/submit_line.html", "start": {"line": 59, "col": 1, "offset": 0}, "end": {"line": 60, "col": 19, "offset": 59}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/submit_line.html", "start": {"line": 60, "col": 108, "offset": 0}, "end": {"line": 60, "col": 119, "offset": 11}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/array.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 59, "offset": 75}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/array.html", "start": {"line": 61, "col": 1, "offset": 0}, "end": {"line": 61, "col": 57, "offset": 56}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/array.html:1:\n `{% load i18n %}\n\n{% include 'sb_admin/widgets/includes/field_label.html' %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/array.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/array.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 59, "offset": 75}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/array.html", "start": {"line": 61, "col": 1, "offset": 0}, "end": {"line": 61, "col": 57, "offset": 56}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/attributes.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 59, "offset": 75}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/attributes.html", "start": {"line": 68, "col": 1, "offset": 0}, "end": {"line": 68, "col": 57, "offset": 56}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/attributes.html:1:\n `{% load i18n %}\n\n{% include 'sb_admin/widgets/includes/field_label.html' %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/attributes.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/attributes.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 59, "offset": 75}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/attributes.html", "start": {"line": 68, "col": 1, "offset": 0}, "end": {"line": 68, "col": 57, "offset": 56}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/checkbox_select.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 16, "offset": 15}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/checkbox_select.html", "start": {"line": 40, "col": 1, "offset": 0}, "end": {"line": 40, "col": 57, "offset": 56}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/checkbox_select.html:1:\n `{% load i18n %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/checkbox_select.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/checkbox_select.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 16, "offset": 15}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/checkbox_select.html", "start": {"line": 40, "col": 1, "offset": 0}, "end": {"line": 40, "col": 57, "offset": 56}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/clearable_file_input.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 59, "offset": 99}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/clearable_file_input.html:1:\n `{% load i18n thumbnail sb_admin_tags %}\n\n{% include 'sb_admin/widgets/includes/field_label.html' %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/clearable_file_input.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/clearable_file_input.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 59, "offset": 99}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/date.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 59, "offset": 58}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/date.html", "start": {"line": 10, "col": 1, "offset": 0}, "end": {"line": 10, "col": 57, "offset": 56}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/date.html:1:\n `{% include 'sb_admin/widgets/includes/field_label.html' %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/date.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/date.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 59, "offset": 58}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/date.html", "start": {"line": 10, "col": 1, "offset": 0}, "end": {"line": 10, "col": 57, "offset": 56}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/filer_file.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 16, "offset": 56}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/filer_file.html", "start": {"line": 84, "col": 1, "offset": 0}, "end": {"line": 84, "col": 19, "offset": 18}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/filer_file.html:1:\n `{% load i18n filer_admin_tags static %}\n\n{% spaceless %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/filer_file.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/filer_file.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 16, "offset": 56}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/filer_file.html", "start": {"line": 84, "col": 1, "offset": 0}, "end": {"line": 84, "col": 19, "offset": 18}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/includes/related_item_buttons.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 16, "offset": 15}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/includes/related_item_buttons.html:1:\n `{% load i18n %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/includes/related_item_buttons.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/includes/related_item_buttons.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 16, "offset": 15}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/password.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 59, "offset": 58}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/password.html", "start": {"line": 13, "col": 1, "offset": 0}, "end": {"line": 13, "col": 57, "offset": 56}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/password.html:1:\n `{% include 'sb_admin/widgets/includes/field_label.html' %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/password.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/password.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 59, "offset": 58}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/password.html", "start": {"line": 13, "col": 1, "offset": 0}, "end": {"line": 13, "col": 57, "offset": 56}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/time.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 59, "offset": 58}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/time.html", "start": {"line": 10, "col": 1, "offset": 0}, "end": {"line": 10, "col": 57, "offset": 56}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/time.html:1:\n `{% include 'sb_admin/widgets/includes/field_label.html' %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/time.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/time.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 59, "offset": 58}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/time.html", "start": {"line": 10, "col": 1, "offset": 0}, "end": {"line": 10, "col": 57, "offset": 56}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/tree_base.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 30, "offset": 67}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/tree_base.html", "start": {"line": 15, "col": 1, "offset": 0}, "end": {"line": 15, "col": 12, "offset": 11}}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/tree_base.html", "start": {"line": 55, "col": 1, "offset": 0}, "end": {"line": 55, "col": 76, "offset": 75}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/tree_base.html:1:\n `{% load static sb_admin_tags i18n %}\n\n{% if not hide_tree_search %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/tree_base.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/tree_base.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 3, "col": 30, "offset": 67}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/tree_base.html", "start": {"line": 15, "col": 1, "offset": 0}, "end": {"line": 15, "col": 12, "offset": 11}}, {"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/tree_base.html", "start": {"line": 55, "col": 1, "offset": 0}, "end": {"line": 55, "col": 76, "offset": 75}}]}, {"code": 3, "level": "warn", "type": ["PartialParsing", [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/tree_select.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 25, "offset": 24}}]], "message": "Syntax error at line downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/tree_select.html:1:\n `{% load sb_admin_tags %}` was unexpected", "path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/tree_select.html", "spans": [{"file": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/tree_select.html", "start": {"line": 1, "col": 1, "offset": 0}, "end": {"line": 1, "col": 25, "offset": 24}}]}], "paths": {"scanned": ["downloaded_repos/SmartBase-SK_django-smartbase-admin/.eslintignore", "downloaded_repos/SmartBase-SK_django-smartbase-admin/.eslintrc.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/.github/workflows/workflow.yml", "downloaded_repos/SmartBase-SK_django-smartbase-admin/.gitignore", "downloaded_repos/SmartBase-SK_django-smartbase-admin/LICENSE.md", "downloaded_repos/SmartBase-SK_django-smartbase-admin/README.md", "downloaded_repos/SmartBase-SK_django-smartbase-admin/gulpfile.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/package.json", "downloaded_repos/SmartBase-SK_django-smartbase-admin/poetry.lock", "downloaded_repos/SmartBase-SK_django-smartbase-admin/pyproject.toml", "downloaded_repos/SmartBase-SK_django-smartbase-admin/requirements.txt", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/__init__.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/__init__.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/actions/__init__.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/actions/admin_action_list.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/actions/advanced_filters.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/admin/__init__.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/admin/admin_base.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/admin/site.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/admin/widgets.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/apps.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/compilemessages.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/engine/__init__.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/engine/actions.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/engine/admin_base_view.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/engine/admin_entrypoint_view.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/engine/admin_view.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/engine/configuration.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/engine/const.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/engine/dashboard.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/engine/fake_inline.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/engine/field.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/engine/field_formatter.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/engine/filter_widgets.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/engine/global_filter_form.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/engine/menu_item.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/engine/modal_view.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/engine/request.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/integration/__init__.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/integration/django_cms.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/locale/sk/LC_MESSAGES/django.mo", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/locale/sk/LC_MESSAGES/django.po", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/makemessages.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/migrations/0001_initial.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/migrations/0002_auto_20230402_2316.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/migrations/0003_auto_20230402_2328.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/migrations/0004_alter_sbadminlistviewconfiguration_action_and_more.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/migrations/__init__.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/models.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/monkeypatch/admin_readonly_field_monkeypatch.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/monkeypatch/fake_inline_monkeypatch.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/querysets.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/services/__init__.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/services/configuration.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/services/data.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/services/thread_local.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/services/translations.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/services/views.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/services/xlsx_export.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/css/codemirror/codemirror.min.css", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/css/codemirror/dracula.min.css", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/css/coloris/coloris.min.css", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/css/querybuilder/query-builder.default.min.css", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/favicon/android-chrome-192x192.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/favicon/android-chrome-512x512.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/favicon/apple-touch-icon.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/favicon/browserconfig.xml", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/favicon/favicon-16x16.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/favicon/favicon-32x32.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/favicon/favicon.ico", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/favicon/mstile-144x144.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/favicon/mstile-150x150.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/favicon/mstile-310x150.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/favicon/mstile-310x310.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/favicon/mstile-70x70.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/favicon/safari-pinned-tab.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/favicon/site.webmanifest", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/fonts/Inter-Ext.woff2", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/fonts/Inter.woff2", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/blank.gif", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/file_types/file-csv.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/file_types/file-doc.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/file_types/file-docx.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/file_types/file-other.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/file_types/file-pdf.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/file_types/file-ppt.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/file_types/file-xls.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/file_types/file-xlsx.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/file_types/file-zip.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ad.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ae.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/af.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ag.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ai.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/al.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/am.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/an.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ao.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ar.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/at.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/au.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/aw.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ax.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/az.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ba.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/bb.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/bd.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/be.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/bf.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/bg.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/bh.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/bi.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/bj.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/bm.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/bn.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/bo.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/br.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/bs.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/bt.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/bw.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/by.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/bz.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ca.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/caf.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/cas.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/cd.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ceu.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/cf.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/cg.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ch.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ci.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/cl.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/cm.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/cn.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/cna.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/co.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/coc.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/cr.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/cs.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/csa.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/cu.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/cv.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/cy.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/cz.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/de-at.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/de-ch.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/de.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/dj.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/dk.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/dm.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/do.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/dz.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ec.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ee.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/eg.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/en.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/er.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/es.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/et.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/eu.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/fi.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/fj.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/fk.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/fm.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/fr.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ga.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/gb.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/gd.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ge.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/gg.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/gh.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/gi.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/gm.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/gn.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/gq.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/gr.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/gt.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/gw.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/gy.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/hk.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/hn.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/hr.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ht.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/hu.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/id.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ie.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/il.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/im.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/in.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/iq.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ir.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/is.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/it.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/je.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/jm.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/jo.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/jp.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ke.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/kg.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/kh.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/km.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/kn.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/kp.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/kr.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/kw.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ky.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/kz.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/la.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/lb.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/lc.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/li.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/lk.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/lr.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ls.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/lt.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/lu.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/lv.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ly.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ma.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/mc.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/md.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/me.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/mg.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/mk.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ml.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/mm.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/mn.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/mo.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/mr.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ms.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/mt.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/mu.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/mv.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/mw.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/mx.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/my.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/mz.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/na.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ne.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ng.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ni.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/nl.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/no.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/np.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/nz.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/om.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/pa.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/pe.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/pf.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/pg.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ph.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/pk.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/pl.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/pr.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/pt.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/pw.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/py.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/qa.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ro.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/rs.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ru.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/rw.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/sa.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/sb.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/sc.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/sd.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/se.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/sg.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/sh.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/si.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/sk.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/sl.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/sm.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/sn.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/so.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/sr.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/st.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/sv.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/sy.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/sz.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/tc.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/td.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/tg.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/th.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/tj.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/tl.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/tm.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/tn.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/to.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/tr.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/tt.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/tw.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/tz.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ua.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ug.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/us.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/uy.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/uz.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/vc.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ve.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/vg.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/vn.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/vu.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ws.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ww.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/ye.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/za.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/zm.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/flags/zw.png", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/login.webp", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/logo.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/sk.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/js/prices.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/js/querybuilder/jQuery.extendext.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/js/remove-me.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Accept-email.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Ad-product.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Add-one.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Add-picture.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Add-three.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Aiming.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/All-application.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Alphabetical-sorting-two.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Alphabetical-sorting.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Application-menu.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Application-two.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Application.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Arrow-circle-down.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Arrow-circle-left.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Arrow-circle-right.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Arrow-circle-up.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/At-sign.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Attention.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Back-one.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Bank-card-one.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Bank-card.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Bolt-one.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Bookmark.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Box.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Calendar.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Camera.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Caution.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Check-correct.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Check-one-filled.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Check-one.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Check-small.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Check.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Close-one.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Close-small.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Close.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Column.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Corner-up-left.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Corner-up-right.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Cut.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Cylinder.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Delete-two.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Delete.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Double-down.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Double-left.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Double-right.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Double-up.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Down-c.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Down-small.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Down.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Download-one.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Download.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Drag.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Edit.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Electric-drill.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Excel-one.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Export.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Figma-component.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Filter.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Find.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Fire-extinguisher.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Gas.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Go-ahead.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Go-on.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Hamburger-button.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Headset-one.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Help.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Home.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Id-card-h.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Info.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Left-c.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Left-small-down.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Left-small-up.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Left-small.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Left.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Lightning-fill.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Lightning.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Like.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Link-two.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/List-checkbox.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Lock.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Login.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Logout.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Magic-wand.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Magic.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Mail-download.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Mail-open.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Mail.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Message-emoji.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Message-one.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Minus-the-top.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Minus.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/More-one.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/More-three.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/More-two.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/More.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Paperclip.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Parallel-gateway.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/People-top-card.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Percentage.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Phone-telephone.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Picture-one.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Pin Filled.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Pin.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Plus.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Preview-close-one.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Preview-close.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Preview-open.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Printer.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Pull.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Pushpin.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Reduce-one.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Refresh-one.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Return.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Rewora Filled.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Rewora.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Right-c.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Right-small-down.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Right-small-up.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Right-small.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Right.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Save.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Search.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Send-email.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Setting-config.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Setting-two.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Shop.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Shopping-bag.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Shopping-cart-one.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Shopping.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Sort Alt.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Sort-amount-down.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Sort-amount-up.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Sort-one.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Sort-three.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Sort.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Star.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Success.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Switch.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Table-report.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Tag-one.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Tag.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Time.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Tips-one.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/To-top.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Transfer-data.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Translate.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Translation.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Triangle-round-rectangle.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Truck.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Undo.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Unlock.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Up-c.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Up-small.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Up.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Upload-one.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Upload.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/User-business.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/View-grid-list.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Write.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Zoom-in.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/sprites/sb_admin/Zoom-out.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/css/_base.css", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/css/_choices.css", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/css/_colors.css", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/css/_components.css", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/css/_datepicker.css", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/css/_filer.css", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/css/_inlines.css", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/css/_nouislider.css", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/css/_tabulator.css", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/css/_tailwind_base.css", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/css/_utilities.css", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/css/calendar.css", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/css/components/_button.css", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/css/components/_dropdown.css", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/css/components/_input.css", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/css/components/_modal.css", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/css/components/_nav-tabs.css", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/css/components/_query-builder.css", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/css/components/_toggle.css", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/css/components/_tooltip.css", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/css/style.css", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/css/tree_widget.css", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/autocomplete.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/calendar.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/chart.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/choices.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/code.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/confirmation_modal.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/datepicker.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/datepicker_plugins.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/main.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/multiselect.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/range.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/sb_ajax_params_tabulator_modifier.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/sidebar.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/sorting.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/table.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/table_modules/advanced_filter_module.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/table_modules/base_module.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/table_modules/column_display_module.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/table_modules/data_edit_module.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/table_modules/detail_view_module.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/table_modules/filter_module.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/table_modules/full_text_search_module.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/table_modules/header_tabs_module.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/table_modules/movable_columns_module.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/table_modules/selection_module.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/table_modules/table_params_module.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/table_modules/views_module.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/translations.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/tree_widget.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/utils.js", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_form.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_password.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/dashboard.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_confirmation.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_selected_confirmation.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/list.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/media.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/object_history.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/action_link.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/open_modal_attrs.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/selected_rows_actions.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/tabulator_header_v1.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/tabulator_header_v2.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/translations_status_row.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/translations-detail.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/translations-language-choice.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/translations-list.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/translations.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/tree_list.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/auth/user/add_form.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/login.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/login_base.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/logout.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/password_change_done.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/password_change_form.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/password_reset_complete.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/password_reset_confirm.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/password_reset_done.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/password_reset_email.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/password_reset_form.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/blank_base.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/components/buttons.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/components/columns.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/components/filters.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/components/filters_v2.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/components/inputs.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/config/view.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/dashboard/calendar_widget.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/dashboard/chart_aggregate_sub_widget.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/dashboard/chart_widget.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/dashboard/list_widget.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/dashboard/widget_base.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/filter_widgets/advanced_filters/autocomplete_field.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/filter_widgets/advanced_filters/boolean_field.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/filter_widgets/advanced_filters/choice_field.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/filter_widgets/advanced_filters/date_field.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/filter_widgets/advanced_filters/multiple_choice_field.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/filter_widgets/advanced_filters/number_range_field.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/filter_widgets/advanced_filters/radio_choice_field.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/filter_widgets/advanced_filters/string_field.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/filter_widgets/advanced_filters/tree_select_filter.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/filter_widgets/autocomplete_field.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/filter_widgets/boolean_field.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/filter_widgets/choice_field.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/filter_widgets/date_field.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/filter_widgets/multiple_choice_field.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/filter_widgets/number_range_field.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/filter_widgets/partials/clear.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/filter_widgets/radio_choice_field.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/filter_widgets/string_field.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/filter_widgets/tree_select_filter.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/fonts.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/attrs.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/change_form_title.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/components.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/confirmation.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/fieldset.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/inline_fieldset.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/loading.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/loading_absolute.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/notifications.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/readonly_boolean_field.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/readonly_field.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/table_inline_delete_button.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/stacked_inline.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline_paginated.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/page_list.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/translations_status_row.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/filer_change_form.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/folder_list.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/image_change_form.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/sorting/change_list.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/navigation.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/messages/alert_base.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/messages/alert_close.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/messages/alert_error.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/messages/alert_info.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/messages/alert_success.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/messages/alert_warning.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/modal/modal.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/modal/modal_content.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/sb_admin_base.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/sb_admin_base_no_sidebar.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/sb_admin_js_trans.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/sprites/sb_admin.svg", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/submit_line.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/tailwind_whitelist.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/array.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/attributes.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/attrs.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/autocomplete.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/checkbox.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/checkbox_group.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/checkbox_option.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/checkbox_select.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/ckeditor.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/clearable_file_input.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/code.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/color_field.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/date.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/datetime.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/email.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/file.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/filer_file.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/hidden.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/html_read_only.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/includes/field_label.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/includes/help_text.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/includes/related_item_buttons.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/includes/simple_field_label.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/input.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/input_option.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/multiple_hidden.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/multiwidget.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/number.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/password.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/radio.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/radio_option.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/read_only_password_hash.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/select.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/select_date.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/select_option.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/splitdatetime.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/splithiddendatetime.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/text.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/textarea.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/time.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/toggle.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/tree_base.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/tree_select.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/tree_select_inline.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/url.html", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templatetags/__init__.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templatetags/base.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templatetags/sb_admin_tags.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/urls.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/utils.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/views/__init__.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/views/dashboard_view.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/views/global_filter_view.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/views/media_view.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/views/translations_view.py", "downloaded_repos/SmartBase-SK_django-smartbase-admin/yarn.lock"], "skipped": [{"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/dist/django_smartbase_admin-0.1.0-py3-none-any.whl", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/dist/django_smartbase_admin-0.1.0.tar.gz", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/build/postcss.config.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/build/tailwind.config.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/build/tailwind_config_partials/colors.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/build/tailwind_config_partials/screens.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/build/tailwind_config_partials/spacing.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/build/tailwind_config_partials/typography.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/build/webpack.analyze.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/build/webpack.common.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/build/webpack.dev.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/build/webpack.prod.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/fancytree/jquery.fancytree-all-deps.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/images/login.jpg", "reason": "exceeded_size_limit"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/js/codemirror/codemirror.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/js/codemirror/django.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/js/codemirror/overlay.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/js/coloris/coloris.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/js/fullcalendar.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/js/htmx.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/js/jquery-3.7.1.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/js/jquery.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/js/lazysizes.bgset.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/js/lazysizes.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/js/ls.unveilhooks.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/js/querybuilder/query-builder.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/js/tabulator.min.js", "reason": "semgrepignore_patterns_match"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_form.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/change_password.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_confirmation.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/delete_selected_confirmation.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/list.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/object_history.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/action_link.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/selected_rows_actions.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/tabulator_header_v1.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/tabulator_header_v2.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/partials/translations_status_row.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/translations-detail.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/actions/translations.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/login.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/logout.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/password_change_done.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/authentification/password_reset_complete.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/components/columns.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/components/filters.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/components/filters_v2.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/components/inputs.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/config/view.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/dashboard/calendar_widget.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/filter_widgets/advanced_filters/autocomplete_field.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/components.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/confirmation.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/inline_fieldset.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/includes/table_inline_delete_button.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/stacked_inline.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/inlines/table_inline_paginated.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/page_list.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/cms/translations_status_row.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/filer_change_form.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/folder_list.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/integrations/filer/image_change_form.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/navigation.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/messages/alert_base.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/messages/alert_error.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/messages/alert_info.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/messages/alert_success.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/messages/alert_warning.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/partials/modal/modal_content.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/sb_admin_base.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/sb_admin_base_no_sidebar.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/submit_line.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/array.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/attributes.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/autocomplete.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/checkbox_select.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/clearable_file_input.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/date.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/filer_file.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/includes/related_item_buttons.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/password.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/time.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/tree_base.html", "reason": "analysis_failed_parser_or_internal_error"}, {"path": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/templates/sb_admin/widgets/tree_select.html", "reason": "analysis_failed_parser_or_internal_error"}]}, "time": {"rules": [], "rules_parse_time": 1.859691858291626, "profiling_times": {"config_time": 6.3207688331604, "core_time": 7.621229410171509, "ignores_time": 0.002093076705932617, "total_time": 13.945590257644653}, "parsing_time": {"total_time": 2.9496910572052, "per_file_time": {"mean": 0.019796584276544963, "std_dev": 0.0013205573174617808}, "very_slow_stats": {"time_ratio": 0.0, "count_ratio": 0.0}, "very_slow_files": []}, "scanning_time": {"total_time": 31.9621422290802, "per_file_time": {"mean": 0.019988831913120838, "std_dev": 0.018738620724649577}, "very_slow_stats": {"time_ratio": 0.25873450693602, "count_ratio": 0.0025015634771732333}, "very_slow_files": [{"fpath": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/engine/admin_base_view.py", "ftime": 1.7285349369049072}, {"fpath": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/admin/widgets.py", "ftime": 1.8943779468536377}, {"fpath": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/tree_widget.js", "ftime": 2.2143051624298096}, {"fpath": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/admin/admin_base.py", "ftime": 2.4324910640716553}]}, "matching_time": {"total_time": 13.211828470230103, "per_file_and_rule_time": {"mean": 0.013550593302800106, "std_dev": 0.001317905547108828}, "very_slow_stats": {"time_ratio": 0.34543366641447604, "count_ratio": 0.022564102564102566}, "very_slow_rules_on_files": [{"fpath": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/tree_widget.js", "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.19789600372314453}, {"fpath": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/table.js", "rule_id": "javascript.express.security.audit.express-open-redirect.express-open-redirect", "time": 0.19891691207885742}, {"fpath": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/admin/widgets.py", "rule_id": "python.lang.security.dangerous-system-call.dangerous-system-call", "time": 0.2007749080657959}, {"fpath": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/table.js", "rule_id": "javascript.browser.security.raw-html-concat.raw-html-concat", "time": 0.2147531509399414}, {"fpath": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/admin/admin_base.py", "rule_id": "python.aws-lambda.security.tainted-html-string.tainted-html-string", "time": 0.22261786460876465}, {"fpath": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/views/translations_view.py", "rule_id": "python.django.security.injection.open-redirect.open-redirect", "time": 0.2646918296813965}, {"fpath": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/autocomplete.js", "rule_id": "javascript.express.security.audit.express-ssrf.express-ssrf", "time": 0.31252312660217285}, {"fpath": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/tree_widget.js", "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 0.35370683670043945}, {"fpath": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/engine/admin_base_view.py", "rule_id": "python.django.security.injection.open-redirect.open-redirect", "time": 0.41295599937438965}, {"fpath": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/admin/admin_base.py", "rule_id": "python.lang.security.dangerous-system-call.dangerous-system-call", "time": 0.4423799514770508}]}, "tainting_time": {"total_time": 6.099798679351807, "per_def_and_rule_time": {"mean": 0.0009747201469082468, "std_dev": 2.2525165678349067e-05}, "very_slow_stats": {"time_ratio": 0.16013212733848464, "count_ratio": 0.0015979546180888463}, "very_slow_rules_on_defs": [{"fpath": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/tree_widget.js", "fline": 1, "rule_id": "javascript.express.security.audit.remote-property-injection.remote-property-injection", "time": 0.053421974182128906}, {"fpath": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/tree_widget.js", "fline": 1, "rule_id": "javascript.lang.security.audit.path-traversal.path-join-resolve-traversal.path-join-resolve-traversal", "time": 0.059654951095581055}, {"fpath": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/tree_widget.js", "fline": 1, "rule_id": "javascript.express.security.audit.res-render-injection.res-render-injection", "time": 0.05969810485839844}, {"fpath": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/admin/admin_base.py", "fline": 344, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.06576704978942871}, {"fpath": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/tree_widget.js", "fline": 1, "rule_id": "javascript.express.security.cors-misconfiguration.cors-misconfiguration", "time": 0.06759285926818848}, {"fpath": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/services/xlsx_export.py", "fline": 19, "rule_id": "python.boto3.security.hardcoded-token.hardcoded-token", "time": 0.09168696403503418}, {"fpath": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/tree_widget.js", "fline": 1, "rule_id": "javascript.express.security.x-frame-options-misconfiguration.x-frame-options-misconfiguration", "time": 0.10781598091125488}, {"fpath": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/tree_widget.js", "fline": 1, "rule_id": "javascript.express.security.express-insecure-template-usage.express-insecure-template-usage", "time": 0.13718795776367188}, {"fpath": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/tree_widget.js", "fline": 1, "rule_id": "javascript.express.security.injection.raw-html-format.raw-html-format", "time": 0.14732885360717773}, {"fpath": "downloaded_repos/SmartBase-SK_django-smartbase-admin/src/django_smartbase_admin/static/sb_admin/src/js/tree_widget.js", "fline": 1, "rule_id": "javascript.lang.security.audit.unsafe-formatstring.unsafe-formatstring", "time": 0.18661904335021973}]}, "targets": [], "total_bytes": 0, "max_memory_bytes": 1097464128}, "engine_requested": "OSS", "skipped_rules": []}